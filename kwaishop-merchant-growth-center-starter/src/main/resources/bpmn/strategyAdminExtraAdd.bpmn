<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0lvosu2"
                  targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0"
                  modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
    <bpmn:process id="Process_Strategy_Admin_Extra_Add" name="策略运营平台活动保存" isExecutable="true">
        <bpmn:startEvent id="StartEvent_Strategy_Admin_Extra_Add" name="策略活动后台新增子活动开始">
            <bpmn:outgoing>Flow_1u1oa1q</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_1u1oa1q" sourceRef="StartEvent_Strategy_Admin_Extra_Add"
                           targetRef="Save_Input_Check"/>
        <bpmn:serviceTask id="Save_Input_Check" name="输入参数校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveBasicCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1u1oa1q</bpmn:incoming>
            <bpmn:outgoing>Flow_0hr4tb4</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0hr4tb4" sourceRef="Save_Input_Check" targetRef="Save_BuildStoryBus"/>
        <bpmn:serviceTask id="Save_BuildStoryBus" name="流程StoryBus构建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveBuildBaseStoryBus"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0hr4tb4</bpmn:incoming>
            <bpmn:outgoing>Flow_07ti564</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_07ti564" sourceRef="Save_BuildStoryBus" targetRef="Save_Basic_Check"/>
        <bpmn:serviceTask id="Save_Basic_Check" name="基础协议信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveProtoColBasicCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_07ti564</bpmn:incoming>
            <bpmn:outgoing>Flow_1xcvw7n</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1j6nt12" sourceRef="Extra_Add_Create_Task_Transform"
                           targetRef="Extra_Add_In_Progress_Check"/>
        <bpmn:serviceTask id="Extra_Add_Create_Task_Transform" name="构建保存&#38;创建任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddTask"/>
                    <camunda:property name="task-service" value="extraAddTransformCreateTasks"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0625q1p</bpmn:incoming>
            <bpmn:outgoing>Flow_1j6nt12</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:inclusiveGateway id="Gateway_08khf2f">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="strict-mode" value="false"/>
                    <camunda:property name="open-async" value="true"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_16k716y</bpmn:incoming>
            <bpmn:outgoing>Flow_02g0h3q</bpmn:outgoing>
            <bpmn:outgoing>Flow_02ehb25</bpmn:outgoing>
            <bpmn:outgoing>Flow_0auqehf</bpmn:outgoing>
            <bpmn:outgoing>Flow_1e5oun2</bpmn:outgoing>
            <bpmn:outgoing>Flow_1oq18ll</bpmn:outgoing>
            <bpmn:outgoing>Flow_08se61y</bpmn:outgoing>
            <bpmn:outgoing>Flow_1irz9cd</bpmn:outgoing>
            <bpmn:outgoing>Flow_0w22730</bpmn:outgoing>
            <bpmn:outgoing>Flow_0sxv91d</bpmn:outgoing>
            <bpmn:outgoing>Flow_13kcxqx</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:inclusiveGateway id="Gateway_11zej4y">
            <bpmn:incoming>Flow_1iady6k</bpmn:incoming>
            <bpmn:incoming>Flow_1tvb561</bpmn:incoming>
            <bpmn:incoming>Flow_07n51jv</bpmn:incoming>
            <bpmn:incoming>Flow_03hau9p</bpmn:incoming>
            <bpmn:incoming>Flow_04m1mex</bpmn:incoming>
            <bpmn:incoming>Flow_1v30k9a</bpmn:incoming>
            <bpmn:incoming>Flow_1i5quep</bpmn:incoming>
            <bpmn:incoming>Flow_0zmoaki</bpmn:incoming>
            <bpmn:incoming>Flow_0ib8ct5</bpmn:incoming>
            <bpmn:incoming>Flow_0sde4wl</bpmn:incoming>
            <bpmn:outgoing>Flow_0ta89w6</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_02g0h3q" sourceRef="Gateway_08khf2f" targetRef="Save_Layer_Crowd_Check"/>
        <bpmn:sequenceFlow id="Flow_1iady6k" sourceRef="Save_Layer_Crowd_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Layer_Crowd_Check" name="分层人群互斥校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveCrowdDupCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_02g0h3q</bpmn:incoming>
            <bpmn:outgoing>Flow_1iady6k</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_02ehb25" sourceRef="Gateway_08khf2f" targetRef="Save_Excel_Crowd_Check"/>
        <bpmn:serviceTask id="Save_Excel_Crowd_Check" name="上传Excel和人群匹配校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveExcelCrowdMatchCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_02ehb25</bpmn:incoming>
            <bpmn:outgoing>Flow_1tvb561</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1tvb561" sourceRef="Save_Excel_Crowd_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:sequenceFlow id="Flow_0auqehf" sourceRef="Gateway_08khf2f" targetRef="Save_Pattern_Necessary_Check"/>
        <bpmn:sequenceFlow id="Flow_07n51jv" sourceRef="Save_Pattern_Necessary_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:inclusiveGateway id="Gateway_0knouox">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true"/>
                    <camunda:property name="strict-mode" value="false"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1pj568l</bpmn:incoming>
            <bpmn:outgoing>Flow_1as05ce</bpmn:outgoing>
            <bpmn:outgoing>Flow_097orqy</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1as05ce" sourceRef="Gateway_0knouox" targetRef="Save_Reuslt_Build"/>
        <bpmn:inclusiveGateway id="Gateway_0duhmrg">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true"/>
                    <camunda:property name="strict-mode" value="false"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0qo6tfs</bpmn:incoming>
            <bpmn:incoming>Flow_1sy933e</bpmn:incoming>
            <bpmn:outgoing>Flow_06d5oaz</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_097orqy" sourceRef="Gateway_0knouox" targetRef="Save_Operation_Record"/>
        <bpmn:serviceTask id="Save_Operation_Record" name="操作日志记录">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveOperateLog"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_097orqy</bpmn:incoming>
            <bpmn:outgoing>Flow_0qo6tfs</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0qo6tfs" sourceRef="Save_Operation_Record" targetRef="Gateway_0duhmrg"/>
        <bpmn:serviceTask id="Save_Reuslt_Build" name="结果组装">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveBuildResult"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1as05ce</bpmn:incoming>
            <bpmn:outgoing>Flow_1sy933e</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1sy933e" sourceRef="Save_Reuslt_Build" targetRef="Gateway_0duhmrg"/>
        <bpmn:serviceTask id="Extra_Add_Create_Kim_Notice" name="Kim结果播报">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddTask"/>
                    <camunda:property name="task-service" value="extraAddStrategyAdminKimNotice"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1wuevdc</bpmn:incoming>
            <bpmn:outgoing>Flow_1pj568l</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1pj568l" sourceRef="Extra_Add_Create_Kim_Notice" targetRef="Gateway_0knouox"/>
        <bpmn:endEvent id="EndEvent_Strategy_Admin_Save" name="策略活动后台保存结束">
            <bpmn:incoming>Flow_06d5oaz</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:serviceTask id="Save_Pattern_Necessary_Check" name="活动玩法定制化必须指标校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveNecessaryIndicatorCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0auqehf</bpmn:incoming>
            <bpmn:outgoing>Flow_07n51jv</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1e5oun2" sourceRef="Gateway_08khf2f" targetRef="Save_Return_Indicator_Check"/>
        <bpmn:sequenceFlow id="Flow_03hau9p" sourceRef="Save_Return_Indicator_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Return_Indicator_Check" name="返点指标校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveReturnIndicatorExpireCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1e5oun2</bpmn:incoming>
            <bpmn:outgoing>Flow_03hau9p</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1oq18ll" sourceRef="Gateway_08khf2f" targetRef="Save_Basic_Config_Check"/>
        <bpmn:sequenceFlow id="Flow_04m1mex" sourceRef="Save_Basic_Config_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Basic_Config_Check" name="各分层基值配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveBasicConfigCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1oq18ll</bpmn:incoming>
            <bpmn:outgoing>Flow_04m1mex</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_08se61y" sourceRef="Gateway_08khf2f" targetRef="Save_Target_Extra_Check"/>
        <bpmn:sequenceFlow id="Flow_1v30k9a" sourceRef="Save_Target_Extra_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Target_Extra_Check" name="指标附加条件校验（筛选包&#38;类目）">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveIndicatorExtraInfoCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_08se61y</bpmn:incoming>
            <bpmn:outgoing>Flow_1v30k9a</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1irz9cd" sourceRef="Gateway_08khf2f" targetRef="Save_Target_Check"/>
        <bpmn:sequenceFlow id="Flow_1i5quep" sourceRef="Save_Target_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Target_Check" name="阶梯目标递增/类型唯一/指标有效校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveTargetCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1irz9cd</bpmn:incoming>
            <bpmn:outgoing>Flow_1i5quep</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:exclusiveGateway id="Gateway_18c4p26">
            <bpmn:incoming>Flow_1ps58qt</bpmn:incoming>
            <bpmn:outgoing>Flow_0wa574t</bpmn:outgoing>
            <bpmn:outgoing>Flow_0xltshs</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0wa574t" name="不申请预算" sourceRef="Gateway_18c4p26" targetRef="Gateway_043qlse">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.saveStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_043qlse">
            <bpmn:incoming>Flow_0wa574t</bpmn:incoming>
            <bpmn:incoming>Flow_1wv30vn</bpmn:incoming>
            <bpmn:outgoing>Flow_087rki0</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0xltshs" name="申请预算" sourceRef="Gateway_18c4p26"
                           targetRef="Save_Resource_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1wv30vn" sourceRef="Save_Resource_Check" targetRef="Gateway_043qlse"/>
        <bpmn:serviceTask id="Extra_Add_Create_Activity_Transform" name="构建保存&#38;创建活动模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddTask"/>
                    <camunda:property name="task-service" value="extraAddTransformCreateActivity"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0imcwd8</bpmn:incoming>
            <bpmn:outgoing>Flow_0625q1p</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0625q1p" sourceRef="Extra_Add_Create_Activity_Transform"
                           targetRef="Extra_Add_Create_Task_Transform"/>
        <bpmn:sequenceFlow id="Flow_1xcvw7n" sourceRef="Save_Basic_Check" targetRef="Save_Before_Activity_Check"/>
        <bpmn:serviceTask id="Save_Resource_Check" name="横向信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveResourceCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0xltshs</bpmn:incoming>
            <bpmn:outgoing>Flow_1wv30vn</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Extra_Add_Create_Activity_Task" name="活动任务配置创建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddTask"/>
                    <camunda:property name="task-service" value="extraAddSubActivityCreate"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_087rki0</bpmn:incoming>
            <bpmn:outgoing>Flow_1wuevdc</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_06d5oaz" sourceRef="Gateway_0duhmrg" targetRef="EndEvent_Strategy_Admin_Save"/>
        <bpmn:serviceTask id="Save_Create_Build_Crowd" name="构建分层任务人群信息">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveTransformTaskCrowds"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0ofp2v6</bpmn:incoming>
            <bpmn:outgoing>Flow_16k716y</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_087rki0" sourceRef="Gateway_043qlse" targetRef="Extra_Add_Create_Activity_Task"/>
        <bpmn:serviceTask id="Save_Before_Activity_Check" name="当前活动状态&#38;权限校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveBeforeActivityCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1xcvw7n</bpmn:incoming>
            <bpmn:outgoing>Flow_0th334v</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Extra_Add_In_Progress_Check" name="进行中添加子活动校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddTask"/>
                    <camunda:property name="task-service" value="extraAddSubActivityCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1j6nt12</bpmn:incoming>
            <bpmn:outgoing>Flow_1f66vhf</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_16k716y" sourceRef="Save_Create_Build_Crowd" targetRef="Gateway_08khf2f"/>
        <bpmn:sequenceFlow id="Flow_0w22730" sourceRef="Gateway_08khf2f" targetRef="Save_Component_Show_Check"/>
        <bpmn:sequenceFlow id="Flow_0zmoaki" sourceRef="Save_Component_Show_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Component_Show_Check" name="是否能够组件表达校验校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveComponentShowCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0w22730</bpmn:incoming>
            <bpmn:outgoing>Flow_0zmoaki</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0sxv91d" sourceRef="Gateway_08khf2f" targetRef="Save_Registration_Config_Check"/>
        <bpmn:serviceTask id="Save_Registration_Config_Check" name="分层报名配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveRegistrationConfigCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0sxv91d</bpmn:incoming>
            <bpmn:outgoing>Flow_0ib8ct5</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0ib8ct5" sourceRef="Save_Registration_Config_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:sequenceFlow id="Flow_13kcxqx" sourceRef="Gateway_08khf2f" targetRef="Save_Service_Market_Check"/>
        <bpmn:sequenceFlow id="Flow_0sde4wl" sourceRef="Save_Service_Market_Check" targetRef="Gateway_11zej4y"/>
        <bpmn:serviceTask id="Save_Service_Market_Check" name="服务市场优惠券校验组件">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveServiceMarketCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_13kcxqx</bpmn:incoming>
            <bpmn:outgoing>Flow_0sde4wl</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1wuevdc" sourceRef="Extra_Add_Create_Activity_Task"
                           targetRef="Extra_Add_Create_Kim_Notice"/>
        <bpmn:sequenceFlow id="Flow_0th334v" sourceRef="Save_Before_Activity_Check" targetRef="Activity_095t3ca"/>
        <bpmn:serviceTask id="Activity_095t3ca" name="子活动协议校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveProtoColCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0th334v</bpmn:incoming>
            <bpmn:outgoing>Flow_0imcwd8</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0imcwd8" sourceRef="Activity_095t3ca"
                           targetRef="Extra_Add_Create_Activity_Transform"/>
        <bpmn:sequenceFlow id="Flow_0ta89w6" sourceRef="Gateway_11zej4y" targetRef="Save_Personalized_Check"/>
        <bpmn:serviceTask id="Save_Personalized_Check" name="分活动类型个性化校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="saveActivityPersonalizedCheck"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0ta89w6</bpmn:incoming>
            <bpmn:outgoing>Flow_1ps58qt</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1ps58qt" sourceRef="Save_Personalized_Check" targetRef="Gateway_18c4p26"/>
        <bpmn:serviceTask id="Extend_Tasks" name="扩展任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave"/>
                    <camunda:property name="task-service" value="extendTasks"/>
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1f66vhf</bpmn:incoming>
            <bpmn:outgoing>Flow_0ofp2v6</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1f66vhf" sourceRef="Extra_Add_In_Progress_Check" targetRef="Extend_Tasks"/>
        <bpmn:sequenceFlow id="Flow_0ofp2v6" sourceRef="Extend_Tasks" targetRef="Save_Create_Build_Crowd"/>
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_Strategy_Admin_Extra_Add">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_Strategy_Admin_Extra_Add">
                <dc:Bounds x="472" y="532" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="452" y="495" width="77" height="27"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_18g8asu_di" bpmnElement="Save_Input_Check">
                <dc:Bounds x="310" y="510" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0my219t_di" bpmnElement="Save_BuildStoryBus">
                <dc:Bounds x="160" y="510" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_009pgxq_di" bpmnElement="Save_Basic_Check">
                <dc:Bounds x="160" y="620" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1rhmndu_di" bpmnElement="Extra_Add_Create_Task_Transform">
                <dc:Bounds x="310" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0j92p0w" bpmnElement="Extra_Add_Create_Activity_Transform">
                <dc:Bounds x="160" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0au5b84" bpmnElement="Save_Before_Activity_Check">
                <dc:Bounds x="160" y="730" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_103eb0n" bpmnElement="Extra_Add_In_Progress_Check">
                <dc:Bounds x="460" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1sanuhx" bpmnElement="Activity_095t3ca">
                <dc:Bounds x="160" y="840" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1138kye_di" bpmnElement="Save_Layer_Crowd_Check">
                <dc:Bounds x="1180" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1xding3_di" bpmnElement="Save_Excel_Crowd_Check">
                <dc:Bounds x="1180" y="870" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_07ertqv_di" bpmnElement="Save_Pattern_Necessary_Check">
                <dc:Bounds x="1180" y="780" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_02h7eqw_di" bpmnElement="Save_Return_Indicator_Check">
                <dc:Bounds x="1180" y="690" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0j1l6f2_di" bpmnElement="Save_Basic_Config_Check">
                <dc:Bounds x="1180" y="600" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0mw78n1_di" bpmnElement="Save_Target_Extra_Check">
                <dc:Bounds x="1180" y="510" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1qte01g_di" bpmnElement="Save_Target_Check">
                <dc:Bounds x="1180" y="420" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0m47uff_di" bpmnElement="Save_Component_Show_Check">
                <dc:Bounds x="1180" y="320" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_04mj9f8_di" bpmnElement="Save_Registration_Config_Check">
                <dc:Bounds x="1180" y="210" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0g678l8_di" bpmnElement="Save_Service_Market_Check">
                <dc:Bounds x="1180" y="80" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1nhsv98_di" bpmnElement="Gateway_11zej4y">
                <dc:Bounds x="1345" y="975" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0msn9ne_di" bpmnElement="Save_Personalized_Check">
                <dc:Bounds x="1450" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_18c4p26_di" bpmnElement="Gateway_18c4p26" isMarkerVisible="true">
                <dc:Bounds x="1605" y="975" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1acpnc8_di" bpmnElement="Save_Resource_Check">
                <dc:Bounds x="1580" y="790" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_114tbji_di" bpmnElement="Gateway_043qlse">
                <dc:Bounds x="1745" y="975" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_11b1e57_di" bpmnElement="Extra_Add_Create_Kim_Notice">
                <dc:Bounds x="1860" y="790" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1v0sj2z_di" bpmnElement="Save_Operation_Record">
                <dc:Bounds x="1990" y="620" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1rhg6st_di" bpmnElement="EndEvent_Strategy_Admin_Save">
                <dc:Bounds x="2152" y="802" width="36" height="36"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2134" y="845" width="77" height="27"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1sw79cm_di" bpmnElement="Gateway_08khf2f">
                <dc:Bounds x="1065" y="975" width="50" height="50"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="792" y="793" width="55" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0m8s3kz_di" bpmnElement="Gateway_0knouox">
                <dc:Bounds x="1885" y="635" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_18sog6v" bpmnElement="Save_Reuslt_Build">
                <dc:Bounds x="1990" y="490" width="100" height="80"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0k75rg3_di" bpmnElement="Gateway_0duhmrg">
                <dc:Bounds x="2145" y="635" width="50" height="50"/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1gy4ro1" bpmnElement="Extra_Add_Create_Activity_Task">
                <dc:Bounds x="1860" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0udq3ra_di" bpmnElement="Extend_Tasks">
                <dc:Bounds x="690" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_15hp0j2" bpmnElement="Save_Create_Build_Crowd">
                <dc:Bounds x="890" y="960" width="100" height="80"/>
                <bpmndi:BPMNLabel/>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_1u1oa1q_di" bpmnElement="Flow_1u1oa1q">
                <di:waypoint x="472" y="550"/>
                <di:waypoint x="410" y="550"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0hr4tb4_di" bpmnElement="Flow_0hr4tb4">
                <di:waypoint x="310" y="550"/>
                <di:waypoint x="260" y="550"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07ti564_di" bpmnElement="Flow_07ti564">
                <di:waypoint x="210" y="590"/>
                <di:waypoint x="210" y="620"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1j6nt12_di" bpmnElement="Flow_1j6nt12">
                <di:waypoint x="410" y="1000"/>
                <di:waypoint x="460" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0625q1p_di" bpmnElement="Flow_0625q1p">
                <di:waypoint x="260" y="1000"/>
                <di:waypoint x="310" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xcvw7n_di" bpmnElement="Flow_1xcvw7n">
                <di:waypoint x="210" y="700"/>
                <di:waypoint x="210" y="730"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0th334v_di" bpmnElement="Flow_0th334v">
                <di:waypoint x="210" y="810"/>
                <di:waypoint x="210" y="840"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0imcwd8_di" bpmnElement="Flow_0imcwd8">
                <di:waypoint x="210" y="920"/>
                <di:waypoint x="210" y="960"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_16k716y_di" bpmnElement="Flow_16k716y">
                <di:waypoint x="990" y="1000"/>
                <di:waypoint x="1065" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_02g0h3q_di" bpmnElement="Flow_02g0h3q">
                <di:waypoint x="1115" y="1000"/>
                <di:waypoint x="1180" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1iady6k_di" bpmnElement="Flow_1iady6k">
                <di:waypoint x="1280" y="1000"/>
                <di:waypoint x="1345" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_02ehb25_di" bpmnElement="Flow_02ehb25">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="910"/>
                <di:waypoint x="1180" y="910"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1tvb561_di" bpmnElement="Flow_1tvb561">
                <di:waypoint x="1280" y="910"/>
                <di:waypoint x="1370" y="910"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0auqehf_di" bpmnElement="Flow_0auqehf">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="820"/>
                <di:waypoint x="1180" y="820"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07n51jv_di" bpmnElement="Flow_07n51jv">
                <di:waypoint x="1280" y="820"/>
                <di:waypoint x="1370" y="820"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1e5oun2_di" bpmnElement="Flow_1e5oun2">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="730"/>
                <di:waypoint x="1180" y="730"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_03hau9p_di" bpmnElement="Flow_03hau9p">
                <di:waypoint x="1280" y="730"/>
                <di:waypoint x="1370" y="730"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1oq18ll_di" bpmnElement="Flow_1oq18ll">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="640"/>
                <di:waypoint x="1180" y="640"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04m1mex_di" bpmnElement="Flow_04m1mex">
                <di:waypoint x="1280" y="640"/>
                <di:waypoint x="1370" y="640"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_08se61y_di" bpmnElement="Flow_08se61y">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="550"/>
                <di:waypoint x="1180" y="550"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1v30k9a_di" bpmnElement="Flow_1v30k9a">
                <di:waypoint x="1280" y="550"/>
                <di:waypoint x="1370" y="550"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1irz9cd_di" bpmnElement="Flow_1irz9cd">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="460"/>
                <di:waypoint x="1180" y="460"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1i5quep_di" bpmnElement="Flow_1i5quep">
                <di:waypoint x="1280" y="460"/>
                <di:waypoint x="1370" y="460"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0w22730_di" bpmnElement="Flow_0w22730">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="360"/>
                <di:waypoint x="1180" y="360"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0zmoaki_di" bpmnElement="Flow_0zmoaki">
                <di:waypoint x="1280" y="360"/>
                <di:waypoint x="1370" y="360"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sxv91d_di" bpmnElement="Flow_0sxv91d">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="250"/>
                <di:waypoint x="1180" y="250"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ib8ct5_di" bpmnElement="Flow_0ib8ct5">
                <di:waypoint x="1280" y="250"/>
                <di:waypoint x="1370" y="250"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_13kcxqx_di" bpmnElement="Flow_13kcxqx">
                <di:waypoint x="1090" y="975"/>
                <di:waypoint x="1090" y="120"/>
                <di:waypoint x="1180" y="120"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sde4wl_di" bpmnElement="Flow_0sde4wl">
                <di:waypoint x="1280" y="120"/>
                <di:waypoint x="1370" y="120"/>
                <di:waypoint x="1370" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ta89w6_di" bpmnElement="Flow_0ta89w6">
                <di:waypoint x="1395" y="1000"/>
                <di:waypoint x="1450" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ps58qt_di" bpmnElement="Flow_1ps58qt">
                <di:waypoint x="1550" y="1000"/>
                <di:waypoint x="1605" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0wa574t_di" bpmnElement="Flow_0wa574t">
                <di:waypoint x="1655" y="1000"/>
                <di:waypoint x="1745" y="1000"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1674" y="982" width="55" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0xltshs_di" bpmnElement="Flow_0xltshs">
                <di:waypoint x="1630" y="975"/>
                <di:waypoint x="1630" y="870"/>
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1648" y="916" width="44" height="14"/>
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1wv30vn_di" bpmnElement="Flow_1wv30vn">
                <di:waypoint x="1680" y="830"/>
                <di:waypoint x="1770" y="830"/>
                <di:waypoint x="1770" y="975"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_087rki0_di" bpmnElement="Flow_087rki0">
                <di:waypoint x="1795" y="1000"/>
                <di:waypoint x="1860" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1wuevdc_di" bpmnElement="Flow_1wuevdc">
                <di:waypoint x="1910" y="960"/>
                <di:waypoint x="1910" y="870"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1pj568l_di" bpmnElement="Flow_1pj568l">
                <di:waypoint x="1910" y="790"/>
                <di:waypoint x="1910" y="685"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_097orqy_di" bpmnElement="Flow_097orqy">
                <di:waypoint x="1935" y="660"/>
                <di:waypoint x="1990" y="660"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0qo6tfs_di" bpmnElement="Flow_0qo6tfs">
                <di:waypoint x="2090" y="660"/>
                <di:waypoint x="2145" y="660"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_06d5oaz_di" bpmnElement="Flow_06d5oaz">
                <di:waypoint x="2170" y="685"/>
                <di:waypoint x="2170" y="802"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1as05ce_di" bpmnElement="Flow_1as05ce">
                <di:waypoint x="1910" y="635"/>
                <di:waypoint x="1910" y="530"/>
                <di:waypoint x="1990" y="530"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1sy933e_di" bpmnElement="Flow_1sy933e">
                <di:waypoint x="2090" y="530"/>
                <di:waypoint x="2170" y="530"/>
                <di:waypoint x="2170" y="635"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1f66vhf_di" bpmnElement="Flow_1f66vhf">
                <di:waypoint x="560" y="1000"/>
                <di:waypoint x="690" y="1000"/>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ofp2v6_di" bpmnElement="Flow_0ofp2v6">
                <di:waypoint x="790" y="1000"/>
                <di:waypoint x="890" y="1000"/>
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
