<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0lvosu2"
                  targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0"
                  modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
    <bpmn:process id="Process_Strategy_Admin_Save" name="策略运营平台活动保存" isExecutable="true">
        <bpmn:startEvent id="StartEvent_Strategy_Admin_Save" name="策略活动后台保存开始">
            <bpmn:outgoing>Flow_1u1oa1q</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_1u1oa1q" sourceRef="StartEvent_Strategy_Admin_Save" targetRef="Save_Input_Check" />
        <bpmn:serviceTask id="Save_Input_Check" name="输入参数校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBasicCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1u1oa1q</bpmn:incoming>
            <bpmn:outgoing>Flow_0hr4tb4</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0hr4tb4" sourceRef="Save_Input_Check" targetRef="Save_BuildStoryBus" />
        <bpmn:serviceTask id="Save_BuildStoryBus" name="流程StoryBus构建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBuildBaseStoryBus" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0hr4tb4</bpmn:incoming>
            <bpmn:outgoing>Flow_07ti564</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_07ti564" sourceRef="Save_BuildStoryBus" targetRef="Save_Basic_Check" />
        <bpmn:serviceTask id="Save_Basic_Check" name="基础协议信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveProtoColBasicCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_07ti564</bpmn:incoming>
            <bpmn:outgoing>Flow_1xcvw7n</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:exclusiveGateway id="Gateway_0bakgd7">
            <bpmn:incoming>Flow_1fvxhir</bpmn:incoming>
            <bpmn:outgoing>Flow_1cqs5ll</bpmn:outgoing>
            <bpmn:outgoing>Flow_0uwltc1</bpmn:outgoing>
            <bpmn:outgoing>Flow_01zr2uh</bpmn:outgoing>
            <bpmn:outgoing>Flow_1t74eq8</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1cqs5ll" name="保存草稿" sourceRef="Gateway_0bakgd7" targetRef="Save_Draft_Transform">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.operateType == 3
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_0uwltc1" name="活动创建" sourceRef="Gateway_0bakgd7" targetRef="Save_Create_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.operateType == 1
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1j6nt12" sourceRef="Save_Create_Task_Transform" targetRef="Extend_Tasks" />
        <bpmn:serviceTask id="Save_Create_Task_Transform" name="构建保存&#38;创建任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformCreateTasks" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0625q1p</bpmn:incoming>
            <bpmn:outgoing>Flow_1j6nt12</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:inclusiveGateway id="Gateway_08khf2f">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="strict-mode" value="false" />
                    <camunda:property name="open-async" value="true" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_16k716y</bpmn:incoming>
            <bpmn:outgoing>Flow_02g0h3q</bpmn:outgoing>
            <bpmn:outgoing>Flow_02ehb25</bpmn:outgoing>
            <bpmn:outgoing>Flow_0auqehf</bpmn:outgoing>
            <bpmn:outgoing>Flow_1e5oun2</bpmn:outgoing>
            <bpmn:outgoing>Flow_1oq18ll</bpmn:outgoing>
            <bpmn:outgoing>Flow_08se61y</bpmn:outgoing>
            <bpmn:outgoing>Flow_1irz9cd</bpmn:outgoing>
            <bpmn:outgoing>Flow_0w22730</bpmn:outgoing>
            <bpmn:outgoing>Flow_0sxv91d</bpmn:outgoing>
            <bpmn:outgoing>Flow_13kcxqx</bpmn:outgoing>
            <bpmn:outgoing>Flow_0h9umv5</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:inclusiveGateway id="Gateway_11zej4y">
            <bpmn:incoming>Flow_1iady6k</bpmn:incoming>
            <bpmn:incoming>Flow_1tvb561</bpmn:incoming>
            <bpmn:incoming>Flow_07n51jv</bpmn:incoming>
            <bpmn:incoming>Flow_03hau9p</bpmn:incoming>
            <bpmn:incoming>Flow_04m1mex</bpmn:incoming>
            <bpmn:incoming>Flow_1v30k9a</bpmn:incoming>
            <bpmn:incoming>Flow_1i5quep</bpmn:incoming>
            <bpmn:incoming>Flow_0zmoaki</bpmn:incoming>
            <bpmn:incoming>Flow_0ib8ct5</bpmn:incoming>
            <bpmn:incoming>Flow_0sde4wl</bpmn:incoming>
            <bpmn:incoming>Flow_1kfo93q</bpmn:incoming>
            <bpmn:outgoing>Flow_1n6vock</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_02g0h3q" sourceRef="Gateway_08khf2f" targetRef="Save_Layer_Crowd_Check" />
        <bpmn:sequenceFlow id="Flow_1iady6k" sourceRef="Save_Layer_Crowd_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Layer_Crowd_Check" name="分层人群互斥校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveCrowdDupCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_02g0h3q</bpmn:incoming>
            <bpmn:outgoing>Flow_1iady6k</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_02ehb25" sourceRef="Gateway_08khf2f" targetRef="Save_Excel_Crowd_Check" />
        <bpmn:serviceTask id="Save_Excel_Crowd_Check" name="上传Excel和人群匹配校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveExcelCrowdMatchCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_02ehb25</bpmn:incoming>
            <bpmn:outgoing>Flow_1tvb561</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1tvb561" sourceRef="Save_Excel_Crowd_Check" targetRef="Gateway_11zej4y" />
        <bpmn:sequenceFlow id="Flow_0auqehf" sourceRef="Gateway_08khf2f" targetRef="Save_Pattern_Necessary_Check" />
        <bpmn:sequenceFlow id="Flow_07n51jv" sourceRef="Save_Pattern_Necessary_Check" targetRef="Gateway_11zej4y" />
        <bpmn:exclusiveGateway id="Gateway_1xqnh4t">
            <bpmn:incoming>Flow_1n6vock</bpmn:incoming>
            <bpmn:outgoing>Flow_09jvmsw</bpmn:outgoing>
            <bpmn:outgoing>Flow_1lsa038</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_09jvmsw" name="非排行榜类型" sourceRef="Gateway_1xqnh4t" targetRef="Gateway_1jx0yoa">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.activityPattern !=
                "leaderboard"
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_1jx0yoa">
            <bpmn:incoming>Flow_09jvmsw</bpmn:incoming>
            <bpmn:incoming>Flow_0vqug92</bpmn:incoming>
            <bpmn:outgoing>Flow_0x3zfng</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1lsa038" name="排行榜类型" sourceRef="Gateway_1xqnh4t" targetRef="Save_Rank_Award_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.activityPattern ==
                "leaderboard"
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_01anxza" sourceRef="Save_Rank_Award_Check" targetRef="Save_Basic_Crowd_Check" />
        <bpmn:serviceTask id="Save_Rank_Award_Check" name="排行榜奖励范围校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveRankAwardRangeCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1lsa038</bpmn:incoming>
            <bpmn:outgoing>Flow_01anxza</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:inclusiveGateway id="Gateway_0knouox">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_17o3ktq</bpmn:incoming>
            <bpmn:incoming>Flow_1ad0kav</bpmn:incoming>
            <bpmn:outgoing>Flow_1as05ce</bpmn:outgoing>
            <bpmn:outgoing>Flow_097orqy</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1as05ce" sourceRef="Gateway_0knouox" targetRef="Save_Reuslt_Build" />
        <bpmn:inclusiveGateway id="Gateway_0duhmrg">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0qo6tfs</bpmn:incoming>
            <bpmn:incoming>Flow_1sy933e</bpmn:incoming>
            <bpmn:outgoing>Flow_06d5oaz</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_097orqy" sourceRef="Gateway_0knouox" targetRef="Save_Operation_Record" />
        <bpmn:serviceTask id="Save_Operation_Record" name="操作日志记录">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveOperateLog" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_097orqy</bpmn:incoming>
            <bpmn:outgoing>Flow_0qo6tfs</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0qo6tfs" sourceRef="Save_Operation_Record" targetRef="Gateway_0duhmrg" />
        <bpmn:serviceTask id="Save_Reuslt_Build" name="结果组装">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBuildResult" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1as05ce</bpmn:incoming>
            <bpmn:outgoing>Flow_1sy933e</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1sy933e" sourceRef="Save_Reuslt_Build" targetRef="Gateway_0duhmrg" />
        <bpmn:inclusiveGateway id="Gateway_0wz95qv">
            <bpmn:incoming>Flow_1pj568l</bpmn:incoming>
            <bpmn:incoming>Flow_0wpwlgo</bpmn:incoming>
            <bpmn:incoming>Flow_0ugt9d3</bpmn:incoming>
            <bpmn:outgoing>Flow_17o3ktq</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:serviceTask id="Save_Create_Kim_Notice" name="创建Kim结果播报">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveStrategyAdminKimNotice" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0krkcmm</bpmn:incoming>
            <bpmn:outgoing>Flow_1pj568l</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1pj568l" sourceRef="Save_Create_Kim_Notice" targetRef="Gateway_0wz95qv" />
        <bpmn:endEvent id="EndEvent_Strategy_Admin_Save" name="策略活动后台保存结束">
            <bpmn:incoming>Flow_06d5oaz</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:serviceTask id="Save_Pattern_Necessary_Check" name="活动玩法定制化必须指标校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveNecessaryIndicatorCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0auqehf</bpmn:incoming>
            <bpmn:outgoing>Flow_07n51jv</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1e5oun2" sourceRef="Gateway_08khf2f" targetRef="Save_Return_Indicator_Check" />
        <bpmn:sequenceFlow id="Flow_03hau9p" sourceRef="Save_Return_Indicator_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Return_Indicator_Check" name="返点指标校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveReturnIndicatorExpireCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1e5oun2</bpmn:incoming>
            <bpmn:outgoing>Flow_03hau9p</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1oq18ll" sourceRef="Gateway_08khf2f" targetRef="Save_Basic_Config_Check" />
        <bpmn:sequenceFlow id="Flow_04m1mex" sourceRef="Save_Basic_Config_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Basic_Config_Check" name="各分层基值配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBasicConfigCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1oq18ll</bpmn:incoming>
            <bpmn:outgoing>Flow_04m1mex</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_08se61y" sourceRef="Gateway_08khf2f" targetRef="Save_Target_Extra_Check" />
        <bpmn:sequenceFlow id="Flow_1v30k9a" sourceRef="Save_Target_Extra_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Target_Extra_Check" name="指标附加条件校验（筛选包&#38;类目）">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveIndicatorExtraInfoCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_08se61y</bpmn:incoming>
            <bpmn:outgoing>Flow_1v30k9a</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1irz9cd" sourceRef="Gateway_08khf2f" targetRef="Save_Target_Check" />
        <bpmn:sequenceFlow id="Flow_1i5quep" sourceRef="Save_Target_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Target_Check" name="阶梯目标递增/类型唯一/指标有效校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTargetCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1irz9cd</bpmn:incoming>
            <bpmn:outgoing>Flow_1i5quep</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:exclusiveGateway id="Gateway_18c4p26">
            <bpmn:incoming>Flow_1t631hs</bpmn:incoming>
            <bpmn:outgoing>Flow_0wa574t</bpmn:outgoing>
            <bpmn:outgoing>Flow_0xltshs</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0wa574t" name="不申请预算" sourceRef="Gateway_18c4p26" targetRef="Gateway_043qlse">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.saveStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_043qlse">
            <bpmn:incoming>Flow_0wa574t</bpmn:incoming>
            <bpmn:incoming>Flow_1wv30vn</bpmn:incoming>
            <bpmn:outgoing>Flow_087rki0</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0xltshs" name="申请预算" sourceRef="Gateway_18c4p26" targetRef="Save_Resource_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1wv30vn" sourceRef="Save_Resource_Check" targetRef="Gateway_043qlse" />
        <bpmn:serviceTask id="Save_Create_Check" name="保存&#38;创建协议校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveProtoColCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0uwltc1</bpmn:incoming>
            <bpmn:outgoing>Flow_0lu0v9v</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0lu0v9v" sourceRef="Save_Create_Check" targetRef="Save_Create_Activity_Transform" />
        <bpmn:serviceTask id="Save_Create_Activity_Transform" name="构建保存&#38;创建活动模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformCreateActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0lu0v9v</bpmn:incoming>
            <bpmn:outgoing>Flow_0625q1p</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0625q1p" sourceRef="Save_Create_Activity_Transform" targetRef="Save_Create_Task_Transform" />
        <bpmn:serviceTask id="Save_Draft_Transform" name="构建草稿活动模型对象">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformDraftActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1cqs5ll</bpmn:incoming>
            <bpmn:outgoing>Flow_1ndx4b7</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1xcvw7n" sourceRef="Save_Basic_Check" targetRef="Save_Before_Activity_Check" />
        <bpmn:serviceTask id="Save_Resource_Check" name="横向信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveResourceCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0xltshs</bpmn:incoming>
            <bpmn:outgoing>Flow_1wv30vn</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Save_Basic_Crowd_Check" name="排行榜赛道最小人群校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveRankMinCrowdCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_01anxza</bpmn:incoming>
            <bpmn:outgoing>Flow_0vqug92</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0vqug92" sourceRef="Save_Basic_Crowd_Check" targetRef="Gateway_1jx0yoa" />
        <bpmn:serviceTask id="Save_Create_Activity_Task" name="活动任务配置创建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveActivityTaskCreate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_087rki0</bpmn:incoming>
            <bpmn:outgoing>Flow_1jung0e</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Save_Draft_Activity_Create" name="活动草稿创建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveDraftActivityCreate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1ndx4b7</bpmn:incoming>
            <bpmn:outgoing>Flow_0wpwlgo</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0wpwlgo" sourceRef="Save_Draft_Activity_Create" targetRef="Gateway_0wz95qv" />
        <bpmn:sequenceFlow id="Flow_17o3ktq" sourceRef="Gateway_0wz95qv" targetRef="Gateway_0knouox" />
        <bpmn:sequenceFlow id="Flow_06d5oaz" sourceRef="Gateway_0duhmrg" targetRef="EndEvent_Strategy_Admin_Save" />
        <bpmn:serviceTask id="Save_Create_Build_Crowd" name="构建分层任务人群信息">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformTaskCrowds" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1j6nt12</bpmn:incoming>
            <bpmn:outgoing>Flow_16k716y</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1n6vock" sourceRef="Gateway_11zej4y" targetRef="Gateway_1xqnh4t" />
        <bpmn:sequenceFlow id="Flow_087rki0" sourceRef="Gateway_043qlse" targetRef="Save_Create_Activity_Task" />
        <bpmn:sequenceFlow id="Flow_1sxboxf" sourceRef="Save_Edit_Activity_Task" targetRef="Activity_Edit_Kim_Notice" />
        <bpmn:serviceTask id="Activity_Edit_Kim_Notice" name="编辑Kim结果播报">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveStrategyAdminEditKimNotice" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1sxboxf</bpmn:incoming>
            <bpmn:outgoing>Flow_0ugt9d3</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0ugt9d3" sourceRef="Activity_Edit_Kim_Notice" targetRef="Gateway_0wz95qv" />
        <bpmn:serviceTask id="Save_Before_Activity_Check" name="当前活动状态&#38;权限校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBeforeActivityCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1xcvw7n</bpmn:incoming>
            <bpmn:outgoing>Flow_0jz6x7s</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0jz6x7s" sourceRef="Save_Before_Activity_Check" targetRef="Save_Customize_Rule_Check" />
        <bpmn:sequenceFlow id="Flow_01zr2uh" name="活动编辑" sourceRef="Gateway_0bakgd7" targetRef="Save_Edit_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.operateType == 2
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Save_Edit_Check" name="编辑协议校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveProtoColCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_01zr2uh</bpmn:incoming>
            <bpmn:outgoing>Flow_0l8qpo6</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0l8qpo6" sourceRef="Save_Edit_Check" targetRef="Save_Edit_Activity_Transform" />
        <bpmn:serviceTask id="Save_Edit_Activity_Transform" name="构建编辑活动模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformEditActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0l8qpo6</bpmn:incoming>
            <bpmn:outgoing>Flow_0kkqrgd</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0kkqrgd" sourceRef="Save_Edit_Activity_Transform" targetRef="Save_Edit_Task_Transform" />
        <bpmn:serviceTask id="Save_Edit_Task_Transform" name="构建编辑任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformEditTasks" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0kkqrgd</bpmn:incoming>
            <bpmn:outgoing>Flow_0kkun7v</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Save_Customize_Rule_Check" name="自定义规则管控校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveCustomizeRuleCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0jz6x7s</bpmn:incoming>
            <bpmn:outgoing>Flow_1fvxhir</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1fvxhir" sourceRef="Save_Customize_Rule_Check" targetRef="Gateway_0bakgd7" />
        <bpmn:sequenceFlow id="Flow_16k716y" sourceRef="Save_Create_Build_Crowd" targetRef="Gateway_08khf2f" />
        <bpmn:sequenceFlow id="Flow_1ndx4b7" sourceRef="Save_Draft_Transform" targetRef="Save_Draft_Activity_Create" />
        <bpmn:sequenceFlow id="Flow_0kkun7v" sourceRef="Save_Edit_Task_Transform" targetRef="Activity_Edit_Create_Galaxy_Page" />
        <bpmn:serviceTask id="Save_Edit_Activity_Task" name="活动任务配置编辑">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveEditActivityTask" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1g2q395</bpmn:incoming>
            <bpmn:outgoing>Flow_1sxboxf</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0w22730" sourceRef="Gateway_08khf2f" targetRef="Save_Component_Show_Check" />
        <bpmn:sequenceFlow id="Flow_0zmoaki" sourceRef="Save_Component_Show_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Component_Show_Check" name="是否能够组件表达校验校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveComponentShowCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0w22730</bpmn:incoming>
            <bpmn:outgoing>Flow_0zmoaki</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1jung0e" sourceRef="Save_Create_Activity_Task" targetRef="Save_Bind_Resource_Task" />
        <bpmn:serviceTask id="Save_Bind_Resource_Task" name="绑定横向活动">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBindResourceActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1jung0e</bpmn:incoming>
            <bpmn:outgoing>Flow_0krkcmm</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0krkcmm" sourceRef="Save_Bind_Resource_Task" targetRef="Save_Create_Kim_Notice" />
        <bpmn:sequenceFlow id="Flow_0sxv91d" sourceRef="Gateway_08khf2f" targetRef="Save_Registration_Config_Check" />
        <bpmn:serviceTask id="Save_Registration_Config_Check" name="分层报名配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveRegistrationConfigCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0sxv91d</bpmn:incoming>
            <bpmn:outgoing>Flow_0ib8ct5</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0ib8ct5" sourceRef="Save_Registration_Config_Check" targetRef="Gateway_11zej4y" />
        <bpmn:sequenceFlow id="Flow_13kcxqx" sourceRef="Gateway_08khf2f" targetRef="Save_Service_Market_Check" />
        <bpmn:sequenceFlow id="Flow_0sde4wl" sourceRef="Save_Service_Market_Check" targetRef="Gateway_11zej4y" />
        <bpmn:serviceTask id="Save_Service_Market_Check" name="服务市场优惠券校验组件">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveServiceMarketCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_13kcxqx</bpmn:incoming>
            <bpmn:outgoing>Flow_0sde4wl</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Save_Personalized_Check" name="分活动类型个性化校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveActivityPersonalizedCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0x3zfng</bpmn:incoming>
            <bpmn:outgoing>Flow_1t631hs</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1t631hs" sourceRef="Save_Personalized_Check" targetRef="Gateway_18c4p26" />
        <bpmn:sequenceFlow id="Flow_0x3zfng" sourceRef="Gateway_1jx0yoa" targetRef="Save_Personalized_Check" />
        <bpmn:serviceTask id="Extend_Tasks" name="扩展任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="extendTasks" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1j6nt12</bpmn:incoming>
            <bpmn:outgoing>Flow_0tfkqnx</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0tfkqnx" sourceRef="Extend_Tasks" targetRef="Save_Create_Build_Crowd" />
        <bpmn:serviceTask id="Activity_Edit_Create_Galaxy_Page" name="创建并发布天河页面">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="createGalaxyPageInstanceForEdit" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0kkun7v</bpmn:incoming>
            <bpmn:outgoing>Flow_1g2q395</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1g2q395" sourceRef="Activity_Edit_Create_Galaxy_Page" targetRef="Save_Edit_Activity_Task" />
        <bpmn:serviceTask id="Lite_Save_Create_Check" name="极速版保存&#38;创建协议校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveLiteProtoColCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1t74eq8</bpmn:incoming>
            <bpmn:outgoing>Flow_15bco50</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1t74eq8" name="极速版活动创建" sourceRef="Gateway_0bakgd7" targetRef="Lite_Save_Create_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.saveStoryBus.operateType == 8
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Lite_Save_Create_Activity_Transform" name="构建保存&#38;创建极速版活动模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveTransformCreateLiteActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_15bco50</bpmn:incoming>
            <bpmn:outgoing>Flow_12l523v</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_15bco50" sourceRef="Lite_Save_Create_Check" targetRef="Lite_Save_Create_Activity_Transform" />
        <bpmn:serviceTask id="Activity_19zvufx" name="活动任务配置创建">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveActivityTaskCreate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_12l523v</bpmn:incoming>
            <bpmn:outgoing>Flow_1wpw6ks</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Lite_Save_Create_Kim_Notice" name="创建Kim结果播报">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveStrategyAdminKimNotice" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0gfor8h</bpmn:incoming>
            <bpmn:outgoing>Flow_1ad0kav</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Lite_Save_Bind_Resource_Task" name="绑定横向活动">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveBindResourceActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1wpw6ks</bpmn:incoming>
            <bpmn:outgoing>Flow_0gfor8h</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_12l523v" sourceRef="Lite_Save_Create_Activity_Transform" targetRef="Activity_19zvufx" />
        <bpmn:sequenceFlow id="Flow_1wpw6ks" sourceRef="Activity_19zvufx" targetRef="Lite_Save_Bind_Resource_Task" />
        <bpmn:sequenceFlow id="Flow_0gfor8h" sourceRef="Lite_Save_Bind_Resource_Task" targetRef="Lite_Save_Create_Kim_Notice" />
        <bpmn:sequenceFlow id="Flow_1ad0kav" sourceRef="Lite_Save_Create_Kim_Notice" targetRef="Gateway_0knouox" />
        <bpmn:serviceTask id="Save_Raise_Award_Task_Check" name="加码政策子活动校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminSave" />
                    <camunda:property name="task-service" value="saveRaiseAwardTaskCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0h9umv5</bpmn:incoming>
            <bpmn:outgoing>Flow_1kfo93q</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0h9umv5" sourceRef="Gateway_08khf2f" targetRef="Save_Raise_Award_Task_Check" />
        <bpmn:sequenceFlow id="Flow_1kfo93q" sourceRef="Save_Raise_Award_Task_Check" targetRef="Gateway_11zej4y" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_Strategy_Admin_Save">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_Strategy_Admin_Save">
                <dc:Bounds x="200" y="512" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="179" y="475" width="77" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_18g8asu_di" bpmnElement="Save_Input_Check">
                <dc:Bounds x="168" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0my219t_di" bpmnElement="Save_BuildStoryBus">
                <dc:Bounds x="168" y="720" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_009pgxq_di" bpmnElement="Save_Basic_Check">
                <dc:Bounds x="168" y="843" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0bakgd7_di" bpmnElement="Gateway_0bakgd7" isMarkerVisible="true">
                <dc:Bounds x="333" y="1095" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1rhmndu_di" bpmnElement="Save_Create_Task_Transform">
                <dc:Bounds x="600" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1sw79cm_di" bpmnElement="Gateway_08khf2f">
                <dc:Bounds x="895" y="975" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="792" y="793" width="55" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1nhsv98_di" bpmnElement="Gateway_11zej4y">
                <dc:Bounds x="1175" y="975" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1138kye_di" bpmnElement="Save_Layer_Crowd_Check">
                <dc:Bounds x="1010" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1xding3_di" bpmnElement="Save_Excel_Crowd_Check">
                <dc:Bounds x="1010" y="870" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1xqnh4t_di" bpmnElement="Gateway_1xqnh4t" isMarkerVisible="true">
                <dc:Bounds x="1265" y="975" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1t3ixgj_di" bpmnElement="Gateway_1jx0yoa">
                <dc:Bounds x="1425" y="975" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1fmwuaw_di" bpmnElement="Save_Rank_Award_Check">
                <dc:Bounds x="1400" y="710" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0m8s3kz_di" bpmnElement="Gateway_0knouox">
                <dc:Bounds x="2185" y="1095" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0k75rg3_di" bpmnElement="Gateway_0duhmrg">
                <dc:Bounds x="2435" y="1095" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1v0sj2z_di" bpmnElement="Save_Operation_Record">
                <dc:Bounds x="2280" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_18sog6v" bpmnElement="Save_Reuslt_Build">
                <dc:Bounds x="2280" y="1080" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0g7cjnv_di" bpmnElement="Gateway_0wz95qv">
                <dc:Bounds x="2095" y="1095" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_11b1e57_di" bpmnElement="Save_Create_Kim_Notice">
                <dc:Bounds x="2070" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1rhg6st_di" bpmnElement="EndEvent_Strategy_Admin_Save">
                <dc:Bounds x="2442" y="1222" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2424" y="1265" width="77" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_07ertqv_di" bpmnElement="Save_Pattern_Necessary_Check">
                <dc:Bounds x="1010" y="780" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_02h7eqw_di" bpmnElement="Save_Return_Indicator_Check">
                <dc:Bounds x="1010" y="690" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0j1l6f2_di" bpmnElement="Save_Basic_Config_Check">
                <dc:Bounds x="1010" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0mw78n1_di" bpmnElement="Save_Target_Extra_Check">
                <dc:Bounds x="1010" y="510" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1qte01g_di" bpmnElement="Save_Target_Check">
                <dc:Bounds x="1010" y="420" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_18c4p26_di" bpmnElement="Gateway_18c4p26" isMarkerVisible="true">
                <dc:Bounds x="1685" y="975" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_114tbji_di" bpmnElement="Gateway_043qlse">
                <dc:Bounds x="1815" y="975" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1ix7ahn" bpmnElement="Save_Create_Check">
                <dc:Bounds x="308" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0j92p0w" bpmnElement="Save_Create_Activity_Transform">
                <dc:Bounds x="450" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_11ljiha" bpmnElement="Save_Draft_Transform">
                <dc:Bounds x="600" y="1080" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1acpnc8_di" bpmnElement="Save_Resource_Check">
                <dc:Bounds x="1790" y="840" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_17w0iam" bpmnElement="Save_Basic_Crowd_Check">
                <dc:Bounds x="1400" y="840" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1gy4ro1" bpmnElement="Save_Create_Activity_Task">
                <dc:Bounds x="1920" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1wuv4sm" bpmnElement="Save_Draft_Activity_Create">
                <dc:Bounds x="1920" y="1080" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_15hp0j2" bpmnElement="Save_Create_Build_Crowd">
                <dc:Bounds x="750" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_07ck66a" bpmnElement="Activity_Edit_Kim_Notice">
                <dc:Bounds x="2070" y="1190" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0au5b84" bpmnElement="Save_Before_Activity_Check">
                <dc:Bounds x="168" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_130orsv_di" bpmnElement="Save_Edit_Check">
                <dc:Bounds x="308" y="1190" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_00ynd84" bpmnElement="Save_Edit_Activity_Transform">
                <dc:Bounds x="450" y="1190" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1s4li3n" bpmnElement="Save_Edit_Task_Transform">
                <dc:Bounds x="600" y="1190" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_103eb0n" bpmnElement="Save_Customize_Rule_Check">
                <dc:Bounds x="168" y="1080" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0w9b7a2_di" bpmnElement="Save_Edit_Activity_Task">
                <dc:Bounds x="1920" y="1190" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0m47uff_di" bpmnElement="Save_Component_Show_Check">
                <dc:Bounds x="1010" y="320" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_08bj2t5_di" bpmnElement="Save_Bind_Resource_Task">
                <dc:Bounds x="2070" y="830" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_04mj9f8_di" bpmnElement="Save_Registration_Config_Check">
                <dc:Bounds x="1010" y="230" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0g678l8_di" bpmnElement="Save_Service_Market_Check">
                <dc:Bounds x="1010" y="140" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0ycwx6n_di" bpmnElement="Save_Personalized_Check">
                <dc:Bounds x="1530" y="960" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0uttpc3_di" bpmnElement="Extend_Tasks">
                <dc:Bounds x="676" y="800" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0h7rf8t" bpmnElement="Activity_Edit_Create_Galaxy_Page">
                <dc:Bounds x="770" y="1190" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_11ggrgo" bpmnElement="Lite_Save_Create_Check">
                <dc:Bounds x="470" y="-80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1ui2cys" bpmnElement="Lite_Save_Create_Activity_Transform">
                <dc:Bounds x="650" y="-80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1lcie97" bpmnElement="Activity_19zvufx">
                <dc:Bounds x="890" y="-80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_19djlvs" bpmnElement="Lite_Save_Create_Kim_Notice">
                <dc:Bounds x="1480" y="-80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_10fwrmx" bpmnElement="Lite_Save_Bind_Resource_Task">
                <dc:Bounds x="1140" y="-80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1igdyht_di" bpmnElement="Save_Raise_Award_Task_Check">
                <dc:Bounds x="1010" y="30" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_1u1oa1q_di" bpmnElement="Flow_1u1oa1q">
                <di:waypoint x="218" y="548" />
                <di:waypoint x="218" y="600" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0hr4tb4_di" bpmnElement="Flow_0hr4tb4">
                <di:waypoint x="218" y="680" />
                <di:waypoint x="218" y="720" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07ti564_di" bpmnElement="Flow_07ti564">
                <di:waypoint x="218" y="800" />
                <di:waypoint x="218" y="843" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1cqs5ll_di" bpmnElement="Flow_1cqs5ll">
                <di:waypoint x="383" y="1120" />
                <di:waypoint x="600" y="1120" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="404" y="1103" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0uwltc1_di" bpmnElement="Flow_0uwltc1">
                <di:waypoint x="358" y="1095" />
                <di:waypoint x="358" y="1040" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="368" y="1056" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1j6nt12_di" bpmnElement="Flow_1j6nt12">
                <di:waypoint x="650" y="960" />
                <di:waypoint x="650" y="840" />
                <di:waypoint x="676" y="840" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_02g0h3q_di" bpmnElement="Flow_02g0h3q">
                <di:waypoint x="945" y="1000" />
                <di:waypoint x="1010" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1iady6k_di" bpmnElement="Flow_1iady6k">
                <di:waypoint x="1110" y="1000" />
                <di:waypoint x="1175" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_02ehb25_di" bpmnElement="Flow_02ehb25">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="910" />
                <di:waypoint x="1010" y="910" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1tvb561_di" bpmnElement="Flow_1tvb561">
                <di:waypoint x="1110" y="910" />
                <di:waypoint x="1200" y="910" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0auqehf_di" bpmnElement="Flow_0auqehf">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="820" />
                <di:waypoint x="1010" y="820" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07n51jv_di" bpmnElement="Flow_07n51jv">
                <di:waypoint x="1110" y="820" />
                <di:waypoint x="1200" y="820" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_09jvmsw_di" bpmnElement="Flow_09jvmsw">
                <di:waypoint x="1315" y="1000" />
                <di:waypoint x="1425" y="1000" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1337" y="982" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1lsa038_di" bpmnElement="Flow_1lsa038">
                <di:waypoint x="1290" y="975" />
                <di:waypoint x="1290" y="750" />
                <di:waypoint x="1400" y="750" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1302" y="763" width="55" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_01anxza_di" bpmnElement="Flow_01anxza">
                <di:waypoint x="1450" y="790" />
                <di:waypoint x="1450" y="840" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1as05ce_di" bpmnElement="Flow_1as05ce">
                <di:waypoint x="2235" y="1120" />
                <di:waypoint x="2280" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_097orqy_di" bpmnElement="Flow_097orqy">
                <di:waypoint x="2230" y="1115" />
                <di:waypoint x="2230" y="1000" />
                <di:waypoint x="2280" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0qo6tfs_di" bpmnElement="Flow_0qo6tfs">
                <di:waypoint x="2380" y="1000" />
                <di:waypoint x="2460" y="1000" />
                <di:waypoint x="2460" y="1095" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1sy933e_di" bpmnElement="Flow_1sy933e">
                <di:waypoint x="2380" y="1120" />
                <di:waypoint x="2435" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1pj568l_di" bpmnElement="Flow_1pj568l">
                <di:waypoint x="2120" y="1040" />
                <di:waypoint x="2120" y="1095" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1e5oun2_di" bpmnElement="Flow_1e5oun2">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="730" />
                <di:waypoint x="1010" y="730" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_03hau9p_di" bpmnElement="Flow_03hau9p">
                <di:waypoint x="1110" y="730" />
                <di:waypoint x="1200" y="730" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1oq18ll_di" bpmnElement="Flow_1oq18ll">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="640" />
                <di:waypoint x="1010" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04m1mex_di" bpmnElement="Flow_04m1mex">
                <di:waypoint x="1110" y="640" />
                <di:waypoint x="1200" y="640" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_08se61y_di" bpmnElement="Flow_08se61y">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="550" />
                <di:waypoint x="1010" y="550" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1v30k9a_di" bpmnElement="Flow_1v30k9a">
                <di:waypoint x="1110" y="550" />
                <di:waypoint x="1200" y="550" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1irz9cd_di" bpmnElement="Flow_1irz9cd">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="460" />
                <di:waypoint x="1010" y="460" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1i5quep_di" bpmnElement="Flow_1i5quep">
                <di:waypoint x="1110" y="460" />
                <di:waypoint x="1200" y="460" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0wa574t_di" bpmnElement="Flow_0wa574t">
                <di:waypoint x="1735" y="1000" />
                <di:waypoint x="1815" y="1000" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1749" y="982" width="55" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0xltshs_di" bpmnElement="Flow_0xltshs">
                <di:waypoint x="1710" y="975" />
                <di:waypoint x="1710" y="880" />
                <di:waypoint x="1790" y="880" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1728" y="893" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1wv30vn_di" bpmnElement="Flow_1wv30vn">
                <di:waypoint x="1840" y="920" />
                <di:waypoint x="1840" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0lu0v9v_di" bpmnElement="Flow_0lu0v9v">
                <di:waypoint x="408" y="1000" />
                <di:waypoint x="450" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0625q1p_di" bpmnElement="Flow_0625q1p">
                <di:waypoint x="550" y="1000" />
                <di:waypoint x="600" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xcvw7n_di" bpmnElement="Flow_1xcvw7n">
                <di:waypoint x="218" y="923" />
                <di:waypoint x="218" y="960" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0vqug92_di" bpmnElement="Flow_0vqug92">
                <di:waypoint x="1450" y="920" />
                <di:waypoint x="1450" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0wpwlgo_di" bpmnElement="Flow_0wpwlgo">
                <di:waypoint x="2020" y="1120" />
                <di:waypoint x="2095" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_17o3ktq_di" bpmnElement="Flow_17o3ktq">
                <di:waypoint x="2145" y="1120" />
                <di:waypoint x="2185" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_06d5oaz_di" bpmnElement="Flow_06d5oaz">
                <di:waypoint x="2460" y="1145" />
                <di:waypoint x="2460" y="1222" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1n6vock_di" bpmnElement="Flow_1n6vock">
                <di:waypoint x="1225" y="1000" />
                <di:waypoint x="1265" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_087rki0_di" bpmnElement="Flow_087rki0">
                <di:waypoint x="1865" y="1000" />
                <di:waypoint x="1920" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1sxboxf_di" bpmnElement="Flow_1sxboxf">
                <di:waypoint x="2020" y="1230" />
                <di:waypoint x="2070" y="1230" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ugt9d3_di" bpmnElement="Flow_0ugt9d3">
                <di:waypoint x="2120" y="1190" />
                <di:waypoint x="2120" y="1145" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0jz6x7s_di" bpmnElement="Flow_0jz6x7s">
                <di:waypoint x="218" y="1040" />
                <di:waypoint x="218" y="1080" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_01zr2uh_di" bpmnElement="Flow_01zr2uh">
                <di:waypoint x="358" y="1145" />
                <di:waypoint x="358" y="1190" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="368" y="1153" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0l8qpo6_di" bpmnElement="Flow_0l8qpo6">
                <di:waypoint x="408" y="1230" />
                <di:waypoint x="450" y="1230" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0kkqrgd_di" bpmnElement="Flow_0kkqrgd">
                <di:waypoint x="550" y="1230" />
                <di:waypoint x="600" y="1230" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fvxhir_di" bpmnElement="Flow_1fvxhir">
                <di:waypoint x="268" y="1120" />
                <di:waypoint x="333" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_16k716y_di" bpmnElement="Flow_16k716y">
                <di:waypoint x="850" y="1000" />
                <di:waypoint x="895" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ndx4b7_di" bpmnElement="Flow_1ndx4b7">
                <di:waypoint x="700" y="1120" />
                <di:waypoint x="1920" y="1120" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0kkun7v_di" bpmnElement="Flow_0kkun7v">
                <di:waypoint x="700" y="1230" />
                <di:waypoint x="770" y="1230" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0w22730_di" bpmnElement="Flow_0w22730">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="360" />
                <di:waypoint x="1010" y="360" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0zmoaki_di" bpmnElement="Flow_0zmoaki">
                <di:waypoint x="1110" y="360" />
                <di:waypoint x="1200" y="360" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jung0e_di" bpmnElement="Flow_1jung0e">
                <di:waypoint x="1970" y="960" />
                <di:waypoint x="1970" y="870" />
                <di:waypoint x="2070" y="870" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0krkcmm_di" bpmnElement="Flow_0krkcmm">
                <di:waypoint x="2120" y="910" />
                <di:waypoint x="2120" y="960" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sxv91d_di" bpmnElement="Flow_0sxv91d">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="270" />
                <di:waypoint x="1010" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ib8ct5_di" bpmnElement="Flow_0ib8ct5">
                <di:waypoint x="1110" y="270" />
                <di:waypoint x="1200" y="270" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_13kcxqx_di" bpmnElement="Flow_13kcxqx">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="180" />
                <di:waypoint x="1010" y="180" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sde4wl_di" bpmnElement="Flow_0sde4wl">
                <di:waypoint x="1110" y="180" />
                <di:waypoint x="1200" y="180" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1t631hs_di" bpmnElement="Flow_1t631hs">
                <di:waypoint x="1630" y="1000" />
                <di:waypoint x="1685" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0x3zfng_di" bpmnElement="Flow_0x3zfng">
                <di:waypoint x="1475" y="1000" />
                <di:waypoint x="1530" y="1000" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0tfkqnx_di" bpmnElement="Flow_0tfkqnx">
                <di:waypoint x="776" y="840" />
                <di:waypoint x="800" y="840" />
                <di:waypoint x="800" y="960" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1g2q395_di" bpmnElement="Flow_1g2q395">
                <di:waypoint x="870" y="1230" />
                <di:waypoint x="1920" y="1230" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1t74eq8_di" bpmnElement="Flow_1t74eq8">
                <di:waypoint x="353" y="1100" />
                <di:waypoint x="290" y="1100" />
                <di:waypoint x="290" y="-40" />
                <di:waypoint x="470" y="-40" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="267" y="527" width="77" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_15bco50_di" bpmnElement="Flow_15bco50">
                <di:waypoint x="570" y="-40" />
                <di:waypoint x="650" y="-40" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_12l523v_di" bpmnElement="Flow_12l523v">
                <di:waypoint x="750" y="-40" />
                <di:waypoint x="890" y="-40" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1wpw6ks_di" bpmnElement="Flow_1wpw6ks">
                <di:waypoint x="990" y="-40" />
                <di:waypoint x="1140" y="-40" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0gfor8h_di" bpmnElement="Flow_0gfor8h">
                <di:waypoint x="1240" y="-40" />
                <di:waypoint x="1480" y="-40" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ad0kav_di" bpmnElement="Flow_1ad0kav">
                <di:waypoint x="1580" y="-40" />
                <di:waypoint x="2210" y="-40" />
                <di:waypoint x="2210" y="1095" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0h9umv5_di" bpmnElement="Flow_0h9umv5">
                <di:waypoint x="920" y="975" />
                <di:waypoint x="920" y="70" />
                <di:waypoint x="1010" y="70" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1kfo93q_di" bpmnElement="Flow_1kfo93q">
                <di:waypoint x="1110" y="70" />
                <di:waypoint x="1200" y="70" />
                <di:waypoint x="1200" y="975" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
