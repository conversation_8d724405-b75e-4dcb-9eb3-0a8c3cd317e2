<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0z6o3bg" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
    <bpmn:process id="Process_Strategy_Admin_New_SubActivity_Online" name="策略活动后台上线流程" isExecutable="true">
        <bpmn:extensionElements />
        <bpmn:startEvent id="StartEvent_Strategy_Admin_New_Add_Online" name="策略后台新增子活动上线开始">
            <bpmn:outgoing>Flow_0g792vg</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:serviceTask id="Activity_Online_StoryBus" name="StoryBus构造">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineBuildStoryBus" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_11yyjdd</bpmn:incoming>
            <bpmn:outgoing>Flow_05kbmsf</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:endEvent id="Event_Online_End" name="策略后台新增子活动上线结束">
            <bpmn:incoming>Flow_00xogcr</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:exclusiveGateway id="Gateway_1b9hdca">
            <bpmn:incoming>Flow_06xmt08</bpmn:incoming>
            <bpmn:outgoing>Flow_0z4xc7y</bpmn:outgoing>
            <bpmn:outgoing>Flow_04lqzaf</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0z4xc7y" name="不涉及B补" sourceRef="Gateway_1b9hdca" targetRef="Gateway_1ip3ooo">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_1ip3ooo">
            <bpmn:incoming>Flow_0z4xc7y</bpmn:incoming>
            <bpmn:incoming>Flow_1gr7v9a</bpmn:incoming>
            <bpmn:outgoing>Flow_04nuxd8</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_04lqzaf" name="涉及B补" sourceRef="Gateway_1b9hdca" targetRef="Activity_Online_Resource_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_Online_Resource_Check" name="横向信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineResourceInfoCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04lqzaf</bpmn:incoming>
            <bpmn:outgoing>Flow_1gr7v9a</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1gr7v9a" sourceRef="Activity_Online_Resource_Check" targetRef="Gateway_1ip3ooo" />
        <bpmn:sequenceFlow id="Flow_04nuxd8" sourceRef="Gateway_1ip3ooo" targetRef="Cycle_Create_Single_Activity" />
        <bpmn:sequenceFlow id="Flow_0g792vg" sourceRef="StartEvent_Strategy_Admin_New_Add_Online" targetRef="Activity_Online_Basic_Check" />
        <bpmn:serviceTask id="Activity_Online_Basic_Check" name="输入校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineBasicCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0g792vg</bpmn:incoming>
            <bpmn:outgoing>Flow_11yyjdd</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_Online_Permission_Check" name="权限校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="onlinePermissionCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_05kbmsf</bpmn:incoming>
            <bpmn:outgoing>Flow_05w4f3i</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_05kbmsf" sourceRef="Activity_Online_StoryBus" targetRef="Activity_Online_Permission_Check" />
        <bpmn:sequenceFlow id="Flow_11yyjdd" sourceRef="Activity_Online_Basic_Check" targetRef="Activity_Online_StoryBus" />
        <bpmn:serviceTask id="Activity_Online_Activity_Param" name="构建活动表更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="onlineUpdateActivityParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0liaumy</bpmn:incoming>
            <bpmn:outgoing>Flow_1nwv4lv</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:exclusiveGateway id="Gateway_1d57wm5">
            <bpmn:incoming>Flow_00wgmyl</bpmn:incoming>
            <bpmn:outgoing>Flow_0sgnqoj</bpmn:outgoing>
            <bpmn:outgoing>Flow_0cg3in6</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0sgnqoj" name="不涉及B补" sourceRef="Gateway_1d57wm5" targetRef="Gateway_04136bx">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_04136bx">
            <bpmn:incoming>Flow_0sgnqoj</bpmn:incoming>
            <bpmn:incoming>Flow_0qmin7b</bpmn:incoming>
            <bpmn:outgoing>Flow_1fac7m4</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:serviceTask id="Activity_Online_Task_Param" name="任务更新反绑横向规则">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="onlineUpdateTaskParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fnq454</bpmn:incoming>
            <bpmn:outgoing>Flow_00wgmyl</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_Online_Audit_Param" name="审批更新填充风控保护码">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateAuditParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0cg3in6</bpmn:incoming>
            <bpmn:outgoing>Flow_0qmin7b</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0qmin7b" sourceRef="Activity_Online_Audit_Param" targetRef="Gateway_04136bx" />
        <bpmn:sequenceFlow id="Flow_1fac7m4" sourceRef="Gateway_04136bx" targetRef="Activity_Online_Batch_Update" />
        <bpmn:serviceTask id="Activity_Online_Batch_Update" name="按参数批量更新各配置表">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineActivityTaskUpdate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fac7m4</bpmn:incoming>
            <bpmn:outgoing>Flow_0a9cd7n</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0a9cd7n" sourceRef="Activity_Online_Batch_Update" targetRef="Activity_Online_Submit_Event" />
        <bpmn:serviceTask id="Activity_Online_Operation_Log" name="操作日志记录">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="onlineOperateLog" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1n89i1m</bpmn:incoming>
            <bpmn:outgoing>Flow_00xogcr</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_00xogcr" sourceRef="Activity_Online_Operation_Log" targetRef="Event_Online_End" />
        <bpmn:serviceTask id="Activity_Online_Registration_Param" name="报名表生效更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateRegistrationParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1nwv4lv</bpmn:incoming>
            <bpmn:outgoing>Flow_1fnq454</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1fnq454" sourceRef="Activity_Online_Registration_Param" targetRef="Activity_Online_Task_Param" />
        <bpmn:serviceTask id="Activity_Online_Submit_Event" name="提交批量处理事件">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="submitSubActivityOnlineEvent" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0a9cd7n</bpmn:incoming>
            <bpmn:outgoing>Flow_0lfu5pc</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0cg3in6" sourceRef="Gateway_1d57wm5" targetRef="Activity_Online_Audit_Param" />
        <bpmn:sequenceFlow id="Flow_00wgmyl" sourceRef="Activity_Online_Task_Param" targetRef="Gateway_1d57wm5" />
        <bpmn:serviceTask id="Activity_Extra_Online_Create_Galaxy_Page" name="创建并发布天河页面">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="createGalaxyPageInstanceForSubActivityOnline" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0lfu5pc</bpmn:incoming>
            <bpmn:outgoing>Flow_1n89i1m</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1nwv4lv" sourceRef="Activity_Online_Activity_Param" targetRef="Activity_Online_Registration_Param" />
        <bpmn:sequenceFlow id="Flow_0lfu5pc" sourceRef="Activity_Online_Submit_Event" targetRef="Activity_Extra_Online_Create_Galaxy_Page" />
        <bpmn:sequenceFlow id="Flow_1n89i1m" sourceRef="Activity_Extra_Online_Create_Galaxy_Page" targetRef="Activity_Online_Operation_Log" />
        <bpmn:serviceTask id="Activity_Online_Indicator_Config_Check" name="指标配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-service" value="onlineIndicatorConfigCheck" />
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_05w4f3i</bpmn:incoming>
            <bpmn:outgoing>Flow_06xmt08</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_05w4f3i" sourceRef="Activity_Online_Permission_Check" targetRef="Activity_Online_Indicator_Config_Check" />
        <bpmn:sequenceFlow id="Flow_06xmt08" sourceRef="Activity_Online_Indicator_Config_Check" targetRef="Gateway_1b9hdca" />
        <bpmn:serviceTask id="Cycle_Create_Single_Activity" name="创建周期任务">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminExtraAddOnline" />
                    <camunda:property name="task-service" value="cycleCreateSingleActivity" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04nuxd8</bpmn:incoming>
            <bpmn:outgoing>Flow_0liaumy</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0liaumy" sourceRef="Cycle_Create_Single_Activity" targetRef="Activity_Online_Activity_Param" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_Strategy_Admin_New_SubActivity_Online">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_Strategy_Admin_New_Add_Online">
                <dc:Bounds x="192" y="122" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="171" y="86" width="77" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0qnosbv_di" bpmnElement="Activity_Online_StoryBus">
                <dc:Bounds x="160" y="340" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_04d7u3j_di" bpmnElement="Event_Online_End">
                <dc:Bounds x="1613" y="492" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1595" y="535" width="77" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1b9hdca_di" bpmnElement="Gateway_1b9hdca" isMarkerVisible="true">
                <dc:Bounds x="305" y="355" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="529" y="252" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0m82fvj_di" bpmnElement="Gateway_1ip3ooo">
                <dc:Bounds x="445" y="355" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1xlx1qd_di" bpmnElement="Activity_Online_Resource_Check">
                <dc:Bounds x="420" y="220" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1qsejzp_di" bpmnElement="Activity_Online_Basic_Check">
                <dc:Bounds x="160" y="220" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1352bhm" bpmnElement="Activity_Online_Permission_Check">
                <dc:Bounds x="160" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0ff0j4k" bpmnElement="Gateway_1d57wm5" isMarkerVisible="true">
                <dc:Bounds x="810" y="485" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="529" y="252" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_14212yq_di" bpmnElement="Gateway_04136bx">
                <dc:Bounds x="940" y="485" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_13gnxag" bpmnElement="Activity_Online_Task_Param">
                <dc:Bounds x="695" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0qz3kko" bpmnElement="Activity_Online_Audit_Param">
                <dc:Bounds x="915" y="360" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1ijv3qf" bpmnElement="Activity_Online_Batch_Update">
                <dc:Bounds x="1035" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0ik8k68" bpmnElement="Activity_Online_Operation_Log">
                <dc:Bounds x="1441" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0qcc3st" bpmnElement="Activity_Online_Registration_Param">
                <dc:Bounds x="565" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1wacxgb" bpmnElement="Activity_Online_Submit_Event">
                <dc:Bounds x="1175" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0k0puqd_di" bpmnElement="Activity_Extra_Online_Create_Galaxy_Page">
                <dc:Bounds x="1310" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_15qewep_di" bpmnElement="Activity_Online_Indicator_Config_Check">
                <dc:Bounds x="280" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0htlrhi" bpmnElement="Activity_Online_Activity_Param">
                <dc:Bounds x="420" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_11tsjq7" bpmnElement="Cycle_Create_Single_Activity">
                <dc:Bounds x="420" y="470" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_0z4xc7y_di" bpmnElement="Flow_0z4xc7y">
                <di:waypoint x="355" y="380" />
                <di:waypoint x="445" y="380" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="371" y="393" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04lqzaf_di" bpmnElement="Flow_04lqzaf">
                <di:waypoint x="330" y="355" />
                <di:waypoint x="330" y="260" />
                <di:waypoint x="420" y="260" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="350" y="233" width="41" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1gr7v9a_di" bpmnElement="Flow_1gr7v9a">
                <di:waypoint x="470" y="300" />
                <di:waypoint x="470" y="355" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04nuxd8_di" bpmnElement="Flow_04nuxd8">
                <di:waypoint x="470" y="405" />
                <di:waypoint x="470" y="470" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0g792vg_di" bpmnElement="Flow_0g792vg">
                <di:waypoint x="210" y="158" />
                <di:waypoint x="210" y="220" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_05kbmsf_di" bpmnElement="Flow_05kbmsf">
                <di:waypoint x="210" y="420" />
                <di:waypoint x="210" y="470" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_11yyjdd_di" bpmnElement="Flow_11yyjdd">
                <di:waypoint x="210" y="300" />
                <di:waypoint x="210" y="340" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sgnqoj_di" bpmnElement="Flow_0sgnqoj">
                <di:waypoint x="860" y="510" />
                <di:waypoint x="940" y="510" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="873" y="513" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0qmin7b_di" bpmnElement="Flow_0qmin7b">
                <di:waypoint x="965" y="440" />
                <di:waypoint x="965" y="485" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fac7m4_di" bpmnElement="Flow_1fac7m4">
                <di:waypoint x="990" y="510" />
                <di:waypoint x="1035" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0a9cd7n_di" bpmnElement="Flow_0a9cd7n">
                <di:waypoint x="1135" y="510" />
                <di:waypoint x="1175" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_00xogcr_di" bpmnElement="Flow_00xogcr">
                <di:waypoint x="1541" y="510" />
                <di:waypoint x="1613" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fnq454_di" bpmnElement="Flow_1fnq454">
                <di:waypoint x="665" y="510" />
                <di:waypoint x="695" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0cg3in6_di" bpmnElement="Flow_0cg3in6">
                <di:waypoint x="835" y="485" />
                <di:waypoint x="835" y="400" />
                <di:waypoint x="915" y="400" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_00wgmyl_di" bpmnElement="Flow_00wgmyl">
                <di:waypoint x="795" y="510" />
                <di:waypoint x="810" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1nwv4lv_di" bpmnElement="Flow_1nwv4lv">
                <di:waypoint x="520" y="640" />
                <di:waypoint x="543" y="640" />
                <di:waypoint x="543" y="510" />
                <di:waypoint x="565" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0lfu5pc_di" bpmnElement="Flow_0lfu5pc">
                <di:waypoint x="1275" y="510" />
                <di:waypoint x="1310" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1n89i1m_di" bpmnElement="Flow_1n89i1m">
                <di:waypoint x="1410" y="510" />
                <di:waypoint x="1441" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_05w4f3i_di" bpmnElement="Flow_05w4f3i">
                <di:waypoint x="260" y="510" />
                <di:waypoint x="280" y="510" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_06xmt08_di" bpmnElement="Flow_06xmt08">
                <di:waypoint x="330" y="470" />
                <di:waypoint x="330" y="405" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0liaumy_di" bpmnElement="Flow_0liaumy">
                <di:waypoint x="470" y="550" />
                <di:waypoint x="470" y="600" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
