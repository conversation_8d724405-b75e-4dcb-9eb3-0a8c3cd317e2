<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0z6o3bg"
                  targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.12.1"
                  modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
    <bpmn:process id="Process_Strategy_Admin_Online" name="策略活动后台上线流程" isExecutable="true">
        <bpmn:extensionElements />
        <bpmn:startEvent id="StartEvent_Strategy_Admin_Online" name="策略后台上线开始">
            <bpmn:outgoing>Flow_0g792vg</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:serviceTask id="Activity_Online_StoryBus" name="StoryBus构造">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineBuildStoryBus" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_11yyjdd</bpmn:incoming>
            <bpmn:outgoing>Flow_05kbmsf</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:endEvent id="Event_Online_End" name="策略后台上线结束">
            <bpmn:incoming>Flow_00xogcr</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:exclusiveGateway id="Gateway_1b9hdca">
            <bpmn:incoming>Flow_187co5a</bpmn:incoming>
            <bpmn:outgoing>Flow_0z4xc7y</bpmn:outgoing>
            <bpmn:outgoing>Flow_04lqzaf</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0z4xc7y" name="不涉及B补" sourceRef="Gateway_1b9hdca" targetRef="Gateway_1ip3ooo">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_1ip3ooo">
            <bpmn:incoming>Flow_0z4xc7y</bpmn:incoming>
            <bpmn:incoming>Flow_1gr7v9a</bpmn:incoming>
            <bpmn:outgoing>Flow_1hvhsz0</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_04lqzaf" name="涉及B补" sourceRef="Gateway_1b9hdca" targetRef="Activity_Online_Resource_Check">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_Online_Resource_Check" name="横向信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineResourceInfoCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04lqzaf</bpmn:incoming>
            <bpmn:outgoing>Flow_1gr7v9a</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1gr7v9a" sourceRef="Activity_Online_Resource_Check" targetRef="Gateway_1ip3ooo" />
        <bpmn:sequenceFlow id="Flow_0g792vg" sourceRef="StartEvent_Strategy_Admin_Online" targetRef="Activity_Online_Basic_Check" />
        <bpmn:serviceTask id="Activity_Online_Basic_Check" name="输入校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineBasicCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0g792vg</bpmn:incoming>
            <bpmn:outgoing>Flow_11yyjdd</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_Online_Permission_Check" name="权限校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlinePermissionCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_05kbmsf</bpmn:incoming>
            <bpmn:outgoing>Flow_1171ukm</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_05kbmsf" sourceRef="Activity_Online_StoryBus" targetRef="Activity_Online_Permission_Check" />
        <bpmn:sequenceFlow id="Flow_11yyjdd" sourceRef="Activity_Online_Basic_Check" targetRef="Activity_Online_StoryBus" />
        <bpmn:serviceTask id="Activity_Online_Activity_Param" name="构建活动表更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateActivityParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0x5mmxa</bpmn:incoming>
            <bpmn:outgoing>Flow_1sox4eu</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1sox4eu" sourceRef="Activity_Online_Activity_Param" targetRef="Activity_Online_Create_Galaxy_Page" />
        <bpmn:exclusiveGateway id="Gateway_1d57wm5">
            <bpmn:incoming>Flow_125kiv1</bpmn:incoming>
            <bpmn:outgoing>Flow_0sgnqoj</bpmn:outgoing>
            <bpmn:outgoing>Flow_1l2yflq</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_0sgnqoj" name="不涉及B补" sourceRef="Gateway_1d57wm5" targetRef="Gateway_04136bx">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:inclusiveGateway id="Gateway_04136bx">
            <bpmn:incoming>Flow_0sgnqoj</bpmn:incoming>
            <bpmn:incoming>Flow_0qmin7b</bpmn:incoming>
            <bpmn:incoming>Flow_13nebig</bpmn:incoming>
            <bpmn:outgoing>Flow_1fac7m4</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1l2yflq" name="涉及B补" sourceRef="Gateway_1d57wm5" targetRef="Activity_Online_Task_Param">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.onlineStoryBus.businessSupply
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_Online_Task_Param" name="任务更新反绑横向规则">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateTaskParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1l2yflq</bpmn:incoming>
            <bpmn:outgoing>Flow_16idzna</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_16idzna" sourceRef="Activity_Online_Task_Param" targetRef="Activity_Online_Audit_Param" />
        <bpmn:serviceTask id="Activity_Online_Audit_Param" name="审批更新填充风控保护码">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateAuditParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_16idzna</bpmn:incoming>
            <bpmn:outgoing>Flow_0qmin7b</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0qmin7b" sourceRef="Activity_Online_Audit_Param" targetRef="Gateway_04136bx" />
        <bpmn:sequenceFlow id="Flow_1fac7m4" sourceRef="Gateway_04136bx" targetRef="Activity_Online_Batch_Update" />
        <bpmn:serviceTask id="Activity_Online_Batch_Update" name="按参数批量更新各配置表">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineActivityTaskUpdate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fac7m4</bpmn:incoming>
            <bpmn:outgoing>Flow_0a9cd7n</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0a9cd7n" sourceRef="Activity_Online_Batch_Update" targetRef="Activity_Online_Operation_Log" />
        <bpmn:serviceTask id="Activity_Online_Operation_Log" name="操作日志记录">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineOperateLog" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0a9cd7n</bpmn:incoming>
            <bpmn:outgoing>Flow_00xogcr</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_00xogcr" sourceRef="Activity_Online_Operation_Log" targetRef="Event_Online_End" />
        <bpmn:sequenceFlow id="Flow_1171ukm" sourceRef="Activity_Online_Permission_Check" targetRef="Activity_Online_Crowd_Check" />
        <bpmn:serviceTask id="Activity_Online_Registration_Param" name="报名表生效更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateRegistrationParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1jtisqm</bpmn:incoming>
            <bpmn:outgoing>Flow_1fnq454</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_13geri6" name="构建奖励配置更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateAwardParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1fnq454</bpmn:incoming>
            <bpmn:outgoing>Flow_125kiv1</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1fnq454" sourceRef="Activity_Online_Registration_Param" targetRef="Activity_13geri6" />
        <bpmn:serviceTask id="Activity_Online_Create_Galaxy_Page" name="创建并发布天河页面">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="createGalaxyPageInstanceForOnline" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1sox4eu</bpmn:incoming>
            <bpmn:outgoing>Flow_1jtisqm</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1jtisqm" sourceRef="Activity_Online_Create_Galaxy_Page" targetRef="Activity_Online_Registration_Param" />
        <bpmn:exclusiveGateway id="Gateway_1sutqpn">
            <bpmn:incoming>Flow_1hvhsz0</bpmn:incoming>
            <bpmn:outgoing>Flow_0x5mmxa</bpmn:outgoing>
            <bpmn:outgoing>Flow_0s6wpfj</bpmn:outgoing>
        </bpmn:exclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1hvhsz0" sourceRef="Gateway_1ip3ooo" targetRef="Gateway_1sutqpn" />
        <bpmn:sequenceFlow id="Flow_0x5mmxa" name="非极速版" sourceRef="Gateway_1sutqpn" targetRef="Activity_Online_Activity_Param">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">!sta.onlineStoryBus.liteFlag
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_0s6wpfj" name="极速版" sourceRef="Gateway_1sutqpn" targetRef="Activity_Online_Lite_Activity_Param">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.onlineStoryBus.liteFlag
            </bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_Lite_Online_Registration_Param" name="报名表生效更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateRegistrationParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0006nly</bpmn:incoming>
            <bpmn:outgoing>Flow_13nebig</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_125kiv1" sourceRef="Activity_13geri6" targetRef="Gateway_1d57wm5" />
        <bpmn:sequenceFlow id="Flow_13nebig" sourceRef="Activity_Lite_Online_Registration_Param" targetRef="Gateway_04136bx" />
        <bpmn:serviceTask id="Activity_Online_Lite_Activity_Param" name="构建活动表更新参数">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineUpdateActivityParam" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0s6wpfj</bpmn:incoming>
            <bpmn:outgoing>Flow_0006nly</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0006nly" sourceRef="Activity_Online_Lite_Activity_Param" targetRef="Activity_Lite_Online_Registration_Param" />
        <bpmn:serviceTask id="Activity_Online_Indicator_Config_Check" name="指标配置校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineIndicatorConfigCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1lz836m</bpmn:incoming>
            <bpmn:outgoing>Flow_187co5a</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_187co5a" sourceRef="Activity_Online_Indicator_Config_Check" targetRef="Gateway_1b9hdca" />
        <bpmn:serviceTask id="Activity_Online_Crowd_Check" name="人群包就绪校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="strategyAdminOnline" />
                    <camunda:property name="task-service" value="onlineCrowdReadyCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1171ukm</bpmn:incoming>
            <bpmn:outgoing>Flow_1lz836m</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1lz836m" sourceRef="Activity_Online_Crowd_Check" targetRef="Activity_Online_Indicator_Config_Check" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_Strategy_Admin_Online">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_Strategy_Admin_Online">
                <dc:Bounds x="192" y="122" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="166" y="98" width="88" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0qnosbv_di" bpmnElement="Activity_Online_StoryBus">
                <dc:Bounds x="160" y="350" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_04d7u3j_di" bpmnElement="Event_Online_End">
                <dc:Bounds x="1792" y="622" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1768" y="665" width="88" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1b9hdca_di" bpmnElement="Gateway_1b9hdca" isMarkerVisible="true">
                <dc:Bounds x="305" y="505" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="529" y="252" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0m82fvj_di" bpmnElement="Gateway_1ip3ooo">
                <dc:Bounds x="445" y="445" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1xlx1qd_di" bpmnElement="Activity_Online_Resource_Check">
                <dc:Bounds x="420" y="350" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1qsejzp_di" bpmnElement="Activity_Online_Basic_Check">
                <dc:Bounds x="160" y="210" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1352bhm" bpmnElement="Activity_Online_Permission_Check">
                <dc:Bounds x="160" y="490" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0htlrhi" bpmnElement="Activity_Online_Activity_Param">
                <dc:Bounds x="420" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0ff0j4k" bpmnElement="Gateway_1d57wm5" isMarkerVisible="true">
                <dc:Bounds x="1045" y="615" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="529" y="252" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_14212yq_di" bpmnElement="Gateway_04136bx">
                <dc:Bounds x="1203" y="615" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_13gnxag" bpmnElement="Activity_Online_Task_Param">
                <dc:Bounds x="1020" y="710" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0qz3kko" bpmnElement="Activity_Online_Audit_Param">
                <dc:Bounds x="1178" y="710" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1ijv3qf" bpmnElement="Activity_Online_Batch_Update">
                <dc:Bounds x="1390" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0ik8k68" bpmnElement="Activity_Online_Operation_Log">
                <dc:Bounds x="1600" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0qcc3st" bpmnElement="Activity_Online_Registration_Param">
                <dc:Bounds x="738" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_13geri6_di" bpmnElement="Activity_13geri6">
                <dc:Bounds x="888" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1dhmzpz" bpmnElement="Activity_Online_Create_Galaxy_Page">
                <dc:Bounds x="580" y="600" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0zndx5i" bpmnElement="Gateway_1sutqpn" isMarkerVisible="true">
                <dc:Bounds x="445" y="515" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="529" y="252" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_1yiceiq" bpmnElement="Activity_Lite_Online_Registration_Param">
                <dc:Bounds x="1178" y="500" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0p4kqvf" bpmnElement="Activity_Online_Lite_Activity_Param">
                <dc:Bounds x="760" y="500" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0f4zwdb_di" bpmnElement="Activity_Online_Indicator_Config_Check">
                <dc:Bounds x="280" y="620" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="BPMNShape_0uc0a4n" bpmnElement="Activity_Online_Crowd_Check">
                <dc:Bounds x="160" y="620" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_0z4xc7y_di" bpmnElement="Flow_0z4xc7y">
                <di:waypoint x="330" y="505" />
                <di:waypoint x="330" y="470" />
                <di:waypoint x="445" y="470" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="356" y="483" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04lqzaf_di" bpmnElement="Flow_04lqzaf">
                <di:waypoint x="330" y="505" />
                <di:waypoint x="330" y="390" />
                <di:waypoint x="420" y="390" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="350" y="363" width="41" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1gr7v9a_di" bpmnElement="Flow_1gr7v9a">
                <di:waypoint x="470" y="430" />
                <di:waypoint x="470" y="445" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0g792vg_di" bpmnElement="Flow_0g792vg">
                <di:waypoint x="210" y="158" />
                <di:waypoint x="210" y="210" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_05kbmsf_di" bpmnElement="Flow_05kbmsf">
                <di:waypoint x="210" y="430" />
                <di:waypoint x="210" y="490" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_11yyjdd_di" bpmnElement="Flow_11yyjdd">
                <di:waypoint x="210" y="290" />
                <di:waypoint x="210" y="350" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1sox4eu_di" bpmnElement="Flow_1sox4eu">
                <di:waypoint x="520" y="640" />
                <di:waypoint x="580" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sgnqoj_di" bpmnElement="Flow_0sgnqoj">
                <di:waypoint x="1095" y="640" />
                <di:waypoint x="1203" y="640" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1117" y="653" width="52" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1l2yflq_di" bpmnElement="Flow_1l2yflq">
                <di:waypoint x="1070" y="665" />
                <di:waypoint x="1070" y="710" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1020" y="672" width="41" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_16idzna_di" bpmnElement="Flow_16idzna">
                <di:waypoint x="1120" y="750" />
                <di:waypoint x="1178" y="750" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0qmin7b_di" bpmnElement="Flow_0qmin7b">
                <di:waypoint x="1228" y="710" />
                <di:waypoint x="1228" y="665" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fac7m4_di" bpmnElement="Flow_1fac7m4">
                <di:waypoint x="1253" y="640" />
                <di:waypoint x="1390" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0a9cd7n_di" bpmnElement="Flow_0a9cd7n">
                <di:waypoint x="1490" y="640" />
                <di:waypoint x="1600" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_00xogcr_di" bpmnElement="Flow_00xogcr">
                <di:waypoint x="1700" y="640" />
                <di:waypoint x="1792" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1171ukm_di" bpmnElement="Flow_1171ukm">
                <di:waypoint x="210" y="570" />
                <di:waypoint x="210" y="620" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1fnq454_di" bpmnElement="Flow_1fnq454">
                <di:waypoint x="838" y="640" />
                <di:waypoint x="888" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1jtisqm_di" bpmnElement="Flow_1jtisqm">
                <di:waypoint x="680" y="640" />
                <di:waypoint x="738" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1hvhsz0_di" bpmnElement="Flow_1hvhsz0">
                <di:waypoint x="470" y="495" />
                <di:waypoint x="470" y="515" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0x5mmxa_di" bpmnElement="Flow_0x5mmxa">
                <di:waypoint x="470" y="565" />
                <di:waypoint x="470" y="600" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="463" y="582" width="44" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0s6wpfj_di" bpmnElement="Flow_0s6wpfj">
                <di:waypoint x="495" y="540" />
                <di:waypoint x="760" y="540" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="680" y="522" width="33" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_125kiv1_di" bpmnElement="Flow_125kiv1">
                <di:waypoint x="988" y="640" />
                <di:waypoint x="1045" y="640" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_13nebig_di" bpmnElement="Flow_13nebig">
                <di:waypoint x="1228" y="580" />
                <di:waypoint x="1228" y="615" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0006nly_di" bpmnElement="Flow_0006nly">
                <di:waypoint x="860" y="540" />
                <di:waypoint x="1178" y="540" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_187co5a_di" bpmnElement="Flow_187co5a">
                <di:waypoint x="330" y="620" />
                <di:waypoint x="330" y="555" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1lz836m_di" bpmnElement="Flow_1lz836m">
                <di:waypoint x="260" y="660" />
                <di:waypoint x="280" y="660" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
