<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0g99jp5" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
    <bpmn:process id="Process_Admin_Save" name="纵向活动保存流程" isExecutable="true">
        <bpmn:startEvent id="StartEvent_Admin_Save" name="纵向后台保存开始">
            <bpmn:outgoing>Flow_04h7tvm</bpmn:outgoing>
        </bpmn:startEvent>
        <bpmn:sequenceFlow id="Flow_04h7tvm" sourceRef="StartEvent_Admin_Save" targetRef="Activity_Basic_Validate" />
        <bpmn:serviceTask id="Activity_Basic_Validate" name="输入校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveBasicCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04h7tvm</bpmn:incoming>
            <bpmn:outgoing>Flow_0sqr7iy</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0sqr7iy" sourceRef="Activity_Basic_Validate" targetRef="Activity_Assembler" />
        <bpmn:serviceTask id="Activity_Assembler" name="参数信息StoryBus构造">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveAssembler" />
                    <camunda:property name="task-service" value="saveAssemblerStoryBus" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0sqr7iy</bpmn:incoming>
            <bpmn:outgoing>Flow_0cxhli0</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:endEvent id="Event_1" name="纵向后台保存结束">
            <bpmn:incoming>Flow_0o9niq6</bpmn:incoming>
        </bpmn:endEvent>
        <bpmn:sequenceFlow id="Flow_096egto" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_CrowdDupCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_CrowdDupCheck" name="分层人群重叠校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveCrowdDupCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_096egto</bpmn:incoming>
            <bpmn:outgoing>Flow_01rhf8e</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1lij318" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_CrowdMatchCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_CrowdMatchCheck" name="固定值excel和人群匹配校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveExcelCrowdMatchCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1lij318</bpmn:incoming>
            <bpmn:outgoing>Flow_0i5b2xa</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1hcnntn" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_PhaseTargetCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_PhaseTargetCheck" name="目标递增/目标类型唯一/指标有效">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveTargetCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1hcnntn</bpmn:incoming>
            <bpmn:outgoing>Flow_02mxtsg</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_17uxvte" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_CategoryCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_CategoryCheck" name="指标类目校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveCategoryCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_17uxvte</bpmn:incoming>
            <bpmn:outgoing>Flow_0ef9d85</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_05m6sa8" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_ItemGroupCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_ItemGroupCheck" name="商品筛选包校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveItemGroupCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_05m6sa8</bpmn:incoming>
            <bpmn:outgoing>Flow_1xg2pwv</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_01rhf8e" sourceRef="Activity_CrowdDupCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_0i5b2xa" sourceRef="Activity_CrowdMatchCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_02mxtsg" sourceRef="Activity_PhaseTargetCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_0ef9d85" sourceRef="Activity_CategoryCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_1xg2pwv" sourceRef="Activity_ItemGroupCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:serviceTask id="Activity_BuildActivity" name="拼装活动信息">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveAssembler" />
                    <camunda:property name="task-service" value="saveAssemblerActivityRecord" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1vqirpn</bpmn:incoming>
            <bpmn:outgoing>Flow_187cfx1</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1tqkzyd" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_ResourceCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_ResourceCheck" name="横向信息校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveResourceCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1tqkzyd</bpmn:incoming>
            <bpmn:outgoing>Flow_16ba5zi</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_16ba5zi" sourceRef="Activity_ResourceCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:serviceTask id="Activity_BuildTask" name="拼装任务信息">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveAssembler" />
                    <camunda:property name="task-service" value="saveAssemblerTaskManage" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1ukxi25</bpmn:incoming>
            <bpmn:outgoing>Flow_1mbsptj</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_Create" name="活动创建/更新">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveCreate" />
                    <camunda:property name="task-service" value="saveActivityCreate" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0b7hzjp</bpmn:incoming>
            <bpmn:outgoing>Flow_0635pc3</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1x00827" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_BasicTimeCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_BasicTimeCheck" name="基期时间校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveBasicTimeCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1x00827</bpmn:incoming>
            <bpmn:outgoing>Flow_14xxmw3</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_14xxmw3" sourceRef="Activity_BasicTimeCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_04619b8" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_ActivityStatusCheck" />
        <bpmn:serviceTask id="Activity_ActivityStatusCheck" name="活动状态校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveActivityStatusCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_04619b8</bpmn:incoming>
            <bpmn:outgoing>Flow_028qee0</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:inclusiveGateway id="Gateway_Assemble" name="并行构建任务模型">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0cxhli0</bpmn:incoming>
            <bpmn:outgoing>Flow_1vqirpn</bpmn:outgoing>
            <bpmn:outgoing>Flow_1ukxi25</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:inclusiveGateway id="Gateway_Business_Validate_Start" name="并行校验开启">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1mbsptj</bpmn:incoming>
            <bpmn:incoming>Flow_187cfx1</bpmn:incoming>
            <bpmn:outgoing>Flow_096egto</bpmn:outgoing>
            <bpmn:outgoing>Flow_1lij318</bpmn:outgoing>
            <bpmn:outgoing>Flow_1hcnntn</bpmn:outgoing>
            <bpmn:outgoing>Flow_17uxvte</bpmn:outgoing>
            <bpmn:outgoing>Flow_05m6sa8</bpmn:outgoing>
            <bpmn:outgoing>Flow_1tqkzyd</bpmn:outgoing>
            <bpmn:outgoing>Flow_1x00827</bpmn:outgoing>
            <bpmn:outgoing>Flow_04619b8</bpmn:outgoing>
            <bpmn:outgoing>Flow_0xwkzm4</bpmn:outgoing>
            <bpmn:outgoing>Flow_0g1r6wk</bpmn:outgoing>
            <bpmn:outgoing>Flow_0x57u0z</bpmn:outgoing>
            <bpmn:outgoing>Flow_1izoci5</bpmn:outgoing>
            <bpmn:outgoing>Flow_1pna9x7</bpmn:outgoing>
            <bpmn:outgoing>Flow_1cibfi1</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:inclusiveGateway id="Gateway_Business_Validate_End" name="并行校验结束">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_01rhf8e</bpmn:incoming>
            <bpmn:incoming>Flow_0i5b2xa</bpmn:incoming>
            <bpmn:incoming>Flow_02mxtsg</bpmn:incoming>
            <bpmn:incoming>Flow_0ef9d85</bpmn:incoming>
            <bpmn:incoming>Flow_1xg2pwv</bpmn:incoming>
            <bpmn:incoming>Flow_16ba5zi</bpmn:incoming>
            <bpmn:incoming>Flow_14xxmw3</bpmn:incoming>
            <bpmn:incoming>Flow_1357dlv</bpmn:incoming>
            <bpmn:incoming>Flow_0p0et5k</bpmn:incoming>
            <bpmn:incoming>Flow_028qee0</bpmn:incoming>
            <bpmn:incoming>Flow_0gtr14h</bpmn:incoming>
            <bpmn:incoming>Flow_0h8pa1z</bpmn:incoming>
            <bpmn:incoming>Flow_1mvhv6m</bpmn:incoming>
            <bpmn:incoming>Flow_1vqkmb7</bpmn:incoming>
            <bpmn:outgoing>Flow_0b7hzjp</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:serviceTask id="Activity_OperateLog" name="记录操作日志">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveUtils" />
                    <camunda:property name="task-service" value="saveOperateLog" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_07cm39v</bpmn:incoming>
            <bpmn:outgoing>Flow_12euygk</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0635pc3" sourceRef="Activity_Create" targetRef="Gateway_CompleteProcess_Begin" />
        <bpmn:inclusiveGateway id="Gateway_CompleteProcess_Begin" name="后置流程处理">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="open-async" value="true" />
                    <camunda:property name="strict-mode" value="false" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0635pc3</bpmn:incoming>
            <bpmn:outgoing>Flow_07cm39v</bpmn:outgoing>
            <bpmn:outgoing>Flow_1r61o82</bpmn:outgoing>
            <bpmn:outgoing>Flow_1f4za9d</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_07cm39v" sourceRef="Gateway_CompleteProcess_Begin" targetRef="Activity_OperateLog" />
        <bpmn:sequenceFlow id="Flow_1r61o82" name="sta.industrySave.operateType != 3" sourceRef="Gateway_CompleteProcess_Begin" targetRef="Activity_1e1tgbj">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_1e1tgbj" name="发送kim通知">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveUtils" />
                    <camunda:property name="task-service" value="saveKimNotice" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1r61o82</bpmn:incoming>
            <bpmn:outgoing>Flow_1ks8wt4</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_12euygk" sourceRef="Activity_OperateLog" targetRef="Gateway_CompleteProcess_End" />
        <bpmn:inclusiveGateway id="Gateway_CompleteProcess_End" name="后置流程结束">
            <bpmn:incoming>Flow_12euygk</bpmn:incoming>
            <bpmn:incoming>Flow_1ks8wt4</bpmn:incoming>
            <bpmn:incoming>Flow_0ig1cvb</bpmn:incoming>
            <bpmn:outgoing>Flow_0o9niq6</bpmn:outgoing>
        </bpmn:inclusiveGateway>
        <bpmn:sequenceFlow id="Flow_1ks8wt4" sourceRef="Activity_1e1tgbj" targetRef="Gateway_CompleteProcess_End" />
        <bpmn:sequenceFlow id="Flow_0o9niq6" sourceRef="Gateway_CompleteProcess_End" targetRef="Event_1" />
        <bpmn:sequenceFlow id="Flow_1f4za9d" sourceRef="Gateway_CompleteProcess_Begin" targetRef="Activity_1f33zbs" />
        <bpmn:serviceTask id="Activity_1f33zbs" name="结果组装">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveUtils" />
                    <camunda:property name="task-service" value="saveBuildResult" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1f4za9d</bpmn:incoming>
            <bpmn:outgoing>Flow_0ig1cvb</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0ig1cvb" sourceRef="Activity_1f33zbs" targetRef="Gateway_CompleteProcess_End" />
        <bpmn:sequenceFlow id="Flow_0xwkzm4" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_BaseExcelCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_BaseExcelCheck" name="自定义基期表头/内容校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveBaseExcelCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0xwkzm4</bpmn:incoming>
            <bpmn:outgoing>Flow_1357dlv</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1357dlv" sourceRef="Activity_BaseExcelCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_0g1r6wk" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_BaseIndicatorCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_BaseIndicatorCheck" name="基期指标完整性校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveBaseIndicatorCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0g1r6wk</bpmn:incoming>
            <bpmn:outgoing>Flow_0p0et5k</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0p0et5k" sourceRef="Activity_BaseIndicatorCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_0cxhli0" sourceRef="Activity_Assembler" targetRef="Gateway_Assemble" />
        <bpmn:sequenceFlow id="Flow_1vqirpn" sourceRef="Gateway_Assemble" targetRef="Activity_BuildActivity" />
        <bpmn:sequenceFlow id="Flow_1ukxi25" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Assemble" targetRef="Activity_BuildTask">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1mbsptj" sourceRef="Activity_BuildTask" targetRef="Gateway_Business_Validate_Start" />
        <bpmn:sequenceFlow id="Flow_187cfx1" sourceRef="Activity_BuildActivity" targetRef="Gateway_Business_Validate_Start" />
        <bpmn:sequenceFlow id="Flow_028qee0" sourceRef="Activity_ActivityStatusCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_0b7hzjp" sourceRef="Gateway_Business_Validate_End" targetRef="Activity_Create" />
        <bpmn:sequenceFlow id="Flow_0x57u0z" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_RuleCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_RuleCheck" name="自定义规则校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveCustomizeRuleCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_0x57u0z</bpmn:incoming>
            <bpmn:outgoing>Flow_0gtr14h</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0gtr14h" sourceRef="Activity_RuleCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_1izoci5" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_RankAwardRangeCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_RankAwardRangeCheck" name="排行榜奖励范围校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveRankAwardRangeCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1izoci5</bpmn:incoming>
            <bpmn:outgoing>Flow_0h8pa1z</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_0h8pa1z" sourceRef="Activity_RankAwardRangeCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_1pna9x7" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_ExpireTimeCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:sequenceFlow id="Flow_1cibfi1" name="sta.industrySave.operateType != 3" sourceRef="Gateway_Business_Validate_Start" targetRef="Activity_TaskPatternCustomizeCheck">
            <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">sta.industrySave.operateType != 3</bpmn:conditionExpression>
        </bpmn:sequenceFlow>
        <bpmn:serviceTask id="Activity_ExpireTimeCheck" name="返点指标过期时间校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveReturnIndicatorExpireCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1pna9x7</bpmn:incoming>
            <bpmn:outgoing>Flow_1mvhv6m</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:serviceTask id="Activity_TaskPatternCustomizeCheck" name="活动玩法定制化必须指标校验">
            <bpmn:extensionElements>
                <camunda:properties>
                    <camunda:property name="task-component" value="adminSaveValidator" />
                    <camunda:property name="task-service" value="saveTaskPatternCustomizeCheck" />
                </camunda:properties>
            </bpmn:extensionElements>
            <bpmn:incoming>Flow_1cibfi1</bpmn:incoming>
            <bpmn:outgoing>Flow_1vqkmb7</bpmn:outgoing>
        </bpmn:serviceTask>
        <bpmn:sequenceFlow id="Flow_1mvhv6m" sourceRef="Activity_ExpireTimeCheck" targetRef="Gateway_Business_Validate_End" />
        <bpmn:sequenceFlow id="Flow_1vqkmb7" sourceRef="Activity_TaskPatternCustomizeCheck" targetRef="Gateway_Business_Validate_End" />
    </bpmn:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_1">
        <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_Admin_Save">
            <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_Admin_Save">
                <dc:Bounds x="182" y="1279" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="159" y="1322" width="88" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0nf27jb_di" bpmnElement="Activity_Basic_Validate">
                <dc:Bounds x="290" y="1257" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0uwcif1_di" bpmnElement="Activity_Assembler">
                <dc:Bounds x="450" y="1257" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_1v9v3ov_di" bpmnElement="Event_1">
                <dc:Bounds x="2182" y="1279" width="36" height="36" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2157" y="1322" width="88" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0wb9puo_di" bpmnElement="Activity_CrowdDupCheck">
                <dc:Bounds x="1160" y="1070" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0gs58k5_di" bpmnElement="Activity_CrowdMatchCheck">
                <dc:Bounds x="1160" y="1160" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1lh3vhd_di" bpmnElement="Activity_PhaseTargetCheck">
                <dc:Bounds x="1160" y="980" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_06zbupx_di" bpmnElement="Activity_CategoryCheck">
                <dc:Bounds x="1160" y="890" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0xycako_di" bpmnElement="Activity_ItemGroupCheck">
                <dc:Bounds x="1160" y="800" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_14dxwrn_di" bpmnElement="Activity_BuildActivity">
                <dc:Bounds x="750" y="1257" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0qpklvx_di" bpmnElement="Activity_ResourceCheck">
                <dc:Bounds x="1160" y="710" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_07p8oow_di" bpmnElement="Activity_BuildTask">
                <dc:Bounds x="750" y="1160" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0jc13cf_di" bpmnElement="Activity_Create">
                <dc:Bounds x="1530" y="1257" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1u42pa8_di" bpmnElement="Activity_BasicTimeCheck">
                <dc:Bounds x="1160" y="620" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0t2wmmm_di" bpmnElement="Activity_ActivityStatusCheck">
                <dc:Bounds x="1160" y="1257" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1mb2sy7_di" bpmnElement="Gateway_Assemble">
                <dc:Bounds x="625" y="1272" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="609" y="1329" width="88" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_08hppe7_di" bpmnElement="Gateway_Business_Validate_Start">
                <dc:Bounds x="1005" y="1272" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="997" y="1332" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0uk7kn7_di" bpmnElement="Gateway_Business_Validate_End">
                <dc:Bounds x="1365" y="1272" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1357" y="1332" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0j2nrgt_di" bpmnElement="Activity_OperateLog">
                <dc:Bounds x="1850" y="1160" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_1oh8gnn_di" bpmnElement="Gateway_CompleteProcess_Begin">
                <dc:Bounds x="1705" y="1272" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1701" y="1329" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0g15clz_di" bpmnElement="Activity_1e1tgbj">
                <dc:Bounds x="1850" y="1060" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_0buskav_di" bpmnElement="Gateway_CompleteProcess_End">
                <dc:Bounds x="2045" y="1272" width="50" height="50" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="2038" y="1329" width="66" height="14" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0m62jxx_di" bpmnElement="Activity_1f33zbs">
                <dc:Bounds x="1850" y="1257" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0hefxsv_di" bpmnElement="Activity_BaseExcelCheck">
                <dc:Bounds x="1160" y="530" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1qi29s2_di" bpmnElement="Activity_BaseIndicatorCheck">
                <dc:Bounds x="1160" y="440" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1hdtipt_di" bpmnElement="Activity_RuleCheck">
                <dc:Bounds x="1160" y="350" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_07boyob_di" bpmnElement="Activity_RankAwardRangeCheck">
                <dc:Bounds x="1160" y="260" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_1vt223x_di" bpmnElement="Activity_ExpireTimeCheck">
                <dc:Bounds x="1160" y="170" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_023kltn_di" bpmnElement="Activity_TaskPatternCustomizeCheck">
                <dc:Bounds x="1160" y="80" width="100" height="80" />
                <bpmndi:BPMNLabel />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="Flow_04h7tvm_di" bpmnElement="Flow_04h7tvm">
                <di:waypoint x="218" y="1297" />
                <di:waypoint x="290" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0sqr7iy_di" bpmnElement="Flow_0sqr7iy">
                <di:waypoint x="390" y="1297" />
                <di:waypoint x="450" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_096egto_di" bpmnElement="Flow_096egto">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="1110" />
                <di:waypoint x="1160" y="1110" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="1076" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1lij318_di" bpmnElement="Flow_1lij318">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="1200" />
                <di:waypoint x="1160" y="1200" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="1156" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1hcnntn_di" bpmnElement="Flow_1hcnntn">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="1020" />
                <di:waypoint x="1160" y="1020" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="979" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_17uxvte_di" bpmnElement="Flow_17uxvte">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="930" />
                <di:waypoint x="1160" y="930" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="896" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_05m6sa8_di" bpmnElement="Flow_05m6sa8">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="840" />
                <di:waypoint x="1160" y="840" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="806" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_01rhf8e_di" bpmnElement="Flow_01rhf8e">
                <di:waypoint x="1260" y="1110" />
                <di:waypoint x="1390" y="1110" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0i5b2xa_di" bpmnElement="Flow_0i5b2xa">
                <di:waypoint x="1260" y="1200" />
                <di:waypoint x="1390" y="1200" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_02mxtsg_di" bpmnElement="Flow_02mxtsg">
                <di:waypoint x="1260" y="1020" />
                <di:waypoint x="1390" y="1020" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ef9d85_di" bpmnElement="Flow_0ef9d85">
                <di:waypoint x="1260" y="930" />
                <di:waypoint x="1390" y="930" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1xg2pwv_di" bpmnElement="Flow_1xg2pwv">
                <di:waypoint x="1260" y="840" />
                <di:waypoint x="1390" y="840" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1tqkzyd_di" bpmnElement="Flow_1tqkzyd">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="750" />
                <di:waypoint x="1160" y="750" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="716" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_16ba5zi_di" bpmnElement="Flow_16ba5zi">
                <di:waypoint x="1260" y="750" />
                <di:waypoint x="1390" y="750" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1x00827_di" bpmnElement="Flow_1x00827">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="660" />
                <di:waypoint x="1160" y="660" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="626" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_14xxmw3_di" bpmnElement="Flow_14xxmw3">
                <di:waypoint x="1260" y="660" />
                <di:waypoint x="1390" y="660" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_04619b8_di" bpmnElement="Flow_04619b8">
                <di:waypoint x="1055" y="1297" />
                <di:waypoint x="1160" y="1297" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="685" y="446" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0635pc3_di" bpmnElement="Flow_0635pc3">
                <di:waypoint x="1630" y="1297" />
                <di:waypoint x="1705" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_07cm39v_di" bpmnElement="Flow_07cm39v">
                <di:waypoint x="1730" y="1272" />
                <di:waypoint x="1730" y="1200" />
                <di:waypoint x="1850" y="1200" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1r61o82_di" bpmnElement="Flow_1r61o82">
                <di:waypoint x="1730" y="1272" />
                <di:waypoint x="1730" y="1100" />
                <di:waypoint x="1850" y="1100" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1732" y="1056" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_12euygk_di" bpmnElement="Flow_12euygk">
                <di:waypoint x="1950" y="1200" />
                <di:waypoint x="2070" y="1200" />
                <di:waypoint x="2070" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ks8wt4_di" bpmnElement="Flow_1ks8wt4">
                <di:waypoint x="1950" y="1100" />
                <di:waypoint x="2070" y="1100" />
                <di:waypoint x="2070" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0o9niq6_di" bpmnElement="Flow_0o9niq6">
                <di:waypoint x="2095" y="1297" />
                <di:waypoint x="2182" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1f4za9d_di" bpmnElement="Flow_1f4za9d">
                <di:waypoint x="1755" y="1297" />
                <di:waypoint x="1850" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0ig1cvb_di" bpmnElement="Flow_0ig1cvb">
                <di:waypoint x="1950" y="1297" />
                <di:waypoint x="2045" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0xwkzm4_di" bpmnElement="Flow_0xwkzm4">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="570" />
                <di:waypoint x="1160" y="570" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1057" y="536" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1357dlv_di" bpmnElement="Flow_1357dlv">
                <di:waypoint x="1260" y="570" />
                <di:waypoint x="1390" y="570" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0g1r6wk_di" bpmnElement="Flow_0g1r6wk">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="480" />
                <di:waypoint x="1160" y="480" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1057" y="446" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0p0et5k_di" bpmnElement="Flow_0p0et5k">
                <di:waypoint x="1260" y="480" />
                <di:waypoint x="1390" y="480" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0cxhli0_di" bpmnElement="Flow_0cxhli0">
                <di:waypoint x="550" y="1297" />
                <di:waypoint x="625" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1vqirpn_di" bpmnElement="Flow_1vqirpn">
                <di:waypoint x="675" y="1297" />
                <di:waypoint x="750" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1ukxi25_di" bpmnElement="Flow_1ukxi25">
                <di:waypoint x="650" y="1272" />
                <di:waypoint x="650" y="1200" />
                <di:waypoint x="750" y="1200" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="657" y="1156" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1mbsptj_di" bpmnElement="Flow_1mbsptj">
                <di:waypoint x="850" y="1200" />
                <di:waypoint x="940" y="1200" />
                <di:waypoint x="940" y="1297" />
                <di:waypoint x="1005" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_187cfx1_di" bpmnElement="Flow_187cfx1">
                <di:waypoint x="850" y="1297" />
                <di:waypoint x="1005" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_028qee0_di" bpmnElement="Flow_028qee0">
                <di:waypoint x="1260" y="1297" />
                <di:waypoint x="1365" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0b7hzjp_di" bpmnElement="Flow_0b7hzjp">
                <di:waypoint x="1415" y="1297" />
                <di:waypoint x="1530" y="1297" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0x57u0z_di" bpmnElement="Flow_0x57u0z">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="390" />
                <di:waypoint x="1160" y="390" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="356" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0gtr14h_di" bpmnElement="Flow_0gtr14h">
                <di:waypoint x="1260" y="390" />
                <di:waypoint x="1390" y="390" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1izoci5_di" bpmnElement="Flow_1izoci5">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="300" />
                <di:waypoint x="1160" y="300" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="266" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_0h8pa1z_di" bpmnElement="Flow_0h8pa1z">
                <di:waypoint x="1260" y="300" />
                <di:waypoint x="1390" y="300" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1pna9x7_di" bpmnElement="Flow_1pna9x7">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="210" />
                <di:waypoint x="1160" y="210" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="176" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1cibfi1_di" bpmnElement="Flow_1cibfi1">
                <di:waypoint x="1030" y="1272" />
                <di:waypoint x="1030" y="120" />
                <di:waypoint x="1160" y="120" />
                <bpmndi:BPMNLabel>
                    <dc:Bounds x="1065" y="86" width="85" height="27" />
                </bpmndi:BPMNLabel>
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1mvhv6m_di" bpmnElement="Flow_1mvhv6m">
                <di:waypoint x="1260" y="210" />
                <di:waypoint x="1390" y="210" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_1vqkmb7_di" bpmnElement="Flow_1vqkmb7">
                <di:waypoint x="1260" y="120" />
                <di:waypoint x="1390" y="120" />
                <di:waypoint x="1390" y="1272" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn:definitions>
