CREATE TABLE `merchant_layer_gmv_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '快手Id',
  `gmv` bigint(20) unsigned NOT NULL COMMENT 'gmv',
  `gmv_segment` varchar(300) DEFAULT '' COMMENT 'gmv分段',
  `group_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'task groupId',
  `task_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'task taskId',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1:初始待处理,2:任务创建失败,3:任务创建success,4:参加任务失败,5:参加任务成功 ',
  `task_alias` varchar(1024) DEFAULT '' COMMENT '任务 alias',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE `uniq_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分层gmv数据表';


CREATE TABLE `merchant_shop_inc_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '快手Id',
  `group_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'task groupId',
  `task_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'task taskId',
  `task_alias` varchar(1024) DEFAULT '' COMMENT '任务 alias',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1:初始待处理,2:任务创建失败,3:任务创建success,4:参加任务失败,5:参加任务成功 ',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE `uniq_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺入驻增量数据表';

CREATE TABLE `merchant_monthly_gmv` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint(20) NOT NULL COMMENT '快手Id',
  `gmv` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'gmv',
  `blocked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被风控',
  `dt` bigint(6) unsigned NOT NULL DEFAULT '0' COMMENT '分区，如20200601',
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0:待处理 10：处理失败 20：success -10:忽略不处理',
  `extra` varchar(500) NOT NULL DEFAULT '' COMMENT '扩展参数',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',

  PRIMARY KEY (`id`),
  UNIQUE `uniq_user_id` (`user_id`),
  KEY `idx_dt_state_gmv` (`dt`, `state`, `gmv`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分层gmv数据表';