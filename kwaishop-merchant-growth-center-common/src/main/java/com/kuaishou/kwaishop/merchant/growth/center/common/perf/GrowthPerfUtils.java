package com.kuaishou.kwaishop.merchant.growth.center.common.perf;

import org.apache.commons.lang3.StringUtils;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.util.PerfBuilder;
import com.kuaishou.framework.util.PerfUtils;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.ResponseUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-29
 */
@Deprecated
public class GrowthPerfUtils {

    public static final String NAMESPACE_DEFAULT_BIZ = "ad.merchant"; //namespace

    public static final String TAG_PERFIX = "kwaishop.merchant.growth.";

    public static final String PRE_SUB_TAG = "com.kuaishou.kwaishop.";

    private GrowthPerfUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static void doPerf(PerfProvider perfProvider) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf());
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, String extra2) {
        PerfBuilder builder = PerfUtils
                .perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf(), extra2);
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, String extra2, String extra3) {
        PerfBuilder builder = PerfUtils
                .perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf(), extra2, extra3);
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, String extra2, String extra3, String extra4) {
        PerfBuilder builder = PerfUtils
                .perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf(), extra2, extra3, extra4);
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, String extra2, String extra3, String extra4, long time) {
        PerfBuilder builder = PerfUtils
                .perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf(), extra2, extra3, extra4)
                .value(time);
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, String extra2, String extra3, long value) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, perfProvider.getSubtag(), perfProvider.getPerf(), extra2, extra3)
                        .value(value);
        builder.logstash();
    }

    public static void doPerf(PerfProvider perfProvider, StopWatch stopWatch) {
        perf(perfProvider.getSubtag(), perfProvider.getPerf(), stopWatch);
    }

    public static void doPerf(PerfProvider perfProvider, String extra2, StopWatch stopWatch) {
        perf(perfProvider.getSubtag(), perfProvider.getPerf(), extra2, stopWatch);
    }

    public static void perf(String tag, StopWatch stopWatch) {
        perf(tag, stopWatch.getTimeMicros());
    }

    public static void perf(String tag, String extra1, StopWatch stopWatch) {
        perf(tag, extra1, stopWatch.getTimeMicros());
    }

    public static void perfCount(String tag, int count) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, tag).count(count);
        builder.logstash();
    }

    public static void perf(String tag) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag);
        builder.logstash();
    }

    public static void perf(String tag, String extra1) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1);
        builder.logstash();
    }

    public static void perf(String tag, String extra1, String extra2) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1, extra2);
        builder.logstash();
    }

    public static void perf(String tag, String extra1, String extra2, String extra3) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1, extra2, extra3);
        builder.logstash();
    }

    public static void perf(String tag, String extra1, String extra2, String extra3, String extra4) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1, extra2, extra3, extra4);
        builder.logstash();
    }

    public static void perfSuccess(String tag, String extra1) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildSuccessPerfTag(TAG_PERFIX + tag), extra1);
        builder.logstash();
    }

    public static void perfSuccess(String tag, String extra1, String extra2) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildSuccessPerfTag(TAG_PERFIX + tag), extra1, extra2);
        builder.logstash();
    }

    public static void perfSuccess(String tag, String extra1, String extra2, String extra3) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildSuccessPerfTag(TAG_PERFIX + tag), extra1, extra2, extra3);
        builder.logstash();
    }

    public static void perfSuccess(String tag, String extra1, String extra2, String extra3, String extra4) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildSuccessPerfTag(TAG_PERFIX + tag), extra1, extra2, extra3,
                        extra4);
        builder.logstash();
    }

    public static void perfEx(String tag, String extra1) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildExPefTag(TAG_PERFIX + tag), extra1);
        builder.logstash();
    }

    public static void perfEx(String tag, String extra1, String extra2) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildExPefTag(TAG_PERFIX + tag), extra1, extra2);
        builder.logstash();
    }

    public static void perfEx(String tag, String extra1, String extra2, String extra3) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildExPefTag(TAG_PERFIX + tag), extra1, extra2, extra3);
        builder.logstash();
    }

    public static void perfEx(String tag, String extra1, String extra2, String extra3, String extra4) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, buildExPefTag(TAG_PERFIX + tag), extra1, extra2, extra3, extra4);
        builder.logstash();
    }


    public static void perf(String tag, String extra1, String extra2, StopWatch stopWatch) {
        perf(tag, extra1, extra2, stopWatch.getTimeMicros());
    }

    public static void perfSuccess(String tag, StopWatch stopWatch) {
        perf(buildSuccessPerfTag(tag), stopWatch.getTimeMicros());
    }

    public static void perfSuccess(String tag, String extra1, StopWatch stopWatch) {
        perf(buildSuccessPerfTag(tag), extra1, stopWatch.getTimeMicros());
    }

    public static void perfSuccess(String tag, String extra1, String extra2, StopWatch stopWatch) {
        perf(buildSuccessPerfTag(tag), extra1, extra2, stopWatch.getTimeMicros());
    }

    public static void perfEx(String tag, String extra1, String extra2, StopWatch stopWatch) {
        perf(buildExPefTag(tag), extra1, extra2, stopWatch.getTimeMicros());
    }


    public static void perf(String tag, String extra1, String extra2, String extra3, StopWatch stopWatch) {
        perf(tag, extra1, extra2, extra3, stopWatch.getTimeMicros());
    }

    public static void perfSuccess(String tag, String extra1, String extra2, String extra3, StopWatch stopWatch) {
        perf(buildSuccessPerfTag(tag), extra1, extra2, extra3, stopWatch.getTimeMicros());
    }

    public static void perfEx(String tag, String extra1, String extra2, String extra3, StopWatch stopWatch) {
        perf(buildExPefTag(tag), extra1, extra2, extra3, stopWatch.getTimeMicros());
    }

    public static void perf(String tag, String extra1, long timeMicroSeconds) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1).micros(timeMicroSeconds);
        builder.logstash();
    }

    public static void perf(String tag, long timeMicroSeconds) {
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag).micros(timeMicroSeconds);
        builder.logstash();
    }

    public static void perf(String tag, String extra1, String extra2, long timeMicroSeconds) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1, extra2).micros(timeMicroSeconds);
        builder.logstash();
    }

    public static void perf(String tag, String extra1, String extra2, String extra3, long timeMicroSeconds) {
        PerfBuilder builder =
                PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, TAG_PERFIX + tag, extra1, extra2, extra3)
                        .micros(timeMicroSeconds);
        builder.logstash();
    }

    public static void perfSuccess(Class<?> clazz, Thread thread, StopWatch sw) {
        String subtag = clazz.getName() + "." + thread.getStackTrace()[2].getMethodName() + ".success";
        subtag = getShortSubTag(subtag);
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, subtag).micros(sw.getTimeMicros());
        builder.logstash();
    }

    public static void perfError(Class<?> clazz, Thread thread, StopWatch sw) {
        String subtag = clazz.getName() + "." + thread.getStackTrace()[2].getMethodName() + ".error";
        subtag = getShortSubTag(subtag);
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, subtag).micros(sw.getTimeMicros());
        builder.logstash();
    }

    public static void perfException(Class<?> clazz, Thread thread, Exception e, StopWatch sw) {
        String subtag = clazz.getName() + "." + thread.getStackTrace()[2].getMethodName() + ".exception";
        subtag = getShortSubTag(subtag);
        PerfBuilder builder = PerfUtils.perf(NAMESPACE_DEFAULT_BIZ, subtag, e.getClass().getSimpleName(),
                        String.valueOf(ResponseUtils.getResultCode(e)), ResponseUtils.getResultMsg(e))
                .micros(sw.getTimeMicros());
        builder.logstash();
    }

    private static String buildSuccessPerfTag(String subTag) {
        return appendTagSuffix(subTag, "success");
    }

    private static String buildExPefTag(String subTag) {
        return appendTagSuffix(subTag, "exception");
    }

    private static String appendTagSuffix(String subTag, String suffix) {
        if (StringUtils.isBlank(subTag) || StringUtils.isBlank(suffix)) {
            return subTag;
        }
        return subTag.endsWith(".") ? subTag + suffix : subTag + "." + suffix;
    }

    private static String getShortSubTag(String subTag) {
        return subTag.startsWith(PRE_SUB_TAG) ? subTag.substring(PRE_SUB_TAG.length()) : subTag;
    }
}