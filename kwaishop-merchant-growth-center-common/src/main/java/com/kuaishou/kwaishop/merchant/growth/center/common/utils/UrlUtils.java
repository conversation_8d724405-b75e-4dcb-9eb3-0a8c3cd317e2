package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-24
 */
@Slf4j
public class UrlUtils {
    public static String appendEncodeUrlParams(String url, Map<String, String> params) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        if (MapUtils.isEmpty(params)) {
            return url;
        }
        StringBuilder sb = new StringBuilder();
        params.forEach(
                (key, value) -> {
                    try {
                        sb.append(key).append("=").append(URLEncoder.encode(value, "UTF-8")).append("&");
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }
                });
        sb.deleteCharAt(sb.length() - 1);
        String str = sb.toString();
        int length = url.length();
        int index = url.indexOf("?");
        String result = "";
        if (index > -1) {
            // 存在"?",如果是最后一个字符，则直接拼接，否则拼接"&"
            if ((length - 1) == index) {
                result = url + str;
            } else {
                result = url + "&" + str;
            }
        } else {
            // 不存在"?"，则需拼接"?"
            result = url + "?" + str;
        }
        log.info("appendEncodeUrlParams: url={}, params={}, result={}", url, params, result);
        return result;
    }

    public static void main(String[] args) {
        String url = "https://eshop-app.prt.kwaixiaodian.com/page/kwaishop-seller-operation-h5/mini/LogLanding";
        Map<String, String> map = new HashMap<>();
        map.put("source", "vxPrivate");
        map.put("laneId", "PRT.yctest");
        map.put("nodeInstanceId", "111");
        map.put("executeTaskId", "222");
        map.put("redirectUrl", "https://www.baidu.com");
        String result = appendEncodeUrlParams(url, map);
        System.out.println(result);
    }
}
