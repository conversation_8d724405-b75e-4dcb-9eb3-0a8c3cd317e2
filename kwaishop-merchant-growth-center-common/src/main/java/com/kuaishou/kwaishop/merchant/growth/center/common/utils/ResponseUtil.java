package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * response处理工具类
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-24
 */
public class ResponseUtil {

    /**
     * 解析错误码
     */
    public static int parseCode(Exception e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            return bizException.getCode();
        } else {
            return BaseResultCode.SERVER_ERROR_VALUE;
        }
    }

    /**
     * 解析错误信息
     */
    public static String parseMessage(Exception e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            return bizException.getMessage();
        } else {
            return e.getClass().getSimpleName();
        }
    }
}
