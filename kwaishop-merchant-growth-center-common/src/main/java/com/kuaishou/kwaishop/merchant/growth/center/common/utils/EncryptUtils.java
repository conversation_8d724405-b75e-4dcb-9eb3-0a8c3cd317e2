package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import com.google.api.client.util.Base64;
import com.kuaishou.keycenter.client.ClientProtectionProvider;
import com.kuaishou.keycenter.security.CoderException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-24
 */
@Slf4j
public class EncryptUtils {
    public static String encrypt(String str, String keyName) {
        ClientProtectionProvider provider = ClientProtectionProvider.getProvider(keyName);
        try {
            byte[] cipher = provider.encrypt(str.getBytes());
            String encryptStr = Base64.encodeBase64String(cipher);
            log.info("encrypt success, keyName: {}, str: {}, encryptStr: {}", keyName, str, encryptStr);
            return encryptStr;
        } catch (CoderException e) {
            log.error("encrypt error, keyName: {}, str: {}", keyName, str, e);
            throw new RuntimeException(e);
        }
    }

    public static String decrypt(String str, String keyName) {
        ClientProtectionProvider provider = ClientProtectionProvider.getProvider(keyName);
        try {
            byte[] cipher = Base64.decodeBase64(str);
            byte[] plain = provider.decrypt(cipher);
            String decryptStr = new String(plain);
            log.info("decrypt success, keyName: {}, str: {}, decryptStr: {}", keyName, str, decryptStr);
            return decryptStr;
        } catch (CoderException e) {
            log.error("decrypt error, keyName: {}, str: {}", keyName, str, e);
            throw new RuntimeException(e);
        }
    }
}
