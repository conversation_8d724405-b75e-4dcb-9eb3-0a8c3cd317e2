package com.kuaishou.kwaishop.merchant.growth.center.common.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全局降级开关配置
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GlobalDowngradeConfigBO {
    /**
     * 降价开关 false-正常 true-降价
     */
    private Boolean switchFlag;
    /**
     * 降级原因
     */
    private String reason;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间，格式20210915
     */
    private String operatorTime;
}
