package com.kuaishou.kwaishop.merchant.growth.center.common.config.kconf;

import java.util.HashMap;
import java.util.Map;

import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.kwaishop.merchant.growth.center.common.model.bo.GlobalDowngradeConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.common.model.bo.GrowthCenterWarmupConfigBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-08-01
 */
public enum MerchantGrowthJsonMapConfigKey implements KconfSupplier<Map<?, ?>> {
    dialogDebugUserId(String.class, Long.class), //for debug
    globalDowngradeConfig(String.class, GlobalDowngradeConfigBO.class),
    growthCenterWarmupConfig(String.class, GrowthCenterWarmupConfigBO.class),
    ;

    private Class<?> keyClass;
    private Class<?> itemClass;

    <K, V> MerchantGrowthJsonMapConfigKey(Class<K> keyClass, Class<V> itemClass) {
        this.keyClass = keyClass;
        this.itemClass = itemClass;
    }

    @Override
    public String configKey() {
        return name();
    }

    @Override
    public Map<?, ?> defaultValue() {
        return Maps.newHashMap();
    }

    // 默认空实现
    @Override
    public Kconf<Map<?, ?>> getKConf() {
        return null;
    }

    // 覆盖底层解析方法
    @Override
    public Map<?, ?> get() {
        return getMap(keyClass, itemClass);
    }

    @SuppressWarnings("unchecked")
    public <K, V> Map<K, V> getMap(Class<K> keyClassArg, Class<V> itemClassArg) {
        Kconf<HashMap<K, V>> kConf = Kconfs
                .ofJsonMap(getConfigKey(), Maps.newHashMap(), keyClassArg, itemClassArg).build();
        if (kConf == null) {
            return Maps.newHashMap();
        }

        return kConf.get();
    }

    public <K, V> Map<K, V> getMap() {
        return (Map<K, V>) getMap(keyClass, itemClass);
    }
}
