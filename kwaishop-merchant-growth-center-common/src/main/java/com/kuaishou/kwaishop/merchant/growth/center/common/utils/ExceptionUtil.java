package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR>
 */
public class ExceptionUtil {

    private static final Logger log = LoggerFactory.getLogger(ExceptionUtil.class);


    /**
     * 有选择性的进行日志打印，BizException情况下打印warn日志
     */
    public static void selectiveLogException(Logger logger, String logContent, Exception e) {
        if (e instanceof BizException) {
            logger.warn(logContent, e);
        } else {
            logger.error(logContent, e);
        }
    }

    public static void selectiveLogException(Exception e, String logPattern, Object... params) {
        if (e instanceof BizException) {
            if (((BizException) e).isLogErrorMsg()) {
                log.error(logPattern, params, e);
            } else {
                log.warn(logPattern, params, e);
            }
        } else {
            log.error(logPattern, params, e);
        }
    }

    public static void selectiveLogThrowable(Throwable e, String logPattern, Object... params) {
        if (e instanceof BizException) {
            if (((BizException) e).isLogErrorMsg()) {
                log.error(logPattern, params, e);
            } else {
                log.warn(logPattern, params, e);
            }
        } else {
            log.error(logPattern, params, e);
        }
    }
}
