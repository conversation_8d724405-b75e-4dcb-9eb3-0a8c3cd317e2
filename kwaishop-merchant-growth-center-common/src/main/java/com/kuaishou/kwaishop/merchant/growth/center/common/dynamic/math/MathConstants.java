package com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-01
 */
public class MathConstants {

    /**
     * 最大最小值前缀
     */
    public static final String MAX_MIN_PREFIX = "M";

    /**
     * 最低级运算符
     */
    public static final String LOWEST_OPERATOR = ",";

    /**
     * 最值多元运算符
     */
    public static final List<String> MULTIMODAL_OPERATOR = Arrays.asList("MIN", "MAX");

    /**
     * 取最大值
     */
    public static final String MAX = "MAX";

    /**
     * 取最小值
     */
    public static final String MIN = "MIN";

    /**
     * 右方括号
     */
    public static final String RIGHT_SQUARE_BRACKET = "]";

    /**
     * 右括号
     */
    public static final String RIGHT_BRACKET = ")";

    public static final String PARAM_OCCUPY = "$";
    /**
     * 左括号
     */
    public static final String LEFT_BRACKET = "(";

    public static final String MOCK_NUMBER = "MOCK_NUMBER";

    /**
     * 运算符优先级，越大优先级越高
     */
    public static final Map<String/*操作符*/, Integer/*仅占位*/> OPERATOR_PRECEDENCE = new HashMap<>();

    static {
        // 逗号默认最低优先级
        OPERATOR_PRECEDENCE.put(",", -1);
        OPERATOR_PRECEDENCE.put("(", 0);
        OPERATOR_PRECEDENCE.put("[", 0);
        OPERATOR_PRECEDENCE.put("MAX", 1);
        OPERATOR_PRECEDENCE.put("MIN", 1);
        OPERATOR_PRECEDENCE.put("+", 2);
        OPERATOR_PRECEDENCE.put("-", 2);
        OPERATOR_PRECEDENCE.put("*", 3);
        OPERATOR_PRECEDENCE.put("/", 3);
        OPERATOR_PRECEDENCE.put("^", 4);
        OPERATOR_PRECEDENCE.put(")", 5);
        OPERATOR_PRECEDENCE.put("]", 5);
    }

    /**
     * 二元运算方法
     */
    public static final Map<String/*运算符*/, BiFunction<String, String, String>/*运算方法*/>
            BINARY_CALCULATES =
            new HashMap<>();

    static {
        BINARY_CALCULATES.put("+", ArithmeticHelper::add);
        BINARY_CALCULATES.put("-", ArithmeticHelper::subtract);
        BINARY_CALCULATES.put("*", ArithmeticHelper::multiply);
        BINARY_CALCULATES.put("/", ArithmeticHelper::divide);
        BINARY_CALCULATES.put("^", ArithmeticHelper::square);
    }

}
