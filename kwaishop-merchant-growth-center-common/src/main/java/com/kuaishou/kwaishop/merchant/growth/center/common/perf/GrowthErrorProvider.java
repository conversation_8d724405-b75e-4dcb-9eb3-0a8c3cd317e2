package com.kuaishou.kwaishop.merchant.growth.center.common.perf;

/**
 * 定义服务处理相关异常 错误perf  extra1维度
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-28
 */
public enum GrowthErrorProvider implements PerfProvider {
    gmvLayerDataSyncTaskErr,
    taskParticipateTaskErr,

    createLayerTaskErr,
    layerGmvTaskAwardConfigEmpty,
    gmvNotHitLayerConfig,
    stageAwardsAboveLimit,

    createTaskTemplateFailed,
    createdIllegalTaskId,

    saveUserTaskRegistryFailed,
    participateTaskErr,

    retryForTaskCreateErr,
    retryForTaskParticipateErr,

    processShopIncDataErr,
    processShopChangedErr,

    growthTaskGmvDataNotReady,

    levelGmvDataSyncTaskErr,
    levelGmvDataSyncErr,
    levelGmvTaskAwardConfigEmpty,
    levelGmvInBlackList,
    levelGmvCreateTaskGroupFailed,
    levelGmvGetGroupByAlias,
    levelGmvCreatedIllegalGroupId,
    levelGmvDoCreateTaskGroupErr,
    levelGmvBatchSaveUserTaskRegistrationFail,
    levelGmvParticipateTaskSuccess,
    levelGmvStageAwardsAboveLimit,
    levelGmvNotHitLayerConfig,

    ;
    @Override
    public String getSubtag() {
        return DEFAULT_SUB_TAG + ".doError";
    }

    @Override
    public String getPerf() {
        return name();
    }
}