package com.kuaishou.kwaishop.merchant.growth.center.common.monitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MonitorEvent {
     /**
      * 业务域
      */
     private String biz;

     /**
      * 监控项
      */
     private String metricName;

     /**
      * 结果
      */
     private boolean success;

     /**
      * 错误码
      */
     private String errorCode;

     /**
      * 错误信息
      */
     private String errorMsg;

     /**
      * 耗时,unit:ms
      */
     private long rt;

     /**
      * 业务id
      * 如订单类监控为订单id,用户类监控为用户id
      */
     private String bizId;

     /**
      * 扩展信息
      */
     private String ext;
}
