package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.framework.util.ObjectMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-08
 */
@Slf4j
public class JsonUtils {

    /**
     * 校验字符串是否是json字符串，空字符串也算
     */
    public static boolean isJsonStr(String str) {
        if (StringUtils.isBlank(str)) {
            return true;
        }
        Object jsonObject = ObjectMapperUtils.fromJSON(str, Object.class);
        return jsonObject != null;
    }

    public static String replacePlaceholders(String template, Map<String, Object> values, boolean missingKeyException) {
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(template);
        // 检查是否有缺失的键
        while (matcher.find()) {
            String key = matcher.group(1);
            if (!values.containsKey(key)) {
                log.error("Missing key: {}in template {}", key, template);
                if (missingKeyException) {
                    throw new IllegalArgumentException("Missing key: " + key + " in template: " + toJSON(template));
                }
                values.put(key, "-");
            }
        }

        // 替换占位符
        for (Map.Entry<String, Object> entry : values.entrySet()) {
            String key = "\\$\\{" + entry.getKey() + "}";
            String value = String.valueOf(entry.getValue());
            template = template.replaceAll(key, value);
        }

        return template;
    }
}
