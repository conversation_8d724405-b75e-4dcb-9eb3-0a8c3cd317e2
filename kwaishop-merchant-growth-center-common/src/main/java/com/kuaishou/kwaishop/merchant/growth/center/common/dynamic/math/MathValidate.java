package com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math;

import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.BINARY_CALCULATES;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MAX;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MIN;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MOCK_NUMBER;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MULTIMODAL_OPERATOR;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.OPERATOR_PRECEDENCE;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.PARAM_OCCUPY;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;

import java.util.Collections;
import java.util.Objects;
import java.util.Stack;
import java.util.function.BiFunction;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-01
 */
public class MathValidate {

    /**
     * 校验动态计算表达式
     */
    public static void check(String expression) {
        if (StringUtils.isBlank(expression)) {
            throw new BizException(PARAM_INVALID, "表达式为空");
        }

        /**
         * 每个计算元素和operator作为整体是否能够压栈进入逆波兰式
         */
        Stack<String> postFix;
        try {
            postFix = MathBase.convertToPostfix(expression, false);
        } catch (Exception e) {
            throw new BizException(PARAM_INVALID, "表达式不合法");
        }

        /**
         * 校验每个独立元素是否合法
         */
        if (postFix.isEmpty()) {
            throw new BizException(PARAM_INVALID, "公式表达式为空");
        }
        Collections.reverse(postFix);
        int currentOp = 0;
        for (; currentOp < postFix.size(); currentOp++) {
            String element = postFix.get(currentOp);
            if (StringUtils.isBlank(element)) {
                continue;
            }
            if (element.equals(MAX) || element.equals(MIN)) {
                // 多元取大取小元素固定为2个
                if (currentOp + 1 >= postFix.size() || !postFix.get(currentOp + 1).equals("2")) {
                    throw new BizException(PARAM_INVALID, "公式MAX、MIN参数不正确");
                }
                currentOp += 1;
                continue;
            }
            // 参与计算元素连续存在于表达式中
            if (!expression.contains(element)) {
                throw new BizException(PARAM_INVALID, "公式参数非法");
            }
            // 非计算符，1. 数字常量 2. 指标id
            if (OPERATOR_PRECEDENCE.get(element) == null) {
                checkIndicatorNumber(element);
            }
        }

        /**
         *  模拟计算顺序是否正确
         */
        Stack<String> resultStack = new Stack<>();
        // 参与计算的第一个参数，第二个参数，当前值
        String firstVar, secondVar, currentValue;
        // 多元取最值运算的参数个数
        int paramCount;
        while (!postFix.isEmpty()) {
            currentValue = postFix.pop();
            if (StringUtils.isBlank(currentValue)) {
                continue;
            }
            // 非操作符，直接存入计算结果集栈中
            if (!MathBase.isOperator(currentValue)) {
                checkIndicatorNumber(currentValue);
                resultStack.push(currentValue);
                // 多元运算计算：操作符在栈顶，下一个值为参数个数，之后的连续参数个数个值为要取最值的范围集
            } else if (MULTIMODAL_OPERATOR.contains(currentValue)) {
                paramCount = Integer.parseInt(resultStack.pop());
                if (resultStack.size() < paramCount) {
                    throw new BizException(PARAM_INVALID, "公式配置错误");
                }
                while (paramCount-- > 0) {
                    String param = resultStack.pop();
                    checkIndicatorNumber(param);
                }
                // 模拟结果用1表示
                resultStack.push(MOCK_NUMBER);
                // 二元运算，从结果栈中依次取两个元素进行计算
            } else {
                BiFunction<String, String, String> calculate = BINARY_CALCULATES.get(currentValue);
                if (Objects.nonNull(calculate)) {
                    if (resultStack.size() < 2) {
                        throw new BizException(PARAM_INVALID, "公式配置错误");
                    }
                    secondVar = resultStack.pop();
                    firstVar = resultStack.pop();
                    checkIndicatorNumber(secondVar);
                    checkIndicatorNumber(firstVar);
                    // 模拟结果用1表示
                    resultStack.push(MOCK_NUMBER);
                } else {
                    throw new BizException(PARAM_INVALID, "公式配置错误");
                }
            }
        }
        // 结果栈不为1 或者 计算栈没有完全退出
        if (resultStack.size() != 1 || !resultStack.get(0).equals(MOCK_NUMBER)) {
            throw new BizException(PARAM_INVALID, "公式配置错误");
        }

    }

    public static void checkIndicatorNumber(String element) {
        // 指标id
        if (element.startsWith(PARAM_OCCUPY)) {
            if (!ArithmeticHelper.judgeParamOccupy(element)) {
                throw new BizException(PARAM_INVALID, "公式指标参数非法");
            }
        } else if (!MOCK_NUMBER.equals(element) && !ArithmeticHelper.judgeDouble(element)) {
            throw new BizException(PARAM_INVALID, "公式计算元素不是数字类型");
        }
    }
}


