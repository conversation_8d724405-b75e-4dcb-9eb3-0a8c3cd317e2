package com.kuaishou.kwaishop.merchant.growth.center.common.model.dos;

import java.math.BigDecimal;

import com.kuaishou.merchant.db.base.annotation.PrimaryKey;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商家分层GMV 目标Task关联， 用于等级预演红利计划
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantLevelGmvTaskDO {
    /**
     * 主键
     */
    @PrimaryKey
    private long id;
    /**
     * 商家id
     */
    private long sellerId;
    /**
     * 商家近30天gmv,9.19(含)近30天gmv
     */
    private long payOrderAmt;
    /**
     * 商家近30天自建订单gmv, 剔除预售以及三方
     */
    private long targetPayOrderAmt;
    /**
     * 商家近30天可开运费险商品类目下的订单数(在订单上无需真实投保)
     */
    private long insurancePayOrderCnt;
    /**
     * 退货率
     */
    private BigDecimal refundRate;
    /**
     * 用来计算退货率的分母
     */
    private long refundRateDenominator;
    /**
     * 用来计算退货率的分子
     */
    private long refundRateNumerator;
    /**
     * 活跃字段
     */
    private int isActive;

    private long groupId;

    @Builder.Default
    private String groupAlias = "";

    private int status;

    private long createTime;

    private long updateTime;
}
