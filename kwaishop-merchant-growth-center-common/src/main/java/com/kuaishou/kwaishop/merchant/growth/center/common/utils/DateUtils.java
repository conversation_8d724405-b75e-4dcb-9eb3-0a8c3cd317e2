package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.google.api.client.util.Lists;

/**
 * 时间工具类
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-06-11
 */
@SuppressWarnings("checkstyle:MagicNumber")
public class DateUtils {

    public static final String NORMAL_DOT_DATE_FORMAT_PATTERN = "yyyy.MM.dd";
    private static final String DAY_CODE_FORMAT_PATTERN = "yyyyMMdd";
    private static final String DAY_CODE_FORMAT_PATTERN_SECOND = "yyyy-MM-dd HH:mm:ss";
    public static final String NORMAL_MINUTE_DATE_PATTERN = "yyyy-MM-dd HH:mm";

    // 一天的小时数
    public static final int HOUR_DAY = 24;

    // 一小时的分钟数
    public static final int MINIUTE_OF_HOUR = 60;
    // 一秒钟的毫秒数
    public static final long SECOND_MILLISECONDS = 1000;
    // 一分钟的毫秒数
    public static final long MINUTE_MILLISECONDS = SECOND_MILLISECONDS * 60;
    // 一小时的毫秒数
    public static final long HOUR_MILLISECONDS = MINUTE_MILLISECONDS * 60;
    // 一天的毫秒数
    public static final long DAY_MILLISECONDS = HOUR_MILLISECONDS * HOUR_DAY;
    // 一天的秒数
    public static final long DAY_SECONDS = 60 * 60 * 24;
    // 一天的天数
    public static final int DAY_OF_DAY = 1;
    // 一周的天数
    public static final int DAY_OF_WEEK = 7;


    private static final int MOCK_DAY = 20210615;

    /**
     * 获取按照天数计算的时间code 20211102
     */
    public static int getDayDateCode(long timestamp) {
        String str = DateFormatUtils.format(timestamp, DAY_CODE_FORMAT_PATTERN);
        return Integer.parseInt(str);
    }

    public static String getTodayPureDigitalDate() {
        return String.valueOf(getDayDateCode(System.currentTimeMillis()));
    }

    public static long parsePureDigitalDateToTimeStamp(String dateStr) {
        Date date = parsePureDigitalDate(dateStr);
        return date == null ? 0 : date.getTime();
    }

    /**
     * 解析纯数字格式的日期字符串
     *
     * @param dateStr 满足yyyyMMdd的日期字符串
     * @return 日期
     */
    public static Date parsePureDigitalDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(DAY_CODE_FORMAT_PATTERN);
        try {
            return format.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("Date str [%s] not is pure digital pattern!", dateStr));
        }
    }

    /**
     * 将yyymmdd格式字符串转成时间戳
     * @param dateStr
     * @return
     */
    public static long convertToTimestamp(String dateStr) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat(DAY_CODE_FORMAT_PATTERN);
            Date date = formatter.parse(dateStr);
            return date.getTime();
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析日期字符串: " + dateStr, e);
        }
    }

    /**
     * 解析日期字符串
     */
    public static long parseDateStrToTimestamp(String dateStr, String pattern) {
        if (StringUtils.isBlank(dateStr)) {
            return 0;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try {
            return format.parse(dateStr).getTime();
        } catch (ParseException e) {
            throw new RuntimeException(String.format("Date str [%s] not is pure digital pattern!", dateStr));
        }
    }


    /**
     * 解析纯数字格式的日期字符串
     *
     * @param dateStr 满足yyyy-MM-dd HH:mm:ss的日期字符串
     * @return 日期
     */
    public static Date parseDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(DAY_CODE_FORMAT_PATTERN_SECOND);
        try {
            return format.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("Date str [%s] not is pure digital pattern!", dateStr));
        }
    }

    /**
     * 将yyyy-MM-dd HH:mm:ss的日期字符串解析为对应的时间戳
     */
    public static long normalParseDateStrToTimestamp(String dateStr) {
        Date date = parseDate(dateStr);
        return date == null ? 0 : date.getTime();
    }

    /*
     * 解析纯数字格式的日期字符串
     *
     * @return 日期
     */
    public static Date parseDate(String pattern, String dateStr) {
        if (StringUtils.isAnyBlank(pattern, dateStr)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        try {
            return format.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(String.format("Date str [%s] not is pure digital pattern!", dateStr));
        }

    }

    /**
     * 格式化为 yyyy-MM-dd HH:mm:ss 的标准时间样式
     */
    public static String normalFormatTimeStamp(Long timeStamp) {
        if (timeStamp == null) {
            return null;
        }
        SimpleDateFormat normalFormat = new SimpleDateFormat(DAY_CODE_FORMAT_PATTERN_SECOND);
        return normalFormat.format(new Date(timeStamp));
    }

    /**
     * 格式化时间戳
     */
    public static String formatTimeStamp(Long timestamp, String formatPattern) {
        if (timestamp == null || StringUtils.isBlank(formatPattern)) {
            return null;
        }
        SimpleDateFormat format = new SimpleDateFormat(formatPattern);
        return format.format(new Date(timestamp));
    }

    /**
     * 判断指定时间是否满足该区间 [startTime, endTime)
     */
    public static boolean checkTimestampInRange(Long checkTime, Long startTime, Long endTime) {
        if (checkTime == null) {
            return false;
        }
        if (ObjectUtils.isNotNull(startTime, endTime)) {
            return checkTime >= startTime && checkTime < endTime;
        }
        if (startTime == null) {
            return checkTime < endTime;
        }
        return checkTime >= startTime;
    }

    /**
     * 校验开始结束时间
     */
    public static boolean checkValidStartEndTime(long startTime, long endTime) {
        return startTime >= 0L && endTime >= 0L && startTime <= endTime;
    }

    /**
     * 获取是当月的第几天
     */
    public static int getDayOfMonth(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取是当星期的第几天
     */
    public static int getDayOfWeek(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取是某天的第几小时
     */
    public static int getHourOfDay(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取是一年的几月
     */
    public static int getMothOfYear(long timeStamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        // public static final int JANUARY = 0; 一月的值为0，需要做加1操作
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 给指定时间加多少月，会自动进行跨年
     */
    public static long addMonth(long timeStamp, int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        calendar.add(Calendar.MONTH, amount);
        return calendar.getTimeInMillis();
    }

    /**
     * 计算startDay->endDay 增量步长为intervalDay的元素顺序集合
     */
    public static List<Long> calculateDayList(long startDay, long endDay, long intervalDay) {
        return LongStream.iterate(startDay, n -> n + intervalDay)
                .limit((endDay - startDay + intervalDay) / intervalDay).boxed().collect(Collectors.toList());
    }

    /**
     * 给指定时间加多少天，会自动进行跨月
     */
    public static long addDay(long timeStamp, int amount) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        calendar.add(Calendar.DAY_OF_MONTH, amount);
        return calendar.getTimeInMillis();
    }

    /**
     * 设置天和小时,分钟和秒会设置为0
     */
    public static long setDayAndHour(long timeStamp, Integer dayOfMonth, Integer hourOfDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(timeStamp));
        if (dayOfMonth != null) {
            calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
        }
        if (hourOfDay != null) {
            calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        }
        return calendar.getTimeInMillis();
    }

    /**
     * 获取两个时间戳相差的天数，2022-10-01 23:59:59 和 2022-10-03 00:00:00 相差的是两天,只比较天维度
     *
     * @param startTime 开始的时间
     * @param endTime 结束的时间
     * @return 相差的天数
     */
    public static long getDayBetween(long startTime, long endTime) {
        LocalDateTime startLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime),
                        ZoneId.systemDefault());
        LocalDateTime endLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime),
                        ZoneId.systemDefault());
        return ChronoUnit.DAYS.between(startLocalDateTime.toLocalDate(),
                endLocalDateTime.toLocalDate());
    }

    /**
     * 获取两个时间戳相差的周数
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 相差多少周
     */
    public static long getWeekBetween(long startTime, long endTime) {
        LocalDateTime startLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime),
                        ZoneId.systemDefault());
        LocalDateTime endLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime),
                        ZoneId.systemDefault());
        return ChronoUnit.WEEKS.between(startLocalDateTime.toLocalDate(),
                endLocalDateTime.toLocalDate());
    }

    /**
     * 获取两个时间戳相差的月数
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return 相差的多少月
     */
    public static long getMonthBetween(long startTime, long endTime) {
        LocalDateTime startLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime),
                        ZoneId.systemDefault());
        LocalDateTime endLocalDateTime =
                LocalDateTime.ofInstant(Instant.ofEpochMilli(endTime),
                        ZoneId.systemDefault());
        return ChronoUnit.MONTHS.between(startLocalDateTime.toLocalDate(),
                endLocalDateTime.toLocalDate());
    }

    /**
     * 判断是否是同一天
     */
    public static boolean isSameDay(Long time1, Long time2) {
        if (time1 == null || time2 == null) {
            return false;
        }
        Date date1 = new Date(time1);
        Date date2 = new Date(time2);
        return org.apache.commons.lang3.time.DateUtils.isSameDay(date1, date2);
    }

    /**
     * 获取相差的小时数，向下取整
     */
    public static long getDiffHour(long startTime, long endTime) {
        long diff = endTime - startTime;
        long day = diff / DAY_MILLISECONDS;
        return diff % DAY_MILLISECONDS / HOUR_MILLISECONDS + day * HOUR_DAY;
    }

    /**
     * 获取相差的秒数，向下取整
     */
    public static long getDiffSecond(long startTime, long endTime) {
        long diff = endTime - startTime;
        return diff / SECOND_MILLISECONDS;
    }

    /**
     * 时间截断到天
     */
    public static long cutToDay(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 将时间转换为一天开始的时间，00:00:00
     */
    public static long getStartOfDay(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }

    /**
     * 将时间转换为一天结束的时间 23:59:59
     */
    public static long getEndOfDay(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant()).getTime();
    }

    /**
     * 获取此时间对应月份的开始时间
     */
    public static long getMonthFirstDay(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time));
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, 0);
        return getStartOfDay(calendar.getTime().getTime());
    }

    /**
     * 获取此时间对应月份的结束时间
     */
    public static long getMonthLastDay(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(time));
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        calendar.add(Calendar.MONTH, 1);
        return getEndOfDay(calendar.getTime().getTime());
    }

    /**
     * 获取现在时间到第二天0点的时间间隔
     */
    public static long getIntervalToNextDay(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return (calendar.getTimeInMillis() - time);
    }

    /**
     * 获取前interval日dt
     * @param interval
     * @return
     */
    public static String getDayDtByInterval(int interval) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.add(Calendar.DATE, -interval);
        return DateFormatUtils.format(cal.getTimeInMillis(), DAY_CODE_FORMAT_PATTERN);
    }

    /**
     * 时间戳转yyyy/MM/dd
     * @param timestamp
     * @return
     */
    public static String getDayCodeFormatPattern(Long timestamp, String format) {
        // 将时间戳转换为LocalDateTime对象
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        // 格式化日期时间对象为字符串
        return dateTime.format(formatter);
    }

    /**
     * 将时间戳转成对应yyyyMMdd的ds格式
     * @param timestamp
     * @return
     */
    public static String toDateString(long timestamp) {
        Date date = new Date(timestamp);
        SimpleDateFormat sdf = new SimpleDateFormat(DAY_CODE_FORMAT_PATTERN);
        return sdf.format(date);
    }

    /**
     * 比较两个ds大小（转成整型）
     * @param dateString1
     * @param dateString2
     * @return
     */
    public static int compareDates(String dateString1, String dateString2) {
        int date1 = Integer.parseInt(dateString1);
        int date2 = Integer.parseInt(dateString2);
        return Integer.compare(date1, date2);
    }

    /**
     * 毫秒->小时
     * 保留一位小数
     */
    public static double millSecondsToHour(Long millSeconds) {
        BigDecimal divide =
                BigDecimal.valueOf(millSeconds).divide(BigDecimal.valueOf(HOUR_MILLISECONDS), 1, RoundingMode.DOWN);
        return divide.doubleValue();
    }

    /**
     * 小时 -> 毫秒
     */
    public static Long hourToMillSeconds(double hour) {
        return BigDecimal.valueOf(hour).multiply(BigDecimal.valueOf(HOUR_MILLISECONDS)).longValue();
    }

    /**
     * 将日历设置为当月8号的23:59:59.999
     */
    public static void setToEndOfDay8th(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_MONTH, 8);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }

    /**
     * 生成指定时间段内的日期字符串列表
     * @param startTime
     * @param periodNum
     * @return
     */
    public static List<String> generateDateStringList(Long startTime, Integer periodNum) {
        List<String> dateStrings = Lists.newArrayList();

        if (startTime == null || periodNum == null || periodNum <= 0) {
            return dateStrings;
        }

        LocalDate startDate = Instant.ofEpochMilli(startTime)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        for (int i = 0; i < periodNum; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            dateStrings.add(currentDate.format(formatter));
        }

        return dateStrings;
    }

    public static long getMinuteTime(long time) {
        return (time / MINUTE_MILLISECONDS) * MINUTE_MILLISECONDS;
    }
}
