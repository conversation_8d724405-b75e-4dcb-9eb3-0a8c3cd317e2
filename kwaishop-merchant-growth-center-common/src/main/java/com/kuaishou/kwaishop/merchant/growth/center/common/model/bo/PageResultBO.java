package com.kuaishou.kwaishop.merchant.growth.center.common.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页结果数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResultBO <T> {

    private Integer pageNo;

    private Integer pageSize;

    private Long total;

    private List<T> data;
}
