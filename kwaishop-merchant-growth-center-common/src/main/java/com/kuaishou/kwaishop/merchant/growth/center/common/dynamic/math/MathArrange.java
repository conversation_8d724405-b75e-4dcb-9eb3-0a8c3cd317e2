package com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math;

import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.BINARY_CALCULATES;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MAX;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MULTIMODAL_OPERATOR;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;
import java.util.function.BiFunction;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * 计算
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-28
 */
public class MathArrange {

    /**
     * 计算字符串表达式
     * 1+3*4+MAX[3,MAX[5,8]]
     */
    public static String calculate(String expression, Map<String/*对应key*/, String/*对应值*/> inputData) {
        if (StringUtils.isEmpty(expression)) {
            throw new BizException(PARAM_INVALID, "表达式为空");
        }
        // 占位数据替换
        if (inputData != null) {
            for (Map.Entry<String, String> entry : inputData.entrySet()) {
                String indicatorIdKey = entry.getKey();
                String baseIndicatorValue = entry.getValue();
                expression =  StringUtils.replace(expression, indicatorIdKey,
                        baseIndicatorValue);
            }
        }
        Stack<String> resultStack = new Stack<>();

        //  将表达式转换为后缀式，并存入后缀式栈中
        Stack<String> postfixStack = MathBase.convertToPostfix(expression, true);
        // 将后缀式栈反转
        Collections.reverse(postfixStack);
        // 参与计算的第一个参数，第二个参数，当前值
        String firstVar, secondVar, currentValue;
        // 多元取最值运算的参数个数
        int paramCount;
        while (!postfixStack.isEmpty()) {
            currentValue = postfixStack.pop();
            // 非操作符，直接存入计算结果集栈中
            if (!MathBase.isOperator(currentValue)) {
                resultStack.push(currentValue);
                // 多元运算计算：操作符在栈顶，下一个值为参数个数，之后的连续参数个数个值为要取最值的范围集
            } else if (MULTIMODAL_OPERATOR.contains(currentValue)) {
                paramCount = Integer.parseInt(resultStack.pop());
                List<String> paramList = new ArrayList<>();
                while (paramCount-- > 0) {
                    paramList.add(resultStack.pop());
                }
                resultStack.push(StringUtil.equals(MAX, currentValue) ? ArithmeticHelper.max(paramList)
                                                                      : ArithmeticHelper.min(paramList));
                // 二元运算，从结果栈中依次取两个元素进行计算
            } else {
                BiFunction<String, String, String> calculate = BINARY_CALCULATES.get(currentValue);
                if (Objects.nonNull(calculate)) {
                    secondVar = resultStack.pop();
                    firstVar = resultStack.pop();
                    resultStack.push(calculate.apply(firstVar, secondVar));
                }
            }
        }
        return resultStack.pop();
    }


}

