package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import org.json.JSONObject;

import com.google.common.base.Preconditions;
import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors.Descriptor;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.DynamicMessage.Builder;
import com.google.protobuf.util.JsonFormat;
import com.google.protobuf.util.JsonFormat.Parser;
import com.google.protobuf.util.JsonFormat.TypeRegistry;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ProtobufUtils {

    /**
     * 将json转换为pbBytes
     *
     * @param pbDescriptor 入参类型定义
     * @param request 请求参数
     * @return pb bytes
     */
    public static ByteString convertJsonToPbBytes(Descriptor pbDescriptor, JSONObject request) {
        Preconditions.checkNotNull(pbDescriptor, "Convert json to pb bytes, pb descriptor can't null!");
        Parser parser = JsonFormat.parser()
                .usingTypeRegistry(TypeRegistry.newBuilder().add(pbDescriptor).build())
                .ignoringUnknownFields();
        Builder messageBuilder = DynamicMessage.newBuilder(pbDescriptor);
        try {
            parser.merge(request.toString(), messageBuilder);
            return messageBuilder.build().toByteString();
        } catch (Exception ex) {
            log.error("Convert json to pb bytes failed, error msg:", ex);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "Convert json to pb bytes failed!");
        }
    }

    /**
     * 将pb二进制转换为json串
     *
     * @param pbDescriptor Pb 描述符信息
     * @param pbBytes pb二进制
     * @return json
     */
    public static JSONObject convertPbBytesToJson(Descriptor pbDescriptor, ByteString pbBytes) {
        Preconditions.checkNotNull(pbDescriptor, "Parse pb bytes to json, pb descriptor can't null!");
        Preconditions.checkNotNull(pbBytes, "Parse pb bytes to json, pb bytes can't null!");
        try {
            DynamicMessage message = DynamicMessage
                    .newBuilder(pbDescriptor)
                    .mergeFrom(pbBytes)
                    .build();
            String respJson = JsonFormat.printer()
                    .includingDefaultValueFields()
                    .printingEnumsAsInts()
                    .print(message);
            return new JSONObject(respJson);
        } catch (Exception ex) {
            log.error("Parse pb bytes to json failed, error msg:", ex);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "Parse pb bytes to json failed!");
        }
    }

}
