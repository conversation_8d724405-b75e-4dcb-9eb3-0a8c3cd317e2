package com.kuaishou.kwaishop.merchant.growth.center.common.model.bo;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class GrowthCenterWarmupConfigBO {

    /**
     * warmUp执行次数
     */
    private Integer warmUpCount = 1;

    /**
     * 各模块自定义参数
     */
    private String customArgs;


    public boolean inValid() {
        if (null == warmUpCount || warmUpCount <= 0) {
            return true;
        }
        return StringUtils.isBlank(customArgs);
    }
}
