package com.kuaishou.kwaishop.merchant.growth.center.common.constant;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-06
 * 命名规则：pre_模块_领域_方法名
 */
public class RedisKeyPrefixConstant {
    public static final String PRE_ACTIVITY_APPROVE_APPROVE = "pre_activity_approve_createApprove_";

    /**
     * 等级权益发放/回收处理，用户级分布式锁前缀
     */
    public static final String PRE_LEVEL_INTEREST_PROCESS = "pre_level_interest_process";

    /**
     * 加入达人公约分布式锁的前缀
     */
    public static final String TALENT_APPOINT_JOIN_LOCK_KEY = "talent_appoint_join_%s";

    /**
     * 策略报名分布式锁
     */
    public static final String STRATEGY_REGISTRATION_LOCK_KEY = "strategy_registration_%s_%s";

    /**
     * 退出策略分布式锁
     */
    public static final String STRATEGY_QUIT_REGISTRATION_LOCK_KEY = "strategy_quit_registration_%s_%s";
}
