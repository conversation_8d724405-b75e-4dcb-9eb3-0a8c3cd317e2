package com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math;

import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * 计算工具类
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-29
 */
public class ArithmeticHelper {

    private ArithmeticHelper() {

    }

    /**
     * 加
     */
    public static String add(String var1, String var2) {
        if (StringUtils.isAnyBlank(var1, var2)) {
            throw new BizException(PARAM_INVALID);
        }
        return new BigDecimal(var1).add(new BigDecimal(var2)).toPlainString();
    }

    /**
     * 除
     */
    public static String divide(String var1, String var2) {
        if (StringUtils.isAnyBlank(var1, var2)) {
            throw new BizException(PARAM_INVALID);
        }
        return new BigDecimal(var1).divide(new BigDecimal(var2), 2, RoundingMode.DOWN).toPlainString();
    }

    /**
     * 乘
     */
    public static String multiply(String var1, String var2) {
        if (StringUtils.isAnyBlank(var1, var2)) {
            throw new BizException(PARAM_INVALID);
        }
        return new BigDecimal(var1).multiply(new BigDecimal(var2)).toPlainString();
    }

    /**
     * 减
     */
    public static String subtract(String var1, String var2) {
        if (StringUtils.isAnyBlank(var1, var2)) {
            throw new BizException(PARAM_INVALID);
        }
        return new BigDecimal(var1).subtract(new BigDecimal(var2)).toPlainString();
    }

    /**
     * 平方计算
     */
    public static String square(String base, String index) {
        if (StringUtils.isAnyBlank(base, index)) {
            throw new BizException(PARAM_INVALID);
        }
        BigDecimal baseValue = BigDecimal.ZERO;
        if (StringUtil.isNotBlank(base)) {
            baseValue = new BigDecimal(base);
        }
        int indexValue = 0;
        if (StringUtil.isNotBlank(index)) {
            indexValue = Integer.parseInt(index);
        }
        if (indexValue == 0) {
            return "1";
        } else {
            while (indexValue-- > 0) {
                baseValue = baseValue.multiply(baseValue);
            }
            return baseValue.toPlainString();
        }
    }

    /**
     * 最大计算
     */
    public static String max(List<String> varList) {
        if (CollectionUtils.isEmpty(varList)) {
            throw new BizException(PARAM_INVALID);
        }
        BigDecimal currValue;
        BigDecimal maxValue = new BigDecimal(varList.get(0));
        for (String var : varList) {
            currValue = new BigDecimal(var);
            if (currValue.compareTo(maxValue) > 0) {
                maxValue = currValue;
            }
        }
        return maxValue.toPlainString();
    }

    /**
     * 最新计算
     */
    public static String min(List<String> varList) {
        if (CollectionUtils.isEmpty(varList)) {
            throw new BizException(PARAM_INVALID);
        }
        BigDecimal currValue;
        BigDecimal minValue = new BigDecimal(varList.get(0));
        for (String var : varList) {
            currValue = new BigDecimal(var);
            if (currValue.compareTo(minValue) < 0) {
                minValue = currValue;
            }
        }
        return minValue.toPlainString();
    }

    public static boolean judgeDouble(String s) {
        Pattern pattern = Pattern.compile("[+-]?\\d+(.\\d+)?");
        return pattern.matcher(s).matches();
    }

    public static boolean judgeParamOccupy(String s) {
        Pattern pattern = Pattern.compile("\\$\\{[1-9]\\d*\\}");
        return pattern.matcher(s).matches();
    }


}
