package com.kuaishou.kwaishop.merchant.growth.center.common.monitor;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.kuaishou.infra.ktrace.core.internal.propagator.UpstreamInfo;
import com.kuaishou.infra.ktrace.sdk.TraceContext;
import com.kuaishou.infra.kws.model.KwsModel;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-26
 */
public class Monitors {
    private static final Logger logger = LoggerFactory.getLogger("file_metric");

    private static final String DELIMITER = "|";

    private static final String NULL_STR = "null";

    public static void push(MonitorEvent event) {
        if (null == event) {
            return;
        }
        // traceId
        String traceId = TraceContext.traceId();
        // 上游服务
        String upstreamKsn = null;
        UpstreamInfo upstreamInfo = TraceContext.getUpstreamInfo();
        if (null != upstreamInfo) {
            upstreamKsn = Optional.ofNullable(upstreamInfo.getKws()).map(KwsModel::getServiceName).orElse(null);
        }
        String msg = StringUtils.joinWith(DELIMITER,
                StringUtils.isNotBlank(traceId) ? traceId : NULL_STR,
                StringUtils.isNotBlank(event.getBiz()) ? event.getBiz() : NULL_STR,
                StringUtils.isNotBlank(event.getMetricName()) ? event.getMetricName() : NULL_STR,
                event.isSuccess(),
                StringUtils.isNotBlank(event.getErrorCode()) ? event.getErrorCode() : NULL_STR,
                StringUtils.isNotBlank(event.getErrorMsg()) ? event.getErrorMsg() : NULL_STR,
                event.getRt(),
                StringUtils.isNotBlank(upstreamKsn) ? upstreamKsn : NULL_STR,
                StringUtils.isNotBlank(event.getBizId()) ? event.getBizId() : NULL_STR,
                StringUtils.isNotBlank(event.getExt()) ? event.getExt() : NULL_STR);
        logger.info(msg);
    }


}
