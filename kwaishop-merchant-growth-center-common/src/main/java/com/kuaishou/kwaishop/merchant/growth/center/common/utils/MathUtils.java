package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
public class MathUtils {

    private static final String CHECK_NUM_SCALE_PATTERN = "^\\d+(\\.\\d{1,%s})?$";

    /**
     * 判断数据精度是否满足 即判断一个小数的小数点后的位数是否小于等于 scale
     */
    public static boolean checkNumScale(String number, int scale) {
        String pattern = String.format(CHECK_NUM_SCALE_PATTERN, scale);
        return number.matches(pattern);
    }

    public static boolean moreThanZero(Long number) {
        return number != null && number > 0;
    }

    public static boolean moreThanZero(Integer number) {
        return number != null && number > 0;
    }


    public static void main(String[] args) {
        List<String> nums = Arrays.asList("15", "15.01", "15.02", "15.033", "15.111");
        for (String num : nums) {
            System.out.println(checkNumScale(num, 2));
        }
    }
}
