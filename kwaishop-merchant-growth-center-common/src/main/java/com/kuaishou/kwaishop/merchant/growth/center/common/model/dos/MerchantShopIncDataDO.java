package com.kuaishou.kwaishop.merchant.growth.center.common.model.dos;

import com.kuaishou.merchant.db.base.annotation.PrimaryKey;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资质元数据
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantShopIncDataDO {

    /** 主键 */
    @PrimaryKey
    private long id;

    private long userId;

    private long groupId;

    private long taskId;

    @Builder.Default
    private String taskAlias = "";

    private int status;

    private long createTime;

    private long updateTime;
}
