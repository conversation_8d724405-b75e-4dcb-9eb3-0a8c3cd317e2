package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-27
 */
public class ResponseUtils {

    public static int getResultCode(Throwable throwable) {
        if (throwable instanceof BizException) {
            return ((BizException) throwable).getCode();
        } else {
            return SERVER_ERROR.getCode();
        }
    }

    public static String getResultMsg(Throwable throwable) {
        if (throwable instanceof BizException) {
            return throwable.getMessage();
        } else {
            return SERVER_ERROR.getMessage();
        }
    }
}
