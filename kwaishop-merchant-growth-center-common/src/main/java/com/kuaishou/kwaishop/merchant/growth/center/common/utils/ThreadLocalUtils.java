package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-29
 * @description threadLocal工具类
 */
public class ThreadLocalUtils {
    private static final ThreadLocal<Map<String, Object>> THREAD_LOCAL_MAP =
            ThreadLocal.withInitial(HashMap::new);

    public static void setThreadLocalValue(String key, Object value) {
        THREAD_LOCAL_MAP.get().put(key, value);
    }

    public static Object getThreadLocalValue(String key) {
        return THREAD_LOCAL_MAP.get().get(key);
    }

    public static void removeThreadLocalValue(String key) {
        THREAD_LOCAL_MAP.get().remove(key);
    }

    public static void clearThreadLocals() {
        THREAD_LOCAL_MAP.remove();
    }

    public static <R> R execute(Supplier<R> executor) {
        R r;
        try {
            r = executor.get();
        } finally {
            clearThreadLocals();
        }
        return r;
    }
}
