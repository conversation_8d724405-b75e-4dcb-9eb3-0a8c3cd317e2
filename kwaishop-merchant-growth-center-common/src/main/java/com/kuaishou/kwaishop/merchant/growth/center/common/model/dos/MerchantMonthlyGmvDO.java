package com.kuaishou.kwaishop.merchant.growth.center.common.model.dos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商家月GMV with risk control
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantMonthlyGmvDO {

    /**
     * 主键
     */
    private long id;

    /**
     * 商家ID
     */
    private long userId;

    /**
     * GMV
     */
    private long gmv;

    /**
     * 是否被风控
     */
    private boolean blocked;

    /**
     * 数据时间分区
     */
    private int dt;

    /**
     * 状态码，默认为0
     */
    private int state;

    /**
     * 扩展参数，用来记录
     */
    private String extra;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 更新时间
     */
    private long updateTime;
}
