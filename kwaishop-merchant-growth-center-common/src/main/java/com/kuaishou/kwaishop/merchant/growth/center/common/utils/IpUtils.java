package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IpUtils {

    private static final Logger log = LoggerFactory.getLogger(IpUtils.class);

    public static void main(String[] args) throws Exception {
        System.out.println(getIp());
    }

    /**
     * 获取本机IP地址
     *
     * @return 本机IP地址
     */
    public static String getIp() {
        try {
            InetAddress localHost = getLocalHostExactAddress();
            return localHost.getHostAddress();
        } catch (Exception e) {
            log.error("getIp occur exception", e);
        }
        return null;
    }

    /**
     * 获取本机网卡IP地址
     *
     * @return IP网卡地址
     */
    private static InetAddress getLocalHostExactAddress() {
        try {
            InetAddress candidateAddress = null;
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface iface = networkInterfaces.nextElement();
                // 该网卡接口下的ip会有多个，也需要一个个的遍历，找到自己所需要的
                for (Enumeration<InetAddress> inetAddrs = iface.getInetAddresses(); inetAddrs.hasMoreElements(); ) {
                    InetAddress inetAddr = inetAddrs.nextElement();
                    // 排除loopback回环类型地址（不管是IPv4还是IPv6 只要是回环地址都会返回true）
                    if (!inetAddr.isLoopbackAddress()) {
                        // 如果是site-local地址
                        if (inetAddr.isSiteLocalAddress()) {
                            return inetAddr;
                        }
                        // 若不是site-local地址 那就记录下该地址当作候选
                        if (candidateAddress == null) {
                            candidateAddress = inetAddr;
                        }

                    }
                }
            }
            // 如果出去loopback回环地之外无其它地址了，那就回退到原始方案吧
            return candidateAddress == null ? InetAddress.getLocalHost() : candidateAddress;
        } catch (Exception e) {
            log.error("getLocalHostExactAddress occur exception", e);
        }
        return null;
    }
}
