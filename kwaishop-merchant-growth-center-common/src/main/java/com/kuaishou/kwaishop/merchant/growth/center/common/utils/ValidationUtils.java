package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;

import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.HibernateValidator;

import com.google.common.base.Joiner;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

/**
 * 统一校验工具类
 * https://hibernate.org/validator/
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-07
 */
public class ValidationUtils {
    /**
     * 开启快速结束模式 failFast (true)
     */
    private static final Validator VALIDATOR =
            Validation.byProvider(HibernateValidator.class).configure().failFast(false).buildValidatorFactory()
                    .getValidator();

    /**
     * 校验对象
     */
    public static <T> void validate(T obj) {
        Set<ConstraintViolation<T>> validationSets =
                VALIDATOR.validate(obj);
        if (CollectionUtils.isEmpty(validationSets)) {
            return;
        }
        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<T> violation : validationSets) {
            errors.add(violation.getPropertyPath().toString() + ":" + violation.getMessage());
        }
        throw new BizException(BasicErrorCode.PARAM_INVALID, Joiner.on(",").join(errors));
    }
}
