package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import static com.kuaishou.kwaishop.merchant.growth.center.common.config.kconf.MerchantGrowthJsonMapConfigKey.globalDowngradeConfig;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import com.kuaishou.kwaishop.merchant.growth.center.common.model.bo.GlobalDowngradeConfigBO;

/**
 * 全局降级开关配置
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-15
 */
public class GlobalDowngradeUtils {
    public static GlobalDowngradeConfigBO getGlobalDowngradeConfig(String scene) {
        if (StringUtils.isEmpty(scene)) {
            return null;
        }
        Map<String, GlobalDowngradeConfigBO> globalDowngradeConfigBOMap =
                globalDowngradeConfig.getMap(String.class, GlobalDowngradeConfigBO.class);
        if (CollectionUtils.isEmpty(globalDowngradeConfigBOMap)) {
            return null;
        }
        return globalDowngradeConfigBOMap.get(scene);
    }
}
