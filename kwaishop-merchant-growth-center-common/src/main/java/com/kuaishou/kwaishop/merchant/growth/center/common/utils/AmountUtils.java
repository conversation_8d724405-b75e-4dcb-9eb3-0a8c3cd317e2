package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.kuaishou.kwaishop.merchant.growth.center.common.model.enums.BasicUnitEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.converter.enums.ShowUnitEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-07-24
 */
public class AmountUtils {
    /**
     * 金额为分的格式
     */
    private static final String CURRENCY_FEN_REGEX = "\\-?[0-9]+";

    private static final long FEN = 100L;

    private static final long BAI = 100L;

    private static final long WAN = 10000L;

    private static final long QIAN_WAN = 10000000L;

    private static final long YI = 10000 * 10000L;

    private static final long HOUR_DAY = 24;
    // 一秒钟的毫秒数
    private static final long SECOND_MILLISECONDS = 1000;
    // 一分钟的毫秒数
    private static final long MINUTE_MILLISECONDS = SECOND_MILLISECONDS * 60;
    // 一小时的毫秒数
    private static final long HOUR_MILLISECONDS = MINUTE_MILLISECONDS * 60;
    // 一天的毫秒数
    private static final long DAY_MILLISECONDS = HOUR_MILLISECONDS * HOUR_DAY;


    /**
     * 数值转换
     */
    public static String convertToShowValue(Long value, String unit) {
        String res;

        if (unit.equals("元")) {
            value = value / 100;
        }
        if (value < WAN) {
            res = String.valueOf(value);
        } else if (value < QIAN_WAN) {
            res = BigDecimal.valueOf(value).divide(new BigDecimal(WAN)).setScale(2, RoundingMode.DOWN).toString();
            res = res + "万";
        } else if (value < YI) {
            res = BigDecimal.valueOf(value).divide(new BigDecimal(WAN)).setScale(0, RoundingMode.DOWN).toString();
            res = res + "万";
        } else {
            res = BigDecimal.valueOf(value).divide(new BigDecimal(YI)).setScale(2, RoundingMode.DOWN).toString();
            res = res + "亿";
        }
        return res;
    }

    /**
     * 根据单位，转换展示类型
     */
    public static String convertToShowValueWithDecimal(Long value, String unit, int scale) {
        if (value == null || value == 0) {
            return "0";
        }
        scale = scale != 0 ? scale : 2;
        ShowUnitEnum unitType = ShowUnitEnum.getByUnit(unit);
        String result;
        long unitDivide = 1;
        switch (unitType) {
            case HOUR:
                unitDivide = HOUR_MILLISECONDS;
                break;
            case MINUTE:
                unitDivide = MINUTE_MILLISECONDS;
                break;
            case SECOND:
                unitDivide = SECOND_MILLISECONDS;
                break;
            case YUAN:
                unitDivide = FEN;
                break;
            case FEN:
            case MILLISECOND:
            case OTHER:
            default:
        }
        result = new BigDecimal(value)
                .divide(new BigDecimal(unitDivide), scale, RoundingMode.DOWN).toPlainString();
        return result;
    }

    /**
     * 将分为单位的转换为元并返回金额格式的字符串 （除100）
     */
    public static String changeF2Y(Long amount) {
        if (!amount.toString().matches(CURRENCY_FEN_REGEX)) {
            throw BizException.ofMessage(PARAM_INVALID, "金额格式有误");
        }

        int flag = 0;
        String amString = amount.toString();
        if (amString.charAt(0) == '-') {
            flag = 1;
            amString = amString.substring(1);
        }
        StringBuffer result = new StringBuffer();
        if (amString.length() == 1) {
            result.append("0.0").append(amString);
        } else if (amString.length() == 2) {
            result.append("0.").append(amString);
        } else {
            String intString = amString.substring(0, amString.length() - 2);
            for (int i = 1; i <= intString.length(); i++) {
                if ((i - 1) % 3 == 0 && i != 1) {
                    result.append(",");
                }
                result.append(intString.substring(intString.length() - i, intString.length() - i + 1));
            }
            result.reverse().append(".").append(amString.substring(amString.length() - 2));
        }
        if (flag == 1) {
            return "-" + result.toString();
        } else {
            return result.toString();
        }
    }

    /**
     * 将分为单位的转换为元 （除100）
     */
    public static String changeF2Y(String amount) {
        if (!amount.matches(CURRENCY_FEN_REGEX)) {
            throw BizException.ofMessage(PARAM_INVALID, "金额格式有误");
        }
        BigDecimal res = BigDecimal.valueOf(Long.parseLong(amount)).divide(new BigDecimal(100));
        return res.stripTrailingZeros().toPlainString();
    }

    /**
     * 将分转为元 保留两位小数
     */
    public static String changeF2YWith2Decimal(Long amount) {
        if (null == amount) {
            return "0";
        }
        BigDecimal res = BigDecimal.valueOf(amount).divide(new BigDecimal(100), 2, RoundingMode.DOWN);
        return res.stripTrailingZeros().toPlainString();
    }

    /**
     * 将分为单位的转换为元/万元
     */
    public static String changeF2YWithY(long amount, BasicUnitEnum unit) {
        if (amount <= 0L) {
            return "0" + unit.getDesc();
        }
        BigDecimal res;
        if (amount >= YI * 100) {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(100 * YI)).setScale(2, RoundingMode.FLOOR);
            return res.stripTrailingZeros().toPlainString() + "亿" + unit.getDesc();
        } else if (amount >= WAN * 100) {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(100 * WAN)).setScale(2, RoundingMode.FLOOR);
            return res.stripTrailingZeros().toPlainString() + "万" + unit.getDesc();
        } else {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(100));
            return res.stripTrailingZeros().toPlainString() + unit.getDesc();
        }
    }

    /**
     * 金额取整
     * 不足万，向元取整
     * 不足千万，向万取整
     * 不足亿，向千万取整
     * 超过亿，向亿取整
     */
    public static Long changeF2Rounding(long amount, boolean isRoundingDown) {
        if (amount <= 0L) {
            return 0L;
        }
        RoundingMode roundingMode = isRoundingDown ? RoundingMode.DOWN : RoundingMode.UP;
        amount =
                new BigDecimal(amount).divide(new BigDecimal(FEN), 0, roundingMode).longValue();
        BigDecimal res;
        BigDecimal roundingDecimal;
        if (amount <= WAN) {
            roundingDecimal = new BigDecimal(1);
        } else if (amount <= QIAN_WAN) {
            roundingDecimal = new BigDecimal(WAN);
        } else if (amount <= YI) {
            roundingDecimal = new BigDecimal(QIAN_WAN);
        } else {
            roundingDecimal = new BigDecimal(YI);
        }
        String roundingAfter = new BigDecimal(amount)
                .divide(roundingDecimal, 2, roundingMode).toPlainString();
        res = new BigDecimal(roundingAfter).multiply(roundingDecimal);

        return res.multiply(new BigDecimal(FEN)).longValue();
    }

    /**
     * 金额大于等于一万元转换为元/万元 保留一位小数
     */
    public static String changeF2WanYRounding1(long amount) {
        if (amount <= 0L) {
            return "0";
        }
        BigDecimal res;
        if (amount >= WAN * 100) {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(100 * WAN)).setScale(1, RoundingMode.FLOOR);
            if (amount % (WAN * 100) == 0) {
                return res.stripTrailingZeros().toPlainString() + "万";
            } else {
                return res.toPlainString() + "万";
            }
        } else {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(100));
            return res.stripTrailingZeros().toPlainString();
        }
    }

    /**
     *大于等于一万转换为万 保留一位小数
     */
    public static String change2Wan(long amount) {
        if (amount <= 0L) {
            return "0";
        }
        BigDecimal res;
        if (amount >= WAN) {
            res = BigDecimal.valueOf(amount).divide(new BigDecimal(WAN)).setScale(1, RoundingMode.FLOOR);
            if (amount % WAN == 0) {
                return res.stripTrailingZeros().toPlainString() + "万";
            } else {
                return res.toPlainString() + "万";
            }
        } else {
            res = BigDecimal.valueOf(amount);
            return res.stripTrailingZeros().toPlainString();
        }
    }

    /**
     * 将分转为元，四舍五入保留两位小数
     */
    public static String changeF2YRounding(long amount) {
        BigDecimal cent = new BigDecimal(amount);
        return String.valueOf(cent.divide(new BigDecimal(BAI), 2, RoundingMode.HALF_UP));
    }

    /**
     * 将元为单位的转换为分 （乘100）
     */
    public static String changeY2F(Long amount) {
        return BigDecimal.valueOf(amount).multiply(new BigDecimal(100)).toPlainString();
    }

    /**
     * 将元为单位的转换为分 替换小数点，支持以逗号区分的金额
     */
    public static String changeY2F(String amount) {
        String currency = amount.replaceAll("\\$|\\￥|\\,", "");  //,处理包含 ￥ 或者$的金额
        int index = currency.indexOf(".");
        int length = currency.length();
        Long amLong = 0L;
        if (index == -1) {
            amLong = Long.valueOf(currency + "00");
        } else if (length - index >= 3) {
            amLong = Long.valueOf((currency.substring(0, index + 3)).replace(".", ""));
        } else if (length - index == 2) {
            amLong = Long.valueOf((currency.substring(0, index + 2)).replace(".", "") + 0);
        } else {
            amLong = Long.valueOf((currency.substring(0, index + 1)).replace(".", "") + "00");
        }
        return amLong.toString();
    }

    /**
     * 将厘转为分 （除10）向下取整
     */
    public static Long changeLi2Fen(Long amount) {
        return amount / 10;
    }
}

