package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.kuaishou.framework.util.ObjectMapperUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-02
 */
@Slf4j
public class ThreadUtils {
    public static <T, R> List<R> fastFailThreadExecute(List<T> paramList, ExecutorService executorService,
            Function<T, R> function) {
        AtomicBoolean hasError = new AtomicBoolean(false);
        List<Future<Callable<R>>> list = paramList.stream().map(param -> {
            if (hasError.get()) {
                return null;
            }
            return executorService.submit(() -> (Callable<R>) () -> {
                R result = null;
                try {
                    result = function.apply(param);
                } catch (Exception e) {
                    log.error("线程执行异常, param: {}", ObjectMapperUtils.toJSON(param), e);
                    hasError.set(Boolean.TRUE);
                }
                return result;
            });
        }).collect(Collectors.toList());
        return list.stream().filter(Objects::nonNull).map(future -> {
            try {
                return future.get().call();
            } catch (Exception e) {
                log.error("线程执行异常", e);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
