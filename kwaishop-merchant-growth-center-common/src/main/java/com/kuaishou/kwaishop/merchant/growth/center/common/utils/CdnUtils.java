package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.kuaishou.cdn.api.dispatch.ApiCdnDispatch;
import com.kuaishou.cdn.common.model.CdnBizType;
import com.kuaishou.cdn.common.model.CdnDispatchContext;
import com.kuaishou.cdn.common.model.CdnDispatchRequest;
import com.kuaishou.cdn.common.model.CdnDispatchResponse;
import com.kuaishou.cdn.dispatch.core.builder.CdnDispatchResult;
import com.kuaishou.freetraffic.constant.FreeTrafficProduct;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-25
 */
@Slf4j
public class CdnUtils {

    public static final String CDN_PROJECT_NAME = "kwaishop-img-default";

    /**
     * 单个URL调度
     *
     * @param urlPath 文件地址
     * @param cdnBizType cdn资源类型
     * @param userId 用户Id
     * @param forceHttps 是否使用Https
     * @return
     */
    public static String getCdnUrl(String urlPath, CdnBizType cdnBizType, Long userId, boolean forceHttps) {
        if (StringUtils.isBlank(urlPath)) {
            return urlPath;
        }
        cdnBizType = cdnBizType == null ? CdnBizType.IMAGE : cdnBizType;
        CdnDispatchContext dispatchContext = CdnDispatchContext.newBuilder()    // CDN调度参数
                .setFreeTrafficProduct(FreeTrafficProduct.UNKNOWN)
                .setIsHttps(forceHttps)
                .build();
        CdnDispatchRequest.Builder builder = CdnDispatchRequest.newBuilder()
                .setProjectName(CDN_PROJECT_NAME)
                .setUrlPath(urlPath)
                .setDispatchContext(dispatchContext);
        if (userId != null && userId > 0L) {
            builder.setUserId(userId);
        }
        try {
            List<CdnDispatchResponse> responseList = ApiCdnDispatch.unionDispatch(builder.build(), cdnBizType);
            // 标准用法：每次使用的时候都从SDK实时获取结果，不做额外的处理和缓存操作。这样能够做到域名的故障切换/逃生和CDN调度策略时效性。
            // 调用结果包含多个可用的URL，第一个为主，后续为备。建议下载CDN资源的时候加入兜底逻辑，主不可用的时候兜底到备份域名。
            return responseList.stream().findAny().map(CdnDispatchResponse::getUrl).orElse(urlPath);
        } catch (Exception e) {
            log.error("[CDN调度失败] urlPath:{}, cdnBizType:{}", urlPath, cdnBizType, e);
            return urlPath;
        }
    }

    /**
     * 批量调度：适合高QPS场景
     *
     * @param urlPathList 文件地址
     * @param cdnBizType cdn资源类型
     * @param userId 用户Id
     * @param forceHttps 是否使用Https
     */
    public static List<String> getCdnUrlList(List<String> urlPathList, CdnBizType cdnBizType, Long userId,
            boolean forceHttps) {
        if (CollectionUtils.isEmpty(urlPathList)) {
            return Collections.emptyList();
        }
        cdnBizType = cdnBizType == null ? CdnBizType.IMAGE : cdnBizType;
        CdnDispatchContext dispatchContext = CdnDispatchContext.newBuilder()    // CDN调度参数
                .setFreeTrafficProduct(FreeTrafficProduct.UNKNOWN)
                .setIsHttps(forceHttps)
                .build();
        CdnDispatchRequest.Builder builder = CdnDispatchRequest.newBuilder()
                .setProjectName(CDN_PROJECT_NAME)
                .setUrlPathList(urlPathList)
                .setDispatchContext(dispatchContext);
        if (userId != null && userId > 0L) {
            builder.setUserId(userId);
        }
        try {
            List<CdnDispatchResult> responseList = ApiCdnDispatch.batchUnionDispatch(builder.build(), cdnBizType);
            // 标准用法：每次使用的时候都从SDK实时获取结果，不做额外的处理和缓存操作。这样能够做到域名的故障切换/逃生和CDN调度策略时效性。
            // 调用结果包含多个可用的URL，第一个为主，后续为备。建议下载CDN资源的时候加入兜底逻辑，主不可用的时候兜底到备份域名。
            return responseList.stream()
                    .map(result -> result.getDispatchResultList().get(0).getUrl())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("[CDN批量调度失败] urlPathList:{}, bizType:{}", urlPathList, cdnBizType, e);
            return urlPathList;
        }
    }
}