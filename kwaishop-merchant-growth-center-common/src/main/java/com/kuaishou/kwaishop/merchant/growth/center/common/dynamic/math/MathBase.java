package com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math;

import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.LEFT_BRACKET;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.LOWEST_OPERATOR;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MAX_MIN_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.MULTIMODAL_OPERATOR;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.OPERATOR_PRECEDENCE;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.RIGHT_BRACKET;
import static com.kuaishou.kwaishop.merchant.growth.center.common.dynamic.math.MathConstants.RIGHT_SQUARE_BRACKET;

import java.util.Stack;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.csp.sentinel.util.StringUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-05
 */
public class MathBase {

    /**
     * 将表达式转换为后缀式，并存入后缀式栈中
     */
    public static Stack<String> convertToPostfix(String expression, boolean replaceSpace) {
        if (replaceSpace) {
            expression = StringUtils.replace(expression, " ", "");
        }
        // 后缀式栈
        Stack<String> postfixStack = new Stack<>();
        // 运算符栈
        Stack<String> operatorStack = new Stack<>();
        // 最大值最小值参数个数栈
        Stack<Integer> paramCountStack = new Stack<>();

        // 栈底放入最低优先级运算符号
        operatorStack.push(",");
        // 当前字符的位置
        int currentIndex = 0;
        // 上次运算符到本次运算符之间的长度(用于获取数值)
        int count = 0;
        // 当前操作字符和栈顶字符
        String currentOp, peekOp;
        for (; currentIndex < expression.length(); currentIndex++) {
            currentOp = expression.substring(currentIndex, currentIndex + 1);
            // 如果当前字符是运算符
            if (isOperator(currentOp)) {
                //取两个运算符之间的数值
                if (count > 0) {
                    postfixStack.push(expression.substring(currentIndex - count, currentIndex));
                }
                peekOp = operatorStack.peek();
                // MAX、MIN运算符特殊处理：
                if (StringUtil.equals(currentOp, MAX_MIN_PREFIX)) {
                    operatorStack.push(expression.substring(currentIndex, currentIndex + 3));
                    paramCountStack.push(0);
                    currentIndex += 3;
                    // 遇到“,”运算符
                } else if (StringUtil.equals(currentOp, LOWEST_OPERATOR)) {
                    // 参数栈顶参数个数+1
                    increaseStackTop(paramCountStack);
                    // 将运算符栈中的元素移到后缀式栈中直到遇到最值符号
                    while (!MULTIMODAL_OPERATOR.contains(operatorStack.peek())) {
                        postfixStack.push(operatorStack.pop());
                    }
                    // 遇到取最值运算结束符
                } else if (StringUtil.equals(RIGHT_SQUARE_BRACKET, currentOp)) {
                    // 将运算符栈中的元素移到后缀式栈中直到遇到最值符号
                    while (!MULTIMODAL_OPERATOR.contains(operatorStack.peek())) {
                        postfixStack.push(operatorStack.pop());
                    }
                    // 将最值运算参数个数和最值运算符依次存入后缀式栈
                    postfixStack.push(String.valueOf(paramCountStack.pop() + 1));
                    postfixStack.push(operatorStack.pop());
                    // 遇到反括号则将运算符栈中的元素移除到后缀式栈中直到遇到左括号
                } else if (StringUtil.equals(RIGHT_BRACKET, currentOp)) {
                    while (!StringUtil.equals(LEFT_BRACKET, operatorStack.peek())) {
                        postfixStack.push(operatorStack.pop());
                    }
                    operatorStack.pop();
                } else {
                    // 将运算符栈中，比当前运算符优先级大的符号都转移到后缀式栈中
                    while (!StringUtil.equals(LEFT_BRACKET, currentOp) && !StringUtil.equals(LOWEST_OPERATOR, peekOp)
                            && operatorCompare(currentOp, peekOp)) {
                        postfixStack.push(operatorStack.pop());
                        peekOp = operatorStack.peek();
                    }
                    operatorStack.push(currentOp);
                }
                count = 0;
            } else {
                count++;
            }
        }
        // 最后一个字符不是运算符，加入后缀式栈中
        if (count > 0) {
            postfixStack.push(expression.substring(currentIndex - count, currentIndex));
        }
        // 将运算符栈中的剩余元素加入到后缀式栈中
        while (!StringUtil.equals(LOWEST_OPERATOR, operatorStack.peek())) {
            postfixStack.push(operatorStack.pop());
        }
        return postfixStack;
    }

    /**
     * 判断是否为运算符号，其中"M"标识MAX或者MIN
     */
    public static boolean isOperator(String op) {
        return (OPERATOR_PRECEDENCE.containsKey(op) || MAX_MIN_PREFIX.equals(op));
    }

    /**
     * 将整数栈顶元素值+1
     */
    public static void increaseStackTop(Stack<Integer> stack) {
        if (!stack.isEmpty()) {
            Integer temp = stack.pop();
            temp += 1;
            stack.push(temp);
        }
    }

    /**
     * 比较栈顶操作符和当前操作符的优先级，如果大于返回true
     */
    public static boolean operatorCompare(String cur, String peek) {
        return OPERATOR_PRECEDENCE.get(peek) >= OPERATOR_PRECEDENCE.get(cur);
    }
}
