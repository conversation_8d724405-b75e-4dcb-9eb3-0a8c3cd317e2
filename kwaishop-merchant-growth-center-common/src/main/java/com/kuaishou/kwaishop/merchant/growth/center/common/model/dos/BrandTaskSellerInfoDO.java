package com.kuaishou.kwaishop.merchant.growth.center.common.model.dos;

import com.kuaishou.merchant.db.base.annotation.PrimaryKey;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 品牌商家信息表
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-10-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandTaskSellerInfoDO {

    /**
     * 主键
     */
    @PrimaryKey
    private long id;

    /**
     * 商家ID
     */
    private long userId;

    /**
     * 6-9月单日GMV峰值（风控前）分层用
     */
    private long topDayGmv;

    /**
     * GMV峰值日期
     */
    private String topDayGmvDate;

    /**
     * 6-9月单场直播GMV峰值（风控前）计算用
     */
    private long topLiveGmv;

    /**
     * 直播ID
     */
    private long topLiveGmvId;

    /**
     * 单场直播峰值日期
     */
    private String topLiveGmvDate;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 更新时间
     */
    private long updateTime;

    /**
     * 报名状态
     */
    private int status;

    /**
     * 版本信息
     */
    private String version;

    /**
     * 任务组ID 多个groupId 以 "," 分隔
     */
    private String groupId;

    /**
     * 任务组别名 多个alias 以 "," 分隔
     */
    private String groupAlias;
}
