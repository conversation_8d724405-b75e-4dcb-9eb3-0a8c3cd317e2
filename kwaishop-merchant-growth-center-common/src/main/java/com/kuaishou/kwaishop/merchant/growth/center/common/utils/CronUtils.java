package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.quartz.CronExpression;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-11
 */
@Slf4j
public class CronUtils {
    private static final String DAILY_CRON_PATTERN = "%s %s %s ? * *";

    private static final String INTERVAL_DAY_CRON_PATTERN = "%s %s %s */%s * ?";

    public static List<Date> getCronExecutionTimes(Date startTime, Date endTime, String cronExpression) {
        List<Date> executionTimes = Lists.newArrayList();
        try {
            CronExpression cron = new CronExpression(cronExpression);

            // 获取定时任务的下次执行时间
            Date nextExecutionTime = cron.getNextValidTimeAfter(startTime);
            while (nextExecutionTime != null && nextExecutionTime.before(endTime)) {
                // 添加下次执行时间到结果列表中
                executionTimes.add(nextExecutionTime);
                // 计算下一次的执行时间
                nextExecutionTime = cron.getNextValidTimeAfter(nextExecutionTime);
            }
        } catch (ParseException e) {
            log.error("cron表达式解析失败，cronExpression:{}", cronExpression, e);
            throw new RuntimeException("cron表达式解析失败，请检查cron表达式是否正确");
        }
        return executionTimes;
    }

    /**
     * 生成日维度定时任务cron表达式
     */
    public static String generateDailyDimensionCronExpression(Integer hour, Integer minute, Integer second,
            Integer intervalDays) {
        if (intervalDays == 1) {
            // "0 15 10 ? * *" 每天的10:15:00
            return String.format(DAILY_CRON_PATTERN, second, minute, hour);
        }
        // "0 15 10 */2 * ?" 代表每隔2天的10:15:00
        return String.format(INTERVAL_DAY_CRON_PATTERN, second, minute, hour, intervalDays);
    }
}
