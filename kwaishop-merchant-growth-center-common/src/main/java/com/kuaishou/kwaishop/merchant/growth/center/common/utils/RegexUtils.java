package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-25
 */
public class RegexUtils {
    public static boolean containsLetter(String str) {
        return str.matches(".*[a-zA-Z]+.*");
    }

    public static void main(String[] args) {
        String a = "123";
        String b = "-";
        String c = "12a2";
        String d = "ab";
        System.out.println(containsLetter(a));
        System.out.println(containsLetter(b));
        System.out.println(containsLetter(c));
        System.out.println(containsLetter(d));
    }
}
