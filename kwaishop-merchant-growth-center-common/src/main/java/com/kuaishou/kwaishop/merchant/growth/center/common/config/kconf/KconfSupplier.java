package com.kuaishou.kwaishop.merchant.growth.center.common.config.kconf;

import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kconf.client.Kconf;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-08-01
 */
public interface KconfSupplier<T> extends Supplier<T> {

    String K_CONF_BIZ = "kwaishop";
    String K_CONF_NAMESPACE = "merchantGrowthCenter";
    String K_CONF_KEY_SPLIT = ".";

    default String getConfigKey() {
        String nameSpace = configKeyNameSpace();
        if (StringUtils.isBlank(nameSpace)) {
            throw new IllegalArgumentException(String.format("kConf %s name space must don't blank",
                    K_CONF_BIZ));
        }

        String configKey = configKey();
        if (StringUtils.isBlank(configKey)) {
            throw new IllegalArgumentException(String.format("kConf %s.%s config key must don't blank",
                    K_CONF_BIZ, nameSpace));
        }

        return K_CONF_BIZ + K_CONF_KEY_SPLIT + nameSpace + K_CONF_KEY_SPLIT + configKey;
    }

    String configKey();

    T defaultValue();

    Kconf<T> getKConf();

    default String configKeyNameSpace() {
        return K_CONF_NAMESPACE;
    }

    @Override
    default T get() {
        Kconf<T> kConf = getKConf();
        if (kConf == null) {
            return defaultValue();
        }

        return kConf.get();
    }
}
