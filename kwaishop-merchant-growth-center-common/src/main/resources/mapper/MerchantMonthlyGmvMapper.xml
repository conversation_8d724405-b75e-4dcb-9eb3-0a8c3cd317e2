<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaishou.kwaishop.merchant.growth.center.common.dao.MerchantMonthlyGmvMapper">

<sql id="Base_Column_List">
  id, user_id, gmv, blocked, dt, state, extra, create_time, update_time
</sql>

  <select id="selectByPrimaryKey" resultType="com.kuaishou.kwaishop.merchant.growth.center.common.model.dos.MerchantMonthlyGmvDO">
    select
    <include refid="Base_Column_List"/>
     from merchant_monthly_gmv
    <where>
      id = #{id}
    </where>
    limit 1
  </select>

  <select id="listByDtAndStatus" resultType="com.kuaishou.kwaishop.merchant.growth.center.common.model.dos.MerchantMonthlyGmvDO">
    select
    <include refid="Base_Column_List"/>
    from merchant_monthly_gmv
    <where>
    dt = #{dt}
    and id &gt;= #{cursor}
    and gmv &lt;= #{gmv}
    and state in
      <foreach collection="statusList" item="state" index="index" open="(" close=")" separator=",">
        #{state}
      </foreach>
    </where>
    order by id asc limit #{limit}
  </select>


  <select id="countByDt" resultType="java.lang.Long">
    select count(1)
    from merchant_monthly_gmv
    <where>
      dt = #{dt}
    </where>
  </select>

  <select id="selectByUserId" resultType="com.kuaishou.kwaishop.merchant.growth.center.common.model.dos.MerchantMonthlyGmvDO">
    select
    <include refid="Base_Column_List"/>
    from merchant_monthly_gmv
    <where>
      user_id = #{userId}
    </where>
    limit 1
  </select>
  <!--  @Update("update merchant_monthly_gmv set state=#{state}, extra=#{extra}, update_time=#{updateTime} where id = #{id}")-->

  <update id="updateState" parameterType="com.kuaishou.kwaishop.merchant.growth.center.common.model.dos.MerchantMonthlyGmvDO">
    update merchant_monthly_gmv
    set state = #{state},
    extra = #{extra},
    update_time = #{updateTime}
    <where>
      id = #{id}
    </where>
  </update>


</mapper>