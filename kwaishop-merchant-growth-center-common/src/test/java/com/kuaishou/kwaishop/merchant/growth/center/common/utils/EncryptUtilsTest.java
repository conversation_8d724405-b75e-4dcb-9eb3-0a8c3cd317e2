package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-01-02
 */
class EncryptUtilsTest {

    @Test
    void testEncrypt() {
        // 提取 userId 参数的值
        String userIdEncoded = "ChRvcGVyYXRpb25QbGFuLnVzZXJJZBIg6NTMpQanz%2Bgyoo5JujjA3YNknha39p%2F9J3%2Fd15of" +
                "%2BtUaEr1JqOxp2g%2FnkDFQo%2Foj9%2FCe7iIgKLm3rH%2FWVm3Bmd%2FAoG%2BsQALbGEbDiHhP0XmtMp5lDkkoDzAB";

        try {
            // 解码 userId 参数的值
            String userIdDecoded = URLDecoder.decode(userIdEncoded, "UTF-8");
            System.out.println("解码后的 userId: " + userIdDecoded);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

}
