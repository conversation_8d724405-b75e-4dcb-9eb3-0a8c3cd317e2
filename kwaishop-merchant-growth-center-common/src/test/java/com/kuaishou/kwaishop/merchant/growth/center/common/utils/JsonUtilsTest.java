package com.kuaishou.kwaishop.merchant.growth.center.common.utils;

import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.JsonUtils.replacePlaceholders;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-23
 */
class JsonUtilsTest {


    @Test
    void testReplacePlaceholders() {
        String template = "[\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"目标触达商家数\",\n" +
                "        \"level\": 1,\n" +
                "        \"value\": \"${actionTargetUserCnt}\",\n" +
                "        \"fillOpacity\": 1,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 2,\n" +
                "                \"value\": \"${actionSendSuccessRatio}%\",\n" +
                "                \"stroke\": \"red\",\n" +
                "                \"position\": \"left\",\n" +
                "                \"fontColor\": \"red\",\n" +
                "                \"lineWidth\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"to\": 4,\n" +
                "                \"value\": \"${actionTargetFinishRatio}%\",\n" +
                "                \"stroke\": \"red\",\n" +
                "                \"position\": \"left\",\n" +
                "                \"fontColor\": \"red\",\n" +
                "                \"lineWidth\": 1\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"成功发送商家数\",\n" +
                "        \"level\": 2,\n" +
                "        \"value\": \"${actionSendSuccessUserCnt}%\",\n" +
                "        \"fillOpacity\": 0.9,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 3,\n" +
                "                \"value\": \"${actionReachSuccessRatio}%\",\n" +
                "                \"position\": \"left\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"有效触达商家数\",\n" +
                "        \"level\": 3,\n" +
                "        \"value\": \"${actionReachSuccessUserCnt}\",\n" +
                "        \"fillOpacity\": 0.8,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 4,\n" +
                "                \"value\": \"${actionTargetFinishRatio}%\",\n" +
                "                \"position\": \"left\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"执行目标商家数\",\n" +
                "        \"level\": 4,\n" +
                "        \"value\": \"${actionTargetFinishCnt}\",\n" +
                "        \"fillOpacity\": 0.7\n" +
                "    }\n" +
                "]";
        Map<String, Object> funnelData = new HashMap<>();
        funnelData.put("actionTargetUserCnt", 11000); // 目标触达商家数
        funnelData.put("actionSendSuccessUserCnt", 10000); // 发送成功商家数
        funnelData.put("actionReachSuccessUserCnt", 8000); // 有效触达商家数
        funnelData.put("actionTargetFinishCnt", 700); // 执行目标商家数
        funnelData.put("actionSendSuccessRatio", 80); // 触达执行率
        funnelData.put("actionReachSuccessRatio", 132); // 有效触达率
        funnelData.put("actionTargetFinishRatio", 121); // 目标完成率
        funnelData.put("targetSumValue", 121); // 累计目标达成值

        try {
            String result = replacePlaceholders(template, funnelData, true);
            System.out.println(result);
        } catch (IllegalArgumentException e) {
            System.err.println(e.getMessage());
        }
    }

    void testReplacePlaceholdersMissKey() {
        String template = "[\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"目标触达商家数\",\n" +
                "        \"level\": 1,\n" +
                "        \"value\": \"${actionTargetUserCnt}\",\n" +
                "        \"fillOpacity\": 1,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 2,\n" +
                "                \"value\": \"${actionSendSuccessRatio}%\",\n" +
                "                \"stroke\": \"red\",\n" +
                "                \"position\": \"left\",\n" +
                "                \"fontColor\": \"red\",\n" +
                "                \"lineWidth\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"to\": 4,\n" +
                "                \"value\": \"${actionTargetFinishRatio}%\",\n" +
                "                \"stroke\": \"red\",\n" +
                "                \"position\": \"left\",\n" +
                "                \"fontColor\": \"red\",\n" +
                "                \"lineWidth\": 1\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"成功发送商家数\",\n" +
                "        \"level\": 2,\n" +
                "        \"value\": \"${actionSendSuccessUserCnt}%\",\n" +
                "        \"fillOpacity\": 0.9,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 3,\n" +
                "                \"value\": \"${actionReachSuccessRatio}%\",\n" +
                "                \"position\": \"left\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"有效触达商家数\",\n" +
                "        \"level\": 3,\n" +
                "        \"value\": \"${actionReachSuccessUserCnt}\",\n" +
                "        \"fillOpacity\": 0.8,\n" +
                "        \"relationshipList\": [\n" +
                "            {\n" +
                "                \"to\": 4,\n" +
                "                \"value\": \"${actionTargetFinishRatio}%\",\n" +
                "                \"position\": \"left\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": \"left\",\n" +
                "        \"label\": \"执行目标商家数\",\n" +
                "        \"level\": 4,\n" +
                "        \"value\": \"${actionTargetFinishCnt}\",\n" +
                "        \"fillOpacity\": 0.7\n" +
                "    }\n" +
                "]";
        Map<String, Object> funnelData = new HashMap<>();
        funnelData.put("actionTargetUserCnt", 11000); // 目标触达商家数
        funnelData.put("actionSendSuccessUserCnt", 10000); // 发送成功商家数
        funnelData.put("actionReachSuccessUserCnt", 8000); // 有效触达商家数
        funnelData.put("actionTargetFinishCnt", 700); // 执行目标商家数
//        funnelData.put("actionSendSuccessRatio", 80); // 触达执行率
        funnelData.put("actionReachSuccessRatio", 132); // 有效触达率
        funnelData.put("actionTargetFinishRatio", 121); // 目标完成率
        funnelData.put("targetSumValue", 121); // 累计目标达成值

        String result = replacePlaceholders(template, funnelData, true);
        System.out.println(result);
        assertThrows(IllegalArgumentException.class, () -> {
            replacePlaceholders(template, funnelData, true);
        });
    }

}
