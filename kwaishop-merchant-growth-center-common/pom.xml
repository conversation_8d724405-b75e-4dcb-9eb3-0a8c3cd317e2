<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>kwaishop-merchant-growth-center-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>kwaishop-merchant-growth-center-common</artifactId>
    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-center-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-interest-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-center-kit</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 电商依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-framework-common</artifactId>
            <!--            <version>1.0.15</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-user-sdk</artifactId>
            <!--            <version>1.0.217</version>-->
            <exclusions>
                <exclusion>
                    <groupId>kuaishou</groupId>
                    <artifactId>kuaishou-merchant-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>kuaishou</groupId>
                    <artifactId>kwaishop-education-center-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-utils-adapter-utils</artifactId>
            <!--            <version>1.0.33</version>-->
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-utils-adapter-db</artifactId>
            <!--            <version>1.0.33</version>-->
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-shop-sdk</artifactId>
            <!--            <version>1.1.14</version>-->
        </dependency>

        <!-- 快手依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-biz-def</artifactId>
            <!--            <version>1.0.1649</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-env-utils</artifactId>
            <!--            <version>1.0.7</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-client</artifactId>
        </dependency>

        <!-- 基础架构依赖 -->
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-redis-impl</artifactId>
            <!--            <version>1.0.476</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-api</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-perf-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-starter-krpc</artifactId>
        </dependency>

        <!-- 三方依赖 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <!--            <version>6.0.13.Final</version>-->
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>8.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <!--            <version>3.10</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <!--            <version>4.3</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <!--            <version>1.7.30-kwai-2</version>-->
        </dependency>
        <dependency>
            <groupId>com.ecyrd.speed4j</groupId>
            <artifactId>speed4j</artifactId>
            <!--            <version>0.18</version>-->
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <!--            <version>28.1-jre-kwai5</version>-->
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <!--            <version>1.18.20</version>-->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <!--            <version>2.1.9.RELEASE</version>-->
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <!--            <version>1.3.2</version>-->
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <!--            <version>3.1.0</version>-->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-keycenter-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>