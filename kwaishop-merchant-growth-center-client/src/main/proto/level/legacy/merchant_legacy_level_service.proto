syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;
import "level/level/merchant_level_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchanLegacytLevelServiceProto";


message GetLegacyUserLevelRequest {
  /**
  ID
   */
  uint64 user_id = 1;
}

message LegacyUserLevelDTO {
  /**
  当前等级
   */
  int32 level_id = 1;

  /**
  等级名称
   */
  string level_name = 2;
}

message GetLegacyUserLevelResponse {

  int32 result = 1;

  string error_msg = 2;

  LegacyUserLevelDTO user_level = 3;
}


service MerchantLegacyLevelService {
  /**
  根据商家ID查询商家当前等级
 */
  rpc GetUserLevel (GetLegacyUserLevelRequest) returns (GetLegacyUserLevelResponse);
}