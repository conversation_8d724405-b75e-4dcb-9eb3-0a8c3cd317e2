syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;
import "level/level/merchant_level_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchantLevelTestServiceProto";

message InsertMerchantImportIndicatorRequest {
  uint64 seller_id = 1;
  string content = 2;
  uint64 order_value = 3;
  string dt = 4;
  int32 sync_flag = 5;
}

message InsertMerchantImportIndicatorResponse {
  int32 result = 1;
  string error_msg = 2;
}

message DeleteMerchantImportIndicatorRequest {
  uint64 seller_id = 1;
  string dt = 2;
}

message DeleteMerchantImportIndicatorResponse {
  int32 result = 1;
  string error_msg = 2;
}

message UpdateLevelIndicatorRecordRequest {
  uint64 seller_id = 1;
  string curr_dt = 2;
  string target_dt = 3;
  string content = 4;
  int32 use_flag = 5;
}

message UpdateLevelIndicatorRecordResponse  {
  int32 result = 1;
  string error_msg = 2;
}

message InsertLevelUnionPullNumTargetCacheRequest {
  string dt = 1;
  string data = 2;
}

message InsertLevelUnionPullNumTargetCacheResponse  {
  int32 result = 1;
  string error_msg = 2;
}

message InsertLevelInitUnionPullNumTargetCacheRequest {
  uint64 user_id = 1;
  uint64 data = 2;
}

message InsertLevelInitUnionPullNumTargetCacheResponse  {
  int32 result = 1;
  string error_msg = 2;
}

message InsertLevelIndicatorRecordRequest {
  uint64 user_id = 1;
  string content = 2;
  string dt = 3;
}

message InsertLevelIndicatorRecordResponse  {
  int32 result = 1;
  string error_msg = 2;
}

message DeleteLevelIndicatorRecordRequest {
  uint64 user_id = 1;
  string dt = 2;
}

message DeleteLevelIndicatorRecordResponse  {
  int32 result = 1;
  string error_msg = 2;
}

message UpdateUserLevelRecordRequest {
  uint64 user_id = 1;
  string order_id = 2;
  int32 pre_level = 3;
  string dt = 4;
  int32 check_flag = 5;
}

message UpdateUserLevelRecordResponse  {
  int32 result = 1;
  string error_msg = 2;
}


service MerchantLevelTestService {
  /**
  往中间表插入指标
 */
  rpc InsertMerchantImportIndicator (InsertMerchantImportIndicatorRequest) returns (InsertMerchantImportIndicatorResponse);

  /**
  删除中间表指标
 */
  rpc DeleteMerchantImportIndicator (DeleteMerchantImportIndicatorRequest) returns (DeleteMerchantImportIndicatorResponse);

  /**
   更新level_indicator_record
  */
  rpc UpdateLevelIndicatorRecord (UpdateLevelIndicatorRecordRequest) returns (UpdateLevelIndicatorRecordResponse);

  /**
  插入 等级纬度商家联合拉新40分位数
   */
  rpc InsertLevelUnionPullNumTargetCache (InsertLevelUnionPullNumTargetCacheRequest) returns (InsertLevelUnionPullNumTargetCacheResponse);

  /**
  插入 等级纬度商家联合拉新40分位数
 */
  rpc InsertLevelInitUnionPullNumTargetCache (InsertLevelInitUnionPullNumTargetCacheRequest) returns (InsertLevelInitUnionPullNumTargetCacheResponse);

  /**
  插入 指标数据
  */
  rpc InsertLevelIndicatorRecord (InsertLevelIndicatorRecordRequest) returns (InsertLevelIndicatorRecordResponse);

  /**
  删除 指标数据
  */
  rpc DeleteLevelIndicatorRecord (DeleteLevelIndicatorRecordRequest) returns (DeleteLevelIndicatorRecordResponse);

  /**
  更新 等级变更记录
  */
  rpc UpdateUserLevelRecord (UpdateUserLevelRecordRequest) returns (UpdateUserLevelRecordResponse);
}