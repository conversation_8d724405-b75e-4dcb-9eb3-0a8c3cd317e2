syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;
import "level/level/merchant_level_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchantLevelServiceProto";

message BatchGetCurrentLevelRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  repeated uint64 user_id = 2;
}

message BatchGetCurrentLevelResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息列表
   */
  repeated LevelInfoDTO level_infos = 3;
}

message GetCurrentLevelAndExtraInfoRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
}

message GetCurrentLevelAndExtraInfoResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelInfoDTO level_infos = 3;
  /**
  等级额外信息
  */
  LevelExtraInfoDTO level_extra_infos = 4;
}

message GetHistoryLevelRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID
   */
  uint64 user_id = 2;
  /**
  分页page_no
  */
  int32 page_no = 3;
  /**
  分页page_size
   */
  int32 page_size = 4;
}

message GetHistoryLevelResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  repeated LevelInfoDTO level_infos = 3;
}

message GetLevelIndicatorsRequest {
  /**
   查询场景ID
 */
  string scene_code = 1;
  /**
  商家ID
   */
  uint64 user_id = 2;
  /**
  dt查询条件
   */
  repeated string dt = 3;
}

message GetLevelIndicatorsResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  指标列表
   */
  repeated LevelIndicatorDTO indicators = 3;

  uint64 next_update_time = 4;

  uint64 latest_update_time = 5;
}

message GetLevelIndicatorListRequest {
  /**
   查询场景ID
 */
  string scene_code = 1;
  /**
  商家ID
   */
  uint64 user_id = 2;
  /**
  dt查询条件
   */
  repeated string dt = 3;
}

message GetLevelIndicatorListResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  指标列表
   */
  repeated MerchantLevelIndicatorDTO indicators = 3;
  uint64 next_update_time = 4;

  uint64 latest_update_time = 5;
}


message GetLevelIndicatorConfigRequest {
  /**
   查询场景ID
 */
  string scene_code = 1;
  /**
   商家ID
 */
  uint64 user_id = 2;
}

message GetLevelIndicatorConfigResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  指标状态（是否达标）列表
   */
  LevelIndicatorConfigDTO config = 3;
}

message GetLevelInfoRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
}

message GetLevelInfoResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelShowInfoDTO show_info = 3;
}

message BatchGetLevelInfoRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  repeated uint64 user_id = 2;
}

message BatchGetLevelInfoResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  repeated LevelShowInfoDTO show_info = 3;
}

message GetLevelTaskInfoRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
}

message GetLevelTaskInfoResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  repeated LevelTaskInfoDTO task_info = 3;
}

message GetLevelSnapshotRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
  /**
  时间
   */
  uint64 time_stamp = 3;
}

message GetLevelSnapshotResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelInfoDTO level_infos = 3;
}

message GetLevelRecordListRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
  /**
  更新类型
   */
  int32 updater_type = 3;
  /**
  是否查询最新记录
   */
  bool is_latest = 4;
}

message GetLevelRecordListResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级定级记录信息
   */
  repeated UserLevelRecordSimpleDTO level_record = 3;
}

/**
批量查询商家的等级变更趋向，目前用于等级预警场景使用
 */
message BatchQueryLevelChangeTrendInfoRequest {
  repeated uint64 user_ids = 1;
  string scene_code = 2;
}
// 等级变化的趋势信息
message LevelChangeTrendInfoDTO {
  uint64 user_id = 1;
  // 有降级的可能
  bool level_maybe_degrade = 2;
}
message BatchQueryLevelChangeTrendInfoResponse {
  /*
    result=1 为成功
  */
  int32 result = 1;
  /*
    error_msg
   */
  string error_msg = 2;

  repeated LevelChangeTrendInfoDTO data = 3;

}

// 添加UpdateLevelByIndicatorRecord请求和响应消息定义
message UpdateLevelByIndicatorRecordRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 数据日期，格式为yyyyMMdd
   */
  string dt = 2;
  /**
   * 指标数据内容
   */
  string content = 3;
  /**
   * 是否常规更新
   */
  bool is_routine = 4;
}

message UpdateLevelByIndicatorRecordResponse {
  /**
   * 结果码，1表示成功
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
商家等级查询服务
 */
service MerchantLevelService {
  /**
  根据商家ID查询商家当前等级
   */
  rpc GetCurrentLevel (GetCurrentLevelRequest) returns (GetCurrentLevelResponse);

  /**
  根据商家ID查询商家当前等级-3.0
   */
  rpc GetLevel (GetCurrentLevelRequest) returns (GetCurrentLevelResponse);

  /**
  批量根据商家ID查询商家当前等级
   */
  rpc BatchGetCurrentLevel (BatchGetCurrentLevelRequest) returns (BatchGetCurrentLevelResponse);

  /**
  批量根据商家ID查询商家当前等级-3.0
   */
  rpc BatchGetLevel (BatchGetCurrentLevelRequest) returns (BatchGetCurrentLevelResponse);

  /**
  根据商家ID查询商家当前等级(包含风控/干预等信息)
 */
  rpc GetCurrentLevelAndExtraInfo (GetCurrentLevelAndExtraInfoRequest) returns (GetCurrentLevelAndExtraInfoResponse);

  /**
  根据商家ID查询商家当前等级(包含风控/干预等信息)-3.0
 */
  rpc GetLevelAndExtraInfo (GetCurrentLevelAndExtraInfoRequest) returns (GetCurrentLevelAndExtraInfoResponse);

  /**
  查询历史等级
   */
  rpc GetHistoryLevel(GetHistoryLevelRequest) returns (GetHistoryLevelResponse);
  /**
  查询指标信息
   */
  rpc GetLevelIndicators(GetLevelIndicatorsRequest) returns (GetLevelIndicatorsResponse);

  /**
  查询指标信息-3.0
   */
  rpc GetLevelIndicatorList(GetLevelIndicatorListRequest) returns (GetLevelIndicatorListResponse);

  /**
  查询等级对应的指标是信息（根据区间判断是否达标）
  */
  rpc GetLevelIndicatorConfig(GetLevelIndicatorConfigRequest) returns (GetLevelIndicatorConfigResponse);

  /**
  查询等级对应的指标是信息（根据区间判断是否达标）-3.0
  */
  rpc GetLevelIndicatorConfigInfo(GetLevelIndicatorConfigRequest) returns (GetLevelIndicatorConfigResponse);

  /**
  根据商家ID查询商家当前等级信息（包括展示信息）
  */
  rpc GetLevelInfo (GetLevelInfoRequest) returns (GetLevelInfoResponse);

  /**
  根据商家ID查询商家当前等级信息（包括展示信息）-3.0
  */
  rpc GetLevelShowInfo (GetLevelInfoRequest) returns (GetLevelInfoResponse);

  /**
  根据商家ID查询商家当前等级信息（包括展示信息）
  */
  rpc BatchGetLevelInfo (BatchGetLevelInfoRequest) returns (BatchGetLevelInfoResponse);

  /**
  根据商家ID查询商家当前等级信息（包括展示信息）-3.0
  */
  rpc BatchGetLevelShowInfo (BatchGetLevelInfoRequest) returns (BatchGetLevelInfoResponse);

  /**
  查询等级任务当前目标和进度
   */
  rpc GetLevelTaskInfo (GetLevelTaskInfoRequest) returns (GetLevelTaskInfoResponse);
  /**
  查询等级快照
   */
  rpc GetLevelSnapshot (GetLevelSnapshotRequest) returns (GetLevelSnapshotResponse);

  /**
  查询等级快照-3.0
   */
  rpc GetLevelSnapshotInfo (GetLevelSnapshotRequest) returns (GetLevelSnapshotResponse);

  /**
  查询商家定级记录-3.0
   */
  rpc GetLevelRecordList (GetLevelRecordListRequest) returns (GetLevelRecordListResponse);
  /**
  批量查询等级变化的趋势信息
 */
  rpc BatchQueryLevelChangeTrendInfo (BatchQueryLevelChangeTrendInfoRequest)
      returns (BatchQueryLevelChangeTrendInfoResponse);

  /**
  根据商家ID查询商家当前等级信息
 */
  rpc GetMerchantLevelShowInfo (GetLevelInfoRequest) returns (GetLevelInfoResponse);
  /**
  根据商家ID查询商家当前等级信息（包括展示信息）
  */
  rpc BatchGetMerchantLevelShowInfo (BatchGetLevelInfoRequest) returns (BatchGetLevelInfoResponse);
  /**
   * 根据指标数据更新商家等级
   * 内部调用MerchantLevelUpdateServiceImpl.updateLevelByIndicatorRecord方法
   */
  rpc UpdateLevelByIndicatorRecord (UpdateLevelByIndicatorRecordRequest) returns (UpdateLevelByIndicatorRecordResponse);
}