syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;
import "level/level/merchant_level_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchantLevelApiServiceProto";

message GetCurrentLevelWithPrimaryRequest {

  /**
  账号信息
   */
  UserInfoDTO user_info = 1;

  /**
  查询场景ID
   */
  string scene_code = 2;
}

message GetCurrentLevelWithPrimaryResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelInfoDTO level_infos = 3;
}

service MerchantLevelApiService {
  /**
  根据商家ID查询商家当前等级
 */
  rpc GetCurrentLevel (GetCurrentLevelRequest) returns (GetCurrentLevelResponse);

  /**
  根据商家ID查询商家当前等级-兼容主子账号
 */
  rpc GetCurrentLevelWithPrimary (GetCurrentLevelWithPrimaryRequest) returns (GetCurrentLevelWithPrimaryResponse);

  /**
  根据商家ID查询商家当前等级-兼容主子账号-3.0
 */
  rpc GetLevelWithPrimary (GetCurrentLevelWithPrimaryRequest) returns (GetCurrentLevelWithPrimaryResponse);
}