syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;
import "level/level/merchant_level_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchantLevelAdminServiceProto";

message LevelInterveneRequest {
  /**
  商家ID
   */
  uint64 user_id = 1;
  /**
  当前等级
   */
  int32 current_level = 2;
  /**
  目标等级
  */
  int32 target_level = 3;
  /**
  operator
  */
  string operator = 4;
  /**
  操作描述
   */
  string description = 5;
  /**
  开始时间
   */
  int32 start_time = 6;
  /**
  结束时间
   */
  int32 end_time = 7;
  /**
  是否立即生效
   */
  bool immediate_effective = 8;
}

message LevelInterveneResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
}

message ListLevelInterveneRecordRequest {
  /**
  商家ID
   */
  uint64 user_id = 1;
  /**
  operator
  */
  string operator = 2;
  /**
  status
   */
  uint32 status = 3;
  /**
  分页page_no
   */
  int32 page_no = 4;
  /**
  分页page_size
   */
  int32 page_size = 5;
}

message ListLevelInterveneRecordResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  干预记录
   */
  repeated LevelInterveneRecordDTO data = 3;
}

message GetLevelCombineRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
  /**
  操作人
   */
  string operator = 3;
}

message GetLevelCombineResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelCombineDTO data = 3;
}
// 等级3.0新需求
message GetLevelCombineResponseV2{
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelCombineDTOV2 data = 3;
}

service MerchantLevelAdminService {
  /**
  根据商家ID查询商家当前等级聚合信息
  */
  rpc GetLevelCombineInfo(GetLevelCombineRequest) returns (GetLevelCombineResponse);
  /**
  等级干预
 */
  rpc LevelIntervene(LevelInterveneRequest) returns (LevelInterveneResponse);
  /**
    查询等级干预记录
    */
  rpc ListLevelInterveneRecord(ListLevelInterveneRecordRequest) returns (ListLevelInterveneRecordResponse);
  /**
  根据商家ID查询商家当前等级3.0聚合信息
   */
  rpc GetLevelCombineInfoV2(GetLevelCombineRequest) returns (GetLevelCombineResponseV2);
  /**
    等级干预3.0
    */
  rpc LevelInterveneV2(LevelInterveneRequest) returns (LevelInterveneResponse);

  /**
  查询等级干预记录
   */
  rpc ListLevelInterveneRecordV2(ListLevelInterveneRecordRequest) returns
      (ListLevelInterveneRecordResponse);
}