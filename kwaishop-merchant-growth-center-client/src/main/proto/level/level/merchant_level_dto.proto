syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.level;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.level";
option java_outer_classname = "MerchantLevelInfoDTOProto";

message LevelInfoDTO {
  /**
  ID
   */
  uint64 user_id = 1;
  /**
  商家当前等级
   */
  uint32 level = 2;
  /**
  等级名称
   */
  string name = 3;
  /**
  商家当前大等级
   */
  uint32 big_level = 4;
  /**
  大等级名称
   */
  string big_level_name = 5;
}

message LevelExtraInfoDTO {
  /**
  是否被风控 false-未被风控 1-被风控
   */
  bool risk = 4;
  /**
  变更类型 10-等级每月固定更新 20-运营等级干预 30-系统干预
   */
  int32 type = 5;
}

message LevelExtraInfoDTOV2{
  /**
  是否被风控 false-未被风控 1-被风控
  */
  bool risk = 4;
  /**
  变更类型 10-等级每月固定更新 20-运营等级干预 30-系统干预
   */
  int32 type = 5;
  /**
   上期等级
   */
  int32 pre_level = 6;
  /**
  上期等级名称
   */
  string pre_level_name = 7;
  /**
   上期大等级
   */
  int32 pre_big_level = 8;
  /**
  上期大等级名称
   */
  string pre_big_level_name = 9;
}

message LevelInterveneRecordDTO {
  /**
  商家ID
   */
  uint64 user_id = 1;
  /**
  当前等级
   */
  int32 current_level = 2;
  /**
  目标等级
  */
  int32 target_level = 3;
  /**
  operator
  */
  string operator = 4;
  /**
  操作描述
   */
  string description = 5;
  /**
  开始时间
   */
  int32 start_time = 6;
  /**
  结束时间
   */
  int32 end_time = 7;
  /**
  状态
   */
  int32 status = 8;
  /**
   当前大等级等级
 */
  string current_big_level_name = 9;
  /**
  目标等级
  */
  string target_big_level_name = 10;
}

/**
等级指标信息
 */
message LevelIndicatorDTO {
  /**
  dt
   */
  string dt = 1;
  /**
  运费险覆盖率
   */
  uint64 freight_insurance = 2;
  /**
  小店星级
 */
  uint64 shop_star = 3;
  /**
  近30天GMV
  */
  uint64 gmv30 = 4;
  /**
  等级分
   */
  uint64 level_score = 5;
  /**
  风控标记
   */
  bool risk = 6;
}
/**
等级3.0指标信息
 */
message LevelIndicatorDTOV2 {
  /**
   * T-36~T-7当前商家是否被风控(1-被风控，0-未被风控)
   */
  bool risk = 1;
  /**
   * 每个自然月小店星级大于等于3.5天数
   */
  uint64 conform_shop_star_day_cnt = 2;
  /**
   * 每个自然月风控后支付订单金额（分）
   */
  uint64 unrisk_pay_order_amt_30d = 3;
  /**
   * 彩虹分
   */
  uint64 rainbow_score = 4;
  /**
   * 店铺入驻时间
   */
  uint64 shop_enter_time = 5;
  /**
   * T-36～T-7风控后支付订单金额（分）
   */
  uint64 unrisk_pay_order_amt = 6;
  /**
   * 商家等级分
   */
  uint64 level_score = 7;
  /**
   * 自然月商家是否被风控（1-被风控，0-未被风控）
   */
  bool risk_month = 8;
  /**
   * 小店星级
   */
  uint64 shop_star = 9;
  /**
  购物体验分
   */
  uint64 exp_score = 10;
  /**
 * 每个自然月购物体验分>=4.1分的天数
 */
  uint64 conform_exp_score_day_cnt = 11;
  /**
  原始的指标数据json
   */
  string original_indicator_json = 12;
}

/**
等级指标信息
 */
message MerchantLevelIndicatorDTO {
  /**
  dt
   */
  string dt = 1;

  /**
  指标详情，json字符串
   */
  string indicator_info = 2;
}

message LevelIndicatorConfigItemDTO {
  /**
  等级
   */
  int32 level = 1;
  /**
  名称
   */
  string name = 2;
  /**
  运费险覆盖率区间
   */
  uint64 start_freight_insurance = 3;
  /**
  运费险覆盖率区间
   */
  uint64 end_freight_insurance = 4;
  /**
  小店星级区间
   */
  uint64 start_shop_star = 5;
  /**
  小店星级区间
   */
  uint64 end_shop_star = 6;
  /**
  等级分区间
   */
  uint64 start_level_score = 7;
  /**
  等级分区间
  */
  uint64 end_level_score = 8;
  /**
  近30天GMV 区间
  */
  uint64 start_gmv30 = 9;
  /**
  近30天GMV 区间
  */
  uint64 end_gmv30 = 10;
}

/**
当前等级指标是否达标
 */
message LevelIndicatorConfigDTO {
  /**
  指标配置区间
   */
  repeated LevelIndicatorConfigItemDTO item = 1;
}

/**
等级记录DO对应的DTO，用于MQ message
 */
message UserLevelRecordDTO {
  uint64 id = 1;
  /**
   * 创建时间
   */
  uint64 create_time = 2;
  /**
   * 最后更新时间
   */
  uint64 update_time = 3;
  /**
   * 是否被删除，0-未删除，1-已删除
   */
  int32 deleted = 4;
  /**
 * 用户ID
 */
  uint64 user_id = 5;
  /**
   * 先前等级
   */
  int32 pre_level = 6;
  /**
   * 当前等级
   */
  int32 current_level = 7;
  /**
  风控
   */
  bool risk = 8;
  /**
   * 更新类型
   */
  int32 type = 9;
  /**
   * dt
   */
  string dt = 10;
  /**
   * 扩展字段
   */
  string ext = 11;
  /**
   * version
   */
  int64 version = 12;
  /**
幂等order_id
 */
  string order_id = 13;

  /**
  周期，格式：202201
   */
  string period = 14;

  /**
  是否是例行定级(每月月初任务定级)
   */
  bool is_routine = 15;

  /**
   * 风控前可达到的等级
   */
  int32 before_risk_level = 16;
}

/**
UserLevelIndicatorRecordDO对应的DTO，用于mq message
 */
message UserLevelIndicatorRecordDTO {
  uint64 id = 1;
  /**
   * 创建时间
   */
  uint64 create_time = 2;
  /**
   * 最后更新时间
   */
  uint64 update_time = 3;
  /**
   * 是否被删除，0-未删除，1-已删除
   */
  int32 deleted = 4;
  /**
 * 用户ID
 */
  uint64 user_id = 5;
  /**
   * 指标
   */
  string content = 6;
  /**
   * dt
   */
  string dt = 7;
  /**
   * 是否被用于等级更新 0-未被使用 1-被使用
   */
  uint32 use_flag = 8;
  /**
   * version
   */
  int64 version = 9;
}

/**
等级展示信息
 */
message LevelShowInfoDTO {
  /**
  ID
   */
  uint64 user_id = 1;
  /**
  商家当前等级
   */
  uint32 level = 2;
  /**
  名称
   */
  string name = 3;
  /**
  icon
   */
  string icon = 4;
  /**
  跳转链接
   */
  string jump_url = 5;
  /**
  等级称号，比如 卖货达人
   */
  string title = 6;
  /**
  等级称号 icon
   */
  string title_icon = 7;
  /**
  商家当前大等级
  */
  uint32 big_level = 8;
  /**
  大等级名称
   */
  string big_level_name = 9;
}

/**
  等级任务当前目标和进度
 */
message LevelTaskInfoDTO {
  /**
  任务类型
   */
  int32 type = 1;
  /**
  任务目标
   */
  string target = 2;
  /**
  任务进度
   */
  string process = 3;
  /**
  状态
   */
  int32 status = 4;
}

/**
等级聚合信息
 */
message LevelCombineDTO {
  /**
  等级信息
 */
  LevelInfoDTO level_infos = 1;
  /**
  等级指标信息(定级时的指标快照)
   */
  LevelIndicatorDTO indicator = 2;
  /**
  等级额外信息
   */
  LevelExtraInfoDTO ext = 3;
  /**
  当前指标信息
   */
  LevelIndicatorDTO current_indicator = 4;
}

/**
等级指标详细信息
 */
message LevelIndicatorDataDTO {
  /**
  指标编码
   */
  string code = 1;
  /**
  指标名称
   */
  string name = 2;
  /**
  指标描述
   */
  string desc = 3;
  /**
  指标的数值
   */
  map<string, string> values = 4;
}

/**
等级3.0聚合信息
 */
message LevelCombineDTOV2 {
  /**
  等级信息
 */
  LevelInfoDTO level_infos = 1;
  /**
  等级指标信息(定级时的指标快照)
   */
  LevelIndicatorDTOV2 indicator = 2;
  /**
  等级额外信息
   */
  LevelExtraInfoDTOV2 ext = 3;
  /**
  当前指标信息
   */
  LevelIndicatorDTOV2 current_indicator = 4;
  /**
  等级的指标数据
   */
  repeated LevelIndicatorDataDTO indicator_data_list = 5;
}

/**
UserLevelIndicatorRecordDO对应的DTO List，用于mq message
 */
message UserLevelIndicatorRecordListDTO {
  repeated UserLevelIndicatorRecordDTO record = 1;
}

message GetCurrentLevelRequest {
  /**
  查询场景ID
   */
  string scene_code = 1;
  /**
  商家ID列表
   */
  uint64 user_id = 2;
}

message GetCurrentLevelResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  等级信息
   */
  LevelInfoDTO level_infos = 3;
}

message UserInfoDTO {

  /**
  当前访问账号id
   */
  uint64 user_id = 1;

  /**
  主账号用户ID
   */
  uint64 primary_user_id = 2;
}

message UserLevelRecordSimpleDTO {

  /**
   * 创建时间
   */
  uint64 create_time = 1;

  /**
  上期等级
   */
  int32 pre_level = 2;
  /**
   * 当前等级
   */
  int32 current_level = 3;
  /**
  风控
   */
  bool risk = 4;
  /**
   * 更新类型
   */
  int32 type = 5;
  /**
   * dt
   */
  string dt = 6;
  /**
   * 扩展字段
   */
  string ext = 7;
  /**
  周期，格式：202201
   */
  string period = 8;
  /**
  是否是例行定级(每月月初任务定级)
   */
  bool is_routine = 9;
}