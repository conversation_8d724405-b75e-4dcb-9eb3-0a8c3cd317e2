syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.interest;
import "level/interest/merchant_level_interest_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.interest";
option java_outer_classname = "MerchantLevelInterestServiceProto";


message GetUserInterestDetailRequest{

  /**
  用户ID
   */
  uint64 user_id = 1;
}

message GetUserInterestDetailResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  权益墙详情
   */
  repeated UserInterestDetailListDTO detail_info = 3;
}

/**
商家等级权益服务
 */
service MerchantLevelInterestService {

  /**
  查询权益墙的数据
  */
  rpc GetUserInterestDetail(GetUserInterestDetailRequest) returns (GetUserInterestDetailResponse);

  /**
  查询权益墙的数据-3.0
  */
  rpc GetUserInterestWallInfo(GetUserInterestDetailRequest) returns (GetUserInterestDetailResponse);
}