syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.interest;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.interest";
option java_outer_classname = "MerchantLevelInterestAdminDTOProto";

/**
等级权益配置DTO
 */
message LevelInterestConfigDTO{

  /**
  ID
   */
  uint64 id = 1;

  /**
  创建时间
   */
  uint64 create_time = 2;

  /**
  更新时间
   */
  uint64 update_time = 3;

  /**
  等级
   */
  uint32 level = 4;

  /**
  权益配置ID
   */
  uint64 interest_config_id = 5;

  /**
  权益ID
   */
  uint64 interest_id = 6;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 7;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 8;

  /**
  状态，1-有效，0-失效
   */
  int32 status = 9;

  /**
  权益展示信息模版ID，前台权益展示用
   */
  string show_template_key = 10;

  /**
  权益展示优先级，前台权益展示用
   */
  int32 show_priority = 11;

  /**
  操作人
   */
  string operator_id = 12;

  /**
  版本号
   */
  uint32 version = 13;

  /**
  扩展信息
   */
  string ext = 14;

  /**
  是否主动发放
   */
  int32 active_send = 15;
}

/**
用户权益黑名单配置DTO
 */
message UserInterestBlackListDTO{

  /**
  ID
   */
  uint64 id = 1;

  /**
  创建时间
   */
  uint64 create_time = 2;

  /**
  更新时间
   */
  uint64 update_time = 3;

  /**
  用户ID
   */
  uint64 user_id = 4;

  /**
  权益ID
   */
  uint64 interest_id = 5;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 6;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 7;

  /**
  状态，1-有效，0-失效
   */
  int32 status = 8;

  /**
  操作人
   */
  string operator_id = 9;

  /**
  版本号
   */
  uint32 version = 10;

  /**
  扩展信息
   */
  string ext = 11;
}

/**
用户等级权益发放/回收记录DTO
 */
message UserLevelInterestRecordDTO{

  /**
  ID
   */
  uint64 id = 1;

  /**
  创建时间
   */
  uint64 create_time = 2;

  /**
  更新时间
   */
  uint64 update_time = 3;

  /**
  用户ID
   */
  uint64 user_id = 4;

  /**
  等级
   */
  uint32 level = 5;

  /**
  权益配置ID
   */
  uint64 interest_config_id = 6;

  /**
  权益ID
   */
  uint64 interest_id = 7;

  /**
  等级变更幂等键
   */
  string unique_id = 8;

  /**
  动作类型：发放/回收
   */
  uint32 action = 9;

  /**
  处理状态
   */
  int32 status = 10;

  /**
  重试次数
   */
  uint32 try_num = 11;

  /**
  失败类型
   */
  int32 failed_type = 12;

  /**
  处理时间
   */
  uint64 process_time = 13;

  /**
  版本号
   */
  uint32 version = 14;

  /**
  操作者ID
   */
  string operator_id = 15;

  /**
  扩展信息
   */
  string ext = 16;
}