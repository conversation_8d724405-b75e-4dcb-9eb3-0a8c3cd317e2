syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.interest;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.interest";
option java_outer_classname = "MerchantLevelInterestDTOProto";

/**
用户权益状态信息-for权益墙
 */
message UserInterestDetailItem{

  /**
  权益ID
   */
  uint64 interest_id = 1;

  /**
  解锁最低等级
   */
  uint32 unlock_min_level = 2;

  /**
  解锁状态，-1未解锁，1-解锁
   */
  int32 status = 3;
}

/**
用户权益状态信息列表-for权益墙
 */
message UserInterestDetailListDTO{

  /**
  等级
   */
  int32 level = 1;

  /**
  权益详情列表
   */
  repeated UserInterestDetailItem detail = 2;
}