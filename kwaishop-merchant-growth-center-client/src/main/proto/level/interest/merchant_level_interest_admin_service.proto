syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.level.interest;
import "level/interest/merchant_level_interest_admin_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.level.interest";
option java_outer_classname = "MerchantLevelInterestAdminServiceProto";

message AddInterestToLevelRequest{

  /**
  权益配置ID
   */
  uint64 interest_config_id = 1;

  /**
  等级
   */
  uint32 level = 2;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 3;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 4;

  /**
  是否有效
   */
  int32 status = 5;

  /**
  权益展示信息模版ID，前台权益展示用
   */
  string show_template_key = 6;

  /**
  权益展示优先级，前台权益展示用
   */
  int32 show_priority = 7;

  /**
  操作者工号
   */
  string operator_id = 8;

  /**
  附加参数，json字符串
   */
  string ext = 9;

  /**
  是否主动发放
   */
  int32 active_send = 10;
}

message AddInterestToLevelResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  配置详情
   */
  uint64 record_id = 3;
}

message AddInterestFromMinLevelRequest{

  /**
  权益配置ID
   */
  uint64 interest_config_id = 1;

  /**
  最低等级
   */
  uint32 min_level = 2;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 3;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 4;

  /**
  记录状态
   */
  int32 status = 5;

  /**
  权益展示信息模版ID，前台权益展示用
   */
  string show_template_key = 6;


  /**
  权益展示优先级，前台权益展示用
   */
  int32 show_priority = 7;

  /**
  操作者工号
   */
  string operator_id = 8;

  /**
  附加参数，json字符串
   */
  string ext = 9;

  /**
  是否主动发放
   */
  int32 active_send = 10;
}

message AddInterestFromMinLevelResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message InvalidLevelInterestConfigRequest{

  /**
  权益配置ID
   */
  uint64 interest_config_id = 1;

  /**
  等级
   */
  uint32 level = 2;

  /**
  操作者工号
   */
  string operator_id = 3;
}

message InvalidLevelInterestConfigResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message OfflineInterestRequest{

  /**
  权益配置ID
   */
  uint64 interest_config_id = 1;

  /**
  操作者工号
   */
  string operator_id = 2;
}

message OfflineInterestResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message BatchDeleteLevelInterestConfigRequest{

  /**
  等级权益配置ID列表
   */
  repeated uint64 level_interest_config_id = 1;

  /**
  操作者工号
   */
  string operator_id = 2;
}

message BatchDeleteLevelInterestConfigResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message UpdateLevelInterestConfigRequest{

  /**
  记录ID
   */
  uint64 id = 1;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 2;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 3;

  /**
  权益展示信息模版ID，前台权益展示用
   */
  string show_template_key = 4;

  /**
  权益展示优先级，前台权益展示用
   */
  int32 show_priority = 5;

  /**
  操作者工号
   */
  string operator_id = 6;

  /**
  扩展数据
   */
  string ext = 7;

  /**
  是否主动发放
   */
  int32 active_send = 8;
}

message UpdateLevelInterestConfigResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  更新后的id
   */
  uint64 record_id = 3;
}

message GetLevelInterestConfigListRequest{

  /**
  记录ID
   */
  uint32 level = 1;

  /**
  指定时间
   */
  uint64 point_time = 2;
}

message GetLevelInterestConfigListResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  更新后的详情
   */
  repeated LevelInterestConfigDTO config_detail = 3;
}

message AddBlackListRequest{

  /**
  权益ID
   */
  uint64 interest_id = 1;

  /**
  用户ID
   */
  uint64 user_id = 2;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 3;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 4;

  /**
  是否有效
   */
  int32 status = 5;

  /**
  操作者工号
   */
  string operator_id = 6;

  /**
  附加参数，json字符串
   */
  string ext = 7;
}

message AddBlackListResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message UpdateUserInterestBlackListRequest{

  /**
  记录ID
   */
  uint64 id = 1;

  /**
  生效开始时间，0-表示无开始时间限制
   */
  uint64 start_time = 2;

  /**
  生效结束时间，0-表示无结束时间限制
   */
  uint64 end_time = 3;

  /**
  操作者工号
   */
  string operator_id = 4;

  /**
  扩展数据
   */
  string ext = 5;
}

message UpdateUserInterestBlackListResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  更新后的详情
   */
  uint64 record_id = 3;
}

message InvalidUserInterestBlackListRequest{

  /**
  黑名单记录ID
   */
  uint64 black_list_id = 1;

  /**
  操作者工号
   */
  string operator_id = 3;
}

message InvalidUserInterestBlackListResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

message GetUserValidInterestBlackListRequest{

  /**
  用户ID
   */
  uint64 user_id = 1;

  /**
  指定时间
   */
  uint64 point_time = 2;
}

message GetUserValidInterestBlackListResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  详情
   */
  repeated UserInterestBlackListDTO black_list = 3;
}

message QueryUserLevelInterestRecordRequest{

  /**
  用户ID
   */
  uint64 user_id = 1;

  /**
  等级
   */
  uint32 level = 2;

  /**
  权益配置ID
   */
  uint64 interest_config_id = 3;

  /**
  权益ID
   */
  uint64 interest_id = 4;

  /**
  等级变更流水幂等键
   */
  string unique_id = 5;

  /**
  动作类型，1-发放，2-回收
   */
  int32 action = 6;

  /**
  处理状态
   */
  int32 status = 7;

  /**
  失败类型
   */
  int32 failed_type = 8;

  /**
  记录ID
   */
  uint64 id = 9;
}

message QueryUserLevelInterestRecordResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  记录详情
   */
  repeated UserLevelInterestRecordDTO record_list = 3;
}

message RecoveryAndSendInterestForLevelChangeRequest{

  /**
  用户ID
   */
  uint64 user_id = 1;

  /**
  处理时间
   */
  uint64 process_time = 2;

  /**
  变更前等级
   */
  uint32 from_level = 3;

  /**
  变更后等级
   */
  uint32 to_level = 4;

  /**
  等级变更流水幂等键
   */
  string unique_id = 5;

  /**
  是否是风控校验失败
   */
  bool is_risk_failed = 6;

  /**
  操作者ID
   */
  string operator_id = 7;

  /**
  等级变更记录ID
   */
  uint64 id = 8;
}

message RecoveryAndSendInterestForLevelChangeResponse{

  /**
   *返回结果码
   */
  int32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;
}

/**
商家等级权益配置服务
 */
service MerchantLevelInterestAdminService {

  /**
  给等级增加权益配置
   */
  rpc AddInterestToLevel (AddInterestToLevelRequest) returns (AddInterestToLevelResponse);
  /**
  从某一最小等级开始的所有等级加上权益
   */
  rpc AddInterestFromMinLevel (AddInterestFromMinLevelRequest) returns (AddInterestFromMinLevelResponse);
  /**
  失效某一等级的某一权益配置
   */
  rpc InvalidLevelInterestConfig(InvalidLevelInterestConfigRequest) returns (InvalidLevelInterestConfigResponse);
  /**
  下线权益
   */
  rpc OfflineInterest(OfflineInterestRequest) returns (OfflineInterestResponse);
  /**
  批量删除等级权益配置
   */
  rpc BatchDeleteLevelInterestConfig(BatchDeleteLevelInterestConfigRequest) returns (BatchDeleteLevelInterestConfigResponse);
  /**
  更新某一等级权益配置
   */
  rpc UpdateLevelInterestConfig(UpdateLevelInterestConfigRequest) returns (UpdateLevelInterestConfigResponse);
  /**
  查询某一等级权益配置
   */
  rpc GetLevelInterestConfigList(GetLevelInterestConfigListRequest) returns (GetLevelInterestConfigListResponse);

  /**
  新增权益黑名单
   */
  rpc AddBlackList(AddBlackListRequest) returns (AddBlackListResponse);

  /**
  更新某一黑名单
   */
  rpc UpdateUserInterestBlackList(UpdateUserInterestBlackListRequest) returns (UpdateUserInterestBlackListResponse);

  /**
  失效某一黑名单
   */
  rpc InvalidUserInterestBlackList(InvalidUserInterestBlackListRequest) returns (InvalidUserInterestBlackListResponse);

  /**
  查询某一用户的权益黑名单列表
   */
  rpc GetUserValidInterestBlackList(GetUserValidInterestBlackListRequest) returns (GetUserValidInterestBlackListResponse);

  /**
  查询用户的发放/回收记录
   */
  rpc QueryUserLevelInterestRecord(QueryUserLevelInterestRecordRequest) returns (QueryUserLevelInterestRecordResponse);

  /**
  手动触发一次权益变更
   */
  rpc RecoveryAndSendInterestForLevelChange(RecoveryAndSendInterestForLevelChangeRequest) returns (RecoveryAndSendInterestForLevelChangeResponse);
}