syntax = "proto3";

import "activity/statistics/activity_statistics_dto.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.statistics;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics";
option java_outer_classname = "ActivityStatisticsServiceProto";


message GetStatisticsInfoByEventTypeRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 子活动order
   */
  uint32 sub_activity_order = 2;
  /**
   * 分层order
   */
  uint32 layer_order = 3;
  /**
   * 周期order
   */
  uint32 period_order = 4;
  /**
   * 实体类型
   */
  uint32 event_type = 5;
}

message AggActivityStatisticsInfoRequest {
  /**
 * 活动ID
 */
  uint64 activity_id = 1;

  string operator = 2;
}

message GetStatisticsInfoByEventTypeResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 各实体维度统计信息
   */
  repeated StatisticsDTO data = 3;
}

message GetActivityStatisticsInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 活动统计信息
   */
   ActivityStatisticsDTO data = 3;
}

message StatisticsDTO {
  /**
   * 实体类型
   */
  uint32 entity_type = 1;
  /**
   * 实体ID
   */
  uint64 entity_id = 2;
  /**
   * 活动ID
   */
  uint64 activity_id = 3;
  /**
   * 实际总奖励
   */
  uint64 total_award = 4;
  /**
   * 实际ROI
   */
  string roi = 5;
  /**
   * 预测总奖励
   */
  uint64 forecast_total_award = 6;
  /**
   * 预测ROI
   */
  string forecast_roi = 7;
  /**
   * 预测数据
   */
  string forecast_data = 8;
  /**
   * 期望总奖励
   */
  uint64 expect_total_award = 9;
  /**
   * 期望数据
   */
  string expect_data = 10;
  /**
   * 实际数据
   */
  string actual_data = 11;
  /**
   * 状态
   */
  uint32 status = 12;
  /**
   * 数据版本
   */
  string data_version = 13;
  /**
   * 统计信息
   */
  string aggregation_info = 14;
  /**
   * 指标配置
   */
  repeated uint64 indicator_id = 15;
  /**
   * 奖励配置
   */
  repeated uint32 award_type = 16;
}

service ActivityStatisticsDomainService {
  /**
   * 根据实体类型查询统计信息
   */
  rpc GetStatisticsInfoByEventType(GetStatisticsInfoByEventTypeRequest) returns (GetStatisticsInfoByEventTypeResponse);

  /**
   * 查询活动统计信息
   */
  rpc AggActivityStatisticsInfo(AggActivityStatisticsInfoRequest) returns (GetActivityStatisticsInfoResponse);
}