syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.statistics;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics";
option java_outer_classname = "ActivityStatisticsAdminServiceProto";

message GetActivityDimensionOptionRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message GetActivityDimensionOptionResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message QueryActivityUserReviewDetailRequest {
  /**
   * 操作人
   */
  string operator = 1;

  /**
    * 请求参数
    */
  string query_param = 2;
}

message QueryActivityUserReviewDetailResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message ExportActivityReviewDataRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 导出场景
   */
  string scene = 2;
  /**
   * 活动
   */
  uint64 activity_id = 3;
  /**
   * 子活动序号
   */
  uint32 sub_activity_order = 4;
  /**
   * 分层序号
   */
  uint32 layer = 5;
  /**
   * 选择表头列表
   */
  repeated string table_head = 6;

  // 2:已报名 1:未报名 0:全部
  int32 draw_type = 7;

  int64 user_id = 8;

  repeated string first_industry_code = 9;

  repeated string second_industry_code = 10;

  repeated string third_industry_code = 11;

  string belong_staff = 12;

  string export_source = 13;

  string site_code = 14;
}

message ExportActivityReviewDataResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryExportSelectHeaderInfoRequest {
  // 操作人
  string operator = 1;
  // 场景
  string scene = 2;
}

message QueryExportSelectHeaderInfoResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 数据
  string data = 3;
}

service ActivityStatisticsAdminService {
  /**
   * 查询活动下各维度选项信息
   */
  rpc GetActivityDimensionOption(GetActivityDimensionOptionRequest) returns (GetActivityDimensionOptionResponse);
  /**
   * 获取活动商达明细数据
   */
  rpc QueryActivityUserReviewDetail(QueryActivityUserReviewDetailRequest) returns (QueryActivityUserReviewDetailResponse);
  /**
   * 导出复盘数据
   */
  rpc ExportActivityReviewData(ExportActivityReviewDataRequest) returns (ExportActivityReviewDataResponse);
  /**
   * 查询导入可选表头
   */
  rpc QueryExportSelectHeaderInfo(QueryExportSelectHeaderInfoRequest) returns(QueryExportSelectHeaderInfoResponse);
}