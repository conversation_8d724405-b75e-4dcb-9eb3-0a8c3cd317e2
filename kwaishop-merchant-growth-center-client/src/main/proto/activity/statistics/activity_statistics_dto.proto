syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.statistics;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics";
option java_outer_classname = "ActivityStatisticsDTOProto";

message ActivityStatisticsFinishMsg {

  int64 activity_id = 1;

  int64 event_time = 2;
}


message ActivityStatisticsDTO {


  ActivityBaseDTO activity_info = 1;
  /**
  * 活动统计数据
  */
  repeated StatisticsIndexDataDTO activity_review_idx = 2;

  /**
   * 子活动统计数据
   */
  repeated SubActivityStatisticsDTO sub_activity_data = 3;

}


message SubActivityStatisticsDTO {

  string name = 1;

  /**
   * 任务统计数据
   */
  repeated ActivityLayerStatisticsDTO task_data = 2;

}

message ActivityLayerStatisticsDTO {

  string name = 1;

  int32 layer = 2;

  StrategyConfig strategy_config = 3;

  repeated StatisticsIndexDataDTO achieve_data = 4;

  repeated ActivityStatisticsIndicatorDTO review_indicator = 5;
}

message ActivityStatisticsIndicatorDTO {

  string indicator_name = 1;

  string indicator_value = 2;

  string indicator_desc = 3;

}

message StrategyConfig {


  int64 strategy_id = 1;

  string strategy_version = 2;

  int64 parent_strategy_id = 3;

  int32 strategy_type = 4;
}


message ActivityBaseDTO {
  string activity_id = 1;
  string activity_name = 2;
  string creator = 3;
  int64 start_time = 4;
  int64 end_time = 5;
}

message StatisticsIndexDataDTO {

  string label = 1;

  string key = 2;

  string value = 3;


}