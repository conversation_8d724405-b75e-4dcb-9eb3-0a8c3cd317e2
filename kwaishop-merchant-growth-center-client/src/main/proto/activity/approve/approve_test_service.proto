syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.approve;
import "activity/approve/approve_admin_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.approve";
option java_outer_classname = "ApproveTestServiceProto";

message UpdateApproveRequest {
  /**
  id
   */
  int64 id = 1;
  /**
  状态
   */
  int32 status = 2;
  /**
  实体状态
   */
  int32 entity_status = 3;
  /**
  逻辑删除
   */
  int32 deleted = 4;
  /**
  flowId
   */
  string flow_id = 5;
  /**
  version
   */
  int64 version = 6;
  /**
 * 操作人邮箱前缀
 */
  string modifier = 7;
}

message UpdateApproveResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
审批域测试服务
 */
service ApproveTestService {
  /**
  更新审批单,测试用
   */
  rpc UpdateApprove (UpdateApproveRequest) returns (UpdateApproveResponse);
}