syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.approve;
import "activity/approve/approve_admin_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.approve";
option java_outer_classname = "ApproveAdminServiceProto";

message CreateApproveRequest {
  /**
 * 操作人邮箱前缀
 */
  string operator = 1;
  /**
  操作人部门编码
   */
  string code = 2;
  /**
  * 审批单ID
  * 参数格式:[{\"activityId\":100,\"entityId\":100,\"type\":1},{\"activityId\":100,\"entityId\":101,\"type\":1}]
  * activityId 活动ID ；entityId  策略ID ；type 联合拉新奖励审批传1
   */
  string entities = 3;
}

message CreateApproveResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message BatchUpdateApproveStatusRequest {
  repeated uint64 id = 1;
  int32 status = 2;
  int32 entity_status = 3;
  string operator = 4;
}

message BatchUpdateApproveStatusResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  更新失败的记录ID
   */
  repeated uint64 data = 3;
}

/**
审批域服务
 */
service ApproveAdminService {
  /**
  发起审批
   */
  rpc CreateApprove (CreateApproveRequest) returns (CreateApproveResponse);
  /**
  更新审批记录状态
   */
  rpc UpdateApproveRecordStatus (BatchUpdateApproveStatusRequest) returns (BatchUpdateApproveStatusResponse);
}