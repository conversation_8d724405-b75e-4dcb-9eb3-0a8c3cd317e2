syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.registration;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.registration";
option java_outer_classname = "ActivityRegistrationServiceProto";

message CustomizeActivityBasicDataRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message CustomizeActivityBasicDataResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateOneUserActivityRegistrationRequest{
  /**
 * 活动ID
 */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;

  /**
   * 用户id
   */
  uint64 user_id = 3;

  /**
   * 资格id
   */
  uint64 entity_id = 4;

  /**
   * 资格json data
   */
  int32 entity_type = 5;

  string json_data = 6;
}

message UpdateOneUserActivityRegistrationResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

service ActivityRegistrationService {
  /**
   * 设置自定义基值
   */
  rpc CustomizeActivityBasicData (CustomizeActivityBasicDataRequest) returns (CustomizeActivityBasicDataResponse);

  /**
   * 订正某一条资格的json data
   */
  rpc UpdateOneUserActivityRegistration (UpdateOneUserActivityRegistrationRequest) returns (UpdateOneUserActivityRegistrationResponse);
}