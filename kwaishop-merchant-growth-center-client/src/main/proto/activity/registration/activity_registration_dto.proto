syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.registration;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.registration";
option java_outer_classname = "RegistrationDTOProto";

/**
用户报名记录变更事件
 */
message UserRegistrationRecordStatusChangeEvent {
  /**
  活动ID
   */
  uint64 activity_id = 1;

  /**
  实体ID
   */
  uint64 entity_id = 2;

  /**
  用户ID
   */
  uint64 user_id = 3;

  /**
   * 更新时间
   */
  uint64 update_time = 4;

  /**
   * 变更前的记录状态
   */
  int32 old_record_status = 5;

  /**
   * 变更后的记录状态
   */
  int32 new_record_status = 6;

  /**
  是否删除，1-未删除，0-已删除
   */
  int32 deleted = 7;

  /**
  变更类型
   */
  string change_type = 8;

  /**
   * 消息实体类型 1活动 2任务 3指标 10策略
   */
  int32 entity_type = 9;
  /*
     资格来源
   */
  string source = 10;

}

message UserRegistrationRecordOptionChangeEvent {
  /**
  活动ID
   */
  uint64 activity_id = 1;

  /**
  实体ID
   */
  uint64 entity_id = 2;

  /**
  用户ID
   */
  uint64 user_id = 3;

  /**
   * 消息实体类型 1活动 2任务 3指标 10策略
   */
  int32 entity_type = 4;
}

message TaskCrowdDiffRetryMsg {
  uint64 activity_id = 1;
  uint64 parent_task_id = 2;
  string crowd_key = 3;
  string source = 4;
}

enum UserRegistrationRecordStatus {
  // 初始状态
  NONE = 0;
  // 未报名
  NOT_REGISTRATION = 1;
  // 已报名
  ALREADY_REGISTRATION = 10;
}


