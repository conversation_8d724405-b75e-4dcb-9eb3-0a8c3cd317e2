syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator";
option java_outer_classname = "BaseIndicatorDTOProto";

message SingleBasicConfig {
  int64 indicator_id = 1;
  string basic_value_type = 2;
  // 自定义上传基期的配置
  CustomBasicConfig customize_basic_config = 3;
  // 系统计算基期的配置
  SystemCalcBasicConfig system_calc_basic_config = 4;
}

message CustomBasicConfig {
    BasicFactorConfig customize_basic_config = 1;
}

message SystemCalcBasicConfig {
  // 基期指标因子配置
  repeated BasicFactorConfig basic_factor_config_list = 1;
  // 多基期因子取值方式
  string basic_calc_type = 2;
  // 基期上限值
  string fixed_max_value = 3;
  // 基期下限值
  string fixed_min_value = 4;
}

message BasicFactorConfig {
  // 基期算法类型
  string base_algorithm = 1;
  // 因子配置
  BasicFactorConfigDetail factor_config = 2;
}

message BasicFactorConfigDetail {
  // 基期时间类型
  string indicator_time_type = 1;
  // excel、hive
  int32 customize_type = 2;
  // 自定义上传文件地址
  string base_algorithm_customize_url = 3;
  // Hive导入配置
  HiveImportConfig hive_import_config = 4;
  // 基期开始时间
  int64 fixed_start_time = 5;
  // 基期结束时间
  int64 fixed_end_time = 6;
  // 偏移事件类型
  string offset_event_type = 7;
  // 相对时间
  int64 relative_day = 8;
  // 持续时间
  int64 period_day = 9;
  // 系数
  string coefficient = 10;
  // 是否剔除节假日
  bool exclusive_festival_days = 11;
  // 是否剔除最大最小值
  bool exclusive_max_min_days = 12;
  // 是否剔除有效直播日
  bool exclusive_invalid_live_days = 13;
  // 有效直播时长
  string valid_live_time = 14;
  // 自定义基期计算规则Code
  string custom_base_calc_rule_code = 15;
}

message HiveImportConfig {
  string database = 1;
  string table = 2;
  string crowd_condition = 3; // 人群分层信息
  string seller_id_column_name = 4;
  string partition_condition = 5;
  repeated HiveExtraColumn extra_column_list = 6;
}

message HiveExtraColumn {
  int64 entity_id = 1;
  string column_name = 2;
  string unit = 3;
}