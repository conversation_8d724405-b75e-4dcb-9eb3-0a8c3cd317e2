syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.audit;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.audit";
option java_outer_classname = "AuditTestServiceProto";

message MockRiskControlCallbackMsgRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 审批对象唯一ID
   */
  string subject_id = 2;
  /**
   * 风控结果类型
   */
  uint32 result_type = 3;
  /**
   * 操作人
   */
  string operator = 4;
  /**
   * 活动ID
   */
  uint64 activity_id = 5;
}

message MockRiskControlCallbackMsgResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message OneKeyFinishActivityAuditRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 风控结果类型
   */
  uint32 result_type = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message OneKeyFinishActivityAuditResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message ReTriggerTaskFinishMsgRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  uint64 task_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message ReTriggerTaskFinishMsgResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateAuditConfigRequest {
  /**
   * 配置id
   */
  uint64 id = 1;
  /**
   * 风控保护码
   */
  string risk_code = 2;
}

message UpdateAuditConfigResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message MockRoiTriggerMsgRequest {
  /**
   * 事件ID
   */
  string event_id = 1;
  /**
   * 计算类型
   */
  uint32 roi_type = 2;
  /**
   * 横向ID
   */
  uint64 resource_activity_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message MockRoiTriggerMsgResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}
message MockRcCommonReq {
  string type = 1;
  /**
  * 用户ID
  */
  uint64 user_id = 2;

  string parameter = 3;

  uint64 activity_id = 4;
}

message UpdateUserIndicatorWhenReDrawActivityRequest {

  /**
   * 用户ID集合
   * 如果为空，则读取kconf配置数据
   */
  repeated uint64 user_id = 1;

  /**
  * 活动id
  */
  int64 activity_id = 2;
  /**
   * 操作人
   */
  string operator = 3;

  /**
 * 来源
 */
  string source = 4;

  /**
 * 场景
 */
  string scene = 5;

  /**
   * 指标ID
   */
  uint64 indicator_id = 6;
  /**
   * 事件ID
   */
  string event_id = 7;
  /**
   * 指标数值
   */
  uint64 indicator_value = 8;

  /**
 * 是否更新
 */
  bool update = 9;
}
message UpdateIndicatorCacheWhenMissingRequest {

  /**
   * 用户ID集合
   * 如果为空，则读取kconf配置数据
   */
  repeated uint64 user_id = 1;

  /**
  * 活动id
  */
  int64 activity_id = 2;
  /**
   * 操作人
   */
  string operator = 3;

  /**
   * 指标ID
   */
  uint64 indicator_id = 4;

  /**
   * 是否更新
   */
  bool update = 5;

  /**
   * 取消后重新领取
   */
  bool cancel_and_draw = 6;

}

message UpdateUserIndicatorWhenReDrawActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateIndicatorCacheWhenMissingResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateUserAuditRecordStatusRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
    */
  uint64 activity_id = 2;
  /**
   * 审批记录ID
   */
  string subject_id = 3;

  int32 status = 4;

  string operator = 5;
}

message AwardReissueAuditMsgMockRequest {
  /**
   * 消息体
   */
  string message = 1;
}

message AwardReissueAuditMsgMockResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
测试服务
 */
service AuditTestService {
  /**
   * 模拟风控审核结果
   */
  rpc MockRiskControlCallbackMsg (MockRiskControlCallbackMsgRequest) returns (MockRiskControlCallbackMsgResponse);

  /**
   * 一键完成活动下的所有审批
   */
  rpc OneKeyFinishActivityAudit (OneKeyFinishActivityAuditRequest) returns (OneKeyFinishActivityAuditResponse);

  /**
   * 模拟任务完成消息
   */
  rpc ReTriggerTaskFinishMsg (ReTriggerTaskFinishMsgRequest) returns (ReTriggerTaskFinishMsgResponse);

  /**
   * 修改审批记录
   */
  rpc UpdateAuditConfig (UpdateAuditConfigRequest) returns (UpdateAuditConfigResponse);
  /**
   * 修改用户审批记录
   */
  rpc UpdateUserAuditRecordStatus(UpdateUserAuditRecordStatusRequest) returns (UpdateAuditConfigResponse);

  /**
   * mock roi计算触发消息
   */
  rpc MockRoiTriggerMsg(MockRoiTriggerMsgRequest) returns (MockRoiTriggerMsgResponse);

  /**
   * mock 风控接口
   */
  rpc MockRcApi(MockRcCommonReq) returns (MockRoiTriggerMsgResponse);

  /**
   * 重新领取任务后更新当前用户指标数据
   */
  rpc UpdateIndicatorProgressWhenReDrawActivity(UpdateUserIndicatorWhenReDrawActivityRequest) returns (UpdateUserIndicatorWhenReDrawActivityResponse);

  /**
   * 用户task指标缓存丢失进行补充
   */
  rpc UpdateIndicatorCacheWhenMissing(UpdateIndicatorCacheWhenMissingRequest) returns (UpdateIndicatorCacheWhenMissingResponse);

  /**
   * mock 奖励复核消息
   */
  rpc MockAwardReissueAuditMsg(AwardReissueAuditMsgMockRequest) returns (AwardReissueAuditMsgMockResponse);
}