syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.audit;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.audit";
option java_outer_classname = "ActivityAuditDomainServiceProto";

message QueryUserAuditRecordRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动id
   */
  uint64 activity_id = 2;
}

message QueryUserAuditRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 审批单记录
   */
  repeated AuditRecordInfoDTO data = 3;
}

message AuditRecordInfoDTO {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 审批单唯一id
   */
  string subject_id = 2;
  /**
   * 审批时间
   */
  uint64 audit_time = 3;
  /**
   * 状态
   */
  uint32 status = 4;
  /**
   * 奖励配置ID
   */
  uint64 config_id = 5;
  /**
   * 活动id
   */
  uint64 activity_id = 6;
}

/**
测试服务
 */
service ActivityAuditDomainService {
  /**
   * 查询用户审批单记录
   */
  rpc QueryUserAuditRecord (QueryUserAuditRecordRequest) returns (QueryUserAuditRecordResponse);
}