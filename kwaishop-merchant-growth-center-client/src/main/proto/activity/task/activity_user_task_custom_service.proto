syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.task.custom;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.custom";
option java_outer_classname = "ActivityUserTaskCustomServiceProto";


message BatchGetActivityInfoForFlowBoostRequest{
  /**
   *用户id
   */
  repeated int64 user_id = 1;
  /**
   * 系列类型
   */
  int32 series_type = 2;
  /**
   * 查询时间
   */
  int64 query_time = 3;


}

message BatchGetActivityInfoForFlowBoostResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 是否完成任务
   */
  map<int64, ActivityInfoForFlowBoostDTO> data = 3;
}

message ActivityInfoForFlowBoostDTO{
  /**
   * 活动id
   */
  int64 activity_id = 1;
  /**
   * 任务id
   */
  int64 task_id = 2;
  /**
   * 任务状态（参考：UserTaskStatusEnum）
   */
  int32 status = 3;
}


message ManualDrawSignUpActivityRequest{
  /**
   * 类型，Excel，人群包
   */
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;
  /**
   *  任务ID列表
   */
  repeated int64 task_id = 4;
  /**
   * 是否报名
   */
  bool sign_up = 5;
  /**
   * 操作人
   */
  string operator = 6;
  /**
   * 类型
   */
  int32 type = 7;
}

message ManualDrawSignUpActivityResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message DeleteUserActivityDataRequest {
  /**
  * 类型，Excel，人群包
  */
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message DeleteUserActivityDataResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message DeleteUserTaskDataRequest {
  /**
 * 类型，Excel，人群包
 */
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;

  /**
  * 父任务ids
  * ⚠️：如果删除的父任务id为当前用户的全部子任务，不要使用当前方法，请使用DeleteUserActivityData方法
   */
  repeated int64 parent_task_id = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message DeleteUserAuditDataRequest {
  /**
* 类型，Excel，人群包
*/
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;

  /**
  * 父任务ids
  * ⚠️：如果删除的父任务id为当前用户的全部子任务，不要使用当前方法，请使用DeleteUserActivityData方法
   */
  repeated int64 parent_task_id = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message DeleteUserTaskDataResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message DeleteUserAuditDataResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UpdateUserTaskDataReq {
  /**
* 类型，Excel，人群包
*/
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;

  /**
   * 任务状态
   */
  int32 update_status = 4;

   /**
   * 父任务id
   */
  int64 parent_task_id = 5;

  repeated int64 update_indicator_id = 6;

  /**
  * 子任务ids
   */
  repeated int64 sub_task_id = 7;
  /**
   * 操作人
   */
  string operator = 8;

}

message UpdateUserTaskDataResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

service ActivityUserTaskCustomService{

  /**
   * 流量助推任务特殊查询接口
   */
  rpc BatchGetActivityInfoForFlowBoost (BatchGetActivityInfoForFlowBoostRequest) returns (BatchGetActivityInfoForFlowBoostResponse);

  /**
   * 手动增量领取资格或报名
   */
  rpc ManualDrawSignUpActivity (ManualDrawSignUpActivityRequest) returns (ManualDrawSignUpActivityResponse);

  /**
   * 手动删除一批商家的任务数据
   */
  rpc DeleteUserActivityData (DeleteUserActivityDataRequest) returns (DeleteUserActivityDataResponse);

  /**
   * 手动删除一批商家的子活动数据
   */
  rpc DeleteUserTaskData (DeleteUserTaskDataRequest) returns (DeleteUserTaskDataResponse);
  /**
  * 手动删除一批商家的子活动审批记录数据
  * 谨慎使用，写这个方法的目的是由于在调用CancelUserActivityServiceImpl.cancelUserTask删除对应的子任务审批记录时由于新的subject_id是用“_”
  * 拼接，导致会出现ArrayIndexOutOfBoundsException，同时在封网期，因此只能现在prt临时使用这个方法删掉对应活动的子任务审批记录再执行DeleteUserTaskData
  */
  rpc DeleteUserAuditData (DeleteUserAuditDataRequest) returns (DeleteUserAuditDataResponse);
  /**
   * 手动更新一批用户任务数据状态
   */
  rpc UpdateUserTaskStatus (UpdateUserTaskDataReq) returns (UpdateUserTaskDataResponse);
}