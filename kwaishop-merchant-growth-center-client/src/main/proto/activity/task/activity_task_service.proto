syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.task;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task";
option java_outer_classname = "ActivityTaskServiceProto";

/**
通用返回体
 */
message CommonUserActivityResponse {
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}


message DrawActivityByActivityIdResponse {
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message BatchDrawActivityByActivityIdResponse {
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}


message BatchDrawActivityByTaskIdResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message GetActivityByIdRequest{
  /**
   *活动id
   */
  int64 activity_id = 1;
  /**
   *用户id
   */
  int64 user_id = 2;
}

message BatchGetActivityByIdRequest{
  /**
   *活动id
   */
  repeated uint64 activity_id = 1;
  /**
   *用户id
   */
  int64 user_id = 2;
}

message GetActivityByIdResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 活动信息
   */
  ActivityDTO data = 3;
}

message BatchGetActivityByIdResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 活动信息
   */
  map<uint64, ActivityDTO> data = 3;
}

/**
业务信息
 */
message ActivityDTO {
  /**
   *活动ID
   */
  uint64 id = 1;
  /**
   *活动名称
   */
  string name = 2;
  /**
   * 活动类型
   */
  int32 type = 3;
  /**
   * 系列类型
   */
  int32 series_type = 4;
  /**
   *描述
   */
  string description = 5;
  /**
   * 人群类型：1-所有商家，2-先报名，3-画像圈定，4-商家等级
   */
  int32 crowd_type = 6;
  /**
   * 领取限制类型：0-无限制，1-只可系统领取
   */
  int32 draw_limit_type = 7;
  /**
   * 人群配置参数
   */
  string crowd_config = 8;
  /**
   * 别名
   */
  string alias = 9;
  /**
   *开始时间
   */
  uint64 start_time = 10;
  /**
   *结束时间
   */
  uint64 end_time = 11;
  /**
   * 领取开始时间
   */
  uint64 draw_start_time = 12;
  /**
   * 领取结束时间
   */
  uint64 draw_end_time = 13;
  /**
   * 活动状态
   */
  int32 status = 14;
  /**
   * 扩展参数
   */
  string ext = 15;
  /**
   * 活动完成条件
   */
  string complete_condition = 16;
  /**
   * 活动标签
   */
  string show_tag = 17;
  /**
   * 活动对外名称
   */
  string show_name = 18;
  /**
   * 前台配置
   */
  string front_config = 19;
  /**
   * 关联的横向活动id
   */
  uint64 resource_activity_id = 20;
  /**
   * 展示配置
   */
  string show_config = 21;
  /**
   * 附加信息
   */
  string attach_info = 22;
  /**
   * 创建人
   */
  string creator = 23;
  /**
   * 所属行业
   */
  string biz_name = 24;
  /**
   * 创建时间
   */
  uint64 create_time = 25;
  /**
   * 天河页面配置
   */
  string galax_page_config = 26;

  /**
   * 极速版配置
   */
  string lite_config = 27;
}


/**
 * 通过活动id领取任务request
 */
message DrawActivityByActivityIdRequest {
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 来源
   */
  string source = 3;
  /**
  * 业务方扩展参数
  */
  string ext = 4;
}

/**
 * 批量通过活动id领取任务request
 */
message BatchDrawActivityByActivityIdRequest {
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   * 来源
   */
  string source = 2;
  /**
   * 活动参数
   */
  repeated ActivityAttributeDTO activity = 3;
}

message ActivityAttributeDTO {
  /**
   *活动id
   */
  int64 activity_id = 1;
  /**
 * 业务方扩展参数
 */
  string ext = 2;
}

message UserRegistrationRequest{
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 报名的实体类型
   */
  int32 entity_type = 3;
  /**
   * 报名的实体id
   */
  repeated int64 entity_id = 4;
  /**
   * 来源
   */
  string source = 5;
}

message UserRegistrationWithJsonDateRequest{
  /**
  *用户id
  */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 报名的实体id
   */
  repeated int64 task_id = 3;
  /**
   * 用户基础数据，用于动态计算
   */
  string json_data = 4;
  /**
   * 来源
   */
  string source = 5;
}

message UserRegistrationResponse{
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message UserRegistrationWithJsonDateResponse{
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message BatchDrawActivityByTaskIdRequest{
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 任务id列表
   */
  repeated int64 task_id = 3;
  /**
   * 来源
   */
  string source = 4;
  /**
  * 业务方扩展参数
  */
  string ext = 5;
}

message GetRecentlyUserActivityInfoBySeriesTypeRequest{
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *类型(1-商家策略,2-商家任务)
   */
  int32 type = 2;
  /**
   * 系列类型
   */
  int64 series_type = 3;
}

message GetRecentlyUserActivityInfoBySeriesTypeResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  UserActivityInfoDTO data = 3;
}

message UserActivityInfoDTO{
  /**
   * 活动下已领取的任务列表
   */
  repeated UserTaskInfoDTO already_draw_task_info = 1;
  /**
   * 活动下未领取的任务列表
   */
  repeated CanDrawTaskInfoDTO can_draw_task_info = 2;
  /**
   *用户id
   */
  int64 user_id = 3;

  /**
   *活动id
   */
  int64 activity_id = 4;
  /**
   * 活动状态
   */
  int32 status = 5;
  /**
   * creator
   */
  string creator = 6;
  /**
   * modifier
   */
  string modifier = 7;
  /**
   * 扩展参数
   */
  string ext = 8;
  /**
   * 活动alias
   */
  string alias = 9;
  /**
   * 活动领取截止时间
   */
  int64 draw_end_time = 10;
  /**
   * 活动开始时间
   */
  int64 start_time = 11;
  /**
   * 活动截止时间
   */
  int64 end_time = 12;
  /**
   * 活动领取开始时间
   */
  int64 draw_start_time = 13;
  /**
   * 活动名称
   */
  string activity_name = 14;
  /**
   * 活动展示标签
   */
  string show_tag = 15;
  /**
   * 活动系列类型
   */
  int64 series_type = 16;
  /**
   * 活动记录创建时间
   */
  int64 create_time = 17;
  /**
   * 用户完成任务后可以解锁的任务
   */
  repeated LockTaskInfoDTO lock_task_info = 18;

  /**
   * 是否为极速版活动
   */
  bool lit_config_flag = 19;
}

message LockTaskInfoDTO {
  /**
   * 待解锁的父任务id
   */
  int64 task_id = 1;
  /**
   * 前置父任务id
   */
  int64 pre_task_id = 2;
}

message AlreadyDrawActivityInfoDTO {
  /**
   * 活动下已领取的任务列表
   */
  repeated UserTaskInfoDTO task_info = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 活动状态
   */
  int32 status = 3;
}

message UserTaskInfoDTO{
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
  /**
   * 父任务id
   */
  int64 parent_id = 3;
  /**
   *任务id
   */
  int64 task_id = 4;
  /**
   *任务开始时间
   */
  int64 start_time = 5;
  /**
   *任务结束时间
   */
  int64 end_time = 6;
  /**
   * 任务状态
   */
  int32 status = 7;
  /**
   * creator
   */
  string creator = 8;
  /**
   * modifier
   */
  string modifier = 9;
  /**
   *  任务alias
   */
  string alias = 10;
  /**
   * 任务展示优先级
   */
  int32 priority = 11;
  /**
   * 阶段
   */
  uint32 stage = 12;
}

message CanDrawTaskInfoDTO{
  /**
   * 可领取的任务id(活动一级子任务)
   */
  int64 task_id = 1;
  /**
   * 父任务id
   */
  int64 parent_id = 2;
  /**
   * 任务alias
   */
  string alias = 3;
}

message GetUserActivityInfoRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
}

message GetAlreadyDrawActivityInfoRequest{
  /**
 * 用户id
 */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
}

message BatchGetUserActivityInfoRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  repeated int64 activity_id = 2;
}

message BatchGetAllChildTaskRequest{
  /**
 * 用户id
 */
  int64 activity_id = 1;
  /**
   * 活动id
   */
  repeated int64 parent_task_id = 2;
}

message BatchGetTaskByAliasRequest{
  repeated string alias = 1;
}

message BatchGetAllChildTaskResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  repeated TaskDTO data = 3;
}

message BatchGetTaskByAliasResponse{
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  repeated TaskDTO data = 3;
}

message TaskDTO {
  /**
   * 任务id
   */
  int64 id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 父任务id
   */
  int64 parent_task = 3;
  /**
   * 前置任务id
   */
  int64 pre_task = 4;
  /**
   * 优先级
   */
  int32 priority = 5;
  /**
   * 开始时间
   */
  int64 start_time = 6;
  /**
   * 结束时间
   */
  int64 end_time = 7;
  /**
   * 完成条件
   */
  string complete_condition = 8;
  /**
   * alias
   */
  string alias = 9;
  /**
   * 任务状态
   */
  int32 status = 10;
  /**
   * 任务周期类型：1-绝对时间，2-相对时间
   */
  int32 period_type = 11;
  /**
   * 周期天数
   */
  int32 period_day = 12;
  /**
   * 任务标题
   */
  string name = 13;
  /**
   * 任务描述
   */
  string description = 14;
  /**
   * 补贴任务ID
   */
  uint64 resource_rule_id = 15;
  /**
   * 阶段
   */
  uint64 stage = 16;
  /**
   * 额外信息
   */
  string ext = 17;
  /**
   * 任务类型 1单阶段 2多阶段
   */
  uint32 type = 18;
}

message GetUserActivityInfoResponse{
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  UserActivityInfoDTO data = 3;

  /**
   * 校验结果
   */
  CheckResultDTO check_result = 4;
}

message CheckResultDTO{

  /**
   * 失败类型
   */
  uint32 status_code = 1;

  /**
   * 错误信息
   */
  string status_msg = 3;
}

message GetAlreadyDrawActivityInfoResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;

  /**
   * 活动下已领取的任务列表
   */
  AlreadyDrawActivityInfoDTO data = 3;
}

message BatchGetUserActivityInfoResponse{
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  map<int64, UserActivityInfoDTO> data = 3;
}

message GetTaskByIdRequest {
  /**
   * 任务id列表
   */
  repeated uint64 task_id = 1;
}

message GetTaskByIdResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 任务对象
   */
  map<uint64, TaskDTO> data = 3;
}

message GetUserRegistrationActivityInfoRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
}

message GetUserRegistrationTaskInfoRequest{
  /**
 * 用户id
 */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 任务id
   */
  int64 task_id = 3;
}

message GetUserRegistrationActivityInfoResponse{
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 活动报名信息
   */
  RegistrationActivityInfoDTO data = 3;
}

message RegistrationActivityInfoDTO{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 基础数据
   */
  string json_data = 3;
}

message GetCardUserActivityInfoRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 展示位置
   */
  string show_type = 2;
}

message GetCardUserActivityInfoResponse{
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 操作结果对象
   */
  repeated UserActivityInfoDTO data = 3;
  /**
   * 各个活动的奖励总和
   */
  repeated UserActivityAwardInfoDTO award_info = 4;
  /**
   * 各个活动的获得奖励总和
   */
  repeated UserActivityAwardInfoDTO reward_info = 5;
}

message UserActivityAwardInfoDTO {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动id
   */
  uint64 activity_id = 2;
  /**
   * 奖励类型
   */
  uint32 award_type = 3;
  /**
   * 总数
   */
  uint64 total = 4;
  /**
   * 任务维度
   */
  repeated UserTaskAwardInfoDTO task_award_info = 5;
  /**
   * 是否有奖励选择
   */
  bool award_select = 6;
  /**
   * 最大值总数
   */
  uint64 total_max = 7;
}

message UserTaskAwardInfoDTO {
  /**
   * 任务id
   */
  uint64 task_id = 1;
  /**
   * 奖励类型
   */
  uint32 award_type = 2;
  /**
   * 总数
   */
  uint64 total = 3;
}

message GetUserActivityListInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 来源
   */
  string source = 2;

  /**
   * 用户活动状态 0 查全部 1 已参加 2 未参加
   */
  uint32 draw_status = 3;

  /**
   * 活动状态 0 查全部 1 未开始 2 进行中 3 已结束
   */
  uint32 activity_status = 4;

  /**
   * 分页参数
   */
  PageRequest page_request = 5;

  /**
   * 场景
   */
  uint32 scene = 6;
}

message PageRequest {

  /**
   * offset
   */
  uint32 offset = 1;

  /**
   * limit
   */
  uint32 limit = 2;
}

message GetUserActivityListInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  repeated UserActivityListInfoDTO data = 3;
  /**
   * 聚合数据
   */
  UserActivityListAggInfoDTO agg_data = 4;
}

message UserActivityListAggInfoDTO {
  /**
   * 数据
   */
  repeated UserActivityListInfoDTO user_activity_list = 1;
  /**
   * 各个tab活动数量
   */
  repeated ActivityListTabInfoDTO tab_info_list = 2;
}

message UserActivityListInfoDTO {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 当前状态
   */
  uint32 status = 2;
  /**
   * 活动领取开始时间
   */
  uint64 draw_start_time = 3;
  /**
   * 活动领取截止时间
   */
  uint64 draw_end_time = 4;
  /**
   * 活动开始时间
   */
  uint64 start_time = 5;
  /**
   * 活动结束时间
   */
  uint64 end_time = 6;

  /**
    * 活动名称
    */
  string activity_name = 7;

  /**
   * 活动图片
   */
  string activity_image = 8;

  /**
    * 活动对外名称
    */
  string show_name = 9;

  /**
    * 报名人数
    */
  uint64 draw_count = 10;

  /**
   * 活动创建时间
   */
  uint64 create_time = 11;

  /**
   * 纵向活动 活动报名页面
   */
  string activity_join_url = 12;

  /**
  活动标签
  */
  string show_tag = 13;

  /**
    展示结束时间
   */
  uint64 show_end_time = 14;

  /**
   * 活动当前展示状态 1、未开始 2、进行中 3、已结束 4、未报名（新增）
   */
  uint64 show_status = 15;

  /**
   * 活动真实开始时间
   */
  uint64 real_start_time = 16;

  /**
   * 活动真实结束时间
   */
  uint64 real_end_time = 17;

  /**
   * 用户完成活动可获得的奖励
   */
  repeated AwardShowDTO award_info = 18;

  /**
   * 用户在该活动下已获得的奖励
   */
  repeated AwardShowDTO award_record = 19;

  /**
   * 构建活动可选奖励配置
   */
  AwardSelectionShowConfigDTO award_selection_config = 20;

  /**
   * 用户活动完成进度 完成的任务数/总任务数
   */
  CompleteProgressDTO user_activity_progress = 21;

  /**
   * 展示配置
   */
  bool promote_activity = 22;

  /**
   * 活动列表页总数 冗余一份（后续不使用）
   */
  uint32 total = 23;

  /**
   * 奖励发放状态
   * 1：未发放 2：部分发放 3：全部发放
   */
  uint32 award_settle_status = 24;

  /**
   * 任务聚合展示信息
   */
  TaskAggShowInfoDTO task_agg_show_info = 25;

  /**
   * 任务简述
   */
  string activity_brief_desc = 26;

  /**
   * 权益配置信息
   */
  EquityConfigDTO equity_config = 27;
}

message ActivityListTabInfoDTO {

  /**
   * 场景
   */
  uint32 scene = 1;

  /**
   * 总数
   */
  uint32 total = 2;
}

message EquityConfigDTO {
  /**
   * 是否展示图标
   */
  bool display_icon = 1;
  /**
   * 权益内容
   */
  string content = 2;
}

message TaskAggShowInfoDTO {
  /**
   * 任务聚合展示状态
   * 1：未开始 2：进行中 3：已完成 4：未完成
   */
  uint32 task_agg_show_status = 1;
}

message DynamicDrawRuleDTO {
  /**
   * 规则字段
   */
  string field = 1;
  /**
   * 规则描述
   */
  string desc = 2;
  /**
   * 字段选项
   */
  repeated DynamicDrawRuleCondition field_options = 3;

}

message DynamicDrawRuleCondition {
  /**
    条件标识
   */
  string condition_code = 1;
  /**
    条件名称
   */
  string condition_name = 2;
}

message DynamicDrawActivityRequest {
  // 用户id
  uint64 user_id = 1;
  // 事件标识
  string code = 2;
  // 来源
  string source = 3;
  // 类型
  uint32 registration_type = 4;
  /**
 * 领取规则
 */
  repeated DynamicDrawRuleDTO draw_rule = 5;
  /**
  * 指定活动id
  */
  uint64 activity_id = 6;
}

message QueryDynamicDrawOptionRequest{
  /**
   * 活动id
   * 事件标识 二选其一 活动id高优
   */
  int64 activity_id = 1;
  /**
   * 事件标识
   * 活动id 二选其一 活动id高优
   */
  string code = 2;
}

message QueryDynamicDrawOptionResponse{
  /**
   * 返回结果码
   */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  * 领取规则
  */
  repeated DynamicDrawRuleDTO draw_rule = 3;
}

message DynamicDrawActivityResponse {
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
}

message GetAllTaskByActivityIdRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
}

message GetAllTaskByActivityIdResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;

  /**
   * 错误信息
   */
  string error_msg = 2;

  /**
   * 任务
   */
  repeated TaskDTO data = 3;
}

message SubmitActivityRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 父任务ID
   */
  uint64 parent_task_id = 3;
  /**
   * 子任务ID列表
   */
  repeated uint64 sub_task_id = 4;
  /**
   * source
   */
  string source = 5;
  /**
  * 业务方扩展参数
  * 目标金额，奖励信息
  */
  string ext = 6;
}

message SubmitActivityResponse {
  /**
   * 结果返回码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message PageQueryActivityListRequest {
  /**
   * 活动列表查询参数
   */
  string param = 1;
}

message PageQueryActivityListResponse {
  /**
   * 结果返回码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 活动列表
   */
  string data = 3;
}

service ActivityTaskDomainService{
  /**
   * 通过活动Id领取任务
   */
  rpc DrawActivityByActivityId (DrawActivityByActivityIdRequest) returns (DrawActivityByActivityIdResponse);
  /**
   * 批量通过活动Id领取任务
   */
  rpc BatchDrawActivityByActivityId (BatchDrawActivityByActivityIdRequest) returns (BatchDrawActivityByActivityIdResponse);
  /**
   * 批量通过任务id领取任务
   */
  rpc BatchDrawActivityByTaskId (BatchDrawActivityByTaskIdRequest) returns (BatchDrawActivityByTaskIdResponse);
  /**
   * 获取用户活动信息
   */
  rpc GetUserActivityInfo(GetUserActivityInfoRequest) returns (GetUserActivityInfoResponse);
  /**
   * 批量获取用户活动信息
   */
  rpc BatchGetUserActivityInfo(BatchGetUserActivityInfoRequest) returns (BatchGetUserActivityInfoResponse);
  /**
   * 获取seriesType下用户最近参与的活动记录
   */
  rpc GetRecentlyUserActivityInfoBySeriesType (GetRecentlyUserActivityInfoBySeriesTypeRequest) returns (GetRecentlyUserActivityInfoBySeriesTypeResponse);
  /**
   * 批量获取所有子孙任务
   */
  rpc BatchGetAllChildTask(BatchGetAllChildTaskRequest) returns (BatchGetAllChildTaskResponse);
  /**
   * 通过alias获取任务信息
   */
  rpc BatchGetTaskByAlias(BatchGetTaskByAliasRequest) returns (BatchGetTaskByAliasResponse);
  /**
   * 用户报名接口
   */
  rpc UserRegistration(UserRegistrationRequest) returns (UserRegistrationResponse);

  /**
   * 用户报名接口需提供用户基础数据
   */
  rpc UserRegistrationWithJsonDate(UserRegistrationWithJsonDateRequest) returns (UserRegistrationWithJsonDateResponse);

  /**
   * 通过活动id获取活动配置信息
   */
  rpc GetActivityById(GetActivityByIdRequest) returns (GetActivityByIdResponse);

  /**
   * 通过活动ID批量获取活动配置信息
   */
  rpc BatchGetActivityById(BatchGetActivityByIdRequest) returns (BatchGetActivityByIdResponse);

  /**
 * 通过活动id获取活动配置信息
 */
  rpc GetTaskById(GetTaskByIdRequest) returns (GetTaskByIdResponse);
  /**
   * 获取已领取的活动信息
   */
  rpc GetAlreadyDrawActivityInfo(GetAlreadyDrawActivityInfoRequest) returns (GetAlreadyDrawActivityInfoResponse);
  /**
   * 获取用户报名活动的基础数据信息
   */
  rpc GetUserRegistrationActivityInfo(GetUserRegistrationActivityInfoRequest) returns (GetUserRegistrationActivityInfoResponse);

  /**
 * 获取用户报名活动的基础数据信息
 */
  rpc GetUserRegistrationTaskInfo(GetUserRegistrationTaskInfoRequest) returns (GetUserRegistrationActivityInfoResponse);

  /**
   * 获取工作台卡片展示的所有用户活动信息
   */
  rpc GetCardUserActivityInfo(GetCardUserActivityInfoRequest) returns (GetCardUserActivityInfoResponse);

  /**
   * 获取用户所有已领取和可领取的活动记录
   */
  rpc GetUserActivityListInfo(GetUserActivityListInfoRequest) returns (GetUserActivityListInfoResponse);

  /**
  * 通过事件Code，领取活动或报名
  */
  rpc DynamicDrawActivity (DynamicDrawActivityRequest) returns(DynamicDrawActivityResponse);
  /**
   * 获取事件活动选项接口
   */
  rpc QueryDynamicDrawOption(QueryDynamicDrawOptionRequest)returns(QueryDynamicDrawOptionResponse);

  /**
   * 根据活动ID获取活动下的所有任务
   */
  rpc GetAllTaskByActivityId (GetAllTaskByActivityIdRequest) returns (GetAllTaskByActivityIdResponse);

  /**
   * 提报任务接口
   */
  rpc SubmitActivity (SubmitActivityRequest) returns (SubmitActivityResponse);

  /**
   * 查询活动列表接口
   */
  rpc PageQueryActivityList (PageQueryActivityListRequest) returns (PageQueryActivityListResponse);
}