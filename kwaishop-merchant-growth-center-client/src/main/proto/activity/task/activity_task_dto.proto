syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.registration;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task";
option java_outer_classname = "TaskDTOProto";

/**
用户任务记录变更事件
 */
message UserTaskRecordStatusChangeEvent {
  /**
  活动ID
   */
  uint64 activity_id = 1;
  /**
  任务ID
   */
  uint64 task_id = 2;
  /**
  用户ID
   */
  uint64 user_id = 3;
  /**
   * 更新时间
   */
  uint64 update_time = 4;
  /**
  事件类型
   */
  uint32 event_type = 5;
  /**
   * 变更前的记录状态
   */
  uint32 old_record_status = 6;
  /**
   * 变更后的记录状态
   */
  uint32 new_record_status = 7;
  /**
  是否删除，1-未删除，0-已删除
   */
  int32 deleted = 8;
}

