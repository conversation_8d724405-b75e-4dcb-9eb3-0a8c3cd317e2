syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.task.custom;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.test";
option java_outer_classname = "ActivityTestServiceProto";


/**
通用返回体
 */
message CommonActivityTestResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message CancelUserActivityRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message CancelUserTaskRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
  * 活动id
  */
  int64 activity_id = 2;
  /**
   * 任务id
   */
  int64 task_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message DrawActivityUnlimitedRequest{
  /**
   *用户id
   */
  int64 user_id = 1;
  /**
   *活动id
   */
  int64 activity_id = 2;
}

message CancelRegistrationInfoRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 任务组id
   */
  int64 activity_id = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message InsertLayerDataRequest{
  /**
  * 用户id
  */
  int64 user_id = 1;
  /**
   * 任务组id
   */
  string tag = 2;
  /**
   * 分层参数
   */
  string json_data = 3;
}

message DeleteLayerDataRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 任务组id
   */
  string tag = 2;
}

message DeleteEntireActivityRequest {
  /**
   * 活动ID
   */
  int64 activity_id = 1;
  /**
   * 场景
   */
  string scene_code = 2;
}

message DeleteEntireActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message SaveActivityTestUserRequest{
  int64 user_id = 1;
}

message RemoveActivityTestUserRequest{
  int64 user_id = 1;
}

message UpdateActivityInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 活动开始时间
   */
  uint64 start_time = 2;
  /**
   * 活动结束时间
   */
  uint64 end_time = 3;
  /**
   * 领取开始时间
   */
  uint64 draw_start_time = 4;
  /**
   * 领取结束时间
   */
  uint64 draw_end_time = 5;
  /**
   * 人群ID
   */
  uint64 crowd_id = 6;
  /**
   * 关联补贴活动ID
   */
  uint64 resource_activity_id = 7;
  /**
   * 是否重置关联活动ID
   */
  bool reset_resource_id = 8;
  /**
   * 活动名称
   */
  string activity_name = 9;
  /**
   * showConfig
   */
  string show_config = 10;
  /**
   * 展示截止时间
   */
  uint64 show_end_time = 11;
  /**
   * 活动标签
   */
  string show_tag = 12;
  /**
   * 前端展示配置
   */
  string front_config = 13;
  /**
   * 自定义基值文件url
   */
  string basic_data_file_url = 14;
  /**
   * 活动展示名称
   */
  string activity_show_name = 15;
  /**
   * 活动状态
   */
  uint32 status = 16;
  /**
   * 操作人
   */
  string operator = 17;
  /**
   * 创建人
   */
  string creator = 18;
  /**
   * 行业名称
   */
  string biz_name = 19;
  /**
   * 应用
   */
  string application = 20;
  /**
   * 统计过滤
   */
  string need_statistics = 21;

  string ext = 22;
  // 小二库展示配置
  string staff_repo_show_config = 23;

  string lite_config = 24;
}

message UpdateActivityInfoResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}


message UpdateTaskInfoRequest {
  /**
   * 任务id
   */
  uint64 task_id = 1;
  /**
   * 任务名称
   */
  string name = 2;
  /**
   * 任务描述
   */
  string description = 3;
  /**
   * 任务开始时间
   */
  uint64 start_time = 4;
  /**
   * 任务结束时间
   */
  uint64 end_time = 5;
  /**
   * 完成条件
   */
  string complete_condition = 6;
  /**
   * 任务状态
   */
  uint32 status = 7;
  /**
   * 周期类型
   */
  uint32 period_type = 8;
  /**
   * 周期天数
   */
  uint32 period_day = 9;
  /**
   * 人群类型
   */
  uint32 crowd_type = 10;
  /**
   * 人群配置
   */
  string crowd_config = 11;
  /**
   * 补贴规则id
   */
  uint64 resource_rule_id = 12;
  /**
   * 任务标签
   */
  string tags = 13;
  /**
   * 操作人
   */
  string operator = 14;
  /**
   * 活动id
   */
  uint64 activity_id = 15;
  /**
   * 拓展信息
   */
  string ext = 16;

  string award_condition = 17;

  int32 type = 18;
}

message UpdateUserTaskStatusRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 任务id
   */
  int64 task_id = 3;
  /**
   * 更新状态
   */
  int32 update_status = 4;
}

message DeleteUserAwardRecordRequest{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 奖励id
   */
  int64 user_award_record_id = 3;
}

message UpdateUserActivityStatusRequest{
  /**
 * 用户id
 */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 更新状态
   */
  int32 update_status = 3;
}

message DeleteTaskRequest {
  /**
   * 活动ID
   */
  int64 task_id = 1;
  /**
   * 场景
   */
  string scene_code = 2;
  /**
   * 活动ID
   */
  int64 activity_id = 3;
}

message DeleteTaskResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message TestOnlineStrategyAdminActivityRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
  /**
   * 是否mock
   */
  bool mock = 3;
}

message TestOnlineStrategyAdminActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message ReportAdminActivityOnlineProgressRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message ReportAdminActivityOnlineProgressResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message StagingMockScheduleOnlineRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
}

message StagingMockScheduleOnlineResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UserIndicatorVersionCheckRequest {
  uint64 user_id = 1;
  uint64 activity_id = 2;
  uint64 task_id = 3;
  uint64 indicator_id = 4;
  uint64 mq_version = 5;
}

message UserIndicatorVersionCheckResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

/**********************user_indicator_calc_detail清理**************************/
message UserIndicatorCalcDetailDeleteRequest {
  string start_biz_occur_date = 1;
  string end_biz_occur_date = 2;
  uint32 start_shard_id = 3;
  uint32 end_shard_id = 4;
}

message UserIndicatorCalcDetailDeleteResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message ReDrawActivityAndRecoverIndicatorProgressRequest {

  uint64 activity_id = 1;
  /**
  * 手动方式（单用户/批量Excel/批量kconf ）
  */
  uint32 manual_type = 2;
  /**
   * Excel地址或者单用户ID/kconf
   */
  string manual_entity = 3;

  bool delete_enable = 4;

  string operator = 5;

  int64 sleep_time = 6;

  bool only_update_indicator = 7;

  int64 sleep_time_before_update_indicator = 8;

}

message QueryCrowdRequest {
  string selection_source = 1;

  string crowd_key = 2;
}

message QueryAbCrowdRequest {
  uint64 crowd_id = 1;

  uint64 experiment_id = 2;
}

message QueryAbCrowdResponse {
  repeated uint64 user_id = 1;

  /**
   * 返回结果码
   */
  int32 result = 2;
  /**
   * 返回信息
   */
  string error_msg = 3;
}

message QueryAbExperimentRequest {
  uint64 user_id = 1;
  repeated uint64 crowd_id = 2;
}

message QueryAbExperimentResponse {
  repeated uint64 experiment_id = 1;

  /**
   * 返回结果码
   */
  int32 result = 2;
  /**
   * 返回信息
   */
  string error_msg = 3;
}

message CreateFissionRelationRequest {
  uint64 activity_id = 1;
  uint64 inviter_id = 2;
  uint64 invitee_id = 3;
}

message DeleteFissionRelationRequest {
  uint64 activity_id = 1;
  uint64 inviter_id = 2;
  uint64 invitee_id = 3;
}

service ActivityTestService{

  /**
   * 删除已参加的活动
   */
  rpc CancelUserActivity(CancelUserActivityRequest) returns (CommonActivityTestResponse);

  /**
   * 删除已参加活动下的单个任务
   */
  rpc CancelUserTask(CancelUserTaskRequest) returns (CommonActivityTestResponse);

  /**
   * 删除整个活动
   */
  rpc DeleteEntireActivity(DeleteEntireActivityRequest) returns (DeleteEntireActivityResponse);

  /**
   * 删除某个任务
   */
  rpc DeleteTask(DeleteTaskRequest) returns (DeleteTaskResponse);

  /**
   * 插入商家分层数据
   */
  rpc InsertLayerData(InsertLayerDataRequest) returns (CommonActivityTestResponse);
  /**
   * 删除商家分层数据
   */
  rpc DeleteLayerData(DeleteLayerDataRequest) returns (CommonActivityTestResponse);
  /**
   * 取消报名信息
   */
  rpc CancelRegistrationInfo(CancelRegistrationInfoRequest) returns (CommonActivityTestResponse);

  /**
   * 更新活动时间
   */
  rpc UpdateActivityInfo(UpdateActivityInfoRequest) returns (UpdateActivityInfoResponse);

  /**
   * 更新任务信息
   */
  rpc UpdateTaskInfo(UpdateTaskInfoRequest) returns (CommonActivityTestResponse);

  /**
   * 通过活动Id领取任务
   */
  rpc DrawActivityUnlimited (DrawActivityUnlimitedRequest) returns (CommonActivityTestResponse);
  /**
   * 新增测试账号
   */
  rpc SaveActivityTestUser(SaveActivityTestUserRequest) returns (CommonActivityTestResponse);
  /**
   * 删除测试账号
   */
  rpc RemoveActivityTestUser(RemoveActivityTestUserRequest) returns (CommonActivityTestResponse);
  /**
   * 更新用户任务状态
   */
  rpc UpdateUserTaskStatus(UpdateUserTaskStatusRequest) returns (CommonActivityTestResponse);
  /**
   * 清理用户奖励
   */
  rpc DeleteUserAwardRecordById(DeleteUserAwardRecordRequest) returns (CommonActivityTestResponse);
  /**
   * 更新用户活动状态
   */
  rpc UpdateUserActivityStatus(UpdateUserActivityStatusRequest) returns (CommonActivityTestResponse);
  /**
   * 策略后台mock上线
   */
  rpc TestOnlineStrategyAdminActivity(TestOnlineStrategyAdminActivityRequest) returns (TestOnlineStrategyAdminActivityResponse);
  /**
   * 查询活动上线进度
   */
  rpc ReportAdminActivityOnlineProgress(ReportAdminActivityOnlineProgressRequest) returns (ReportAdminActivityOnlineProgressResponse);
  /**
   * mock上线定时任务逻辑
   */
  rpc StagingMockScheduleOnline(StagingMockScheduleOnlineRequest) returns (StagingMockScheduleOnlineResponse);
  /**
   * 指标版本比对
   */
  rpc UserIndicatorVersionCheck(UserIndicatorVersionCheckRequest) returns (UserIndicatorVersionCheckResponse);
  /**
   * 数据清理
   */
  rpc HardDeleteCalcDetail(UserIndicatorCalcDetailDeleteRequest) returns (UserIndicatorCalcDetailDeleteResponse);
  /**
  * 针对指定商家删除老任务重新领取任务，但在删除老任务之前需要吧对应的指标进度缓存下来，后续更新进去
  */
  rpc ReDrawActivityAndRecoveryIndicatorProgress(ReDrawActivityAndRecoverIndicatorProgressRequest) returns (CommonActivityTestResponse);
  /**
   * 查询人群包信息
   */
  rpc QueryCrowdUser(QueryCrowdRequest) returns (CommonActivityTestResponse);

  /**
   * 查询人群包关联实验下人群数据
   */
  rpc QueryCrowdUserIdsByExperimentId(QueryAbCrowdRequest) returns (QueryAbCrowdResponse);

  rpc QueryUserHitExperimentIdsInCrowds(QueryAbExperimentRequest) returns (QueryAbExperimentResponse);
  /**
   * 创建裂变关联关系
   */
  rpc CreateFissionRelation (CreateFissionRelationRequest) returns (CommonActivityTestResponse);
  /**
   * 删除裂变关联关系
   */
  rpc DeleteFissionRelation (DeleteFissionRelationRequest) returns (CommonActivityTestResponse);
}