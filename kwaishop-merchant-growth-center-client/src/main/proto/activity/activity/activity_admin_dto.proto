syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.activity;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.activity";
option java_outer_classname = "ActivityDTOProto";

/**
业务信息
 */
message ActivityListDTO {
  /**
  业务总数
   */
  int32 total = 1;
  /**
  业务列表
   */
  repeated ActivityDTO list = 2;
}

/**
业务信息
 */
message ActivityDTO {
  /**
  业务ID
   */
  uint64 id = 1;
  /**
  名称
   */
  string name = 2;
  /**
  开始时间
   */
  uint64 start_time = 3;
  /**
  结束时间
   */
  uint64 end_time = 4;
  /**
  业务描述
   */
  string description = 5;
  /**
  creator
   */
  string creator = 6;
  /**
  modifier
   */
  string modifier = 7;
  /**
  状态
   */
  uint32 status = 8;
  /**
 * 活动类型
 */
  uint32 type = 9;
  /**
    * 活动玩法
    */
  string activity_pattern_type = 10;
  /*
   * 活动系列
   */
  uint64 series_type = 11;
  /**
    任务开始结束时间
   */
  repeated TaskTimeDTO task_times = 12;
}

/**
任务时间
 */
message TaskTimeDTO {
  /**
  开始时间
  */
  uint64 start_time = 1;
  /**
  结束时间
   */
  uint64 end_time = 2;
}

/**
 * 活动概览信息
 */
message ActivitySimpleInfo {
  /**
  业务ID
   */
  uint64 id = 1;
  /**
  名称
   */
  string name = 2;
}
