syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.activity;
import "activity/activity/activity_admin_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.activity";
option java_outer_classname = "ActivityAdminServiceProto";

message ListActivityRequest {
  /**
 * 操作人邮箱前缀
 */
  string operator = 1;
  /**
 分页查询-页号
 */
  int32 page_no = 2;
  /**
  分页查询-每页条数
   */
  int32 page_size = 3;
  /**
  活动类型
   */
  int32 type = 4;
  /**
  页面名称
   */
  string name = 5;
}

message ListActivityResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  ActivityListDTO data = 3;
}

message QueryActivityDetailRequest {
  /**
 * 操作人邮箱前缀
 */
  string operator = 1;
  /**
 * 业务ID
 */
  uint64 id = 2;
}

message QueryActivityDetailResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  ActivityDTO data = 3;
}

message UpdateActivityRequest {
  /**
 * 操作人邮箱前缀
 */
  string operator = 1;
  /**
 * 业务ID
 */
  uint64 id = 2;
  /**
名称
 */
  string name = 3;
  /**
  开始时间
   */
  uint64 start_time = 4;
  /**
  结束时间
   */
  uint64 end_time = 5;
  /**
  业务描述
   */
  string description = 6;
}

message UpdateActivityResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message CreateActivityRequest {
  /**
 * 操作人邮箱前缀
 */
  string operator = 1;
  /**
名称
 */
  string name = 2;
  /**
  开始时间
   */
  uint64 start_time = 3;
  /**
  结束时间
   */
  uint64 end_time = 4;
  /**
  业务描述
   */
  string description = 5;
  /**
  活动类型：1-商家策略，2-商家任务
   */
  int32 type = 6;
}

message CreateActivityResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message ListActivityWithConditionRequest {
  /**
  操作人邮箱前缀
  */
  string operator = 1;
  /**
  分页查询-页号
  如果不传，默认查询第一页
  */
  int32 page_no = 2;
  /**
  分页查询-每页条数
  如果不传默认查询十条，最大查询一页100条
   */
  int32 page_size = 3;
  /**
    业务ID
  */
  uint64 id = 4;
  /**
   状态
  */
  uint32 status = 5;
  /**
    名称
  */
  string name = 6;
  /**
   * id列表
   */
  repeated uint64 ids = 7;
}

message ListActivityWithConditionResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  ActivityListDTO data = 3;
}

message ListVerticalActivityRequest {
  /**
  操作人邮箱前缀
  */
  string operator = 1;
  /**
  活动类型
   */
  string type = 2;
}

message ListVerticalActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  repeated ActivitySimpleInfo data = 3;
}

message ActivityBaseCustomCheckEventMsg {
  /**
  活动ID
  */
  uint64 activity_id = 1;

  /**
    实体ID
   */
  string operator = 2;

  /**
   * 更新时间
   */
  uint64 update_time = 3;
}

message HiveImportCheckMsg {

  /**
   * Hive导入校验类型
   */
  uint32 entity_type = 1;
  /**
   * 数据库
   */
  string database = 2;

  /**
   * 表
   */
  string table = 3;

  /**
   * 人群分层条件
   */
  string crowd_condition = 4;

  /**
   * 快手Id列名
   */
  string seller_id_column_name = 5;

  /**
   * 操作人
   */
  string operator = 6;

  /**
   * 时间分区
   */
  string partition_condition = 7;

  /**
   * 唯一键
   */
  string event_id = 8;

  /**
   * 其他列名
   */
  repeated ExtraImportColumnDTO extra_columns = 9;

  /**
   * Hive查询总数
   */
  uint64 count = 10;
}



message ExtraImportColumnDTO {
  /**
   * 指标为指标id 奖励为awardType 基期为指标id
   */
  uint64 entity_id = 1;

  /**
   * 指标对应hive表列名
   */
  string column_name = 2;
}

message BatchDrawTaskWithParamReq {
  int64 activity_id = 1;
  repeated int64 task_id_list = 2;
  string source = 3;
  string draw_task_param = 4;
  int64 user_id = 5;
}

message BatchDrawTaskWithParamResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message ChangeRegistrationConfigRequest {
  int64 config_id = 1;
  string operator = 2;
  int64 effective_start_time = 3;
  int64 effective_end_time = 4;
  int32 status = 5;
}

message ChangeRegistrationConfigResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}
/**
业务域服务
 */
service ActivityAdminService {
  /**
  查询业务列表
   */
  rpc ListActivity (ListActivityRequest) returns (ListActivityResponse);
  /**
  查询业务详情
 */
  rpc QueryActivityDetail (QueryActivityDetailRequest) returns (QueryActivityDetailResponse);
  /**
  更新业务信息
  */
  rpc UpdateActivity (UpdateActivityRequest) returns (UpdateActivityResponse);
  /**
  创建业务信息
  */
  rpc CreateActivity (CreateActivityRequest) returns (CreateActivityResponse);

  /**
  多条件查询业务列表
   */
  rpc ListActivityWithCondition (ListActivityWithConditionRequest) returns
      (ListActivityWithConditionResponse);

  /**
  查询某种纵向类型活动
   */
  rpc ListVerticalActivity(ListVerticalActivityRequest) returns(ListVerticalActivityResponse);

  /**
  测试使用，KBUS 验证不是很方便
   */
  rpc TestOnline (QueryActivityDetailRequest) returns (QueryActivityDetailResponse);

  // 修改资格config（谨慎操作）
  rpc ChangeRegistrationConfig(ChangeRegistrationConfigRequest) returns(ChangeRegistrationConfigResponse);

  // 批量领取任务(for补偿有多选一奖励导致无法自动领取任务的情况)
  rpc BatchDrawTaskWithParam(BatchDrawTaskWithParamReq) returns (BatchDrawTaskWithParamResponse);
}