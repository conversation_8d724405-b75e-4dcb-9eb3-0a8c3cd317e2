syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.activity;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.activity";
option java_outer_classname = "ActivityUserServiceProto";

// 根据tag 进行报名
message ActivityTagSignUpRequest {
  // 用户id
  uint64 user_id = 1;
  // tag 标签
  string tag = 2;
  // 操作人
  string operator = 3;
  // 是否自动领取
  bool is_auto_draw = 4;
}

message ActivityTagBatchSignUpRequest {
  // 用户ID集合
  repeated uint64 user_id = 1;
  // tag标签
  repeated string tag = 2;
  // 操作人
  string operator = 3;
  // 是否自动领取
  bool is_auto_draw = 4;
  // excel链接
  string excel_url = 5;
}

// 查询当前用户有资格报名的活动
message QueryUserRegistrationActivityRequest {
  /**
    用户id
   */
  uint64 user_id = 1;
  /**
    是否过滤已经报名的活动
   */
  bool is_filter_draw = 2;
}

// 查询用户已经领取的活动
message QueryUserDrawActivityRequest {
  /**
   用户id
  */
  uint64 user_id = 1;
  /**
    标签
   */
  repeated string tags = 2;
  /**
    是否仅查询最新一条活动
   */
  bool is_filter_expired_activity = 3;
  /**
   是否过滤时间已经过了活动结束时间的活动
  */
  bool is_filter_end = 4;
}

message ActivityTagSignUpResponse {
  /**
 * 返回结果码
 */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message ActivityTagBatchSignUpResponse {
  /**
 * 返回结果码
 */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UserActivityResponse {
  /**
     返回结果码
   */
  uint32 result = 1;
  /**
    返回错误信息
   */
  string error_msg = 2;
  /**
    活动信息
   */
  repeated UserActivityDTO user_activity = 3;
}

message UserActivityDTO {
  /**
    活动名称
   */
  string name = 1;
  /**
     活动外部名称
   */
  string show_name = 2;
  /**
    活动描述
   */
  string description = 3;
  /**
    活动开始时间
   */
  uint64 start_time = 4;
  /**
    活动结束时间
   */
  uint64 end_time = 5;
  /**
    活动领取开始时间
   */
  uint64 draw_start_time = 6;
  /**
    活动领取结束时间
   */
  uint64 draw_end_time = 7;
  /**
    活动标签
   */
  string tags = 8;
  /**
    活动Id
   */
  uint64 activity_id = 9;

  /**
    活动下的任务
   */
  repeated UserTaskDTO user_task = 10;

  /**
   * 用户活动状态
   */
  uint32 status = 11;

  /**
   * 行动点信息
   */
  repeated TaskActionDTO task_action_dto = 12;
}

// 对应任务
message UserTaskDTO{
  /**
    任务名称
   */
  string name = 1;
  /**
     任务描述
   */
  string description = 2;
  /*
    任务状态，完成，未完成，
   */
  uint32 status = 3;
  /**
    任务 tags
   */
  repeated string tags = 4;
  /*
    动态信息
   */
  string dynamic_data = 5;
  /**
    任务开始时间
   */
  uint64 start_time = 6;
  /*
    任务结束
   */
  uint64 end_time = 7;
  /**
    当前任务对应的指标信息
   */
  repeated UserActivityIndicatorDTO user_activity_indicator = 8;
  /**
    当前任务对应的奖励信息
   */
  repeated UserActivityAwardDTO user_activity_award = 9;
  /*
    任务配置ID
   */
  uint64 task_id = 10;
  /**
    动态信息组件类型
   */
  string component_name = 11;
  /**
    父任务ID
   */
  uint64 parent_task_id = 12;
}

// 奖励信息
message UserActivityAwardDTO {
  /**
    奖励名称
   */
  string name = 1;
  /**
    奖励类型
   */
  uint32 award_type = 2;
  /**
    奖励金额
   */
  uint64 award_value = 3;
  /**
    奖励状态
   */
  uint32 award_status = 4;
  /**
    奖励过期时间
   */
  uint64 expire_time = 5;
}
// 指标信息
message UserActivityIndicatorDTO {
  /**
    指标名称
   */
  string name = 1;
  /**
    指标类型
   */
  uint32 indicator_type = 2;
  /**
    当前值
   */
  uint64 current_value = 3;
  /**
    目标值
   */
  uint64 target_value = 4;
  /**
   指标开始时间
  */
  uint64 start_time = 6;
  /*
    指标结束时间
   */
  uint64 end_time = 7;
  /*
    指标达成时间
  */
  uint64 complete_time = 8;
  /**
     指标完成状态
   */
  uint32 status = 9;
  /**
    指标单位
   */
  string unit = 10;
}

message GetPromotionSuggestionInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动id
   */
  uint64 activity_id = 2;
}

message ActivityUserResponse {
  /**
   * 返回结果码
   */
  uint32 result = 1;
  /**
   * 返回错误信息
   */
  string error_msg = 2;
  /**
   * 返回数据
   */
  string data = 3;
}

message GetPromotionSuggestionInfoResponse{
  /**
 * 返回结果码
 */
  uint32 result = 1;
  /**
   * 返回错误信息
   */
  string error_msg = 2;
  /**
   * 建议信息
   */
  GetPromotionSuggestionInfo data = 3;
}

message GetPromotionSuggestionInfo{
  /**
   * 任务名称
   */
  string name = 1;
  /**
   * 任务标题
   */
  string title = 2;
  /**
 * 是否展示特效
 */
  bool effects = 3;
  /**
   * 按钮信息
   */
  PromotionSuggestionButtonInfo button_info = 4;

}

message PromotionSuggestionButtonInfo{
  /**
   * 按钮文案
   */
  string text = 1;
  /**
   * PC链接
   */
  string pc_url = 2;
  /**
   * 商家端链接
   */
  string merchant_url = 3;
  /**
   * 主站链接
   */
  string main_url = 4;
}

message ActivityDistributeByTagRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动标签
   */
  string tag = 2;
  /**
   * 是否自动领取
   */
  bool auto_draw = 3;
  /**
   * 领取来源
   */
  string source = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message ActivityDistributeByTagResponse {
  /**
   * 结果码
   */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回体
   */
  UserActivityDTO data = 3;
}

message ActivityActionDTO {
  /**
   * 活动id
   */
  uint64 activity_id = 1;
  /**
   * 活动名称
   */
  string name = 2;
  /**
   * 活动类型
   */
  uint32 type = 3;
  /**
   * 活动描述
   */
  string desc = 4;
  /**
   * alias
   */
  string alias = 5;
  /**
   * 开始时间
   */
  uint64 start_time = 6;
  /**
   * 结束时间
   */
  uint64 end_time = 7;
  /**
   * 领取结束时间
   */
  uint64 draw_end_time = 8;
  /**
   * 活动状态
   */
  uint32 status = 9;
  /**
   * 任务行动点
   */
  repeated TaskActionDTO task = 10;
}

message TaskActionDTO {
  /**
   * 任务id
   */
  uint64 task_id = 1;
  /**
   * 副标题
   */
  string sub_title = 2;
  /**
   * 组件名称
   */
  string component_name = 3;
  /**
   * 按钮名称
   */
  string button_text = 4;
  /**
   * 按钮跳转链接
   */
  string button_jump_url = 5;
  /**
   * 按钮PC跳转链接
   */
  string button_pc_jump_url = 6;
  /**
   * 攻略跳转链接
   */
  string strategy_jump_url = 7;
  /**
   * 指标行动点
   */
  repeated IndicatorActionDTO target_info = 8;
}

message IndicatorActionDTO {
  /**
   * 指标id
   */
  uint64 indicator_id = 1;
  /**
   * 指标名称
   */
  string indicator_name = 2;
  /**
   * 主标题
   */
  string main_title = 3;
  /**
   * 副标题
   */
  string sub_title = 4;
  /**
     * 是否展示进度条
     */
  bool show_progress = 5;
  /**
   * 按钮文案
   */
  string button_text = 6;
  /**
   * 按钮跳转链接
   */
  string button_jump_url = 7;
  /**
   * 按钮PC跳转链接
   */
  string button_pc_jump_url = 8;
  /**
   * 攻略跳转链接
   */
  string strategy_jump_url = 9;
}

message CancelNewSellerActivityRequest {
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动tag
   */
  string tag = 2;
  /**
   * 操作人
   */
  string operator = 3;
  /**
   * 操作源
   */
  string source = 4;
}

message CancelUserActivityRequest{
  /**
  * 活动id
  */
  int64 activity_id = 1;
  /**
  * 用户id
  */
  int64 user_id = 2;
  /**
  * 操作源
  */
  string source = 4;
}

message CancelUserActivityResponse{
  /**
  * 返回结果码
  */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message GetUserCustomizePlanRequest {
  /**
   * 商家ID
   */
  uint64 user_id = 1;

  /**
   * 来源
   */
  string scene = 2;
}

message GetUserCustomizePlanResponse {
  /**
   * 返回结果码
   */
  uint32 result = 1;

  /**
   * 返回错误信息
   */
  string error_msg = 2;

  /**
   * 数据
   */
  repeated CustomizePlanDTO data = 3;
}

message CustomizePlanDTO {
  /**
   * 计划唯一Code
   */
  string plan_code = 1;

  /**
   * 计划关联的活动ID
   */
  repeated uint64 activity_id = 2;

  /**
   * 优先级
   */
  uint32 priority = 3;

  /**
   * PC端Banner
   */
  string banner_pc = 4;

  /**
   * 移动端banner
   */
  string banner_app = 5;

  /**
   * 图片
   */
  string pic_url = 6;

  /**
   * 计划标题
   */
  TextStyleDTO main_title = 7;

  /**
   * 计划开始时间
   */
  uint64 start_time = 8;

  /**
   * 计划结束时间
   */
  uint64 end_time = 9;

  /**
   * 活动简介
   */
  string desc = 10;

  /**
   * 活动报名结束时间
   */
  uint64 draw_end_time = 11;

  /**
   * 计划任务完成度
   */
  CompleteProgressDTO plan_progress = 12;

  /**
   * 计划状态 对应PlanStatusEnum
   */
  uint32 plan_status = 13;

  /**
   * 用户计划状态 对应userPlanStatusEnum
   */
  uint32 user_plan_status = 14;

  /**
   * 定制计划关联的活动
   */
  repeated CustomizeActivityDTO activity = 15;
}

message CustomizeActivityDTO {

  /**
   * 开店准备
   */
  string title = 1;

  /**
   * 活动ID
   */
  uint64 activity_id = 2;

  /**
   * 活动完成进度
   */
  CompleteProgressDTO activity_progress = 3;

  /**
   * 活动关联的任务
   */
  repeated CustomizeTaskDTO task = 4;

  /**
   * 用户活动状态
   */
  uint32 user_activity_status = 5;
}

message CustomizeTaskDTO {
  /**
   * 任务ID
   */
  uint64 task_id = 1;

  /**
   * 标题
   */
  string title = 2;

  /**
   * 指标
   */
  repeated CustomizeIndicatorDTO indicator = 3;

  /**
   * 奖励
   */
  repeated AwardShowDTO award = 4;

  /**
   * 用户任务状态
   */
  uint32 user_task_status = 5;

}

message CustomizeIndicatorDTO {

  /**
   * 指标ID
   */
  uint64 indicator_id = 1;

  /**
   * 是否展示进度
   */
  bool show_progress = 2;

  /**
   * 完成进度
   */
  CompleteProgressDTO indicator_progress = 3;

  /**
   * 攻略
   */
  string guide_url = 4;

  /**
   * 按钮
   */
  ButtonInfoDTO button = 5;

  /**
   * 用户指标状态
   */
  uint32 user_indicator_status = 6;

  /**
   * 指标单位
   */
  string unit = 7;
}

message TextStyleDTO {

  /**
   * 文案内容
   */
  string text = 1;
}

message ButtonInfoDTO {
  /**
   * 文本
   */
  string text = 1;
  /**
   * 跳转链接
   */
  string jump_url = 2;
  /**
   * PC跳转链接
   */
  string pc_jump_url = 3;
}

message BatchDrawUserActivityByTagsRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;

  /**
   * 活动标签
   */
  repeated string tag = 2;

  /**
   * 来源
   */
  string source = 3;
}

message BatchDrawUserActivityByTagsResponse {
  /**
  * 返回结果码
  */
  uint32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 成功活动ID
   */
  repeated uint64 success_activity_id = 3;
  /**
   * 失败活动ID
   */
  repeated uint64 fail_activity_id = 4;
}

/**
  业务域服务
 */
service ActivityUserService {
  /**
    根据tag进行报名最新活动
   */
  rpc DrawUserActivityByTag (ActivityTagSignUpRequest) returns (ActivityTagSignUpResponse);

  /**
    根据tag进行报名最新活动
   */
  rpc BatchDrawUserActivityByTag (ActivityTagBatchSignUpRequest) returns (ActivityTagBatchSignUpResponse);

  /**
   * 活动下发并返回活动信息
   */
  rpc ActivityDistributeByTag (ActivityDistributeByTagRequest) returns (ActivityDistributeByTagResponse);

  /**
    查询用户有资格报名的活动
   */
  rpc QueryUserRegistrationActivity (QueryUserRegistrationActivityRequest) returns (UserActivityResponse);

  /**
    查询用户已经领取的活动
   */
  rpc QueryUserDrawActivity (QueryUserDrawActivityRequest) returns (UserActivityResponse);

  /**
   * 获取大促推荐任务
   */
  rpc GetPromotionSuggestionInfo (GetPromotionSuggestionInfoRequest) returns (GetPromotionSuggestionInfoResponse);
  /**
   用户取消活动
   */
  rpc CancelUserActivity (CancelUserActivityRequest) returns (CancelUserActivityResponse);

  /**
   * 查询定制计划接口
   */
  rpc GetUserCustomizePlan (GetUserCustomizePlanRequest) returns (GetUserCustomizePlanResponse);

  /**
   * 根据tag进行报名
   */
  rpc BatchDrawUserActivityByTags(BatchDrawUserActivityByTagsRequest) returns (BatchDrawUserActivityByTagsResponse);
}