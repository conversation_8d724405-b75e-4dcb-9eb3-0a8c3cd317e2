syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.estimation;
import "activity/estimation/estimation_dto.proto";
option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.estimation";
option java_outer_classname = "EstimationTestServiceProto";


message TriggerDAPEstimationReq {
  int64 strategy_id = 1;

  string strategy_version = 2;

  int32 seller_cnt = 3;

  string p_date = 4;
}
message TriggerDAPEstimationResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message TestBitmapCacheReq {
  repeated int64 user_id = 1;

  int64 strategy_id = 2;

  string strategy_version = 3;

  int32 strategy_type = 4;

  bool only_query = 5;

  string biz_key = 6;
}

message TestBitmapCacheResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message RemoveEstimationPDateCacheReq {
  int64 strategy_id = 1;
}

message RemoveEstimationPDateCacheResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}
message ReCorrectEstimationStrategyReq {
  int64 strategy_id = 1;
}

message ReCorrectEstimationStrategyResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message EstimationFinishMsgReq {
  int64 out_strategy_id = 1;

  string out_version = 2;

  int32 strategy_type = 3;

  bool success = 4;

  string result_info = 5;

  string reason = 6;

  int64 group_id = 7;
}

message MockEstimationFinishMsgResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message ChangeOneStrategySnapshotsResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message TriggerAsyncStartReq {
  string operator = 1;

  /**
   * 测算策略id
   */
  int64 strategy_id = 2;

  /**
   * 测算策略版本
   */
  string strategy_version = 3;

  string link_type = 4;

  /**
   * pipelineCode
   */
  string pipeline_code = 5;

  int32 strategy_type = 6;

}

message TriggerAsyncStartResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message TriggerInspectorStartReq {
  string operator = 1;

  /**
   * 测算策略id
   */
  int64 strategy_id = 2;

  /**
   * 测算策略版本
   */
  string strategy_version = 3;


  string link_type = 4;

  /**
   * pipelineCode
   */
  string pipeline_code = 5;

  string step_code = 6;

  int32 strategy_type = 7;

  //  string inspect_code = 6;
}

message TriggerInspectorStartResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message ChangeConfigReq {
  int64 strategy_id = 1;
  string biz_date = 2;
  int32 period_num = 3;
  string basic_config = 6;
  int32 goal_type = 4;
  int32 status = 5;
  string ext = 7;
  int32 stage_num = 8;
  string indicator_config = 9;
}

message ChangeConfigResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message DeleteEstimationKvCacheReq {
  string biz_key = 1;
}

message DeleteEstimationKvCacheResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message DeleteEstimationGroupReq {
  string operator = 1;

  /**
   * 测算策略组id
   */
  int64 group_id = 2;

  /**
    * 只删除策略组
   */
  bool  delete_group_only = 3;
}

message ChangeGroupConfigReq {
  string operator = 1;

  /**
 * 测算策略组id
 */
  int64 group_id = 2;

  int32 status = 3;

  string biz_date = 4;

  string ext = 5;

  int32 stage_num = 6;

  string period_config = 7;

  string indicator_config = 8;

  int32 role_type = 9;
}

message DeleteEstimationGroupResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message QueryUserBaseIndicatorValueReq {
  string operator = 1;

  int64 strategy_id = 2;

  string version = 3;

  // 传入则只查询该指标
  int64 indicator_id = 4;

  int64 user_id = 5;
}

message QueryUserBaseIndicatorValueResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message AppendEstimationTriggerReq {
  // 定义日期字段
  string p_date = 1;

  // 定义活动 ID 列表字段
  repeated string activity_ids = 2;

  // 定义策略 ID 字段
  int64 strategy_id = 3;

  // 定义策略版本字段
  string strategy_version = 4;

  // 定义人群 ID 字段
  int64 crowd_id = 5;

  // 定义追加用户数量字段
  int64 append_user_cnt = 6;

  // 定义策略类型字段
  int32 strategy_type = 7;
}

message CancelOfflineInspectTaskReq {
  string operator = 1;

  // queryVo json字符串
  string cancel_param = 2;

  string job_id = 3;

  string job_instance_id = 4;

  string job_owner = 5;

  int32 job_owner_group_id = 6;

}

message RefreshEstimationStrategyCrowdCacheReq {
  string operator = 1;

  int64 strategy_id = 2;

  string strategy_version = 3;

  int32 strategy_type = 4;
}

message SendBasicInfoKafkaMsgReq {
  string operator = 1;

  repeated int64 user_id = 2;

  int64 strategy_id = 3;

  string strategy_version = 4;

  string biz_date = 5;

  int32 strategy_type = 6;

  bool send_prod = 7;
}

message GetUserCustomBaseCacheReq {
  int64 strategy_id = 1;
  string strategy_version = 2;
  int64 user_id = 3;
  int32 strategy_type = 4;
}

message GetUserCustomBaseCacheResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message EstimationBindActivityInfoReq {
  int64 activity_id = 1;
}

message ChangeGroupSnapshotsReq {
  int64 strategy_id = 1;
  string strategy_version = 2;
  int32 status = 3;
  string ext = 4;
  int64 event_end_time = 5;
  string indicator_rule = 6;
  string basic_config = 7;
}
message TriggerPeriodEstimationReq {
  int64 strategy_id = 1;
  string strategy_version = 2;
  int32 strategy_type = 3;
  bool  real_trigger = 4;
  string ds = 5;
  // 降级测试
  bool degrade_trigger = 6;
}
message TriggerPeriodEstimationResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message DeleteEstimationGroupPeriodNextConfigReq {
  string operator = 1;
  int64 group_id = 2;
}

message DeleteEstimateGroupSubStrategyUserDataReq {
  string operator = 1;
  int64 strategy_id = 2;
  string strategy_version = 3;
  int32 strategy_type = 4;
}

message SendCoverReportReq {
  int64 activity_id = 1;
  string operator = 2;
}

message SendCoverReportResponse{
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}
service EstimationTestService {

  // dap触发测算测试
  rpc TriggerDAPEstimationTest(TriggerDAPEstimationReq) returns (TriggerDAPEstimationResponse);
  // bitMap缓存测试
  rpc TestBitmapCache(TestBitmapCacheReq) returns (TestBitmapCacheResponse);
  // 删除测算pDate缓存
  rpc RemoveEstimationPDateCache(RemoveEstimationPDateCacheReq) returns (RemoveEstimationPDateCacheResponse);
  // 脚本矫正策略最新版本&状态
  rpc ReCorrectEstimationStrategy(ReCorrectEstimationStrategyReq) returns(ReCorrectEstimationStrategyResponse);
  // mock 测算完成消息
  rpc MockEstimationFinishMsg(EstimationFinishMsgReq) returns(MockEstimationFinishMsgResponse);
  // 谨慎操作:变更快照版本数据
  rpc ChangeOneStrategySnapshots(EstimationStrategySnapshotDTO) returns(ChangeOneStrategySnapshotsResponse);
  // 谨慎操作:触发异步流程
  rpc TriggerAsyncStart(TriggerAsyncStartReq) returns(TriggerAsyncStartResponse);
  // 手动触发巡检事件
  rpc TriggerInspectorStart(TriggerInspectorStartReq) returns(TriggerInspectorStartResponse);
  // 修改测算配置
  rpc ChangeConfig(ChangeConfigReq) returns(ChangeConfigResponse);
  // 删除测算缓存
  rpc DeleteEstimationKvCache(DeleteEstimationKvCacheReq) returns(DeleteEstimationKvCacheResponse);
  // for测试自动化
  rpc DeleteEstimationGroup(DeleteEstimationGroupReq) returns(DeleteEstimationGroupResponse);

  rpc QueryUserBaseIndicatorValue(QueryUserBaseIndicatorValueReq) returns(QueryUserBaseIndicatorValueResponse);

  // 修改测算组
  rpc ChangeGroupConfig(ChangeGroupConfigReq) returns(ChangeConfigResponse);
  // 追加测算触发mock
  rpc AppendEstimationTrigger(AppendEstimationTriggerReq) returns(ChangeConfigResponse);

  // 手动kill无用的离线任务
  rpc CancelOfflineInspectTask(CancelOfflineInspectTaskReq) returns(ChangeConfigResponse);
  // 刷新策略人群缓存
  rpc RefreshEstimationStrategyCrowdCache(RefreshEstimationStrategyCrowdCacheReq) returns(ChangeConfigResponse);
  // 发送测算用户基础信息kafka消息
  rpc SendUserBasicInfoMsgManual(SendBasicInfoKafkaMsgReq) returns(ChangeConfigResponse);
  // 获取某个用户测算的自定义基期缓存数据
  rpc GetUserCustomBaseCache(GetUserCustomBaseCacheReq) returns(GetUserCustomBaseCacheResponse);
  // 绑定活动信息
  rpc BindActivityInfo(EstimationBindActivityInfoReq) returns(ChangeConfigResponse);
  // 测试测算活动覆盖播报
  rpc SendActivityCoverReport(SendCoverReportReq) returns(SendCoverReportResponse);
  // changeGroupSnapshots
  rpc ChangeGroupSnapshots(ChangeGroupSnapshotsReq) returns(ChangeOneStrategySnapshotsResponse);
  // 周期测算手动触发
  rpc TriggerPeriodEstimation(TriggerPeriodEstimationReq) returns(TriggerPeriodEstimationResponse);
  // 手动删除对应策略组的后续版本&子策略版本（谨慎操作）
  rpc DeleteEstimationGroupPeriodNextConfig(DeleteEstimationGroupPeriodNextConfigReq) returns(TriggerPeriodEstimationResponse);
  // 手动删除对应策略组版本的子策略用户测算数据
  rpc DeleteEstimateGroupSubStrategyUserData(DeleteEstimateGroupSubStrategyUserDataReq) returns(TriggerPeriodEstimationResponse);
}