syntax = "proto3";


package kuaishou.kwaishop.merchant.growth.center.activity.estimation;

import "activity/baseindicator/base_indicator_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.estimation";
option java_outer_classname = "EstimationDTOProto";


message EstimationStrategySnapshotDTO {
  uint64 strategy_id = 1;

  string strategy_version = 2;

  uint64 out_strategy_id = 3;

  string out_strategy_version = 4;

  uint32 status = 5;

  uint32 sub_status = 6;

  string modifier = 7;

  string p_date = 8;

  string report_json = 9;

  string excel_url = 10;

  string ext = 11;

  int64 event_end_time = 12;

  string indicator_rule = 13;

  string basic_config = 14;

}

message EstimationGroupDTO{
  uint64 group_id = 1;

  string group_name = 2;

  int64 apply_role = 3;

  string creator = 4;

  int64 create_time = 5;

  repeated EstimationStrategyDTO strategy_list = 6;

  int32 goal_type = 7;
  // 衡量指标配置
  repeated MeasurementConfig measurement_config = 8;
  // 测算天数
  int32 strategy_period = 9;
  // 自动测算配置
  AutoEstimationConfig auto_estimation_config = 10;
  string single_roi_threshold = 11;
  string project_roi_threshold = 12;
  // 预算分配类型
  int32 enable_budget_allocation = 13;
  string settle_rate = 14;
  string tr_rate = 15;
  // 人群包配置
  EstimationCrowdConfig crowd_config = 16;
  // 匹配方式
  string match_type = 17;
  // 匹配id
  int32 match_model_id = 18;
  // 指标配置集合
  repeated EstimationInputIndicatorConfig indicator_configs = 19;
  // 奖励配置
  EstimationAwardConfig award_configs = 20;
  // 基期配置
  EstimationBasicConfig basic_config = 21;
  // 指标考核方式
  int32 indicator_assessment_type = 22;
  //  单任务最多指标数量
  int32 single_indicator_max_num = 23;

  // 阶梯数
  int32 stage_num = 24;

  // 最新版本
  string newly_version = 33;

  int32 status = 25;

  string status_desc = 26;

  // 策略组行动点
  repeated ActionBO actions = 27;

  string trace_key = 28;

  // 实验高级配置
  repeated ExpAdvancedConfig exp_advanced_configs = 29;

  string remark = 30;

  // 子状态
  SubStatusDetail sub_status = 31;

  // 策略时效性
  string timeliness = 32;

  // 自动测算类型集合
  repeated int32 auto_estimation_types = 34;

  int32 auto_estimate_flag = 42; // 0-一次性导入，1-按周期更新导入

  // 是否批量生成子策略
  bool batch_gene = 35;

  string group_version = 36;

  // 策略详情行动点
  repeated ActionBO detail_actions = 37;
  // 首次测算完成的人群包是否为空
  bool first_version_crowd_empty = 38;

  // 测算业务标签
  string biz_tag = 39;

  // 策略测算计算模式
  int32 estimate_calc_mode = 40;

  // 策略版本应用开始时间
  int64 estimate_apply_start_time = 41;

}

message MeasurementConfig {
  int64 index_id = 1;

  string index_name = 2;

  string index_value = 3;

  string unit = 4;
}

message AutoEstimationConfig {
  // 自动测算类型集合（@see AutoEstimationTypeEnum）
  repeated int32 auto_estimation_types = 1;
  // 周期测算配置
  PeriodEstimationConfig period_estimation_config = 2;
  // 实时测算配置
  RealtimeEstimationConfig realtime_estimation_config = 3;
}

message PeriodEstimationConfig {
  // 周期测算批数
  int32 cycle_batch_num = 1;
  // 周期循环天数
  repeated int32 cycle_day_list = 2;
  // 周期开始时间
  int64 cycle_start_time = 3;
  // 周期结束时间（子策略专属）
  int64 cycle_end_time = 4;
  // 周期index（子策略专属）
  int32 cycle_index = 5;
  // 策略周期天数
  int32 period_num = 6;

}

message RealtimeEstimationConfig {
  // 动态事件标签集合
  repeated string dynamic_event_tags = 1;

  // 事件结束时间
  //  int64 dynamic_event_end_time = 2;
}
message EstimationStrategyDTO {
  int64 strategy_id = 1;
  string version = 2;
  int64 group_id = 3;
  string group_version = 37;
  string strategy_name = 4;
  int32 strategy_period = 5;
  int32 strategy_period_type = 6;
  int64 measurement_idx = 7;
  string measurement_idx_value = 8;
  string measurement_idx_name = 9;
  int32 goal_type = 10;
  string single_roi_threshold = 11;
  string project_roi_threshold = 12;
  string settle_rate = 13;
  int32 stage_num = 14;
  EstimationCrowdConfig crowd_config = 15;
  repeated EstimationInputIndicatorConfig indicator_configs = 16;
  EstimationAwardConfig award_configs = 17;
  EstimationBasicConfig basic_config = 18;
  string creator = 19;
  int64 create_time = 20;
  int64 update_time = 21;
  int32 status = 22;
  string status_desc = 23;
  repeated ActionBO actions = 24;
  string tr_rate = 25;
  SubStatusDetail sub_status = 26;
  string strategy_goal = 27;
  string measurement_idx_unit = 28;
  string estimation_res_url = 29;
  string trace_key = 30 ;
  // 自动测算配置
  AutoEstimationConfig auto_estimation_config = 31;
  // 是否是批量测算来源的策略
  bool batch_source = 32;
  // 实验高级配置
  repeated ExpAdvancedConfig exp_advanced_configs = 33;

  string remark = 34;
  // 周期测算返回：策略版本应用开始时间
  int64 apply_start_time = 35;
  // 周期测算返回：策略版本应用结束时间
  int64 apply_end_time = 36;

  // 基期数据下发方式
  string basic_fetch_mode = 38;

  // 自动测算标识
  int32 auto_estimate_flag = 39;

  // 周期批次号
  int32 period_index = 40;

  // 策略详情行动点
  repeated ActionBO detail_actions = 41;

  // 测算业务标签
  string biz_tag = 42;

  // 策略测算计算模式
  int32 estimate_calc_mode = 43;

  // 策略版本应用开始时间
  int64 estimate_apply_start_time = 44;
}

message ExpAdvancedConfig {
  string param_key = 1;
  int32 param_type = 2;
  string param_value = 3;
}

message SubStatusDetail{
  int32 sub_status = 1;
  string sub_status_desc = 2;
  string fail_reason = 3;
}

message ActionBO{
  string label = 1;
  string code = 2;
  bool disable = 3;
}

message EstimationStrategyReportDTO{
  RoiAnalysisModule roi_analysis_module = 1;
  repeated IndicatorAnalysisModule indicator_analysis_module = 2;
  repeated AwardAnalysisModule award_analysis_module = 3;
}

message RoiAnalysisModule {
  string predict_roi = 1;
  string predict_goal_increment_desc = 2;
  string predict_goal_increment_value = 3;
  string cost_desc = 4;
  int64 diagnose_time = 5;
  repeated PredictFinishRoi predict_finish_list = 6;
  // 分销返现成本(元)
  string predict_distribution_investment = 7;
  // 分销返现ROI
  string predict_roi_distribution = 8;
  // 预估直播流量成本（曝光）
  string predict_live_investment_exposure = 9;
  // 预估短视频流量成本（曝光）
  string predict_photo_investment_exposure = 10;
  // 现金成本（元）
  string cash_investment = 11;
}

message PredictFinishRoi {
  string ratio = 1;
  string predict_goal_increment_value = 2;
  string cost_desc = 3;
  string roi = 4;
  // 分销返现成本(元)
  string predict_distribution_investment = 7;
  // 分销返现ROI
  string predict_roi_distribution = 8;
  // 预估直播流量成本（曝光）
  string predict_live_investment_exposure = 9;
  // 预估短视频流量成本（曝光）
  string predict_photo_investment_exposure = 10;
  // 现金成本（元）
  string cash_investment = 11;
}

message IndicatorAnalysisModule {
  string name = 1;
  int64 indicator_id = 2;
  repeated StageAnalysisModule data = 3;
  string indicator_target_type = 4;
  string unit = 5;

}

message AwardAnalysisModule{
  string name = 1;
  int32 award_type = 2;
  int64 specify_indicator_id = 3;
  repeated StageAnalysisModule data = 4;
  string specify_indicator_name = 5;
  string award_target_type = 6;
  string unit = 7;

}

message StageAnalysisModule {
  string crowd_name = 1;
  int64 crowd_num = 2;
  repeated StageAnalysisMeta stage_list = 3;
}

message StageAnalysisMeta{
  string name = 1;
  string difficult_degree = 2;
  string min = 3;
  string quantile_25 = 4;
  string quantile_50 = 5;
  string quantile_75 = 6;
  string max = 7;
}


message EstimationHiveImportConfig {
  string database = 1;
  string table = 2;
  string crowd_condition = 3; // 人群分层信息
  string seller_id_column_name = 4;
  string partition_condition = 5;
  repeated EstimationHiveExtraColumn extra_column_list = 6;
}

message  EstimationHiveExtraColumn {
  int64 entity_id = 1;
  string column_name = 2;
  string unit = 3;
}


message EstimationInputIndicatorConfig {
  int64 indicator_id = 1;
  string indicator_name = 2;
  string value_min = 3;
  string value_max = 4;
  string unit = 5;
  int32 modify_type = 6;
  string modify_value = 7;
  int32 order = 8;
  repeated string indicator_tags = 9;
  bool required = 10;
  // 指标约束值
  string restriction_value = 11;
  // 指标约束值单位
  string restriction_unit = 12;
  // 类目信息
  repeated string category_list = 13;
  // 商品包ID
  int64 item_group_id = 14;
  // 不需要基期
  bool without_basic = 15;
}

message EstimationSpecifyIndicatorInfo {
  int64 indicator_id = 1;
  string indicator_name = 2;
  int32 return_type = 3;
  int32 modify_type = 4;
  string modify_value = 5;
  int32 order = 6;
}

message EstimationAwardConfig {
  int32 award_condition = 1;

  repeated EstimationSingleAwardConfig single_award_config = 2;

}

message EstimationSingleAwardConfig {
  int32 award_type = 1;
  string award_name = 2;
  string target_type = 3;
  // 奖励发放方式(可选)
  int32 award_send_type = 12;

  // 奖励延迟时间方式（可选）
  int32 award_delay_time_type = 13;

  // 奖励延迟时间
  int64 award_delay_time = 14;

  // 过期时间类型
  int32 expire_time_type = 15;

  // 相对过期天数
  int64 expire_days = 16;

  // 奖励过期时间
  int64 expire_time = 17;

  // 奖励是否过风控
  int32 award_risk_type = 18;

  string min_award_value = 4;
  string max_award_value = 5;
  string unit = 6;
  repeated EstimationSpecifyIndicatorInfo specify_indicator_infos = 7;
  int32 modify_type = 8;
  string modify_value = 9;

  int32 user_upper_enable = 10;
  // 千人千面的奖励上限最小值
  int64 min_user_upper = 11;
  // 虚拟奖励描述
  string virtual_award_desc = 19;
}

message EstimationBasicConfig {
  repeated int64 base_indicator_list = 1;
  // baseIndicatorList为空时，传入，后续校验强依赖
  bool without_basic = 10;
  string indicator_time_type = 2;
  int64 fixed_start_time = 3;
  int64 fixed_end_time = 4;
  string base_algorithm = 5;
  string base_algorithm_customize_url = 6;
  int32 customize_type = 7;
  EstimationHiveImportConfig hive_import_config = 8;
  // 基期2.0新模型
  repeated SingleBasicConfig basic_indicator_config = 9;
}

message EstimationCrowdConfig {
  int32 crowd_type = 1;
  int64 crowd_id = 2;
  // 人群包新版ID
  string crowd_code = 10;
  string crowd_file_name = 3;
  string crowd_file_url = 4;
  EstimationHiveImportConfig hive_import_config = 5;
  int64 ab_experiment_id = 6;
  string crowd_file_import_type = 7;
  // 测算人群动态追加
  int32 dynamic_append_crowd_type = 8;
  // 开启AB分流后的人群是否全量测算
  bool full_estimate = 9;
}

message StrategyEstimationFinishMsg {
  int64 strategy_id = 1;
  string strategy_version = 2;
  int32 strategy_type = 3; // 策略组/策略
  // 测算完成类型：普通测算、批量测算、追加测算、周期测算
  int32 source = 4;
  string p_date = 5;

}

message RealtimeGroupDimEstimateMsg {
  int64 group_id = 1;
  string group_version = 2;
  repeated RealtimeStrategyDimEstimateMsg strategy_list = 3;
  int64 user_id = 4;
  int64 event_time = 5;
  string event_tag = 6;
}

message PeriodRealtimeGroupDimFixCrowdMsg {
  int64 group_id = 1;
  string group_version = 2;
  int64 seller_cnt = 3;
}

message RealtimeStrategyDimEstimateMsg {
  int64 strategy_id = 1;
  string strategy_version = 2;
}

message EstimationGroupBizTag {
  string name =1;
  string code = 2;
}