syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.estimation;
import "activity/estimation/estimation_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.estimation";
option java_outer_classname = "EstimationAdminServiceProto";

message EstimationAdminCommonResponse{

  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}


message QueryEstimationStrategyGroupListRequest{
  /**
 * 测算组名称
 */
  string name = 1;
  /**
   * 测算组创建人
   */
  string creator = 2;
  /**
   * 测算组id
   */
  int64 group_id = 3;

  string version = 8;

  // 应用对象
  int32 role_type = 4;
  /**
   * 第几页
   */
  int32 page_no = 5;
  /**
   * 每页数据条数
   */
  int32 page_size = 6;

  // 操作人
  string operator = 7;

  // 业务专项
  string biz_tag = 9;
}

message QueryEstimationStrategyIndicatorRequest{
  // 操作人
  string operator = 1;
  //  标签
  repeated string tag = 2;

  int32 scene = 3;

  string application = 4;
}

message EstimationStrategyGroupSaveRequest {

  string strategy_group_name = 1;

  int32 role_type = 2;

  string operator = 3;

  int64 group_id = 4;

  // 自动测算类型集合（@see AutoEstimationTypeEnum）
  repeated int32 auto_estimation_types = 5;
  // 是否批量生成子策略
  bool batch_gene = 6;

  // 业务专项标签
  string biz_tag = 7;

  // 测算模式
  int32 estimate_calc_mode = 8;

  int32 auto_estimate_flag = 9; // 0-一次性导入，1-按周期更新导入
}

message EstimationStrategyGroupModifyRequest {
  /**
  操作人
   */
  string operator = 1;
  /**
    操作类型：1:草稿/2:提交
 */
  int32 save_type = 2;
  /**
  测算组配置
   */
  EstimationGroupDTO group_config = 3;
}

message EstimationStrategySaveRequest {
  /**
  操作人
   */
  string operator = 1;
  /**
  策略
  */
  EstimationStrategyDTO strategy_config = 2;
  /**
  操作类型
   */
  int32 save_type = 3;

}

message EstimationStrategyGeneFromBatchRequest {
  /**
  策略
  */
  EstimationStrategyDTO strategy_config = 1;

  int64 out_strategy_id = 2;

  string out_strategy_version = 3;

  int64 sync_crowd_num = 4;

  string biz_date = 5;

  string operator = 6;
}

message QueryEstimationStrategyDetailRequest{
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;
  // 版本号
  string strategy_version = 3;

  string scene = 4;
  // 状态
  int32 status = 5;
}

message QueryEstimationStrategyReportRequest{
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;
  // 版本号
  string strategy_version = 3;
}

message QueryEstimationStrategyVersionListRequest{
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;

  // 策略类型
  uint32 strategy_type = 3;

  string status = 4;
}

message CalcEstimationStrategyRequest{
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;
}

message IndicatorAdjustConfig{
  int64 indicator_id = 1;
  ModifyConfig modify_config = 2;
}

message ModifyConfig{
  int32 modify_type = 2;
  string modify_value = 3;
}

message AwardAdjustConfig{
  int32 award_type = 1;
  ModifyConfig modify_config = 2;
  int64 specify_indicator_id = 3;
}

message AdjustEstimationStrategyRequest{
  // 操作人
  string operator = 1;
  // 策略调整指标参数
  repeated IndicatorAdjustConfig indicator_config = 2;
  // 策略调整奖励参数
  repeated AwardAdjustConfig award_config = 3;
  // 策略id
  uint64 strategy_id = 4;

  // 版本
  string version = 5;
}

message ExportEstimationStrategyDataRequest {
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;
  // 版本号
  string strategy_version = 3;
  // 1:测算报告 2：用户测算指标 3:用户测算奖励
  int32 export_type = 4;
}

message QueryEstimationStrategyModelListRequest {
  // 操作人
  string operator = 1;

  // 测算模型类型
  int32 strategy_model_type = 2;

}

message QueryEstimationStrategyGroupDetailReq {
  // 策略组id
  int64 group_id = 1;
  // 操作人
  string operator = 2;

  // 场景strategy/task（活动配置）
  string scene = 3;
  // 策略组状态
  int32 status = 4;
  // 版本
  string version = 5;

}

message ManualConfirmStrategyValidRequest {
  // 操作人
  string operator = 1;
  // 策略id
  uint64 strategy_id = 2;
  // 版本号
  string strategy_version = 3;
  uint32 strategy_type = 4;
  // 是否通过
  string confirm_code = 5;
}

message QueryEstimationGroupBizTagRequest {
  // 应用对象
  uint32 role_type = 1;
}

message QueryEstimationGroupBizTagResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  repeated EstimationGroupBizTag data = 3;
}

/**
业务域服务
 */
service EstimationAdminService {
  /**
  查询策略组列表
  */
  rpc QueryEstimationStrategyGroupList (QueryEstimationStrategyGroupListRequest) returns (EstimationAdminCommonResponse);
  /**
  查询策略明细
  */
  rpc QueryEstimationStrategyDetail (QueryEstimationStrategyDetailRequest) returns (EstimationAdminCommonResponse);
  /**
  查询策略报告
  */
  rpc QueryEstimationStrategyReport (QueryEstimationStrategyReportRequest) returns (EstimationAdminCommonResponse);
  /**
  查询策略可关联指标
  */
  rpc QueryEstimationIndicatorList (QueryEstimationStrategyIndicatorRequest) returns (EstimationAdminCommonResponse);
  /**
  查询策略版本号列表
  */
  rpc QueryEstimationVersionList (QueryEstimationStrategyVersionListRequest) returns (EstimationAdminCommonResponse);
  /**
  创建/保存策略
  */
  rpc SaveEstimationStrategy (EstimationStrategySaveRequest) returns (EstimationAdminCommonResponse);
  /**
  创建/保存策略组
  */
  rpc SaveEstimationStrategyGroup (EstimationStrategyGroupSaveRequest) returns (EstimationAdminCommonResponse);
  /**
    变更策略组（for批量测算场景）
  */
  rpc ModifyEstimationStrategyGroup (EstimationStrategyGroupModifyRequest) returns (EstimationAdminCommonResponse);
  /**
    查询测算组详情
  */
  rpc QueryEstimationStrategyGroupDetail (QueryEstimationStrategyGroupDetailReq) returns (EstimationAdminCommonResponse);
  /**
    查询测算模型集合
  */
  rpc QueryEstimationStrategyModelList (QueryEstimationStrategyModelListRequest) returns (EstimationAdminCommonResponse);
  /**
  调整策略
  */
  rpc AdjustEstimationStrategy (AdjustEstimationStrategyRequest) returns (EstimationAdminCommonResponse);
  /**
  测算策略
  */
  rpc CalcEstimationStrategy (CalcEstimationStrategyRequest) returns (EstimationAdminCommonResponse);
  /**
   导出测算数据
   */
  rpc ExportEstimationStrategyData (ExportEstimationStrategyDataRequest) returns (EstimationAdminCommonResponse);
  /**
   机审异常策略人工确认
   */
  rpc ManualConfirmStrategyValid (ManualConfirmStrategyValidRequest) returns (EstimationAdminCommonResponse);
  /**
  根据应用对象查询业务专项标签
   */
  rpc QueryEstimationGroupBizTag (QueryEstimationGroupBizTagRequest) returns
      (QueryEstimationGroupBizTagResponse);
}