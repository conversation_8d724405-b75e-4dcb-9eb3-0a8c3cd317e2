syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.estimation;
import "activity/estimation/estimation_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.estimation";
option java_outer_classname = "EstimationDeriveServiceProto";

message RealtimeEstimationTriggerRequest {
  int64 user_id = 1;

  string event_tag = 2;

  int64 event_time = 3;
}

message RealtimeEstimationTriggerResponse {
  int32 result = 1;
  string error_msg = 2;
  // 返回信息
  string data = 3;
}
service EstimationDeriveService {
  // 用户实时测算触发
  rpc RealtimeEstimationTrigger(RealtimeEstimationTriggerRequest) returns (RealtimeEstimationTriggerResponse);
}