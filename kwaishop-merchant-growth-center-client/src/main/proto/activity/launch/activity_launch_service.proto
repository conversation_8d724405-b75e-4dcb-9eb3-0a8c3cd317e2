syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.launch;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch";
option java_outer_classname = "ActivityLaunchServiceProto";


message QueryLaunchInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 渠道
   */
  string channel = 2;
  /**
   * 场景
   */
  string scene = 3;
  /**
   * 投放过滤条件
   */
  LaunchFilterParamDTO filter_param = 4;

  /**
   * 组装条件参数
   */
  LaunchAssembleParamDTO assemble_param = 5;
}

message LaunchAssembleParamDTO {

  /**
   * 是否不组装字段数据
   */
  bool not_assemble_field_data = 1;
}

message LaunchFilterParamDTO {
  /**
   * 用户实体状态
   */
  repeated int32 user_entity_status_list = 1;
  /**
   * 实体奖励类型列表
   */
  repeated int32 entity_award_type_list = 2;
  /**
   * 实体标签列表
   */
  repeated string entity_tag_list = 3;
}

message QueryLaunchInfoResponse {
  /**
   * 返回值
   */
  int32 result = 1;
  /**
   * 异常信息
   */
  string error_msg = 2;
  /**
   * 返回数据
   */
  LaunchInfoDataDTO data = 3;
}

message LaunchInfoDataDTO {
  /**
   * 投放数据
   */
  repeated LaunchInfoDTO launch_info = 1;
}

message LaunchInfoDTO {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 活动名称
   */
  string activity_name = 2;
  /**
   * 活动描述
   */
  string activity_desc = 3;
  /**
   * 用户活动状态
   */
  int32 user_activity_status = 4;
  /**
   * 投放展示信息（序列化）
   */
  string launch_content_info = 5;
  /**
   * 实体投放信息
   */
  repeated LaunchEntityInfoDTO entity_info = 6;
  /**
   * 拓展信息
   */
  string ext = 7;
  /**
   * 展示标签
   */
  string show_tag = 8;
  /**
   * 优先级
   */
  int32 priority = 9;
  /**
   * 展示标签名称
   */
  string show_tag_name = 10;

  /**
   * 展示配置
   */
  string show_config = 11;

  /**
   * 活动时间
   */
  LaunchActivityTimeInfoDTO activity_time_info = 12;
}

message LaunchActivityTimeInfoDTO {
  /**
   * 开始时间
   */
  uint64 start_time = 1;
  /**
   * 结束时间
   */
  uint64 end_time = 2;
  /**
   * 领取结束时间
   */
  uint64 draw_end_time = 3;
  /**
   * 展示截止时间
   */
  uint64 show_end_time = 4;
}

message LaunchEntityInfoDTO {
  /**
   * 实体类型
   */
  uint32 entity_type = 1;
  /**
   * 实体ID
   */
  uint64 entity_id = 2;
  /**
   * 实体投放内容
   */
  string entity_launch_content_info = 3;
}

service ActivityLaunchService {

  // 查询投放信息
  rpc QueryLaunchInfo (QueryLaunchInfoRequest) returns (QueryLaunchInfoResponse);
}