syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.offline";
option java_outer_classname = "ActivityDeOfflineDataTestServiceProto";

message SaveStrategyTotalAwardRequest {
  // 策略的主键ID
  uint64 strategy_id = 1;
  // 奖励配置ID
  uint64 award_config_id = 2;
  // 奖励金额
  uint64 award_value = 3;
  // 当前的操作人
  string dt = 8;
}
message SaveStrategyTotalAwardResponse {
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}

message DeleteStrategyTotalAwardRequest {
  uint64 strategy_id = 1;
  uint64 award_config_id = 2;
  string dt = 3;
}
message DeleteStrategyTotalAwardResponse {
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}

message SaveTableReadyDataRequest {
  // 表名
  string table_name = 1;
  // 就绪时间
  string dt = 2;
}
message SaveTableReadyDataResponse {
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}

message DeleteTableReadyDataRequest {
  string table_name = 1;
  string dt = 2;
}
message DeleteTableReadyDataResponse {
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}

message MerchantImportStrategyAwardDataDTO {
  /**
  商家ID
   */
  int64 seller_id = 1;
  /**
  策略ID
   */
  int64 strategy_id = 2;
  /**
  奖励配置ID
   */
  int64 award_config_id = 3;
  /**
  奖励值
   */
  int64 award_value = 4;
  /**
  dt
   */
  string dt = 5;
  /**
  sync_flag
   */
  int32 sync_flag = 6;
}

message MerchantImportStrategyIndicatorDataDTO {
  /**
  商家ID
   */
  int64 seller_id = 1;
  /**
  策略ID
   */
  int64 strategy_id = 2;
  /**
  奖励配置ID
   */
  int64 indicator_config_id = 3;
  /**
  奖励值
   */
  int64 indicator_value = 4;
  /**
  dt
   */
  string dt = 5;
  /**
  sync_flag
   */
  int32 sync_flag = 6;
  /**
  指标记录
   */
  string indicator_data = 7;
  /**
  是否写入异常数据
   */
  bool is_unusual_data = 8;
}

message CreateMerchantImportStrategyAwardRequest {
  int64 id = 1;
  MerchantImportStrategyAwardDataDTO award_data = 2;
}

message CreateMerchantImportStrategyAwardResponse {
  int32 result = 1;
  string error_msg = 2;
}

message UpdateMerchantImportStrategyAwardRequest {
  int64 id = 1;
  MerchantImportStrategyAwardDataDTO award_data = 2;
}

message UpdateMerchantImportStrategyAwardResponse {
  int32 result = 1;
  string error_msg = 2;
}

message CreateMerchantImportStrategyIndicatorRequest {
  int64 id = 1;
  MerchantImportStrategyIndicatorDataDTO award_data = 2;
}

message CreateMerchantImportStrategyIndicatorResponse {
  int32 result = 1;
  string error_msg = 2;
}

message UpdateMerchantImportStrategyIndicatorRequest {
  int64 id = 1;
  MerchantImportStrategyIndicatorDataDTO award_data = 2;
}

message UpdateMerchantImportStrategyIndicatorResponse {
  int32 result = 1;
  string error_msg = 2;
}

message SaveStrategyTotalAwardConfigRequest{
  // 策略的主键ID
  uint64 strategy_id = 1;
  // 奖励配置ID
  uint64 award_config_id = 2;
  // 奖励金额
  uint64 award_value = 3;
  // dt时间
  string dt = 4;
}

message SaveStrategyTotalAwardConfigResponse{
  int32 result = 1;
  string error_msg = 2;
}

/**
DE离线导出数据测试服务
 */
service KwaishopMerchantGrowthDeOfflineDataTestService {
  // 插入策略总奖励
  rpc SaveStrategyTotalAward (SaveStrategyTotalAwardRequest) returns (SaveStrategyTotalAwardResponse);
  // 删除策略总奖励数据
  rpc DeleteStrategyTotalAward (DeleteStrategyTotalAwardRequest) returns (DeleteStrategyTotalAwardResponse);
  // 保存导出表就绪数据
  rpc SaveTableReadyData (SaveTableReadyDataRequest) returns (SaveTableReadyDataResponse);
  // 删除导出表就绪数据
  rpc DeleteTableReadyData (DeleteTableReadyDataRequest) returns (DeleteTableReadyDataResponse);
  /**
merchant_import表，插入指标数据
*/
  rpc CreateMerchantImportStrategyIndicator (CreateMerchantImportStrategyIndicatorRequest) returns (CreateMerchantImportStrategyIndicatorResponse);
  /**
  merchant_import表，更新指标数据
  */
  rpc UpdateMerchantImportStrategyIndicator (UpdateMerchantImportStrategyIndicatorRequest) returns (UpdateMerchantImportStrategyIndicatorResponse);
  /**
  merchant_import表，插入奖励数据
   */
  rpc CreateMerchantImportStrategyAward (CreateMerchantImportStrategyAwardRequest) returns (CreateMerchantImportStrategyAwardResponse);
  /**
  merchant_import表，更新奖励数据
  */
  rpc UpdateMerchantImportStrategyAward (UpdateMerchantImportStrategyAwardRequest) returns (UpdateMerchantImportStrategyAwardResponse);
  /**
   保存策略总奖励配置
   */
  rpc SaveStrategyTotalAwardConfig(SaveStrategyTotalAwardConfigRequest) returns(SaveStrategyTotalAwardConfigResponse);
}