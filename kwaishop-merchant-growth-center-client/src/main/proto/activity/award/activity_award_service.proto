syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.award;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.award";
option java_outer_classname = "ActivityAwardServiceProto";

message GetTaskAwardConfigRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 2;
}

message GetTaskAwardConfigResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励配置对象
   */
  AwardConfigInfoDTO data = 3;
}


message GetUserTaskAwardConfigRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 3;
  /**
   * 用户ID
   */
  uint64 user_id = 4;
}

message GetUserTaskAwardConfigResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励配置对象
   */
  AwardConfigInfoDTO data = 3;
}

message AwardConfigInfoDTO {
  /**
   * 奖励配置信息
   */
  repeated ActivityAwardConfigDTO config = 1;
}

message ActivityAwardConfigDTO {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  uint64 task_id = 2;
  /**
   * 奖励类型
   */
  uint32 award_type = 3;
  /**
   * 奖励数值
   */
  uint64 award_count = 4;
  /**
   * 发送规则
   */
  string send_rule = 5;
  /**
   * 配置ID
   */
  uint64 config_id = 6;
  /**
   * 权益配置ID
   */
  uint64 interest_config_id = 7;
  /**
   * 过期时间
   */
  uint64 expire_time = 8;
  /**
   * 最高奖励
   */
  uint64 max_award_count = 9;
  /**
   * 奖励名称
   */
  string award_name = 10;
  /**
   * 最低返点奖励
   */
  uint64 min_return_award_count = 11;
  /**
   * 附加信息
   */
  string ext = 12;
}

message GetUserActivityAwardRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
}

message GetUserActivityAwardResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励记录对象
   */
  repeated AwardRecordInfoDTO data = 3;
}

message AwardRecordInfoDTO {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 发奖数额
   */
  uint64 award_count = 2;
  /**
   * 发奖时间
   */
  uint64 award_time = 3;
  /**
   * 奖励类型
   */
  uint32 award_type = 4;
  /**
   * 奖励名称
   */
  string award_name = 5;
  /**
   * 活动ID
   */
  uint64 activity_id = 6;
  /**
   * 任务ID
   */
  uint64 task_id = 7;
  /**
   * 过期时间
   */
  uint64 expire_time = 8;
  /**
   * 奖励状态
   */
  uint32 status = 9;
  /**
   * 更新时间
   */
  uint64 update_time = 10;
  /**
   * 奖励配置ID
   */
  uint64 config_id = 11;
  /**
   * 额外参数
   */
  string ext = 12;
  /**
   * 唯一ID
   */
  string subject_id = 13;
}

message GetUserAllActivityAwardRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
}

message GetUserAllActivityAwardResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励记录对象
   */
  repeated AwardRecordInfoDTO data = 3;
}

message BatchGetUserRecordByBizIdRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 用户ID
   */
  uint64 user_id = 2;
  /**
   * bizId
   */
  repeated string biz_id = 3;
}

message BatchGetUserRecordByBizIdResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励记录对象
   */
  repeated AwardRecordInfoDTO data = 3;
}

message TriggerHoldAwardSendRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message TriggerHoldAwardSendResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UpdateAwardConfigRequest {
  /**
   * 配置ID
   */
  uint64 id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 实体ID
   */
  uint64 entity_id = 3;
  /**
  权益扩展参数
   */
  string interest_ext_param = 4;
  /**
  权益包ID
   */
  uint64 interest_package_id = 5;
  /**
  权益配置ID
   */
  uint64 interest_config_id = 6;
  /**
   * 操作人
   */
  string operator = 7;
  /**
   * 奖励最大值
   */
  uint64 max_award_value = 8;
}

message UpdateAwardConfigResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message GetBroadcastUserAwardRequest {
  /**
   * 活动id
   */
  uint64 activity_id = 1;
  /**
   * 用户id
   */
  uint64 user_id = 2;
}

message GetBroadcastUserAwardResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 促开播奖励值
   */
  uint64 award = 3;
}

message ExportBroadcastAwardListRequest {
  /**
   * 活动id
   */
  uint64 activity_id = 1;
  /**
   * 人群包
   */
  uint64 crowd_id = 2;
}

message ExportBroadcastAwardListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 导出文件url
   */
  string excel_url = 3;
}

message BatchCancelHoldAwardSendRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 操作人
   */
  string operator = 2;
  /**
   * 取消表格
   */
  string excel_url = 3;
  /**
   * 活动ID
   */
  uint64 activity_id = 4;
  /**
   * 限定任务ID列表
   */
  repeated uint64 task_id = 5;
  /**
   * 是否全活动取消
   */
  bool cancel_activity_award = 6;
}

message BatchCancelHoldAwardSendResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}
message LotteryRequest{
  /**
    用户ID
   */
  uint64 user_id = 1;
  /**
    活动ID
   */
  string activity_id = 2;
  /**
    任务ID
   */
  string task_id = 3;
}
message LotteryResponse{
  /**
   * 返回结果码
   */
  int64 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 抽奖动作完成结果
   */
  string data = 3;
}
message QueryLotteryRequest{
  /**
  商家ID
   */
  uint64 user_id = 1;
  string activity_id = 2;
  string task_id = 3;
}
message QueryLotteryResponse{
  /**
   * 返回结果码
   */
  int64 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 今日是否已经抽奖
   */
  bool data = 3;
}

message QueryUserActivityAwardShowInfoRequest {

  /**
   * 用户ID
   */
  uint64 user_id = 1;

  /**
   * 活动ID
   */
  uint64 activity_id = 2;
}

message QueryUserActivityAwardShowInfoResponse {

  /**
   * 返回结果码
   */
  uint32 result = 1;

  /**
   * 错误信息
   */
  string error_msg = 2;

  /**
   * 用户活动奖励相关数据
   */
  UserActivityAwardShowInfoDTO data = 3;
}

message UserActivityAwardShowInfoDTO {

  /**
   * 用户可获取的活动奖励列表
   */
  repeated AwardShowDTO award_info = 1;

  /**
   * 用户在该活动已获取的活动奖励
   */
  repeated AwardShowDTO award_record = 2;
  /**
   * 用户在各个父任务对应的奖励情况
   */
  repeated UserTaskAwardShowInfoDTO user_task_award = 3;

  /**
   * 活动ID
   */
  uint64 activity_id = 4;

  /**
   * 活动名称
   */
  string activity_name = 5;

  /**
   * 活动纬度奖励选择配置（聚合任务纬度）
   */
  AwardSelectionShowConfigDTO award_selection_config = 6;
}

message UserTaskAwardShowInfoDTO {

  /**
   * 用户任务可获取的奖励列表
   */
  repeated AwardShowDTO award_info = 1;

  /**
   * 若奖励为多选一 奖励选择配置
   */
  AwardSelectionShowConfigDTO award_selection_config = 2;

  /**
   * 用户在各个阶梯对应的奖励情况
   */
  repeated UserTaskStepAwardShowInfoDTO user_step_award = 3;

  /**
   * 父任务ID
   */
  uint64 task_id = 4;
}

message UserTaskStepAwardShowInfoDTO {

  /**
   * 阶梯
   */
  uint32 step = 1;
  /**
   * 用户在该阶梯下可获取的奖励列表
   */
  repeated AwardShowDTO award_info = 2;
}

message UserAwardRecoverDTO {
  //活动ID
  uint64 activity_id = 1;
  //批次ID
  uint64 batch_id = 2;
  // 业务唯一ID，惟一索引
  string unique_id = 3;
  // 商家id
  uint64 seller_id = 4;
  // 追缴金额
  uint64 recover_value = 5;
  // 前返金额
  uint64 pre_award_value = 6;
  // 与前返后返相同
  string group_biz_id = 7;
  // 追缴原因类型
  RecoveryReasonTypeEnum recovery_reason_type = 8;
}

message UserAwardRecoverEvent {
  //活动ID
  uint64 activity_id = 1;
  //批次ID
  uint64 user_id = 2;
  // 业务唯一ID，惟一索引
  uint64 task_id = 3;
  // 追缴的原因
  RecoveryReasonTypeEnum recovery_reason_type = 4;
}

enum RecoveryReasonTypeEnum {
  // 奖励未过风控
  AWARD_RISK = 0;

  // 未达最低目标
  NOT_REACH_TARGET = 1;
}

message BatchQueryUserTaskAwardRequest {
  // 用户id
  uint64 user_id = 1;
  // 子任务id
  repeated uint64 task_id = 2;
}

message BatchQueryUserTaskAwardResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励记录对象
   */
  repeated AwardRecordInfoDTO data = 3;
}

message BatchQueryUserTaskOriginalAwardRequest {
  // 用户id
  uint64 user_id = 1;
  // 发奖单id
  repeated string biz_id = 2;
}

message BatchQueryUserTaskOriginalAwardResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 奖励记录对象
   */
  repeated OriginalAwardRecordInfoDTO data = 3;
}

message OriginalAwardRecordInfoDTO {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 发奖数额
   */
  uint64 award_count = 2;
  /**
   * 发奖时间
   */
  uint64 award_time = 3;
  /**
   * 奖励类型
   */
  uint32 award_type = 4;
  /**
   * 奖励名称
   */
  string award_name = 5;
  /**
   * 活动ID
   */
  uint64 activity_id = 6;
  /**
   * 任务ID
   */
  uint64 task_id = 7;
  /**
   * 过期时间
   */
  uint64 expire_time = 8;
  /**
   * 奖励状态
   */
  uint32 status = 9;
  /**
   * 更新时间
   */
  uint64 update_time = 10;
  /**
   * 奖励配置ID
   */
  uint64 config_id = 11;
  /**
   * 额外参数
   */
  string ext = 12;
  /**
   * 唯一ID
   */
  string subject_id = 13;
  /**
   * 原始奖励数额
   */
  uint64 original_award_value = 14;
}

service ActivityAwardDomainService {
  /**
   * 获取活动多个任务奖励配置信息(仅支持静态奖励值)
   */
  rpc GetTaskAwardConfig (GetTaskAwardConfigRequest) returns (GetTaskAwardConfigResponse);
  /**
   * 获取活动多个任务奖励配置信息(支持动态奖励值)
   */
  rpc GetUserTaskAwardConfig (GetUserTaskAwardConfigRequest) returns (GetUserTaskAwardConfigResponse);
  /**
   * 获取用户活动下所有已发放奖励
   */
  rpc GetUserActivityAward (GetUserActivityAwardRequest) returns (GetUserActivityAwardResponse);
  /**
   * 获取用户所有活动已发放奖励
   */
  rpc GetUserAllActivityAward(GetUserAllActivityAwardRequest) returns (GetUserAllActivityAwardResponse);
  /**
   * 获取用户某批bizId对应的记录
   */
  rpc BatchGetUserRecordByBizId(BatchGetUserRecordByBizIdRequest) returns (BatchGetUserRecordByBizIdResponse);
  /**
   * 触发活动hold奖励发放
   */
  rpc TriggerHoldAwardSend(TriggerHoldAwardSendRequest) returns (TriggerHoldAwardSendResponse);
  /**
   * 更新奖励配置
   */
  rpc UpdateAwardConfig (UpdateAwardConfigRequest) returns (UpdateAwardConfigResponse);
  /**
   * 促开播获取用户实际奖励
   */
  rpc GetBroadcastUserAward (GetBroadcastUserAwardRequest) returns (GetBroadcastUserAwardResponse);
  /**
   * 促开播导出奖励明细
   */
  rpc ExportBroadcastAwardList (ExportBroadcastAwardListRequest) returns (ExportBroadcastAwardListResponse);
  /**
   * 批量取消奖励发放
   */
  rpc BatchCancelHoldAwardSend (BatchCancelHoldAwardSendRequest) returns (BatchCancelHoldAwardSendResponse);
  /**
   用户抽奖
   */
  rpc StartLottery(LotteryRequest) returns (LotteryResponse);
  /**
  查询用户抽奖状态
   */
  rpc QueryLottery(QueryLotteryRequest) returns (QueryLotteryResponse);
  /**
   * 查询用户活动奖励情况
   */
  rpc QueryUserActivityAwardShowInfo(QueryUserActivityAwardShowInfoRequest) returns (QueryUserActivityAwardShowInfoResponse);
  /**
   * 获取用户活动下所有已发放奖励
   */
  rpc BatchQueryUserTaskAward(BatchQueryUserTaskAwardRequest) returns (BatchQueryUserTaskAwardResponse);
  /**
   * 获取用户任务的原始奖励金额（多阶梯提前发奖专用）
   */
  rpc BatchQueryUserTaskOriginalAward(BatchQueryUserTaskOriginalAwardRequest) returns (BatchQueryUserTaskOriginalAwardResponse);
}