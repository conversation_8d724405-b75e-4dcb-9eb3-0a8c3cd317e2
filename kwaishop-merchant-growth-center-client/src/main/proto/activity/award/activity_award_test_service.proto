syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.award;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.award";
option java_outer_classname = "ActivityAwardTestServiceProto";

message UpdateAwardConfigTestRequest {
  /**
   * 配置ID
   */
  uint64 id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 实体ID
   */
  uint64 entity_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
  /**
   * 奖励名称
   */
  string award_name = 5;
  /**
   * 权益配置ID
   */
  uint64 interest_config_id = 6;
  /**
   * 发送规则
   */
  string send_rule = 7;

  /**
   * 延迟计算规则
   */
  string delay_calc_rule = 8;
}

message UpdateAwardConfigDelayRuleRequest {

  string operator = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;

  uint64 parent_task_id = 3;

  uint64 award_config_id = 4;

  string delay_calc_rule = 5;

}

message UpdateAwardConfigTestResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UserAwardValueReviewRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 复核方式（单用户复核/批量复核Excel/批量复核人群包 ）
   */
  uint32 manual_type = 2;
  /**
   * 人群包ID或者Excel地址或者单用户ID
   */
  string manual_entity = 3;
  /**
   * 操作人
   */
  string operator = 4;
  /**
   * 复核不一致数据是否进行修复
   */
  bool fixed = 5;
  /**
   * 是否对结果进行播报
   */
  bool kim_report = 6;

  /**
    * 是否同步横向下发奖励（谨慎设置为true，确保横向已经删除之前发放的奖励）
   */
  bool need_dispatch_award = 7;
}

message UserAwardValueReviewResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 复核结果
   */
  string data = 3;
}
message DirectSendAwardWithoutReviewResponse {


  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;

}

message DirectSendAwardWithoutReviewRequest {
  /**
 * 活动ID
 */
  uint64 activity_id = 1;
  /**
   * userId
  */
  uint64 user_id = 2;
}

message BatchDirectSendAwardWithoutReviewRequest {

  /**
* 活动ID
*/
  uint64 activity_id = 1;

  string operator = 2;

  /**
 * 方式（单用户/批量Excel/kconf ）
 */
  uint32 manual_type = 3;
  /**
   * Excel地址或者单用户ID/kconf
   */
  string manual_entity = 4;

}

message HandleSingleAuditAwardAdminRequest {

  uint64 user_id = 1;

  string subject_id = 2;

  /**
   * 方式（单用户复核/批量复核Excel/批量复核人群包 ）
   */
  uint32 manual_type = 3;
  /**
   * Excel地址或者单用户ID/kconf
   */
  string manual_entity = 4;
}

message HandleSingleAuditAwardAdminResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}
message UserAwardDelayRecordStatusTransferRequest {

  /**
    活动id，subject_id读取kconf
   */
  uint64 activity_id = 1;
  /**
   * 校验奖励类型
  */
  repeated int32 check_award_type = 2;
  /**
   * 是否更新
  */
  bool update_flag = 3;
}

message UserAwardDelayRecordStatusTransferResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}
/**
 * 延迟配置调整
 */
message AwardConfigModifyDelayCalcDayRequest {
  /**
   * award_config表的id
   */
  uint64 award_config_id = 1;

  /**
   * 延迟天数
   */
  uint32 delay_calc_days = 2;
}

message AwardConfigModifyDelayCalcDayResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

/**
 * 延迟算奖测试
 */
message DelayRecordCalcRequest {
  repeated SingleDelayRecordCalcData delay_record_calc_data = 1;
}
message SingleDelayRecordCalcData {
  uint64 user_id = 1;
  uint64 award_record_id = 2;
}
message DelayRecordCalcResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message DeleteUserAwardDataReq {
  /**
* 类型，Excel，人群包
*/
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;

  repeated int64 sub_task_id = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message DeleteUserAwardDataResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

service ActivityAwardTestService {
  /**
   * 更新奖励配置
   */
  rpc UpdateAwardConfigTest (UpdateAwardConfigTestRequest) returns (UpdateAwardConfigTestResponse);
  /**
   * 用户奖励复核
   */
  rpc UserAwardValueReview (UserAwardValueReviewRequest) returns (UserAwardValueReviewResponse);
  /**
    * 谨慎使用，直接给横向下发奖励消息
  */
  rpc DirectSendAwardWithoutReview(DirectSendAwardWithoutReviewRequest) returns(DirectSendAwardWithoutReviewResponse);

  rpc BatchDirectSendAwardWithoutReview(BatchDirectSendAwardWithoutReviewRequest) returns(DirectSendAwardWithoutReviewResponse);

  /**
    * 谨慎使用，直接把风控中的发奖记录给横向下发奖励
    * 名单有风控侧提供
  */
  rpc HandleSingleAuditAwardAdmin(HandleSingleAuditAwardAdminRequest) returns(HandleSingleAuditAwardAdminResponse);
  /**
   * 谨慎使用，用户奖励状态迁移，将读取到的记录状态统一变更到CALCULATING，且删除删除对应user_audit_record
  */
  rpc UserAwardDelayRecordStatusTransfer(UserAwardDelayRecordStatusTransferRequest) returns(UserAwardDelayRecordStatusTransferResponse);
  /**
   * 修改延迟算奖日期
   */
  rpc ModifyAwardConfigDelayCalcDay(AwardConfigModifyDelayCalcDayRequest) returns(AwardConfigModifyDelayCalcDayResponse);
  /**
   * 奖励计算
   */
  rpc HandleSingleDelayRecordCalc(DelayRecordCalcRequest) returns(DelayRecordCalcResponse);
  /**
   * 刷新活动奖励延迟规则
   */
  rpc UpdateAwardConfigDelayRule(UpdateAwardConfigDelayRuleRequest) returns(DelayRecordCalcResponse);
  /**
   * 删除用户发奖单数据（包含审批单）
   */
  rpc DeleteUserAwardData(DeleteUserAwardDataReq) returns(DeleteUserAwardDataResponse);
}