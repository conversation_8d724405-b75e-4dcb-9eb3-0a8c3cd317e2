syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.strategy";
option java_outer_classname = "ActivityStrategyTestServiceProto";

message TestSaveStrategyRequest {
  // 策略的主键ID，第一次保存时为空，编辑时需要带上
  uint64 id = 1;
  // 业务场景，通过业务查询接口获取
  uint64 activity_id = 2;
  // 开始时间
  uint64 start_time = 3;
  // 结束时间
  uint64 end_time = 4;
  // 策略配置
  string strategy_config = 5;
  // 策略状态
  int32 status = 6;
  // 扩展数据
  string extra = 7;
  // 当前的操作人
  string operator = 8;
}

message TestSaveStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  uint64 data = 3;
}

message TestDeleteStrategyRequest {
  uint64 strategy_id = 1;
}
message TestDeleteStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  uint64 data = 3;
}

/**
策略测试服务
 */
service KwaishopMerchantGrowthStrategyTestService {
    rpc SaveStrategy (TestSaveStrategyRequest) returns (TestSaveStrategyResponse);
    rpc DeleteStrategy (TestDeleteStrategyRequest) returns (TestDeleteStrategyResponse);
}