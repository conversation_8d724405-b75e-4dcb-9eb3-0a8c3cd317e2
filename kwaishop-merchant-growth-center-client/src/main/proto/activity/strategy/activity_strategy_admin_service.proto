syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.strategy";
option java_outer_classname = "ActivityStrategyServiceProto";

//===================================================
// 保存策略
//===================================================
message SaveStrategyRequest {
  // 策略的主键ID，第一次保存时为空，编辑时需要带上
  uint64 id = 1;
  // 业务场景，通过业务查询接口获取
  uint64 activity_id = 2;
  // 策略名称
  string name = 3;
  // 文案策略
  string tips = 4;
  // 开始时间
  uint64 start_time = 5;
  // 结束时间
  uint64 end_time = 6;
  // 供给类型，通过策略编辑页供给类型查询接口获取
  int32 supply_type = 7;
  // 商家范围类型，通过策略编辑页商家范围查询接口获取
  int32 crowd_type = 8;
  // 商家范围的取值，全量商家类型该字段为空
  string crowd_value = 9;
  // 激励方式
  int32 award_type = 10;
  // 激励方式对应的规则
  string account_type = 11;
  // 奖励对象
  string award_subject = 12;
  // 奖励方案
  string award_rule = 13;
  // 补贴模式
  string subsidy_model = 14;
  // 补贴渠道
  string subsidy_channel = 15;
  // 预算金额，10000元
  string budget_amount = 16;
  // 当前的操作人
  string operator = 17;
  // 单用户最大奖励金额，单位为元/人次/个
  string single_user_max_award_value = 18;
  // 参与形式
  int32 join_type = 19;
  // 是否使用福袋
  bool use_lucky_bag = 20;
  // 报名开始时间
  uint64 registration_start_time = 21;
  // 报名结束时间
  uint64 registration_end_time = 22;
  // 不使用福袋时，选择对应的直播形式
  int32 broadcast_room_effect = 23;
  // 策略触达渠道
  //string push_channel = 24;
  // 负责人
  string owner = 24;
  // 自动分销的佣金比例
  uint64 auto_promote_fee_percent = 26;
  // 自动分销的钱包类型
  string auto_promoter_account_type = 27;
}
// 冲突的商家信息
message StrategyConflictSellerInfoDTO {
  uint64 strategy_id = 1;
  string strategy_name = 2;
  repeated uint64 conflict_seller_ids = 3;
}
message StrategySaveDTO {
  uint64 strategy_id = 1;
  // 被风控的商家名单，用于前端提醒用户
  repeated uint64 risked_seller_ids = 2;
  // 冲突的商家信息
  repeated StrategyConflictSellerInfoDTO conflict_seller_info = 3;
}
message SaveStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的主键ID和被风控的商家名单
   */
  StrategySaveDTO data = 3;
}

//===================================================
// 查询策略详情
//===================================================
message QueryStrategyDetailRequest {
  // 策略ID
  uint64 strategy_id = 1;
}
message StrategyDetailDTO {
  // 策略的主键ID，第一次保存时为空，编辑时需要带上
  uint64 id = 1;
  // 业务场景，通过业务查询接口获取
  uint64 activity_id = 2;
  // 业务名称
  string activity_name = 3;
  // 策略名称
  string name = 4;
  // 文案策略
  string tips = 5;
  // 开始时间
  uint64 start_time = 6;
  // 结束时间
  uint64 end_time = 7;
  // 供给类型，通过策略编辑页供给类型查询接口获取
  int32 supply_type = 8;
  // 供给类型名称
  string supply_type_name = 9;
  // 商家范围类型，通过策略编辑页商家范围查询接口获取
  int32 crowd_type = 10;
  // 商家范围的取值，全量商家类型该字段为空
  string crowd_value = 11;
  // 激励方式
  int32 award_type = 12;
  // 激励方式名称
  string award_type_name = 13;
  // 激励方式对应的规则
  string account_type = 14;
  // 激励方式规则名称
  string account_type_name = 15;
  // 奖励对象
  string award_subject = 16;
  // 奖励对象名称
  string award_subject_name = 17;
  // 奖励方案
  string award_rule = 18;
  // 补贴模式
  string subsidy_model = 19;
  // 补贴模式名称
  string subsidy_model_name = 20;
  // 补贴渠道
  string subsidy_channel = 21;
  // 预算金额，10000元
  string budget_amount = 22;
  // 当前的操作人
  string operator = 23;
  // 单用户最大奖励金额，单位为元/人次/个
  string single_user_max_award_value = 24;
  // 参与类型
  int32 join_type = 25;
  // 是否使用福袋
  bool use_lucky_bag = 26;
  // 报名开始时间
  uint64 registration_start_time = 27;
  // 报名结束时间
  uint64 registration_end_time = 28;
  // 不使用福袋时，选择对应的直播形式
  int32 broadcast_room_effect = 29;
  // 负责人
  string owner = 30;
  // 状态
  int32 status = 31;
  // 自动分销的佣金比例
  uint64 auto_promote_fee_percent = 32;
  // 自动分销的钱包类型
  string auto_promoter_account_type = 33;
  // 自动分销的钱包名称
  string auto_promoter_account_type_name = 34;
}
message QueryStrategyDetailResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data
   */
  StrategyDetailDTO data = 3;
}

//===================================================
// 上线策略
//===================================================
message OnlineStrategyRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message StrategyOnlineDTO {
  uint64 strategy_id = 1;
  // 被风控的商家名单，用于前端提醒用户
  repeated uint64 risked_seller_ids = 2;
  // 冲突的商家信息
  repeated StrategyConflictSellerInfoDTO conflict_seller_info = 3;
}
message OnlineStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的上线结果
   */
  StrategyOnlineDTO data = 3;
}

//===================================================
// 下线策略
//===================================================
message OfflineStrategyRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message OfflineStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的主键ID
   */
  uint64 data = 3;
}

//===================================================
// 删除策略
//===================================================
message DeleteStrategyRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message DeleteStrategyResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的主键ID
   */
  uint64 data = 3;
}
message DeleteRelevantDataRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
  // ak
  string ak = 3;
}
message DeleteRelevantDataResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
}

//===================================================
// SelectInfoDTO
//===================================================
/**
下拉选项的通用DTO
 */
message SelectInfoDTO {
  // 下拉选项展示的文本值
  string text = 1;
  // 下拉选项对应的选项值
  string value = 2;
}

//===================================================
// 查询策略编辑页下的枚举类信息
//===================================================
message QuerySelectInfoRequest {
  // 业务ID
  uint64 activity_id = 1;
  // 要返回哪些枚举选项的信息
  string info_codes = 2;
}
message StrategySelectInfoDTO {
  // 枚举选项的编码
  string info_code = 1;
  // 枚举选项的数据
  repeated SelectInfoDTO data = 2;
}

message QuerySelectInfoResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data
   */
  repeated StrategySelectInfoDTO data = 3;
}
//===================================================
// 查询奖励类型
//===================================================
message QueryAwardTypeRequest {
  // 业务ID
  uint64 activity_id = 1;
}
message AwardTypeInfoDTO {
  // 奖励类型
  int32 award_type = 1;
  // 奖励名称
  string award_name = 2;
  // 奖励下对应的发放规则
  repeated SelectInfoDTO award_rule = 3;
}
message QueryAwardTypeResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data
   */
  repeated AwardTypeInfoDTO data = 3;
}
//===================================================
// 查询策略列表页
//===================================================
message QueryStrategyListRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 策略名称
  string strategy_name = 2;
  // 开始时间
  uint64 start_time = 3;
  // 结束时间
  uint64 end_time = 4;
  // 供给类型
  string supply_type = 5;
  // 奖励类型
  string award_type = 6;
  // 策略状态
  string strategy_status = 7;
  // 上线状态
  int32 online_status = 8;
  // 发奖状态
  string award_status = 9;
  // 页码
  int32 page_no = 10;
  // 每页大小
  int32 page_size = 11;
  // 业务id集合
  string activity_id = 12;
}
message StrategyListDetailDTO {
  // 策略的主键ID
  uint64 id = 1;
  // 业务场景，通过业务查询接口获
  uint64 activity_id = 2;
  // 策略名称
  string name = 3;
  // 供给类型编码
  int32 supply_type = 4;
  // 供给类型的枚举名称
  string supply_type_name = 5;
  // 商家范围类型
  int32 crowd_type = 6;
  // 奖励类型
  int32 award_type = 7;
  // 覆盖商家数量
  int32 crowd_count = 8;
  // 奖励类型名称
  string award_type_name = 9;
  // 策略状态
  int32 strategy_status = 10;
  // 策略状态名称
  string strategy_status_name = 11;
  // 策略上线状态
  int32 strategy_online_status = 12;
  // 策略上线状态名称
  string strategy_online_status_name = 13;
  // 奖励发送状态
  int32 award_send_status = 14;
  // 奖励发送状态名称
  string award_send_status_name = 15;
  // 奖励发放时间
  int64 award_send_time = 17;
  // 总体预算，单位元
  string budget_amount = 18;
  // 当前预计消耗，单位元
  string already_cost = 19;
  // 额外补贴金额，单位元
  string total_extra_award = 20;
  // 预计消耗占比
  string cost_budget_ratio = 21;
  // 负责人
  string owner = 22;
}
message StrategyPageListDTO {
  int32 page_no = 1;
  int32 page_size = 2;
  uint64 total = 3;
  repeated StrategyListDetailDTO data = 4;
}
message QueryStrategyListResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data
   */
  StrategyPageListDTO data = 3;
}
//===================================================
// 额外补贴查询列表
//===================================================
message QueryExtraAwardRequest {
  // 策略ID
  uint64 strategy_id = 1;
}
message ExtraAwardDetailDTO {
  /**
  奖励主键ID
   */
  uint64 id = 1;
  /**
  卖家ID
   */
  uint64 seller_id = 2;
  /**
  卖家名称
   */
  string seller_name = 3;
  /**
  已拉新人数
   */
  uint64 pull_new_user_count = 4;
  /**
  已获得的奖励，单位元
   */
  string current_award_value = 5;
  /**
  额外补贴，单位元
   */
  string extra_award_value = 6;
}
message QueryExtraAwardResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的主键ID
   */
  repeated  ExtraAwardDetailDTO data = 3;
}
//===================================================
// 设置额外补贴
//===================================================
message SaveExtraAwardRequest {
  // 主键ID
  uint64 id = 1;
  // 策略ID
  uint64 strategy_id = 2;
  // 卖家ID
  uint64 seller_id = 3;
  // 奖励金额，单位元
  string award_value = 4;
  // 操作人
  string operator = 5;
}
message SaveExtraAwardResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回奖励记录的主键ID
   */
  uint64 data = 3;
}
//===================================================
// 删除额外补贴
//===================================================
message DeleteExtraAwardRequest {
  // 主键ID
  uint64 id = 1;
  // 策略ID
  uint64 strategy_id = 2;
  // 商家ID
  uint64 seller_id = 3;
  // 当前操作人
  string operator = 4;
}
message DeleteExtraAwardResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回策略的主键ID
   */
  uint64 data = 3;
}
//===================================================
// 根据商家ID查询商家昵称
//===================================================
message SellerInfoQueryRequest {
  string key = 1;
}
message SellerInfoDTO {
  uint64 seller_id = 1;
  string seller_name = 2;
}
message SellerInfoQueryResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 返回查询到商家信息
   */
  repeated SellerInfoDTO data = 3;
}

//===================================================
// 更新策略发奖时间
//===================================================
message UpdateStrategyAwardTimeRequest {
  repeated uint64 strategy_ids = 1;
  uint64 award_time = 2;
}
message UpdateStrategyAwardTimeResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  /**
  data 更新失败的策略ID
   */
  repeated uint64 data = 3;
}

// 删掉用户报名记录
message RemoveUserRegistrationRecordRequest {
  uint64 strategy_id = 1;
  uint64 user_id = 2;
  string operator = 3;
}
message RemoveUserRegistrationRecordResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
}

// 批量操作商家账号
message BathOperateSellerIdsRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 商家范围的取值，指定商家uid上传，文件上传类型该字段为空
  string seller_ids = 2;
  // 商家范围的取值，文件形式上传，指定商家uid上传该字段为空
  string cdn_url = 3;
  // 操作人
  string operator = 4;
  // 请求类型 1-批量报名 2-批量导入商家ID 3-批量删除
  uint32 type = 5;
  // 人群ID
  uint64 crowd_id = 6;
}

message BathImportSellerIdsDTO {
  uint64 strategy_id = 1;
  // 被风控的商家名单，用于前端提醒用户
  repeated uint64 risked_seller_ids = 2;
  // 冲突的商家信息
  repeated StrategyConflictSellerInfoDTO conflict_seller_info = 3;
}

message BathOperateSellerIdsResponse {
  int32 result = 1;
  string error_msg = 2;
  BathImportSellerIdsDTO data = 3;
}

// 查询用户报名状态请求
message QueryUserRegistrationStatusRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 商家范围的取值，指定商家uid上传，文件上传类型该字段为空
  string seller_ids = 2;
  // 商家范围的取值，文件形式上传，指定商家uid上传该字段为空
  string cdn_url = 3;
  // 操作人
  string operator = 4;
}
message UserRegistrationStatusDTO {
  repeated uint64 already_registration_user_ids = 1;
}
// 查询用户报名状态响应
message QueryUserRegistrationStatusResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
  // data
  UserRegistrationStatusDTO data = 3;
}

//===================================================
// 更新策略下用户的指标和奖励值
//===================================================
message UpdateUserStrategyIndicatorAndAwardDTO {
  uint64 user_id = 1;
  uint64 indicator_id = 2;
  uint64 indicator_value = 3;
  uint64 award_value = 4;
}
message UpdateUserStrategyIndicatorAndAwardRequest {
  uint64 strategy_id = 1;
  string operator = 2;
  string ak = 3;
  repeated UpdateUserStrategyIndicatorAndAwardDTO update_data = 4;
}
message UpdateUserStrategyIndicatorAndAwardResponse {
  int32 result = 1;
  string error_msg = 2;
  // 失败的用户ID
  repeated uint64 data = 3;
}

message StrategyIdIncreaseRequest{
  uint64 activity_id = 1;
}

message StrategyIdIncreaseResponse{
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}
//===================================================
// 触发策略立即发奖
//===================================================
message TriggerStrategySendAwardRequest {
  // 批量发奖的策略ID
  repeated uint64 strategy_ids = 1;
  // 操作人
  string operator = 2;
}
message TriggerStrategySendAwardResponse {
  int32 result = 1;
  string error_msg = 2;
  uint64 data = 3;
}
//===================================================
// 触发策略发放延迟发奖
//===================================================
message TriggerStrategySendDelayAwardRequest {
  // 策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message TriggerStrategySendDelayAwardResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 设置策略定时发奖
//===================================================
message TriggerStrategyTimingSendAwardRequest {
  // 批量发奖的策略ID
  repeated uint64 strategy_ids = 1;
  // 定时发奖的时间
  uint64 award_time = 2;
  // 操作人
  string operator = 3;
}
message TriggerStrategyTimingSendAwardResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 查询延迟发奖名单
//===================================================
message QueryDelaySendAwardUserRequest {
  // 批量发奖的策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message QueryDelaySendAwardUserResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated InterveneUserInfoDTO data = 3;
}
//===================================================
// 删除某个延迟发奖的用户
//===================================================
message DeleteDelaySendAwardUserRequest {
  // 批量发奖的策略ID
  uint64 strategy_id = 1;
  uint64 user_id = 2;
  // 操作人
  string operator = 3;
}
message DeleteDelaySendAwardUserResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 查询干预过拉新数据的用户名单
//===================================================
message QueryInterveneUserRequest {
  // 批量发奖的策略ID
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}
message InterveneUserInfoDTO {
  uint64 id = 1;
  // 批量发奖的策略ID
  uint64 user_id = 2;
  string nick_name = 3;
  uint64 award_config_id = 4;
  int32 award_status = 5;
  string award_value = 6;
  string award_status_name = 7;
  // 指标数据，包含拉新和拉回，eg: 拉新10人-拉回20人
  string indicator_data = 8;
  // 发奖时间
  string expect_award_time = 9;
}
message QueryInterveneUserResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated InterveneUserInfoDTO data = 3;
}
//===================================================
// 导入延迟发奖的名单
//===================================================
message ImportDelaySendAwardUserRequest {
  string file_url = 1;
  string operator = 2;
}
message ImportDelaySendAwardUserResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 导入干预拉新数据的名单
//===================================================
message ImportInterveneUserRequest {
  string file_url = 1;
  string operator = 2;
}
message ImportInterveneUserResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 修改策略记录信息
//===================================================
message UpdateStrategyRecordInfoRequest {
  uint64 user_id = 1;
  uint64 strategy_id = 2;
  int32 status = 3;
  uint64 begin_time = 4;
  uint64 end_time = 5;
  string operator = 6;
}
message UpdateStrategyRecordInfoResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 修改奖励记录信息
//===================================================
message UpdateAwardRecordInfoRequest {
  uint64 user_id = 1;
  uint64 strategy_id = 2;
  int32 status = 3;
  string operator = 6;
}
message UpdateAwardRecordInfoResponse {
  int32 result = 1;
  string error_msg = 2;
}
//===================================================
// 修改自动分销比例的快照数据
//===================================================
message UpdateAutoPromoterPercentSnapshotRequest {
  uint64 award_config_id = 1;
  repeated string dts = 2;
  int32 payed_percent = 3;
  string operator = 6;
}
message UpdateAutoPromoterPercentSnapshotResponse {
  int32 result = 1;
  string error_msg = 2;
}

/**
策略管理服务
 */
service KwaishopMerchantGrowthStrategyAdminService {
  // 增  改
  rpc Save (SaveStrategyRequest) returns (SaveStrategyResponse);
  // 删
  rpc Delete (DeleteStrategyRequest) returns (DeleteStrategyResponse);
  rpc DeleteRelevantData (DeleteRelevantDataRequest) returns (DeleteRelevantDataResponse);
  // 查
  rpc Detail (QueryStrategyDetailRequest) returns (QueryStrategyDetailResponse);
  rpc QueryList (QueryStrategyListRequest) returns (QueryStrategyListResponse);
  // 上线
  rpc Online (OnlineStrategyRequest) returns (OnlineStrategyResponse);
  // 下线
  rpc Offline (OfflineStrategyRequest) returns (OfflineStrategyResponse);
  // 查询枚举类选项信息
  rpc QuerySelectInfo (QuerySelectInfoRequest) returns (QuerySelectInfoResponse);
  // 奖励类型
  rpc QueryAwardType (QueryAwardTypeRequest) returns (QueryAwardTypeResponse);
  // 设置额外补贴
  rpc SaveStrategyExtraAward (SaveExtraAwardRequest) returns (SaveExtraAwardResponse);
  // 删除额外补贴
  rpc DeleteStrategyExtraAward (DeleteExtraAwardRequest) returns (DeleteExtraAwardResponse);
  // 查询额外补贴列表
  rpc QueryStrategyExtraAwardList (QueryExtraAwardRequest) returns (QueryExtraAwardResponse);
  // 根据商家ID搜索商家信息，设置额外补贴时用到
  rpc QuerySellerInfo (SellerInfoQueryRequest) returns (SellerInfoQueryResponse);
  // 批量更新策略发奖时间
  rpc BatchUpdateStrategyAwardTime (UpdateStrategyAwardTimeRequest)
      returns (UpdateStrategyAwardTimeResponse);
  // 删除用户报名记录
  rpc RemoveUserRegistrationRecord (RemoveUserRegistrationRecordRequest)
      returns (RemoveUserRegistrationRecordResponse);
  // 批量操作商家账号
  rpc BathOperateSellerIds(BathOperateSellerIdsRequest) returns (BathOperateSellerIdsResponse);
  // 查询用户的报名状态
  rpc QueryUserRegistrationStatus(QueryUserRegistrationStatusRequest)
      returns (QueryUserRegistrationStatusResponse);
  // 批量更新用户的策略指标和奖励
  rpc BatchUpdateUserIndicatorAndAward(UpdateUserStrategyIndicatorAndAwardRequest)
      returns(UpdateUserStrategyIndicatorAndAwardResponse);
  // 策略id自增
  rpc StrategyIdIncrease(StrategyIdIncreaseRequest) returns(StrategyIdIncreaseResponse);
  // 触发策略立即发奖
  rpc TriggerStrategySendAward(TriggerStrategySendAwardRequest) returns(TriggerStrategySendAwardResponse);
  // 触发策略立即发奖暂缓奖励
  rpc TriggerStrategySendDelayAward(TriggerStrategySendDelayAwardRequest) returns(TriggerStrategySendDelayAwardResponse);
  // 触发策略定时发奖
  rpc TriggerStrategyTimingSendAward(TriggerStrategyTimingSendAwardRequest)
      returns(TriggerStrategyTimingSendAwardResponse);
  // 查询延迟发奖的用户列表
  rpc QueryDelaySendAwardUserList(QueryDelaySendAwardUserRequest) returns(QueryDelaySendAwardUserResponse);
  // 删除某个延迟发奖的用户
  rpc DeleteDelaySendAwardUser(DeleteDelaySendAwardUserRequest) returns(DeleteDelaySendAwardUserResponse);
  // 查询干预拉新数据的用户列表
  rpc QueryInterveneUserInfoList(QueryInterveneUserRequest) returns(QueryInterveneUserResponse);
  // 导入延迟发奖的用户
  rpc ImportDelaySendAwardUser(ImportDelaySendAwardUserRequest) returns(ImportDelaySendAwardUserResponse);
  // 导入干预拉新数据的用户
  rpc ImportInterveneUser(ImportInterveneUserRequest) returns(ImportInterveneUserResponse);
  // 更新策略记录信息
  rpc UpdateStrategyRecordInfo(UpdateStrategyRecordInfoRequest) returns(UpdateStrategyRecordInfoResponse);
  // 更新奖励记录状态
  rpc UpdateAwardRecordInfo(UpdateAwardRecordInfoRequest) returns(UpdateAwardRecordInfoResponse);
  // 更新奖励记录状态
  rpc UpdateAutoPromoterPercentSnapshot(UpdateAutoPromoterPercentSnapshotRequest)
      returns(UpdateAutoPromoterPercentSnapshotResponse);
}