syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.strategy";
option java_outer_classname = "ActivityStrategyNotificationCompensationServiceProto";

// 补偿创建策略触达
message CreateStrategyNotificationRequest{
  repeated uint32 channel_list = 1;
  repeated uint64 strategy_id_list = 2;
  string operator = 3;
}

message CreateStrategyNotificationResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 异步扫描user_registration_record补偿发送催报名push
message CompensateNotSignUpNotificationRequest{
  repeated uint64 strategy_id_list = 1;
  string operator = 3;
}

message CompensateNotSignUpNotificationResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 异步扫描user_strategy_record补偿发送用户达标和未达标push
message CompensateSignUpNotificationRequest {
  repeated uint64 strategy_id_list = 1;
  string operator = 3;
}

message CompensateSignUpNotificationResponse {
  int32 result = 1;
  string error_msg = 2;
}

service KwaishopMerchantGrowthStrategyNotificationCompensationService {
  // 补偿创建触达配置
  rpc CreateStrategyNotification(CreateStrategyNotificationRequest) returns(CreateStrategyNotificationResponse);
  // 未报名用户补偿发送push，包含策略开始报名未报名、策略开始未报名
  rpc CompensateNotSignUpNotification(CompensateNotSignUpNotificationRequest) returns(CompensateNotSignUpNotificationResponse);
  // 已报名用户补偿发送push，包含已达标、未达标
  rpc CompensateSignUpNotification(CompensateSignUpNotificationRequest) returns(CompensateSignUpNotificationResponse);
}