syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.indicator;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.strategy";
option java_outer_classname = "StrategyDTOProto";

/**
用户策略记录变更事件
 */
message UserStrategyRecordStatusChangeEvent {
  /**
  活动ID
   */
  uint64 activity_id = 1;

  /**
  实体ID
   */
  uint64 entity_id = 2;

  /**
  用户ID
   */
  uint64 user_id = 3;

  /**
   * 更新时间
   */
  uint64 update_time = 4;

  /**
   * 变更前的记录状态
   */
  int32 old_record_status = 5;

  /**
   * 变更后的记录状态
   */
  int32 new_record_status = 6;

  /**
  是否删除，1-未删除，0-已删除
   */
  int32 deleted = 7;

}

enum UserStrategyRecordStatus {
    // 初始状态，对应一开始还未创建策略
    NONE = 0;
    // 生效中
    EFFECTIVE = 1;
    // 已发送
    ALREADY_SEND = 10;
    // 已失效
    INVALID = 20;
    // 被风控
    RISKED = 21;
}


