syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.lottery;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.lottery";
option java_outer_classname = "LotteryServiceProto";


message UserLotteryRecordRequest {
  /**
   业务类型
   */
  string biz_code = 1;

  /**
  抽奖活动ID
   */
  repeated int64 entity_id = 2;

  /**
   * 用户ID
   */
  int64 user_id = 3;
}

message UserLotteryRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  /**
   * 用户奖励记录
   */
  repeated UserLotteryRecordDTO user_lottery_record = 3;
}
message UserLotteryRecordDTO{
  /**
   * 业务类型
   */
  string biz_code = 1;
  /**
   * 用户ID
   */
  int64 user_id = 2;
  /**
   * 抽奖任务ID
   */
  int64 entity_id = 3;
  /**
   * 抽奖子任务ID
   */
  int64 sub_entity_id = 4;
  /**
   * 抽奖状态 10-未开始 20-已中奖 30-未中奖
   */
  int32 status = 5;
  /**
   * 奖励值
   */
  int64 award_count = 6;
  /**
   * 奖励类型
   */
  int32 award_type = 7;
}

message LotteryDrawRequest {
  /**
   * 业务空间
   */
  string biz_code = 1;
  /**
   * 抽奖活动 ID
   */
  uint64 lottery_event_id = 2;
  /**
   * 抽奖项 ID
   */
  uint64 lottery_item_id = 3;
}

message LotteryDrawResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 抽奖结果
   */
  LotteryDrawDataDTO data = 3;
}

message LotteryDrawDataDTO {

}
/**
 * 业务域服务
 */
service LotteryService {

  rpc QueryUserLotteryRecord(UserLotteryRecordRequest) returns (UserLotteryRecordResponse);

  rpc DrawLottery (LotteryDrawRequest) returns (LotteryDrawResponse);
}
