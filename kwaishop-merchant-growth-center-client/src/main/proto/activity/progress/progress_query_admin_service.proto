syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.progress;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.progress";
option java_outer_classname = "ProgressQueryAdminServiceProto";

message GetEmpViewActivityProgressIdxListRequest {
  /**
  * 操作人
  */
  string biz_code = 1;

  /**
    * 操作人
    */
  string operator = 2;

  /**
    * 指标类型枚举（近30日、近60日、近90日）
    */
  string index_type = 3;
}

message GetEmpViewActivityProgressIdxListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  ProgressQuerySlrActivityIndexDTO data = 3;
}

message GetSlrPreviewIndexOptionsRequest {
  /**
   * 操作人
   */
  string biz_code = 1;

}

message GetSlrPreviewIndexOptionsResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  repeated SlrPreviewIndexOptionDTO data = 3;

}

message SlrPreviewIndexOptionDTO {

  /**
  * 标签
  */
  string label = 1;
  /**
  * code
  */
  string value = 2;
}

message ProgressQuerySlrActivityIndexDTO {
  /**
   * 被下发激励活动人数
   */
  string dispatch_cnt = 1;

  /**
    * 报名激励活动人数
 */
  string draw_cnt = 2;

  /**
 * 预估完成商家人数
 */
  string predict_finish_cnt = 3;

  /**
 * 实际完成商家人数
 */
  string fact_finish_cnt = 4;

  /**
   * 指标类型（近30日、近60日、近90日）
   */
  string index_type = 5;

  /**
   * 当前读版本
   */
  uint32 version = 6;
}

message GetUserActivityProgressListRequest {
  /**
   * 操作人
   */
  string operator = 1;

  /**
    * 请求参数
    */
  string query_param = 2;
}

message GetUserActivityProgressListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message ExportUserActivityProgressListRequest {
  /**
  * 操作人
  */
  string operator = 1;

  /**
   * 导出场景
   */
  string scene = 2;

  /**
   * 活动
   */
  uint64 activity_id = 3;

  /**
   * 用户id
  */
  uint64 user_id = 4;
}


message ExportUserActivityProgressListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}
message GetActivityTaskDimOptionsRequest {
  /**
    * 活动id
   */
  uint64 activity_id = 1;

  /**
   * 用户id（可以判断出在在哪个分层）
  */
  uint64 user_id = 2;

  /**
 * 操作人
 */
  string operator = 3;
}

message GetActivityTaskDimOptionsResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message GetUserSubActivityProgressListRequest {
  /**
   * 操作人
  */
  string operator = 1;

  /**
  * 活动id
 */
  uint64 activity_id = 2;

  /**
   * 用户id（可以判断出在在哪个分层）
  */
  uint64 user_id = 3;

  /**
    * 子活动order
    */
  uint32 sub_activity_order = 4;

  /**
  * 分层order
  */
  uint32 layer_order = 5;
  /**
   * 周期order
   */
  uint32 period_order = 6;

}

message GetUserSubActivityProgressListResponse {
  /**
    * 返回结果码
    */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

service ProgressQueryAdminService {
  /**
   * 查询小二商家活动指标类型下拉集合
   */
  rpc GetSlrPreviewIndexOptions(GetSlrPreviewIndexOptionsRequest) returns (GetSlrPreviewIndexOptionsResponse);
  /**
   * 查询当前小二下可查询商家活动进度指标情况
   */
  rpc GetEmpViewActivityProgressIdxList(GetEmpViewActivityProgressIdxListRequest)  returns (GetEmpViewActivityProgressIdxListResponse);

  /**
   * 查询当前小二下可查询商家活动列表
   */
  rpc GetUserActivityProgressList(GetUserActivityProgressListRequest) returns (GetUserActivityProgressListResponse);

  /**
   * 导出当前小二下可查询商家活动列表
   */
  rpc ExportUserActivityProgressList(ExportUserActivityProgressListRequest) returns (ExportUserActivityProgressListResponse);

  /**
    * 查询当前活动下子任务/周期下拉配置
   */
  rpc GetActivityTaskDimensionOptions(GetActivityTaskDimOptionsRequest) returns (GetActivityTaskDimOptionsResponse);

  /**
   * 查询当前活动下子任务的完成进度(无需分页)
  */
  rpc GetUserSubActivityProgressList(GetUserSubActivityProgressListRequest) returns (GetUserSubActivityProgressListResponse);
}