syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.indicator;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.progress";
option java_outer_classname = "ProgressTestServiceProto";


message QueryUserListByPartyRequest {

  /**
   * 组织id
   */
  string party_id = 1;

  /**
   * 业务标识
   */
  string biz_code = 2;
}

message SellerProfileTriggerRequest {

  repeated int64 user_id = 1;

  repeated string tag = 2;

}


message SellerProfileTriggerResponse {
  /**
 *返回结果码
 */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;

  string data = 3;
}

message QueryUserListByPartyResponse {
  /**
   * 返回结果码
   */
  repeated string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryUserAndOrgDataPermResourcesRequest {

  /**
   * user id
   */
  string user_id = 1;
}

message QueryUserAndOrgDataPermResourcesResponse {
  /**
 * 返回结果码
 */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}

message UpdateEsUserActivityDataFRequest {
  /**
   * user id
   */
  uint64 user_id = 1;

  /**
  * 负责小二
  */
  string owner_staff = 2;

  /**
   * 叶子组织code
   */
  string leaf_party_code = 3;

  /**
   * 变更类型
   */
  string diff_type = 4;

  /**
   * user id
  */
  int32 page_size = 5;

  /**
   * 是否需要更新活动数据
  */
  bool need_update_activity = 6;

}
message UpdateEsUserActivityDataFResponse {
  /**
 * 返回结果码
 */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message ManualSwitchReadVersionRequest {
  /**
   *  key
   */
  string biz_key = 1;

  /**
  *  version
  */
  uint32 version = 2;

  /**
   *  expire
   */
  uint64 expire = 3;

}

message ManualSwitchReadVersionResponse {

  /**
   * 返回结果码
  */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}

message InsertOneReviewIndexRequest {


  /**
   * 指标类型（近30日、近60日、近90日）
   */
  string staff = 1;

  /**
   * 当前读版本
   */
  uint32 version = 2;
  /**
   * 被下发激励活动人数
   */
  uint64 dispatch_cnt_30d = 3;

  /**
    * 报名激励活动人数
 */
  uint64 draw_cnt_30d = 4;

  /**
 * 预估完成商家人数
 */
  uint64 predict_finish_cnt_30d = 5;

  /**
 * 实际完成商家人数
 */
  uint64 fact_finish_cnt_30d = 6;

  /**
 * 被下发激励活动人数
 */
  uint64 dispatch_cnt_60d = 7;

  /**
    * 报名激励活动人数
 */
  uint64 draw_cnt_60d = 8;

  /**
 * 预估完成商家人数
 */
  uint64 predict_finish_cnt_60d = 9;

  /**
 * 实际完成商家人数
 */
  uint64 fact_finish_cnt_60d = 10;

  /**
 * 被下发激励活动人数
 */
  uint64 dispatch_cnt_90d = 11;

  /**
    * 报名激励活动人数
 */
  uint64 draw_cnt_90d = 12;

  /**
 * 预估完成商家人数
 */
  uint64 predict_finish_cnt_90d = 13;

  /**
 * 实际完成商家人数
 */
  uint64 fact_finish_cnt_90d = 14;
}

message InsertOneReviewIndexResponse {

  /**
   * 返回结果码
  */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}

message ScrollUserListByConditionRequest {

  uint64 activity_id = 1;

  uint64 task_id = 2;

  uint32 size = 3;

  /**
    * 预估完成
   */
  uint32 predict_success = 4;
}

message ScrollUserListByConditionResponse {

  /**
   * 返回结果码
  */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}

message TriggerStockActivityEsUpdateRequest {
  /**
任务运行参数
 */
  string task_args = 1;
}
message QuerySlrBelongInfoDataRequest {
   /**
   * 商家id
   */
   uint64 user_id = 1;

   string biz_code = 2;
}
message QuerySlrBelongInfoDataResponse {

  /**
   * 返回结果码
  */
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

}

message QueryIndustryPathRequest {
  string party_id = 1;

  string biz_code = 2;
}

message QueryIndustryPathResponse {
  /**
 * 返回结果码
*/
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message SendActivityReviewMsgResponse {
  /**
* 返回结果码
*/
  string result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message SendActivityReviewMsgRequest {
   /**
   * 商家id
   */
   uint64 activity_id = 1;

   string operator = 2;
}
/**
业务域服务
 */
service ProgressTestService {

  /**
    * 查询小二集合byParty
  */
  rpc QueryUserListByParty (QueryUserListByPartyRequest) returns (QueryUserListByPartyResponse);

  /**
    *  查询当前小二下可管理的小二和组织集合（包含间接可管理的）
   */
  rpc QueryUserAndOrgDataPermResources(QueryUserAndOrgDataPermResourcesRequest) returns (QueryUserAndOrgDataPermResourcesResponse);

  /**
    * 更新当前商家的es归属数据
   */
  rpc UpdateEsUserActivityData(UpdateEsUserActivityDataFRequest) returns(UpdateEsUserActivityDataFResponse);

  /**
    * 手动切换版本
   */
  rpc ManualSwitchReadVersion(ManualSwitchReadVersionRequest) returns(ManualSwitchReadVersionResponse);

  /**
  * 手动读取版本
 */
  rpc ManualReadVersion(ManualSwitchReadVersionRequest) returns(ManualSwitchReadVersionResponse);

  /**
    * 插入一条指标记录
   */
  rpc InsertOneReviewIndex(InsertOneReviewIndexRequest) returns(InsertOneReviewIndexResponse);

  /**
    * scroll商家名单
   */
  rpc ScrollUserListByCondition(ScrollUserListByConditionRequest) returns(ScrollUserListByConditionResponse);

  /**
    * 标签测试
   */
  rpc SellerProfileTrigger (SellerProfileTriggerRequest) returns (SellerProfileTriggerResponse);

  /**
  * 触发ES存量数据更新
   */
  rpc TriggerStockActivityEsUpdate(TriggerStockActivityEsUpdateRequest) returns (ScrollUserListByConditionResponse);
  /**
  * 查询商家归属数据
   */
  rpc QuerySlrBelongInfoData(QuerySlrBelongInfoDataRequest) returns (QuerySlrBelongInfoDataResponse);

  /**
  * 查询行业路径
   */
  rpc QueryIndustryPath(QueryIndustryPathRequest) returns (QueryIndustryPathResponse);
  /**
  * 发送活动复盘消息forTest
   */
  rpc SendActivityReviewMsgTest(SendActivityReviewMsgRequest) returns (SendActivityReviewMsgResponse);

}