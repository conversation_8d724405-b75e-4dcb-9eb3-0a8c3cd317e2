syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.hugeLive;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.hugeLive";
option java_outer_classname = "HugeLiveServiceProto";

message HugeLiveActivityQueryRequest {
  uint64 user_id = 1;
}

message HugeLiveActivityQueryResponse {
  // 结果返回码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;

  repeated HugeLiveActivityBaseInfo activity_base_info_list = 3;
}

message HugeLiveActivityBaseInfo {
  // 活动id
  uint64 activity_id = 1;
  // 活动对外展示标题
  string activity_name = 2;
  // 活动报名状态 1.未报名 2.已报名
  uint32 activity_sign_status = 3;
  // 是否存在可提报场次
  bool enable_submit_task = 4;
  // 活动开始时间
  uint64 activity_start_time = 5;
  // 活动结束时间
  uint64 activity_end_time = 6;
  // 目标匹配方式，1：系统下发 2：人工提报
  uint32 stage_target_match_type = 7;
  // 展示配置
  ShowConfigInfo show_config_info = 8;
  // 剩余可报名任务个数
  uint32 remain_can_submit_task_num = 9;
}

message ShowConfigInfo {
  // 展示图片链接
  string show_pic_url = 1;
  // 活动展示名称
  string show_activity_name = 2;
  // 活动展示副标题
  string show_activity_sub_name = 3;
  // 活动介绍文案
  string activity_desc = 4;
  // 活动规则链接
  string rule_url = 5;
  // banner图
  string banner_pic_url = 6;
}

message HugeLiveTaskInfoQueryRequest {
  // 用户id
  uint64 user_id = 1;
  // 活动id
  uint64 activity_id = 2;
}

message HugeLiveTaskInfoQueryResponse {
  // 结果返回码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 活动任务信息
  HugeLiveActivityTaskInfo data = 3;
}

message HugeLiveActivityTaskInfo {
  // 活动id
  HugeLiveActivityBaseInfo activity_base_info = 3;
  // 参与流程
  string submit_flow_desc = 4;
  // 可提报场次
  repeated HugeLiveTaskInfo task_info_list = 5;
}

message HugeLiveTaskInfo {
  // 场次id（子任务id）
  uint64 task_id = 1;
  // 父任务id
  uint64 parent_task_id = 2;
  // 场次开始时间
  uint64 task_start_time = 3;
  // 场次结束时间
  uint64 task_end_time = 4;
  // 各阶梯目标
  repeated HugeLiveStepInfo step_info_list = 5;
  // 签约协议链接
  string sign_document_url = 6;
}

message HugeLiveStepInfo {
  // 阶梯数
  uint32 stage_num = 1;
  // 本阶梯目标gmv
  uint64 target_gmv = 2;
}

message HugeLiveTaskDetailQueryRequest {
  // 用户id
  uint64 user_id = 1;
  // 活动id
  uint64 activity_id = 2;
  // 场次id（子任务id）
  uint64 task_id = 3;
  // 提报gmv
  uint64 submit_gmv = 4;
}

message HugeLiveTaskDetailQueryResponse {
  // 结果返回码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 任务详情信息
  HugeLiveTaskDetailInfo data = 3;
}

message HugeLiveTaskDetailInfo {
  // 场次基础信息
  HugeLiveTaskInfo task_base_info = 3;
  // 校验项
  CheckItemInfo check_item_info = 4;
  // 命中的阶梯对应的金额
  HitStepInfo hit_step_info = 5;
}

message CheckItemInfo {
  bool exist_magnet_app = 1;
  // 保证金信息
  BailInfo bail_info = 2;
}

message HitStepInfo {
  // 前返奖励金额
  uint64 pre_award_amount = 1;
  // 前返奖励公式说明
  string pre_award_formula_desc = 2;
  // 最低目标
  uint64 minimum_target_value = 3;
  // 实际目标
  uint64 real_target_value = 4;
  // 最低比例
  uint32 min_target_rate = 5;
}

message BailInfo {
  // 是否足额
  bool full_bail = 1;
  // 需要冻结的保证金金额
  uint64 need_bail_amount = 2;
  // 可冻结的保证金金额
  uint64 total_bail_amount = 3;
}

message FlowDetailQueryRequest {
  // 用户id
  uint64 user_id = 1;
  // 活动id
  uint64 activity_id = 2;
  // 场次id（子任务id）
  uint64 task_id = 3;
}

message FlowDetailQueryResponse {
  // 结果返回码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 流程信息
  FlowInfo flow_info = 3;
}

message FlowInfo {
  uint64 task_id = 1;
  // 节点
  repeated FlowNodeInfo flow_node_info_list = 2;
}

message FlowNodeInfo {
  // 步骤编号
  uint32 node_step = 1;
  // 流程节点主标题
  string node_title = 2;
  // 流程节点副标题
  ActionInfo sub_title = 3;
  // 流程节点描述
  ActionInfo detail_info = 4;
  // 是否当前节点
  bool current_node_point = 5;
  // 当前节点状态 0:进行中 1：成功 2:失败  FlowNodeStatusEnum
  uint32 node_status = 6;
}

message ActionInfo {
  // 文案
  string action_content = 1;
  // 行动点标题
  string action_title = 2;
  // 行动点跳转链接
  string action_url = 3;
}

message FlowDetailBatchQueryRequest {
  // 用户id
  uint64 user_id = 1;
  // 场次id（子任务id）
  repeated uint64 task_id = 2;
}


message FlowDetailBatchQueryResponse {
  // 结果返回码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 流程信息
  repeated FlowInfo flow_info = 3;
}

/**
  业务域服务
 */
service HugeLiveService {
  // 查询大场活动列表(有资格的)
  rpc QueryHugeLiveActivityList (HugeLiveActivityQueryRequest) returns (HugeLiveActivityQueryResponse);
  // 查询活动下场次(任务)列表 (可提报的)
  rpc QueryHugeLiveTaskListByActivityId (HugeLiveTaskInfoQueryRequest) returns (HugeLiveTaskInfoQueryResponse);
  // 查询场次(任务)信息详情
  rpc QueryHugeLiveTaskDetail (HugeLiveTaskDetailQueryRequest) returns (HugeLiveTaskDetailQueryResponse);
  // 查询流程节点详情
  rpc QueryHugeLiveTaskFlowDetailInfo(FlowDetailQueryRequest) returns (FlowDetailQueryResponse);
  // 批量查询流程节点详情
  rpc BatchQueryHugeLiveTaskFlowDetailInfo(FlowDetailBatchQueryRequest) returns (FlowDetailBatchQueryResponse);
}