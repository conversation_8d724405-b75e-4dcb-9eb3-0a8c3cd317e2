syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.component;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.component";
option java_outer_classname = "ActivityComponentDomainServiceProto";

message BatchGetComponentInfoRequest {
  /**
   * 组件code列表
   */
  repeated string component_code = 1;
  /**
   * 来源
   */
  string source = 2;
}

message BatchGetComponentInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 组件信息
   */
  repeated ComponentDTO data = 3;
}

message ComponentDTO {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
   * 组件名称
   */
  string text = 2;
  /**
   * 组件类型
   */
  string type = 3;
  /**
   * 所需数据源列表
   */
  repeated string data_source = 4;
  /**
   * 前端协议结构
   */
  string content = 5;
  /**
   * 组件对应cdn链接
   */
  string cdn_url = 6;
  /**
   * 展示配置
   */
  string view_conf = 7;
  /**
   * 样式配置
   */
  string style_conf = 8;
}

message QueryComponentArrangeRequest {
  /**
   * 编排场景
   */
  string scene = 1;
  /**
   * 来源
   */
  string source = 2;
}

message QueryComponentArrangeResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 场景组件编排信息
   */
  SceneComponentArrangeDTO data = 3;
}

message SceneComponentArrangeDTO {
  /**
   * 编排场景ID
   */
  string scene = 1;
  /**
   * 编排场景名称
   */
  string name = 2;
  /**
   * 组件类型
   */
  repeated ComponentArrangeDTO arrange_component = 3;
  /**
   * 实体类型
   */
  uint32 entity_type = 4;
}

message ComponentArrangeDTO {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
   * 开始时间
   */
  uint64 begin_time = 2;
  /**
   * 结束时间
   */
  uint64 end_time = 3;
  /**
   * 是否能降级
   */
  bool can_fallback = 4;
  /**
   * 编排类型
   */
  string type = 5;
  /**
   * 编排名称
   */
  string text = 6;
  /**
   * 展示配置
   */
  string view_conf = 7;
  /**
   * 样式配置
   */
  string style_conf = 8;
  /**
   * 标签
   */
  string tag = 9;
  /**
   * 联动的组件text
   */
  string combine_component_text = 10;
}

message CreateGalaxyPageRequest {
  repeated uint64 activity_id = 1; // 活动id
  string operator = 2; // 操作人
}

message CreateGalaxyPageResponse {
  int32 result = 1; // 返回结果码
  string error_msg = 2; // 错误信息
}

/**
组件服务
 */
service ActivityComponentDomainService {
  /**
   * 查询用户审批单记录
   */
  rpc BatchGetComponentInfo (BatchGetComponentInfoRequest) returns (BatchGetComponentInfoResponse);
  /**
   * 查询场景下组件编排
   */
  rpc QueryComponentArrange (QueryComponentArrangeRequest) returns (QueryComponentArrangeResponse);

  /**
   * 创建活动的天河页面
   */
  rpc CreateGalaxyPage (CreateGalaxyPageRequest) returns (CreateGalaxyPageResponse);
}