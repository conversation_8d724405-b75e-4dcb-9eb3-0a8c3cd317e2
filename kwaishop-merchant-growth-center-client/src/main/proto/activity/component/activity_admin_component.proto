syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.component;
import "activity/component/activity_component.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.component.admin";
option java_outer_classname = "ActivityComponentAdminServiceProto";

/**
 * 翻页查询组件列表请求
 */
message QueryComponentListPageRequest {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
   * 页码
   */
  uint32 page_no = 2;
  /**
   * 页大小
   */
  uint32 page_size = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

/**
 * 翻页查询组件列表响应
 */
message QueryComponentListPageResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 组件信息
   */
  repeated ComponentDTO data = 3;
  /**
   * 总数
   */
  uint64 total = 4;
}

message UpdateComponentInfoRequest {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
 * 组件名称
 */
  string text = 2;
  /**
   * 组件类型
   */
  string type = 3;
  /**
   * 所需数据源列表
   */
  repeated string data_source = 4;
  /**
   * 组件对应cdn链接
   */
  string cdn_url = 5;
  /**
   * 操作人
   */
  string operator = 6;
}

message UpdateComponentInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message CreateComponentRequest {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
   * 组件名称
   */
  string text = 2;
  /**
   * 组件类型
   */
  string type = 3;
  /**
   * 所需数据源列表
   */
  repeated string data_source = 4;
  /**
   * 组件对应cdn链接
   */
  string cdn_url = 5;
  /**
   * 操作人
   */
  string operator = 6;
}

message CreateComponentResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryComponentDatasourceRequest {
  /**
   * 操作人
   */
  string operator = 1;
}

message QueryComponentDatasourceResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 数据源列表
   */
  repeated string data = 3;
}

message UpdateComponentContentRequest {
  /**
   * 组件code
   */
  string component_code = 1;
  /**
   * 内容
   */
  string content = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message UpdateComponentContentResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
组件后台服务
 */
service ActivityComponentAdminService {
  /**
   * 翻页查询组件列表
   */
  rpc QueryComponentListPage (QueryComponentListPageRequest) returns (QueryComponentListPageResponse);
  /**
   * 更新组件信息
   */
  rpc UpdateComponentInfo (UpdateComponentInfoRequest) returns (UpdateComponentInfoResponse);
  /**
   * 更新组件信息
   */
  rpc UpdateComponentContent (UpdateComponentContentRequest) returns (UpdateComponentContentResponse);
  /**
   * 新建组件
   */
  rpc CreateComponent (CreateComponentRequest) returns (CreateComponentResponse);
  /**
   * 数据源列表
   */
  rpc QueryComponentDatasource (QueryComponentDatasourceRequest) returns (QueryComponentDatasourceResponse);
}