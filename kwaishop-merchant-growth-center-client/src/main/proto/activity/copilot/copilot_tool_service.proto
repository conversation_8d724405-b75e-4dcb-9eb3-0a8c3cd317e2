syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.copilot.tool;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.copilot.tool";
option java_outer_classname = "CopilotToolServiceProto";

message GetSellerActivityListRequest {
  // 操作人
  string operator = 1;
  // 商家id
  uint64 seller_id = 2;
}

message GetSellerActivityListResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 商家活动列表
  repeated SellerActivityInfoDTO seller_activity_list = 3;
}

message GetSellerActivityDetailRequest {
  // 操作人
  string operator = 1;
  // 商家id
  uint64 seller_id = 2;
  // 活动id
  uint64 activity_id = 3;
}

message GetSellerActivityDetailResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 商家活动详情
  SellerActivityInfoDTO seller_activity_detail = 3;
}

message SellerActivityInfoDTO {
  // 商家id
  uint64 seller_id = 1;
  // 商家名称
  string seller_name = 2;
  // 活动id
  uint64 activity_id = 3;
  // 活动名称
  string activity_name = 4;
  // 活动开始时间
  string activity_start_time = 5;
  // 活动结束时间
  string activity_end_time = 6;
  // 活动状态
  string activity_status = 7;
  // 所属小二
  string belong_staff = 8;
  // 是否被风控
  string registration_risk = 9;
  // 风控原因
  string registration_risk_reason = 10;
  // 当前是否被风控
  string current_risk = 11;
  // 当前风控原因
  string current_risk_reason = 12;
  // 是否报名活动
  string sign_up_activity = 13;
  // 预估是否能完成
  string predict_finish = 14;
  // 最终是否完成
  string final_finish = 15;
  // 所属行业
  string seller_industry = 17;
}

message GetSellerActivityProgressRequest {
  // 操作人
  string operator = 1;
  // 活动id
  uint64 activity_id = 2;
  // 商家id
  uint64 seller_id = 3;
}

message GetSellerActivityProgressResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 商家活动进度
  repeated SellerSubActivityProgressDTO seller_sub_activity_progress = 3;
}

message SellerSubActivityProgressDTO {
  string sub_activity_name = 1;
  string sub_activity_status = 2;
  string period_start_time = 3;
  string period_end_time = 4;
  repeated SellerTaskDetailDTO task_detail = 5;
  repeated SellerAwardDetailDTO award_detail = 6;
}

message SellerTaskDetailDTO {
  uint64 task_id = 1;
  string task_name = 2;
  string task_status = 3;
  string task_start_time = 4;
  string task_end_time = 5;
  string task_step = 6;
  uint64 parent_task_id = 7;
  repeated SellerIndicatorDetailDTO indicator_detail = 8;
}

message SellerIndicatorDetailDTO {
  uint64 indicator_id = 1;
  string indicator_name = 2;
  string current_value = 3;
  string target_value = 4;
  string status = 5;
  string progress_rate = 6;
}

message SellerAwardDetailDTO {
  uint64 task_id = 1;
  string award_name = 2;
  string award_type = 3;
  string award_value = 4;
  string award_status = 5;
  string risk_reason = 6;
}

message GetIndicatorDetailRequest {
  // 指标ID
  uint64 indicator_id = 1;
  // 指标名称
  string indicator_name = 2;
}

message GetIndicatorDetailResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 指标详情
  repeated IndicatorDetailDTO indicator_detail = 3;
}

message IndicatorDetailDTO {
  // 指标ID
  uint64 indicator_id = 1;
  // 指标名称
  string name = 2;
  // 审核时间
  string audit_time = 3;
  // 更新方式
  string update_type = 4;
  // 完成方式
  string finish_type = 5;
  // 关闭方式
  string close_type = 6;
  // 创建时间
  string create_time = 7;
  // 创建人
  string creator = 8;
  // 描述
  string desc = 9;
  // 操作人
  string modifier = 10;
  // 计算方式
  string calc_type = 11;
  // 单位
  string unit = 12;
  // 标签
  repeated string tags = 13;
  // 指标数据源类型
  string statistics_type = 14;
}

message GetIndicatorDataFetchConfigRequest {
  // 指标ID
  uint64 indicator_id = 1;
}

message GetIndicatorDataFetchConfigResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 数据获取配置
  DataFetchConfigDTO data_fetch_config = 3;
}

message DataFetchConfigDTO {
  uint64 query_id = 1;
  string res_code = 2;
  uint64 show_query_id = 3;
  string show_res_code = 4;
  uint64 base_query_id = 5;
  string base_res_code = 6;
  string dm2_service_code = 7;
  string dm2_res_code = 8;
  string dm2_show_service_code = 9;
  string dm2_show_res_code = 10;
  string dm2_base_service_code = 11;
  string dm2_base_res_code = 12;
  string dm_service_grade = 13;
  string data_fetch_sql = 14;
  string data_fetch_template = 15;
  string data_source_type = 16;
}

service CopilotToolService {
  /**
   * 查询商家活动列表
   */
  rpc GetSellerActivityList(GetSellerActivityListRequest) returns (GetSellerActivityListResponse);
  /**
   * 查询商家活动详情
   */
  rpc GetSellerActivityDetail(GetSellerActivityDetailRequest) returns (GetSellerActivityDetailResponse);
  /**
   * 查询商家子活动进度
   */
  rpc GetUserActivityProgress(GetSellerActivityProgressRequest) returns (GetSellerActivityProgressResponse);
  /**
   * 查询指标详情
   */
  rpc GetIndicatorDetail(GetIndicatorDetailRequest) returns (GetIndicatorDetailResponse);
  /**
   * 查询指标数据获取配置
   */
  rpc GetIndicatorDataFetchConfig(GetIndicatorDataFetchConfigRequest) returns (GetIndicatorDataFetchConfigResponse);
}