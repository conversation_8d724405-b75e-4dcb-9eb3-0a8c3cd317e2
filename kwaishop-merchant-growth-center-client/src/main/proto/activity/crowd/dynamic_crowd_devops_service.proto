syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.crowd;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.crowd";
option java_outer_classname = "DynamicCrowdDevopsServiceProto";

message ProcessDynamicCrowdRequest {
  /**
   * 错误信息
   */
  string part_date = 1;
}

message ProcessDynamicCrowdResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message CleanDiffDataRequest {
  /**
   * id列表
   */
  repeated uint64 diff_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}


message SaveDiffDataItemData {
  /**
 * 活动id
 */
  uint64 activity_id = 1;
  /**
 * 任务id
 */
  uint64 task_id = 2;
  /**
 * 人群id
 */
  uint64 crowd_id = 3;
  /**
 * 用户id
 */
  uint64 user_id = 4;
  /**
 * 回流分区
 */
  string p_date = 5;
}

message SaveDiffDataRequest {
  repeated SaveDiffDataItemData diff_data = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message ReportAbInfoByCrowdReq {
  /**
 * 操作人
 */
  string operator = 1;

  // 人群包来源
  string crowd_source = 4;
  /**
   * 需要上报AB平台的人群包ids
   * 请确保传入的都是配置了AB实验的人群包
   */
  repeated string crowd_id = 2;

  /**
    * 上报停顿时间，防止prt请求QPS过高
   */
  int64 report_interval_time = 3;
}

message ReportAbInfoByCrowdResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message TestDiffCrowdRequest {
  string crowd_code = 1;

  uint64 activity_id = 2;

  uint64 parent_task_id = 3;
}

message TestDiffCrowdResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
 * 业务域服务
 */
service DynamicCrowdDevopsService {
  /**
   * 按日期分区处理动态人群包
   */
  rpc ProcessDynamicCrowdByPartDate (ProcessDynamicCrowdRequest) returns (ProcessDynamicCrowdResponse);
  /**
   * 插入diff数据
   */
  rpc SaveDiffData (SaveDiffDataRequest) returns (ProcessDynamicCrowdResponse);

  /**
   * 清理diff数据
   */
  rpc CleanDiffDataByIds (CleanDiffDataRequest) returns (ProcessDynamicCrowdResponse);
  /**
   * 人群包AB信息手动上报
   */
  rpc ReportAbInfoByCrowd(ReportAbInfoByCrowdReq) returns (ReportAbInfoByCrowdResponse);

  rpc TestDiffCrowd(TestDiffCrowdRequest) returns (TestDiffCrowdResponse);
}