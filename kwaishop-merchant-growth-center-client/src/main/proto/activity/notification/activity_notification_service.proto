syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.notification;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification";
option java_outer_classname = "ActivityNotificationServiceProto";

message DelayNotificationPushMsg {
    /**
     * 推送配置唯一id
     */
    uint64 config_id = 1;
    /**
     * 用户id
     */
    uint64 user_id = 2;
    /**
     * 事件产生时间,用于计算消息延迟
     */
    uint64 event_time = 3;
    /**
     * 消息发送时间,用于比对配置是否更改
     */
    uint64 message_time = 4;
}


message FinishNotificationPushMsg {
    /**
     * 推送配置唯一id
     */
    uint64 config_id = 1;
    /**
     * 用户id
     */
    uint64 user_id = 2;
    /**
     * 触发时机
     */
    uint32 occasion = 3;
    /**
     * 事件产生时间,用于比对配置是否更改
     */
    uint64 event_time = 4;
}

message CreateNotificationConfigRequest {
    /**
     * 活动id
     */
    int64 activity_id = 1;
    /**
     * 实体id
     */
    int64 entity_id = 2;
    /**
     * 实体类型 1、活动 2、任务 3、奖励
     */
    int32 entity_type = 3;
    /**
     * 实体状态
     */
    int32 entity_status = 4;
    /**
     * 渠道 1、通知中心 2、流量中台
     */
    int32 channel = 5;
    /**
     * 触发时机 1、前 2、立即 3、后
     */
    int32 occasion = 6;
    /**
     * 推送模版配置
     */
    string template_config = 7;
    /**
     * 周期性配置
     */
    string period_config = 8;
    /**
     * 扩展字段
     */
    string ext = 9;
    /**
     * 创建者
     */
    string creator = 10;
}

message BatchCreateNotificationConfigRequest {
    /**
     * 活动id
     */
    int64 activity_id = 1;
    /**
     * 实体id
     */
    repeated int64 entity_id = 2;
    /**
     * 实体类型 1、活动 2、任务 3、奖励
     */
    int32 entity_type = 3;
    /**
     * 实体状态
     */
    int32 entity_status = 4;
    /**
     * 渠道 1、通知中心 2、流量中台
     */
    int32 channel = 5;
    /**
     * 触发时机 1、前 2、立即 3、后
     */
    int32 occasion = 6;
    /**
     * 推送模版配置
     */
    string template_config = 7;
    /**
     * 周期性配置
     */
    string period_config = 8;
    /**
     * 扩展字段
     */
    string ext = 9;
    /**
     * 创建者
     */
    string creator = 10;
}

message CreateNotificationConfigResponse {
    int32 result = 1;
    string error_msg = 2;
}

message TriggerNotificationConfigRequest {
    int64 user_id = 1;
    repeated int64 config_id = 2;
    int64 event_time = 3;
    bool hard = 4;
}

message TriggerNotificationConfigResponse {
    int32 result = 1;
    string error_msg = 2;
}

message UpdateNotificationConfigRequest {
    int64 config_id = 1; // 必填 主键id
    int32 status = 2;
    string template_config = 3;
    string period_config = 4;
    int64 entity_id = 5;
    int32 entity_type = 6;
    int32 entity_status = 7;
    int32 occasion = 8;
    int32 channel = 9;
    int64 activity_id = 10;
    string modifier = 11; // 必填 修改人
}

message UpdateNotificationConfigResponse {
    int32 result = 1;
    string error_msg = 2;
}

message InvalidNotificationByConfigIdRequest {
    /**
     * 配置主键id
     */
    repeated int64 config_id = 1;
    /**
     * 修改者
     */
    string modifier = 2;
}

message InvalidNotificationByConfigIdResponse {
    int32 result = 1;
    string error_msg = 2;
}

message InvalidNotificationByConditionRequest {
    int64 activity_id = 1;
    int32 entity_type = 2;
    int32 entity_status = 3;
    string modifier = 4;
}

message InvalidNotificationByConditionResponse {
    int32 result = 1;
    string error_msg = 2;
}

message ValidNotificationByConditionRequest {
    int64 activity_id = 1;
    int32 entity_type = 2;
    int32 entity_status = 3;
    string modifier = 4;
}

message ValidNotificationByConditionResponse {
    int32 result = 1;
    string error_msg = 2;
}

message ValidNotificationByConfigIdRequest {
    /**
     * 配置主键id
     */
    repeated int64 config_id = 1;
    /**
     * 修改者
     */
    string modifier = 2;
}

message ValidNotificationByConfigIdResponse {
    int32 result = 1;
    string error_msg = 2;
}

message GetNotificationConfigDetailByConfigIdRequest {
    /**
     * config_id 必填
     */
    repeated int64 config_id = 1;
}

message GetNotificationConfigDetailByConfigIdResponse {
    int32 result = 1;
    string error_msg = 2;
    GetNotificationConfigDetailByConfigIdDTO data = 3;
}

message GetNotificationConfigsByConditionsRequest {
    /**
     * 活动id 必填
     */
    int64 activity_id = 1;
    /**
     * 实体类型
     */
    int32 entity_type = 2;
    /**
     * 实体状态
     */
    int32 entity_status = 3;
    /**
     * 实体id 必填
     */
    repeated int64 entity_id = 4;
}

message GetNotificationConfigsByConditionsResponse {
    int32 result = 1;
    string error_msg = 2;
    GetNotificationConfigsByConditionsDTO data = 3;
}

message GetNotificationConfigDetailByConfigIdDTO {
    repeated NotificationConfigDTO notification_config = 1;
}

message GetNotificationConfigsByConditionsDTO {
    repeated int64 config_id = 1;
    int32 size = 2;
}

message NotificationConfigDTO {
    int64 config_id = 1;
    /**
     * 活动id
     */
    int64 activity_id = 2;
    /**
     * 实体id
     */
    int64 entity_id = 3;
    /**
     * 实体类型
     */
    int32 entity_type = 4;
    /**
     * 实体状态
     */
    int32 entity_status = 5;
    /**
     * 渠道
     */
    int32 channel = 6;
    /**
     * 触发时机
     */
    int32 occasion = 7;
    /**
     * 推送模版配置
     */
    string template_config = 8;
    /**
     * 周期性配置
     */
    string period_config = 9;
    /**
     * 扩展字段
     */
    string ext = 10;
    /**
     * 创建者
     */
    string creator = 11;
    /**
     * 修改者
     */
    string modifier = 12;
    /**
     * 创建时间
     */
    int64 create_time = 13;
    /**
     * 更新时间
     */
    int64 update_time = 14;
}

message HardDeleteNotificationConfigRequest {
    /**
     * 活动id
     */
    int64 activity_id = 1;
    /**
     * 实体类型
     */
    int32 entity_type = 2;
    /**
     * 实体id
     */
    repeated int64 entity_id = 3;
    /**
     * 操作人
     */
    string modifier = 4;

    /**
     * 渠道
     */
    uint32 channel = 5;
}

message HardDeleteNotificationConfigResponse {
    int32 result = 1;
    string error_msg = 2;
}

message DelNotificationLockRequest {
    int64 user_id = 1;
    int64 config_id = 2;
}

message DelNotificationLockResponse {
    int32 result = 1;
    string error_msg = 2;
}

message BatchUpdateNotificationTemplateAndPeriodConfigRequest {
    /**
     * 配置主键id
     */
    repeated int64 config_id = 1;
    /**
     * 模版配置
     */
    string template_config = 2;
    /**
     * 周期性配置
     */
    string period_config = 3;
    /**
     * 修改者
     */
    string modifier = 4;
}

message QueryUserNotificationInfoRequest {
    /**
     * 商家id
     */
    uint64 user_id = 1;

    /**
    * 参数
    */
    string params = 2;
    /**
    *
    */
    string source = 3 ;
}

message QueryUserNotificationInfoResponse {
    /**
     * 返回结果码
     */
    int32 result = 1;

    /**
     * 错误信息
     */
    string error_msg = 2;

    /**
     * 选品决策展示配置
     */
    UserNotificationShowConfig show_config = 3;
}

message UserNotificationShowConfig {
    /**
     * 是否展示
     */
    bool show = 1;

    /**
     * 展示内容 {"content":"xxxx",jumpUrl:"https://www.xxx.com"}
     */
    string data = 2;
}

message BatchUpdateNotificationTemplateAndPeriodConfigResponse {
    int32 result = 1;
    string error_msg = 2;
}

service ActivityNotificationDomainService {
    /**
     * 创建推送配置
     */
    rpc CreateNotificationConfig (CreateNotificationConfigRequest) returns (CreateNotificationConfigResponse);
    /**
     * 批量创建推送配置
     */
    rpc BatchCreateNotificationConfig (BatchCreateNotificationConfigRequest) returns (CreateNotificationConfigResponse);
    /**
     * 触发推送
     */
    rpc TriggerNotificationConfig (TriggerNotificationConfigRequest) returns (TriggerNotificationConfigResponse);
    /**
     * 更新推送配置
     */
    rpc UpdateNotificationConfig (UpdateNotificationConfigRequest) returns (UpdateNotificationConfigResponse);
    /**
     * 根据配置id失效配置
     */
    rpc InvalidNotificationByConfigId (InvalidNotificationByConfigIdRequest) returns (InvalidNotificationByConfigIdResponse);
    /**
     * 根据配置id生效配置
     */
    rpc ValidNotificationByConfigId (ValidNotificationByConfigIdRequest) returns (ValidNotificationByConfigIdResponse);

    /**
     * 根据条件失效配置
     */
    rpc InvalidNotificationByCondition (InvalidNotificationByConditionRequest) returns (InvalidNotificationByConditionResponse);

    /**
     * 根据条件生效配置
     */
    rpc ValidNotificationByCondition (ValidNotificationByConditionRequest) returns (ValidNotificationByConditionResponse);

    /**
     * 根据主键id获取推送配置详情
     */
    rpc GetNotificationConfigDetailByConfigId (GetNotificationConfigDetailByConfigIdRequest) returns (GetNotificationConfigDetailByConfigIdResponse);
    /**
     * 获取推送配置
     */
    rpc GetNotificationConfigsByConditions (GetNotificationConfigsByConditionsRequest) returns (GetNotificationConfigsByConditionsResponse);
    /**
     * 删除推送配置 (硬删除)
     */
    rpc HardDeleteNotificationConfig (HardDeleteNotificationConfigRequest) returns (HardDeleteNotificationConfigResponse);
    /**
     * 解锁
     */
    rpc DelNotificationLock (DelNotificationLockRequest) returns (DelNotificationLockResponse);
    /**
     * 批量更新推送模版和周期性配置
     */
    rpc BatchUpdateNotificationTemplateAndPeriodConfig (BatchUpdateNotificationTemplateAndPeriodConfigRequest) returns (BatchUpdateNotificationTemplateAndPeriodConfigResponse);
    /**
     * 查询用户触达信息
     */
    rpc QueryUserNotificationInfo (QueryUserNotificationInfoRequest) returns (QueryUserNotificationInfoResponse);
}