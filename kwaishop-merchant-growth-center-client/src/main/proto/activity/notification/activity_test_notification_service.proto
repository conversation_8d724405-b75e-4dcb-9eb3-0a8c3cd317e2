syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.notification;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification";
option java_outer_classname = "ActivityNotificationTestServiceProto";


message DelUserActivityPushLockRequest {
    int64 user_id = 1;
    int64 activity_id = 2;
    int32 entity_type = 3;
}

message DelUserActivityPushLockResponse {
    int32 result = 1;
    string error_msg = 2;
}

service ActivityNotificationTestService {
    // 清空用户活动下推送的所有锁
    rpc DelUserActivityPushLock (DelUserActivityPushLockRequest) returns (DelUserActivityPushLockResponse);
}