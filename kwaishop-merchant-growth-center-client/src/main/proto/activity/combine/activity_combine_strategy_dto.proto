syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.combine;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.combine";
option java_outer_classname = "ActivityStrategyCombineDTOProto";

/**
 * 奖励信息
 */
message AwardInfoDTO {
  /**
  奖励类型
   */
  int32 type = 1;
  /**
  奖励名称
   */
  string title = 2;
  /**
  奖励值
   */
  string value = 3;
  /**
  奖励单位
   */
  string unit = 4;
  /**
  奖励推广页
   */
  string detail_url = 5;
  /**
  icon url
   */
  string icon = 6;
}

/**
总奖励信息
 */
message TotalAwardInfoDTO {
  /**
  状态
   */
  uint32 status = 1;
  /**
  奖励列表
   */
  repeated AwardInfoDTO awards = 2;
}

/**
* 指标信息
 */
message IndicatorInfoDTO {
  /**
  id
   */
  int32 id = 1;
  /**
  title
   */
  string title = 2;
  /**
  指标值
   */
  uint64 value = 3;
  /**
  单位
   */
  string unit = 4;
}

/**
策略信息
 */
message StrategyInfoDTO {
  /**
  策略ID
   */
  uint64 strategy_id = 1;
  /**
  策略说明
   */
  string description = 2;
  /**
  策略状态
   */
  int32 status = 3;
  /**
  奖励文案
   */
  string award_title = 4;
  /**
  是否被风控
   */
  int32 risk = 5;
  /**
  策略开始时间
   */
  uint64 start_time = 6;
  /**
  策略结束时间
   */
  uint64 end_time = 7;
  /**
  奖励发放时间
   */
  uint64 send_time = 8;
  /**
  奖励列表
   */
  repeated AwardInfoDTO awards = 9;
  /**
  指标列表
   */
  repeated IndicatorInfoDTO indicators = 10;
}

/**
Header信息
 */
message HeaderDTO {
  /**
  标题
   */
  string title = 1;
  /**
  副标题
   */
  string sub_title = 2;
}

/**
联合拉新落地页
 */
message UnionPageDTO {
  /**
  是否降级
   */
  uint32 degrade = 1;
  /**
  标题信息
   */
  HeaderDTO header = 2;
  /**
  总指标信息
   */
  repeated IndicatorInfoDTO total_indicator_info = 3;
  /**
  总奖励信息
   */
  repeated TotalAwardInfoDTO total_award_info = 4;
  /**
  策略列表信息
   */
  repeated StrategyInfoDTO strategies = 5;
}