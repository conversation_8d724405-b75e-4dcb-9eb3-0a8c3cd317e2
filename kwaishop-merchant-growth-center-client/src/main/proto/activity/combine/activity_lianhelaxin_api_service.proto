syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.combine;
import "activity/combine/activity_lianhelaxin_api_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.combine";
option java_outer_classname = "ActivityLianHeLaXinApiServiceProto";

message GetActivityInfoRequest {
  /**
  uid
   */
  int64 uid = 1;
  /**
  策略详情-页号
   */
  int32 page_no = 2;
  /**
  策略详情-每页条数
   */
  int32 page_num = 3;
  /**
  活动ID
   */
  uint64 activity_id = 4;
  /**
  调用方来源
   */
  string source = 5;
}

message GetActivityInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  活动信息
   */
  LHLXActivityInfoDTO data = 3;
}

message GetStrategyInfoRequest {
  /**
  uid
   */
  int64 uid = 1;
  /**
  活动类型
   */
  int32 type = 2;
  /**
  活动系列类型
   */
  int32 series_type = 3;
  /**
  策略详情-页号
   */
  int32 page_no = 4;
  /**
  策略详情-每页条数
   */
  int32 page_num = 5;
}

message GetStrategyInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  活动信息
   */
  repeated LHLXStrategyInfoDTO data = 3;
}

message GetStrategyActivityPageInfoRequest {
  /**
  uid
   */
  int64 uid = 1;
  /**
  活动ID
   */
  uint64 activity_id = 2;
  /**
  调用方来源
   */
  string source = 3;
  /**
  活动ID列表
   */
  repeated uint64 activity_ids = 4;
  /**
  查询分销权限信息
   */
  bool query_promoter_info = 5;
  /**
  是否首先查询待报名的策略
   */
  bool first_query_wait_join = 6;
}

message GetStrategyActivityPageInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  活动页信息
   */
  LHLXStrategyActivityPageDTO data = 3;
}

/**
策略报名请求
 */
message StrategyRegistrationRequest {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  策略ID
   */
  uint64 strategy_id = 2;
  /**
  调用方来源
   */
  string source = 3;
  /**
  仅开通自动分销
   */
  bool only_open_auto_promoter = 4;
}
/**
策略报名结果
 */
message StrategyRegistrationResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  策略报名结果DTO
   */
  StrategyRegistrationDTO data = 3;
}

/**
  查询用户是在激活的策略有资格(进行中策略、或者待生效策略)
 */
message QueryUserActiveStrategyRequest {
    /**
      uid
    */
    int64 user_id = 1;
    /**
      活动ID
     */
    repeated int64 activity_ids = 2;
    /**
    是否查询预热期的策略
     */
    bool query_wait_join_strategy = 3;
    /**
      调用来源标识
     */
    string source = 4;
}
message QueryUserActiveStrategyResponse {
    /**
      返回结果码
    */
    int32 result = 1;
    /**
      错误信息
     */
    string error_msg = 2;
    /**
      策略报名结果DTO
     */
    repeated ActiveStrategyInfoDTO data = 3;
}

/**
退出人参果活动
 */
message QuitStrategyRequest {
  // 用户ID
  uint64 user_id = 1;
  // 策略ID
  uint64 strategy_id = 2;
  // 来源标识
  string source = 3;
}
message QuitStrategyResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
}

/**
联合拉新C端聚合服务
 */
service ActivityLianHeLaXinApiService {
  /**
  查询活动详情
   */
  rpc GetActivityInfo (GetActivityInfoRequest) returns (GetActivityInfoResponse);

  /**
  用户参与的策略信息
   */
  rpc GetStrategyInfo (GetStrategyInfoRequest) returns (GetStrategyInfoResponse);

  /**
  查询活动页信息
   */
  rpc GetStrategyActivityPageInfo (GetStrategyActivityPageInfoRequest) returns (GetStrategyActivityPageInfoResponse);

  /**
  报名策略
   */
  rpc RegistrationStrategy(StrategyRegistrationRequest) returns (StrategyRegistrationResponse);

  /**
  查询用户有资格策略的信息
   */
  rpc QueryUserActiveStrategyInfo(QueryUserActiveStrategyRequest) returns (QueryUserActiveStrategyResponse);

  /**
  退出人参果策略，如果开通了自动分销会一并退出自动分销
   */
  rpc QuitStrategy(QuitStrategyRequest) returns (QuitStrategyResponse);
}