syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.combine;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.combine";
option java_outer_classname = "ActivityFlowSpeedApiDTOProto";

message DEMagnetFlowSpeedMessageDTO{
  /**
   * 商家id
   */
  uint64 seller_id = 1;
  /**
   * 拉新数量
   */
  uint32  pull_new_count = 2;
  /**
   * 拉回数量
   */
  uint32  pull_back_count = 3;
  /**
   * 拉活跃数量
   */
  uint32 pull_active_count = 4;
  /**
   * 数据日期
   */
  string p_date = 5;
}

