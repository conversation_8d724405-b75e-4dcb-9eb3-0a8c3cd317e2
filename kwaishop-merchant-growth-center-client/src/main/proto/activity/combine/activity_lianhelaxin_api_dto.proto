syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.combine;

import "activity/combine/activity_combine_strategy_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.combine";
option java_outer_classname = "ActivityLianHeLaXinApiDTOProto";

message LHLXHeaderDTO {
  /**
  标题
   */
  string title = 1;
  /**
  副标题
   */
  string sub_title = 2;
}

message LHLXAwardInfoDTO {
  /**
  奖励类型 1-磁力金牛移动版 2-磁力金牛PC版本 3-快币 4-官方助推
   */
  int32 type = 1;
  /**
  奖励名称
   */
  string title = 2;
  /**
  奖励值
   */
  string value = 3;
  /**
  奖励单位
   */
  string unit = 4;
  /**
  奖励详情链接
   */
  string detail_url = 5;
  /**
  icon URL
   */
  string icon = 6;
  /**
  奖励tag:移动端/PC端
   */
  string tag = 7;
  /**
   奖励文案 奖励币/奖随心推
   */
  string sub_title = 8;
  /**
  奖励状态
   */
  int32 status = 9;
  /**
  奖励发放方式
   */
  string send_type = 10;
  /**
   itemTitle
  */
  string item_title = 11;
  /**
  奖励对应的激励描述文案
 */
  string incentive_desc = 12;
  /**
  详细版的激励描述文案
   */
  string full_incentive_desc = 13;
}

message LHLXTotalAwardInfoDTO {
  /**
  奖励状态 1-已结算 2-待结算
   */
  int32 status = 1;
  /**
  奖励列表
   */
  repeated LHLXAwardInfoDTO awards = 2;
}

message LHLXTotalIndicatorInfoDTO {
  /**
  指标ID
   */
  int64 id = 1;
  /**
  指标名称
   */
  string title = 2;
  /**
  指标值
   */
  string value = 3;
  /**
  单位
   */
  string unit = 4;
  /**
  指标更新时间
  */
  uint64 indicator_update_time = 5;
  /**
  提示信息
   */
  string tips = 6;
  /**
  指标的批注信息
   */
  string comment = 7;
}

message LHLXStrategyInfoDTO {
  /**
  策略ID
   */
  int64 strategy_id = 1;
  /**
  策略状态
   */
  int32 status = 2;
  /**
  策略说明
   */
  string description = 3;
  /**
  奖励文案：奖快币/奖随心推
   */
  string award_title = 4;
  /**
  0-正常 1-被风控
   */
  int32 risk = 5;
  /**
  策略开始时间
   */
  int64 start_time = 6;
  /**
  策略结束时间
   */
  int64 end_time = 7;
  /**
  奖励发放时间
   */
  int64 send_time = 8;
  /**
  奖励信息
   */
  repeated LHLXAwardInfoDTO awards = 9;
  /**
  指标信息
   */
  repeated LHLXTotalIndicatorInfoDTO indicators = 10;
  /**
  供给类型
   */
  int32 supply_type = 11;
  // 是否屏蔽区块
  bool is_shield = 12;
  // 屏蔽后的展示文本
  string shield_text = 13;
  // 屏蔽的描述信息
  string shield_desc = 14;
}

// 活动整体的屏蔽公告
message LHLXActivityShieldNoticeDTO {
  // 是否屏蔽区块
  bool is_shield = 12;
  // 屏蔽后的展示文本
  string shield_text = 13;
  // 屏蔽的描述信息
  string shield_desc = 14;
}

message LHLXActivityInfoDTO {
  /**
  标题
   */
  LHLXHeaderDTO header = 1;
  /**
  总奖励信息
   */
  repeated LHLXTotalAwardInfoDTO total_award_info = 2;
  /**
  总指标信息
   */
  repeated LHLXTotalIndicatorInfoDTO total_indicator_info = 3;
  /**
  策略详情列表
   */
  repeated LHLXStrategyInfoDTO strategies = 4;
  /**
  0-正常 1-降级
   */
  int32 degrade = 5;
  /**
  策略总个数
   */
  int32 total_nums = 6;
  /**
  全局的屏蔽公告
   */
  LHLXActivityShieldNoticeDTO shield_notice = 7;
}

message LHLXStrategyAwardSendRuleItemDTO {
  /**
  门槛
   */
  string beyond = 1;
  /**
  奖励值
   */
  string award_value = 2;
  /**
  额外奖励的描述
   */
  string present_award_value_desc = 3;
  /**
  门槛的数字
   */
  int64 beyond_count = 4;
}


message LHLXIndicatorAwardRuleDTO {
  /**
  指标奖励规则标题
   */
  string rule_title = 1;
  /**
  指标的进度信息
   */
  string progress = 2;
  /**
  具体的规则配置项
   */
  repeated LHLXStrategyAwardSendRuleItemDTO rules = 3;
}

message LHLXStrategyAwardSendRuleDTO {
  /**
  award_config_id
   */
  int64 award_config_id = 2;
  /**
  规则列表，第一版规则表格信息，后面逐步废弃，改用indicator_award_rules
   */
  repeated LHLXStrategyAwardSendRuleItemDTO rules = 3;
  /**
  指标ID
   */
  uint64 indicator_id = 4;
  /**
  指标的名称
   */
  string indicator_name = 5;
  /**
  指标奖励规则
   */
  repeated LHLXIndicatorAwardRuleDTO indicator_award_rules = 6;
}

/**
奖励概况信息
 */
message PresentSummaryInfoDTO {
  /**
  奖励类型
   */
  uint32 award_type = 1;
  /**
  奖励名称
   */
  string award_name = 2;
  /**
  奖励的标签
   */
  string award_tag = 3;
  /**
  奖励icon
   */
  string award_icon = 4;
  /**
  总奖励描述
   */
  string total_present_desc = 5;
  /**
  总奖励的金额
   */
  string total_present_value = 6;
  /**
  总奖励的单位描述
   */
  string total_present_unit_desc = 7;
  /**
  达成的描述信息
   */
  string reach_desc = 8;
}

/**
活动完成情况
 */
message CompleteInfoDTO {
  /**
  指标完成情况
   */
  repeated LHLXTotalIndicatorInfoDTO indicator_info = 1;
  /**
  奖励完成情况
   */
  repeated LHLXAwardInfoDTO award_info = 2;
}

/**
收益概览信息
 */
message IncomeSummaryInfoDTO {
  // 收益达成情况
  repeated IncomeCompleteInfoDTO income_complete_infos = 1;
}

/**
商家参与活动收益达成信息
 */
message IncomeCompleteInfoDTO {
  // 标题
  string title = 1;
  // 区块的编码
  string code = 2;
  // 是否屏蔽区块
  bool is_shield = 3;
  // 屏蔽后的展示文案
  string shield_text = 4;
  string shield_desc = 5;
  // 提示
  string tips = 6;
  // 达成信息
  repeated CompleteInfoDTO complete_infos = 7;
}

/**
联合拉新活动页数据
 */
message LHLXStrategyActivityPageDTO {
  /**
  strategy_id
  */
  int64 strategy_id = 1;
  /**
  策略奖励元信息
   */
  repeated LHLXStrategyAwardSendRuleDTO award_send_rules = 2;
  /**
  历史指标提示
   */
  string history_indicator_tips = 3;
  /**
  策略开始时间
   */
  uint64 strategy_start_time = 4;
  /**
  策略结束时间
   */
  uint64 strategy_end_time = 5;
  /**
  报名时间
   */
  uint64 registration_time = 6;
  /**
  奖励的概况信息
   */
  repeated PresentSummaryInfoDTO present_summary_info = 7;
  /**
  拉新完成情况
   */
  CompleteInfoDTO complete_info = 8;
  /**
  活动ID
   */
  uint64 activity_id = 9;
  /**
  是否有好物联盟的推广权限
   */
  bool is_promoter = 10;
  /**
  策略状态
   */
  uint32 strategy_status = 11;
  /**
  是否使用福袋
   */
  bool use_lucky_bag = 12;
  /**
  报名开始时间
   */
  uint64 registration_start_time = 13;
  /**
  报名结束时间
   */
  uint64 registration_end_time = 14;
  /**
  策略的自定义tips配置
   */
  repeated StrategyTipDTO strategy_tips = 15;
  /**
  当前返回的是否为下期可加入的策略
   */
  bool cur_is_next_join_strategy = 16;
  /*
  不使用福袋时的直播间效果
  */
  int32 broadcast_room_effect = 17;
  /**
  组件灰度控制
   */
  repeated ComponentGrayControlDTO component_gray_control = 18;
  /**
  收益概览信息
   */
  IncomeSummaryInfoDTO income_summary_info = 19;
  /**
  是否开通了自动分销
   */
  bool auto_promoter = 20;
  /**
  是否电商主播
   */
  bool eshop_anchor = 21;
}

/**
组件灰度控制DTO
 */
message ComponentGrayControlDTO {
  /**
  组件编码
   */
  string code = 1;
  /**
  是否展示组件
   */
  bool show = 2;

}

message StrategyTipDTO {
  /**
  场景编码
   */
  string scene_code = 1;
  /**
  文案内容
   */
  string tip_content = 2;
}

/**
策略报名结果DTO
 */
message StrategyRegistrationDTO {

  uint64 activity_id = 1;
}

/**
  用户命中的策略信息
 */
message ActiveStrategyInfoDTO {
    /**
      业务ID
     */
    uint64 activity_id = 1;
    /**
  ``策略ID
   */
    uint64 strategy_id = 2;
    /**
    策略状态
     */
    int32 strategy_status = 3;
    /**
      策略名称
     */
    string strategy_name = 4;
    /**
    0-正常 1-被风控
     */
    int32 risk = 5;
    /**
    策略开始时间
     */
    uint64 strategy_start_time = 6;
    /**
    策略结束时间
     */
    uint64 strategy_end_time = 7;
    /**
    报名开始时间
    */
    uint64 registration_start_time = 8;
    /**
    报名结束时间
     */
    uint64 registration_end_time = 9;
    /**
    报名时间
    */
   uint64 registration_time = 10;
   /**
    供给类型
   */
  int32 supply_type = 11;
  /**
  是否使用福袋
   */
  bool use_lucky_bag = 12;
}