syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.combine;

import "activity/combine/activity_combine_strategy_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.combine";
option java_outer_classname = "ActivityStrategyCombineServiceProto";

message GetUnionAwardInfoRequest {
  /**
  uid
   */
  uint64 uid = 1;
  /**
  策略详情-页号
   */
  int32 page_no = 2;
  /**
  策略详情-每页条数
   */
  int32 page_size = 3;
}

message GetUnionAwardInfoResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  UnionPageDTO data = 3;
}

/**
策略聚合服务
 */
service ActivityStrategyCombineService {
  /**
  联合拉新-查询奖励信息
   */
  rpc GetUnionAwardInfo (GetUnionAwardInfoRequest) returns (GetUnionAwardInfoResponse);
}