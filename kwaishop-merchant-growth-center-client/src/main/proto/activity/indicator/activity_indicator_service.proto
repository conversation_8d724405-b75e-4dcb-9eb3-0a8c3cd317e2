syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.indicator;
import "activity/indicator/indicator_admin_servcie.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.indicator";
option java_outer_classname = "ActivityIndicatorServiceProto";

message GetTaskIndicatorInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  uint64 task_id = 3;
}

message GetTaskIndicatorInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标信息
   */
  TaskIndicatorInfoDTO data = 3;
}

message TaskIndicatorInfoDTO {
  /**
   * 记录列表
   */
  repeated IndicatorInfoDTO indicator_info = 1;
}

message IndicatorInfoDTO {
  /**
  * 用户ID
  */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  uint64 task_id = 3;
  /**
   * 指标ID
   */
  uint64 indicator_id = 4;
  /**
   * 初始值
   */
  uint64 begin_value = 5;
  /**
   * 当前值
   */
  uint64 current_value = 6;
  /**
   * 目标值
   */
  uint64 target_value = 7;
  /**
   * 状态
   */
  uint32 status = 8;
  /**
   * 开始时间
   */
  uint64 start_time = 9;
  /**
   * 结束时间
   */
  uint64 end_time = 10;

  /**
  * 指标更新时间
  */
  uint64 update_time = 11;

  /**
  * 条件集合
  */
  repeated IndicatorConditionSimpleDTO indicator_condition = 12;

  /**
   * 对应IndicatorStatisticsTypeEnum枚举
   */
  uint32 statistics_type = 13;

}

message IndicatorConditionSimpleDTO {
  /**
   * 名称
   */
  string name = 1;
  /**
   * 指标默认值
   */
  string value = 2;
  /**
   * 是否必要条件
   */
  bool necessary = 3;
  /**
  * 值类型 IndicatorValueTypeEnum
  */
  int32 value_type = 4;
}


message GetActivityIndicatorInfoRequest {
  /**
  * 用户ID
  */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;

  /**
  * 是否需要条件集合
  */
  bool need_indicator_condition = 3;
}

message GetActivityIndicatorInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标信息
   */
  TaskIndicatorInfoDTO data = 3;
}

message GetIndicatorConfigRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 2;
}

message GetIndicatorConfigResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标配置
   */
  repeated IndicatorConfigInfoDTO data = 3;
}

message GetUserIndicatorConfigRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 2;
  /**
   * 用户ID
   */
  uint64 user_id = 3;
}

message GetUserIndicatorConfigWithStepRequest{
  /**
 * 活动ID
 */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 2;
  /**
   * 用户ID
   */
  uint64 user_id = 3;
}

message GetUserIndicatorConfigResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标配置
   */
  repeated IndicatorConfigInfoDTO data = 3;
}

message IndicatorConfigInfoDTO {
  /**
  * 活动ID
  */
  uint64 activity_id = 1;
  /**
   * 任务ID
   */
  uint64 task_id = 2;
  /**
   * 配置ID
   */
  uint64 config_id = 3;
  /**
   * 目标值
   */
  uint64 target_value = 4;
  /**
   * 指标ID
   */
  uint64 indicator_id = 5;
  /**
   * 阶梯组成信息
   */
  map<string, int64> component_values = 6;

  /**
   * 动态目标值
   */
  string dynamic_target_value = 7;
}

message IndicatorConfigWithStepDTO{
  uint64 task_id = 1;
  /**
   * 指标ID
   */
  uint64 indicator_id = 2;
  /**
   * 指标名称
   */
  string indicator_name = 3;
  /**
  * 指标单位
  */
  string indicator_unit = 4;
  /**
   * 目标值
   */
  uint64 target_value = 5;
  /**
   * 改指标组成信息
   */
  map<string, int64> component_values = 6;
  /**
   * 配置id
   */
  uint64 config_id = 7;
  /**
  * 指标对外名称
  */
  string indicator_show_name = 8;
}

message GetUserIndicatorConfigWithStepResponse{
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标配置
   */
  repeated IndicatorConfigWithStepDTO data = 3;
}

message GetMultiActivityIndicatorInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  repeated uint64 activity_id = 2;
}

message GetMultiActivityIndicatorInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标配置
   */
  TaskIndicatorInfoDTO data = 3;
}

message IncrementUpdateAllIndicatorValueRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 用户ID
   */
  uint64 user_id = 2;
  /**
   * 指标ID
   */
  uint64 indicator_id = 3;
  /**
   * 事件ID
   */
  string event_id = 4;
}

message IncrementUpdateAllIndicatorValueResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UpdateAllIndicatorByIdentityValueRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 用户ID
   */
  uint64 user_id = 2;
  /**
   * 指标ID
   */
  uint64 indicator_id = 3;
  /**
   * 事件ID
   */
  string event_id = 4;
  /**
   * 指标数值
   */
  uint64 indicator_value = 5;
}

message UpdateAllIndicatorByIdentityValueResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message BatchGetIndicatorMetaRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * ID
   */
  repeated uint64 id = 2;
}

message BatchGetIndicatorMetaResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标元数据
   */
  map<uint64, IndicatorMetaDTO> data = 3;
}

message UserRealtimeLiveDurationRequest{
  /**
   * 商家ID
   */
  int64 user_id = 1;
}

message UserRealtimeLiveDurationResponse{
  /**
* 返回结果码
*/
  uint32 result = 1;
  /**
   * 返回错误信息
   */
  string error_msg = 2;
  /**
   * 开播时长
   */
  int64 data = 3;
}

message QueryUserLiveDurationByDayRequest {
  /**
   * 商家ID
   */
  int64 user_id = 1;
  /**
   * 日期列表
   */
  repeated string date = 2;
}
message QueryUserLiveDurationByDayResponse {
  /**
  * 返回结果码
  */
  uint32 result = 1;
  /**
   * 返回错误信息
   */
  string error_msg = 2;
  /**
   * 开播时长
   */
  repeated UserLiveDurationDTO data = 3;
}

message UserLiveDurationDTO {
  /**
   * 日期
   */
  string date = 1;
  /**
   * 开播时长
   */
  uint64 live_duration = 2;
}

service ActivityIndicatorDomainService {
  /**
   * 批量查询指标元数据
   */
  rpc BatchGetIndicatorMeta(BatchGetIndicatorMetaRequest) returns (BatchGetIndicatorMetaResponse);
  /**
   * 获取用户某个任务的指标信息
   */
  rpc GetTaskIndicatorInfo (GetTaskIndicatorInfoRequest) returns (GetTaskIndicatorInfoResponse);
  /**
   * 获取用户某个活动的指标信息
   */
  rpc GetActivityIndicatorInfo (GetActivityIndicatorInfoRequest) returns (GetActivityIndicatorInfoResponse);
  /**
   * 批量获取用户某些活动的指标信息
   */
  rpc GetMultiActivityIndicatorInfo (GetMultiActivityIndicatorInfoRequest) returns (GetMultiActivityIndicatorInfoResponse);
  /**
   * 获取指标配置信息
   */
  rpc GetIndicatorConfig(GetIndicatorConfigRequest) returns (GetIndicatorConfigResponse);
  /**
   * 获取指标配置信息
   */
  rpc GetUserIndicatorConfig(GetUserIndicatorConfigRequest) returns (GetUserIndicatorConfigResponse);
  /**
   * 根据指标ID，增量更新所有任务的指标值(+1)
   */
  rpc IncrementUpdateAllIndicatorValue(IncrementUpdateAllIndicatorValueRequest) returns (IncrementUpdateAllIndicatorValueResponse);
  /**
   * 根据指标ID，按照所带的指标值，更新所有任务的指标值
   */
  rpc UpdateAllIndicatorByIdentityValue(UpdateAllIndicatorByIdentityValueRequest) returns (UpdateAllIndicatorByIdentityValueResponse);
  /**
   * 根据指标ID，按照所带的指标值，更新所有任务的指标值（限制场景）
   */
  rpc UpdateAllIndicatorByIdentityValueWithSceneLimited(UpdateAllIndicatorByIdentityValueRequest) returns (UpdateAllIndicatorByIdentityValueResponse);

  /**
   * 获取指标配置信息
   */
  rpc GetUserIndicatorConfigWithStep(GetUserIndicatorConfigWithStepRequest) returns (GetUserIndicatorConfigWithStepResponse);
  /**
   * 查询商家每日实时开播时长
   */
  rpc QueryUserRealtimeLiveDuration(UserRealtimeLiveDurationRequest)returns(UserRealtimeLiveDurationResponse);
  /**
   * 批量查询用户各日开播时长
   */
  rpc QueryUserLiveDurationByDay(QueryUserLiveDurationByDayRequest) returns (QueryUserLiveDurationByDayResponse);
}