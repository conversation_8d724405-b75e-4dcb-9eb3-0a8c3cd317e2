syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.indicator;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.indicator";
option java_outer_classname = "IndicatorAdminServiceProto";


message QueryIndicatorRequest {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
}

message QueryIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 指标数据
   */
  IndicatorMetaDTO data = 3;
}

message IndicatorMetaDTO {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 指标ID
   */
  string name = 2;
  /**
   * 审核时间
   */
  uint64 audit_time = 3;
  /**
   * 更新方式
   */
  uint32 update_type = 4;
  /**
   * 完成方式
   */
  uint32 finish_type = 5;
  /**
   * 关闭方式
   */
  uint32 close_type = 6;
  /**
   * 是否同步风控
   */
  uint32 sync_risk = 7;
  /**
   * 状态
   */
  uint32 status = 8;
  /**
   * 条件
   */
  string indicator_condition = 9;
  /**
   * 创建时间
   */
  uint64 create_time = 10;
  /**
   * 创建人
   */
  string creator = 11;
  /**
   * 描述
   */
  string desc = 12;
  /**
   * 版本
   */
  uint32 version = 13;
  /**
   * 操作人
   */
  string modifier = 14;
  /**
   * 指标来源
   */
  uint32 source = 15;
  /**
   * dataManage查询配置
   */
  string data_manage_config = 16;
  /**
   * 指标来源
   */
  uint32 type = 17;
  /**
   * 指标来源
   */
  uint32 calc_type = 18;
  /**
   * 指标单位
   */
  string unit = 19;
  /**
   * 标签
   */
  repeated string tags = 20;
  /**
   * 指标对外名称
   */
  string show_name = 21;
  /**
   * 基值表头
   */
  string base_header = 22;
  /**
   * 基值jsonKey
   */
  string base_header_json_key = 23;
  /**
   * 基值数据转存储倍数
   */
  string base_data_multiple = 24;
  /**
   * 指标提示
   */
  string indicator_tip = 25;
  /**
   * 指标返点方式
   */
  repeated uint32 return_type = 26;
  /**
   * 指标玩法配置
   */
  repeated ActivityPatternTypeConfig activity_pattern_type_config = 27;
  /**
   * 是否小数
   */
  bool decimal = 28;
  /**
   * 不支持返点的奖励类型
   */
  repeated int32 return_award_type_blacklist = 29;

  /**
  * 对应IndicatorStatisticsTypeEnum枚举
  */
  uint32 statistics_type = 30;

  /**
   * 指标默认绑定行动点信息
   */
  IndicatorFrontConfig indicator_front_config = 31;

  /**
   * 基值精度
   */
  string base_data_scale = 32;
}

message CreateIndicatorRequest {
  /**
   * ID
   */
  uint64 id = 1;
  /**
   * 名称
   */
  string name = 2;
  /**
   * 描述
   */
  string desc = 3;
  /**
   * 审核时间
   */
  uint64 audit_time = 4;
  /**
   * 更新方式
   */
  uint32 update_type = 5;
  /**
   * 完成方式
   */
  uint32 finish_type = 6;
  /**
   * 关闭方式
   */
  uint32 close_type = 7;
  /**
   * 同步风控
   */
  uint32 sync_risk = 8;
  /**
   * 条件集合
   */
  repeated IndicatorConditionDTO condition = 9;
  /**
   * 创建人
   */
  string operator = 10;
  /**
   * 指标来源
   */
  uint32 source = 11;
  /**
   * dataManage查询配置
   */
  string data_manage_config = 12;
  /**
   * 指标来源
   */
  uint32 type = 13;
  /**
   * 指标来源
   */
  uint32 calc_type = 14;
  /**
   * 指标单位
   */
  string unit = 15;
  /**
   * 指标标签
   */
  repeated string tags = 16;
  /**
   * 指标对外名称
   */
  string show_name = 17;
  /**
   * 指标基值表头
   */
  string base_header = 18;
  /**
   * 指标基值jsonKey
   */
  string base_header_json_key = 19;
  /**
   * 基值数据转存储倍数
   */
  string base_data_multiple = 20;
  /**
   * 指标提示
   */
  string indicator_tip = 21;

  /**
   * 指标基值数据精度
   */
  string base_data_scale = 22;
}

message IndicatorConditionDTO {
  /**
   * 名称
   */
  string name = 1;
  /**
   * 指标默认值
   */
  uint64 value = 2;
  /**
   * 是否必要条件
   */
  bool necessary = 3;
  /**
  * 值类型 IndicatorValueTypeEnum
  */
  int32 value_type = 4;
  /**
   * 表单名称
   */
  string label = 5;
  /**
   * 提示语
   */
  string placeholder = 6;
  /**
   * 是否展示表单
   */
  bool display_form = 7;
  /**
   * 下面提示内容
   */
  string tips_content = 8;
  /**
   * 下面提示跳转
   */
  string tips_button_jump_url = 9;
  /**
   * 下面提示跳转文案
   */
  string tips_button_text = 10;
  /**
   * 是否展示表单
   */
  bool display_in_award = 11;
}

message CreateIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryAllIndicatorRequest {
  /**
   * 当前页数
   */
  uint32 page_no = 1;
  /**
   * 每页大小
   */
  uint32 page_size = 2;
  /**
   * 主键ID
   */
  uint64 id = 3;
  /**
   * 指标名称
   */
  string name = 4;
  /**
   * 负责人
   */
  string owner = 5;
  /**
   * 业务类型
   */
  string biz_type = 6;
  /**
   * 活动玩法
   */
  string patter_type = 7;
  /**
   * 使用平台
   */
  string platform = 8;
}

message QueryAllIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  repeated IndicatorMetaDTO data = 3;
  /**
   * 总数
   */
  uint64 total = 4;
}

message UpdateIndicatorConditionRequest {
  /**
   * 指标id
   */
  uint64 indicator_id = 1;
  /**
 * 指标来源
 */
  uint32 source = 2;
  /**
   * 条件集合
   */
  repeated IndicatorConditionDTO condition = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message UpdateIndicatorRequest {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
    * 名称
    */
  string name = 2;
  /**
   * 描述
   */
  string desc = 3;
  /**
   * 审核时间
   */
  uint64 audit_time = 4;
  /**
   * 更新方式
   */
  uint32 update_type = 5;
  /**
   * 完成方式
   */
  uint32 finish_type = 6;
  /**
   * 关闭方式
   */
  uint32 close_type = 7;
  /**
   * 同步风控
   */
  uint32 sync_risk = 8;
  /**
   * 条件集合
   */
  repeated IndicatorConditionDTO condition = 9;
  /**
   * 操作人
   */
  string operator = 10;
  /**
   * 指标来源
   */
  uint32 source = 11;
  /**
   * dataManage查询配置
   */
  string data_manage_config = 12;
  /**
   * 指标来源
   */
  uint32 type = 13;
  /**
   * 指标来源
   */
  uint32 calc_type = 14;
  /**
   * 指标单位
   */
  string unit = 15;
  /**
  * 指标标签
  */
  repeated string tags = 16;
  /**
   * 指标对外名称
   */
  string show_name = 17;
  /**
   * 指标基值表头
   */
  string base_header = 18;
  /**
   * 指标基值jsonKey
   */
  string base_header_json_key = 19;
  /**
   * 基值数据转存储倍数
   */
  string base_data_multiple = 20;
  /**
   * 指标提示
   */
  string indicator_tip = 21;
  /**
   * 指标基值精度
   */
  string base_data_scale = 22;
}

message UpdateIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message CopyIndicatorRequest {
  /**
   * 指标id
   */
  int64 indicator_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message CopyIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryIndicatorTagsRequest {

}

message QueryIndicatorTagsResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 指标标签
   */
  string data = 3;
}

message UpdateIndicatorExtraRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 操作指标
   */
  uint64 indicator_id = 2;
  /**
   * 返点方式
   */
  repeated uint32 return_type = 3;
  /**
   * 指标玩法配置
   */
  repeated ActivityPatternTypeConfig activity_pattern_type_config = 4;
  /**
   * 是否小数
   */
  bool decimal = 5;
  /**
   * 不支持返点的奖励类型
   */
  repeated uint32 return_award_type_blacklist = 6;
  /**
   * 指标默认行动点信息
   */
  IndicatorFrontConfig indicator_front_config = 7;
}

message ActivityPatternTypeConfig {
  /**
   * 玩法
   */
  string activity_pattern_type = 1;
  /**
   * 计算方式
   */
  repeated string calculate_type = 2;
  /**
   * 返点方式
   */
  repeated uint32 return_type = 3;
  /**
   * 计算公式
   */
  repeated uint32 calculate_formula = 4;
}

message IndicatorFrontConfig {
  /**
   * 任务标题
   */
  string title = 1;
  /**
   * 行动点文案
   */
  string action_text = 2;
  /**
   * 行动点链接
   */
  string action_jump_url = 3;
}

message UpdateIndicatorExtraResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateIndicatorDataManageRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 操作指标
   */
  uint64 indicator_id = 2;
  /**
   * 取数配置
   */
  string data_manage_config = 3;
}

message UpdateIndicatorDataManageResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message OnlineIndicatorDataManageRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 操作指标
   */
  uint64 indicator_id = 2;
}

message OnlineIndicatorDataManageResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message OfflineIndicatorDataManageRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 操作指标
   */
  uint64 indicator_id = 2;
  /**
   * 指标版本
   */
  int32 version = 3;
}

message OfflineIndicatorDataManageResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryIndicatorStatisticsConfigRequest {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message QueryIndicatorStatisticsConfigResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 统计配置
   */
  string data = 3;
}

message UpdateIndicatorStatisticsConfigRequest {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
  /**
   * 统计配置
   */
  string config = 3;

  /**
   * 指标统计类型
   */
  int32 type = 4;
}

message UpdateIndicatorStatisticsConfigResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

service IndicatorAdminService {
  /**
   * 插入指标
   */
  rpc CreateIndicator (CreateIndicatorRequest) returns (CreateIndicatorResponse);
  /**
   * 查询指标
   */
  rpc QueryIndicator (QueryIndicatorRequest) returns (QueryIndicatorResponse);
  /**
   * 查询所有指标
   */
  rpc QueryAllIndicator (QueryAllIndicatorRequest) returns (QueryAllIndicatorResponse);
  /**
   * 更新指标
   */
  rpc UpdateIndicator (UpdateIndicatorRequest) returns (UpdateIndicatorResponse);
  /**
   * 更新指标source和condition
   */
  rpc UpdateIndicatorCondition (UpdateIndicatorConditionRequest) returns (UpdateIndicatorResponse);
  /**
   * 更新指标附加信息
   */
  rpc UpdateIndicatorExtra (UpdateIndicatorExtraRequest) returns (UpdateIndicatorExtraResponse);
  /**
   * 更新指标取数配置
   */
  rpc UpdateIndicatorDataManage (UpdateIndicatorDataManageRequest) returns (UpdateIndicatorDataManageResponse);

  /**
   * 上线指标取数配置
   */
  rpc OnlineIndicatorDataManage (OnlineIndicatorDataManageRequest) returns (OnlineIndicatorDataManageResponse);

  /**
   * 下线指标取数配置
   */
  rpc OfflineIndicatorDataManage (OfflineIndicatorDataManageRequest) returns (OfflineIndicatorDataManageResponse);
  /**
   * 查询指标标签
   */
  rpc QueryIndicatorTags(QueryIndicatorTagsRequest) returns(QueryIndicatorTagsResponse);
  /**
   * 查询统计配置
   */
  rpc QueryIndicatorStatisticsConfig(QueryIndicatorStatisticsConfigRequest) returns (QueryIndicatorStatisticsConfigResponse);
  /**
   * 更新统计配置
   */
  rpc UpdateIndicatorStatisticsConfig(UpdateIndicatorStatisticsConfigRequest) returns (UpdateIndicatorStatisticsConfigResponse);
  /**
   * 复制指标
   */
  rpc CopyIndicator(CopyIndicatorRequest) returns (CopyIndicatorResponse);
}