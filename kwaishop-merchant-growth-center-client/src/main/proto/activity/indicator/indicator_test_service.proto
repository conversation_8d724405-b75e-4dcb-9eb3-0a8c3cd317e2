syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.indicator;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.indicator";
option java_outer_classname = "IndicatorTestServiceProto";

message CreateIndicatorRecordRequest {
  /**
 * user_id
 */
  int64 user_id = 1;
  /**
  活动ID
   */
  int64 activity_id = 2;
  /**
  实体ID
   */
  int64 entity_id = 3;
  /**
  配置ID
   */
  int64 config_id = 4;
  /**
  指标ID
   */
  int64 indicator_id = 5;
  /**
  指标子ID
   */
  string sub_indicator_id = 6;
  /**
  起始值
   */
  int64 begin_value = 7;
  /**
  当前值
   */
  int64 current_value = 8;
  /**
  目标值
   */
  int64 target_value = 9;
}

message CreateIndicatorRecordResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateIndicatorRecordRequest {
  /**
   * user_id
   */
  int64 user_id = 1;
  /**
   * id
   */
  int64 id = 2;
  /**
  起始值
   */
  int64 begin_value = 3;
  /**
  当前值
   */
  int64 current_value = 4;
  /**
  目标值
   */
  int64 target_value = 5;
  /**
  状态
   */
  int32 status = 6;
}

message UpdateIndicatorRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message MockMultiTaskIndicatorInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 3;
  /**
   * 结束时间
   */
  uint64 end_time = 4;
  /**
   * 开始时间
   */
  uint64 start_time = 5;
  /**
   * 状态
   */
  uint32 status = 6;
  /**
   * 操作人
   */
  string operator = 7;
}

message MockMultiTaskIndicatorInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message TriggerUserIndicatorStatusCheckRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 操作人
   */
  string operator = 2;

  int64 activity_id = 3;
}

message TriggerUserIndicatorStatusCheckResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message OneKeyFinishAllChildTaskRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 父任务ID
   */
  uint64 parent_task_id = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message OneKeyFinishAllChildTaskResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UpdateIndicatorValueRequest {
  /**
  * 用户ID
  */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  uint64 task_id = 3;
  /**
   * 指标ID
   */
  uint64 indicator_id = 4;
  /**
   * 指标数值
   */
  uint64 value = 5;
  /**
   * 是否要mock，指定更新+跳过更新条件过滤
   */
  bool mock = 6;
  /**
   * 父任务ID，有的话，统一更新所有子任务的同一个指标
   */
  uint64 parent_task_id = 7;
  /**
   * 操作人
   */
  string operator = 8;
}

message UpdateIndicatorValueResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message QueryRealtimeInvestIndicatorRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 开始时间
   */
  uint64 start_time = 2;
  /**
   * 结束时间
   */
  uint64 end_time = 3;
}

message QueryRealtimeInvestIndicatorResponse {
  /**
   * 返回结果码
   */
  uint32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 风控后GMV
   */
  uint64 risk_gmv = 3;
  /**
   * 商业化投流
   */
  uint64 cost = 4;
  /**
   * 自播GMV
   */
  uint64 self_gmv = 5;
}

message QueryRealtimeIndicatorRequest {
  uint64 user_id = 1;

  uint64 activity_id = 2;

  uint64 task_id = 3;

  uint64 indicator_id = 4;
}

message QueryRealtimeIndicatorResponse {
  /**
   * 返回结果码
   */
  uint32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 风控后GMV
   */
  uint64 indicator_value = 3;
}

message QueryUserSingleFansLTVRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 开始时间
   */
  uint64 start_time = 2;
  /**
   * 结束时间
   */
  uint64 end_time = 3;
  /**
   * 所需数据类型
   */
  uint32 data_type = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message QueryUserSingleFansLTVResponse {
  /**
   * 返回结果码
   */
  uint32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  QueryDataDTO data = 3;
}

message QueryDataDTO {
  /**
   * 单粉LTV90
   */
  uint64 single_fans_ltv = 1;
  /**
   * 基期有效天数(剔除节假日大促)
   */
  string effective_days = 2;
  /**
   * 有效直播天数
   */
  string effective_live_days = 3;
  /**
   * 数据类型
   */
  uint32 data_type = 4;
}

message QuerySelectionTraceCrowdRequest {
  /**
   * 筛选包ID
   */
  uint64 crowd_id = 1;
  /**
   * 赛道ID
   */
  uint32 trace_id = 2;
}

message QuerySelectionTraceCrowdResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户列表
   */
  repeated uint64 user_id = 3;
}

message DeleteSelectionTraceCrowdRequest {
  /**
   * 筛选包ID
   */
  repeated uint64 crowd_id = 1;
  /**
   * 用户ID
   */
  uint64 user_id = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message DeleteSelectionTraceCrowdResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UpdateIndicatorInfoRequest {
  /**
   * id
   */
  uint64 id = 1;
  /**
   * 基期开始时间
   */
  uint64 fix_start_time = 2;
  /**
   * 基期结束时间
   */
  uint64 fix_end_time = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message UpdateIndicatorInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message UpdateActivityIndicatorCompleteTypeRequest {
  /**
   * 活动ID
   */
  int64 activity_id = 1;
  /**
   * 指标ID
   */
  int64 indicator_id = 2;
  /**
   * 指标类型
   */
  int32 type = 3;
  /**
   * 结算类型
   */
  int32 execute_complete_type = 4;
  /**
   * 指标配置ID
   */
  int64 id = 5;
  /**
   * 操作人
   */
  string operator = 6;
}

message UpdateActivityIndicatorCompleteTypeResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message ExportUserBasicDataRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 导出用户ID
   */
  repeated uint64 user_id = 3;
  /**
   * 导出基值指标
   */
  repeated uint64 indicator_id = 4;
}

message ExportUserBasicDataResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 导出数据
   */
  string data = 3;
}

message UpdateIndicatorConfigInfoRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 指标配置ID
   */
  int64 indicator_config_id = 2;
  /**
   * 动态目标规则
   */
  string dynamic_target_value = 3;
  /**
   * 统计方式
   */
  int32 calc_range_type = 4;
}

message UpdateIndicatorConfigInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}
//long userId,
//long indicatorId,
//long indicatorConfigId,
//long taskStartTime,
//long taskEndTime
message CustomCalculationIndicatorRequest {
  int64 user_id = 1;
  int64 indicator_id = 2;
  int64 indicator_config_id = 3;
  int64 task_start_time = 4;
  int64 task_end_time = 5;
}

message CustomCalculationIndicatorResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标数值
   */
  int64 indicator_final_value = 3;
}

message CacheProcessRequest {
  int64 op_type = 1;
  int64 indicator_id = 2;
  repeated string add_date = 3;
  repeated string delete_date = 4;
}

message CalculationIndicatorNotifyRequest {
  int64 order_id = 1;
  int64 seller_id = 2;
  int64 real_seller_id = 3;
  int64 item_id = 4;
  string sub_business_type_v2 = 5;
  string order_pay_date = 6;
  int64 order_pay_time = 7;
  int64 is_risk_flag = 8;
}

message CalcUserAwardValueRequest {
  //long userId, long activityId, long taskId, long awardConfigId
  int64 user_id = 1;
  int64 activity_id = 2;
  int64 task_id = 3;
  int64 award_config_id = 4;
}

message TrAmountQueryRequest {
  //Map<Long, OrderSettlementTrAmount> queryTrAmount(long userId, List<Long> orderIdList);
  int64 user_id = 1;
  repeated int64 order_id = 2;
}

message MockCalcUserAwardValueRequest {
  // Long id = null;
  // Long userId = null;
  // Long mockCrowdId = null;
  int64 id = 1;
  int64 user_id = 2;
  int64 mock_crowd_id = 3;
  repeated int64 mock_user_id = 4;
}

message HardDeleteOrderDetailRequest {
  int64 user_id = 1;
}

message CacheProcessResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 指标数值
   */
  string op_result_msg = 3;
}

message GetShopDecorateStatusRequest {
  /**
   * userId
   */
  int64 user_id = 1;
}

message GetShopDecorateStatusResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message Param {
  string name = 1;
  repeated string value = 2;
}

// long queryId, List<Param> paramList
message DdmGeneralQueryRequest {
  int64 query_id = 1;
  repeated Param param_list = 2;
}

message DdmGeneralQueryResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 查询返回数值
   */
  string sdm_result = 3;
}

message DeleteOfflineIndicatorFinishKeyReq {
  string operator = 1;

  // 指定删除某个分片的key
  int64 shard_index = 2;

  string biz_date = 3;

  int64 all_shard_num = 4;
}

message DeleteOfflineIndicatorFinishKeyResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message TestQueryBaseDataRequest {
  uint64 user_id = 1;
  string base_indicator_config_json = 2;
  string indicator_config_json = 3;
}

message TestQueryBaseDataResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

/**
业务域服务
 */
service IndicatorTestService {
  /**
   * 插入用户指标记录
   */
  rpc CreateIndicatorRecord (CreateIndicatorRecordRequest) returns (CreateIndicatorRecordResponse);
  /**
   * 更新用户指标记录
   */
  rpc UpdateIndicatorRecord (UpdateIndicatorRecordRequest) returns (UpdateIndicatorRecordResponse);
  /**
   * 批量更改任务指标状态
   */
  rpc MockMultiTaskIndicatorInfo (MockMultiTaskIndicatorInfoRequest) returns (MockMultiTaskIndicatorInfoResponse);
  /**
   * 触发用户指标过期校验
   */
  rpc TriggerUserIndicatorStatusCheck (TriggerUserIndicatorStatusCheckRequest) returns (TriggerUserIndicatorStatusCheckResponse);
  /**
   * 子任务一键完成
   */
  rpc OneKeyFinishAllChildTask (OneKeyFinishAllChildTaskRequest) returns (OneKeyFinishAllChildTaskResponse);
  /**
   * 更新某个指标的数值
   */
  rpc UpdateIndicatorValue (UpdateIndicatorValueRequest) returns (UpdateIndicatorValueResponse);
  /**
   * 查询某个用户的实时对投指标数据
   */
  rpc QueryRealtimeInvestIndicator (QueryRealtimeInvestIndicatorRequest) returns (QueryRealtimeInvestIndicatorResponse);
  /**
   * 查询某个用户的实时对投指标数据
   */
  rpc QueryRealtimeIndicator (QueryRealtimeIndicatorRequest) returns (QueryRealtimeIndicatorResponse);
  /**
   * 查询某个筛选包赛道人群
   */
  rpc QuerySelectionTraceCrowd (QuerySelectionTraceCrowdRequest) returns (QuerySelectionTraceCrowdResponse);
  /**
   * 删除某个筛选包赛道人群
   */
  rpc DeleteSelectionTraceCrowd (DeleteSelectionTraceCrowdRequest) returns (DeleteSelectionTraceCrowdResponse);
  /**
   * 更新基期指标时间
   */
  rpc UpdateIndicatorInfo(UpdateIndicatorInfoRequest) returns (UpdateIndicatorInfoResponse);
  /**
   * 查询某个用户的单粉LTV
   */
  rpc QueryUserSingleFansLTV (QueryUserSingleFansLTVRequest) returns (QueryUserSingleFansLTVResponse);
  /**
   * 更新活动下指标配置的结算类型
   */
  rpc UpdateActivityIndicatorCompleteType(UpdateActivityIndicatorCompleteTypeRequest) returns (UpdateActivityIndicatorCompleteTypeResponse);
  /**
   * 导出用户基值数据
   */
  rpc ExportUserBasicData(ExportUserBasicDataRequest) returns (ExportUserBasicDataResponse);
  /**
   * 更新指标配置
   */
  rpc UpdateIndicatorConfigInfo(UpdateIndicatorConfigInfoRequest) returns (UpdateIndicatorConfigInfoResponse);
  /**
   * 动态指标计算
   */
  rpc QueryUserActivityCustomCalculationIndicatorValue(CustomCalculationIndicatorRequest) returns (CustomCalculationIndicatorResponse);

  rpc ProcessCalculationIndicatorCacheValue(CacheProcessRequest) returns (CacheProcessResponse);

  rpc ProcessCalculationIndicatorNotifyValue(CalculationIndicatorNotifyRequest) returns (CacheProcessResponse);
  /**
   * 算奖
   */
  rpc CalcUserAwardValue(CalcUserAwardValueRequest) returns (CacheProcessResponse);
  //Map<Long, OrderSettlementTrAmount> queryTrAmount(long userId, List<Long> orderIdList);
  /**
   * 查询结算：结算GMV和返佣金额
   */
  rpc QueryTrAmount(TrAmountQueryRequest) returns (CacheProcessResponse);
  /**
   * mock返佣金额的计算
   */
  rpc MockCalcUserAwardValue(MockCalcUserAwardValueRequest) returns (CacheProcessResponse);
  /**
   * 测试数据清理
   */
  rpc HardDeleteOrderDetailByUserId(HardDeleteOrderDetailRequest) returns (CacheProcessResponse);

  /**
   * 获取店铺状态
   */
  rpc GetShopDecorateStatus(GetShopDecorateStatusRequest) returns (GetShopDecorateStatusResponse);

  rpc SdmGeneralQueryMap(DdmGeneralQueryRequest) returns (DdmGeneralQueryResponse);
  /**
   * 删除离线指标每日更新分片key删除(谨慎操作)
   */
  rpc DeleteOfflineIndicatorUpdateFinishKey(DeleteOfflineIndicatorFinishKeyReq) returns (DeleteOfflineIndicatorFinishKeyResponse);

  rpc TestQueryBaseData(TestQueryBaseDataRequest) returns (TestQueryBaseDataResponse);
}