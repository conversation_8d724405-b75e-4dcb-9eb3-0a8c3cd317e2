syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.admin;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.admin";
option java_outer_classname = "ActivityConfigPlatformServiceProto";

message ActivityAdminCommonResponse{

  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message SaveActivityAdminDomainRequest{
  // 操作人
  string operator = 1;
  // 配置
  string config = 2;
  // 活动类型
  string type = 3;
  // 标识
  int32 flag = 4;
}

message GetActivityComponentConfigRequest{
  // 操作人
  string operator = 1;
  // 应用场景
  string application = 2;
}

message QueryActivityAdminDomainRequest{
  // 活动id
  uint64 activity_id = 1;
  // 操作人
  string operator = 2;
}

message QueryActivityTagRequest{
  // 操作人
  string operator = 1;
  // 面向业务
  string biz_type = 2;
}


message QueryCrowdEventRequest{
  // 操作人
  string operator = 1;
  // 场景
  string biz_type = 2;
  // 极速版
  bool lite_flag = 3 ;
  // 来源
  string outer_source = 4;
}


message QueryActivityListPageRequest{
  // 操作人
  string operator = 1;
  // 活动系列类型
  string type = 2;
  // 请求参数
  string query_param = 3;
}

message UpdateActivityStatusRequest{
  // 操作人
  string operator = 1;
  // 活动id
  int64 activity_id = 2;
  // 活动状态
  int32 status = 3;
}

message QueryNotificationListRequest{
  // 操作人
  string operator = 1;
  // 场景
  int32 scene = 2;
}

message QueryIndicatorListRequest{
  // 操作人
  string operator = 1;
  // 指标内部名称
  string name = 2;
  // 类型(多种类型用逗号分隔)
  string type = 3;
}

message QueryIndicatorConfigConditionRequest{
  // 操作人
  string operator = 1;
  // 指标id
  int64 indicator_id = 2;
}

message DeleteActivityRequest{
  // 操作人
  string operator = 1;
  // 活动id
  int64 activity_id = 2;
}

message QueryAwardSimpleConfigListRequest{
  // 操作人
  string operator = 1;
  // 奖励类型名称
  string award_type_name = 2;
}

message OnlineActivityRequest{
  // 操作人
  string operator = 1;
  // 上线的活动id
  uint64 activity_id = 2;
}

message QueryIndicatorListByTagRequest{
  // 操作人
  string operator = 1;
  // 标签集合
  repeated string tag_list = 2;
  // 标签分类
  repeated string category = 3;
  // 应用业务
  string application = 4;
  /**
   * 活动玩法
   */
  string activity_pattern_type = 5;
}

message QueryActivityGeneralSelectInfoListRequest{
  // 操作人
  string operator = 1;
  // 查询类型
  string type = 2;
}

message CheckExcelContentRequest{
  // 操作人
  string operator = 1;
  // excel文件地址
  string excel_cdn_url = 2;
  // 类型
  string type = 3;
}

message QueryCategoryListRequest{
  // 操作人
  string operator = 1;
  // 层级
  uint32 hierarchy = 2;
}

message  CheckMathCalculatorValidRequest{
  // 操作人
  string operator = 1;

  string math_expression = 2;

  uint32 check_type = 3;

}

message QueryActivityInfoRequest{
  // 横向活动id
  uint64 resource_activity_id = 1;
}

message ResourceOnlineActivityRequest{
  // 横向活动id
  uint64 resource_activity_id = 1;
  // 纵向活动id
  uint64 activity_id = 2;
  // 操作人
  string operator = 3;
}

message QueryActivityInfoResponse{
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  ActivityDetailDTO data = 3;
}

message ActivityDetailDTO{
  // 活动id
  uint64 activity_id = 1;
  // 名称
  string name = 2;
  // 开始时间
  uint64 start_time = 3;
  // 结束时间
  uint64 end_time = 4;
  //业务描述
  string description = 5;
  // creator
  string creator = 6;
  // modifier
  string modifier = 7;
  // 状态
  uint32 status = 8;
  // 活动类型
  uint32 type = 9;
  // 活动玩法
  string activity_pattern_type = 10;
  // 活动系列
  uint64 series_type = 11;
  // 外部名称
  string show_name = 12;
  // 任务时间
  repeated TaskTimeDTO task_times = 13;
  // 纵向活动周期信息
  repeated PeriodDTO period_info = 14;
  // 配置奖励类型
  repeated uint32 award_types = 15;
  // 货主类型
  uint32 distributor_type = 16;
  // 预算管控活动预算 （仅在活动为预算管控活动时有值）key：奖励类型 value：关联的预算
  map<uint32, uint64> budget_limit_map = 17;
  // 活动标签tagCode
  string activity_tag = 18;
}

message TaskTimeDTO {
  /**
  开始时间
  */
  uint64 start_time = 1;
  /**
  结束时间
   */
  uint64 end_time = 2;
}

message PeriodDTO {
  // 任务id
  uint64 task_id = 1;
  // 任务开始时间
  uint64 start_time = 2;
  // 任务结束时间
  uint64 end_time = 3;
}

message GenerateCustomizeBasicExcelRequest {
  /**
   * 基值指标列表
   */
  repeated uint64 base_indicator_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message GenerateCustomizeBasicExcelResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message AdminSaveActivityRequest {
  // 操作人
  string operator = 1;
  // 配置
  string config = 2;
}

message GetActivityStatisticsRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message GetNotificationInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message CreateNotificationRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 模板code
   */
  repeated string notification_code = 2;
  /**
   * 操作人
   */
  string operator = 3;
  /**
   * 触达配置config
   */
  repeated ChannelNotificationConfig notification_info = 4;
}

message ChannelNotificationConfig {
  /**
   * 触达渠道
   */
  uint32 channel = 1;

  /**
   * 触达场景配置
   */
  repeated NotificationConfig notification_configs = 2;

  /**
   * 触达场景url
   */
  string banner_url = 3;

  /**
   * 该触达渠道是否选中
   */
  bool choose = 4;
}
message NotificationConfig {
  /**
   * 触达code
   */
  string notification_code = 1;

  /**
   * 模版code
   */
  string template_code = 2;

  /**
   * 间隔天数
   */
  uint64 interval_day = 3;
}

message  QueryActivityCrowdConfigRequest{
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message AddActivityCrowdRequest{
  /**
  * 活动ID
  */
  uint64 activity_id = 1;
  /**
  * 人群配置
  */
  string config = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message AddActivityDrawCountRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;

  /**
   * 追加人数
   */
  uint64 add_count = 2;

  /**
   * 操作人
   */
  string operator = 3;
}

message QueryOperatorStrategyPermissionRequest{
  /**
 * 操作人
 */
  string operator = 1;
}

message GenerateFixTemplateExcelRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 指标or奖励
   */
  uint32 entity_type = 2;
  /**
   * 指标ID or 奖励类型
   */
  uint64 entity_id = 3;
}

message GenerateFixTemplateExcelResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  string data = 3;
}

message GetOperatorFunctionWhiteListRequest {
  /**
   * 操作人
   */
  string operator = 1;
}

message CommonParamValidationRequest {
  /**
   * 操作人
   */
  string operator = 1;

  /*
   * 校验场景
   */
  int32 scene = 2;
  /**
   * 通用参数值
   */
  string param_value = 3;
}

message QueryActivityRoiAccessRequest {
  /**
   * 事件ID
   */
  string event_id = 1;
  /**
   * ROI类型
   */
  uint32 roi_type = 2;
  /**
   * 横向活动ID
   */
  uint64 resource_activity_id = 3;
  /**
   * 前端协议配置
   */
  string calc_config = 4;
  /**
   * 操作人
   */
  string operator = 5;

  /**
 * 纵向活动ID
 */
  uint64 activity_id = 6;
}

message QueryActivityRoiAccessResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  RoiAccessDTO data = 3;
}

message RoiAccessDTO {
  /**
   * 是否能够计算ROI
   */
  bool access = 1;
  /**
   * 错误信息
   */
  string reject_reason = 2;
}

message QueryActivityOnlineProgressRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
}

message QueryActivityOnlineProgressResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  OnlineProgressDTO data = 3;
}

message OnlineProgressDTO {
  /**
   * 进度
   */
  uint32 progress = 1;
  /**
   * 描述
   */
  string desc = 2;
}

message SubmitActivityAuditRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message SubmitActivityAuditResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message BindResourceActivityRequest {
  /**
   * 横向活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 绑定ID
   */
  uint64 bind_activity_id = 2;
  /**
   * 绑定类型
   */
  uint32 bind_type = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message BindResourceActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 绑定结果
   */
  BindResDTO data = 3;
}

message BindResDTO {
  /**
   * 是否绑定成功
   */
  bool bind_success = 1;
  /**
   * 绑定失败原因
   */
  string fail_reason = 2;
}

message QueryCommonCheckProcessRequest {
  /**
   * 唯一事件ID
   */
  string event_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
  /**
   * 场景
   */
  string scene = 3;
}

message QueryCommonCheckProcessResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 校验结果
   */
  CheckProcessDTO data = 3;
}

message OperateActivityDynamicEventRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作方式
   */
  uint32 execute_type = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message OperateActivityDynamicEventResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message BatchGetUserResourceRuleInfoRequest{
  /**
  活动ID
  */
  repeated uint64 seller_ids = 1;
  /**
  发奖单ID
   */
  uint64 resource_rule_id = 2;
  /**
   横向活动
   */
  uint64 resource_activity_id = 3;
}

message BatchGetUserResourceRuleInfoResponse{
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  map<uint64, UserResourceRuleInfo> data = 3;
}

message UserResourceRuleInfo{
  /**
   发送状态 0 未推送结束 1 推送结束
   */
  uint32 send_status = 1;
  /**
   已推送数量
   */
  uint32 send_count = 2;
}

message CheckProcessDTO {
  /**
   * 进度
   */
  uint32 process = 1;
  /**
   * 描述
   */
  string desc = 2;
  /**
   * 校验通过标识
   */
  bool pass = 3;
  /**
   * 校验结果状态
   */
  string check_result_key = 4;

  /**
   * 校验数据详情
   */
  string check_detail = 5;
}

message AddNewSubActivityAdminRequest {
  // 操作人
  string operator = 1;
  // 配置
  string config = 2;
}

message AddNewSubActivityAdminResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message QueryNewAddSubActivityAdminDetailRequest {
  // 活动id
  uint64 activity_id = 1;
  // 操作人
  string operator = 2;
}

message OnlineNewAddSubActivityRequest {
  // 操作人
  string operator = 1;
  // 上线的活动id
  uint64 activity_id = 2;
}

message QueryAwardCustomizeRuleCalcConfigRequest{
  // 操作人
  string operator = 1;
  // 场景
  string biz_type = 2;
  // 奖励玩法
  string award_pattern = 3;
}

message QueryActivityLaunchRequest{
  // 活动ID
  int64 activity_id = 1;
  // 场景
  string scene = 2;
}

message QueryActivityLaunchResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}


message ActivityFrontConfigRequest {

}

message ActivityFrontConfigResponse {
  uint32 result = 1;
  string error_msg = 2;
  string config = 3;
}

message ActivityBriefConfigInfoRequest {
  uint64 activity_id = 1;
}

message ActivityBriefConfigInfoResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  ActivityBriefInfoDTO activity_brief_detail = 3;
}

message ActivityBriefInfoDTO{
  // 活动id
  uint64 activity_id = 1;
  // 名称
  string name = 2;
  // 开始时间
  uint64 start_time = 3;
  // 结束时间
  uint64 end_time = 4;
  // 状态
  uint32 status = 5;
  // 活动玩法
  string activity_pattern_type = 6;
  // 活动配置链接
  string activity_url = 7;
  // 子活动
  repeated SubActivityConfigDTO sub_activity_config_list = 8;
}

message SubActivityConfigDTO {
  // 子活动名称
  string name = 1;
  // 开始时间
  uint64 start_time = 2;
  // 结束时间
  uint64 end_time = 3;
  // 子活动顺序
  uint32 sub_activity_order = 4;
  // 子活动分层
  repeated SubActivityLevelConfigDTO sub_activity_level_config_list = 5;
}

message SubActivityLevelConfigDTO {
  // 分层id
  uint64 level_id = 1;
  // 分层名称
  string level_name = 2;
  // 分层排序
  uint32 level_order = 3;
  // 前返配置
  PreAwardSendConfigDTO pre_award_send_config = 4;
  // 阶梯信息
  repeated LevelStepConfigDTO level_step_config_list = 5;
}

message PreAwardSendConfigDTO {
  //最低目标比例
  uint32 minimum_target_rate = 1;
  // 前返比例
  uint32 pre_send_award_rate = 2;
}

message LevelStepConfigDTO {
  uint32 step_order = 1;

  repeated IndicatorInfo target_indicator_list = 2;

  repeated AwardInfo award_info_list = 3;
}

message IndicatorInfo {
  // 指标id
  uint64 indicator_id = 1;
  // 指标名称
  string name = 2;
}

message AwardInfo {
  // 奖励类型
  uint32  award_type = 1;
  // 奖励类型名称
  string award_type_name = 2;
  // 奖励算法类型
  string award_target_type = 3;
  // 返点指标(不一定有)
  repeated IndicatorInfo return_indicator_list = 4;
}

message QueryLaunchEntityInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 实体类型
   */
  uint32 entity_type = 2;
}

message QueryLaunchConfigRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message SaveLaunchConfigRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 配置
   */
  string config = 2;
  /**
   * 操作人
   */
  string operator = 3;
}

message DirectSaveLaunchConfigRequest {

  /**
   * 活动ID
   */
  uint64 activity_id = 1;

  /**
   * 配置
   */
  string config = 2;

  /**
   * 操作人
   */
  string operator = 3;

  /**
   * 资源类型
   */
  uint32 resource_type = 4;
}

message DeleteLaunchConfigRequest {

  /**
   * 配置ID
   */
  uint64 config_id = 1;
}

message QueryLaunchMetaRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message QueryCustomBaseCalcRuleRequest {
  /**
   * 业务类型
   */
  string biz_type = 1;
  /**
   * 活动玩法
   */
  string activity_pattern_type = 2;
}

message QueryCustomBaseCalcRuleResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 返回信息
   */
  QueryCustomBaseCalcRuleData data = 3;
}

message QueryCustomBaseCalcRuleData {
  /**
   * item
   */
  repeated QueryCustomBaseCalcRuleItem list = 1;
}

message QueryCustomBaseCalcRuleItem {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 自定义基期计算规则Code
   */
  string custom_base_calc_rule_code = 2;
  /**
   * 自定义基期计算规则名称
   */
  string custom_base_calc_rule_name = 3;
}

message QueryActivityForPlanOperationPageRequest {
  string activity_name = 1;
  uint64 activity_id = 2;
  uint32 page_no = 3;
  uint32 page_size = 4;
}

message QuerySubActivityTagRequest {
  /**
   * 操作人
   */
  string operator = 1;
  /**
   * 业务类型
   */
  string biz_type = 2;
  /**
   * 活动玩法类型
   */
  string activity_pattern_type = 3;
}

message TestLaunchAuditCallbackRequest {
  string audit_biz_key = 1;
  uint32 audit_result = 2;
  uint64 activity_id = 3;
  string channel = 4;
}

message TestLaunchAuditSubmitRequest {
  uint64 activity_id = 1;
  string channel = 2;
  string biz_key = 3;
  string operator = 4;
}

message GenerateLaunchContentAsyncRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 投放渠道
   */
  string channel = 2;
  /**
   * 投放场景
   */
  string scene = 3;
  /**
   * 实体ID列表
   */
  repeated uint64 entity_id = 4;
  /**
   * 生成类型
   * 1：全部 2：仅图片
   */
  uint32 generate_type = 5;
  /**
   * operator
   */
  string operator = 6;
}

message QueryGeneratedLaunchContentRequest {
  /**
   * 投放内容生成查询唯一键
   */
  string unique_key = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message TestLaunchContentGenAsyncProcessRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 投放渠道
   */
  string channel = 2;
  /**
   * 投放场景
   */
  string scene = 3;
  /**
   * 实体ID列表
   */
  repeated uint64 entity_id = 4;
  /**
   * 生成类型
   * 1：全部 2：仅图片
   */
  uint32 generate_type = 5;
  /**
   * operator
   */
  string operator = 6;
  /**
   * 唯一键
   */
  string unique_key = 7;
}

message SaveExperimentConfigRequest {
  string config = 1;
}

message QueryExperimentConfigByKeyRequest {
  string experiment_key = 1;
}

message QueryBucketRequest {
  string experiment_key = 1;
  string item_key = 2;
  uint64 user_id = 3;
}

message GenerateComponentDataContentAsyncRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 子活动配置
   */
  string sub_activity_config = 2;
  /**
   * 生成类型
   * 1：全部 2：仅图片
   */
  uint32 generate_type = 3;
  /**
   * operator
   */
  string operator = 4;
}

message QueryGeneratedComponentDataContentRequest {
  /**
   * 投放内容生成查询唯一键
   */
  string unique_key = 1;
  /**
   * 操作人
   */
  string operator = 2;
}

message TestExpBucketAssignReportRequest {
  /**
   * 实验分桶上报数据
   */
  string exp_bucket_assign_data = 1;
}


message QueryUserAwardLimitConfigRequest {
  /**
   * 活动id
   */
  int64 activity_id = 1;
  /**
   * 操作人
   */
  string operator = 2;
}


message SaveUserAwardLimitConfigRequest {
  string scene = 1;
  string operator = 2;
  string config = 3;
}



service ActivityConfigPlatformService{
  // ------------------------------------------------------------------------------------
  // ---------------------------------- 策略活动后台 ---------------------------------------
  // ------------------------------------------------------------------------------------
  // 策略活动后台保存
  rpc AdminSaveActivity(AdminSaveActivityRequest) returns (ActivityAdminCommonResponse);
  // 查询活动标签列表
  rpc QueryActivityTag(QueryActivityTagRequest) returns (ActivityAdminCommonResponse);
  // 活动组件获取
  rpc GetActivityComponentConfig(GetActivityComponentConfigRequest) returns (ActivityAdminCommonResponse);
  // 根据标签查询指标列表
  rpc QueryIndicatorListByTag(QueryIndicatorListByTagRequest) returns(ActivityAdminCommonResponse);
  // 生成自定义基期表格头
  rpc GenerateCustomizeBasicExcel(GenerateCustomizeBasicExcelRequest) returns (GenerateCustomizeBasicExcelResponse);
  // 生成目标或奖励固定值表格表头
  rpc GenerateFixTemplateExcel(GenerateFixTemplateExcelRequest) returns (GenerateFixTemplateExcelResponse);
  // 获取用户使用功能白名单
  rpc GetOperatorFunctionWhiteList(GetOperatorFunctionWhiteListRequest) returns (ActivityAdminCommonResponse);
  // 活动统计数据
  rpc GetActivityStatistics(GetActivityStatisticsRequest) returns (ActivityAdminCommonResponse);
  // 触达信息回显
  rpc GetNotificationInfo(GetNotificationInfoRequest) returns (ActivityAdminCommonResponse);
  // 创建触达配置
  rpc CreateNotification(CreateNotificationRequest) returns (ActivityAdminCommonResponse);
  // 人群追加信息渲染
  rpc QueryActivityCrowdConfig(QueryActivityCrowdConfigRequest) returns (ActivityAdminCommonResponse);
  // 人群追加
  rpc AddActivityCrowd(AddActivityCrowdRequest) returns (ActivityAdminCommonResponse);
  // 追加报名限额活动限额人数
  rpc AddActivityDrawCount(AddActivityDrawCountRequest) returns (ActivityAdminCommonResponse);
  // 横向活动放量开关
  rpc QueryOperatorStrategyPermission(QueryOperatorStrategyPermissionRequest) returns  (ActivityAdminCommonResponse);
  // 查询人群校验进度（人货撮合）
  rpc QueryCommonCheckProcess(QueryCommonCheckProcessRequest) returns
      (QueryCommonCheckProcessResponse);
  // 查询自定义奖励计算规则
  rpc QueryAwardCustomizeRuleCalcConfig (QueryAwardCustomizeRuleCalcConfigRequest) returns (ActivityAdminCommonResponse);
  //  查询子活动标签
  rpc QuerySubActivityTag (QuerySubActivityTagRequest) returns (ActivityAdminCommonResponse);
  rpc QuerySubActivityTagV2 (QuerySubActivityTagRequest) returns (ActivityAdminCommonResponse);


  // ------------------------------------------------------------------------------------
  // ---------------------------------- 纵向+自定义后台 ------------------------------------
  // ------------------------------------------------------------------------------------
  // 保存活动
  rpc SaveActivity(SaveActivityAdminDomainRequest) returns(ActivityAdminCommonResponse);
  // 校验公式合法性
  rpc CheckMathCalculatorValid(CheckMathCalculatorValidRequest) returns (ActivityAdminCommonResponse);
  // 查询活动
  rpc QueryActivityAdminDetail(QueryActivityAdminDomainRequest) returns(ActivityAdminCommonResponse);
  // 查询活动标签列表
  rpc QueryCrowdEvent(QueryCrowdEventRequest) returns (ActivityAdminCommonResponse);
  // 查询活动列表
  rpc QueryActivityListPage(QueryActivityListPageRequest) returns (ActivityAdminCommonResponse);
  // 修改活动状态
  rpc UpdateActivityStatus(UpdateActivityStatusRequest) returns (ActivityAdminCommonResponse);
  // 查询通知配置列表
  rpc QueryNotificationList(QueryNotificationListRequest) returns (ActivityAdminCommonResponse);
  // 查询指标列表
  rpc QueryIndicatorList(QueryIndicatorListRequest) returns (ActivityAdminCommonResponse);
  // 查询指标配置条件信息
  rpc QueryIndicatorConfigCondition(QueryIndicatorConfigConditionRequest) returns (ActivityAdminCommonResponse);
  // 删除活动
  rpc DeleteActivity(DeleteActivityRequest) returns (ActivityAdminCommonResponse);
  // 查询奖励基本配置信息列表
  rpc QueryAwardSimpleConfigList(QueryAwardSimpleConfigListRequest) returns (ActivityAdminCommonResponse);
  // 活动下拉框信息查询
  rpc QueryActivityGeneralSelectInfoList(QueryActivityGeneralSelectInfoListRequest) returns (ActivityAdminCommonResponse);
  // 上线活动
  rpc OnlineAdminActivity(OnlineActivityRequest) returns (ActivityAdminCommonResponse);
  // 校验excel内容
  rpc CheckExcelContent(CheckExcelContentRequest) returns (ActivityAdminCommonResponse);
  // 查询类目列表
  rpc QueryCategoryLis(QueryCategoryListRequest) returns (ActivityAdminCommonResponse);
  // 根据横向活动id反查纵向活动信息
  rpc QueryActivityInfo(QueryActivityInfoRequest) returns (QueryActivityInfoResponse);
  // 横向测上线玩法
  rpc ResourceOnlineActivity(ResourceOnlineActivityRequest) returns (ActivityAdminCommonResponse);
  // 根据横向活动id反查纵向活动信息(新)
  rpc QueryByResourceActivityId(QueryActivityInfoRequest) returns (QueryActivityInfoResponse);
  // 查询任务是否能够ROI
  rpc QueryActivityRoiAccess(QueryActivityRoiAccessRequest) returns (QueryActivityRoiAccessResponse);
  // 查询活动上线进度
  rpc QueryActivityOnlineProgress(QueryActivityOnlineProgressRequest) returns (QueryActivityOnlineProgressResponse);
  // 活动提审
  rpc SubmitActivityAudit(SubmitActivityAuditRequest) returns (SubmitActivityAuditResponse);
  // 横向活动手动绑定
  rpc BindResourceActivity(BindResourceActivityRequest) returns (BindResourceActivityResponse);
  // 中止/开启动态事件报名
  rpc OperateActivityDynamicEvent(OperateActivityDynamicEventRequest) returns (OperateActivityDynamicEventResponse);
  // 通用参数校验
  rpc CommonParamValidation(CommonParamValidationRequest) returns (ActivityAdminCommonResponse);
  // 过程中新增子活动
  rpc AddNewSubActivityAdmin(AddNewSubActivityAdminRequest) returns (AddNewSubActivityAdminResponse);
  // 过程中新增子活动回显
  rpc QueryNewAddSubActivityAdminDetail(QueryNewAddSubActivityAdminDetailRequest) returns (ActivityAdminCommonResponse);
  // 过程中新增子活动上线
  rpc OnlineNewAddSubActivity(OnlineNewAddSubActivityRequest) returns (ActivityAdminCommonResponse);
  // 查询用户发奖单下奖励状态
  rpc BatchGetUserResourceRuleInfo(BatchGetUserResourceRuleInfoRequest) returns (BatchGetUserResourceRuleInfoResponse);
  // 查询活动投放信息
  rpc QueryActivityLaunch(QueryActivityLaunchRequest) returns (QueryActivityLaunchResponse);
  // 获取下发给前端的配置
  rpc GetActivityFrontConfig(ActivityFrontConfigRequest) returns (ActivityFrontConfigResponse);
  // 获取纵向活动配置的信息
  rpc GetActivityBriefConfig(ActivityBriefConfigInfoRequest) returns (ActivityBriefConfigInfoResponse);
  // 查询基期自定义计算规则
  rpc QueryCustomBaseCalcRule(QueryCustomBaseCalcRuleRequest) returns (QueryCustomBaseCalcRuleResponse);
  // 查询投放配置元数据信息
  rpc QueryLaunchMeta (QueryLaunchMetaRequest) returns (ActivityAdminCommonResponse);
  // 保存投放配置
  rpc SaveLaunchConfig (SaveLaunchConfigRequest) returns (ActivityAdminCommonResponse);
  // 快速保存投放配置
  rpc DirectSaveLaunchConfig (DirectSaveLaunchConfigRequest) returns (ActivityAdminCommonResponse);
  // 删除投放配置
  rpc DeleteLaunchConfig (DeleteLaunchConfigRequest) returns (ActivityAdminCommonResponse);
  // 查询投放配置
  rpc QueryLaunchConfig (QueryLaunchConfigRequest) returns (ActivityAdminCommonResponse);
  // 查询投放实体信息
  rpc QueryLaunchEntityInfo (QueryLaunchEntityInfoRequest) returns (ActivityAdminCommonResponse);
  // 分页查询活动信息
  rpc PageQueryActivityForPlanOperation(QueryActivityForPlanOperationPageRequest) returns (ActivityAdminCommonResponse);
  // 测试投放审批回调
  rpc TestLaunchAuditCallback (TestLaunchAuditCallbackRequest) returns (ActivityAdminCommonResponse);
  // 测试提交投放审批
  rpc TestLaunchAuditSubmit (TestLaunchAuditSubmitRequest) returns (ActivityAdminCommonResponse);
  // 异步生成投放内容
  rpc GenerateLaunchContentAsync (GenerateLaunchContentAsyncRequest) returns (ActivityAdminCommonResponse);
  // 投放内容查询
  rpc QueryGeneratedLaunchContent (QueryGeneratedLaunchContentRequest) returns (ActivityAdminCommonResponse);
  // 测试投放物料生成
  rpc TestLaunchContentGenAsyncProcess (TestLaunchContentGenAsyncProcessRequest) returns (ActivityAdminCommonResponse);
  // 保存实验配置
  rpc SaveExperimentConfig (SaveExperimentConfigRequest) returns (ActivityAdminCommonResponse);
  // 查询实验配置
  rpc QueryExperimentByKey (QueryExperimentConfigByKeyRequest) returns (ActivityAdminCommonResponse);
  // 查询分桶
  rpc QueryBucket (QueryBucketRequest) returns (ActivityAdminCommonResponse);
  // 异步生成投放内容
  rpc GenerateComponentDataContentAsync (GenerateComponentDataContentAsyncRequest) returns (ActivityAdminCommonResponse);
  // 投放内容查询
  rpc QueryGeneratedComponentDataContent (QueryGeneratedComponentDataContentRequest) returns (ActivityAdminCommonResponse);
  // 测试实验分桶上报
  rpc TestExpBucketAssignReport (TestExpBucketAssignReportRequest) returns (ActivityAdminCommonResponse);
  // 用户奖励上限-查询接口
  rpc QueryUserAwardLimitConfig (QueryUserAwardLimitConfigRequest) returns (ActivityAdminCommonResponse);
  //用户奖励上限-保存&提交接口
  rpc SaveUserAwardLimitConfig (SaveUserAwardLimitConfigRequest) returns (ActivityAdminCommonResponse);
}