syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.admin;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.admin";
option java_outer_classname = "ActivityOriginCalcOutputServiceProto";

message ActivityCalcAllRequest{
  // 操作人
  string operator = 1;
  // 活动ID
  uint64 activity_id = 2;
}

message ActivityCalcAllResponse{
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
}

message QueryIndustryActivityInfoRequest {
  /**
   * 纵向活动ID
   */
  uint64 activity_id = 1;
}

message QueryIndustryActivityInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 活动信息
   */
  ActivityInfoDTO dto = 3;
}

message ActivityInfoDTO {
  /**
   * 活动配置
   */
  IndustryActivityDTO industry_activity = 1;

  /**
   * 任务配置
   */
  repeated IndustryTaskDTO task_config = 2;
}

message IndustryActivityDTO {
  /**
   * 活动玩法
   */
  string activity_pattern_type = 1;

  /**
   * 基期时间类型
   */
  string indicator_time_type = 2;

  /**
   * 基期开始时间
   */
  uint64 fixed_start_time = 3;

  /**
   * 基期结束时间
   */
  uint64 fixed_end_time = 4;

  /**
    基期算法
   */
  string bas_algorithm = 5;

  /**
   * 自定义上传基期Excel地址
   */
  string base_algorithm_customize_url = 6;
}

message IndustryTaskDTO {
  /**
   * 任务开始时间
   */
  uint64 start_time = 1;

  /**
   * 任务结束时间
   */
  uint64 end_time = 2;

  /**
   * 商家分层
   */
  repeated PhaseTaskDTO phase_task = 3;
}


message PhaseTaskDTO {
  /**
   * 圈选类型
   */
  uint32 crowd_type = 1;

  /**
   * 圈选配置内容，根据圈选类型去做不同的序列化
   */
  string crowd_content = 2;

  /**
   * 任务名称
   */
  string task_name = 3;

  /**
   * 子任务
   */
  repeated IndustrySubTaskDTO sub_task = 4;
}

message IndustrySubTaskDTO{
  /**
    * 指标配置
    */
  repeated IndustryIndicatorDTO indicator_config = 1;

  /**
   * 奖励配置
   */
  repeated IndustryAwardDTO award_config = 2;
}

message IndustryIndicatorDTO {
  /**
    * 指标id
    */
  uint64 indicator_id = 1;

  /**
   * 指标目标值类型
   */
  string target_type = 2;

  /**
   * 指标单位
   */
  string unit = 3;

  /**
   * 目标值
   */
  string target_value = 4;

  /**
   * 标签
   */
  repeated string tags = 5;

  /**
    类目信息
   */
  repeated CategoryDTO category_list = 6;

  /**
  * 指标目标值子类型
  */
  string target_sub_type = 7;
}

/*
 类目信息
 */
message CategoryDTO {

  /**
     * 类目ID
     */
  uint64 category_id = 1;

  /**
   * 类目名称
   */
  string category_name = 2;

  /**
    *  层级，从1开始
   */
  uint32 hierarchy = 3;

  /**
    子类目
   */
  repeated CategoryDTO child_category = 4;
}

message IndustryAwardDTO {
  /**
     * 奖励类型
     */
  uint32 award_type = 1;

  /**
   * 奖励目标值类型
   */
  string target_type = 2;

  /**
   * 奖励目标值子类型
   */
  string target_sub_type = 3;

  /**
   * 目标值json
   */
  string target_value = 4;

  /**
   * 奖励过期时间
   */
  uint64 expire_time = 5;

  /**
   * 奖励分层，实际奖励人群
   */
  repeated uint32 rankings = 6;
}

message QueryActivityCalcRequest {
  /**
   * 周期开始时间
   */
  uint64 period_start_time = 1;

  /**
   * 周期结束时间
   */
  uint64 period_end_time = 2;

  /**
   * 商家ID列表
   */
  repeated uint64 seller_id = 3;

  /**
   * 基期类型
   */
  string indicator_time_type = 4;

  /**
   * 基期开始时间
   */
  uint64 fixed_start_time = 5;

  /**
   * 基期结束时间
   */
  uint64 fixed_end_time = 6;

  /**
    每个ROI计算的唯一key
   */
  string activity_calc_unique_key = 7;

  /**
    活动玩法
   */
  string activity_pattern_type = 8;

  /**
   * 指标信息
   */
  repeated IndustryIndicatorDTO indicator_config = 9;

  /**
   * 奖励信息
   */
  repeated IndustryAwardDTO award_config = 10;

  /**
  基期算法
 */
  string base_algorithm = 11;

  /**
   * 自定义上传基期Excel地址
   */
  string  base_algorithm_customize_url = 12;

}

message QueryActivityCalcResponse {

  /**
   * 返回结果码
   */
  int32 result = 1;

  /**
   * 返回信息
   */
  string error_msg = 2;

  /**
   * 商家基本信息
   */
  repeated ActivityCalcDTO activity_calc_dto = 3;
}

message ActivityCalcDTO {

  /**
   * 商家ID列表
   */
  uint64 seller_id = 1;

  /**
    不同指标对应的值
   */
  repeated IndicatorValueDTO indicator_value = 3;

  /**
    奖励值，通过公式计算后得到的
   */
  repeated AwardValueDTO award_value = 4;
}

message IndicatorValueDTO {

  /**
   * 指标ID
   */
  uint64 indicator_id = 1;

  /**
   * 基期日均
   */
  uint64 base_agv_value = 2;

  /**
   * 目标值
   */
  uint64 target_value = 3;

  /**
  * 指标名称
  */
  string indicator_name = 4;

  /**
  * 指标单位
  */
  string unit = 5;

  /**
   * 指标标签
   */
  repeated string tags = 6;
}

message AwardValueDTO {

  /**
   * 奖励类型
   */
  uint32 award_type = 1;

  /**
   *奖励值
   */
  uint64 award_value = 2;
}

message QueryActivityByRuleIdRequest {

  /**
   * 商家ID列表
   */
  repeated uint64 seller_id = 1;

  /**
   * 规则ID，对应任务侧的任务信息 （根据周期信息创建）
   */
  uint64 resource_rule_id = 2;
}

message QueryActivityByRuleIdResponse {

  /**
   * 返回结果码
   */
  int32 result = 1;

  /**
   * 返回信息
   */
  string error_msg = 2;

  /**
   * 商家基本信息
   */
  repeated ActivityByRuleIdDTO activity_by_rule_id = 3;
}

message ActivityByRuleIdDTO {

  /**
   * 商家ID
   */
  uint64 seller_id = 1;

  /**
   * 指标ID
   */
  uint64 indicator_id = 2;

  /**
    指标当前值
   */
  uint64 current_value = 3;
}


service ActivityOriginCalcOutputService{

  /**
    计算该活动下的所有目标值和奖励值
   */
  rpc CalcAllByActivityId(ActivityCalcAllRequest) returns(ActivityCalcAllResponse);

  /**
    活动ID查询活动配置信息
   */
  rpc QueryIndustryActivityInfo(QueryIndustryActivityInfoRequest) returns(QueryIndustryActivityInfoResponse);

  /**
    获取商家对应周期下的活动期数据
   */
  rpc QueryActivityByRuleId(QueryActivityByRuleIdRequest) returns(QueryActivityByRuleIdResponse);

  /**
    获取每个商家的任务配置的指标具体数值
  */
  rpc ActivityCalc(QueryActivityCalcRequest) returns(QueryActivityCalcResponse);
}

