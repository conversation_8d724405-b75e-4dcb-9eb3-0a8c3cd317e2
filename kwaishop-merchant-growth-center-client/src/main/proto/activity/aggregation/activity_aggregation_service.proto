syntax = "proto3";
import "kwaishop_merchant_growth_common.proto";

package kuaishou.kwaishop.merchant.growth.center.activity.aggregation;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.aggregation";
option java_outer_classname = "ActivityAggregationServiceProto";

message GetUserActivityAggregationInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 场景值
   */
  string scene = 3;
}

message GetUserActivityStrategyInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 场景值
   */
  string scene = 3;

  /**
   * 周期锚点时间 (仅对周期子活动生效)
   * 对于周期子活动, 不传返回全部周期任务信息
   * 如果锚点时间所在周期不存在, 则返回值为空
   */
  uint64 cycle_anchor_time = 4;

  /**
   * 周期数量区间 (仅对周期子活动生效)
   * 对于周期子活动, 不传返回全部周期任务信息
   */
  repeated uint32 cycle_size_interval = 5;
}

message GetUserActivitySummaryInfoRequest{
  /**
 * 用户id
 */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 场景值UserTaskAggregationDTO
   */
  string scene = 3;
}

message GetUserActivityAggregationInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户聚合活动信息
   */
  UserActivityAggregationDTO data = 3;
}

message GetUserActivityStrategyInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户活动展示信息
   */
  UserActivityStrategyDTO data = 3;
}

message GetUserActivitySummaryInfoResponse{
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户活动聚合信息
   */
  UserActivitySummaryInfoDTO data = 3;
}

message UserActivitySummaryInfoDTO{
  /**
   * 活动状态
   */
  int32 activity_status = 1;
  /**
   * 已完成的任务数量
   */
  int32 task_succeed_count = 3;
  /**
   * 进行中任务数量
   */
  int32 task_process_count = 4;
  /**
   * 审核中任务数量
   */
  int32 task_audit_count = 5;
  /**
   * 奖励汇总
   */
  repeated AwardSummaryInfoDTO award_summary = 6;
}

message AwardSummaryInfoDTO{
  /**
   * 奖励类型：3-磁力金牛奖励
   */
  int32 award_type = 1;
  /**
   * 奖励总数量
   */
  uint64 award_total_count = 2;
  /**
   * 已获得的奖励数量
   */
  uint64 award_got_count = 3;

  /**
   * 剩余可获得奖励
    */
  uint64 award_remain_count = 4;

}

message UserActivityAggregationDTO{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 活动状态
   */
  int32 activity_status = 3;
  /**
   * 可领取的任务列表
   */
  repeated UserTaskAggregationInfoDTO can_draw_task = 4;
  /**
   * 已领取的任务列表
   */
  repeated UserTaskAggregationInfoDTO already_draw_task = 5;
  /**
   * 活动领取时间
   */
  int64 draw_time = 6;
  /**
    * 活动详情聚合
    */
  ActivityAggregationInfoDTO activity_info = 7;
}

//  活动详情
message ActivityAggregationInfoDTO{

  /**
   * 活动id
   */
  int64 activity_id = 1;

  /**
   * 活动名称
   */
  string activity_name = 2;

  /**
    活动外部名称
  */
  string show_name = 3;

  /**
   * 活动图片
   */
  string activity_image = 4;

  /**
    活动开始时间
   */
  uint64 start_time = 5;

  /**
    活动结束时间
   */
  uint64 end_time = 6;

  /**
    活动玩法类型
   */
  string activity_type = 7;

  /**
   * 纵向活动 活动报名页面
   */
  string activity_join_url = 8;

  /**
    活动标签
   */
  string show_tag = 9;

  /**
    报名开始时间
   */
  uint64 draw_start_time = 10;

  /**
    报名结束时间
   */
  uint64 draw_end_time = 11;

  /**
    展示结束时间
   */
  uint64 show_end_time = 12;

  /**
   * 活动当前展示状态 1、未开始 2、进行中 3、已结束 4、未报名
   */
  uint64 show_status = 13;

  /**
   * 活动真实开始时间
   */
  uint64 real_start_time = 14;

  /**
   * 活动真实结束时间
   */
  uint64 real_end_time = 15;
  /**
   * 活动描述
   */
  string activity_desc = 16;
}


message UserTaskAggregationInfoDTO{
  /**
   * 任务信息
   */
  UserTaskAggregationDTO user_task = 1;
  /**
   * 指标信息
   */
  repeated UserIndicatorAggregationDTO user_indicator = 2;
  /**
   * 奖励信息
   */
  repeated UserAwardAggregationDTO user_award = 3;
}

message UserTaskAggregationDTO{
  /**
   * 任务id
   */
  int64 task_id = 1;
  /**
   *  任务alias
   */
  string alias = 2;
  /**
   * 任务状态
   */
  int32 status = 3;
  /**
   *任务开始时间
   */
  int64 start_time = 4;
  /**
   *任务结束时间
   */
  int64 end_time = 5;
  /**
   * 任务所处阶段
   */
  int32 stage = 6;
  /**
   * 任务名称
   */
  string task_name = 7;
  /**
    最近的指标更新时间
   */
  int64 last_indicator_update_time = 8;
  /**
   * 父任务id
   */
  uint64 parent_task_id = 9;
  /**
   * 父任务名称
   */
  string parent_task_name = 10;
  /**
   * 父任务tag
   */
  string task_tag = 11;
  /**
   * 任务更新时间
   */
  int64 task_update_time = 12;
  /**
   * 父任务阶段
   */
  int32 parent_stage = 13;
  /**
   * 任务对外描述
   */
  string task_desc = 14;
}

message UserIndicatorAggregationDTO{
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 当前值
   */
  uint64 current_value = 2;
  /**
   * 目标值
   */
  uint64 target_value = 3;
  /**
   * 状态
   */
  uint32 status = 4;
  /**
   * 指标名称
   */
  string indicator_name = 5;
  /**
   * 指标日均值
   */
  uint64 current_day_avg_value = 6;

  /**
   *  基期指标日均值
   */
  uint64 base_day_avg_value = 7;

  /**
    纵向活动指标数据
   */
  UserPortraitIndicatorAggregationDTO portrait_indicator = 8;

  /**
   * 指标提示
   */
  string indicator_tip = 9;

  /**
   * 指标统计类型
   */
  uint32 indicator_statistics_type = 10;

  /**
   * 指标ROI计算种类
   */
  string roi_indicator_type = 11;
  /**
   * 指标配置名称
   */
  string indicator_config_name = 12;
  /**
   * 指标配置描述
   */
  string indicator_config_desc = 13;
  /**
   * 指标动作
   */
  IndicatorActionDTO indicator_action = 14;

  /**
   * 额外的指标参数
   */
  map<string, uint64> extra_indicator_map = 15;
}

/**
 * 指标动作
 */
message IndicatorActionDTO {
  /**
   * 行动点名称
   */
  string action_name = 1;
  /**
   * app 跳转链接
   */
  string app_link = 2;
  /**
   * pc 跳转链接
   */
  string pc_link = 3;
  /**
   * 攻略链接
   */
  string strategy_link = 4;
}

// 纵向专属指标数据
message UserPortraitIndicatorAggregationDTO{

  /**
   * 指标跳转URL
   */
  string action_jump_url = 1;

  /**
   指标单位
  */
  string unit = 2;

  /**
    指标标签
  */
  repeated string tags = 3;

  /**
    指标跳转按钮文案(去邀约、去设置)
   */
  string action_jump_text = 4;

  /**
    指标多阶梯目标值列表
   */
  repeated uint64 indicator_step_target_value = 5;

  /**
    指标类型，固定值，增幅，增量
   */
  string indicator_value_type = 6;

  /**
  * 纵向排行榜链接
  */
  string leaderboard_url = 7;

  /**
  * 纵向排行榜排名
  */
  uint64 leaderboard_rank = 8;

  /**
   * 指标展示名称
   */
  string main_title = 9;
  /**
    是否展示指标进度
   */
  bool display_progress = 10;

  /**
   * 指标教程文案
   */
  string tutorial = 11;

  /**
   * 指标教程文案跳转链接
   */
  string tutorial_jump_url = 12;
  /**
   * 行动点动作类型
   */
  int32 action_type = 13;
}

message SpecifyIndicatorInfoDTO {
  /**
  * 指标ID
  */
  int64 indicator_id = 1;
  /**
  * 指标名称
  */
  string indicator_name = 2;
  /**
  * 返点计算方式 1 活动期增量 2 活动期全量
  */
  int32 return_type = 3;
  /**
  * 返点比例
  */
  string return_percent = 4;

  /**
   * 计算因子
   */
  string calc_factor = 5;
}

message UserAwardAggregationDTO{
  /**
   * 奖励类型：3-磁力金牛奖励
   */
  int32 award_type = 1;
  /**
   * 已获得奖励数量（已发放 + 待发放）
   */
  uint64 award_got_count = 2;
  /**
   * 奖励总数量
   */
  uint64 award_total_count = 3;
  /**
   * 待发放奖励数量
   */
  uint64 award_wait_send_count = 4;
  /*
    是否预估奖励  1 预估奖励，2 非预估奖励
   */
  uint32 estimate_award = 6;
  /*
    预估奖励
  */
  uint64 predict_award = 7;
  /**
 * 最高奖励
 */
  uint64 award_max_count = 9;
  /*
   * 奖励发放状态
   */
  uint32 award_status = 10;
  /**
   * 奖励固定配置类型 10：固定值 20：固定返点比例
   */
  uint32 config_fix_type = 11;
  /**
   * 奖励固定配置值
   */
  uint64 config_fix_value = 12;

  /**
   * 奖励对应阶段
   */
  uint32 step = 13;
  /**
   * 奖励返点规则
   */
  repeated SpecifyIndicatorInfoDTO return_indicator_info = 14;

  /**
   * 奖励对外名称
   */
  string award_show_name = 15;

  /**
   * 返点指标覆盖
   */
  bool rebate_indicator_covered = 16;
  /**
   * 排行奖励分层（仅排行榜）
   */
  RankAwardStageDTO rank_award_stage = 17;
  /**
   * 奖励展示内容（本次新增）
   */
  AwardCardShowInfoDTO award_show_info = 18;
  /**
   * 奖励展示状态
   * 1：已发放 2：核算中 3：审核未通过
   */
  int32 award_show_status = 19;
  /**
   * 奖励记录更新时间
   */
  uint64 award_update_time = 21;
  /**
   * 奖励发放时间
   */
  uint64 award_send_time = 22;
}

message AwardCardShowInfoDTO {
  /**
   * 奖励卡片标题 返点奖励：award_show_name + 返点比例 其他：award_show_name
   */
  string award_card_title = 1;

  /**
   * 最高可返、完成可得、当前累计、可得
   */
  string award_card_content_prefix = 2;

  /**
   * 奖励卡片公式 奖励金额=店铺服务费*1% + 分销服务费（增量）*1%
   */
  string award_card_formula = 3;

  /**
   *奖励说明
   */
  repeated AwardCardDescriptionDTO award_description = 4;

  /**
   * 奖励值
   */
  string value = 5;

  /**
   * 奖励单位
   */
  string unit = 6;

  /**
   * 奖励卡片详情按钮展示
   */
  bool award_card_button_display = 7;

  /**
   * 奖励卡片详情按钮文案
   */
  string award_card_button_text = 8;

  /**
   * 奖励卡片详情信息
   */
  AwardCardDetailInfoDTO award_card_detail_info = 9;

  /**
   * 奖励说明弹窗按钮列表
   */
  repeated AwardDescriptionButton award_description_button_list = 10;
}

message AwardDescriptionButton {
  /**
   * 行动点名称
   */
  string action_name = 1;
  /**
   * 行动点类型
   */
  string action_type = 2;
  /**
   * 行动点跳转链接
   */
  string action_jump_url = 3;
  /**
   * 行动点样式类型
   */
  string style_type = 4;
  /**
   * 行动点key
   */
  string key = 5;
}

message AwardCardDetailInfoDTO {
  /**
   * 奖励图标
   */
  string award_icon = 1;
  /**
   * 奖励描述
   */
  string award_name = 2;
  /**
   * 奖励规则
   */
  string award_rule = 3;
  /**
   * 奖励介绍
   */
  string award_desc = 4;
  /**
   * 奖励使用图片链接
   */
  string award_usage_url = 5;
  /**
   * 奖励使用介绍（到账说明）
   */
  string award_usage_desc = 6;
  /**
   * 奖励详情按钮类型
   * 1：跳转 2：复制
   */
  int32 award_detail_button_type = 7;
  /**
   * 奖励详情按钮文案
   */
  string award_detail_button_text = 8;
  /**
   * 奖励详情按钮跳转链接
   */
  string award_detail_button_url = 9;
}

message AwardCardDescriptionDTO {
  /**
   * 标题 公式说明、金额说明
   */
  string description_title = 1;

  /**
   * 内容 多个返点指标有多个返点公式
   */
  repeated string description_content = 2;
}

message UserActivityStrategyDTO {
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 标签
   */
  string tag = 3;
  /**
   * 活动状态
   */
  int32 activity_status = 4;
  /**
   * 活动领取时间
   */
  int64 draw_time = 5;
  /**
   * 活动详情
   */
  ActivityAggregationInfoDTO activity_info = 6;
  /**
   * 可领取用户子活动信息
   */
  repeated UserSubActivityStrategyDTO can_draw_sub_activity = 7;
  /**
   * 用户子活动信息
   */
  repeated UserSubActivityStrategyDTO user_sub_activity = 8;
  /**
  * 用户活动更新时间
  */
  int64 user_activity_update_time = 9;
  /**
   * 活动玩法类型
   */
  string activity_pattern_type = 10;
  /**
   * 用户完成活动可获得的奖励
   */
  repeated AwardShowDTO award_info = 11;
  /**
   * 活动领取人数
   */
  int64 draw_count = 12;
  /**
   * 场景展示配置
   */
  SceneShowConfigDTO scene_show_config = 13;
  /**
   * 手动提报信息
   */
  repeated ManualSubmitInfo manual_submit_info = 14;
  /**
   * 是否裂变活动
   */
  bool is_fission_activity = 15;
}

message SceneShowConfigDTO {
  /**
   * 场景展示类型
   * 0:不区分 1:一层场景区分 2:二层父子场景区分
   */
  int32 show_type = 1;
  /**
   * 场景展示信息
   */
  repeated SceneShowInfoDTO scene_show_info = 2;
}

message SceneShowInfoDTO {
  /**
   * 展示类型
   */
  int32 input_type = 1;
  /**
   * 场景展示名称
   */
  string name = 2;
  /**
   * 开始时间
   */
  int64 start_time = 3;
  /**
   * 结束时间
   */
  int64 end_time = 4;
  /**
   * 层级
   */
  int32 hierarchy = 5;
  /**
   * 子场景
   */
  repeated SceneShowInfoDTO sub_scene_show_info = 6;
  /**
   * 关联实体类型
   */
  int32 related_entity_type = 7;
  /**
   * 关联实体信息
   */
  repeated SceneShowRelatedEntityDTO related_entity = 8;
}

message SceneShowRelatedEntityDTO {
  /**
   * 实体ID
   * 子活动ID
   */
  int64 entity_id = 1;
}

message UserSubActivityStrategyDTO {
  /**
   * 子活动名称
   */
  string name = 1;
  /**
   * 子活动描述
   */
  string desc = 2;
  /**
   * 子活动开始时间
   */
  uint64 start_time = 3;
  /**
   * 子活动结束时间
   */
  uint64 end_time = 4;
  /**
   * 周期类型
   * 1: 活动期不变 2: 每日循环 3: 按特定周期循环
   */
  uint32 cycle_type = 5;
  /**
   * 活动周期
   */
  uint32 cycle_day = 6;
  /**
   * 完成奖励发放方式 1、发最高档 2、完成一档发一档
   */
  uint32 step_complete_config = 7;
  /**
   * 排序
   */
  uint32 sub_activity_order = 8;
  /**
   * 详情页 url
   */
  string detail_page_url = 9;
  /**
   * 已领取的任务周期列表
   * (对于周期类型为 1: 活动期不变 只有一个元素)
   */
  repeated UserTaskCycleStrategyDTO user_task_cycle = 10;
  /**
   * 子活动 ID
   */
  uint64 sub_activity_id = 11;
  /**
   * 奖励选择配置
   * 未领取时返回
   */
  AwardSelectionConfigDTO award_selection_config = 12;
  /**
   * 排行榜单类型（仅排行榜）
   */
  uint32 rank_cycle_type = 13;
  /**
   * 排行奖励范围（仅排行榜）
   */
  uint64 rank_award_range = 14;
  /**
   * 活动玩法类型
   */
  string activity_pattern_type = 15;
  /**
   * 活动ID
   */
  uint64 activity_id = 16;
  /**
   * 前置子活动ID
   */
  uint64 pre_stage_sub_activity_id = 17;
  /**
   * 阶段状态
   */
  uint32 stage_status = 18;
  /**
   * 子活动展示名称
   */
  string sub_activity_show_name = 19;
  /**
   * 后置子活动ID
   */
  repeated uint64 post_stage_sub_activity_id = 20;
  /**
   * 是否是裂变子活动
   */
  bool is_fission_sub_activity = 21;
}

message AwardSelectionConfigDTO {
  /**
   * 奖励选择类型 1为任选其一 2为全部奖励
   */
  uint32 award_selection_type = 1;
  /**
   * 可选奖励数量
   * 奖励选择类型为 部分奖励 返回
   */
  uint32 selectable_award_num = 2;
  /**
   * 活动可选奖励展示
   * 奖励选择类型为 部分奖励 返回
   */
  repeated UserAwardAggregationDTO selectable_user_award = 12;
  /**
   * 奖励分层信息（仅排行榜）
   */
  RankAwardStageDTO rank_award_stage = 3;
}

message UserTaskCycleStrategyDTO {
  /**
   * 周期时间 (仅用于周期类型为 2: 每日循环 3: 按特定周期循环)
   */
  uint64 cycle_time = 1;

  /**
   * 周期状态
   */
  uint32 status = 2;

  /**
   * 周期展示状态
   */
  uint32 show_status = 3;

  /**
   * 是否是当前周期
   */
  bool is_current_cycle = 4;

  /**
   * 任务阶梯信息 (对于单阶梯任务, 只有一个元素)
   */
  repeated UserTaskStepStrategyDTO user_task_step = 5;
  /**
   * 周期结束时间 (仅用于周期类型为 2: 每日循环 3: 按特定周期循环)
  */
  uint64 cycle_end_time = 6;
}

message UserTaskStepStrategyDTO {
  /**
   * 任务信息 (如果同周期下, 单个任务映射多个阶梯任务信息一致)
   */
  UserTaskAggregationDTO user_task = 1;

  /**
   * 任务状态
   */
  int32 status = 2;

  /**
   * 任务展示状态
   */
  int32 show_status = 3;

  /**
   * 阶梯
   */
  uint32 step = 4;

  /**
   * 指标信息 (不同阶梯指标目标值存在差异)
   */
  repeated UserIndicatorStepStrategyDTO user_indicator = 5;

  /**
   * 奖励聚合信息 (不同阶梯奖励存在差异)
   */
  repeated UserAwardAggregationDTO user_award = 6;

  /**
   * 奖励结算状态
   * 1 未结算 2 已结算
   */
  uint32 award_settle_status = 7;

  /**
   * 用户排名信息（仅排行榜）
   */
  UserRankingInfoDTO user_ranking_info = 8;

  /**
   * 用户所在排行奖励分层（仅排行榜）
   * 如果没在任何一个分层，返回最接近的分层
   */
  RankAwardStageDTO closest_rank_award_stage = 9;

  /**
   * 全部奖励分层信息（仅排行榜）
   */
  repeated UserRankAwardConfigDTO user_rank_award_config = 10;

  /**
   * 裂变数据
   * 仅裂变子活动
   */
  FissionDataDTO fission_data = 11;
}

message FissionDataDTO {
  /**
   * 待裂变用户数量
   */
  int32 pending_fission_user_count = 1;
  /**
   * 用户裂变数据列表
   */
  repeated UserFissionDataDTO user_fission_data = 2;
}

message UserFissionDataDTO {
  /**
   * 用户ID
   */
  int64 user_id = 1;
}

message UserRankingInfoDTO {
  /**
   * 名次
   */
  uint64 rank_index = 1;
  /**
   * 排名指标 ID
   */
  uint64 rank_indicator_id = 2;
  /**
   * 排名指标名称
   */
  string rank_indicator_name = 3;
  /**
   * 当前的展示值（含单位）
   */
  string rank_indicator_value = 4;
  /**
   * 指标统计类型
   */
  uint32 indicator_statistics_type = 5;
  /**
   * 排名更新时间
   */
  uint64 rank_update_time = 6;
  /**
   * 指标提示
   */
  string indicator_tip = 7;
}

message UserRankAwardConfigDTO {
  /**
   * 奖励分层信息
   */
  RankAwardStageDTO rank_award_stage = 1;
  /**
   * 奖励聚合信息
   */
  repeated UserAwardAggregationDTO user_award = 3;
}

message RankAwardStageDTO {
  /**
   * 奖励分层序号
   */
  uint32 rank_stage_num = 1;
  /**
   * 奖励开始名次
   */
  uint32 rank_start_index = 2;
  /**
   * 奖励结束名次
   */
  uint32 rank_end_index = 3;
}

message UserIndicatorStepStrategyDTO{
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 当前值
   */
  uint64 current_value = 2;
  /**
   * 目标值
   */
  uint64 target_value = 3;
  /**
   * 状态
   */
  uint32 status = 4;
  /**
   * 指标名称
   */
  string indicator_name = 5;
  /**
   * 指标日均值
   */
  uint64 current_day_avg_value = 6;

  /**
   *  基期指标日均值
   */
  uint64 base_day_avg_value = 7;

  /**
    纵向活动指标数据
   */
  UserPortraitIndicatorAggregationDTO portrait_indicator = 8;

  /**
   * 指标展示状态
   */
  uint32 show_status = 9;

  /**
 * 当前的展示值（含单位）
 */
  string show_current_value = 10;

  /**
   * 目标的展示值（含单位）
   */
  string show_target_value = 11;


  /**
   * 指标提示
   */
  string indicator_tip = 12;

  /**
   * 指标统计类型
   */
  uint32 indicator_statistics_type = 13;
}

message GetUserResourceRuleAwardInfoRequest{
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 2;
  /**
   * 补贴规则ID
   */
  uint64 resource_rule_id = 3;

}

message GetUserResourceRuleAwardInfoResponse{
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  UserResourceAwardDetailDTO data = 3;
}

message UserResourceAwardDetailDTO {
  /**
  * 奖励记录总和
  */
  uint64 total_record_award = 1;
  /**
   * 已发放的奖励总和
   */
  uint64 total_send_award = 2;
  /**
   * 被风控的奖励总和
   */
  uint64 total_risk_award = 3;
}

message GetActivitySummaryInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 场景值
   */
  string scene = 2;
}

message GetActivitySummaryInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  ActivitySummaryInfo data = 3;
}

message ActivitySummaryInfo {
  /**
   * 活动基础数据
   */
  ActivityInfoDTO activity_info = 1;
  /**
   * 任务基础数据
   */
  repeated TaskSummaryInfo task_info = 2;
}

message ActivityInfoDTO {
  /**
   * 活动ID
   */
  uint64 id = 1;
  /**
   * 活动名称
   */
  string name = 2;
  /**
   * 别名
   */
  string alias = 3;
  /**
   * 开始时间
   */
  uint64 start_time = 4;
  /**
   * 结束时间
   */
  uint64 end_time = 5;
  /**
   * 领取开始时间
   */
  uint64 draw_start_time = 6;
  /**
   * 领取结束时间
   */
  uint64 draw_end_time = 7;
  /**
   * 活动状态
   */
  int32 status = 8;
}

message TaskSummaryInfo {
  /**
   * 任务ID
   */
  uint64 id = 1;
  /**
   * 任务名称
   */
  string name = 2;
  /**
   * 别名
   */
  string alias = 3;
  /**
   * 任务开始时间
   */
  uint64 start_time = 4;
  /**
   * 任务结束时间
   */
  uint64 end_time = 5;
  /**
   * 阶段
   */
  uint32 stage = 6;
}

message GetRankActivityAggregationInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 来源
   */
  string source = 2;
  /**
   * 用户ID，不传返回所有赛道
   * FIXME scene 区分
   */
  uint64 user_id = 3;
}

message GetRankActivityAggregationInfoResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 排行榜活动数据
   */
  RankActivityDetailInfo data = 3;
}

message RankActivityDetailInfo {
  /**
   * 活动基础数据
   */
  ActivityInfoDTO activity_info = 1;
  /**
   * 榜单类型
   */
  string rank_cycle = 2;
  /**
   * 排行榜赛道数据
   */
  repeated RankTraceDetailInfo trace_info = 3;
}

message RankTraceDetailInfo {
  /**
   * 赛道名称
   */
  string trace_name = 1;
  /**
   * 赛道序号
   */
  uint32 trace_num = 2;
  /**
   * 开始时间
   */
  uint64 trace_start_time = 3;
  /**
   * 结束时间
   */
  uint64 trace_end_time = 4;
  /**
   * top20用户
   */
  repeated uint64 top_twenty_user = 5;
  /**
   * 是否已经终结
   */
  bool terminal = 6;
  /**
   * 排名指标ID
   */
  uint64 rank_indicator_id = 7;
  /**
   * 排名指标名称
   */
  string rank_indicator_name = 8;
  /**
   * 用户排名信息
   */
  repeated UserRankingBriefInfoDTO user_ranking_brief_info = 9;
  /**
   * 赛道统计类型
   */
  uint32 trace_statistics_type = 10;
  /**
   * 赛道更新时间
   */
  uint64 trace_update_time = 11;
}

message UserRankingBriefInfoDTO {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 排名
   */
  uint64 rank_index = 2;
  /**
   * 热力值
   */
  string rank_indicator_value = 3;
}

message GetUserAwardRoiInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 发奖bizId
   */
  string unique_id = 2;
  /**
   * 场景值
   */
  string scene = 3;
}

message GetUserAwardRoiInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户发奖ROI信息
   */
  UserAwardRoiDTO data = 3;
}

message UserAwardRoiDTO {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动id
   */
  uint64 activity_id = 2;
  /**
   * 任务ID
   */
  uint64 task_id = 3;
  /**
   * 基值信息
   */
  BaseInfoDTO base_info = 4;
  /**
   * 指标信息
   */
  repeated UserIndicatorAggregationDTO user_indicator = 5;
}

message BaseInfoDTO {
  /**
   * 基值开始时间
   */
  uint64 fixed_start_time = 1;
  /**
   * 基值结束时间
   */
  uint64 fixed_end_time = 2;
  /**
   * 基值算法类型
   */
  string base_algorithm = 3;
  /**
   * 基值时间类型
   */
  string indicator_time_type = 4;
}

message GetUserTaskSummaryInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 场景值
   */
  string scene = 3;
}

message GetUserTaskSummaryInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户任务信息
   */
  repeated UserTaskSummaryDTO data = 3;
}

message UserTaskSummaryDTO {
  /**
   * 任务ID
   */
  uint64 task_id = 1;
  /**
   * 任务状态
   */
  uint32 status = 2;
  /**
   * 是否配置奖励
   */
  bool has_award = 3;
}

message GetUserActivityTipsRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
}

message GetUserActivityTipsResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 提示
   */
  ActivityTipsDTO data = 3;
}

message ActivityTipsDTO {
  /**
   * 任务进度更新数
   */
  uint32 task_update_num = 1;
  /**
   * 子任务完成数
   */
  uint32 task_complete_num = 2;
  /**
   * 奖励下发数
   */
  repeated ActivityTipsAwardDTO task_award_info = 3;
  /**
   * 任务总数
   */
  uint64 task_total_num = 4;
  /**
   * 已完成任务数
   */
  uint64 task_success_num = 5;
}

message ActivityTipsAwardDTO {
  /**
   * 奖励类型
   */
  uint32 award_type = 1;
  /**
   * 奖励总数
   */
  uint64 total = 2;
}

message GetUserPlatformActivityInfoRequest {
  /**
   * 场景
   */
  string scene = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 用户ID
   */
  uint64 user_id = 3;
}

message GetUserPlatformActivityInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户活动配置信息
   */
  repeated UserLayerConfigDTO data = 3;
}

message UserLayerConfigDTO {
  /**
   * 分层父任务ID
   */
  uint64 parent_task_id = 1;
  /**
   * 分层 任务类型
   */
  uint32 task_type = 2;
  /**
   * 各阶段奖励配置
   */
  repeated UserStepConfigDTO step_config = 3;
  /**
   * 分层任务状态
   */
  uint32 user_task_status = 4;
  /**
   * 分层任务对象
   */
  LayerTaskDTO layer_task = 5;
}

message LayerTaskDTO {
  /**
   * 任务id
   */
  int64 id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 父任务id
   */
  int64 parent_task = 3;
  /**
   * 前置任务id
   */
  int64 pre_task = 4;
  /**
   * 优先级
   */
  int32 priority = 5;
  /**
   * 开始时间
   */
  int64 start_time = 6;
  /**
   * 结束时间
   */
  int64 end_time = 7;
  /**
   * 完成条件
   */
  string complete_condition = 8;
  /**
   * alias
   */
  string alias = 9;
  /**
   * 任务状态
   */
  int32 status = 10;
  /**
   * 任务周期类型：1-绝对时间，2-相对时间
   */
  int32 period_type = 11;
  /**
   * 周期天数
   */
  int32 period_day = 12;
  /**
   * 任务标题
   */
  string name = 13;
  /**
   * 任务描述
   */
  string description = 14;
  /**
   * 补贴任务ID
   */
  uint64 resource_rule_id = 15;
  /**
   * 阶段
   */
  uint64 stage = 16;
  /**
   * 额外信息
   */
  string ext = 17;
  /**
   * 任务类型 1单阶段 2多阶段
   */
  uint32 type = 18;
  /**
   * 用户任务开始时间
   */
  uint64 user_task_start_time = 19;
  /**
   * 用户任务结束时间
   */
  uint64 user_task_end_time = 20;
  /**
   * 排行榜相关信息
   */
  RankInfoDTO rank_info = 21;
}

message RankInfoDTO {
  /**
   * 是否排行榜
   */
  bool leaderboard = 1;
  /**
   * 有奖励的排行范围
   */
  uint32 ranking_range = 2;
}

message UserStepConfigDTO {
  /**
   * 阶段
   */
  uint32 step = 1;
  /**
   * 任务ID
   */
  uint64 task_id = 2;
  /**
   * 周期
   */
  uint32 period = 3;
  /**
   * 周期开始时间
   */
  uint64 start_time = 4;
  /**
   * 周期结束时间
   */
  uint64 end_time = 5;
  /**
   * 奖励阶段配置
   */
  repeated UserStepAwardInfoDTO award_step_info = 6;
  /**
   * 指标阶段配置
   */
  repeated UserStepIndicatorInfoDTO indicator_step_info = 7;
  /**
   * 任务当前状态
   */
  uint32 user_task_status = 8;
  /**
   * 奖励选择方式
   */
  uint32 award_select_type = 9;
  /**
   * 用户选择奖励
   */
  repeated uint64 user_select_award = 10;
  /**
    用户选择奖励组
   */
  repeated AwardSelectGroupDTO award_select_group= 11;
}

message AwardSelectGroupDTO {
  uint32  condition_type = 1 ;
  
  repeated  uint32  award_type = 2 ;
}

message UserStepIndicatorInfoDTO {
  /**
   * 指标配置ID
   */
  uint64 indicator_config_id = 1;
  /**
   * 指标ID
   */
  uint64 indicator_id = 2;
  /**
   * 指标名称
   */
  string indicator_name = 3;
  /**
   * 指标单位
   */
  string indicator_unit = 4;
  /**
   * 指标目标
   */
  uint64 target_value = 5;
  /**
   * 当前值
   */
  uint64 current_value = 6;
  /**
   * 当前状态
   */
  uint32 status = 7;
  /**
   * 展示值转换值
   */
  string target_show_value = 8;
  /**
   * 当前值转换值
   */
  string current_show_value = 9;
}

message UserStepAwardInfoDTO {
  /**
   * 奖励类型
   */
  uint32 award_type = 1;
  /**
   * 奖励计算类型
   */
  string type = 2;
  /**
   * 固定奖励值
   */
  uint64 fixed_award_value = 3;
  /**
   * 奖励上限
   */
  uint64 max_award_value = 4;
  /**
   * 奖励下限
   */
  uint64 min_award_value = 5;
  /**
   * 返点信息
   */
  repeated ReturnAwardConfigDTO return_config = 6;
  /**
   * 奖励配置ID
   */
  uint64 award_config_id = 7;
  /**
   * 奖励发放状态
   */
  uint32 award_status = 8;
  /**
   * 达成阶段
   */
  uint32 max_reach_step = 9;
  /**
   * 追加的奖励集合
   * 场景1:不能被展示到b端的奖励类型case，由表达侧组装控制
   */
  repeated UserStepAwardInfoDTO append_award_info = 10;
  /**
   * 动态计算奖励值
   */
  uint64 dynamic_award_value = 11;
  /**
   * 奖励计算附加信息
   */
  string ext = 12;
  /**
   * 奖励名称
   */
  string award_name = 13;
  /**
   * 用户奖励记录值
   */
  uint64 user_award_record_value = 14;

  /**
   * 磁力金牛权益包信息
   */
  MagnetInterestPackageInfoDTO magnet_interest_package_info = 15;
}

message ReturnAwardConfigDTO {
  /**
   * 返点指标ID
   */
  uint64 indicator_id = 1;
  /**
   * 返点指标名称
   */
  string indicator_name = 2;
  /**
   * 返点方式
   */
  uint32 return_type = 3;
  /**
   * 返点比例
   */
  string return_percent = 4;
  /**
   * 计算因子
   */
  string calc_factor = 5;
  /**
   * 计算公式
   */
  string formula_show_name = 6;
}

message GetUserActivityProgressRequest {
  /**
   * 商家Id
   */
  uint64 user_id = 1;
  /**
   * 活动id
   */
  uint64 activity_id = 2;

  /**
   * 场景 （当前只有TaskCenterComponent）
   */
  string scene = 3;
}

message GetUserActivityProgressResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 活动完成进度
   */
  UserActivityProgressDTO data = 3;
}

message UserActivityProgressDTO {
  /**
   * 活动完成进度
   */
  uint32 complete_rate = 3;
}

message GetUserActivityBriefInfoRequest {
  /**
   * 用户id
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 场景值
   */
  string scene = 3;
}

message GetUserActivityBriefInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户聚合活动信息
   */
  UserActivityBriefInfoDTO data = 3;
}

message UserActivityBriefInfoDTO {
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 活动id
   */
  int64 activity_id = 2;
  /**
   * 标签
   */
  string tag = 3;
  /**
   * 活动状态
   */
  int32 activity_status = 4;
  /**
   * 活动领取时间
   */
  int64 draw_time = 5;
  /**
   * 活动详情
   */
  ActivityAggregationInfoDTO activity_info = 6;
  /**
  * 用户活动更新时间
  */
  int64 user_activity_update_time = 7;
  /**
   * 活动玩法类型
   */
  string activity_pattern_type = 8;
}

message GetUserSubActivityInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 子活动ID
   */
  repeated uint64 sub_activity_id = 3;
  /**
   * 场景
   */
  string scene = 4;
  /**
   * 周期锚点时间 (仅对周期子活动生效)
   * 对于周期子活动, 不传返回全部周期任务信息
   * 如果锚点时间所在周期不存在, 则返回值为空
   */
  uint64 cycle_anchor_time = 5;

  /**
   * 周期数量区间 (仅对周期子活动生效)
   * 对于周期子活动, 不传返回全部周期任务信息
   */
  repeated uint32 cycle_size_interval = 6;
}

message GetUserSubActivityInfoResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 用户子活动信息
   */
  repeated UserSubActivityStrategyDTO data = 3;
}

message GetCarouselListRequest {
  /**
   * 用户ID
   */
  int64 user_id = 1;
  /**
   * 场景
   */
  int32 scene = 2;
  /**
   * 轮播数量
   */
  int32 limit = 3;
}

message GetCarouselListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 轮播信息
   */
  GetCarouselListResultDTO data = 3;
}

message GetCarouselListResultDTO {
  /**
   * 轮播信息
   */
  string item_list = 1;
}

message BatchGetUserActivityDetailInfoRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;

  /**
   * 活动ID
   */
  repeated uint64 activity_id = 2;

  /**
   * 来源
   */
  string source = 3;
}

message BatchGetUserActivityDetailInfoResponse {
  /**
   *返回结果码
   */
  uint32 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
   * 用户活动详情
   */
  map<uint64, UserActivityDetailInfoDTO> data = 3;
}

message UserActivityDetailInfoDTO {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;

  /**
   * 用户ID
   */
  uint64 user_id = 2;

  /**
   * 活动名称
   */
  string activity_name = 3;

  /**
   * 活动开始时间
   */
  uint64 start_time = 4;

  /**
   * 活动结束时间
   */
  uint64 end_time = 5;

  /**
   * 活动领取截止时间
   */
  uint64 draw_end_time = 6;

  /**
   * 用户活动状态
   */
  uint32 user_activity_status = 7;

  /**
   * 用户子活动
   */
  repeated UserSubActivityDetailInfoDTO user_sub_activity = 8;

  /**
   * 活动描述
   */
  string activity_desc = 9;

  /**
   * 活动详情页链接
   */
  string activity_detail_url = 10;
}

message UserSubActivityDetailInfoDTO {
  /**
  * 父任务ID
  */
  uint64 task_id = 1;

  /**
  * 任务开始时间
  */
  uint64 start_time = 2;

  /**
  * 任务结束时间
  */
  uint64 end_time = 3;

  /**
  * 子活动名称
  */
  string name = 4;

  /**
  * 标题
  */
  string main_title = 5;

  /**
  * 用户周期任务信息
  */
  repeated UserCycleTaskDetailInfoDTO user_cycle_task = 6;

  /**
   * 状态
   */
  uint32 status = 7;
}

message UserCycleTaskDetailInfoDTO {
  /**
   * 周期
   */
  uint32 cycle_num = 1;

  /**
 * 开始时间
 */
  uint64 start_time = 2;

  /**
   * 结束时间
   */
  uint64 end_time = 3;

  /**
  * 用户任务信息
  */
  repeated UserTaskDetailInfoDTO user_task = 4;

  /**
   * 用户任务状态
   */
  uint32 status = 5;
}

message UserTaskDetailInfoDTO {
  /**
   * 阶梯
   */
  uint32 step = 1;

  /**
   * 用户任务状态
   */
  uint32 status = 2;

  /**
  * 用户指标信息
  */
  repeated UserIndicatorDetailInfoDTO user_indicator = 3;

  /**
  * 用户奖励信息
  */
  repeated UserAwardDetailInfoDTO user_award = 4;

  /**
   * 开始时间
   */
  uint64 start_time = 5;

  /**
   * 结束时间
   */
  uint64 end_time = 6;

  /**
   * 子任务ID
   */
  uint64 task_id = 7;
}

message UserIndicatorDetailInfoDTO {
  /**
   * 指标ID
   */
  uint64 indicator_id = 1;

  /**
   * 当前值
   */
  uint64 current_value = 2;

  /**
   * 目标值
   */
  uint64 target_value = 3;

  /**
   * 用户指标状态
   */
  uint32 user_indicator_status = 4;

  /**
   * 主标题
   */
  string main_title = 5;

  /**
   * 副标题
   */
  string sub_title = 6;

  /**
   * 攻略链接
   */
  string guide_url = 7;

  /**
   * 按钮
   */
  ButtonInfoDTO button = 8;

  /**
   * 是否展示进度条
   */
  bool show_progress = 9;

  /**
   * 指标名称
   */
  string indicator_name = 10;

  /**
   * 单位
   */
  string unit = 11;
}

message UserAwardDetailInfoDTO {
  /**
   * 奖励类型
   */
  int32 award_type = 1;

  /**
   * 奖励名称
   */
  string award_name = 2;

  /**
   * 奖励固定配置类型 10：固定值 20：固定返点比例
   */
  uint32 config_fix_type = 3;

  /**
   * 奖励固定配置值
   */
  uint64 config_fix_value = 4;

  /**
   * 奖励返点规则
   */
  repeated SpecifyIndicatorInfoDTO return_indicator_info = 5;

  /**
   * 单位
   */
  string unit = 6;

  /**
   * 奖励图标
   */
  string icon = 7;

  /**
   * 奖励展示信息
   */
  AwardShowDTO award_show_info = 8;
}

message ManualSubmitInfo {
  // 父任务id
  uint64 parent_task_id = 1;
  // 手动提报的字任务信息，默认按照开始时间正排
  repeated ManualSubmitChildTaskInfo task_info = 2;
  // 手动提报次数
  uint32 limit_num = 3;
}

message ManualSubmitChildTaskInfo {
  // 子任务id
  uint64 task_id = 1;
  // 子任务开始时间
  uint64 start_time = 2;
  // 子任务结束时间
  uint64 end_time = 3;
  // 提报状态 不可提报：0；已提报：1；等待提报：2
  uint32 submit_status = 4;
}


message GetUserFissionTaskDetailInfoRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 子任务ID
   */
  uint64 task_id = 2;
  /**
   * 用户ID
   */
  uint64 user_id = 3;
  /**
   * 分页参数
   */
  CursorPageRequest cursor_page_request = 4;
}

message CursorPageRequest {
  /**
   * 游标
   */
  int32 cursor = 1;
  /**
   * limit
   */
  int32 limit = 2;
}

message GetUserFissionTaskDetailInfoResponse {
  /**
   * 结果码
   */
  uint32 result = 1;
  /**
   * 异常信息
   */
  string error_msg = 2;
  /**
   * data
   */
  UserFissionTaskDetailInfoData data = 3;
}

message UserFissionTaskDetailInfoData {
  /**
   * 游标分页返回值
   */
  CursorPageResponse cursor_page_response = 1;
  /**
   * 用户裂变任务详细信息列表
   */
  repeated UserFissionTaskDetailInfoItem user_fission_task_detail_info_item = 2;
}

message CursorPageResponse {
  /**
   * 游标
   */
  int32 next_cursor = 1;
  /**
   * 是否有更多记录
   */
  bool has_more = 2;
}

message UserFissionTaskDetailInfoItem {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 用户裂变指标数据
   */
  repeated UserFissionIndicatorDataDTO user_fission_indicator_data = 2;
}

message UserFissionIndicatorDataDTO {
  /**
   * 裂变原始指标ID
   */
  int64 fission_raw_indicator_id = 1;
  /**
   * 裂变原始指标名称
   */
  string fission_raw_indicator_name = 2;
  /**
   * 裂变原始指标展示值（含单位）
   */
  string fission_raw_indicator_show_value = 3;
}

service UserActivityAggregationService {
  /**
   * 获取用户活动聚合信息
   */
  rpc GetUserActivityAggregationInfo (GetUserActivityAggregationInfoRequest) returns (GetUserActivityAggregationInfoResponse);

  /**
   * 获取活动汇总信息
   */
  rpc GetUserActivitySummaryInfo (GetUserActivitySummaryInfoRequest) returns (GetUserActivitySummaryInfoResponse);

  /**
   * 查询补贴奖励详情
   */
  rpc GetUserResourceRuleAwardInfo (GetUserResourceRuleAwardInfoRequest) returns (GetUserResourceRuleAwardInfoResponse);

  /*
   * 获取活动基础信息
   */
  rpc GetActivitySummaryInfo (GetActivitySummaryInfoRequest) returns (GetActivitySummaryInfoResponse);

  /**
   * 获取排行活动用户总信息
   */
  rpc GetRankActivityAggregationInfo (GetRankActivityAggregationInfoRequest) returns (GetRankActivityAggregationInfoResponse);

  /**
   * 获取用户活动展示信息
   */
  rpc GetActivityStrategyInfo (GetUserActivityStrategyInfoRequest) returns (GetUserActivityStrategyInfoResponse);

  /**
   * 查询用户发奖ROI信息
   */
  rpc GetUserAwardRoiInfo (GetUserAwardRoiInfoRequest) returns (GetUserAwardRoiInfoResponse);

  /**
   * 获取用户任务信息
   */
  rpc GetUserTaskSummaryInfo (GetUserTaskSummaryInfoRequest) returns (GetUserTaskSummaryInfoResponse);

  /**
   * 获取用户活动进度提示
   */
  rpc GetUserActivityTips (GetUserActivityTipsRequest) returns (GetUserActivityTipsResponse);

  /**
   * 获取用户在一个活动下，各个分层任务的奖励配置信息（运营策略后台任务模型，支持自定义上传奖励解析、阶梯规则解析）
   */
  rpc GetUserPlatformActivityInfo (GetUserPlatformActivityInfoRequest) returns (GetUserPlatformActivityInfoResponse);

  rpc GetUserActivityProgress (GetUserActivityProgressRequest) returns (GetUserActivityProgressResponse);

  /**
   * 获取用户活动基本信息
   */
  rpc GetUserActivityBriefInfo (GetUserActivityBriefInfoRequest) returns (GetUserActivityBriefInfoResponse);

  /**
   * 获取用户子活动信息列表
   */
  rpc GetUserSubActivityInfo (GetUserSubActivityInfoRequest) returns (GetUserSubActivityInfoResponse);
  /**
   * 获取用户轮播列表
   */
  rpc GetCarouselList (GetCarouselListRequest) returns (GetCarouselListResponse);

  /**
   * 批量查询用户活动详情信息
   */
  rpc BatchGetUserActivityDetailInfo (BatchGetUserActivityDetailInfoRequest) returns (BatchGetUserActivityDetailInfoResponse);

  /**
   * 获取用户裂变任务信息
   */
  rpc GetUserFissionTaskDetailInfo (GetUserFissionTaskDetailInfoRequest) returns (GetUserFissionTaskDetailInfoResponse);
}