syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.policy;
import "activity/policy/staff_policy_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy";
option java_outer_classname = "StaffPolicySellerServiceProto";


message QuerySlrPolicyInfoReq {

  string operator = 1;

  int64 activity_id = 2;

  int64 user_id = 3;

  // 2:已报名 1:未报名 0:全部
  int32 draw_type = 4;

  int32 page_num = 5;

  int32 page_size = 6;

  repeated int64 activity_ids = 7;

  repeated string first_industry_code = 8;

  repeated string second_industry_code = 9;

  repeated string third_industry_code = 10;

  string scene = 11;


  // 角色标签
  string site_code = 12;

  // 在政策详情页支持用户名称过滤
  string nick_name_filter = 13;

}

message ExportSlrPolicyInfoReq {
  string operator = 1;

  int64 activity_id = 2;
  // 在政策详情页支持用户id过滤
  int64 user_id = 3;

  // 2:已报名 1:未报名 0:全部
  int32 draw_type = 4;

  repeated string first_industry_code = 5;

  repeated string second_industry_code = 6;

  repeated string third_industry_code = 7;

  // 角色标签
  string site_code = 8;

  // 在政策详情页支持用户名称过滤
  string nick_name_filter = 9;

}

message QuerySlrAwardInfoReq {
  string operator = 1;

  int64 activity_id = 2;

  int64 user_id = 3;

  int32 sub_activity_order = 4;

  int32 layer_order = 5;

  int32 period_order = 6;
}

message QuerySlrPolicyDetailReq {

  string operator = 1;

  int64 activity_id = 2;

  int64 user_id = 3;

  string scene = 4;

}

message QuerySlrPolicySummaryInfoReq {
  string operator = 1;
  // 不一定支持批量，有性能问题
  repeated int64 user_id = 2;
}


message QueryPolicySignUpInfoResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  repeated PolicyUserSignUpDTO data = 3;

  int64 total = 4;
  /**
   * 下发被风控数量
   */
  int64 risk_total = 5;
}

message QuerySlrAwardInfoResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  repeated PolicyUserAwardDTO data = 3;
}

message QuerySlrPolicyDetailResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  string data = 3;
}

message QuerySlrPolicySummaryInfoResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  repeated PolicyUserSummaryDTO data = 3;

}

message ExportSlrPolicyInfoReqResponse {
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

service StaffPolicySellerService {
  /**
  查询政策&商家列表信息
   */
  rpc QuerySlrPolicyInfoList(QuerySlrPolicyInfoReq) returns (QueryPolicySignUpInfoResponse);
  /**
  查询政策下商家奖励信息
   */
  rpc QuerySlrAwardInfoList(QuerySlrAwardInfoReq) returns (QuerySlrAwardInfoResponse);
  /**
  查询商家政策详情信息（service组件化提供）
   */
//  rpc QuerySlrPolicyDetail(QuerySlrPolicyDetailReq) returns (QuerySlrPolicyDetailResponse);
  /**
  查询商家政策汇总
   */
  rpc QuerySlrPolicySummaryInfo(QuerySlrPolicySummaryInfoReq) returns (QuerySlrPolicySummaryInfoResponse);
  /**
    数据导出
   */
  rpc ExportSlrPolicyInfoList(ExportSlrPolicyInfoReq) returns (ExportSlrPolicyInfoReqResponse);

}