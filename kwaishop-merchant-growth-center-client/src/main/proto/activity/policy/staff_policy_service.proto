syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.policy;
import "activity/policy/staff_policy_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy";
option java_outer_classname = "StaffPolicyServiceProto";

message QueryPolicyListReq {

  string operator = 1;

  repeated int64 activity_id = 2;
   // 一级行业编码
  repeated string first_industry_code = 3;
  // 二级行业编码
  repeated string second_industry_code = 4;
  // 三级行业编码
  repeated string third_industry_code = 5;

  // 站点标签
  string site_code = 6;
}

message QueryPolicyDetailReq {
  string operator = 1;

  int64 activity_id = 2;
}

message QueryPolicySignUpInfoReq {
  string operator = 1;

  int64 activity_id = 2;

  int64 page_num = 3;

  int64 page_size = 4;
}

message QueryPolicyListResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
  data
   */
  repeated StaffPolicyDTO data = 3;
}

message QueryPolicyDetailResponse {
  /**
 * 返回结果码
 */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;

  StaffPolicyDTO data = 3;
}



service StaffPolicyService {
  /**
  查询小二政策库-政策列表
   */
  rpc QueryPolicyList(QueryPolicyListReq) returns (QueryPolicyListResponse);
  /**
  查询小二政策库政策详情
   */
  rpc QueryPolicyDetail(QueryPolicyDetailReq) returns (QueryPolicyDetailResponse);

}