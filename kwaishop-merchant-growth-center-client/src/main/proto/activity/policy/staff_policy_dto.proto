syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.policy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy";
option java_outer_classname = "StaffPolicyDTOProto";


message StaffPolicyDTO {

  StaffPolicyBaseInfoDTO base_info = 1;
  // 需要返回当前小二负责的商家的报名率
  string sign_up_ratio = 2;
  // 需要返回当前小二负责的商家的未报名人数
  int64 no_sign_up_user_cnt = 3;
  // 子活动规则描述
  repeated StaffSubPolicyRuleDTO sub_rule_list = 4;

  string seller_selection_threshold = 5;

  string rule_desc = 6;

  string outer_link = 7;
}

message StaffPolicyBaseInfoDTO {
  string name = 1;

  int64 policy_id = 2;

  repeated string show_tag = 3;
  // 政策周期描述
  string policy_period = 4;

  int32 sign_up_limit_day = 5;

  string policy_rule_desc = 6;

  repeated PolicyAwardDescDTO award_desc_list = 7;

  bool need_sign_up = 8;

  int32 status = 9;

  string status_name = 10;

  string sign_up_pc_url = 11;

  string sign_up_app_url = 12;
  // 玩法
  string policy_pattern = 13;

  string creator = 14;
  // 是否为极速版
  bool is_lite = 15;
  // 活动截止天数
  int32 activity_end_limit_day = 16;
  // 是否隐藏任务进度
  bool hide_task_progress = 17;
}

message StaffSubPolicyRuleDTO {

  // 商家圈选门槛
  string seller_selection_threshold = 1;

  string rule_desc = 2;

  string rule_pic_url = 3;

  string rule_name = 4;
}

message PolicyAwardDescDTO {

  string award_name = 1;

  // 奖励上限制
  string award_max_threshold = 2;
}

message PolicyUserSignUpDTO {


  string user_name = 1;

  int64 user_id = 2;

  string user_pic = 3;

  string staff_code = 4;

  int32 risk = 5;
 // 预测完成
  int32 predict_success = 6;
  // 实际完成
  int32 success = 7;
  // 报名状态 1 已报名 2 未报名 3 无需报名
  int32 sign_up_status = 8;

  StaffPolicyBaseInfoDTO base_info = 9;


}

message PolicyUserAwardDTO {
  int64 task_id = 1;

  string award_name = 2;

  string award_type = 3;

  int64 award_value = 4;

  string status_desc = 5;

  string risk_reason = 6;

  int64 update_time = 7;

  string award_show_value = 8;

  string task_name = 9;
}

message PolicyUserSummaryDTO {
  int64 user_id = 1;

  int64 process_cnt = 3;

  int64 wait_sign_up_cnt = 4;

  int64 wait_finish_cnt = 5;

  repeated PolicyAwardDescDTO award_desc_list = 6;
}