syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.policy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy";
option java_outer_classname = "StaffPolicyTestServiceProto";

message UpdateStaffShowConfigReq {
  int64 activity_id  = 1;

  string show_config = 2;

  string operator = 3;
}

message UpdateStaffShowConfigResponse {
  /**
    * 返回结果码
    */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

service StaffPolicyTestService {
  /**
  更新小二政策库配置
   */
  rpc UpdateStaffShowConfig(UpdateStaffShowConfigReq) returns (UpdateStaffShowConfigResponse);


}