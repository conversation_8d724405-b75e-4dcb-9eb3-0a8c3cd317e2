syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.activity.manage;
import "activity/indicator/activity_indicator_service.proto";
import "activity/award/activity_award_service.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage";
option java_outer_classname = "ManageAdminServiceProto";

message QueryAllTemplateRequest {
  /**
   * 当前页数
   */
  uint32 page_no = 1;
  /**
   * 每页大小
   */
  uint32 page_size = 2;
}

message AgainRegistrationByLocalBasicsDataRequest{
  string user_id_str = 1;
}

message WhiteListRegistrationRequest{
  /**
   * 请求类型：1-报名校验，2-报名
   */
  int32 type = 1;
  /**
   * 报名的活动id
   */
  int64 activity_id = 2;
  /**
   * 报名的用户id，多个逗号分隔
   */
  string user_id_list = 3;
  /**
   * 商家等级
   */
  string level = 4;
  /**
   * 行业
   */
  string industry = 5;
}

message AgainRegistrationByLocalBasicsDataResponse{
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 用户报名信息
   */
  repeated AgainRegistrationInfoDTO data = 3;
}

message WhiteListRegistrationResponse{
  /**
* 返回结果码
*/
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 用户报名信息
   */
  repeated WhiteListRegistrationDTO data = 3;
}

message  WhiteListRegistrationDTO{
  /**
 * 用户id
 */
  int64 user_id = 1;
  /**
   * 用户报名信息
   */
  string message = 2;
}

message AgainRegistrationInfoDTO{
  /**
   * 用户id
   */
  int64 user_id = 1;
  /**
   * 用户报名信息
   */
  string message = 2;
  /**
   * 报名的tag
   */
  string tag = 3;
}

message QueryAllTemplateResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 活动+任务模板
   */
  repeated ActivityTaskTemplateDTO data = 3;
  /**
   * 总数
   */
  uint64 total = 4;
}

message ActivityTaskTemplateDTO {
  /**
   * 活动模板
   */
  ActivityTemplateDTO activity_template = 1;
  /**
   * 任务模板
   */
  repeated TaskTemplateDTO task_template = 2;
  /**
   * 模板tag
   */
  string tag = 3;
}

message ActivityTemplateDTO {
  /**
  * 活动名称
  */
  string name = 1;
  /**
   * 活动别名
   */
  string alias = 2;
  /**
   * 活动系列
   */
  uint32 series_type = 3;
  /**
   * 活动描述
   */
  string description = 4;
  /**
   * 活动开始时间
   */
  string start_time = 5;
  /**
   * 活动结束时间
   */
  string end_time = 6;
  /**
   * 活动领取开始时间
   */
  string draw_start_time = 7;
  /**
   * 活动领取结束时间
   */
  string draw_end_time = 8;
  /**
   * 活动状态
   */
  uint32 status = 9;
  /**
   * 活动达成条件
   */
  string complete_condition = 10;
  /**
   * 人群类型
   */
  uint32 crowd_type = 11;
  /**
   * 人群配置
   */
  string crowd_config = 12;
  /**
   * 活动扩展数据
   */
  string ext = 13;
  /**
   * 活动通知配置
   */
  repeated NoticeTemplateDTO notice_configs = 14;
}

message TaskTemplateDTO {
  /**
   * 任务名称
   */
  string name = 1;
  /**
   * 任务别名
   */
  string alias = 2;
  /**
   * 任务描述
   */
  string description = 3;
  /**
   * 任务顺序
   */
  uint32 priority = 4;
  /**
   * 任务周期类型
   */
  uint32 period_type = 5;
  /**
   * 任务周期范围
   */
  uint32 period_day = 6;
  /**
   * 任务开始时间
   */
  string start_time = 7;
  /**
   * 任务结束时间
   */
  string end_time = 8;
  /**
   * 任务完成条件
   */
  string complete_condition = 9;
  /**
   * 任务状态
   */
  uint32 status = 10;
  /**
   * 子任务
   */
  repeated TaskTemplateDTO sub_tasks = 11;
  /**
   * 下一级任务
   */
  TaskTemplateDTO next_task = 12;
  /**
   * 任务指标配置
   */
  repeated IndicatorTemplateDTO indicator_configs = 13;
  /**
   * 任务奖励配置
   */
  repeated AwardTemplateDTO award_configs = 14;
  /**
   * 任务通知配置
   */
  repeated NoticeTemplateDTO notice_configs = 15;
  /**
   * 扩展数据
   */
  string ext = 16;
}

message NoticeTemplateDTO {
  /**
  * 实体类型
  */
  uint32 entity_type = 1;
  /**
   * 实体状态
   */
  uint32 entity_status = 2;
  /**
   * 推送渠道
   */
  uint32 channel = 3;
  /**
   * 推送时机
   */
  uint32 occasion = 4;
  /**
   * 模板ID
   */
  string template_config = 5;
  /**
   * 周期配置
   */
  string period_config = 6;
}

message IndicatorTemplateDTO {
  /**
  * 活动类型
  */
  uint32 type = 1;
  /**
   * 指标ID
   */
  uint64 indicator_id = 2;
  /**
   * 指标子ID
   */
  string indicator_sub_id = 3;
  /**
   * 指标初始值
   */
  string begin_value = 4;
  /**
   * 指标目标值
   */
  string target_value = 5;
  /**
   * 指标条件
   */
  string indicator_condition = 6;
  /**
   * 指标状态
   */
  uint32 status = 7;
  /**
   * 指标扩展参数
   */
  string ext = 8;
}

message AwardTemplateDTO {
  /**
  * 奖励名称
  */
  string award_name = 1;
  /**
   * 奖励类型
   */
  uint32 award_type = 2;
  /**
   * 奖励发送规则类型
   */
  string send_rule_type = 3;
  /**
   * 奖励发送规则
   */
  string send_rule = 4;
  /**
   * 奖励状态
   */
  uint32 status = 5;
  /**
   * 奖励目标
   */
  string award_subject = 6;
  /**
   * 奖励过期时间
   */
  uint64 expire_time = 7;
  /**
   * 奖励权益配置
   */
  string interest_ext_param = 8;
  /**
   * 任务通知配置
   */
  repeated NoticeTemplateDTO notice_configs = 9;
}

message CreateTaskByTemplateCodeRequest {
  /**
   * 模板tag
   */
  string code = 1;

  /**
   *模板变量
   */
  map<string, string> variables = 2;
}

message CreateTaskByTemplateCodeResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message CreateTaskOfActivityWithVariableRequest {
  /**
   * 模板tag
   */
  string code = 1;

  /**
   * 模板变量
   */
  map<string, string> variables = 2;

  /**
   * 活动ID
   */
  int64 activity_id = 3;
}

message CreateTaskOfActivityWithVariableResponse {
  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message BatchUpdateSameTypeTaskInfoRequest {
  /**
   * 任务名称
   */
  string name = 1;
  /**
   * 任务描述
   */
  string desc = 2;
  /**
   * alias标识
   */
  string alias_tag = 3;
  /**
   * 次序
   */
  uint32 tag_index = 4;
  /**
   * 活动ID
   */
  uint64 activity_id = 5;
}

message BatchUpdateSameTypeTaskInfoResponse {

  /**
  * 返回结果码
  */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 操作结果
   */
  uint32 operate_num = 3;
}

message UserDataStatisticRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
}

message UserDataStatisticResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message UserDataStatisticDetailRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 指标ID
   */
  uint64 indicator_id = 3;
  /**
   * 奖励类型
   */
  uint32 award_type = 4;
}

message UserDataStatisticDetailResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 用户ID
   */
  uint64 user_id = 3;
  /**
   * 活动ID
   */
  uint64 activity_id = 4;
  /**
   * 指标信息
   */
  repeated UserIndicatorStatisticDTO indicator_info = 5;
  /**
   * 任务信息
   */
  repeated UserTaskStatisticDTO task_info = 6;
  /**
   * 奖励信息
   */
  repeated kuaishou.kwaishop.merchant.growth.center.activity.award.AwardRecordInfoDTO award_info = 7;
  /**
   * 资格信息
   */
  repeated UserRegistrationStatisticDTO registration_info = 8;
  /**
   * 审批信息
   */
  repeated UserAuditStatisticDTO audit_info = 9;
}

message UserAuditStatisticDTO {
  /**
   * 唯一ID
   */
  string subject_id = 1;
  /**
   * 状态
   */
  uint32 status = 2;
  /**
   * 发送日志
   */
  string send_log = 3;
  /**
   * 结果日志
   */
  string receive_log = 4;
}

message UserRegistrationStatisticDTO {
  /**
   * 实体类型
   */
  uint32 entity_type = 1;
  /**
   * 实体ID
   */
  uint64 entity_id = 2;
  /**
   * 状态
   */
  uint32 status = 3;
  /**
   * 基准数据
   */
  string data = 4;
}

message UserIndicatorStatisticDTO {
  /**
   * 任务ID
   */
  uint64 task_id = 1;
  /**
   * 任务名称
   */
  string task_name = 2;
  /**
   * 指标ID
   */
  uint64 indicator_id = 3;
  /**
   * 指标名称
   */
  string indicator_name = 4;
  /**
   * 当前值
   */
  uint64 current_value = 5;
  /**
   * 目标值
   */
  uint64 target_value = 6;
  /**
   * 开始时间
   */
  uint64 start_time = 7;
  /**
   * 结束时间
   */
  uint64 end_time = 8;
  /**
   * 状态
   */
  uint32 status = 9;
}

message UserTaskStatisticDTO {
  /**
   * 任务ID
   */
  uint64 id = 1;
  /**
   * 任务名称
   */
  string name = 2;
  /**
   * 任务状态
   */
  uint32 status = 3;
  /**
   * 开始时间
   */
  uint64 start_time = 4;
  /**
   * 结束时间
   */
  uint64 end_time = 5;
  /**
   * 阶段
   */
  uint32 stage = 6;
  /**
   * 父任务ID
   */
  uint64 parent_id = 7;
}

message QueryUserTaskOperatorLogRequest {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
}

message QueryUserTaskOperatorLogResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 操作日志集
   */
  repeated string operator_log = 3;
}

message ActivityDataStatisticRequest {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
}

message ActivityDataStatisticResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 奖励信息
   */
  AwardStatisticInfo award_statistic = 3;
}

message AwardStatisticInfo {
  /**
   * 总发奖数额
   */
  uint64 total_value = 1;
  /**
   * top10高金额记录
   */
  repeated kuaishou.kwaishop.merchant.growth.center.activity.award.AwardRecordInfoDTO top_record = 2;
}

message CreateResourceActivityRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 活动名称
   */
  string activity_name = 2;
  /**
   * 活动模版id
   */
  string template_id = 3;
  /**
   * 活动开始时间
   */
  uint64 start_time = 4;
  /**
   * 活动结束时间
   */
  uint64 end_time = 5;
  /**
   * 操作人
   */
  string operator = 6;
}

message CreateResourceActivityResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
  * 活动ID
   */
  uint64 activity_id = 3;
}

message CreateResourceTaskRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 补贴规则id
   */
  uint64 resource_rule_id = 2;
  /**
   * 任务名称
   */
  string task_name = 3;
  /**
   * 任务模版id
   */
  string template_id = 4;
  /**
   * 任务开始时间
   */
  uint64 start_time = 5;
  /**
   * 任务结束时间
   */
  uint64 end_time = 6;
  /**
   * 操作人
   */
  string operator = 7;
  /**
   * 变量
   */
  CreateTaskVariableDTO variable = 8;
}

message CreateTaskVariableDTO {
  /**
   * 指标
   */
  repeated uint64 indicator_id = 1;
  /**
   * 奖励类型
   */
  uint32 award_type = 2;
  /**
   * 奖励过期时间
   */
  uint64 award_expire_time = 3;
  /**
   * 阶段数
   */
  uint32 stage_num = 4;
  /**
   * 资金发奖活动id
   */
  string fund_budget_id = 5;
}

message CreateResourceTaskResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
  /**
   * 任务ID
   */
  repeated uint64 task_id = 3;
}

message JoinResourceTaskRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 补贴规则id
   */
  uint64 resource_rule_id = 2;
  /**
   * 用户id
   */
  uint64 user_id = 3;
  /**
   * 开始时间
   */
  uint64 start_time = 4;
  /**
   * 结束时间
   */
  uint64 end_time = 5;
  /**
   * 变量
   */
  repeated DrawTaskVariableDTO variable = 6;
  /**
   * 操作人
   */
  string operator = 7;
  /**
   * 账户id
   */
  uint64 account_id = 8;
  /**
   * 奖励类型
   */
  uint32 award_type = 9;
  /**
   * 唯一bizCode
   */
  string biz_code = 10;
}

message JoinResourceTaskResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}


message DrawTaskVariableDTO {
  /**
  * 指标id
  */
  uint64 indicator_id = 1;
  /**
   * 指标目标值
   */
  uint64 indicator_target = 2;
  /**
   * 返点比例(基准数据)
   */
  uint64 rebate_ratio = 3;
  /**
   * 奖励上限(基准数据)
   */
  uint64 max_award = 4;
  /**
   * 标识
   */
  string tag = 5;
  /**
   * 返点门槛（基准数据）
   */
  uint64 basic_threshold = 6;
}

message SendUserResourceActivityAwardRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 用户id
   */
  repeated uint64 user_id = 2;
  /**
   * 权益配置id
   */
  uint64 interest_config_id = 3;
}

message SendUserResourceActivityAwardResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message InterveneUserTimeRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 补贴规则id
   */
  uint64 resource_rule_id = 2;
  /**
   * 用户id
   */
  uint64 user_id = 3;
  /**
   * 原始结束时间
   */
  uint64 origin_end_time = 4;
  /**
   * 干预结束时间
   */
  uint64 intervene_end_time = 5;
  /**
   * 原始开始时间
   */
  uint64 origin_begin_time = 6;
  /**
   * 干预开始时间
   */
  uint64 intervene_begin_time = 7;
}

message InterveneUserTimeResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message TerminateUserResourceRuleRequest {
  /**
   * 补贴活动ID
   */
  uint64 resource_activity_id = 1;
  /**
   * 补贴规则id
   */
  uint64 resource_rule_id = 2;
  /**
   * 用户id
   */
  uint64 user_id = 3;
}

message TerminateUserResourceRuleResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message JoinIndustryTaskRequest {
  /**
   * 用户id
   */
  repeated uint64 user_id = 1;
  /**
   * 开始时间
   */
  uint64 start_time = 2;
  /**
   * 操作人
   */
  string operator = 3;
  /**
   * 行业
   */
  string industry = 4;
}

message JoinIndustryTaskResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 返回信息
   */
  string error_msg = 2;
}

message QueryAllReportRecordRequest {
  /**
   * 当前页数
   */
  uint32 page_no = 1;
  /**
   * 每页大小
   */
  uint32 page_size = 2;
  /**
   * 行业
   */
  string industry = 3;
  /**
   * 提报人
   */
  string reporter = 4;
}

message QueryAllReportRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 数据
   */
  repeated ReportRecordDTO data = 3;
  /**
   * 总数
   */
  uint64 total = 4;
}

message ReportRecordDTO {
  /**
   * 主键ID
   */
  uint64 id = 1;
  /**
   * 被提报用户
   */
  uint64 user_id = 2;
  /**
   * 提报人
   */
  string operator = 3;
  /**
   * 提报参数
   */
  string report_param = 4;
  /**
   * 提报活动
   */
  string report_activity = 5;
  /**
   * 提报时间
   */
  uint64 report_time = 6;
  /**
   * 提报状态
   */
  uint32 status = 7;
  /**
   * 归属行业
   */
  string industry = 8;
}

message CancelUserJoinRecordRequest {
  /**
   * id
   */
  uint64 id = 1;
  /**
   * 被提报人
   */
  uint64 user_id = 2;
  /**
   * 操作人
   */
  string operator = 3;
  /**
   * 行业
   */
  string industry = 4;
}

message CancelUserJoinRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

message QueryReportOperatorIndustryRequest {
  /**
   * 操作人
   */
  string operator = 1;
}

message QueryReportOperatorIndustryResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 所属行业
   */
  string industry = 3;
  /**
   * 对应活动
   */
  string activity_name = 4;
}

message DeleteUserReportRecordRequest {
  /**
   * 记录ID
   */
  uint64 id = 1;
  /**
   * 用户ID
   */
  uint64 user_id = 2;
}

message DeleteUserReportRecordResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

service ManageAdminService {
  /**
   * 查询所有模板
   */
  rpc QueryAllTemplate (QueryAllTemplateRequest) returns (QueryAllTemplateResponse);
  /**
   * 按照模板tag创建任务
   */
  rpc CreateTaskByTemplateCode (CreateTaskByTemplateCodeRequest) returns (CreateTaskByTemplateCodeResponse);
  /**
   * 按照模板tag和变量，创建指定活动下的任务
   */
  rpc CreateTaskOfActivityWithVariable (CreateTaskOfActivityWithVariableRequest) returns (CreateTaskOfActivityWithVariableResponse);
  /**
   * 批量更新活动下同一类型的任务名称和标题
   */
  rpc BatchUpdateSameTypeTaskInfo (BatchUpdateSameTypeTaskInfoRequest) returns (BatchUpdateSameTypeTaskInfoResponse);
  /**
   * 人维度数据统计
   */
  rpc UserDataStatistic(UserDataStatisticRequest) returns (UserDataStatisticResponse);
  /**
   * 用户各维度数据详情
   */
  rpc UserDataStatisticDetail(UserDataStatisticDetailRequest) returns (UserDataStatisticDetailResponse);
  /**
   * 活动维度数据统计
   */
  rpc ActivityDataStatistic(ActivityDataStatisticRequest) returns (ActivityDataStatisticResponse);
  /**
   * 重新通过本地表基础数据报名（用于第一次报名被过滤，运营重新提报场景，需要过滤加白）
   */
  rpc AgainRegistrationByLocalBasicsData(AgainRegistrationByLocalBasicsDataRequest) returns (AgainRegistrationByLocalBasicsDataResponse);
  /**
   * 查询用户任务路径日志
   */
  rpc QueryUserTaskOperatorLog(QueryUserTaskOperatorLogRequest) returns (QueryUserTaskOperatorLogResponse);
  /**
   * 创建补贴活动
    */
  rpc CreateResourceActivity (CreateResourceActivityRequest) returns (CreateResourceActivityResponse);
  /**
   * 创建补贴任务
    */
  rpc CreateResourceTask (CreateResourceTaskRequest) returns (CreateResourceTaskResponse);
  /**
   * 领取补贴任务
    */
  rpc JoinResourceTask (JoinResourceTaskRequest) returns (JoinResourceTaskResponse);
  /**
   * 提报重点招商
   */
  rpc JoinIndustryTask(JoinIndustryTaskRequest) returns (JoinIndustryTaskResponse);
  /**
   * 查询重点招商提报记录
   */
  rpc QueryAllReportRecord(QueryAllReportRecordRequest) returns (QueryAllReportRecordResponse);
  /**
   * 解除提报
   */
  rpc CancelUserJoinRecord(CancelUserJoinRecordRequest) returns (CancelUserJoinRecordResponse);
  /**
   * 删除提报
   */
  rpc DeleteUserReportRecord(DeleteUserReportRecordRequest) returns (DeleteUserReportRecordResponse);
  /**
   * 查询操作人有权限提报的行业
   */
  rpc QueryReportOperatorIndustry(QueryReportOperatorIndustryRequest) returns (QueryReportOperatorIndustryResponse);
  /**
   * 补贴活动——干预用户活动时间接口
   */
  rpc InterveneUserTime (InterveneUserTimeRequest) returns (InterveneUserTimeResponse);
  /**
   * 补贴活动——干预活动进程接口（终止）
   */
  rpc TerminateUserResourceRule (TerminateUserResourceRuleRequest) returns (TerminateUserResourceRuleResponse);
  /**
   * 发放奖励
   */
  rpc SendUserResourceActivityAward (SendUserResourceActivityAwardRequest) returns (SendUserResourceActivityAwardResponse);

  /**
   * 运营加白报名活动场景（用于大促活动加白报名场景）
   */
  rpc WhiteListRegistration(WhiteListRegistrationRequest) returns (WhiteListRegistrationResponse);

}