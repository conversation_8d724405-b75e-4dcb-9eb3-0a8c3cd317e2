syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.common.protobuf";
option java_outer_classname = "ActivityCommonDtoProto";

/**
用户指标事件消息
 */
message UserIndicatorEventMsg {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  活动ID
   */
  uint64 activity_id = 2;
  /**
  任务ID
   */
  uint64 task_id = 3;
  /**
  指标ID
   */
  uint64 indicator_id = 4;
  /**
   * 更新时间
   */
  uint64 event_time = 5;
  /**
   * 事件类型
   */
  int32 event_type = 6;
  /**
   * 版本
   */
  uint64 version = 7;
  /**
   * 指标配置ID
   */
  uint64 indicator_config_id = 8;
  /**
   * 指标开始时间
   */
  uint64 start_time = 9;
  /**
   * 指标结束时间
   */
  uint64 end_time = 10;
  /**
   * 指标目标值
   */
  uint64 target_value = 11;
  /**
   * 初始值
   */
  uint64 begin_value = 12;
  /**
   * 当前值
   */
  uint64 current_value = 13;
}

/**
用户奖励事件消息
 */
message UserAwardEventMsg {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  活动ID
   */
  uint64 activity_id = 2;
  /**
  任务ID
   */
  uint64 task_id = 3;
  /**
  配置ID
   */
  uint64 config_id = 4;
  /**
   * 更新时间
   */
  uint64 event_time = 5;
  /**
   * 事件类型
   */
  uint32 event_type = 6;
  /**
   * 奖励数值
   */
  uint64 award_value = 7;
  /**
   * 唯一id
   */
  string unique_id = 8;
  /**
   * 额外参数
   */
  string extra = 9;

  /**
   * 奖励类型
   */
  uint32 award_type = 10;
}

/**
用户任务事件消息
 */
message UserTaskEventMsg {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  活动ID
   */
  uint64 activity_id = 2;
  /**
  实体ID
   */
  uint64 task_id = 3;
  /**
   * 更新时间
   */
  uint64 event_time = 4;
  /**
   * 事件类型
   */
  int32 event_type = 5;
  /**
   * 事件描述
   */
  string event_desc = 6;
  /**
   * 任务状态
   */
  uint32 status = 7;
  /**
   * 最后修改人（可用于表示数据来源）
   */
  string modifier = 8;
  /**
   * 数据版本
   */
  int64 version = 9;
  /**
 * 任务唯一标识
 */
  string alias = 10;
  /**
   * 任务开始时间
   */
  uint64 start_time = 11;
  /**
   * 任务结束时间
   */
  uint64 end_time = 12;
  /**
   * 任务阶段
   */
  int32 stage = 13;
}

/**
用户活动事件消息
 */
message UserActivityEventMsg {
  /**
  用户ID
   */
  uint64 user_id = 1;

  /**
  活动ID
   */
  uint64 activity_id = 2;

  /**
   * 更新时间
   */
  uint64 event_time = 3;

  /**
   * 事件类型
   */
  int32 event_type = 4;
  /**
   * 事件描述
   */
  string event_desc = 5;
  /**
   * 任务状态
   */
  uint32 status = 6;
  /**
   * 最后修改人（可用于表示数据来源）
   */
  string modifier = 7;
  /**
   * 数据版本
   */
  int64 version = 8;
  /**
   * 活动标签
   */
  string show_tag = 9;
  /**
   * 拓展参数
   */
  string ext = 10;
}

/**
 * 用户审批事件消息
 */
message UserAuditEventMsg {
  /**
  用户ID
  */
  uint64 user_id = 1;

  /**
  活动ID
   */
  uint64 activity_id = 2;

  /**
   审核类型
   */
  uint32 audit_type = 3;

  /**
  实体类型
   */
  uint32 entity_type = 4;

  /**
  实体ID
   */
  uint64 entity_id = 5;

  /**
  事件类型
   */
  uint32 event_type = 6;

  /**
  事件时间
   */
  uint64 event_time = 7;

  /**
   * 审批单ID
   */
  string subject_id = 8;
}

/**
 * 策略域事件消息
 */
message StrategyEventMsg {
  // 用户id
  uint64 user_id = 1;
  // 活动id
  uint64 activity_id = 2;
  // 实体id
  uint64 strategy_id = 3;
  // 事件状态
  uint32 event_status = 4;
  // 事件时间
  uint64 event_time = 5;
}
/**
 *延迟指标更新消息
 */
message IndicatorDelayUpdateMsg {
  // 事件id
  string event_id = 1;
  // 用户ID
  uint64 user_id = 2;
  // 指标ID
  uint64 indicator_id = 3;
  // 活动ID
  uint64 activity_id = 4;
  // 任务ID
  uint64 task_id = 5;
  // 指标值
  uint64 indicator_value = 6;
}

/**
 * 通用指标更新消息
 */
message IndicatorCommonUpdateMsg {
  // 事件id
  string event_id = 1;
  // 用户ID
  uint64 user_id = 2;
  // 指标ID
  uint64 indicator_id = 3;
  // 活动ID
  uint64 activity_id = 4;
  // 任务ID
  uint64 task_id = 5;
  // 指标值
  uint64 indicator_value = 6;
  // 消息源
  string source = 7;
}

message ActivityFlowChangeSendNotifyMsg {
  // 活动ID
  uint64 activity_id = 1;

  string notify_user = 2;
  // 通知类型
  uint32 notify_type = 3;
  // 时间
  uint64 event_time = 4;
  // 业务上下文key
  string biz_key = 5;
}

/**
 * 活动事件
 */
message ActivityEventMsg {
  // 活动ID
  uint64 activity_id = 1;
  // 活动名称
  string activity_name = 2;
  // 活动开始时间
  uint64 start_time = 3;
  // 活动结束时间
  uint64 end_time = 4;
  // 活动领取开始时间
  uint64 draw_start_time = 5;
  // 获取领取结束时间
  uint64 draw_end_time = 6;
  // 活动标识
  string activity_alias = 7;
  // 活动状态
  uint32 status = 8;
  // 事件类型
  uint32 event_type = 9;
}

message ActivityStatusChangeEventMsg {
  // 活动ID
  uint64 activity_id = 1;
  // 事件类型
  uint32 event_type = 2;
}

message ActivityAppendEstimationFinishMsg {

    uint64 strategy_id = 1;

    string strategy_version = 2;

    uint64 start_time = 3;

}

/**
 * 风控奖励审核消息
 */
message AwardRiskAuditMsg {
  // 用户ID
  uint64 user_id = 1;
  // 审批单标识
  string subject_id = 2;
  // 活动ID
  uint64 activity_id = 3;
  // 任务ID
  uint64 task_id = 4;
  // 风控保护码
  string risk_code = 5;
  // 待发放奖励金额
  uint64 award_value = 6;
  // 奖励计算周期开始时间
  uint64 award_start_time = 7;
  // 奖励计算周期结束时间
  uint64 award_end_time = 8;
  // 发奖计算基数
  string basic_data = 9;
  // 指标完成基本信息
  string complete_info = 10;
  // 发送时间
  uint64 timestamp = 11;
  // 额外参数
  string extra_param = 12;
  // 资源活动ID
  uint64 resource_activity_id = 13;
  // 资源规则ID
  uint64 resource_rule_id = 14;
  // 前返奖励
  uint64 pre_award_value = 15;
  // 发放节点类型
  string send_time_type = 16;
}

message ManualDrawSignUpActivityEventMsg {
  /**
   * 类型，Excel，人群包
   */
  int32 manual_type = 1;
  /**
   * 人群包ID或者Excel地址
   */
  string manual_entity = 2;
  /**
   * 活动id
   */
  int64 activity_id = 3;
  /**
   *  任务ID
   */
  repeated int64 task_id = 4;
  /**
   * 是否报名
   */
  bool sign_up = 5;
  /**
   * 操作人
   */
  string operator = 6;
  /**
   * 创建时间
   */
  int64 create_time = 7;
}

message OnlineSingleUserEventMsg {
  /**
   * 用户ID
   */
  repeated uint64 user_id = 1;
  /**
   * 上线活动ID
   */
  uint64 activity_id = 2;
  /**
   * 领取类型
   */
  uint32 sign_type = 3;
}

message BatchExecuteUserEventMsg {
  /**
   * 用户ID
   */
  repeated uint64 user_id = 1;
  /**
   * 事件ID
   */
  string event_id = 2;
  /**
   * 批量处理类型
   */
  uint32 execute_type = 3;
  /**
   * 批量处理配置
   */
  string execute_config = 4;
  /**
   * 是否有结束动作
   */
  bool finish_action = 5;

  /**
   * 有失败是否有需要重新消费
   */
  bool retry = 6;
}

message AddActivityCrowdEventMsg {
  string data = 1;

  string operator = 2;

  uint64 report_id = 3;

}

message SingleAddActivityCrowdEventMsg {
  uint64 seller_id = 1;

  uint64 activity_id = 2;

  repeated uint64 task_ids = 3;

  uint64 report_id = 4;

  uint64 start_time = 5;

  // 追加来源
  string source = 6;

}

message OnlineCheckFinishEventMsg {
  /**
   * 上线活动ID
   */
  uint64 activity_id = 1;
  /**
   * 轮次
   */
  uint32 cycle_num = 2;
  /**
   * 开始时间
   */
  uint64 start_time = 3;
}

message NotificationCreateEventMsg {
  /**
   * 活动id
   */
  uint64 activity_id = 1;
  /**
   * 创建时间
   */
  uint64 create_time = 2;

  /**
   * 触达渠道
   */
  repeated uint64 notification_change_id = 3;
}

message UserLotteryRegisterMsg {
  /**
  业务空间
   */
  string biz_code = 1;
  /**
  用户ID
   */
  uint64 user_id = 2;
  /**
  活动ID
   */
  uint64 entity_id = 3;
  /**
  任务ID
   */
  uint64 sub_entity_id = 4;
}

message UserLotteryAwardEventMsg {
  /**
  业务空间
   */
  string biz_code = 1;
  /**
  用户ID
   */
  uint64 user_id = 2;
  /**
  抽奖项ID
   */
  uint64 lottery_item_id = 3;
  /**
  抽奖事件ID
   */
  uint64 lottery_event_id = 4;
  /**
  奖励数量
   */
  uint64 award_count = 5;
  /**
  事件类型
   */
  uint32 event_type = 6;
  /**
  当前最新的状态
   */
  uint32 status = 7;
  /**
  扩展数据
   */
  string extra = 8;
}

message UserStatisticsInitMsg {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 初始化信息
   */
  string init_msg = 3;
}

message UserStatisticsChangeSyncMsg {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 变化时间
   */
  uint64 change_time = 3;
  /**
   * 发送时间
   */
  uint64 send_time = 4;
}

/**
用户资格事件消息
 */
message UserRegistrationEventMsg {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 实体ID
   */
  uint64 entity_id = 3;
  /**
   * 实体类型
   */
  uint64 entity_type = 4;
  /**
   * 事件时间
   */
  uint64 event_time = 5;
  /**
   * 事件类型
   */
  int32 event_type = 6;
  /**
   旧状态
   */
  int32 old_status = 7;
  /**
   新状态
   */
  int32 new_status = 8;
  /**
   更新类型
   */
  string change_type = 9;
}


message InvestmentOnlineFlushEventMsg {
  uint64 investment_activity_id = 1;

  uint64 activity_id = 2;
}

message BudgetLimitActivityEventMsg {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;

  /**
   * 发奖单ID
   */
  uint64 resource_rule_id = 2;
}

message SingleUserReportAbMsg {
  uint64 user_id = 1;
  uint64 activity_id = 2;
  repeated uint64 crowd_id = 3;
  // 上报ab的人群包id v2
  repeated string crowd_id_v2 = 4;
  // 人群包来源
  string crowd_source = 5;
}


message LaunchConfigChangeMsg {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 变更时间
   */
  uint64 change_time = 2;
  /**
   * 变更内容
   */
  string change_content = 3;
}

message HandlerExecuteMsg {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 发起时间
   */
  uint64 execute_time = 2;
  /**
   * 处理器名称
   */
  string handler_config = 3;
}

message LaunchConfigAuditMsg {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 渠道
   */
  string channel = 2;
  /**
   * 业务键
   */
  string biz_key = 3;
  /**
   * 操作人
   */
  string operator = 4;
}

message LaunchUserExpBucketSubmitMsg {
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 投放渠道
   */
  string channel = 3;
  /**
   * 投放场景
   */
  string scene = 4;
  /**
   * 投放用户分桶数据
   */
  repeated LaunchUserExpBucketData data = 5;
}

message LaunchUserExpBucketData {
  /**
   * 用户ID
   */
  uint64 user_id = 1;
  /**
   * 活动ID
   */
  uint64 activity_id = 2;
  /**
   * 投放渠道
   */
  string channel = 3;
  /**
   * 投放场景
   */
  string scene = 4;
  /**
   * 实体类型
   */
  uint32 entity_type = 5;
  /**
   * 实体ID
   */
  uint64 entity_id = 6;
  /**
   * 实验ID
   */
  string experiment_id = 7;
  /**
   * 分桶ID
   */
  string bucket_id = 8;
  /**
   * 时间戳
   */
  uint64 timestamp = 9;
}

message LaunchContentGenAsyncMsg {
  /**
   * 活动ID
   */
  uint64 activity_id = 1;
  /**
   * 投放渠道
   */
  string channel = 2;
  /**
   * 投放场景
   */
  string scene = 3;
  /**
   * 投放实体ID列表
   */
  repeated uint64 entity_id = 4;
  /**
   * 生成类型
   */
  uint32 generate_type = 5;
  /**
   * 操作人
   */
  string operator = 6;
  /**
   * 唯一键
   */
  string unique_key = 7;
}

message ExpBucketAssignReportMsg {
  /**
   * 实验Key
   */
  string experiment_key = 1;
  /**
   * 实验场景
   */
  string scene = 2;
  /**
   * 分桶ID
   */
  string bucket_id = 3;
  /**
   * 用户ID
   */
  uint64 user_id = 4;
  /**
   * 附加信息
   */
  string ext = 5;
  /**
   * 时间戳
   */
  uint64 assign_time = 6;
}

message LLMContentGenAsyncMsg {
  /**
   * 生成场景
   */
  string scene = 1;
  /**
   * 唯一键
   */
  string unique_key = 2;
  /**
   * 自定义信息
   */
  string customize_info = 3;
  /**
   * 生成类型
   */
  uint32 generate_type = 4;
  /**
   * 操作人
   */
  string operator = 5;
}

message ShopRedrawMsg {
  uint64 user_id = 1;
  string tag = 2;
}