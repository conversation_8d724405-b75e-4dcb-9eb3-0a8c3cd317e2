syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.dialog;

import "dialog/kwaishop_merchant_growth_dialog_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog";
option java_outer_classname = "KwaishopMerchantGrowthDialogServiceProto";

/**
 * 获取用户某个子场景下的弹窗列表请求
 */
message GetDialogInfoRequest {
  /**
   * 用户ID
   */
  int64 user_id = 1;
  /**
   * 业务子场景
   */
  int32 sub_scene = 2;
}

/**
 * 获取用户某个子场景下的弹窗列表返回
 */
message GetDialogInfoResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
  /**
   * 弹窗列表 json
   */
  string data = 3;
}

/**
 * 创建弹窗请求
 */
message CreateDialogRequest {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  事件类型
   */
  uint32 event_type = 2;
  /**
  事件ID
   */
  uint64 event_id = 3;
  /**
  任务模版ID
   */
  uint64 panel_id = 4;
  /**
  任务组ID
   */
  uint64 group_id = 5;
  /**
  任务ID
   */
  uint64 task_id = 6;
  /**
  阶段index
   */
  uint32 stage_index = 7;
  /**
  指标ID
   */
  uint64 indicator_id = 8;
  /**
  展示模版ID
   */
  string show_template_id = 9;
  /**
  场景ID，定义接入系统
 */
  uint32 scene_id = 10;
}

/**
 * 创建弹窗响应
 */
message CreateDialogResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
 * 创建弹窗请求
 */
message DeleteDialogRequest {
  /**
  用户ID
   */
  uint64 user_id = 1;
  /**
  弹窗业务唯一ID
   */
  string dialog_id = 2;
  /**
   场景ID，定义接入系统
  */
  uint32 scene_id = 3;
  /**
  请求来源
   */
  string source = 4;
}

/**
 * 创建弹窗响应
 */
message DeleteDialogResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
 * 创建弹窗请求
 */
message CreateDialogTemplateRequest {
  /**
  *接入弹窗的业务系统场景 1.任务系统 2.等级系统
  */
  uint32 scene_id = 1;
  /**
  * 弹窗模式 1.对话框 2.toast/tips 3.下拉弹窗
  */
  uint32 dialog_pattern = 2;
  /**
  业务弹窗类型
   */
  uint32 dialog_type = 3;
  /**
  弹窗状态1.有效 2.失效
   */
  uint32 status = 4;
  /**
  弹窗模版内容-json
   */
  string content = 5;
  /**
  创建人工号
   */
  string creator = 6;
  /**
  更新人工号
   */
  uint32 modifier = 7;
}

/**
 * 创建弹窗响应
 */
message CreateDialogTemplateResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
 * 删除弹窗请求
 */
message DeleteDialogTemplateRequest {
  /**
  弹窗模版ID
   */
  uint64 id = 1;

  /**
  更新人工号
 */
  uint32 modifier = 2;
}

/**
 * 删除弹窗响应
 */
message DeleteDialogTemplateResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
 * 更新弹窗请求，只支持更新内容
 */
message UpdateDialogTemplateRequest {
  /**
  弹窗模版ID
  */
  uint64 id = 1;
  /**
  弹窗模版内容-json
   */
  string content = 2;
  /**
   弹窗状态1.有效 2.失效
   */
  uint32 status = 3;
  /**
  更新人工号
   */
  string modifier = 4;
}

/**
 * 更新弹窗响应
 */
message UpdateDialogTemplateResponse {
  /**
   * 返回结果码
   */
  int32 result = 1;
  /**
   * 错误信息
   */
  string error_msg = 2;
}

/**
弹窗服务
 */
service KwaishopMerchantGrowthDialogService {
  /**
  查询弹窗信息
   */
  rpc GetDialogInfo (GetDialogInfoRequest) returns (GetDialogInfoResponse);
  /**
  创建弹窗
   */
  rpc CreateDialog (CreateDialogRequest) returns (CreateDialogResponse);
  /**
  删除弹窗
  */
  rpc DeleteDialog (DeleteDialogRequest) returns (DeleteDialogResponse);
  /**
  创建弹窗模版
 */
  rpc CreateDialogTemplate (CreateDialogTemplateRequest) returns (CreateDialogTemplateResponse);
  /**
  更新弹窗模版
  */
  rpc UpdateDialogTemplate (UpdateDialogTemplateRequest) returns (UpdateDialogTemplateResponse);
}