syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.dialog;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog";
option java_outer_classname = "KwaishopMerchantGrowthDialogDTOProto";

/**
 * 弹窗信息
 */
message DialogDTO {
  /**
   * 弹窗业务唯一id
   */
  string id = 1;
  /**
   * 弹窗类型
   */
  uint32 type = 2;
  /**
   * 弹窗标题
   */
  TextUrlDTO title = 3;
  /**
   * 弹窗描述
   */
  TextUrlDTO description = 4;
  /**
  * 弹窗背景
  */
  BackGroundDTO back_ground = 5;
  /**
   * 弹窗数据列表
   */
  repeated DataTabDTO data_list = 6;
  /**
   * 按钮列表
   */
  repeated ButtonDTO button_list = 7;
  /**
   * 底部操作文案
   */
  TextUrlDTO bottom_desc = 8;
  /**
   * 埋点数据
   */
  map<string, string> track_params = 9;
  /**
   * 创建时间
   */
  int64 create_time = 10;
}

/**
 * 弹窗背景
 */
message BackGroundDTO {
  /**
  * 图片url
  */
  string pic_url = 1;
  /**
  * 跳转url
  */
  string jump_url = 2;
  /**
   * 埋点
   */
  map<string, string> track_params = 3;
}

/**
 * 带链接的文案DTO
 */
message TextUrlDTO {
  /**
   * 内容文案
   */
  string text = 1;
  /**
   * 文案中填充的数据
   */
  repeated FillingDataDTO data = 2;
  /**
 * 链接
 */
  string url = 3;
  /**
   * 埋点
   */
  map<string, string> track_params = 4;
}

/**
 * 弹窗中数据DTO
 */
message DataTabDTO {
  /**
   * 文案
   */
  TextDTO data = 1;
  /**
   * 相应描述
   */
  TextDTO desc = 2;
  /**
 * 链接
 */
  string url = 3;
  /**
   * 埋点
   */
  map<string, string> track_params = 4;
}

/**
 * 按钮DTO
 */
message ButtonDTO {
  /**
   * 按钮文案
   */
  string button_text = 1;
  /**
   * 是否加深显示
   */
  bool highlight = 2;
  /**
   * 是否可点击
   */
  bool can_click = 3;
  /**
 * 链接
 */
  string url = 4;
  /**
   * 埋点
   */
  map<string, string> track_params = 5;
}

/**
 * 跳转链接DTO
 */
message JumpUrlDTO {
  /**
   * 链接
   */
  string url = 1;
  /**
   * 埋点
   */
  map<string, string> track_params = 2;
}

/**
 * 填充数据DTO
 */
message FillingDataDTO {
  /**
   * 文案中占位符替换的内容
   */
  string content = 1;
  /**
   * 颜色
   */
  string color = 2;
}

/**
 * 不带链接的文案DTO
 */
message TextDTO {
  /**
   * 图片链接
   */
  string pic_url = 1;
  /**
   * 文案
   */
  string text = 2;
  /**
   * 填充数据
   */
  repeated FillingDataDTO data = 3;
}