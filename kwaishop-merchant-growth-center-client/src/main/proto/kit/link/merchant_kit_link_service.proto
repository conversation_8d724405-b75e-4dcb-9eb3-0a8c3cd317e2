syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.kit.link;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link";
option java_outer_classname = "MerchantKitLinkServiceProto";

/**
短链接平台
 */
message AddKitLinkRequest {
  /**
  业务类型
   */
  string biz = 1;
  /**
  业务来源
   */
  string source = 2;
  /**
  备注
  */
  string note = 3;
  /**
  原始链接
  */
  string long_url = 4;
  /*
  创建人
   */
  string creator = 5;
}

message AddKitLinkDTO{
  /*
  生成的短链接
   */
  string short_url = 1;
  /*
  id
   */
  uint64 id = 2;

}

message AddKitLinkResponse {
  /*
  result=1 为成功
  */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  /*
  短链接信息
   */
  AddKitLinkDTO data = 3;
}

message  UpdateKitLinkRequest{
  /*
  自增id
   */
  uint64 id = 1;
  /**
  业务类型
  */
  string biz = 2;
  /**
  业务来源
   */
  string source = 3;
  /**
  备注
  */
  string note = 4;
  /**
  原始链接
  */
  string long_url = 5;
  /*
  创建人
   */
  string creator = 6;

}

message UpdateKitLinkResponse{
  /*
  错误码,result=1 为成功
   */
  int32 result = 1;
  /*
  错误信息
   */
  string error_msg = 2;
  /*
  生成的短链接
   */
  string short_url = 3;
  /*
  更新后的id
   */
  uint64 record_id = 4;
}

message QueryKitLinkRequest{
  /*
  业务类型
   */
  string biz = 1;
  /*
  创建者
   */
  string creator = 2;
  /*
  业务来源
   */
  string source = 3;
  /*
  页码
   */
  uint32 page_num = 4;
  /*
  每页数据数量
   */
  uint32 page_size = 5;
}

message QueryKitLinkResponse{
  /*
  错误码,result=1 为成功
   */
  int32 result = 1;
  /*
  错误信息
   */
  string error_msg = 2;
  /*
  链接信息
   */
  QueryKitLinkDTO data = 3;
}
message  QueryKitLinkDTO{
  /*
  总数量
   */
  uint64 count = 1;
  /*
  短链接信息
   */
  repeated LinkUrlsDTO urls = 2;
}
message LinkUrlsDTO{
  /*
  业务类型
   */
  string biz = 1;
  /*
  业务来源
   */
  string source = 2;
  /*
  备注
   */
  string note = 3;
  /*
  原始链接
   */
  string long_url = 4;
  /*
  短链接
   */
  string short_url = 5;
  /*
  创建人
   */
  string creator = 6;
  /*
  创建时间
   */
  int64 create_time = 7;
  /*
  修改时间
   */
  int64 update_time = 8;
  /*
  id
   */
  uint64 id = 9;
}

message DeleteKitLinkRequest{
  /*
  自增id
   */
  uint64 id = 1;
  /*
  创建人
   */
  string creator = 2;
}
message DeleteKitLinkResponse{
  /*
  返回结果码，1为成功
   */
  int32 result = 1;
  /*
  返回信息
   */
  string error_msg = 2;
}

message GetBizListRequest{

}
message GetBizListDTO{
  /*
  kConf配置key
   */
  string key = 1;
  /*
  kConf配置value
   */
  string value = 2;
}
message GetBizListResponse{
  /*
  返回结果码，1为成功
   */
  int32 result = 1;
  /*
  返回信息
   */
  string error_msg = 2;
  /*
  kConf数组
   */
  repeated GetBizListDTO biz_list = 3;

}

service MerchantKitLinkAdminService {
  /**
  创建短链接
 */
  rpc AddKitLink (AddKitLinkRequest) returns (AddKitLinkResponse);

  /*
  查询短链接
   */
  rpc QueryKitLink (QueryKitLinkRequest) returns (QueryKitLinkResponse);

  /*
  根据id删除短链接
   */
  rpc DeleteKitLink (DeleteKitLinkRequest) returns (DeleteKitLinkResponse);
  /*
  根据id修改短链接
   */
  rpc UpdateKitLink (UpdateKitLinkRequest) returns (UpdateKitLinkResponse);
  /*
  获取到biz列表
   */
  rpc GetBizList (GetBizListRequest) returns (GetBizListResponse);
}
