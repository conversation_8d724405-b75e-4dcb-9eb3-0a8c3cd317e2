syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.plan;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.plan";
option java_outer_classname = "OperationPlanDTOProto";

message OperationPlanCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message OperationPlanPageCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
  // 所有条数
  uint64 total = 4;
}

// 计划配置
message OperationPlanDTO {
  // 计划id
  uint64 plan_id = 1;
  // 计划名称
  string plan_name = 2;
  // 计划开始时间
  uint64 plan_start_time = 3;
  // 计划结束时间
  uint64 plan_end_time = 4;
  // 所属组织code
  string industry_code = 5;
  // 正式/测试计划
  FrontPlanApplicationEnum plan_application = 6;
  // 所属分组标签
  string group_tag = 7;
  // 复盘配置
  ReviewConfigDTO review_config = 8;
  // 策略配置
  repeated OperationStrategyDTO operation_strategy = 9;
}

// 复盘配置
message ReviewConfigDTO {
  // 复盘周期配置
  ReviewTimeConfigDTO review_time_config = 1;
  // 复盘指标配置
  repeated ReviewIndicatorConfigDTO review_indicator_config = 2;
}

// 复盘时间配置
message ReviewTimeConfigDTO {
  // 时间范围类型
  ReviewTimeTypeEnum review_time_type = 1;
  // 持续时间
  int32 period_days = 2;
  // 开始时间
  int64 start_time = 3;
  // 结束时间
  int64 end_time = 4;
}

// 时间范围类型
enum ReviewTimeTypeEnum {
  UNKNOWN_REVIEW_TIME_TYPE = 0;
  // 固定时间范围
  FIXED_TIME = 1;
  // 相对动作有效执行
  RELATIVE_ACTION_EFFECTIVE = 2;
}

message ReviewIndicatorConfigDTO {
  // 复盘指标类型
  IndicatorTypeEnum indicator_type = 1;
  // 复盘指标id
  int64 indicator_id = 2;
  // 目标值类型
  string value_type = 3;
  // 目标值配置
  string value_config = 4;
  // 查询参数(比如可以填活动id，针对商品筛选包类的指标，可以填商品筛选包id)
  string query_param = 5;
}

// 指标分类
enum IndicatorTypeEnum {
  UNKNOWN_INDICATOR_TYPE = 0;
  // 激励活动类
  STRATEGY_ACTIVITY = 1;
  // 指标类
  INDICATOR = 2;
}

message OperationStrategyDTO {
  // 策略id
  uint64 strategy_id = 1;
  // 策略名称
  string strategy_name = 2;
  // 触达类型
  PushTypeEnum push_type = 3;
  // 人群配置
  CrowdConfigDTO crowd_config = 4;
  // 实验配置
  ExperimentConfigDTO experiment_config = 5;
  // 执行时间配置
  ExecuteTimeConfigDTO execute_time_config = 6;
  // 各分桶执行动作配置
  repeated BucketActionConfigDTO bucket_action_config = 7;
}

// 触达类型
enum PushTypeEnum {
  // 非法
  UNKNOWN_PUSH_TYPE = 0;
  // 消息触达
  OPERATOR_PUSH = 1;
  // 平台号触达
  PLATFORM_PUSH = 2;
  // rpa触达
  RPA_PUSH = 3;
}

// 人群配置
message CrowdConfigDTO {
  // 是否初始人群配置
  CrowdConfigDetailDTO init_crowd = 1;
  // 圈选人群配置
  CrowdConfigDetailDTO selection_crowd = 2;
}

message CrowdConfigDetailDTO {
  // 人群包类型或者标签型
  CrowdTypeEnum crowd_type = 1;
  // 具体配置
  string config = 2;
}

enum CrowdTypeEnum {
  UNKNOWN_CROWD_TYPE = 0;
  // 人群包型
  CROWD = 1;
  // 标签型
  TAG = 2;
}

message CrowdPackageDTO {
  string crowd_key = 1;
}

message CrowdTagDTO {
  string tag_code = 1;
  CrowdTagConditionDTO condition = 2;
}

message CrowdTagConditionDTO {
  uint64 activity_id = 1;
  string channel_type = 2;
}

// 实验配置
message ExperimentConfigDTO {
  // 是否使用实验
  bool use_experiment = 1;
  // 对照组比例
  int32 control_group_rate = 2;
  // 实验组比例
  int32 treatment_group_rate = 3;
  // 实验分组数
  int32 treatment_group_size = 4;
}

// 指定时间配置
message ExecuteTimeConfigDTO {
  // 时间类型
  ExecuteTimeTypeEnum execute_time_type = 1;
  // 间隔天数（在循环模式下使用）
  int32 interval_days = 2;
  // 小时（在循环模式下使用）
  int32 hour = 3;
  // 分钟（在循环模式下使用）
  int32 minutes = 4;
  // 指定时间（在指定时间模式下使用）
  repeated int64 determine_time = 5;
  // 结束时间（在循环模式下使用）
  int64 end_time = 6;
}

enum ExecuteTimeTypeEnum {
  UNKNOWN_EXECUTE_TIME_TYPE = 0;
  // 循环
  CYCLE = 1;
  // 指定
  DETERMINE = 2;
  // 立即
  IMMEDIATE = 3;
}

message BucketActionConfigDTO {
  // 分桶key
  string bucket_key = 1;
  // 分桶名称
  string bucket_name = 2;
  // 触达通道配置
  repeated string config = 3;
}

enum FrontPlanApplicationEnum {
  UNKNOWN = 0;
  // 测试
  TEST = 1;
  // 正式
  OFFICIAL = 2;
}

enum FrontPlanStatusEnum {
  // 未知状态
  UNKNOWN_STATUS = 0;
  // 草稿
  DRAFT = 10;
  // 编辑中
  EDIT = 20;
  // 待执行
  WAIT_START = 30;
  // 执行中
  EXECUTING = 40;
  // 暂停中
  PAUSE = 50;
  // 已结束
  END = 60;
}

message UserActionStatisticsChangeSyncMsg {

  uint64 id = 12;
  /**
   * 用户ID
   */
  uint64 user_id = 1;

  /**
   * 计划id
   */
  uint64 plan_id = 2;

  /**
   * 计划id
   */
  uint64 strategy_id = 3;
  /**
   * uniqueId
   */
  string unique_id = 4;

  string action_code = 5;

  repeated string sub_action_code_list = 13;

  string action_instance_id = 6;

  int32 status = 7;

  /**
   * 效果状态
   */
  int32 effect_status = 8;

  /**
   * 变化时间
   */
  uint64 change_time = 9;
  /**
   * 变化类型
   */
  int32 change_type = 10;

  string target_indicator_info = 11;

  int64 create_time = 14;
}

message StrategyExecuteTaskMsg {
  uint64 execute_task_id = 1;
  uint32 event_type = 2;
}

message UserStrategyExecuteTaskMsg {
  uint64 execute_task_id = 1;
  uint64 user_id = 2;
  uint64 strategy_id = 3;
}

message VxPrivatePushMsg {
  uint64 execute_task_id = 1;
  uint64 node_instance_id = 2;
  string crowd_key = 3;
  string source = 4;
  uint32 execute_times = 5;
}

message UserActionRecordUpdateMsg {
  string unique_id = 1;
  uint64 user_id = 2;
  string ext_param = 3;
}



