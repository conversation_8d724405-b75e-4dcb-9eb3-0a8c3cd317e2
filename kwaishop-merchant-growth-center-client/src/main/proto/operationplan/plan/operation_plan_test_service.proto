syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.plan;

import "operationplan/plan/operation_plan_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.plan";
option java_outer_classname = "OperationPlanTestServiceProto";

message StrategyNodeTemplateSaveRequest {
  uint64 node_template_id = 1;

  string node_template_code = 2;

  string node_template_name = 3;

  string out_code = 4;

  string node_type = 5;

  string ext = 7;

  string operator = 8;
}

message ExecuteStrategyTaskRequest {
  uint64 execute_task_id = 1;
}

service OperationPlanTestApiService {
  // 分页查询运营计划
  rpc SaveStrategyNodeTemplate(StrategyNodeTemplateSaveRequest) returns (OperationPlanCommonResponse);
  // 指定执行任务id手动执行
  rpc ExecuteStrategyTask(ExecuteStrategyTaskRequest) returns (OperationPlanCommonResponse);
}