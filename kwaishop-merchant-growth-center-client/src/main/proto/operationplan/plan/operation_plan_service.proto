syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.plan;

import "operationplan/plan/operation_plan_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.plan";
option java_outer_classname = "OperationPlanServiceProto";

message OperationPlanPageQueryRequest {
  // 页码
  uint32 page_no = 1;
  // 大小
  uint32 page_size = 2;
  // 分组标签
  string group_tag = 3;
  // 计划标题
  string plan_name = 4;
  // 计划id
  uint64 plan_id = 5;
  // 测试/正式计划
  FrontPlanApplicationEnum application = 6;
  // 创建人
  string creator = 7;
  // 计划状态
  FrontPlanStatusEnum plan_status = 8;
  // 所属组织
  repeated string industry_code = 9;
}

message OperationPlanQueryDetailRequest {
  // 计划id
  uint64 plan_id = 1;
}

message OperationPlanPublishRequest {
  // 计划id
  uint64 plan_id = 1;
  // 操作人
  string operator = 2;
}

message OperationPlanPauseRequest {
  // 计划id
  uint64 plan_id = 1;
  // 操作人
  string operator = 2;
}

message OperationPlanRestartRequest {
  // 计划id
  uint64 plan_id = 1;
  // 操作人
  string operator = 2;
}

message OperationPlanSaveRequest {
  // 操作类型
  OperateTypeEnum operate_type = 1;
  // 操作人
  string operator = 2;
  // 运营计划配置
  OperationPlanDTO operation_plan = 3;
}

message OperationPlanFrontConfigFetchRequest {

}

enum OperateTypeEnum {
  UNKNOWN_OPERATE_TYPE = 0;
  SAVE_DRAFT = 1;
  CREATE = 2;
}

message OperationPlanQueryBriefDetailRequest {
  // 计划id
  uint64 plan_id = 1;
}

service OperationPlanApiService {
  // 分页查询运营计划
  rpc PageQueryOperationPlanList(OperationPlanPageQueryRequest) returns (OperationPlanPageCommonResponse);
  // 查询运营计划详情
  rpc QueryOperationPlanDetail(OperationPlanQueryDetailRequest) returns (OperationPlanCommonResponse);
  // 保存运营计划
  rpc SaveOperationPlan(OperationPlanSaveRequest) returns (OperationPlanCommonResponse);
  // 发布运营计划
  rpc PublishOperationPlan(OperationPlanPublishRequest) returns (OperationPlanCommonResponse);
  // 暂停运营计划
  rpc PauseOperationPlan(OperationPlanPauseRequest) returns (OperationPlanCommonResponse);
  // 重启运营计划
  rpc RestartOperationPlan(OperationPlanRestartRequest) returns (OperationPlanCommonResponse);
  // 获取运营计划下发给前端的配置 指标等等
  rpc FetchOperationPlanFrontConfig(OperationPlanFrontConfigFetchRequest) returns (OperationPlanCommonResponse);
  // 查询运营计划简要详情
  rpc QueryOperationPlanBriefDetail(OperationPlanQueryBriefDetailRequest) returns (OperationPlanCommonResponse);
}
