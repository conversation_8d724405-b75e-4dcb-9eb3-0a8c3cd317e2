syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.statistics;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.statistics";
option java_outer_classname = "OperationPlanStatisticsTestServiceProto";


message RefreshStrategyPlanReq {
  string operator = 1;

  int64 plan_id = 2;

  int64 strategy_id = 3;
}

message RefreshStrategyPlanResponse {
  int32 result = 1;
  string error_msg = 2;
}

message DeletePlanStatisticsReq {
  int64 biz_id = 1;
  string operator = 2;
}

message DeletePlanStatisticsResponse {
  int32 result = 1;
  string error_msg = 2;
}

message UpdateUserActionReq {
  int64 user_id = 7;
  string unique_id = 1;
  string operator = 2;
  int32 effect_status = 3;
  //  int32 action_reach = 4;
  //  int32 target_finish = 5;
  int64 review_end_time = 6;
}

message UpdateUserActionResponse {
  int32 result = 1;
  string error_msg = 2;
}

message DeleteUserActionReq {
  // 谨慎传入，只是for测试，否则量级很大，会有超时/OOM风险
  int64 plan_id = 4;
  // 谨慎传入，只是for测试，否则量级很大，会有超时/OOM风险
  int64 strategy_id = 5;
  int64 user_id = 1;
  string unique_id = 2;
  string operator = 3;
}

message DeleteUserActionResponse {
  int32 result = 1;
  string error_msg = 2;
}


service OperationPlanStatisticsTestService {
  // 刷新某个策略的编排协议
  rpc RefreshStrategyPlan(RefreshStrategyPlanReq) returns (RefreshStrategyPlanResponse);

  // 删除某个计划下的复盘数据
  rpc DeletePlanStatistics(DeletePlanStatisticsReq) returns (DeletePlanStatisticsResponse);

  // 更新某个用户动作完成
  rpc UpdateUserAction(UpdateUserActionReq) returns (UpdateUserActionResponse);
  // 删除某个用户动作
  rpc DeleteUserAction(DeleteUserActionReq) returns (DeleteUserActionResponse);

}