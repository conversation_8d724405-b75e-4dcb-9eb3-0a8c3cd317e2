syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.statistics;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.statistics";
option java_outer_classname = "OperationPlanStatisticsDTOProto";


message PlanStrategyConditionDTO {

  int64 strategy_id = 1;

  string strategy_name = 2;

  // 当前节点路径
  string agg_path = 3;

  int32 path_type = 4;

  repeated PlanStrategyConditionBaseDTO exp_condition = 5;

}

message PlanStrategyConditionBaseDTO {
  // 名称
  string name = 1;
  // 业务值
  string value = 2;

  // 当前节点路径
  string agg_path = 3;

  int32 path_type = 4;

  repeated PlanStrategyConditionBaseDTO sub_condition = 5;
}

message PlanStrategyStatisticsDTO {
  // 漏斗聚合结果
  string summary_funnel_result = 1;
  // 统计明细
  PlanStrategyStatisticsDetailDTO statistics_detail_result = 2;
  // 展示渠道名称
  string display_channel_name = 3;
}

message PlanStrategyStatisticsDetailDTO {
  // 动态表头
  repeated DynamicColumnDTO dynamic_column = 1;

  // 明细集合
  repeated PlanStrategyStatisticsDetailBaseDTO detail = 2;

  // 展示渠道名称
  string display_channel_name = 3;

}

message PlanStrategyStatisticsDetailBaseDTO {
  // 明细map
  map<string, string> detail_map = 1;
  // 子明细集合
  repeated PlanStrategyStatisticsDetailBaseDTO sub_detail = 2;
}

message DynamicColumnDTO {
  string column_name = 1;
  string column_key = 2;
  string tips = 3;
}

message ReviewPlanStatisticsMsg {
  int64 plan_id = 1;
  repeated int64 strategy_id = 2;
  int64 handle_time = 3;
}

message UserActionRecordDTO {
  string user_name = 1;
  int64 user_id = 2;

  string user_pic_url = 12;

  string strategy_name = 3;

  string action_name = 4;

  string exp_name = 5;

  string action_status_desc = 6;

  int64 action_execute_time = 7;

  string action_template = 8;

  string send_success = 9;

  string reach_valid = 10;

  string finish_target = 11;

  int64 create_time  = 13;


}