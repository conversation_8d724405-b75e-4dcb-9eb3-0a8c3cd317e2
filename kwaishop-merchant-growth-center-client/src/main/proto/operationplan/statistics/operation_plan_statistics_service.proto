syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.operationplan.statistics;

import "operationplan/statistics/operation_plan_statistics_dto.proto";


option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.operationplan.statistics";
option java_outer_classname = "OperationPlanStatisticsServiceProto";

message QueryStatisticsConditionsReq {
  // 计划id
  int64 plan_id = 1;
  // 策略id
  int64 strategy_id = 2;

  string operator = 3;
}

message QueryStatisticsConditionsResponse {
  int32 result = 1;

  string error_msg = 2;

  repeated PlanStrategyConditionDTO conditions = 3;

  repeated ChannelConfigDTO channel_configs = 4;
}

message ChannelConfigDTO {
  string name = 1;
  string code = 2;
  string agg_path = 3;
  int32 path_type = 4;
}

message QueryStatisticsDataRequest {
  // 计划id
  int64 plan_id = 1;
  // 策略id
  int64 strategy_id = 2;

  // 聚合全路径
  string agg_path = 3;
  // 聚合类型
  int32 agg_type = 4;

  string operator = 5;
}

message ExportStatisticsDataRequest {
  // 计划id
  int64 plan_id = 1;
  // 策略id
  int64 strategy_id = 2;

  // 聚合全路径
  string agg_path = 3;
  // 聚合类型
  int32 agg_type = 4;

  string operator = 5;

  string scene = 6;
}

message QueryUserActionRecordLogReq {

  string operator = 1;
  // 计划id
  int64 plan_id = 2;
  // 策略id
  int64 strategy_id = 3;

  string exp_key = 4;

  string action_code = 5;

  int64 action_execute_start_time = 6;

  int64 action_execute_end_time = 7;

  string user_name = 10;

  int64 user_id = 11;

  int64 page_num = 8;

  int64 page_size = 9;

}

message ExportUserActionRecordLogReq {
  string operator = 1;
  // 计划id
  int64 plan_id = 2;
  // 策略id
  int64 strategy_id = 3;

  string exp_key = 4;

  string action_code = 5;

  int64 action_execute_start_time = 6;

  int64 action_execute_end_time = 7;

  string user_name = 10;

  int64 user_id = 11;
  /**
   * 导出场景
   */
  string scene = 12;
}

message QueryStatisticsDataResponse {

  int32 result = 1;

  string error_msg = 2;

  PlanStrategyStatisticsDTO data = 3;
}

message ExportStatisticsDataResponse {

  int32 result = 1;

  string error_msg = 2;

}

message QueryUserActionRecordLogResponse {
  int32 result = 1;

  string error_msg = 2;

  int64 total = 3;

  repeated UserActionRecordDTO data_list = 4;

}

message ReportUserActionValidReachReq {

  map<string, string> report_param = 1;

}

message ReportUserActionValidReachResponse {
  int32 result = 1;
  string error_msg = 2;
}

message ExportUserActionRecordLogResponse {
  int32 result = 1;
  string error_msg = 2;
}

service OperationPlanStatisticsApiService {
  // 查询指标统计条件
  rpc QueryStatisticsConditions(QueryStatisticsConditionsReq) returns (QueryStatisticsConditionsResponse);

  // 查询指标统计数据
  rpc QueryStatisticsData(QueryStatisticsDataRequest) returns (QueryStatisticsDataResponse);

  // 导出指标统计数据
  rpc ExportStatisticsData(ExportStatisticsDataRequest) returns (ExportStatisticsDataResponse);

  // 分页查询用户动作流水
  rpc PageQueryUserActionRecordLog(QueryUserActionRecordLogReq) returns (QueryUserActionRecordLogResponse);

  // 导出用户动作流水
  rpc ExportUserActionRecordLog(ExportUserActionRecordLogReq) returns (ExportUserActionRecordLogResponse);

  // 上报用户动作有效触达
  rpc ReportUserActionValidReach(ReportUserActionValidReachReq) returns (ReportUserActionValidReachResponse);
}