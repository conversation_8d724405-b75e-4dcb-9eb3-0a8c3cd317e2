syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.fansgroup;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.fansgroup";
option java_outer_classname = "KwaishopMerchantGrowthMetadataServiceProto";

// 插入或更新活动
message SaveActivityRequest {
  /**
  id
   */
  uint64 id = 1;
  /**
  活动名称
  */
  string title = 2;
  /**
  完成条件-json,比如商家范围
   */
  string finish_condition = 3;
  /**
  活动开始时间
   */
  uint64 start_time = 4;
  /**
  活动结束时间
   */
  uint64 end_time = 5;
  /**
  活动结束后页面展示时间
   */
  uint64 show_time = 6;
  /**
  活动状态
   */
  uint32 status = 7;
  /**
  创建人
   */
  string creator = 8;
  /**
  版本号
   */
  uint32 version = 9;
  /**
  扩展参数
   */
  string ext = 10;
}
message SaveActivityDTO {
  uint64 activity_id = 1;
}
message SaveActivityResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  SaveActivityDTO data = 3;
}

// 删除活动
message RemoveActivityRequest {
  uint64 activity_id = 1;
}
message RemoveActivityDTO {
  uint64 activity_id = 1;
}
message RemoveActivityResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  RemoveActivityDTO data = 3;
}


// 插入或更新规则
message SaveRuleRequest {
  /**
id
 */
  uint64 id = 1;
  /**
  活动ID
  */
  uint64 activity_id = 2;
  /**
  规则名称
   */
  string title = 3;
  /**
  权益类型
   */
  int32 award_type = 4;
  /**
  权益
   */
  uint64 award_value = 5;
  /**
  人群包ID
   */
  string crowd_id = 6;
  /**
  状态
   */
  uint32 status = 7;
  /**
  创建人
   */
  string creator = 8;
  /**
  版本号
   */
  uint32 version = 9;
  /**
  扩展参数
   */
  string ext = 10;
}

message SaveRuleDTO {
  uint64 rule_id = 1;
}

message SaveRuleResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  SaveRuleDTO data = 3;
}

// 删除规则
message RemoveRuleRequest {
  uint64 rule_id = 1;
}

message RemoveRuleDTO {
  uint64 rule_id = 1;
}

message RemoveRuleResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  RemoveRuleDTO data = 3;
}

/**
元数据服务
 */
service KwaishopMerchantGrowthMetadataService {
  // 活动相关
  rpc SaveActivity(SaveActivityRequest) returns (SaveActivityResponse);
  rpc RemoveActivity(RemoveActivityRequest) returns (RemoveActivityResponse);
  // 规则相关
  rpc SaveRule(SaveRuleRequest) returns (SaveRuleResponse);
  rpc RemoveRule(RemoveRuleRequest) returns (RemoveRuleResponse);
}