syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.fansgroup;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.fansgroup";
option java_outer_classname = "KwaishopMerchantGrowthFansGroupServiceProto";

message AwardDetailDTO {

  /**
  是否降级
   */
  bool is_downgrade = 1;

  /**
  活动ID
   */
  int64 activity_id = 2;

  /**
  模块/活动名称
   */
  string module_title = 3;

  /**
  累计获得奖励
   */
  int64 total_obtain_amount = 4;

  /**
  待结算奖励
   */
  int64 settlement_amount = 5;

  /**
  已领取金额
   */
  int64 has_received_amount = 6;

  /**
  未领取金额
   */
  int64 can_receive_amount = 7;

  /**
  按钮状态：1-去提现，2-提现中
   */
  int32 button_status = 8;
}

message AwardCompensateDTO {
  /**
  发放流水记录ID
   */
  uint64 id = 1;

  /**
  bizId
   */
  string biz_id = 2;
}

message DrawAwardRequest {
  /**
  商家ID
   */
  uint64 uid = 1;
  /**
  活动ID
  */
  uint64 activity_id = 2;
  /**
  领取奖励金额，仅校验用
   */
  uint64 award_value = 3;
}

message DrawAwardResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;
}

message GetAwardDetailRequest {

  /**
  用户ID
   */
  int64 user_id = 1;
}

message GetAwardDetailResponse {
  /**
   *返回结果码
   */
  int64 result = 1;

  /**
   *返回信息
   */
  string error_msg = 2;

  /**
  结果详情
   */
  AwardDetailDTO data = 3;
}

// 插入或更新指标记录
message SaveIndicatorRecordRequest {
  /**
  业务场景ID
   */
  uint64 activity_id = 1;
  /**
  业务唯一ID，比如粉丝团中是货主ID
   */
  string biz_id = 2;
  /**
  事件指标id,比如1.预计领取 2.可领取
   */
  uint64 indicator_id = 3;
  /**
  先前指标值
   */
  int64 start_value = 4;
  /**
  当前指标值
   */
  int64 end_value = 5;
  /**
  指标的同步时间
   */
  int64 sync_time = 6;
  /**
   版本号
    */
  uint64 version = 7;
  /**
  扩展参数
   */
  string ext = 8;
  /**
  创建者
   */
  string creator = 9;
}

message SaveIndicatorRecordDTO {
  uint64 indicator_record_id = 1;
}

message SaveIndicatorRecordResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  SaveIndicatorRecordDTO data = 3;
}

// 批量保存指标记录
message BatchSaveIndicatorRecordRequest {
  repeated SaveIndicatorRecordRequest save_indicator_requests = 1;
}
// 批量保存指标记录返回结果
message BatchSaveIndicatorRecordDTO {
  // 没有保存成功的请求
  repeated SaveIndicatorRecordRequest failed_save_indicator_requests = 1;
}
// 批量保存指标记录返回值
message BatchSaveIndicatorRecordResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  BatchSaveIndicatorRecordDTO data = 3;
}


// 删除指标记录
message RemoveIndicatorRecordRequest {
  uint64 indicator_record_id = 1;
}

message RemoveIndicatorRecordDTO {
  uint64 indicator_record_id = 1;
}

message RemoveIndicatorRecordResponse {
  /*
result=1 为成功
*/
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

  RemoveIndicatorRecordDTO data = 3;
}

message DrawAwardByHandRequest {
  /**
业务唯一ID，比如粉丝团中是货主ID
 */
  string biz_id = 1;
  /**
  奖励流水表id
   */
  uint64 id = 2;
}

message DrawAwardByHandResponse {
  /*
result=1 为成功
 */
  int32 result = 1;
  /*
  error_msg
   */
  string error_msg = 2;

}


/**
商家领取奖励
 */
service KwaishopMerchantGrowthFansGroupService {
  /**
  领取奖励
   */
  rpc DrawAward (DrawAwardRequest) returns (DrawAwardResponse);

  /**
  查询粉丝团专属品活动奖励详情
   */
  rpc GetAwardDetail (GetAwardDetailRequest) returns (GetAwardDetailResponse);

  /**
  保存指标记录
   */
  rpc SaveIndicatorRecord(SaveIndicatorRecordRequest) returns (SaveIndicatorRecordResponse);

  /**
  批量保存指标记录
   */
  rpc BatchSaveIndicatorRecord(BatchSaveIndicatorRecordRequest) returns (BatchSaveIndicatorRecordResponse);

  /**
  删除指标记录，异常场景中使用
   */
  rpc RemoveIndicatorRecord(RemoveIndicatorRecordRequest) returns (RemoveIndicatorRecordResponse);

  /**
  手动补偿
   */
  rpc DrawAwardByHand (DrawAwardByHandRequest) returns (DrawAwardByHandResponse);
}