syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.indicator;
import "diagnosis/indicator/diagnosis_indicator_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.indicator";
option java_outer_classname = "DiagnosisIndicatorApiServiceProto";

message PageQueryIndicatorInfoRequest {
  // 页码
  uint32 page_num = 1;
  // 页大小
  uint32 page_size = 2;
  // 指标id
  uint64 indicator_id = 3;
  // 指标名称
  string indicator_name = 4;
  // 指标创建人
  string creator = 5;
}

message SaveIndicatorBaseInfoRequest {
  // 指标id
  uint64 indicator_id = 1;
  // 指标基础和信息
  IndicatorBaseInfoDTO indicator_base_info = 2;
  // 操作人
  string operator = 7;
}

message SaveIndicatorDataFetchConfigRequest {
  // 指标id
  uint64 indicator_id = 1;
  // 取数配置
  IndicatorDataFetchConfigDTO indicator_data_fetch_config = 2;
  // 操作人
  string operator = 3;
}

message PublishIndicatorDataFetchConfigRequest {
  // 指标id
  uint64 indicator_id = 1;
  // 操作人
  string operator = 2;
}

message OfflineIndicatorDataFetchConfigRequest {
  // 指标id
  uint64 indicator_id = 1;
  // 版本号
  uint32 version = 2;
  // 操作人
  string operator = 3;
}

message DeleteIndicatorRequest {
  // 指标id
  uint64 indicator_id = 1;
  // 操作人
  string operator = 2;
}

service DiagnosisIndicatorApiService {
  // 分页查询指标信息
  rpc PageQueryIndicatorInfo(PageQueryIndicatorInfoRequest) returns (IndicatorPageCommonResponse);
  // 保存指标
  rpc SaveIndicatorBaseInfo(SaveIndicatorBaseInfoRequest) returns (IndicatorCommonResponse);
  // 保存取数配置
  rpc SaveIndicatorDataFetchConfig(SaveIndicatorDataFetchConfigRequest) returns (IndicatorCommonResponse);
  // 发布取数配置
  rpc PublishIndicatorDataFetchConfig(PublishIndicatorDataFetchConfigRequest) returns (IndicatorCommonResponse);
  // 下线指定版本
  rpc OfflineIndicatorDataFetchConfig(OfflineIndicatorDataFetchConfigRequest) returns (IndicatorCommonResponse);
  // 删除指标
  rpc DeleteIndicator(DeleteIndicatorRequest) returns (IndicatorCommonResponse);
}