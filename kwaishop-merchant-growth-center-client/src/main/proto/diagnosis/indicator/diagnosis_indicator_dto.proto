syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.indicator;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.indicator";
option java_outer_classname = "DiagnosisIndicatorDTOProto";

message IndicatorCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message IndicatorPageCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
  // 所有条数
  uint64 total = 4;
}

message IndicatorBaseInfoDTO {
  // 指标名称
  string indicator_name = 1;
  // 实体类型
  string entity_type = 2;
  // 指标结果类型
  string indicator_result_type = 3;
  // 结果转换配置
  ResultConvertConfig result_convert_config = 4;
  // 查询条件配置
  ConditionConfigDTO condition_config = 5;
  // 取数配置
  string calc_type = 6;
}

message ConditionConfigDTO {
  // 日期范围类型
  string date_range_type = 1;
  // 日期范围列表
  repeated string date_range = 2;
  // 指标条件列表
  string condition = 3;
}

message ResultConvertConfig {
  // 指标查询返回的结果单位
  string output_unit = 1;
  // 最终展示在画布上的单位
  string show_unit = 2;
  // 枚举映射
  string enum_mapping = 3;
}

message IndicatorDataFetchConfigDTO {
  // 取数类型
  string data_fetch_type = 1;
  // DM2.0 serviceCode
  string service_code = 2;
  // DM2.0 字段
  string res_code = 3;
  // RPC 服务名称
  string service_name = 4;
  // RPC 接口名称
  string interface_name = 5;
  // RPC 方法名称
  string method_name = 6;
  // RPC kFlow规则code
  string rule_code = 7;
  // DM实时取数场景
  string scene = 8;
  // DM实时取数key
  string key = 9;
  // DM实时取数字段
  string field = 10;
}