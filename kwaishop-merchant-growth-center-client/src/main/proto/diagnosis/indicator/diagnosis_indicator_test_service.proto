syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.indicator;
import "diagnosis/indicator/diagnosis_indicator_dto.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.indicator";
option java_outer_classname = "DiagnosisIndicatorTestApiServiceProto";

message SaveIndicatorInfoRequest {
  // 指标基本信息
  IndicatorBaseInfoDTO indicator_base_info = 1;
  // 取数配置
  IndicatorDataFetchConfigDTO indicator_data_fetch_config = 2;
  // 操作人
  string operator = 3;
}

service DiagnosisIndicatorTestApiService {
  // 保存指标
  rpc SaveIndicatorInfo(SaveIndicatorInfoRequest) returns (IndicatorCommonResponse);
}