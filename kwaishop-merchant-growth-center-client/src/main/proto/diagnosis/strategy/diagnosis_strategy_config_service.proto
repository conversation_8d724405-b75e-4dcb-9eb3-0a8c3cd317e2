syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.strategy";
option java_outer_classname = "DiagnosisStrategyConfigApiServiceProto";

message DiagnosisCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message SaveDiagnosisStrategyRequest {
  // 基础信息
  DiagnosisBaseInfoDTO base_info = 1;
  // 规则配置
  RuleConfigDTO rule_config = 2;
  // 操作人
  string operator = 3;
}

message CrowdConfigDTO {
  // 人群包类型
  string crowd_type = 1;
  // 人群包key
  string crowd_key = 2;
}

message ScheduleConfigDTO {
  // 调度类型
  string schedule_type = 1;
  // 调度配置
  string config = 2;
}

message RuleConfigDTO {
  // 节点
  repeated NodeDTO node = 1;
  // 边
  repeated EdgeDTO edge = 2;
}

message NodeDTO {
  // 节点id
  string node_id = 1;
  // 组件code
  string module_code = 2;
  // 组件版本
  uint32 module_version = 3;
  // 组件配置
  string module_config = 4;
}

message DiagnoseAdviceConfigDTO {
  // 诊断建议
  map<string, string> advice_content = 1;
  // 建议展示配置
  AdviceShowConfigDTO advice_show_config = 2;
}

message AdviceShowConfigDTO {
  // 展示优先级
  uint32 priority = 1;
}

message IndicatorConfigDTO {
  // 指标id
  uint64 indicator_id = 1;
  // 日期范围
  string date_range = 2;
  // 相对天数
  int32 relative_days = 3;
  // 指标条件
  map<string, string> extra_condition = 4;
}

message EdgeDTO {
  // 边id
  string edge_id = 1;
  // 边名称
  string edge_name = 2;
  // 源节点id
  string source_node_id = 3;
  // 目标节点id
  string target_node_id = 4;
  // 边条件表达式(后续会扩展为json)
  string expression = 5;
}

message PublishDiagnosisStrategyRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 发布类型 prt/gray/online
  string publish_type = 2;
  // 灰度配置
  GrayConfigDTO gray_config = 3;
  // 操作人
  string operator = 4;
}

message GrayConfigDTO {
  uint32 rate = 1;
  repeated uint64 white_seller_id = 2;
}

message DeleteDiagnosisStrategyRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 策略code
  string strategy_code = 2;
  // 操作人
  string operator = 3;
}

message PageQueryDiagnosisStrategyRequest {
  // 页码
  uint32 page_num = 1;
  // 页大小
  uint32 page_size = 2;
  // 策略code
  string strategy_code = 3;
  // 策略名称
  string strategy_name = 4;
  // 策略创建人
  string creator = 5;
}

message PageQueryDiagnosisStrategyResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 组件模版列表
  repeated DiagnosisStrategyDTO diagnosis_strategy = 3;
  // 所有条数
  uint64 total = 4;
}

message DiagnosisStrategyDTO {
  // 策略id
  uint64 strategy_id = 1;
  // 策略code
  string strategy_code = 2;
  // 策略名称
  string strategy_name = 3;
  // 关联的KFlow code
  string outer_code = 4;
  // 场景code
  string scene_code = 5;
  // 诊断实体类型
  string entity_type = 6;
  // 触发类型
  string trigger_type = 7;
  // 应用场景
  string application = 8;
  // prt生效版本
  uint32 prt_version = 9;
  // 线上生效版本
  uint32 online_version = 10;
  // 创建人
  string creator = 11;
  // 更新人
  string modifier = 12;
  // 创建时间
  uint64 create_time = 13;
  // 更新时间
  uint64 update_time = 14;
  // 版本配置信息
  repeated DiagnosisRuleVersionDTO version_config = 15;
  // 缓存配置
  CacheConfigDTO cache_config = 16;
}

message DiagnosisRuleVersionDTO {
  // 关联的外部code
  uint64 change_order_id = 1;
  // kFlow组件版本
  string out_version = 2;
  // 版本号
  uint32 version = 3;
  // 状态
  uint32 status = 4;
  // 创建人
  string creator = 5;
  // 更新人
  string modifier = 6;
  // 创建时间
  uint64 create_time = 7;
  // 更新时间
  uint64 update_time = 8;
  // kFlow id
  uint64 k_flow_id = 9;
  // kFlow 主版本
  uint32 k_flow_major_version = 10;
  // kFlow 小版本
  uint32 k_flow_minor_version = 11;
  // 规则配置
  string rule_config = 12;
}

message DiagnosisBaseInfoDTO {
  // 策略id
  int64 strategy_id = 1;
  // 策略名称
  string strategy_name = 2;
  // 策略code
  string strategy_code = 3;
  // 场景code
  string scene_code = 4;
  // 诊断实体类型
  string entity_type = 5;
  // 人群配置
  CrowdConfigDTO crowd_config = 6;
  // 触发类型
  string trigger_type = 7;
  // 调度配置
  ScheduleConfigDTO schedule_config = 8;
  // 应用场景
  string application = 9;
  // 缓存配置
  CacheConfigDTO cache_config = 10;
}

message SaveDiagnosisStrategyBaseInfoRequest {
  // 基础信息
  DiagnosisBaseInfoDTO base_info = 1;
  // 操作人
  string operator = 2;
}

message SaveDiagnosisStrategyRuleInfoRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 规则配置
  RuleConfigDTO rule_config = 2;
  // 操作人
  string operator = 3;
}

message CacheConfigDTO {
  bool enable_cache = 1;
  uint64 expire_time = 2;
}

message FastUpgradeRuleModuleVersionRequest {
  // 策略id
  uint64 strategy_id = 1;
  // 操作人
  string operator = 2;
}

service DiagnosisStrategyConfigApiService {
  // 保存诊断策略
  rpc SaveDiagnosisStrategy(SaveDiagnosisStrategyRequest) returns (DiagnosisCommonResponse);
  // 发布诊断策略
  rpc PublishDiagnosisStrategy(PublishDiagnosisStrategyRequest) returns (DiagnosisCommonResponse);
  // 删除诊断策略
  rpc DeleteDiagnosisStrategy(DeleteDiagnosisStrategyRequest) returns (DiagnosisCommonResponse);
  // 分页查询组件模版信息
  rpc PageQueryDiagnosisStrategy(PageQueryDiagnosisStrategyRequest) returns (PageQueryDiagnosisStrategyResponse);
  // 保存诊断策略基础信息
  rpc SaveDiagnosisStrategyBaseInfo(SaveDiagnosisStrategyBaseInfoRequest) returns (DiagnosisCommonResponse);
  // 保存诊断策略规则信息
  rpc SaveDiagnosisStrategyRuleInfo(SaveDiagnosisStrategyRuleInfoRequest) returns (DiagnosisCommonResponse);
  // 一键升级诊断策略规则模版版本
  rpc FastUpgradeDiagnosisStrategyRuleModuleVersion(FastUpgradeRuleModuleVersionRequest) returns (DiagnosisCommonResponse);
}
