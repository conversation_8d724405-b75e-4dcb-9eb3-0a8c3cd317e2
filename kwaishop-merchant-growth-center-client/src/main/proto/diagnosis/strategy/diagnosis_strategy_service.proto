syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.strategy;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.strategy";
option java_outer_classname = "DiagnosisStrategyApiServiceProto";

message DiagnoseRequest {
  // 商家id
  uint64 seller_id = 1;
  // 诊断实体类型
  string entity_type = 2;
  // 诊断实体id
  string entity_id = 3;
  // 诊断策略code
  string strategy_code = 4;
  // 扩展字段
  map<string, string> ext_info = 5;
}

message DiagnoseResponse {
  // 返回结果码DiagnoseResponse
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 诊断结果
  repeated DiagnoseResultDTO data = 3;
}

message DiagnoseResultDTO {
  // 诊断结果信息
  map<string, string> advice_content = 1;
  // 优先级
  uint32 priority = 2;
}

service DiagnosisStrategyApiService {
  // 基于策略code诊断
  rpc DiagnoseByStrategyCode(DiagnoseRequest) returns (DiagnoseResponse);
}