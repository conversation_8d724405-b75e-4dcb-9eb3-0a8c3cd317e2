syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.diagnosis.moduletemplate;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.diagnosis.moduletemplate";
option java_outer_classname = "DiagnosisModuleTemplateApiServiceProto";

message ModuleTemplateCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
}

message ModuleTemplatePageCommonResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 返回信息
  string data = 3;
  // 所有条数
  uint64 total = 4;
}

message PageQueryModuleTemplateRequest {
  // 页码
  uint32 page_num = 1;
  // 页大小
  uint32 page_size = 2;
  // 组件模版code
  string module_template_code = 3;
  // 组件模版名称
  string module_template_name = 4;
  // 组件模版创建人
  string creator = 5;
}

message SaveModuleTemplateBaseInfoRequest {
  // 组件模版id
  uint64 module_template_id = 1;
  // 组件模版code
  string module_template_code = 2;
  // 组件模版名称
  string module_template_name = 3;
  // 组件模版类型
  string module_template_type = 4;
  // 组件适用范围
  string scope_type = 5;
  // 场景code
  string scene_code = 6;
  // 操作
  string operator = 7;
}

message SaveModuleTemplateVersionConfigRequest {
  // 组件模版code
  uint64 module_template_id = 1;
  // 关联的外部code
  string out_code = 2;
  // kFlow组件版本
  string out_version = 3;
  // 组件表单schema
  string schema = 4;
  // 操作人
  string operator = 5;
}

message PublishModuleTemplateRequest {
  // 组件模版code
  uint64 module_template_id = 1;
  // 发布类型 prt/online
  string publish_type = 2;
  // 操作人
  string operator = 3;
}

message ModuleTemplateDTO {
  // 组件模版id
  uint64 module_template_id = 1;
  // 组件模版code
  string module_template_code = 2;
  // 组件模版名称
  string module_template_name = 3;
  // 组件模版类型
  string module_template_type = 4;
  // 组件适用范围
  string scope_type = 5;
  // 场景code
  string scene_code = 6;
  // prt生效版本
  uint32 prt_version = 7;
  // 线上生效版本
  uint32 online_version = 8;
  // 创建人
  string creator = 9;
  // 更新人
  string modifier = 10;
  // 创建时间
  uint64 create_time = 11;
  // 更新时间
  uint64 update_time = 12;
  // 版本配置信息
  repeated ModuleTemplateVersionConfigDTO version_config = 13;
}

message ModuleTemplateVersionConfigDTO {
  // 关联的外部code
  string out_code = 1;
  // kFlow组件版本
  string out_version = 2;
  // 组件表单schema
  string schema = 3;
  // 版本号
  uint32 version = 4;
  // 状态
  uint32 status = 5;
  // 创建人
  string creator = 6;
  // 更新人
  string modifier = 7;
  // 创建时间
  uint64 create_time = 8;
  // 更新时间
  uint64 update_time = 9;
}

message PageQueryModuleTemplateResponse {
  // 返回结果码
  int32 result = 1;
  // 错误信息
  string error_msg = 2;
  // 组件模版列表
  repeated ModuleTemplateDTO module_template = 3;
  // 所有条数
  uint64 total = 4;
}

message DeleteModuleTemplateRequest {
  // 组件模版id
  uint64 module_template_id = 1;
  // 操作人
  string operator = 2;
}

message DeleteModuleTemplateVersionConfigRequest {
  // 组件模版id
  uint64 module_template_id = 1;
  // 版本号
  uint32 version = 2;
  // 操作人
  string operator = 3;
}

message FastUpgradeModuleTemplateVersionRequest {
  // 组件模版id
  uint64 module_template_id = 1;
  // 操作人
  string operator = 2;
}

service DiagnosisModuleTemplateApiService {
  // 分页查询组件模版信息
  rpc PageQueryModuleTemplate(PageQueryModuleTemplateRequest) returns (PageQueryModuleTemplateResponse);
  // 保存组件模版基础信息
  rpc SaveModuleTemplate(SaveModuleTemplateBaseInfoRequest) returns (ModuleTemplateCommonResponse);
  // 保存组件模版版本配置信息
  rpc SaveModuleTemplateVersionConfig(SaveModuleTemplateVersionConfigRequest) returns (ModuleTemplateCommonResponse);
  // 发布组件模版版本配置信息
  rpc PublishModuleTemplate(PublishModuleTemplateRequest) returns (ModuleTemplateCommonResponse);
  // 删除组件模版
  rpc DeleteModuleTemplate(DeleteModuleTemplateRequest) returns (ModuleTemplateCommonResponse);
  // 删除组件模版指定版本
  rpc DeleteModuleTemplateVersionConfig(DeleteModuleTemplateVersionConfigRequest) returns (ModuleTemplateCommonResponse);
  // 一键升级组件模版版本
  rpc FastUpgradeModuleTemplateVersion(FastUpgradeModuleTemplateVersionRequest) returns (ModuleTemplateCommonResponse);
}
