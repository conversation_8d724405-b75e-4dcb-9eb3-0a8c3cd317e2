syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.talent;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent";
option java_outer_classname = "TalentAppointApiServiceProto";

/**
  达人的年度信息
 */
message TalentAnnualInfoRequest {
  // 用户ID
  uint64 user_id = 1;
}
message TalentAnnualInfoResponse {
  // 返回结果码
  int64 result = 1;
  // 返回信息
  string error_msg = 2;
  // 结果详情
  string data = 3;
}

/**
  加入达人公约
 */
message JoinTalentAppointRequest {
  // 用户ID
  uint64 user_id = 1;
}
message JoinTalentAppointResponse {
  // 返回结果码
  int64 result = 1;
  // 返回信息
  string error_msg = 2;
  // 结果详情
  string data = 3;
}

/**
  删除达人公约记录
 */
message RemoveTalentRecordRequest {
  // 用户ID
  repeated uint64 ids = 1;
}
message RemoveTalentRecordResponse {
  // 返回结果码
  int64 result = 1;
  // 返回信息
  string error_msg = 2;
  // 结果详情
  int32 data = 3;
}


service KwaishopMerchantGrowthTalentAppointApiService {
  // 查询达人的年度信息
  rpc QueryAnnualInfo (TalentAnnualInfoRequest) returns (TalentAnnualInfoResponse);
  // 加入达人公约
  rpc JoinTalentAppoint (JoinTalentAppointRequest) returns (JoinTalentAppointResponse);
  // 删除达人公约记录
  rpc RemoveTalentRecord (RemoveTalentRecordRequest) returns (RemoveTalentRecordResponse);
}