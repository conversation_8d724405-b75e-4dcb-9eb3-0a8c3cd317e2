syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center.test.tool;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf.test";
option java_outer_classname = "TestToolServiceProto";


/**
任务触发请求
 */
message TaskTriggerRequest {
  /**
  任务名称
   */
  string task_name = 1;
  /**
  任务运行参数
   */
  string task_args = 2;
  /**
  访问表示
   */
  string ak = 3;
}
message TaskTriggerDTO {
  /**
  运行时长
   */
  uint64 cost_ms = 1;
  /**
  执行的机器IP
   */
  string ip = 2;
}

message TaskTriggerResponse {
  /**
   *返回结果码
   */
  int32 result = 1;
  /**
   *返回信息
   */
  string error_msg = 2;
  /**
   * 是否完成任务
   */
  TaskTriggerDTO data = 3;
}


/**
 测试工具服务
 */
service TestToolService {

  /**
   * 触发任务运行
   */
  rpc TaskTrigger (TaskTriggerRequest) returns (TaskTriggerResponse);

}