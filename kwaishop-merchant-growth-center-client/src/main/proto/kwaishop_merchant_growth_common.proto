syntax = "proto3";

package kuaishou.kwaishop.merchant.growth.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.merchant.growth.center.protobuf";
option java_outer_classname = "KwaishopMerchantGrowthCenterCommonProto";

// common pojo example
message ExampleObject {
}

enum MerchantGrowthStatus {
  UNKNOWN_STATUS = 0;
  WAIT_PROCESS = 1;           //待处理
  TASK_CRETE_FAILED = 2;      //任务创建失败（废弃）
  TASK_CREATE_SUCCESS = 3;    //任务创建成功（废弃）
  PARTICIPATE_FAILED = 4;     //参加任务失败（废弃）
  PARTICIPATE_SUCCESS = 5;    //参加任务成功
  IGNORE = 6;  //不需要报名
}

message GrowthTaskSignRecordDTO {
  uint64 user_id = 1;
  uint64 gmv = 2;
  bool blocked = 3;
  uint32 state = 4;
  uint64 operate_time = 5;
  string extra = 6;
  uint64 dt = 7;
  bool new_shop = 8;
}

/**
 * 店铺装修变更消息
 */
message KwaishopShopDecorationChangeMessageV2 {
  uint64 seller_id = 1;
  uint64 decoration_status = 2;
  repeated string decoration_release_module = 3;
  repeated string decoration_release_module_type = 4;
}

/**
 * 创作中心短视频消息
 */
message DistributePhotoPageViewMessage {
  uint64 user_id = 1; // 用户id
  uint32 scene_id = 2;  // 场景id
  string scene_name = 3; // 场景名称
  uint64 photo_id = 4; //视频id
}

/**
 * 新商档案消息
 */
message SellerRecordDTO {
  /**
   * 商家ID
   */
  int64 seller_id = 1;
  /**
   * 问卷类型
   */
  string record_type = 2;
  /**
   * 内容
   */
  string full_content = 3;
  /**
   * 提交时间
   */
  int64 submit_time = 4;
}

/**
导出任务消息
 */
message MerchantGrowthFileExportMessage {
  /**
  文件ID
   */
  int64 file_id = 1;
  /**
  商家ID
   */
  int64 seller_id = 2 ;

  /**
  导出子类型
   */
  string sub_type = 3;
  /**
  最后更新时间
   */
  int64  update_time = 4;
  /**
  发送时间
   */
  int64 send_time = 5;
}


message CommonParam {
  string scene = 1; //使用场景类型
}

message AwardShowDTO {
  /**
   * 奖励类型
   */
  uint32 award_type = 1;

  /**
   * 奖励金额 若无具体金额（返点）未空
   */
  string value = 2;

  /**
   * 原始奖励金额
   * 例如在多阶梯提前放奖模式，这个阶梯奖励值是配置的奖励值,value是扣减后的奖励值
   */
  string original_value = 3;

  /**
   * 奖励名称 目前虚拟奖励使用此字段
   */
  string award_name = 4;

  /**
   * 磁力金牛权益包信息
   */
  repeated MagnetInterestPackageInfoDTO magnet_interest_package_info = 5;
}

message MagnetInterestPackageInfoDTO {

  /**
   * 磁力金牛权益包ID
   */
  uint64 package_id = 1;

  /**
   * 磁力金牛权益包价值
   */
  uint64 value = 2;

  /**
   * 磁力金牛权益包明细
   */
  repeated MagnetInterestBenefitInfoDTO benefit_info = 3;

}

message MagnetInterestBenefitInfoDTO {
  /**
   * 权益名称
   */
  string name = 1;

  /**
   * 权益价值
   */
  uint64 value = 2;

  /**
   * 权益数量
   */
  uint64 number = 3;
}

message AwardSelectionShowConfigDTO {

  /**
   * 活动可选奖励信息
   */
  repeated AwardShowDTO award_info = 1;
}

message CompleteProgressDTO {
  /**
   * 当前值
   */
  uint64 current_value = 1;

  /**
   * 目标值
   */
  uint64 target_value = 2;
}

message ButtonInfoDTO {
  /**
   * 文本
   */
  string text = 1;
  /**
   * 跳转链接
   */
  string jump_url = 2;
  /**
   * PC跳转链接
   */
  string pc_jump_url = 3;
}