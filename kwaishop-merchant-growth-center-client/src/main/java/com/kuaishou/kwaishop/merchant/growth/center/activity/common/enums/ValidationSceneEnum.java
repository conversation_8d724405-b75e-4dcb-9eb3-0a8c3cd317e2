package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * 活动后台参数校验场景
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-12
 */
public enum ValidationSceneEnum {

    UNKNOWN(0, "未知"),

    SERVICE_MARKET(1, "服务市场优惠券ID校验"),
    DISTRIBUTOR_TYPE(2, "货主类型校验"),
    HIVE_IMPORT(3, "Hive导入校验"),

    MAGNET_INTEREST_PACKAGE(4, "磁力金牛权益包校验"),
    ;

    private final int value;
    private final String type;

    public static ValidationSceneEnum of(int value) {
        for (ValidationSceneEnum val : ValidationSceneEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    ValidationSceneEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
