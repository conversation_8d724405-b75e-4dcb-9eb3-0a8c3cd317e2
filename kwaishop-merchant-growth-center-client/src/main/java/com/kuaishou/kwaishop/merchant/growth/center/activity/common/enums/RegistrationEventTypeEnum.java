package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-27
 */
public enum RegistrationEventTypeEnum {
    UNKNOWN(0, "未知"),
    VALID_EVENT(1, "资格生效事件"),
    INVALID_EVENT(2, "资格取消事件"),
    RISK_EVENT(3, "资格风控事件"),
    ;
    private final int value;
    private final String type;

    public static RegistrationEventTypeEnum of(int value) {
        for (RegistrationEventTypeEnum val : RegistrationEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    RegistrationEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
