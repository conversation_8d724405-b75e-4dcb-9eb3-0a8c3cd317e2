package com.kuaishou.kwaishop.merchant.growth.center.client.common.kconf;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * Long类型配置枚举
 */
public enum MerchantGrowthLongClientConfigKey implements KconfClientSupplier<Long> {

    /**
     * 粉丝团专属品活动ID
     */
    fansGroupActivityId(1L),
    ;

    private Long defaultValue;

    MerchantGrowthLongClientConfigKey() {

    }

    MerchantGrowthLongClientConfigKey(Long defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String configKey() {
        return name();
    }

    private static final long DEFAULT_BOOL_VALUE = 0;

    @Override
    public Long defaultValue() {
        return DEFAULT_BOOL_VALUE;
    }

    @Override
    public Kconf<Long> getKConf() {
        return Kconfs.ofLong(getConfigKey(), defaultValue).build();
    }
}
