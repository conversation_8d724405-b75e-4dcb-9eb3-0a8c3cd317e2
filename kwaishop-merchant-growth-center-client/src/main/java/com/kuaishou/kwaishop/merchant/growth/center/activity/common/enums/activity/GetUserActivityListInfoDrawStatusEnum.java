package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-21
 */
public enum GetUserActivityListInfoDrawStatusEnum {

    // 初始状态, 默认查全部
    NONE(0, "查全部"),
    // 已参加
    DRAW(1, "已参加"),
    // 未参加
    NOT_DRAW(2, "未参加"),
    ;
    private final long value;
    private final String type;

    public static GetUserActivityListInfoDrawStatusEnum of(long value) {
        for (GetUserActivityListInfoDrawStatusEnum val : GetUserActivityListInfoDrawStatusEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return NONE;
    }

    GetUserActivityListInfoDrawStatusEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public long getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
