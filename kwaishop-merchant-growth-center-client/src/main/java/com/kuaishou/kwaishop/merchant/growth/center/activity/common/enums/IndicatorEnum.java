package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorUnitTypeEnum.AMOUNT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorUnitTypeEnum.DURATION;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorUnitTypeEnum.NUMBER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorUnitTypeEnum.OTHER;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-10-13
 */
public enum IndicatorEnum {
    UNKNOWN(0, "unknown"),
    PULL_NEW_USER(1, "拉新"),
    PULL_USER_BACK(2, "拉回"),
    PULL_NEW_USER_AND_USER_BACK(3, "拉新回"),
    FANS_COUNT(4, "粉丝关注指标"),
    VALID_LIVE_FLOW(5, "有效直播（4小时）"),
    PROMOTE_COST(6, "推广花费", AMOUNT),
    DAILY_GMV(7, "商家每日GMV", AMOUNT),
    VALID_LIVE_YEAR_GOODS(8, "年货节有效直播"),
    MERCHANT_VIDEO(9, "带货短视频"),
    LIVE_PLAN(10, "直播计划"),
    LIVE_RESERVATION(11, "直播预告"),
    CHALLENGE_AUTHOR(12, "挑战吧主播"),
    VALID_LIVE(13, "有效直播"),
    TRAINING_COURSE(14, "完成训练班考试"),
    PAY_SHOP_DEPOSIT(15, "缴纳店铺保证金"),
    PUBLISH_GOODS(16, "上架商品"),
    DOWNLOAD_MERCHANT_APP(17, "下载【快小店商家APP】"),
    LIVE_COURSE_APPLY(18, "直播课预约"),
    OPEN_MAGNET_ACCOUNT(19, "开通磁力金牛账户"),
    COURSE_FINISH(20, "课程学习"),
    SELLER_REPLY(21, "回头客说"),
    PROMOTION_TOOL(22, "营销工具"),
    USE_SYT(23, "使用生意通"),
    COMMON_VIDEO(24, "发布短视频"),
    LIVE_DURATION(25, "直播时长", DURATION),
    AD_PULL_NEW(26, "商业化拉新"),
    AD_PULL_BACK(27, "商业化拉回"),
    AD_PULL_ACTIVE(28, "商业化拉活跃"),
    VIDEO_GMV(29, "短视频成交额", AMOUNT),
    LIVE_GMV(30, "直播成交额", AMOUNT),
    FOLLOW_LIVE_ASSISTANT(31, "跟播助手", NUMBER),
    NEW_CUSTOMER(32, "拉新人数", NUMBER),
    PREHEAT_VIDEO(33, "预热短视频"),
    DISTRIBUTE_PLAN(34, "分销计划"),
    AUTO_PROMOTER_CLOSED_ORDER(35, "自动分销已结算订单数", NUMBER),
    AUTO_PROMOTER_PROCESSING_ORDER(36, "自动分销待结算订单数", NUMBER),
    DE_WEEKLY_LIVE_DURATION(37, "DE指标-周维度-直播时长", DURATION),
    DE_WEEKLY_RISK_GMV(38, "DE指标-周维度-电商销售额", AMOUNT),
    DE_WEEKLY_DAILY_LIVE_NUM(39, "DE指标-周维度-有效开播天数"),
    DE_WEEKLY_FANS_ADD(40, "DE指标-周维度-净涨粉"),
    DE_WEEKLY_WATCH_DURATION(41, "DE指标-周维度-次均观流时长", DURATION),
    DE_WEEKLY_VIDEO_PUBLISH(42, "DE指标-周维度-发布电商短视频数", NUMBER),
    DE_WEEKLY_VIDEO_VIEWS(43, "DE指标-周维度-短视频累计播放量"),
    DM_LIVE_DURATION(44, "直播时长", DURATION),
    MERCHANT_LEVEL(45, "商家等级"),
    DECORATE_SHOP(58, "店铺装修"),
    OPEN_SHOP_PROFILE_ENTRANCE(59, "开启店铺页入口"),
    REALTIME_RISK_GMV(62, "（实时对投）风控后GMV"),
    REALTIME_PROMOTION_COST(63, "（实时对投）商业化投流"),
    REALTIME_SELF_GMV(64, "（实时对投）自播GMV"),
    INDUSTRY_PROMOTION_COST(65, "招商商业化消耗"),
    SNOWBALL_FANS_GMV(66, "雪球新粉GMV"),
    SNOWBALL_FANS_INCREMENT(67, "雪球新粉涨粉量"),
    SNOWBALL_COST(72, "雪球计划非关注页商业化消耗", AMOUNT),
    PREPARE_REGISTRATION(76, "报名任务指标"),
    PREPARE_SPECIAL(77, "专属奖励指标"),
    PREPARE_FANS(79, "涨粉数量"),
    PREPARE_LIVE_PRE(80, "直播预告预约人数"),
    PREPARE_PUBLISH_VIDEO(81, "发布新品短视频"),
    PREPARE_LIVE_DAYS(82, "直播打卡天数"),
    PREPARE_COST(84, "商业化投放指标", AMOUNT),
    PREPARE_NATIONAL_DAY_2HOUR(85, "国庆直播打卡2小时任务"),
    PREPARE_NATIONAL_DAY_3HOUR(86, "国庆直播打卡3小时任务"),
    OPEN_MAGNET_MOBILE_ACCOUNT(87, "开通磁力移动端自助户"),
    OUTBREAK_VIDEO_GMV(88, "新品挂车短视频成交", AMOUNT),
    OUTBREAK_PUBLISH_GOODS(90, "上架新品数"),
    OUTBREAK_LIVE_PRE(92, "爆发期直播预约人数"),
    OUTBREAK_CHALLENGE_GMV(93, "大促GMV挑战任务", AMOUNT),
    OUTBREAK_CHALLENGE_COST(94, "商业化增投任务", AMOUNT),
    RANK(134, "排行名次"),
    PLAN_COUNT(144, "计划直播数"),
    FAST_DISTRIBUTION(142, "入驻快分销"),
    SINGLE_LIVE_PCU(148, "单日直播在线人数"),
    TOP_LIVE_GMV(152, "2020.06直播GMV单日峰值"),
    TOP_LIVE_PCU(153, "2020.06直播PCU单日峰值"),
    SINGLE_LIVE_GMV(154, "单日直播GMV"),
    WE_CHAT_BIND(161, "绑定微信"),
    ALI_PAY_BIND(162, "绑定支付宝"),
    RISK_SELLER_GMV(172, "风控后支付GMV(卖家）", AMOUNT),
    RISK_DISTRIBUTION_GMV(173, "风控后被分销GMV", AMOUNT),
    RISK_SELF_GMV(174, "风控后自播GMV", AMOUNT),
    NEW_FANS_CNT(177, "新粉数量"),
    DISTRIBUTION_SETTLE_GMV(182, "被分销结算GMV", AMOUNT),
    RISK_ACTUAL_GMV(189, "风控后实际支付GMV", AMOUNT),
    COMMERCIAL_INVESTMENT(194, "商业化投流"),
    VIDEO_COUNT_TRAILER(197, "挂车短视频发布数"),
    PUSH_FLOW_DURATION_TRAILER(200, "累计推流时长(挂车)", DURATION),
    SETTLE_SERVICE_CHARGE(209, "结算服务费", AMOUNT),
    VALID_LIVE_DAYS_RISK(218, "有效开播天数（风控后）", NUMBER),
    SHOP_HUNG_ITEM_COUNT(219, "店铺常挂品数量"),
    SHOP_DECORATE(220, "店铺按照优秀规范装修"),
    SHOP_VIDEO_TRAILER(221, "挂店铺短视频数"),
    SHOP_RISK_GMV(222, "有效销售额(仅风控后店铺)"),
    SEARCH_RISK_GMV(223, "风控后搜索渠道GMV"),
    VIDEO_WITH_SPECIAL_TAG(224, "（616）短视频带指定tag"),
    PRODUCT_REACH_STANDARD(225, "商品质量达标准分"),


    SHOP_LIVE_DURATION(226, "电商直播时长"),
    SHOP_EXPERIENCE_SCORE(243, "店铺体验分"),

    SHOP_DECORATE_FOR_PROMOTER(249, " 装修店铺（达人）"),
    OFFLINE_ITEM(251, "下架商品数（实时）"),
    RISK_SHOP_PROMOTER_GMV(252, "风控后达人店铺渠道GMV"),

    SHOP_LIVE_STREAM_START(261, "开启电商直播"),

    WINDOW_HOSTING(277, "达人店铺托管"),
    RISK_LIVE_SELLER_GMV(280, "风控后直播渠道支付GMV（卖家）"),
    RISK_VIDEO_SELLER_GMV(282, "风控后短视频渠道支付GMV（卖家）"),

    SHOP_SETTLE_SERVICE_CHARGE(307, "店铺渠道分销服务费"),

    LIVE_SETTLE_SERVICE_CHARGE(308, "直播渠道分销服务费"),

    PHOTO_SETTLE_SERVICE_CHARGE(309, "短视频渠道分销服务费"),

    LIVE_RESERVATION_WITH_VIDEO(311, "发布关联短视频预告并准时开播", NUMBER),
    SHOP_ITEM_MODULE_DECORATE(318, "店铺商品模块装修"),
    SHOP_PICTURE_MODULE_DECORATE(319, "店铺图片模块装修"),
    SHELVES_TR_AMOUNT(346, "风控后泛货架结算订单返佣(货架免佣专用)"),
    MALL_TR_AMOUNT(370, "风控后商城结算订单返佣(货架免佣专用)"),
    NEW_SELL_ITEM_COUNT(395, "动销新品数"),
    HOT_SELL_ITEM_COUNT(396, "5w+爆品数"),
    NEW_SELLER_LEVEL(397, "新商家等级"),
    NEW_SELLER_FILE(434, "完善新商档案"),
    EFFECTIVE_LIVE_DAYS_DOUBLE_COMPLETE(430, "有效电商开播天数（挂车+当天累计 60 分钟）（日周期连续达成）"),
    SETTLE_GMV_RATE(493, "结算率"),
    TR_RATE(494, "TR率"),
    QUALIFICATION_AUTHENTICATION(539, "真实性认证"),
    CONTRACT_ENTERPRISE_WECHAT(552, "建联小二企微"),
    PROMOTER_PRAISE_SCORE(565, "达人分-带货口碑分"),
    PROMOTER_COMMODITY_SCORE(566, "达人分-商品力"),
    PROMOTER_CONTENT_SCORE(567, "达人分-内容力"),
    PROMOTER_DILIGENCE_SCORE(568, "达人分-勤奋力"),
    FISSION_USER_COUNT(595, "拉新报名活动人数"),

    PROMOTER_ITM_REFUND_RATE(610, "品退率（达人）（新）"),
    BROKER_ITM_REFUND_RATE(611, "品退率（团长）（新）"),
    PROMOTER_ALL_CARRIER_ITM_REFUND_RATE(628, "达人品退率（全域分销_支付14日内签收_首因品退）"),
    NEW_SELLER_PUBLISH_GOODS(650, "新商发品"),
    ORDER_FROM_ITEM_SHARE_CNT(651, "商品分享并成交订单数"),
    NEW_SELLER_FIRST_SALE(652, "新商完成首销"),
    JOIN_INSURANCE_ACTIVITY(653, "运费险活动开通"),

    DEFAULT(9999, "默认值");

    private final long value;
    private final String type;
    private final IndicatorUnitTypeEnum unitType;

    public static IndicatorEnum of(long value) {
        for (IndicatorEnum val : IndicatorEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    IndicatorEnum(int value, String type, IndicatorUnitTypeEnum unitType) {
        this.value = value;
        this.type = type;
        this.unitType = unitType;
    }

    IndicatorEnum(int value, String type) {
        this.value = value;
        this.type = type;
        this.unitType = OTHER;
    }

    public long getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public IndicatorUnitTypeEnum getUnitType() {
        return unitType;
    }
}
