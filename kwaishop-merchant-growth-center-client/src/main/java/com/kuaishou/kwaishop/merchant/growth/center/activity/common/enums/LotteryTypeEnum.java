package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-26
 * 抽奖活动类型枚举
 */
public enum LotteryTypeEnum {

    ACTIVITY("activity", "通过活动参与抽奖"),
    ;

    private String code;
    private String desc;

    LotteryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static LotteryTypeEnum getByCode(String code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }
}
