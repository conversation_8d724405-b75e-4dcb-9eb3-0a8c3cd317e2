package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<PERSON><PERSON><PERSON><PERSON>@kuaishou.com>
 * Created on 2021-12-07
 */
public enum UserActivityEventTypeEnum {
    UNKNOWN(0, "未知"),
    REGISTRATION(5, "报名事件"),
    DRAW(10, "领取事件"),
    AUDIT(20, "审核事件"),
    SUCCEED(30, "成功事件"),
    FAILED(40, "失败事件"),
    RISK(41, "风控失败事件"),
    RESTART(50, "活动重启事件"),
    STOP(60, "活动终止事件"),
    ;
    private final int value;
    private final String type;

    public static UserActivityEventTypeEnum of(int value) {
        for (UserActivityEventTypeEnum val : UserActivityEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    UserActivityEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
