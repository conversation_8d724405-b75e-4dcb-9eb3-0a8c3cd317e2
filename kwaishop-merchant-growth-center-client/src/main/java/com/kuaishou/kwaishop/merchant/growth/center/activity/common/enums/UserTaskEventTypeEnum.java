package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <zhu<PERSON><PERSON>@kuaishou.com>
 * Created on 2021-12-07
 */
public enum UserTaskEventTypeEnum {
    UNKNOWN(0, "未知"),
    REGISTRATION(5, "报名事件"),
    DRAW(10, "领取事件"),
    AUDIT(20, "审核事件"),
    SUCCEED(30, "成功事件"),
    FAILED(40, "失败事件"),
    RISK(41, "风控失败事件"),
    RESTART(50, "任务重启事件"),
    STOP(60, "任务终止事件"),
    ;
    private final int value;
    private final String type;

    public static UserTaskEventTypeEnum of(int value) {
        for (UserTaskEventTypeEnum val : UserTaskEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    UserTaskEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
