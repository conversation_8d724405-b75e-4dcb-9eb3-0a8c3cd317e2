package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-21
 */
public enum GetUserActivityListInfoActivityStatusEnum {

    // 初始状态, 默认查全部
    NONE(0, "查全部"),
    // 未开始
    NOT_BEGIN(1, "未开始"),
    // 进行中
    PROCESS(2, "进行中"),
    // 已结束
    END(3, "已结束"),

    UNDRAW(4, "未报名"),

    DEFAULT(5, "默认"),
    ;
    private final long value;
    private final String type;

    public static GetUserActivityListInfoActivityStatusEnum of(long value) {
        for (GetUserActivityListInfoActivityStatusEnum val : GetUserActivityListInfoActivityStatusEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return NONE;
    }

    GetUserActivityListInfoActivityStatusEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public long getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
