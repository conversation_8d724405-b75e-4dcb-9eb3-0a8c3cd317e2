package com.kuaishou.kwaishop.merchant.growth.center.rule.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 规则日志工具
 *
 * <AUTHOR>
 */
public class RuleLogUtil {

    private static final Logger log = LoggerFactory.getLogger(RuleLogUtil.class);

    public static void info(String content) {
        log.info(content);
    }

    public static void info(String content, Object var1) {
        log.info(content, var1);
    }

    public static void info(String content, Object var1, Object var2) {
        log.info(content, var1, var2);
    }

    public static void info(String content, Object var1, Object var2, Object var3) {
        log.info(content, var1, var2, var3);
    }

    public static void info(String content, Object var1, Object var2, Object var3, Object var4) {
        log.info(content, var1, var2, var3, var4);
    }

    public static void info(String content, Object var1, Object var2, Object var3, Object var4, Object var5) {
        log.info(content, var1, var2, var3, var4, var5);
    }

    public static void info(String content, Object var1, Object var2, Object var3, Object var4, Object var5,
            Object var6) {
        log.info(content, var1, var2, var3, var4, var5, var6);
    }

    public static void warn(String content) {
        log.warn(content);
    }

    public static void warn(String content, Object var1) {
        log.warn(content, var1);
    }

    public static void warn(String content, Object var1, Object var2) {
        log.warn(content, var1, var2);
    }

    public static void warn(String content, Object var1, Object var2, Object var3) {
        log.warn(content, var1, var2, var3);
    }

    public static void warn(String content, Object var1, Object var2, Object var3, Object var4) {
        log.warn(content, var1, var2, var3, var4);
    }

    public static void warn(String content, Object var1, Object var2, Object var3, Object var4, Object var5) {
        log.warn(content, var1, var2, var3, var4, var5);
    }

    public static void warn(String content, Object var1, Object var2, Object var3, Object var4, Object var5,
            Object var6) {
        log.warn(content, var1, var2, var3, var4, var5, var6);
    }

    public static void error(String content) {
        log.error(content);
    }

    public static void error(String content, Object var1) {
        log.error(content, var1);
    }

    public static void error(String content, Object var1, Object var2) {
        log.error(content, var1, var2);
    }

    public static void error(String content, Object var1, Object var2, Object var3) {
        log.error(content, var1, var2, var3);
    }

    public static void error(String content, Object var1, Object var2, Object var3, Object var4) {
        log.error(content, var1, var2, var3, var4);
    }

    public static void error(String content, Object var1, Object var2, Object var3, Object var4, Object var5) {
        log.error(content, var1, var2, var3, var4, var5);
    }

    public static void error(String content, Object var1, Object var2, Object var3, Object var4, Object var5,
            Object var6) {
        log.error(content, var1, var2, var3, var4, var5, var6);
    }

}
