package com.kuaishou.kwaishop.merchant.growth.center.plan.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-28
 */
public enum UserActionChangeEventTypeEnum {

    /**
     * 用户动作初始化
     */
    INIT(1),

    /**
     * 用户动作删除
     */
    DELETE(2),

    /**
     * 用户动作执行结果
     * 目前只能依赖hive T+1去判断动作发送完成
     */
    ACTION_EXE_SUCCESS(3),

    /**
     * 用户动作效果收集结果（有效触达）
     */
    EFFECT_COLLECT_FINISH(4),
    /**
     * 用户动作目标达成
     */
    TARGET_INDICATOR_FINISH(5),
    /**
     */
    ;
    private final int type;

    UserActionChangeEventTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static UserActionChangeEventTypeEnum getByType(int type) {
        for (UserActionChangeEventTypeEnum value : UserActionChangeEventTypeEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }
}
