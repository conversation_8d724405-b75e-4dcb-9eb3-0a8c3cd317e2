package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 不使用福袋时，直播间效果枚举
 *
 * <AUTHOR> <ho<PERSON><PERSON><PERSON>@kuaishou.com>
 * Created on 2022-03-22
 */
public enum BroadcastRoomEffectEnum {

    NOT_USE_ANY_EFFECT(0, "不使用任何直播间效果"),
    NOT_USE_LUCK_BAG(10, "不发福袋"),
    GOODS_FROM_SKY(20, "天降商品"),
    ;

    private int code;
    private String desc;

    BroadcastRoomEffectEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static BroadcastRoomEffectEnum getByCode(int code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }
}
