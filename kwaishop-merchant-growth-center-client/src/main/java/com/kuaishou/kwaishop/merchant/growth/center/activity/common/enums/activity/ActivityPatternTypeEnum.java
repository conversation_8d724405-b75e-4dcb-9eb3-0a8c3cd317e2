package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-29
 */
public enum ActivityPatternTypeEnum {

    UNKNOWN("unknown", "未知", 0),
    TARGET_COMPLETE("targetComplete", "目标达成类", 2),
    FANS_RISE("fansRise", "涨粉类", 1),
    TASK("task", "任务类", 6),
    LEADERBOARD("leaderboard", "排行榜类", 3),
    GOODS_MATCH("goodsMatch", "人货撮合", 11),
    HUGE_LIVE_PRE_AWARD("hugeLivePreAward", "大场前返", 12),
    ;

    ActivityPatternTypeEnum(String type, String desc, Integer resourceActivityType) {
        this.type = type;
        this.desc = desc;
        this.resourceActivityType = resourceActivityType;
    }

    private final String type;
    private final String desc;
    private final Integer resourceActivityType;

    public static ActivityPatternTypeEnum getByType(String type) {
        return Stream.of(values()).filter(item -> Objects.equals(type, item.getType()))
                .findFirst().orElse(UNKNOWN);
    }

    /**
     * 各活动类型测算roi准入判断
     *
     * @since 2023.12.01 支持涨粉类活动测算roi
     */
    public static boolean patternCanCalcRoi(String type) {
        ActivityPatternTypeEnum patternTypeEnum = getByType(type);
        return patternTypeEnum.equals(TARGET_COMPLETE) || patternTypeEnum.equals(LEADERBOARD)
                || patternTypeEnum.equals(GOODS_MATCH) || patternTypeEnum.equals(FANS_RISE);
    }

    /**
     * 玩法支持测算
     * @param type
     * @return
     */
    public static boolean patternCanEstimation(String type) {
        ActivityPatternTypeEnum patternTypeEnum = getByType(type);
        return patternTypeEnum.equals(TARGET_COMPLETE) || patternTypeEnum.equals(TASK);
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getResourceActivityType() {
        return resourceActivityType;
    }
}
