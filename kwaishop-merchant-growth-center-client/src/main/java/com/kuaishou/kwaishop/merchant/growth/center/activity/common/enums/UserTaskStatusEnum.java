package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import static java.util.Arrays.asList;

import java.util.List;

/**
 * <AUTHOR> <zhu<PERSON><EMAIL>>
 * Created on 2021-12-07
 */
public enum UserTaskStatusEnum {
    UNKNOWN(0, "未知"),
    DRAWING(10, "待领取"),
    PROCESSING(20, "进行中"),
    AUDITING(30, "审核中"),
    SUCCESS(40, "成功"),
    FAIL(50, "失败"),
    RISK(51, "风控"),
    STOP(60, "终止"),
    ;
    private final int value;
    private final String type;

    public static UserTaskStatusEnum of(int value) {
        for (UserTaskStatusEnum val : UserTaskStatusEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    public static List<UserTaskStatusEnum> getFinalStatus() {
        return asList(SUCCESS, FAIL, RISK, STOP);
    }

    UserTaskStatusEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
