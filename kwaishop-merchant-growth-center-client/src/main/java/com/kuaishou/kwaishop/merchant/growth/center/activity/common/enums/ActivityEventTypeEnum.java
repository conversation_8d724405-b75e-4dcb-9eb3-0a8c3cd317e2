package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-08-07
 */
public enum ActivityEventTypeEnum {
    UNKNOWN(0, "未知"),
    CREATE_EVENT(1, "创建事件"),
    AUDIT_ING_EVENT(4, "审批中事件"),
    AUDIT_PASS_EVENT(5, "审核通过事件"),
    ONLINE_EVENT(6, "活动上线事件");
    private final int value;
    private final String type;

    public static ActivityEventTypeEnum of(int value) {
        for (ActivityEventTypeEnum val : ActivityEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    ActivityEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
