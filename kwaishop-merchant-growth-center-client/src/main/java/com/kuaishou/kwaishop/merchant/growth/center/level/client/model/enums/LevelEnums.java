package com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-01
 */
public enum LevelEnums {
    UNKNOWN(0, "暂无定级"),
    NEW_SELLER(1, "新手商家"),

    COPPER_1(11, "铜1"),
    COPPER_2(12, "铜2"),
    COPPER_3(13, "铜3"),

    SILVER_1(21, "银1"),
    SILVER_2(22, "银2"),
    SILVER_3(23, "银3"),

    GOLD_1(31, "金1"),
    GOLD_2(32, "金2"),
    GOLD_3(33, "金3"),
    GOLD_4(34, "金4"),
    GOLD_5(35, "金5"),
    GOLD_6(36, "金6"),
    ;
    private final int value;
    private final String type;

    private static final Map<Integer, LevelEnums>
            VALUE_MAP = Arrays.stream(LevelEnums.values())
            .collect(Collectors.toMap(LevelEnums::getValue, Function.identity()));

    public static LevelEnums fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private static final Map<String, LevelEnums>
            TYPE_MAP = Arrays.stream(LevelEnums.values())
            .collect(Collectors.toMap(LevelEnums::getType, Function.identity()));

    public static LevelEnums fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN);
    }

    /**
     * 小于最高等级，获取下一等级枚举。若为最高等级则返回最高等级。
     */
    public static LevelEnums getNextLevel(int currentLevel) {
        int num = 0;
        List<LevelEnums> allLevels = Arrays.stream(LevelEnums.values())
                .filter(levelEnum -> levelEnum != NEW_SELLER && levelEnum != UNKNOWN)
                .sorted(Comparator.comparing(LevelEnums::getValue))
                .collect(Collectors.toList());
        for (LevelEnums levelEnum : allLevels) {
            if (levelEnum.getValue() == currentLevel) {
                break;
            }
            num++;
        }
        if (num < allLevels.size() - 1) {
            return allLevels.get(num + 1);
        }
        return allLevels.get(allLevels.size() - 1);
    }

    /**
     * 返回从指定最小的等级的所有等级列表
     */
    public static List<Integer> parseAllLevelEnumsFromMinLevel(int minLevel) {
        return VALUE_MAP.keySet().stream().filter(value -> value >= minLevel).sorted().collect(Collectors.toList());
    }

    /**
     * 返回指定范围的等级集合
     */
    public static List<LevelEnums> getLevelRangeCollections(LevelEnums minLevel, LevelEnums maxLevel) {
        List<LevelEnums> res = new ArrayList<>();
        for (LevelEnums level : LevelEnums.values()) {
            if (level.getValue() >= minLevel.getValue() && level.getValue() <= maxLevel.getValue()) {
                res.add(level);
            }
        }
        return res;
    }

    LevelEnums(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
