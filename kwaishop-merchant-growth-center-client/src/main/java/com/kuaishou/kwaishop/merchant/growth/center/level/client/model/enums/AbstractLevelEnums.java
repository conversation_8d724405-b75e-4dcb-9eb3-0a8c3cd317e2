package com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-18
 */
public enum AbstractLevelEnums {
    UNKNOWN(0, "unknown"),
    NEW_SELLER(1, "新手商家"),
    COPPER(10, "铜"),
    SILVER(20, "银"),
    GOLD(30, "金"),
    ;
    private final int value;
    private final String type;

    private static final Map<Integer, AbstractLevelEnums>
            VALUE_MAP = Arrays.stream(AbstractLevelEnums.values())
            .collect(Collectors.toMap(AbstractLevelEnums::getValue, Function.identity()));

    public static AbstractLevelEnums fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private static final Map<String, AbstractLevelEnums>
            TYPE_MAP = Arrays.stream(AbstractLevelEnums.values())
            .collect(Collectors.toMap(AbstractLevelEnums::getType, Function.identity()));

    public static AbstractLevelEnums fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN);
    }

    /**
     * 返回从指定最小的等级的所有等级列表
     */
    public static List<Integer> parseAllAbstractLevelEnumsFromMinLevel(int minLevel) {
        return VALUE_MAP.keySet().stream().filter(value -> value >= minLevel).sorted().collect(Collectors.toList());
    }

    AbstractLevelEnums(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
