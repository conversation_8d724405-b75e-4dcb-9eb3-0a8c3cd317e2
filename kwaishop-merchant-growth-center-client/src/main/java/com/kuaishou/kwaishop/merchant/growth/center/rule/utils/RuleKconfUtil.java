package com.kuaishou.kwaishop.merchant.growth.center.rule.utils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiFunction;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.KconfBuilder;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.kconf.client.MoreKconfs;

/**
 * 规则Kconf工具类
 *
 * <AUTHOR>
 */
public class RuleKconfUtil {

    public static String ofString(String fullConfigKey, String defaultValue) {
        return ofValue(Kconfs::ofString, fullConfigKey, defaultValue);
    }

    public static Integer ofInteger(String fullConfigKey, Integer defaultValue) {
        return ofValue(Kconfs::ofInteger, fullConfigKey, defaultValue);
    }

    public static Boolean ofBoolean(String fullConfigKey, Boolean defaultValue) {
        return ofValue(Kconfs::ofBoolean, fullConfigKey, defaultValue);
    }

    public static Double ofDouble(String fullConfigKey, Double defaultValue) {
        return ofValue(Kconfs::ofDouble, fullConfigKey, defaultValue);
    }

    public static Long ofLong(String fullConfigKey, Long defaultValue) {
        return ofValue(Kconfs::ofLong, fullConfigKey, defaultValue);
    }

    public static List<String> ofStringList(String fullConfigKey, List<String> defaultValue) {
        return ofValue(Kconfs::ofStringList, fullConfigKey, defaultValue);
    }

    public static Set<String> ofStringSet(String fullConfigKey, Set<String> defaultValue) {
        return ofValue(Kconfs::ofStringSet, fullConfigKey, defaultValue);
    }

    public static List<Integer> ofIntegerList(String fullConfigKey, List<Integer> defaultValue) {
        return ofValue(Kconfs::ofIntegerList, fullConfigKey, defaultValue);
    }

    public static Set<Integer> ofIntegerSet(String fullConfigKey, Set<Integer> defaultValue) {
        return ofValue(Kconfs::ofIntegerSet, fullConfigKey, defaultValue);
    }

    public static List<Double> ofDoubleList(String fullConfigKey, List<Double> defaultValue) {
        return ofValue(Kconfs::ofDoubleList, fullConfigKey, defaultValue);
    }

    public static Set<Double> ofDoubleSet(String fullConfigKey, Set<Double> defaultValue) {
        return ofValue(Kconfs::ofDoubleSet, fullConfigKey, defaultValue);
    }

    public static boolean isOnFor(String fullConfigKey, long id, boolean defaultValue) {
        return MoreKconfs.newTailNumberConfig(fullConfigKey, defaultValue).get().isOnFor(id);
    }

    public static <T> T ofJson(String fullConfigKey, T defaultValue, Class<T> clz) {
        Kconf<T> kConf = Kconfs.ofJson(fullConfigKey, defaultValue, clz).build();
        if (kConf == null) {
            return defaultValue;
        }
        return kConf.get();
    }

    public static <T extends Map<K, V>, K, V> T ofJsonMap(String fullConfigKey, T defaultValue,
            Class<K> kType, Class<V> vType) {
        Kconf<T> kConf = Kconfs.ofJsonMap(fullConfigKey, defaultValue, kType, vType).build();
        if (kConf == null) {
            return defaultValue;
        }
        return kConf.get();
    }

    public static <T> Set<T> ofJsonSet(String fullConfigKey, Set<T> defaultValue, Class<T> clz) {
        Kconf<Set<T>> kConf = Kconfs.ofJsonSet(fullConfigKey, defaultValue, clz).build();
        if (kConf == null) {
            return defaultValue;
        }
        return kConf.get();
    }

    public static <T> List<T> ofJsonList(String fullConfigKey, List<T> defaultValue, Class<T> clz) {
        Kconf<List<T>> kConf = Kconfs.ofJsonList(fullConfigKey, defaultValue, clz).build();
        if (kConf == null) {
            return defaultValue;
        }
        return kConf.get();
    }

    public static <T> T ofValue(BiFunction<String, T, KconfBuilder<T>> function, String fullConfigKey, T defaultValue) {
        KconfBuilder<T> confBuilder = function.apply(fullConfigKey, defaultValue);
        if (confBuilder == null) {
            return defaultValue;
        }
        Kconf<T> conf = confBuilder.build();
        return conf == null ? defaultValue : conf.get();
    }

}
