package com.kuaishou.kwaishop.merchant.growth.center.rule.utils;


import org.apache.commons.lang3.StringUtils;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.util.PerfBuilder;
import com.kuaishou.framework.util.PerfUtils;

/**
 * Perf工具类
 */
public class PerfUtil {
    private static final String DEFAULT_PERF_NAME_SPACE = "growth.strategy.rule";

    private PerfUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static void perfSuccess(String tag) {
        doPerf(buildSuccessTag(tag), null);
    }

    public static void perfSuccess(String tag, StopWatch cost) {
        doPerf(buildSuccessTag(tag), cost.getTimeMicros());
    }

    public static void perfSuccess(String tag, Long cost) {
        doPerf(buildSuccessTag(tag), cost);
    }

    public static void perfSuccess(String tag, String ext, StopWatch cost) {
        doPerf(buildSuccessTag(tag), ext, cost.getTimeMicros());
    }

    public static void perfSuccess(String tag, String ext, Long cost) {
        doPerf(buildSuccessTag(tag), ext, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, StopWatch cost) {
        doPerf(buildSuccessTag(tag), ext1, ext2, cost.getTimeMicros());
    }

    public static void perfSuccess(String tag, String ext1, String ext2, Long cost) {
        doPerf(buildSuccessTag(tag), ext1, ext2, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, StopWatch cost) {
        doPerf(buildSuccessTag(tag), ext1, ext2, ext3, cost.getTimeMicros());
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, Long cost) {
        doPerf(buildSuccessTag(tag), ext1, ext2, ext3, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, String ext4, StopWatch cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, ext4, cost.getTimeMicros());
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, String ext4, Long cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, ext4, cost);
    }

    public static void perfException(String tag) {
        doPerf(buildExceptionTag(tag), null);
    }

    public static void perfException(String tag, StopWatch cost) {
        doPerf(buildExceptionTag(tag), cost.getTimeMicros());
    }

    public static void perfException(String tag, Long cost) {
        doPerf(buildExceptionTag(tag), cost);
    }

    public static void perfException(String tag, String ext, StopWatch cost) {
        doPerf(buildExceptionTag(tag), ext, cost.getTimeMicros());
    }

    public static void perfException(String tag, String ext, Long cost) {
        doPerf(buildExceptionTag(tag), ext, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, StopWatch cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, cost.getTimeMicros());
    }

    public static void perfException(String tag, String ext1, String ext2, Long cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, StopWatch cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, cost.getTimeMicros());
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, Long cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, String ext4, StopWatch cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, ext4, cost.getTimeMicros());
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, String ext4, Long cost) {
        doPerf(buildExceptionTag(tag), ext1, ext2, ext3, ext4, cost);
    }

    private static String buildSuccessTag(String tag) {
        return StringUtils.defaultIfBlank(tag, StringUtils.EMPTY) + ".success";
    }

    private static String buildExceptionTag(String tag) {
        return StringUtils.defaultIfBlank(tag, StringUtils.EMPTY) + ".exception";
    }

    private static void doPerf(String tag, Long timeoutMs) {
        PerfBuilder builder = PerfUtils.perf(DEFAULT_PERF_NAME_SPACE, tag);
        builder = timeoutMs == null ? builder : builder.micros(timeoutMs);
        builder.logstash();
    }

    private static void doPerf(String tag, String ext, Long timeoutMs) {
        PerfBuilder builder = PerfUtils.perf(DEFAULT_PERF_NAME_SPACE, tag, ext);
        builder = timeoutMs == null ? builder : builder.micros(timeoutMs);
        builder.logstash();
    }

    private static void doPerf(String tag, String ext1, String ext2, Long timeoutMs) {
        PerfBuilder builder = PerfUtils.perf(DEFAULT_PERF_NAME_SPACE, tag, ext1, ext2);
        builder = timeoutMs == null ? builder : builder.micros(timeoutMs);
        builder.logstash();
    }

    private static void doPerf(String tag, String ext1, String ext2, String ext3, Long timeoutMs) {
        PerfBuilder builder = PerfUtils.perf(DEFAULT_PERF_NAME_SPACE, tag, ext1, ext2, ext3);
        builder = timeoutMs == null ? builder : builder.micros(timeoutMs);
        builder.logstash();
    }

    private static void doPerf(String tag, String ext1, String ext2, String ext3, String ext4, Long timeoutMs) {
        PerfBuilder builder = PerfUtils.perf(DEFAULT_PERF_NAME_SPACE, tag, ext1, ext2, ext3, ext4);
        builder = timeoutMs == null ? builder : builder.micros(timeoutMs);
        builder.logstash();
    }

    public static StopWatch getStopWatch() {
        return PerfUtils.getWatcher();
    }
}
