package com.kuaishou.kwaishop.merchant.growth.center.diagnosis.enums;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-06
 */
public enum EntityTypeEnum {
    SELLER("seller", "商家", "sellerId"),
    LIVE("live", "直播间", "liveId"),
    VIDEO("video", "短视频", "videoId"),
    ITEM("item", "商品", "itemId"),
    ;

    private final String type;

    private final String desc;

    private final String idStr;

    EntityTypeEnum(String type, String desc, String idStr) {
        this.type = type;
        this.desc = desc;
        this.idStr = idStr;
    }

    public static EntityTypeEnum of(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        return Arrays.stream(EntityTypeEnum.values()).filter(entityType -> entityType.getType().equals(type))
                .findFirst().orElse(null);
    }

    public static List<EntityTypeEnum> getIndicatorEntityTypeList(EntityTypeEnum diagnoseEntityType) {
        if (null == diagnoseEntityType) {
            return Lists.newArrayList();
        }
        switch (diagnoseEntityType) {
            case SELLER:
                return Lists.newArrayList(SELLER);
            case LIVE:
                return Lists.newArrayList(SELLER, LIVE);
            case VIDEO:
                return Lists.newArrayList(SELLER, VIDEO);
            case ITEM:
                return Lists.newArrayList(SELLER, ITEM);
            default:
                return Lists.newArrayList();
        }
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public String getIdStr() {
        return idStr;
    }
}
