package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-17
 */
public enum FlowNodeStatusEnum {
    PROCESSING(0, "进行中"),
    SUCCEED(1, "成功"),
    FAILED(2, "失败"),
    ;

    private int code;
    private String desc;

    FlowNodeStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
