package com.kuaishou.kwaishop.merchant.growth.center.client.config.mq;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 * <p>
 * RocketMQ生产group示例，也可以放在service工程内
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopMerchantGrowthMqProducer {
    /**
     * 商家成长奖励审批到风控消息
     */
    p_kwaishop_merchant_growth_award_audit_to_risk_msg,

    /**
     * 商家成长活动事件消息
     */
    p_kwaishop_merchant_growth_activity_event_msg,

    /**
     * 使用KWAISHOP_MERCHANT_GROWTH前缀
     */
    p_kwaishop_merchant_growth_txexample_mq,

    /**
     * 文件导出消息
     */
    p_kwaishop_merchant_growth_file_export,

    /**
     * 粉丝团奖励发放重试补偿
     */
    p_kwaishop_merchant_growth_fansgroup_award_compensate_mq,

    /**
     * 用户策略记录变更消息
     */
    p_kwaishop_merchant_growth_user_strategy_record_status_change,

    /**
     * 用户活动指标事件消息
     */
    p_kwaishop_growth_indicator_event_msg,

    /**
     * 用户离线指标更新通用消息
     */
    p_kwaishop_growth_user_indicator_update_common_msg,

    /**
     * 用户活动奖励事件消息
     */
    p_kwaishop_growth_user_award_event_msg,

    /**
     * 用户活动审核事件消息
     */
    p_kwaishop_growth_user_audit_event_msg,

    /**
     * 用户活动任务事件消息
     */
    p_kwaishop_growth_user_task_event_msg,

    /**
     * 用户活动事件消息
     */
    p_kwaishop_growth_user_activity_event_msg,
    /**
     * 用户报名记录变更消息
     */
    p_merchant_growth_user_registration_record_status_change,
    /**
     * 用户消息推送-延迟消息
     */
    p_kwaishop_merchant_growth_delay_notification_push,
    /**
     * 推送完成消息
     */
    p_kwaishop_merchant_growth_finish_notification_push,

    /**
     * 策略域消息推送
     */
    p_kuaishop_merchant_growth_strategy_event_msg,
    /**
     * 延迟更新指标
     */
    p_kwaishop_merchant_growth_delay_update_indicator,

    /**
     * 自定义基期异步校验基期均值
     */
    p_kwaishop_merchant_growth_activity_base_custom_check,

    /**
     * 手动导入基期或报名MQ事件
     */
    p_kwaishop_merchant_growth_manual_draw_sign_up_activity,

    /**
     * 用户抽奖状态变更消息
     */
    p_kwaishop_growth_user_lottery_award_event_msg,

    /**
     * Hive导入校验消息
     */
    p_kwaishop_merchant_growth_hive_import_check_msg,

    /**
     * 任务完成后推送用户抽奖资格
     */
    p_kwaishop_growth_user_lottery_registration_event_msg,

    ;

}
