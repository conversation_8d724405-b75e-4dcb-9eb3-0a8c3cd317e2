package com.kuaishou.kwaishop.merchant.growth.center.level.client.utils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums.LevelEnum;

/**
 * 等级3.0工具类
 *
 * <AUTHOR>
 */
public class LevelUtils {

    /**
     * 校验等级是否合法
     */
    public static boolean checkValid(LevelEnum level) {
        return Arrays.stream(LevelEnum.values()).filter(item -> !item.equals(LevelEnum.UNKNOWN))
                .anyMatch(item -> item.equals(level));
    }

    /**
     * 校验等级是否合法
     */
    public static boolean checkValid(int level) {
        return Arrays.stream(LevelEnum.values()).filter(item -> !item.equals(LevelEnum.UNKNOWN))
                .anyMatch(item -> level == item.getValue());
    }

    /**
     * 等级距离, 参数非法返回0； 结果为负数表示降级
     */
    public static int levelDistance(int startLevel, int endLevel) {
        if (!checkValid(startLevel) || !checkValid(endLevel)) {
            return 0;
        }
        List<Integer> levels = Arrays.stream(LevelEnum.values())
                .map(LevelEnum::getValue).sorted().collect(Collectors.toList());
        int start = 0;
        int end = 0;
        for (int i = 0; i < levels.size(); ++i) {
            if (levels.get(i) == startLevel) {
                start = i;
            }
            if (levels.get(i) == endLevel) {
                end = i;
            }
        }
        return end - start;
    }
}
