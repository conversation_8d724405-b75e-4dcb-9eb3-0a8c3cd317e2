package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-05
 */
public enum ActivityBindTypeEnum {

    UNKNOWN(0, "未知"),
    BIND(1, "绑定"),
    UNBINDING(2, "解绑"),
    ;

    private int code;
    private String desc;

    ActivityBindTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return this.desc;
    }

    public int getCode() {
        return this.code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static ActivityBindTypeEnum getByCode(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(UNKNOWN);
    }
}
