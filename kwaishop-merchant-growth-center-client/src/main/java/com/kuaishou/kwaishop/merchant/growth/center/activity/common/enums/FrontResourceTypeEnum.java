package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-02
 */
public enum FrontResourceTypeEnum {

    /**
     * 规则页面
     */
    RULE_PAGE("rulePage", "独立活动页"),
    /**
     * 排行榜详情页面
     */
    RANK_PAGE("rankPage", "排行榜详情页面"),
    /**
     * 组件搭建
     */
    COMPONENT_BUILD("componentBuild", "组件搭建"),
    /**
     * 组件搭建
     */
    STRATEGY_COMPONENT_BUILD("strategyComponentBuild", "组件搭建"),
    /**
     * 达人工作台组件搭建
     */
    DAREN_COMPONENT("darenComponent", "达人组件搭建"),
    /**
     * 使用方自建
     */
    PARTNER_BUILD("partnerBuild", "使用方自建"),
    /**
     * 团长组件搭建
     */
    BROKER_COMPONENT("brokerComponent", "团长组件搭建"),
    /**
     *大场直播页
     */
    HUGE_LIVE_TASK_PAGE("hugeLiveTaskPage", "大场直播页"),
    /**
     * 极速版系统生成
     */
    SYSTEM("system", "系统生成"),
    /**
     * 极速版上传头图
     */
    UPLOAD("upload", "上传头图"),
    ;


    private String type;

    private String name;

    public static FrontResourceTypeEnum of(String type) {
        for (FrontResourceTypeEnum val : FrontResourceTypeEnum.values()) {
            if (val.getType().equals(type)) {
                return val;
            }
        }
        return null;
    }

    FrontResourceTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
