package com.kuaishou.kwaishop.merchant.growth.center.client.common.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.kuaishou.framework.util.ObjectMapperUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-10-26
 */
public enum MerchantGrowthResponseEnum {
    SUCCESS(1, "操作成功"),
    FAILED(2, "操作失败"),
    TO_MANY_REQ(8, "操作过于频繁，请稍后再试"),
    SERVER_ERROR(11, "服务异常"),
    PARAM_BLANK(14, "参数缺失，请检查接口参数"),
    PARAM_INVALID(21, "参数不合法"),
    PARAM_TOO_LONG(24, "存在超长参数"),
    DATA_IS_NOT_EXIST(25, "数据不存在"),

    LAYER_GMV_CONFIG_NOT_EXIST(2620000, "gmv分层配置不存在！"),
    AWARD_IS_ABOVE_LIMIT(2620001, "目标奖励超过最大限制"),
    ACTIVITY_NOT_EXIST(2620002, "活动id不存在！"),


    ;

    /** 领域标识：262 0000 状态code标识 */
    private int code;
    private String desc;

    MerchantGrowthResponseEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        Map<Integer, List<MerchantGrowthResponseEnum>> map = new HashMap<>();
        for (MerchantGrowthResponseEnum key : values()) {
            List<MerchantGrowthResponseEnum> keys = map.computeIfAbsent(key.code,
                    i -> new ArrayList<>());
            keys.add(key);
        }

        List<List<MerchantGrowthResponseEnum>> duplicated = map.values().stream()
                .filter(list -> list.size() > 1)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(duplicated)) {
            throw new IllegalArgumentException(
                    "！！！！严重错误！！！！MerchantGrowthResponseEnum code 冲突:" + ObjectMapperUtils.toJSON(duplicated)
            );
        }

    }


    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
