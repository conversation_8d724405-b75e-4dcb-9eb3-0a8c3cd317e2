package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

import java.util.Objects;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
public enum StrategyTipTypeEnum {

    ACTIVITY_NAME("activityName", "活动名称"),
    ACTIVITY_PROFIT_POINT("activityProfitPoint", "活动利益点"),
    ACTIVITY_DETAIL_RULE_DESC("activityDetailRuleDesc", "活动规则说明"),
    ACTIVITY_PAGE_BACKGROUND_IMG("activityPageBackgroundImg", "活动页背景图片"),
    AWARD_DETAIL_RULE_DESC("awardDetailRuleDesc", "奖励详情页规则说明"),
    ;

    private String code;
    private String desc;

    StrategyTipTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static StrategyTipTypeEnum getByCode(String code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }

}
