package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.Indicator;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * @Description roi计算额外指标数据
 * Created on 2023-12-11
 */
public enum RoiExtraIndicatorTypeEnum {
    BASE_PERIOD_SINGLE_FAN_LTV90("basePeriodSingleFanLtv90", "基期单新粉LTV90"),

    ACTIVITY_PERIOD_FORECAST_SINGLE_FAN_LTV90("activityPeriodForecastSingleFanLtv90", "活动期预估单新粉LTV90"),
    ;
    private final String type;
    private final String desc;

    RoiExtraIndicatorTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static RoiExtraIndicatorTypeEnum of(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return Arrays.stream(RoiExtraIndicatorTypeEnum.values()).filter(val -> val.getType().equals(type)).findFirst()
                .orElse(null);
    }
}
