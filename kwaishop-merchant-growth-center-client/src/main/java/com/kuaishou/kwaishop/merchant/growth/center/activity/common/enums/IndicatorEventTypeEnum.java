package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-04
 */
public enum IndicatorEventTypeEnum {
    UNKNOWN(0, "未知"),
    CREATE_EVENT(1, "创建事件"),
    COMPLETE_EVENT(2, "完成事件"),
    AUDIT_EVENT(3, "过期审核事件"),
    FAIL_EVENT(4, "失败事件"),
    SUCCESS_EVENT(5, "成功事件"),
    RISK_AUDIT_EVENT(6, "风控审核事件"),
    RISK_FAIL_EVENT(7, "风控失败事件"),
    RESTART_EVENT(8, "重启事件"),
    STOP_EVENT(9, "终止事件"),
    ;
    private final int value;
    private final String type;

    public static IndicatorEventTypeEnum of(int value) {
        for (IndicatorEventTypeEnum val : IndicatorEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    IndicatorEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
