package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.lottery;

/**
 * 用户中奖事件
 */
public enum UserLotteryEventTypeEnum {
    UNKNOWN(0, "未知"),
    HIT_EVENT(10, "中奖事件"),
    MISS_EVENT(20, "未中奖事件"),
    ;
    private final int value;
    private final String type;

    public static UserLotteryEventTypeEnum of(int value) {
        for (UserLotteryEventTypeEnum val : UserLotteryEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    UserLotteryEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
