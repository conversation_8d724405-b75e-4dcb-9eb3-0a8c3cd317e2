package com.kuaishou.kwaishop.merchant.growth.center.client.config.kafka;

import static com.kuaishou.framework.kafka.topic.LogTopicBuilder.builder;
import static com.kuaishou.framework.util.EnumUtils.checkDuplicate;

import com.kuaishou.framework.kafka.log.ILogTopic;
import com.kuaishou.framework.kafka.topic.LogTopicBuilder;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopMerchantGrowthLogTopic implements ILogTopic {
    // 使用KWAISHOP_MERCHANT_GROWTH前缀
    kwaishop_merchant_growth_example_topic(builder().hasTestEnv()), //测试
    kwaishop_merchant_growth_task_sign_record(builder().hasTestEnv()),
    // 粉丝团项目奖励金topic
    activity_fans_crowd_res_topic(builder().hasTestEnv()),
    // 审批域同步风控消息topic
    kwaishop_merchant_growth_activity_audit_sync_risk(builder().hasTestEnv()),
    kwaishop_merchant_growth_indicator_calc_detail_order(builder().hasTestEnv()),
    ;

    static {
        checkDuplicate(values(), ILogTopic::getTopicName);
    }

    private final ILogTopic underlying;

    KwaishopMerchantGrowthLogTopic() {
        this(new LogTopicBuilder());
    }

    KwaishopMerchantGrowthLogTopic(LogTopicBuilder builder) {
        this.underlying = builder.build(name());
    }

    @Override
    public String getTopicName() {
        return underlying.getTopicName();
    }

    @Override
    public String getProducerName() {
        return underlying.getProducerName();
    }

    @Override
    public String toString() {
        return getTopicName();
    }
}
