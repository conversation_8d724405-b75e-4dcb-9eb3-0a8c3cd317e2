package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-21
 */
public enum AwardEventTypeEnum {
    UNKNOWN(0, "未知"),
    CALCULATING_EVENT(3, "计算中"),
    SUCCESS_EVENT(10, "成功事件"),
    FAIL_EVENT(20, "失败事件"),
    RISK_EVENT(30, "风控事件"),
    AUDIT_EVENT(40, "审核事件"),
    SEND_EVENT(50, "发放事件"),
    HOLD_EVENT(60, "挂起事件"),
    CANCEL_EVENT(70, "取消事件"),
    OPERATION_SENDING_EVENT(80, "横向审批事件"),
    ;
    private final int value;
    private final String type;

    public static AwardEventTypeEnum of(int value) {
        for (AwardEventTypeEnum val : AwardEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    AwardEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
