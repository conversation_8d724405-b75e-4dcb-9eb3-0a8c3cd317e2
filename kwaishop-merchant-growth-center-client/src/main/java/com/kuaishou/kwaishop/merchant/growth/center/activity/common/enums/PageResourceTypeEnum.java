package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;


import java.util.List;

import com.google.common.collect.Lists;

/**
 * 页面资源类型枚举
 *
 * <AUTHOR>
 */
public enum PageResourceTypeEnum {
    /**
     * 工作台卡片
     */
    WORKBENCH_CARD("workbenchCard", "工作台卡片"),
    /**
     * 规则页面
     */
    RULE_PAGE("rulePage", "独立活动页"),
    /**
     * 纵向任务规则页面
     */
    PORTRAIT_RULE_PAGE("portraitRulePage", "纵向活动规则页"),
    /**
     * 福利中心
     */
    WELFARE_CENTER("welfareCenter", "福利中心"),
    /**
     * 报名页H5
     */
    REGISTRATION_PAGE("registrationPage", "活动报名页"),
    /**
     * 分销达人广场
     */
    DAREN_SQUARE("darenSquare", "达人广场"),
    /**
     * 不展示
     */
    OFFLINE("offline", "不展示"),
    /**
     * 达人工作台
     */
    DAREN_WORKBENCH("darenWorkbench", "达人工作台"),
    /**
     * 达人工作台训练营
     */
    DAREN_WORKBENCH_TRAINING("darenWorkbenchTraining", "达人工作台-训练营"),
    /**
     * 大促专用
     */
    PROMOTION("promotion", "大促专用"),
    /**
     * 团长PC工作台
     */
    BROKER_WORKBENCH("brokerWorkbench", "团长PC工作台"),

    /**
     * 商家工作台
     */
    SELLER_WORKBENCH("sellerWorkbench", "商家工作台"),
    ;
    private String type;

    private String name;

    public static PageResourceTypeEnum of(String type) {
        for (PageResourceTypeEnum val : PageResourceTypeEnum.values()) {
            if (val.getType().equals(type)) {
                return val;
            }
        }
        return null;
    }

    PageResourceTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static final List<String> DAREN_SHOW_CHANNEL_COMBINE = Lists.newArrayList(DAREN_WORKBENCH.getType(),
            DAREN_SQUARE.getType());

}
