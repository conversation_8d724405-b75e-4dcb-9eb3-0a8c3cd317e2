package com.kuaishou.kwaishop.merchant.growth.center.rule.utils;


/**
 * <AUTHOR>
 */
public class RulePerfUtil {
    /**
     * 自定义Perf的公共前缀
     */
    private static final String CUSTOM_PERF_PREFIX = "rule.custom.";

    public static long getCurrentTimeMs() {
        return System.currentTimeMillis();
    }

    public static void perfSuccess(String tag) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag));
    }

    public static void perfSuccess(String tag, Long cost) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag), cost);
    }

    public static void perfSuccess(String tag, String ext, Long cost) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag), ext, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, Long cost) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag), ext1, ext2, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, Long cost) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag), ext1, ext2, ext3, cost);
    }

    public static void perfSuccess(String tag, String ext1, String ext2, String ext3, String ext4, Long cost) {
        PerfUtil.perfSuccess(buildPerfSubTag(tag), ext1, ext2, ext3, ext4, cost);
    }

    public static void perfException(String tag) {
        PerfUtil.perfException(buildPerfSubTag(tag));
    }

    public static void perfException(String tag, Long cost) {
        PerfUtil.perfException(buildPerfSubTag(tag), cost);
    }

    public static void perfException(String tag, String ext, Long cost) {
        PerfUtil.perfException(buildPerfSubTag(tag), ext, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, Long cost) {
        PerfUtil.perfException(buildPerfSubTag(tag), ext1, ext2, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, Long cost) {
        PerfUtil.perfException(buildPerfSubTag(tag), ext1, ext2, ext3, cost);
    }

    public static void perfException(String tag, String ext1, String ext2, String ext3, String ext4, Long cost) {
        PerfUtil.perfException(buildPerfSubTag(tag), ext1, ext2, ext3, ext4, cost);
    }

    private static String buildPerfSubTag(String customSubTab) {
        return CUSTOM_PERF_PREFIX + customSubTab;
    }

}
