package com.kuaishou.kwaishop.merchant.growth.center.plan.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-28
 */
public enum UserActionEffectStatusEnum {

    EXECUTE_SUCCESS(1, "有效执行"),
    REACH_VALID(2, "有效触达"),
    TARGET_INDICATOR_FINISH(3, "目标达成"),
    ;

    private final int code;
    private final String desc;

    UserActionEffectStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static UserActionEffectStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserActionEffectStatusEnum value : UserActionEffectStatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
