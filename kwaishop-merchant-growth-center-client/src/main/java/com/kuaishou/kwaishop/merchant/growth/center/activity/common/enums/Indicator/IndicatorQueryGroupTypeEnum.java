package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.Indicator;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
public enum IndicatorQueryGroupTypeEnum {
    UNKNOWN("", "未知"),
    UNION("union", "并集"),
    INTERSECTION("intersection", "交集"),

    ;
    private final String type;
    private final String desc;

    public static IndicatorQueryGroupTypeEnum of(String type) {
        for (IndicatorQueryGroupTypeEnum val : IndicatorQueryGroupTypeEnum.values()) {
            if (val.getType().equals(type)) {
                return val;
            }
        }
        return UNKNOWN;
    }

    IndicatorQueryGroupTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public String getType() {
        return type;
    }
    public String getDesc() {
        return desc;
    }

}
