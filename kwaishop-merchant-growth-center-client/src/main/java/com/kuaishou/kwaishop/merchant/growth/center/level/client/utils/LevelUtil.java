package com.kuaishou.kwaishop.merchant.growth.center.level.client.utils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums.AbstractLevelEnums;
import com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums.LevelEnums;

/**
 * 等级2.0时的历史工具类，已经废弃，后续工具类统一使用LevelUtil
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-18
 */
@Deprecated
public class LevelUtil {
    /**
     * 根据等级获取金银铜大类等级
     */
    public static int getAbstractLevel(int level) {
        if (!checkValid(level)) {
            return AbstractLevelEnums.UNKNOWN.getValue();
        }
        if (level == LevelEnums.NEW_SELLER.getValue()) {
            return AbstractLevelEnums.NEW_SELLER.getValue();
        }
        if (level >= LevelEnums.GOLD_1.getValue()) {
            return AbstractLevelEnums.GOLD.getValue();
        }
        if (level >= LevelEnums.SILVER_1.getValue()) {
            return AbstractLevelEnums.SILVER.getValue();
        }
        if (level >= LevelEnums.COPPER_1.getValue()) {
            return AbstractLevelEnums.COPPER.getValue();
        }
        return AbstractLevelEnums.UNKNOWN.getValue();
    }

    /**
     * 根据等级获取金银铜大类等级
     */
    public static AbstractLevelEnums getAbstractLevel(LevelEnums level) {
        if (!checkValid(level)) {
            return AbstractLevelEnums.UNKNOWN;
        }
        return AbstractLevelEnums.fromValue(getAbstractLevel(level.getValue()));
    }

    /**
     * 校验等级是否合法
     */
    public static boolean checkValid(LevelEnums level) {
        return Arrays.stream(LevelEnums.values()).filter(item -> !item.equals(LevelEnums.UNKNOWN))
                .anyMatch(item -> item.equals(level));
    }

    /**
     * 校验等级是否合法
     */
    public static boolean checkValid(int level) {
        return Arrays.stream(LevelEnums.values()).filter(item -> !item.equals(LevelEnums.UNKNOWN))
                .anyMatch(item -> level == item.getValue());
    }

    /**
     * 等级距离, 参数非法返回0； 结果为负数表示降级
     */
    public static int levelDistance(int startLevel, int endLevel) {
        if (!checkValid(startLevel) || !checkValid(endLevel)) {
            return 0;
        }
        List<Integer> levels =
                Arrays.stream(LevelEnums.values()).map(LevelEnums::getValue).sorted().collect(Collectors.toList());
        int start = 0;
        int end = 0;
        for (int i = 0; i < levels.size(); ++i) {
            if (levels.get(i) == startLevel) {
                start = i;
            }
            if (levels.get(i) == endLevel) {
                end = i;
            }
        }
        return end - start;
    }
}
