package com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-11
 */
public enum LevelTaskTypeEnum {
    UNKNOWN(0, "unknown"),
    UNION_PULL(10, "联合拉新回"),
    LIVING_PLAN(20, "直播计划"),

    ;
    private final int value;
    private final String type;

    private static final Map<Integer, LevelTaskTypeEnum>
            VALUE_MAP = Arrays.stream(LevelTaskTypeEnum.values())
            .collect(Collectors.toMap(LevelTaskTypeEnum::getValue, Function.identity()));

    public static LevelTaskTypeEnum fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private static final Map<String, LevelTaskTypeEnum>
            TYPE_MAP = Arrays.stream(LevelTaskTypeEnum.values())
            .collect(Collectors.toMap(LevelTaskTypeEnum::getType, Function.identity()));

    public static LevelTaskTypeEnum fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN);
    }

    LevelTaskTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
