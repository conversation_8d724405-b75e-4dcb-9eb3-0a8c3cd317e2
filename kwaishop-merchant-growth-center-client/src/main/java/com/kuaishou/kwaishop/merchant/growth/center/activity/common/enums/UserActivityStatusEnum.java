package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <zhu<PERSON><PERSON>@kuaishou.com>
 * Created on 2021-12-07
 */
public enum UserActivityStatusEnum {
    UNKNOWN(0, "未知"),
    UNQUALIFIED(5, "没资格"),
    DRAWING(10, "待领取"),
    PROCESSING(20, "进行中"),
    AUDITING(30, "审核中"),
    SUCCESS(40, "成功"),
    FAIL(50, "失败"),
    RISK(51, "风控"),
    STOP(60, "终止"),
    ;
    private final int value;
    private final String type;

    public static UserActivityStatusEnum of(int value) {
        for (UserActivityStatusEnum val : UserActivityStatusEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    UserActivityStatusEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
