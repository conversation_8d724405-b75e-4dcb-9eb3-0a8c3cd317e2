package com.kuaishou.kwaishop.merchant.growth.center.client.common.kconf;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-07-24
 */
public enum MerchantGrowthStringClientConfigKey implements KconfClientSupplier<String> {
    /**
     * 粉丝团专属品活动名称
     */
    fansGroupActivityName("粉丝团专属品活动奖励"),
    ;

    private String defaultValue;

    MerchantGrowthStringClientConfigKey() {
    }

    MerchantGrowthStringClientConfigKey(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String configKey() {
        return name();
    }

    private static final String DEFAULT_BOOL_VALUE = "";

    @Override
    public String defaultValue() {
        return DEFAULT_BOOL_VALUE;
    }

    @Override
    public Kconf<String> getKConf() {
        return Kconfs.ofString(getConfigKey(), defaultValue).build();
    }
}
