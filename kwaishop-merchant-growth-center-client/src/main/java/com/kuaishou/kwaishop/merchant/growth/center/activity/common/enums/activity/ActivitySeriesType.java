package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-02
 */
public enum ActivitySeriesType {
    ACTIVITY_SERIES_TYPE_UNKNOWN(0, "unknown"),
    // 1 - 10000  为策略用
    STRATEGY_LIANHELAXIN(1, "联合拉新"),
    STRATEGY_PROMOTE_BROADCAST(2, "促开播"),

    // 10000 以上为任务使用
    ACTIVITY_FLOW_BOOST(10001, "商家等级权益流量助推系列活动"),
    ACTIVITY_YEAR_GOODS(10002, "大促系列活动"),
    ACTIVITY_SHOP_NECESSARY(10003, "开店必做系列活动"),
    ACTIVITY_GMV_PLUS(10004, "GMV加码福利"),
    ACTIVITY_LESS_50W(10005, "50w-任务"),
    ACTIVITY_MORE_50W(10006, "50w+任务"),
    ACTIVITY_REALTIME_INVEST(10007, "实时对投任务"),
    ACTIVITY_INDUSTRY(10008, "行业招商任务"),
    ACTIVITY_SNOWBALL(10009, "雪球任务"),

    ACTIVITY_CUSTOM(10010, "后台自定义活动"),
    ACTIVITY_INDUSTRY_VERTICAL(10011, "行业纵向活动"),
    ACTIVITY_STRATEGY_ADMIN(10012, "策略运营后台活动"),
    ;
    private final int value;
    private final String type;

    private static final Map<Integer, ActivitySeriesType>
            VALUE_MAP = Arrays.stream(ActivitySeriesType.values())
            .collect(Collectors.toMap(ActivitySeriesType::getValue, Function.identity()));

    public static ActivitySeriesType fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, ACTIVITY_SERIES_TYPE_UNKNOWN);
    }

    private static final Map<String, ActivitySeriesType>
            TYPE_MAP = Arrays.stream(ActivitySeriesType.values())
            .collect(Collectors.toMap(ActivitySeriesType::getType, Function.identity()));

    public static ActivitySeriesType fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, ACTIVITY_SERIES_TYPE_UNKNOWN);
    }

    ActivitySeriesType(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public long getLongValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
