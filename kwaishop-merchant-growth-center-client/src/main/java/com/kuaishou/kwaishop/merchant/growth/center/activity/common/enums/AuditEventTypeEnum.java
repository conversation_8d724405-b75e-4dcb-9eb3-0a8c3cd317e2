package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-10
 */
public enum AuditEventTypeEnum {
    UNKNOWN(0, "未知"),
    WARNING_EVENT(1, "警告事件"),
    SPAM_EVENT(2, "风控终止事件"),
    APPROVE_EVENT(3, "审批通过事件"),
    REJECT_EVENT(4, "审批不通过事件"),
    RESTART_EVENT(5, "审批重置事件"),
    REISSUE_EVENT(6, "审批复核事件"),
    ;
    private final int value;
    private final String type;

    public static AuditEventTypeEnum of(int value) {
        for (AuditEventTypeEnum val : AuditEventTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    AuditEventTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
