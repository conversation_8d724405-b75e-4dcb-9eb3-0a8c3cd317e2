package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-23
 */
public enum LiteActivityStatusEnum {

    UNKNOWN(0, "未知"),
    ONLINE(30, "极速版已上线"),
    ADD_DRAFT(40, "补充玩法草稿态"),
    ADD_AUDIT(41, "补充玩法待审批"),
    ADD_AUDIT_ING(42, "补充玩法审批中"),
    ADD_AUDIT_PASS(43, "补充玩法审批通过"),
    ADD_AUDIT_FAILED(44, "补充玩法审批失败"),
    ADD_ONLINE_ING(45, "补充活动玩法上线中"),
    ADD_ONLINE(46, "补充玩法已上线"),;

    private int code;
    private String desc;

    public static LiteActivityStatusEnum of(int code) {
        for (LiteActivityStatusEnum statusEnum : LiteActivityStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return UNKNOWN;
    }

    LiteActivityStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
