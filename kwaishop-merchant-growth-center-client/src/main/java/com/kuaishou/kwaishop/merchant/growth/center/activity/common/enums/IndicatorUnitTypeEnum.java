package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-05
 */
public enum IndicatorUnitTypeEnum {
    UNKNOWN(0, "unknown"),
    AMOUNT(1, "金额"),
    DURATION(2, "时长"),
    OTHER(3, "其他"),
    NUMBER(4, "个数"),
    ;
    private final long value;
    private final String type;

    public static IndicatorUnitTypeEnum of(long value) {
        for (IndicatorUnitTypeEnum val : IndicatorUnitTypeEnum.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    IndicatorUnitTypeEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public long getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
