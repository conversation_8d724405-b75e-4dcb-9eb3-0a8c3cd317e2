package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-26
 */
public enum LotteryRecordStatusEnum {
    UNKNOWN(0, "unknown"),
    NOT_START(10, "未开始"),
    WIN_AWARD(20, "已中奖"),
    NO_AWARD(30, "未中奖"),
    ;
    private final int code;
    private final String type;

    public static LotteryRecordStatusEnum of(int code) {
        for (LotteryRecordStatusEnum val : LotteryRecordStatusEnum.values()) {
            if (val.getCode() == code) {
                return val;
            }
        }
        return UNKNOWN;
    }

    LotteryRecordStatusEnum(int code, String type) {
        this.code = code;
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
