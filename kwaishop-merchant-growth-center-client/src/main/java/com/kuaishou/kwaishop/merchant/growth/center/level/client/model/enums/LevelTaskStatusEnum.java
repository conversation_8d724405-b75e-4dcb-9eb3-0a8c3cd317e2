package com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
public enum LevelTaskStatusEnum {
    UNKNOWN(0, "unknown"),
    UNFINISHED(10, "未完成"),
    FINISHED(20, "已完成"),

    ;
    private final int value;
    private final String type;

    private static final Map<Integer, LevelTaskStatusEnum>
            VALUE_MAP = Arrays.stream(LevelTaskStatusEnum.values())
            .collect(Collectors.toMap(LevelTaskStatusEnum::getValue, Function.identity()));

    public static LevelTaskStatusEnum fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    private static final Map<String, LevelTaskStatusEnum>
            TYPE_MAP = Arrays.stream(LevelTaskStatusEnum.values())
            .collect(Collectors.toMap(LevelTaskStatusEnum::getType, Function.identity()));

    public static LevelTaskStatusEnum fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN);
    }

    LevelTaskStatusEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
