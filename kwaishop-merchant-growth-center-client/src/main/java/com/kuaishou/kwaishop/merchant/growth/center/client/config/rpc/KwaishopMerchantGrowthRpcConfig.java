package com.kuaishou.kwaishop.merchant.growth.center.client.config.rpc;

import javax.annotation.Nonnull;

import com.kuaishou.framework.rpc.config.RpcConfig;
import com.kuaishou.infra.grpc.constant.RpcStubHolder;

import kuaishou.common.BizDef;

/**
 * <AUTHOR> <<EMAIL>>
 */

public enum KwaishopMerchantGrowthRpcConfig implements RpcConfig {

    // TODO: 检查服务注册后缀，service 还是 centerBaseExceptionHandler
    all("kwaishop-merchant-growth-center"), // 全部gRPC聚合部署config
    ;

    private final Class<? extends RpcStubHolder> grpcClass;

    private final KwaishopMerchantGrowthRpcConfig comboConfig;

    private String registerName;

    KwaishopMerchantGrowthRpcConfig(String registerName) {
        this.grpcClass = null;
        this.registerName = registerName;
        this.comboConfig = null;
    }

    KwaishopMerchantGrowthRpcConfig(Class<? extends RpcStubHolder> grpcClass,
            KwaishopMerchantGrowthRpcConfig comboConfig) {
        this.grpcClass = grpcClass;
        this.comboConfig = comboConfig;
    }

    @Override
    public Class<? extends RpcStubHolder> grpcClass() {
        return grpcClass;
    }

    @Nonnull
    @Override
    public String bizNameForRegistry() {
        return all.registerName;
    }

    @Nonnull
    @Override
    public String bizName() {
        return all.registerName;
    }

    @Override
    public boolean disableAutoGrpcPrefix() {
        return true;
    }

    @Override
    public int port() {
        return 0;
    }

    @Override
    public boolean supportDebug() {
        return true;
    }

    @Nonnull
    @Override
    public BizDef bizDef() {
        return BizDef.KWAISHOP_SELLER_CENTER;
    }

}
