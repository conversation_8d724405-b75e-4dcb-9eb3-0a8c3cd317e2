package com.kuaishou.kwaishop.merchant.growth.center.client.config.mq;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopMerchantGrowthMqTopic {
    /**
     * 商家成长奖励审批到风控信息
     */
    kwaishop_merchant_growth_award_audit_to_risk_msg,

    /**
     * 商家成长活动事件信息
     */
    kwaishop_merchant_growth_activity_event_msg,

    /**
     * 文件导出
     */
    kwaishop_merchant_growth_file_export,

    /**
     * 粉丝团奖励发放重试补偿
     */
    kwaishop_merchant_growth_fansgroup_award_compensate_mq,

    /**
     * 用户策略记录状态变更
     */
    kwaishop_merchant_growth_user_strategy_record_status_change,

    /**
     * 用户活动指标事件消息
     */
    kwaishop_growth_user_indicator_event_msg,

    /**
     * 用户离线指标更新通用消息
     */
    kwaishop_growth_user_indicator_update_common_msg,

    /**
     * 用户活动奖励事件消息
     */
    kwaishop_growth_user_award_event_msg,

    /**
     * 用户活动审核事件消息
     */
    kwaishop_growth_user_audit_event_msg,

    /**
     * 用户任务事件消息
     */
    kwaishop_growth_user_task_event_msg,
    /**
     * Hive导入校验消息
     */
    kwaishop_merchant_growth_hive_import_check_msg,
    /**
     * 发奖回调消息topic
     */
    kwaishop_merchant_operation_send_award_result_notify_mq,
    /**
     * 向横向发奖消息通知
     */
    kwaishop_merchant_operation_send_award_mq,

    /**
     * 招商实体审核消息
     */
    kwaishop_activity_investment_report_outer_info,

    /**
     * 用户活动事件消息
     */
    kwaishop_growth_user_activity_event_msg,

    /**
     * 用户报名记录状态变更
     */
    merchant_growth_user_registration_record_status_change,

    /**
     * 等级消息
     */
    merchant_level_record_topic,

    /**
     * 权益发放状态消息
     */
    kwaishop_merchant_interest_async_send_result,

    /**
     * 直播预告消息topic
     */
    kwaishop_flow_reservation_change,

    /**
     * 直播预告更新消息topic
     */
    kwaishop_flow_reservation_update,

    /**
     * 回头客说消息topic
     */
    merchant_comment_regular_seller_replied_event,

    /**
     * 选品排行消息topic
     */
    delivery_horse_race_result_data,

    /**
     * 直播课预约消息topic
     */
    kwaishop_education_university_live_course_apply_message,

    /**
     * 优惠券事件消息topic
     */
    merchant_coupon_seller_out_event,

    /**
     * 优惠券红包消息topic
     */
    kwaishop_marketing_tools_activity_status_change,

    /**
     * 生意通消息topic
     */
    syt_page_show,

    /**
     * 课程完结消息topic
     */
    kwaishop_education_university_course_finish_message,

    /**
     * 课程完结消息topicV2
     */
    education_course_article_finish_message,

    /**
     * 用户消息推送 - 延迟消息
     */
    kwaishop_merchant_growth_delay_notification_push,

    /**
     * 用户消息推送 - 完成消息
     */
    kwaishop_merchant_growth_finish_notification_push,

    /**
     * 策略消息通知
     */
    kuaishop_merchant_growth_strategy_event_msg,
    /**
     * 延迟指标更新消息topic
     */
    kwaishop_merchant_growth_delay_update_indicator,

    /**
     * 商家成长任务中心监听分销计划变更事件
     */
    kwaishop_distribute_plan_change_event,

    /**
     * 上架商品指标消息
     */
    merchant_commodity_shelf_show_status,

    /**
     * 新商品上架消息
     */
    shelf_product_show_status_change,

    /**
     * 真实性认证通过消息
     */
    kwaishop_subject_authentication_result_mq,

    /**
     * 店铺标签变更消息
     */
    shop_center_tag_change,

    /**
     * 下载商家app指标消息
     */
    kwaishop_shop_workbench_behavior,

    /**
     * 保证金指标消息
     */
    deposit_not_enough,

    /**
     * 电商大学课程指标消息
     */
    kwaishop_university_camp_student_message,

    /**
     * 奖励风控回调消息
     */
    risk_eb_industry_activity_reward_check,

    /**
     * 横向活动-活动状态变更事件
     */
    kwaishop_merchant_operation_resource_activity_event_mq,

    /**
     * 横向活动-发奖确认事件
     */
    kwaishop_merchant_operation_resource_award_event_mq,

    /**
     * 自定义基期异步校验基期均值事件
     */
    kwaishop_merchant_growth_activity_base_custom_check,

    /**
     * 手动导入基期或报名MQ事件
     */
    kwaishop_merchant_growth_manual_draw_sign_up_activity,

    /**
     * 上线单用户处理MQ事件
     */
    kwaishop_merchant_growth_online_single_user_handle_mq,

    /**
     * 批量用户处理MQ事件
     */
    kwaishop_merchant_growth_batch_user_execute_mq,

    /**
     * roi计算触发事件
     */
    kwaishop_merchant_resource_roi_trigger_event_msg,

    /**
     * 活动追加用户处理MQ事件
     */
    kwaishop_merchant_growth_activity_add_crowd_handle_mq,

    /**
     * 活动追加用户处理MQ事件
     */
    kwaishop_merchant_growth_activity_single_add_crowd_handle_mq,
    /**
     * 存量刷招商事件
     */
    kwaishop_merchant_growth_activity_flush_investment,

    /**
     * ab 上报
     */
    kwaishop_merchant_growth_activity_report_seller_ab,

    /**
     * 上线结果完成统计MQ事件
     */
    kwaishop_merchant_growth_online_check_finish_mq,

    /**
     * 触达配置创建消息
     */
    kwaishop_merchant_growth_notification_create_mq,

    /**
     * 用户抽奖消息
     */
    kwaishop_growth_user_lottery_registration_event_msg,
    /**
     * 用户抽奖状态变更消息
     */
    kwaishop_growth_user_lottery_award_event_msg,

    /**
     * ROI单商家计算数据
     */
    kwaishop_merchant_resource_seller_roi_data_msg,

    /**
     * 店铺变更事件
     */
    shop_changed_notify,

    /**
     * 非B补活动审批topic
     */
    kwaishop_merchant_growth_activity_kwaiflow_audit_kafka_msg,

    /**
     * 触达配置审批topic
     */
    kwaishop_merchant_growth_notification_kwaiflow_audit_kafka_msg,

    /**
     * 店铺下标事件
     */
    kwaishop_shop_clear_tag_to_subject,
    /**
     * 用户统计初始化事件
     */
    kwaishop_merchant_growth_user_statistics_init_msg,

    /**
     * 用户活动期表现离线消息
     */
    kwaishop_merchant_growth_user_statistics_offline_performance_msg,

    /**
     * 用户活动期表现离线消息
     */
    kwaishop_merchant_growth_history_user_statistics_offline_performance_msg,

    /**
     * 用户统计变动消息
     */
    kwaishop_merchant_growth_user_statistics_change_sync_msg,

    /**
     * 活动统计完成消息
     */
    kwaishop_merchant_growth_activity_statistics_finish_msg,

    /**
     * 活动统计复盘数据消息（对外）
     */
    kwaishop_merchant_growth_activity_statistics_review_msg,

    /**
     * 用户资格事件
     */
    kwaishop_growth_user_registration_event_msg,
    /**
     * ROI结果消息
     */
    kwaishop_merchant_operation_roi_seller_result_mq,
    /**
     * 货架商品上下架消息
     */
    kwaishop_shelf_on_offline_message,
    /**
     * 活动商家归属信息变更消息
     */
    activity_statistics_es_user_belong_info_change_topic,

    /**
     * hive回流任务完成topic
     */
    merchant_hive_sync_task_done_topic,
    /**
     * 风控二次风控topic
     */
    rc_eb_activity_seller_interrupt_result,
    /**
     * 风控二次风控消费组
     */
    rc_eb_activity_seller_interrupt_result_strategy,
    /*
     * 异步链路巡检事件topic
     */
    async_link_inspect_event_topic,

    /**
     * DAP测算结果返回topic
     */
    dp_strategy_calculate_result,

    /**
     * DAP测算结果返回topic for prt
     */
    dp_strategy_calculate_result_prt,
    /**
     * DAP测算结果消费组
     */
    dap_estimation_callback_task_consume_group,

    /**
     * 批量测算结果单个策略处理topic
     */
    batch_estimation_result_transfer_single_strategy_topic,

    /**
     * 批量测算结果单个策略处理消费组
     */
    c_batch_estimation_result_transfer_single_strategy_topic_group,
    /**
     * 策略实验室-策略测算完成消息topic
     */
    strategy_estimation_finish_topic,
    /**
     * 策略实验室-策略测算完成消息消费组
     */
    c_strategy_estimation_finish_topic_group,
    /**
     * 策略实验室-人群包动态追加测算处理topic
     */
    estimation_crowd_dynamic_append_topic,
    /**
     * 策略实验室-人群包动态追加测算处理消费组
     */
    c_estimation_crowd_dynamic_append_topic_group,

    /**
     * 资格option变更消息
     */
    kwaishop_merchant_growth_registration_option_change_topic,
    /**
     * 预算管控活动事件topic
     */
    kwaishop_merchant_growth_budget_limit_activity_event,

    /**
     * 奖励追缴topic
     */
    kwaishop_merchant_growth_user_award_recovery_topic,

    /**
     * 奖励追缴topic(横向)
     */
    kwaishop_merchant_interest_user_award_recovery_topic,

    /**
     * cdp人群追加
     */
    cdp_crowd_event,

    /**
     * 人群diff重试消费组
     */
    kwaishop_merchant_growth_add_crowd_retry_topic,
    /**
     * 投放配置变更
     */
    kwaishop_merchant_growth_launch_config_change_topic,
    /**
     * 投放审批消息
     */
    kwaishop_merchant_growth_launch_config_audit_topic,
    /**
     * 专属小二企微建联消息
     */
    kwaishop_operation_wecom_seller_connect_event_topic,
    /**
     * 平台号建联消息
     */
    kwaishop_wecom_user_external_relationship_event_topic,
    /**
     * 活动变动内部通知topic
     */
    merchant_growth_activity_change_inner_notify_topic,
    /**
     * 执行器执行handler
     */
    kwaishop_merchant_growth_handler_execute_msg,
    /**
     * 活动变动内部通知topic消费组
     */
    c_merchant_growth_activity_change_inner_notify_topic_group,

    /**
     * plan用户动作变更数据消息
     */
    kwaishop_merchant_growth_plan_user_action_change_msg,

    /**
     * plan用户动作变更数据消息消费组
     */
    c_kwaishop_merchant_growth_plan_user_action_change_msg,

    /**
     * plan复盘数据消息
     */
    kwaishop_merchant_growth_plan_statistics_msg,
    /**
     * plan复盘数据消息消费组
     */
    c_kwaishop_merchant_growth_plan_statistics_msg_group,

    /**
     * 用户行为效果收集统计数据消息
     */
    user_action_effect_collect_statistic_change_topic,

    /**
     * 用户行为效果收集统计数据消息消费组
     */
    user_action_effect_collect_statistic_change_group,

    /**
     * 用户行为效果收集统计数据消息
     */
    user_action_target_finish_statistic_topic,
    /**
     * 用户行为效果收集统计数据消息消费组
     */
    user_action_target_finish_statistic_group,
    /**
     * 策略企划执行任务topic
     */
    kwaishop_merchant_growth_operation_plan_execute_task,

    /**
     * 策略企划用户执行任务topic
     */
    kwaishop_merchant_growth_operation_plan_user_execute_task,

    /**
     * 策略企划企微群发渠道消息延迟推送
     */
    kwaishop_merchant_growth_vx_private_channel_delay_push,

    /**
     * 用户动作记录更新
     */
    kwaishop_merchant_growth_user_action_record_update,

    /**
     * 策略活动状态变更消息
     */
    kwaishop_merchant_growth_activity_status_change_event_msg,

    /**
     * 活动审批通过绑定信息到测算域消费组
     */
    c_kwaishop_merchant_growth_activity_status_change_event_msg,

    /**
     * 策略组后续周期测算完成子策略触发追加人群topic
     */
    group_next_period_estimation_finish_strategy_append_topic,
    /**
     * 策略组后续周期测算完成子策略触发追加人群消费组
     */
    c_group_next_period_estimation_finish_strategy_append_topic,

    /**
     * 投放审批topic
     */
    kwaishop_merchant_growth_launch_config_kwaiflow_audit,
    /**
     * 达人入驻消息topic
     */
    daren_changed_notify,
    /**
     * 实时测算监听达人入驻消息消费组
     */
    c_daren_changed_notify_estimation_realtime_task,
    /**
     * 实时测算消费 topic
     */
    kwaishop_merchant_growth_estimation_realtime_topic,

    /**
     * 实时测算消费 topic消费组
     */
    c_kwaishop_merchant_growth_estimation_realtime_topic,

    /**
     * 周期版本测算人群固化消息
     */
    kwaishop_merchant_growth_estimation_crowd_fixed_topic,
    /**
     * 周期版本测算人群固化消息消费组
     */
    c_kwaishop_merchant_growth_estimation_crowd_fixed_topic_group,

    /**
     * 风控奖励复核消息
     */
    risk_eb_antispamforb_reward_reissue_detail,

    /**
     * 订单领域事件
     */
    kwaishop_order_domain_event,

    /**
     * 运费险签约活动消息topic
     */
    insurance_center_sign_activity_event_async_topic,
    /**
     * 店铺状态变更事件
     */
    xiaowei_business_status_change_topic,

    /**
     * 任务投放用户实验入组上报消息
     */
    kwaishop_merchant_growth_launch_user_exp_bucket_submit_topic,

    /**
     * 任务投放物料异步生成消息
     */
    kwaishop_merchant_growth_launch_content_gen_async_topic,

    /**
     * 任务实验分组上报
     */
    kwaishop_merchant_growth_exp_bucket_assign_report_topic,

    /**
     * llm物料生成消息
     */
    kwaishop_merchant_growth_llm_content_gen_async_topic,

    /**
     * designAI回调消息
     */
    design_ai_common,

    /**
     * 任务实验分组同步Hive
     */
    kwaishop_merchant_growth_exp_assign_sync_hive,

    /**
     * 新商关店重开备店任务领取延迟消息topic
     */
    kwaishop_merchant_new_seller_reopen_task_draw_delay_topic,
}
