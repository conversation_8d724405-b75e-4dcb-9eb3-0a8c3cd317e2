package com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-08-22
 */
public enum ActivityStatusEnum {

    UNKNOWN(0, "未知"),
    EFFECTIVE(1, "生效中(兼容老的状态码)"),
    EDIT(10, "编辑中"),
    DRAFT(11, "草稿"),
    DISCARD(12, "废弃"),

    AUDIT(20, "待审批"),
    AUDIT_ING(21, "审批中"),
    AUDIT_PASS(22, "审批通过"),
    AUDIT_FAILED(23, "审批失败"),

    ONLINE_PROCESS(29, "上线中"),
    ONLINE(30, "已上线"),
    // 仅逻辑
    ONLINE_UN_START(31, "已上线且未到达活动开始时间"),
    ONLINE_STARTING(32, "已上线且在活动时间中"),
    ONLINE_END(33, "已上线且到达结束时间"),
    ;

    private final int code;
    private final String desc;

    ActivityStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityStatusEnum of(int value) {
        for (ActivityStatusEnum val : ActivityStatusEnum.values()) {
            if (val.getCode() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
