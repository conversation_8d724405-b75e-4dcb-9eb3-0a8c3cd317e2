package com.kuaishou.kwaishop.merchant.growth.center.level.client.model.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-06-14
 */
public enum LevelEnum {

    UNKNOWN(0, 0, "暂无定级", "暂无定级"),

    NEW_SELLER(1, 1, "新手商家", "新手商家"),
    L0(2, 2, "L0", "一钻"),

    L1_LESS(11, 3, "L1-", "二钻"),
    L1_PLUS(12, 3, "L1+", "二钻"),

    L2_LESS(21, 4, "L2-", "三钻"),
    L2_PLUS(22, 4, "L2+", "三钻"),

    L3_LESS(31, 5, "L3-", "四钻"),
    L3_PLUS(32, 5, "L3+", "四钻"),

    L4_LESS(41, 6, "L4-", "五钻"),
    L4_PLUS(42, 6, "L4+", "五钻"),
    ;
    private final int value;

    private final int bigLevel;
    private final String type;

    private final String bigLevelName;

    private static final Map<Integer, LevelEnum> VALUE_MAP =
            Arrays.stream(LevelEnum.values()).collect(Collectors.toMap(LevelEnum::getValue, Function.identity()));

    public static LevelEnum fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, UNKNOWN);
    }

    public static LevelEnum fromStringValue(String value) {
        if (StringUtils.isBlank(value)) {
            return UNKNOWN;
        }
        return VALUE_MAP.getOrDefault(Integer.parseInt(value), UNKNOWN);
    }

    private static final Map<String, LevelEnum> TYPE_MAP =
            Arrays.stream(LevelEnum.values()).collect(Collectors.toMap(LevelEnum::getType, Function.identity()));

    public static LevelEnum fromValue(String type) {
        return TYPE_MAP.getOrDefault(type, UNKNOWN);
    }

    /**
     * 小于最高等级，获取下一等级枚举。若为最高等级则返回最高等级。
     */
    public static LevelEnum getNextLevel(int currentLevel) {
        int num = 0;
        List<LevelEnum> allLevels = Arrays.stream(LevelEnum.values())
                .filter(levelEnum -> levelEnum != NEW_SELLER && levelEnum != UNKNOWN)
                .sorted(Comparator.comparing(LevelEnum::getValue))
                .collect(Collectors.toList());
        for (LevelEnum levelEnum : allLevels) {
            if (levelEnum.getValue() == currentLevel) {
                break;
            }
            num++;
        }
        if (num < allLevels.size() - 1) {
            return allLevels.get(num + 1);
        }
        return allLevels.get(allLevels.size() - 1);
    }

    //降一级-3.0
    public static int getDegradeLevel(int level) {
        if (level <= LevelEnum.L0.getValue()) {
            return level;
        }
        List<Integer> levels =
                Arrays.stream(LevelEnum.values()).map(LevelEnum::getValue).sorted().collect(Collectors.toList());
        for (int i = 0; i < levels.size(); ++i) {
            if (levels.get(i) == level && i > 0) {
                return levels.get(i - 1);
            }
        }
        return level;
    }

    /**
     * 返回从指定最小的等级的所有等级列表
     */
    public static List<Integer> parseAllLevelEnumsFromMinLevel(int minLevel) {
        return VALUE_MAP.keySet().stream().filter(value -> value >= minLevel).sorted().collect(Collectors.toList());
    }

    /**
     * 返回指定范围的等级集合
     */
    public static List<LevelEnum> getLevelRangeCollections(LevelEnum minLevel, LevelEnum maxLevel) {
        List<LevelEnum> res = new ArrayList<>();
        for (LevelEnum level : LevelEnum.values()) {
            if (level.getValue() >= minLevel.getValue() && level.getValue() <= maxLevel.getValue()) {
                res.add(level);
            }
        }
        return res;
    }

    LevelEnum(int value, int bigLevel, String type, String bigLevelName) {
        this.value = value;
        this.bigLevel = bigLevel;
        this.type = type;
        this.bigLevelName = bigLevelName;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }

    public int getBigLevel() {
        return bigLevel;
    }

    public String getBigLevelName() {
        return bigLevelName;
    }
}
