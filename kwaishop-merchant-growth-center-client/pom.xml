<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>kwaishop-merchant-growth-center-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>kwaishop-merchant-growth-center-client</artifactId>

    <dependencies>
        <!-- 电商依赖 -->
        <!-- 基础架构依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-grpc</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-krpc-metadata</artifactId>
<!--            <version>1.0.127</version>-->
        </dependency>
        <!-- 快手依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-metadata</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-biz-def</artifactId>
<!--            <version>1.0.1649</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-client</artifactId>
        </dependency>
        <!-- 三方依赖 -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
<!--            <version>3.0.2</version>-->
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
<!--            <version>3.16.1</version>-->
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-api</artifactId>
<!--            <version>1.36.1</version>-->
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
<!--            <version>1.36.1</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
<!--            <version>3.10</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
<!--            <version>4.3</version>-->
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
<!--            <version>1.36.1</version>-->
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
<!--            <version>28.1-jre-kwai5</version>-->
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
<!--            <version>1.3.2</version>-->
        </dependency>

    </dependencies>

</project>