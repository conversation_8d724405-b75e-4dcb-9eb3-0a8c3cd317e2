package com.kuaishou.kwaishop.merchant.growth.center.level.app.kafka.group

import com.kuaishou.framework.kafka.ConsumerGroup
import com.kuaishou.framework.util.EnumUtils
import com.kuaishou.framework.util.HostEnvUtils
import com.kuaishou.infra.kws.tool.AvailableZones

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-06-14
 */

@Suppress("EnumEntryName")
enum class KwaishopMerchantLevelConsumerGroup(
    private var customName: String? = null,
    // {@code true}时，在group中自动增加启动机房所在后缀，这样每个机房就可以独立消费全量数据
    private var idcSuffix: Boolean = false,
    // 这个参数是配合--enable-idc-aware使用
    private var idcAware: Boolean = false,
    // 测试和线上消费分离
    private var hasTest: Boolean = true
) : ConsumerGroup {

    kwaishop_merchant_growth_seller_level_common_indicator_message_group,

    ;

    override fun getName(): String {
        var groupName = customName
        if (groupName == null) {
            groupName = name.toLowerCase()
        }
        if (hasTest && HostEnvUtils.debugHost()) groupName += "_test"

        return if (idcSuffix) groupName + "_" + AvailableZones.currentAz().name() else groupName
    }

    override fun enableIdcAware(): Boolean {
        return idcAware
    }

    companion object {
        init {
            EnumUtils.checkDuplicate(values()) { it.getName() }
        }
    }
}