<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kwaishop-merchant-growth-center-parent</artifactId>
        <groupId>kuaishou</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>kwaishop-merchant-growth-center-kit</artifactId>

    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-center-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- 电商依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-framework-common</artifactId>
<!--            <version>1.0.15</version>-->
        </dependency>
        <!-- 快手依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-scheduler-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-scheduler-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-short-link-sdk</artifactId>
<!--            <version>1.0.27</version>-->
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-jdbc</artifactId>
        </dependency>
        <!-- 基础架构依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-common</artifactId>
        </dependency>
        <!-- 三方依赖 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
<!--            <version>1.7.30-kwai-2</version>-->
        </dependency>
        <dependency>
            <groupId>com.ecyrd.speed4j</groupId>
            <artifactId>speed4j</artifactId>
<!--            <version>0.18</version>-->
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
<!--            <version>3.0.1u2</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
<!--            <version>3.10</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
<!--            <version>4.3</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
<!--            <version>28.1-jre-kwai5</version>-->
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
<!--            <version>1.18.20</version>-->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
<!--            <version>1.3.2</version>-->
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
<!--            <version>3.1.0</version>-->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax</groupId>
            <artifactId>javaee-api</artifactId>
            <version>8.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>