package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.model.bo;

import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.infra.scheduler.ShardParam;
import com.kuaishou.infra.scheduler.SimpleTaskInfo;
import com.kuaishou.infra.scheduler.client.TaskContext;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 任务的触发的上下文，相关任务参数默认为空
 */
@NoArgsConstructor
@AllArgsConstructor
public class TriggerTaskContext implements TaskContext {

    private String args;

    @Nullable
    @Override
    public String args() {
        return args;
    }

    @Override
    public int totalShard() {
        return 0;
    }

    @Override
    public List<Integer> shards() {
        return Lists.newArrayList();
    }

    @Override
    public long expectedStartTime() {
        return 0;
    }

    @Nullable
    @Override
    public SimpleTaskInfo taskInfo() {
        return SimpleTaskInfo.getDefaultInstance();
    }

    @Nullable
    @Override
    public String parentResultAsString() {
        return StringUtils.EMPTY;
    }

    @Nullable
    @Override
    public Map<ShardParam, String> parentResults() {
        return Maps.newHashMap();
    }

    @Nullable
    @Override
    public String swimLaneId() {
        return StringUtils.EMPTY;
    }
}