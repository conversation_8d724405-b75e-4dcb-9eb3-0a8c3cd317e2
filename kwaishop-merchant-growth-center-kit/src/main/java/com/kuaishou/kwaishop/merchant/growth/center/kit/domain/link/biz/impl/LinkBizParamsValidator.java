package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.biz.impl;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-03
 */
@Slf4j
public class LinkBizParamsValidator {


    public static void checkAddKitLinkParams(AddKitLinkRequest request) {
        if (StringUtils.isEmpty(request.getBiz())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "biz参数不合法");
        }
        if (StringUtils.isEmpty(request.getSource())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "source参数不合法");
        }
        if (StringUtils.isEmpty(request.getLongUrl())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "longUrl参数不合法");
        }
    }

    public static void checkUpdateKitLinkParams(UpdateKitLinkRequest request) {
        if (StringUtils.isEmpty(request.getBiz())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "biz参数不合法");
        }
        if (StringUtils.isEmpty(request.getSource())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "source参数不合法");
        }
        if (StringUtils.isEmpty(request.getLongUrl())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "longUrl参数不合法");
        }
        if (StringUtils.isEmpty(request.getCreator())) {
            log.error("[Link_SERVICE_ERROR][checkAddKitLinkParams-error][request:{}]",
                    ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(BasicErrorCode.PARAM_INVALID, "creator参数不合法");
        }
    }
}

