package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.dao;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.dao.mapper.LinkUrlMapper;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query.LinkUrlQueryCondition;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Repository
public class LinkUrlDAO extends BaseDAO<LinkUrlDO, LinkUrlQueryCondition> {

    @Autowired
    private LinkUrlMapper linkUrlMapper;


    @Override
    protected void fillQueryCondition(LinkUrlQueryCondition condition, QueryWrapper<LinkUrlDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getBiz())) {
            queryWrapper.and(q -> q.eq("biz", condition.getBiz()));
        }
        if (StringUtils.isNotBlank(condition.getSource())) {
            queryWrapper.and(q -> q.eq("source", condition.getSource()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.eq("creator", condition.getCreator()));
        }
    }

    public PageBO<LinkUrlDO> pageGetUrlList(LinkUrlQueryCondition condition) {

        return queryPageList(condition);
    }


    @Override
    protected BaseMapper<LinkUrlDO> getMapper() {
        return linkUrlMapper;
    }
}
