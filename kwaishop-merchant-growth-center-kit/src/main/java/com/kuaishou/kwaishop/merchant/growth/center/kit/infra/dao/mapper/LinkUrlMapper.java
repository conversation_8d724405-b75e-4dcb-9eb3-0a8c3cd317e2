package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.dao.mapper;

import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.constants.DataSourceConstants.LEVEL_SINGLE_DATA_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Mapper
@DataSourceRouting(LEVEL_SINGLE_DATA_SOURCE_NAME)
public interface LinkUrlMapper extends BaseMapper<LinkUrlDO> {
}
