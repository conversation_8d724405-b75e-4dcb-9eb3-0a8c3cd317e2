package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf;

import java.util.Collections;
import java.util.List;

import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-07
 */
public enum MerchantKitUrlStringListConfigKey implements KconfSupplier<List<String>> {

    /**
     * kit--短链接平台biz权限列表
     */
    kitShortUrlBizCodes(Collections.emptyList());

    private List<String> defaultValue;

    MerchantKitUrlStringListConfigKey() {

    }

    MerchantKitUrlStringListConfigKey(List<String> defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String configKey() {
        return name();
    }

    private static final List<String> DEFAULT_STRING_LIST_VALUE = Lists.newArrayList();

    @Override
    public List<String> defaultValue() {
        return DEFAULT_STRING_LIST_VALUE;
    }

    @Override
    public Kconf<List<String>> getKConf() {
        return Kconfs.ofStringList(getConfigKey(), defaultValue).build();
    }
}
