package com.kuaishou.kwaishop.merchant.growth.center.kit.app.rpc;


import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum.CREATE_DIALOG;
import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum.CREATE_DIALOG_TEMPLATE;
import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum.DELETE_DIALOG;
import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum.GET_DIALOG_INFO;

import java.util.ArrayList;

import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.SubSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.CreateDialogRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.CreateDialogResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.CreateDialogTemplateRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.CreateDialogTemplateResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.DeleteDialogRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.DeleteDialogResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.GetDialogInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.GetDialogInfoResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.KrpcKwaishopMerchantGrowthDialogServiceGrpc.KwaishopMerchantGrowthDialogServiceImplBaseV2;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.UpdateDialogTemplateRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.dialog.UpdateDialogTemplateResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-08-11
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class MerchantDialogApiServiceImpl extends KwaishopMerchantGrowthDialogServiceImplBaseV2 {


    /**
     * 查询弹窗列表
     */
    @Override
    public GetDialogInfoResponse getDialogInfo(GetDialogInfoRequest request) {
        log.info("[GROWTH_CENTER_DIALOG_INFO][request:{}]", ObjectMapperUtils.toJSON(request));
        PerfUtil.perfSuccess(GET_DIALOG_INFO, SubSceneEnum.fromValue(request.getSubScene()).getType());
        return GetDialogInfoResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setData(ObjectMapperUtils.toJSON(new ArrayList<>()))
                .build();
    }

    /**
     * 创建弹窗
     */
    @Override
    public CreateDialogResponse createDialog(CreateDialogRequest request) {
        log.info("[GROWTH_CENTER_DIALOG_INFO][createDialog][request:{}]", ObjectMapperUtils.toJSON(request));
        PerfUtil.perfSuccess(CREATE_DIALOG);
        return CreateDialogResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .build();
    }

    /**
     * 删除用户弹窗
     */
    @Override
    public DeleteDialogResponse deleteDialog(DeleteDialogRequest request) {
        log.info("[GROWTH_CENTER_DIALOG_INFO][deleteDialog][request:{}]", ObjectMapperUtils.toJSON(request));
        PerfUtil.perfSuccess(DELETE_DIALOG);
        return DeleteDialogResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
    }

    @Override
    public CreateDialogTemplateResponse createDialogTemplate(CreateDialogTemplateRequest request) {
        log.info("[GROWTH_CENTER_DIALOG_INFO][createDialogTemplate][request:{}]",
                ObjectMapperUtils.toJSON(request));
        PerfUtil.perfSuccess(CREATE_DIALOG);
        return CreateDialogTemplateResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
    }

    @Override
    public UpdateDialogTemplateResponse updateDialogTemplate(UpdateDialogTemplateRequest request) {
        log.info("[GROWTH_CENTER_DIALOG_INFO][updateDialogTemplate][request:{}]",
                ObjectMapperUtils.toJSON(request));
        PerfUtil.perfSuccess(CREATE_DIALOG_TEMPLATE);
        return UpdateDialogTemplateResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
    }

}
