package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums;

import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfSubtag;

import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
public enum KitPerfEnum implements PerfSubtag {

    DEPRECATED("deprecated", "deprecated"),

    TEST_TRIGGER_TASK("test.trigger.task", "触发任务"),

    GET_DIALOG_INFO("dialog.get", "弹窗查询"),

    CREATE_DIALOG("dialog.create", "创建弹窗"),

    DELETE_DIALOG("dialog.delete", "删除弹窗"),

    CREATE_DIALOG_TEMPLATE("dialog.template.create", "创建模板"),

    UPDATE_DIALOG_TEMPLATE("dialog.template.update", "更新模板"),

    GET_SHORT_URL("kit.get.shortUrl", "获取短链接"),
    QUERY_SHORT_URL("kit.query.shortUrl", "查询短链接"),
    ADD_SHORT_URL("kit.add.shorturl", "添加短链接"),
    DELETE_SHORT_URL("kit.delete.shorturl", "删除短链接"),
    UPDATE_SHORT_URL("kit.update.shorturl", "更新短链接"),
    GET_BIZ_LIST("kit.get.bizlist", "获取Biz配置列表");


    private String scene;

    private String desc;

    KitPerfEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }


    @Override
    public String getScene() {
        return this.scene;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
