package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-23
 */
public enum MerchantKitUrlStringConfigKey implements KconfSupplier<String> {
    kitShortUrlDomain();;

    private String defaultValue;

    MerchantKitUrlStringConfigKey() {

    }

    MerchantKitUrlStringConfigKey(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    @Override
    public String configKey() {
        return name();
    }

    private static final String DEFAULT_BOOL_VALUE = "";

    @Override
    public String defaultValue() {
        return DEFAULT_BOOL_VALUE;
    }

    @Override
    public Kconf<String> getKConf() {
        return Kconfs.ofString(getConfigKey(), defaultValue).build();
    }
}
