package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SpringBeanUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    public static Object getBean(String beanName) {
        try {
            return applicationContext.getBean(beanName);
        } catch (Exception e) {
            log.error("[SpringBeanUtils] Get bean failed, bean name:{}, error msg:", beanName);
        }
        return null;
    }

    public static <T> T getBean(String beanName, Class<T> clz) {
        try {
            return applicationContext.getBean(beanName, clz);
        } catch (Exception e) {
            log.error("[SpringBeanUtils] Get bean failed, bean name:{}, clz is:{}, error msg:",
                    beanName, clz.getSimpleName());
        }
        return null;
    }

    public static <T> T getBean(Class<T> clz) {
        try {
            return applicationContext.getBean(clz);
        } catch (Exception e) {
            log.error("[SpringBeanUtils] Get bean failed, clz is:{}, error msg:", clz.getSimpleName());
        }
        return null;
    }

}
