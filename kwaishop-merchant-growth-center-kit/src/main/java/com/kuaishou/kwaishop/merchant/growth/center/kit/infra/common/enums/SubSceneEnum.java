package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-08-20
 */
public enum SubSceneEnum {
    SUB_SCENE_UNKNOWN(0, "unknown"),
    SUB_SCENE_WORKBENCH(1, "workbench"),
    SUB_SCENE_DETAIL_PAGE(2, "task_detail_page"),
    ;
    private final int value;
    private final String type;

    private static final Map<Integer, SubSceneEnum>
            VALUE_MAP = Arrays.stream(SubSceneEnum.values())
            .collect(Collectors.toMap(SubSceneEnum::getValue, Function.identity()));

    public static SubSceneEnum fromValue(int value) {
        return VALUE_MAP.getOrDefault(value, SUB_SCENE_UNKNOWN);
    }

    SubSceneEnum(int value, String type) {
        this.value = value;
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public String getType() {
        return type;
    }
}
