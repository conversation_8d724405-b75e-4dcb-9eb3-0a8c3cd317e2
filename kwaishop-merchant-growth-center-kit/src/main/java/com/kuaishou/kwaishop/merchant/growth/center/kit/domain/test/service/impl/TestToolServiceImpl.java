package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.service.impl;


import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf.MerchantGrowthObjectConfigKey.testToolTriggerTaskConfig;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.infra.scheduler.client.Task;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.model.bo.TriggerTaskConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.model.bo.TriggerTaskContext;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.service.TestToolService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.utils.SpringBeanUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TestToolServiceImpl implements TestToolService {

    @Override
    public void triggerTask(String taskName, String taskArgs, String ak) {
        log.info("[Test] Receive trigger task request, param is:{}", Lists.newArrayList(taskName, taskArgs, ak));
        if (StringUtils.isAnyBlank(taskName)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "任务名不能为空!");
        }
        TriggerTaskConfigBO triggerTaskConfig = testToolTriggerTaskConfig.getObject();
        if (!StringUtils.equals(ak, triggerTaskConfig.getAk())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "无效的AK!");
        }
        if (CollectionUtils.isEmpty(triggerTaskConfig.getAllowedTasks())
                || !triggerTaskConfig.getAllowedTasks().contains(taskName)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "该任务不支持直接触发!");
        }
        Task task = SpringBeanUtils.getBean(taskName, Task.class);
        if (task == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "不存在该任务!");
        }
        task.execute(new TriggerTaskContext(taskArgs));
    }
}
