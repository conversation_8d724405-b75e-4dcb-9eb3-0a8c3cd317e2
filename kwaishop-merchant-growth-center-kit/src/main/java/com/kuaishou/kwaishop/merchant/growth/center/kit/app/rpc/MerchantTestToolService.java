package com.kuaishou.kwaishop.merchant.growth.center.kit.app.rpc;

import java.net.InetAddress;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.service.TestToolService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.utils.ResponseUtil;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.test.KrpcTestToolServiceGrpc.TestToolServiceImplBaseV2;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.test.TaskTriggerDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.test.TaskTriggerRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.test.TaskTriggerResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 测试工具服务
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class MerchantTestToolService extends TestToolServiceImplBaseV2 {

    @Autowired
    private TestToolService testToolService;

    @Override
    public TaskTriggerResponse taskTrigger(TaskTriggerRequest request) {
        StopWatch sw = new StopWatch();
        try {
            testToolService.triggerTask(request.getTaskName(), request.getTaskArgs(), request.getAk());
            PerfUtil.perfSuccessWithWatch(KitPerfEnum.TEST_TRIGGER_TASK, request.getTaskName(), sw);
            return TaskTriggerResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(buildTaskTriggerResult(sw.getTimeMicros()))
                    .build();
        } catch (Exception e) {
            log.error("[Test] Task trigger failed, request is:{}, error msg:", ObjectMapperUtils.toJSON(request), e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.TEST_TRIGGER_TASK, request.getTaskName(), sw);
            return TaskTriggerResponse.newBuilder()
                    .setResult(ResponseUtil.parseCode(e))
                    .setErrorMsg(ResponseUtil.parseMessage(e))
                    .build();
        }
    }

    private TaskTriggerDTO buildTaskTriggerResult(long costMs) {
        String ip = StringUtils.EMPTY;
        try {
            ip = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.warn("[Test] Build task trigger result get local ip failed, error msg:", e);
        }
        return TaskTriggerDTO.newBuilder()
                .setCostMs(costMs)
                .setIp(ip)
                .build();
    }
}
