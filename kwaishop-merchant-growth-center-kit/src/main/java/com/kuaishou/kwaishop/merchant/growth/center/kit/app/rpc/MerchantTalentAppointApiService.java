package com.kuaishou.kwaishop.merchant.growth.center.kit.app.rpc;


import static com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum.DEPRECATED;

import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.JoinTalentAppointRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.JoinTalentAppointResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.KrpcKwaishopMerchantGrowthTalentAppointApiServiceGrpc.KwaishopMerchantGrowthTalentAppointApiServiceImplBaseV2;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.RemoveTalentRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.RemoveTalentRecordResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.TalentAnnualInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.talent.TalentAnnualInfoResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 达人公约服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class MerchantTalentAppointApiService extends KwaishopMerchantGrowthTalentAppointApiServiceImplBaseV2 {


    @Override
    public TalentAnnualInfoResponse queryAnnualInfo(TalentAnnualInfoRequest request) {
        PerfUtil.perfOther(DEPRECATED, "MerchantTalentAppointApiService.queryAnnualInfo");
        return TalentAnnualInfoResponse.newBuilder()
                .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                .setErrorMsg("活动已经结束")
                .setData("{}")
                .build();

    }

    @Override
    public JoinTalentAppointResponse joinTalentAppoint(JoinTalentAppointRequest request) {
        PerfUtil.perfOther(DEPRECATED, "MerchantTalentAppointApiService.joinTalentAppoint");
        PerfUtil.perfOther(DEPRECATED, "MerchantTalentAppointApiService.queryAnnualInfo");
        return JoinTalentAppointResponse.newBuilder()
                .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                .setErrorMsg("活动已经结束")
                .setData("{}")
                .build();
    }

    @Override
    public RemoveTalentRecordResponse removeTalentRecord(RemoveTalentRecordRequest request) {
        PerfUtil.perfOther(DEPRECATED, "MerchantTalentAppointApiService.removeTalentRecord");
        return RemoveTalentRecordResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .build();

    }

}
