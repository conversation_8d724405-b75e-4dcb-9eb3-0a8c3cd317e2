package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos;

import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@TableName("slink_url")
public class LinkUrlDO extends BaseDO {
    /*
    业务类型
     */
    private String biz;
    /*
    原始链接
     */
    private String longUrl;
    /*
    生成的短链接
     */
    private String shortUrl;
    /*
    来源表示
     */
    private String source;
    /*
    备注
     */
    private String note;
    /*
    创建人
     */
    private String creator;


}
