package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.converter;

import java.util.Map;

import org.springframework.beans.BeanUtils;

import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf.MerchantKitUrlJsonConfigKey;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf.MerchantKitUrlStringConfigKey;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.exception.KitErrorCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.untils.UrlUtils;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.LinkUrlsDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-03
 */
public class UrlRequestConverter {

    public static LinkUrlDO parseFromAddKitLinkNotExistSourceRequest(AddKitLinkRequest request, String url) {
        return LinkUrlDO.builder()
                .biz(request.getBiz())
                .source(request.getSource())
                .note(request.getNote())
                .creator(request.getCreator())
                .longUrl(request.getLongUrl())
                .shortUrl(url)
                .build();

    }

    public static LinkUrlDO parseFromAddKitLinkExistSourceRequest(AddKitLinkRequest request, String url) {
        return LinkUrlDO.builder()
                .biz(request.getBiz())
                .source(UrlUtils.findSource(request.getLongUrl()))
                .note(request.getNote())
                .creator(request.getCreator())
                .longUrl(request.getLongUrl())
                .shortUrl(url)
                .build();
    }

    public static LinkUrlDO parseFromUpdateLinkRequest(UpdateKitLinkRequest request) {
        return LinkUrlDO.builder()
                .id(request.getId())
                .note(request.getNote())
                .creator(request.getCreator())
                .build();
    }


    public static LinkUrlsDTO linkUrlDO2DTO(LinkUrlDO linkUrlDO) {

        BeanUtils.copyProperties(linkUrlDO, LinkUrlsDTO.newBuilder());
        Map<String, String> map = MerchantKitUrlJsonConfigKey.kitShortUrlBizCodesRevise.getMap();
        String biz;
        if (map.containsKey(linkUrlDO.getBiz())) {
            biz = map.get(linkUrlDO.getBiz());
        } else {
            throw BizException.of(KitErrorCode.GENERAL_GET_BIZ);
        }

        LinkUrlsDTO.Builder urlBuilder = LinkUrlsDTO.newBuilder()
                .setSource(linkUrlDO.getSource())
                .setBiz(biz)
                .setShortUrl(MerchantKitUrlStringConfigKey.kitShortUrlDomain.get().trim() + linkUrlDO.getShortUrl())
                .setNote(linkUrlDO.getNote())
                .setLongUrl(linkUrlDO.getLongUrl())
                .setCreator(linkUrlDO.getCreator())
                .setCreateTime(linkUrlDO.getCreateTime())
                .setUpdateTime(linkUrlDO.getUpdateTime())
                .setId(linkUrlDO.getId());

        return urlBuilder.build();
    }
}
