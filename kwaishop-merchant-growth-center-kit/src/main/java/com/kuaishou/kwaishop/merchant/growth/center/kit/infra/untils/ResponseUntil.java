package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.untils;

import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-03
 */
public class ResponseUntil {
    /**
     * 解析错误码
     */
    public static int parseCode(Exception e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            return bizException.getCode();
        } else {
            return BaseResultCode.SERVER_ERROR_VALUE;
        }
    }

    /**
     * 解析错误信息
     */
    public static String parseMessage(Exception e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            return bizException.getMessage();
        } else {
            return e.getClass().getSimpleName();
        }
    }
}
