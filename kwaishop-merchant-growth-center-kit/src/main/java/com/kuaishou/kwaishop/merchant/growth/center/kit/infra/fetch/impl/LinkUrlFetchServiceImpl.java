package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.fetch.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.infra.shortlink.sdk.service.InfraShortLinkClientService;
import com.kuaishou.infra.shortlink.service.InfraShortLinkServiceOuterClass.ExtraInfo;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.exception.KitErrorCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.fetch.LinkUrlFetchService;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-03
 */
@Slf4j
@Lazy
@Service
public class LinkUrlFetchServiceImpl implements LinkUrlFetchService {

    public static final String KPN = "KUAISHOU";

    @Autowired
    private InfraShortLinkClientService infraShortLinkClientService;

    @Override
    public String getShortUrl(String longUrl) {
        StopWatch sw = new StopWatch();
        try {
            String shortUrl = infraShortLinkClientService.globalSyncCompress(KPN, longUrl, ExtraInfo.UPDATE);
            if (StringUtils.isBlank(shortUrl)) {
                throw BizException.of(KitErrorCode.GENERAL_SHORT_URL_EMPTY);
            }
            PerfUtil.perfSuccessWithWatch(KitPerfEnum.GET_SHORT_URL, sw);
            log.info("短链接中心获取短链，costTime {} longUrl {} shortUrl {}", sw.getElapsedTime(), longUrl, shortUrl);
            return shortUrl;
        } catch (BizException be) {
            log.error("[短链接中心]获取短链接为空! costTime {} longUrl:{}", sw.getElapsedTime(), longUrl, be);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.GET_SHORT_URL, String.valueOf(be.getCode()), be.getMessage(), sw);
            throw BizException.of(KitErrorCode.GENERAL_SHORT_URL_EMPTY);
        } catch (Exception e) {
            log.error("[短链接中心]获取短链接异常! costTime {} longUrl:{}", sw.getElapsedTime(), longUrl, e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.GET_SHORT_URL,
                    String.valueOf(BaseResultCode.SERVER_ERROR_VALUE), e.getClass().getSimpleName(), sw);
            throw BizException.of(KitErrorCode.GENERAL_GET_SHORT_URL_ERROR);
        }
    }

    @Override
    public String getPermanentShortUrl(String longUrl) {
        StopWatch sw = new StopWatch();
        try {
            String shortUrl = infraShortLinkClientService.globalSyncCompress(KPN, longUrl, ExtraInfo.PERMANENT);
            if (StringUtils.isBlank(shortUrl)) {
                throw BizException.of(KitErrorCode.GENERAL_SHORT_URL_EMPTY);
            }
            PerfUtil.perfSuccessWithWatch(KitPerfEnum.GET_SHORT_URL, sw);
            log.info("[短链接中心] 获取永久有效短链成功，costTime {} longUrl {} shortUrl {}", sw.getElapsedTime(), longUrl, shortUrl);
            return shortUrl;
        } catch (BizException be) {
            log.error("[短链接中心] 获取永久有效短链接为空! costTime {} longUrl:{}", sw.getElapsedTime(), longUrl, be);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.GET_SHORT_URL, String.valueOf(be.getCode()), be.getMessage(), sw);
            throw BizException.of(KitErrorCode.GENERAL_SHORT_URL_EMPTY);
        } catch (Exception e) {
            log.error("[短链接中心] 获取永久有效短链接异常! costTime {} longUrl:{}", sw.getElapsedTime(), longUrl, e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.GET_SHORT_URL,
                    String.valueOf(BaseResultCode.SERVER_ERROR_VALUE), e.getClass().getSimpleName(), sw);
            throw BizException.of(KitErrorCode.GENERAL_GET_SHORT_URL_ERROR);
        }
    }
}
