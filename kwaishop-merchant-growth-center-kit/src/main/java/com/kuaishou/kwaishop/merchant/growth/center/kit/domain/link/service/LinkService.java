package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.service;

import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query.LinkUrlQueryCondition;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
public interface LinkService {
    /**
     * 新增短链接
     */
    long addLinkUrl(LinkUrlDO linkUrlDO);

    /**
     * 查询短链接
     */
    PageBO<LinkUrlDO> queryLinkUrl(LinkUrlQueryCondition queryCondition);

    /**
     * 根据id删除短链接
     */
    void deleteLinkUrl(long id);

    /**
     * 根据id更新短链接
     */
    long updateLinkUrl(LinkUrlDO linkUrlDO);

    /**
     * 根据id获取该条记录信息
     */
    LinkUrlDO getLinkUrlById(long id);
}
