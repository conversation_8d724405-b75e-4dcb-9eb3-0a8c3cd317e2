package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.biz.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.biz.LinkBizAdminService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.converter.UrlRequestConverter;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.service.LinkService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf.MerchantKitUrlJsonConfigKey;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.exception.KitErrorCode;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.fetch.LinkUrlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query.LinkUrlQueryCondition;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.untils.UrlUtils;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.LinkUrlsDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Lazy
@Slf4j
@Service
public class LinkBizAdminServiceImpl implements LinkBizAdminService {

    @Autowired
    private LinkService linkService;
    @Autowired
    private LinkUrlFetchService linkUrlFetchService;

    @Override
    public AddKitLinkResponse addKitLink(AddKitLinkRequest request) {
        log.info("创建短链接请求, request:{}", ObjectMapperUtils.toJSON(request));
        LinkBizParamsValidator.checkAddKitLinkParams(request);

        if (!MerchantKitUrlJsonConfigKey.kitShortUrlBizCodesRevise
                .getMap().containsKey(request.getBiz())) {
            log.error("biz无权限，request.getBiz():{}", ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(KitErrorCode.AUTHORITY_BIZ_FORBIDDEN, "biz无权限");
        }
        String shortUrl;
        LinkUrlDO linkUrlDO;
        if (StringUtils.isNotBlank(UrlUtils.findSource(request.getLongUrl()))) {  //传入的长链接中没有包含source
            shortUrl = linkUrlFetchService.getShortUrl(request.getLongUrl());
            linkUrlDO = UrlRequestConverter.parseFromAddKitLinkExistSourceRequest(request, shortUrl);
        } else {
            String longUrlSplicing = UrlUtils.appendParams(request.getLongUrl(), request.getSource()); //longUrl与source拼接
            shortUrl = linkUrlFetchService.getShortUrl(longUrlSplicing); //压缩为短链接
            linkUrlDO = UrlRequestConverter.parseFromAddKitLinkNotExistSourceRequest(request, shortUrl);
        }
        linkService.addLinkUrl(linkUrlDO);
        long id = linkUrlDO.getId();

        return AddKitLinkResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setData(AddKitLinkDTO.newBuilder().setShortUrl(shortUrl).setId(id))
                .build();
    }

    @Override
    public QueryKitLinkResponse queryKitLink(QueryKitLinkRequest request) {
        log.info("获取短链接请求, request:{}", ObjectMapperUtils.toJSON(request));

        LinkUrlQueryCondition queryCondition = LinkUrlQueryCondition.builder().biz(request.getBiz())
                .pageNo(request.getPageNum()).pageSize(request.getPageSize())
                .source(request.getSource()).creator(request.getCreator()).orderByUpdateTimeDesc(true).build();

        //查询的列表
        PageBO<LinkUrlDO> pageBO = linkService.queryLinkUrl(queryCondition);
        long count = pageBO.getTotal();
        List<LinkUrlsDTO> linkUrlsDTOList = new ArrayList<>();
        pageBO.getData().forEach(linkUrlDO -> linkUrlsDTOList.add(UrlRequestConverter.linkUrlDO2DTO(linkUrlDO)));

        return QueryKitLinkResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setData(QueryKitLinkDTO.newBuilder().setCount(count).addAllUrls(linkUrlsDTOList).build())
                .build();
    }


    @Override
    public DeleteKitLinkResponse deleteKitLink(DeleteKitLinkRequest request) {
        log.info("删除短链接请求, request:{}", ObjectMapperUtils.toJSON(request));

        LinkUrlDO linkUrl = linkService.getLinkUrlById(request.getId()); //数据库中拿到当前ID对应的数据
        if (linkUrl == null || !request.getCreator().equals(linkUrl.getCreator())) {
            log.error("creator无权限删除，request.getCreator():{}", ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(KitErrorCode.AUTHORITY_CREATOR_FORBIDDEN, "creator无权限删除");
        }
        linkService.deleteLinkUrl(request.getId());
        return DeleteKitLinkResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .build();
    }

    @Override
    public UpdateKitLinkResponse updateKitLink(UpdateKitLinkRequest request) {
        log.info("更新短链接请求,request:{}", ObjectMapperUtils.toJSON(request));
        LinkBizParamsValidator.checkUpdateKitLinkParams(request);

        LinkUrlDO linkUrl = linkService.getLinkUrlById(request.getId()); //数据库中拿到当前ID对应的数据
        if (linkUrl == null || !request.getCreator().equals(linkUrl.getCreator())) {
            log.error("creator无权限编辑，request.getCreator():{}", ObjectMapperUtils.toJSON(request));
            throw BizException.ofMessage(KitErrorCode.AUTHORITY_CREATOR_FORBIDDEN, "creator无权限编辑");
        }
        LinkUrlDO linkUrlDO = UrlRequestConverter.parseFromUpdateLinkRequest(request);
        long recordId = linkService.updateLinkUrl(linkUrlDO);
        return UpdateKitLinkResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setRecordId(recordId)
                .build();
    }

    @Override
    public GetBizListResponse getBizList(GetBizListRequest request) {
        log.info("获取Biz配置列表,request:{}", ObjectMapperUtils.toJSON(request));

        Map<String, String> stringMap = MerchantKitUrlJsonConfigKey.kitShortUrlBizCodesRevise.getMap();
        List<GetBizListDTO> listDTOS = new ArrayList<>();
        for (String key : stringMap.keySet()) {
            listDTOS.add(GetBizListDTO.newBuilder().setKey(key).setValue(stringMap.get(key)).build());
        }

        return GetBizListResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .addAllBizList(listDTOS)
                .build();
    }
}
