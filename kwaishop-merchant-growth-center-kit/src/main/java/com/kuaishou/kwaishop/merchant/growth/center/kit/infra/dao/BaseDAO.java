package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.dao;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query.BaseQueryCondition;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;


/**
 * 基础DAO，单表DAO继承使用，不包含缓存逻辑
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-3-03
 */
public abstract class BaseDAO<T extends BaseDO, Q extends BaseQueryCondition> {

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return DO
     */
    public T queryById(Long id) {
        if (id == null) {
            return null;
        }

        return getMapper().selectById(id);
    }

    public Map<Long, T> batchQueryByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        List<T> dataList = getMapper().selectBatchIds(ids);
        return CollectionUtils.isEmpty(dataList)
               ? Maps.newHashMap() : dataList.stream()
                       .collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
    }

    protected T queryOne(Q condition) {
        return queryOne(condition, false);
    }

    /**
     * 查询一条记录
     *
     * @param condition 查询条件
     * @param needCheckMany 是否需要检查多条的情况，如果为true多条时会抛异常
     * @return 单条记录
     */
    protected T queryOne(Q condition, boolean needCheckMany) {
        List<T> list = queryList(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (needCheckMany && list.size() > 1) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR,
                    "Expected one result (or null) to be returned by queryOne(),but found: " + list.size());
        }
        return list.get(0);
    }

    /**
     * 查询一条记录，如果用该条件查到了多条数据，则默认取第一条
     *
     * @param condition 查询条件
     * @return 单条记录
     */
    public List<T> queryList(Q condition) {
        if (condition == null) {
            return new ArrayList<>();
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectList(queryWrapper);
    }

    /**
     * 分页查询，页码默认从1开始
     */
    public PageBO<T> queryPageList(Q condition) {
        if (condition == null || condition.getPageSize() <= 0) {
            return new PageBO<>(0L, 0, 0, new ArrayList<>());
        }
        // 对页码进行容错处理
        int pageNo = condition.getPageNo() == null || condition.getPageNo() <= 1
                     ? 1 : condition.getPageNo();
        condition.setPageNo(pageNo);
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        Page<T> page = new Page<>(condition.getPageNo(), condition.getPageSize());
        page = getMapper().selectPage(page, queryWrapper);
        return new PageBO<>(page.getTotal(), condition.getPageNo(),
                condition.getPageSize(), page.getRecords());
    }

    /**
     * 根据主键ID更新传入的非空对象
     *
     * @param domainObject 更新对象
     * @return 影响行数
     */
    public long updateSelectiveById(T domainObject) {
        if (domainObject == null) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR, "更新记录不能为null");
        }
        if (domainObject.getId() == null) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR, "更新记录主键ID不能为null");
        }
        domainObject.setUpdateTime(System.currentTimeMillis());
        getMapper().updateById(domainObject);
        return domainObject.getId();
    }

    /**
     * 新增记录
     *
     * @param domainObject 领域对象
     * @return 影响行数
     */
    public long insert(T domainObject) {
        if (domainObject == null) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR, "插入记录不能为null");
        }

        domainObject.setUpdateTime(System.currentTimeMillis());
        domainObject.setCreateTime(System.currentTimeMillis());
        getMapper().insert(domainObject);
        return domainObject.getId();
    }

    public long saveOrUpdate(T domainObject) {
        if (domainObject == null) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR, "插入或更新记录不能为null");
        }
        if (domainObject.getId() == null) {
            return insert(domainObject);
        }
        return updateSelectiveById(domainObject);
    }

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 影响行数
     */
    public int logicDelete(Long id) {
        if (id == null) {
            throw BizException.ofMessage(BasicErrorCode.SERVER_ERROR, "删除操作的目标记录ID不能为null");
        }
        return getMapper().deleteById(id);
    }

    /**
     * 将原始查询条件转换为查询wrapper
     *
     * @param condition 原始查询条件
     * @return 查询wrapper
     */
    public QueryWrapper<T> convertToQueryWrapper(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        if (condition.getId() != null) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
        if (condition.getIdNotEqual() != null) {
            queryWrapper.and(q -> q.ne("id", condition.getId()));
        }
        if (condition.getIds() != null) {
            queryWrapper.and(q -> q.in("id", condition.getIds()));
        }
        // 子类扩展
        fillQueryCondition(condition, queryWrapper);
        if (condition.getOrderByIdAsc() != null) {
            queryWrapper.orderByAsc("id");
        }
        if (condition.getOrderByIdDesc() != null) {
            queryWrapper.orderByDesc("id");
        }
        if (condition.getOrderByCreateTimeDesc() != null) {
            queryWrapper.orderByDesc("create_time");
        }
        if (condition.getOrderByUpdateTimeDesc() != null && condition.getOrderByUpdateTimeDesc()) {
            queryWrapper.orderByDesc("update_time");
        }
        if (condition.getLimit() != null && condition.getLimit() >= 0) {
            queryWrapper.last("limit " + condition.getLimit().toString());
        }
        return queryWrapper;
    }


    /**
     * 开放给子类，用于扩展子类特有的查询条件
     *
     * @param condition 查询条件
     * @param queryWrapper 特定条件拼接在该wrapper上即可
     */
    protected abstract void fillQueryCondition(Q condition, QueryWrapper<T> queryWrapper);

    protected abstract BaseMapper<T> getMapper();
}
