package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.exception;


import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-04
 */
public enum KitErrorCode implements ErrorCode {

    //通用(2623000~2623099)
    GENERAL_TIME_GAP_INVALID(2623000, "开始结束时间配置异常"),
    GENERAL_JSON_STRING_INVALID(2623001, "JSON字符串异常"),
    GENERAL_SHORT_URL_EMPTY(2623002, "短链接为空"),
    GENERAL_GET_SHORT_URL_ERROR(2623003, "获取短链接异常"),
    GENERAL_GET_BIZ(2623004, "kConf中获取biz为空异常"),
    GENERAL_GET_URL_DOMAIN(2623005, "kConf中获取urlDomain为空异常"),
    //权限域
    AUTHORITY_BIZ_FORBIDDEN(2623004, "biz无权限"),
    AUTHORITY_CREATOR_FORBIDDEN(2623004, "creator无权限"),
    ;

    private final int code;
    private final String message;
    private final boolean logErrorMsg;

    KitErrorCode(int code, String message, boolean logErrorMsg) {
        this.code = code;
        this.message = message;
        this.logErrorMsg = logErrorMsg;
    }

    KitErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
        this.logErrorMsg = true;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public boolean isLogErrorMsg() {
        return this.logErrorMsg;
    }
}
