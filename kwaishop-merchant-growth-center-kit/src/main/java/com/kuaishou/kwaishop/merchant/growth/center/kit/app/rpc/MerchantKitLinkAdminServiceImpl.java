package com.kuaishou.kwaishop.merchant.growth.center.kit.app.rpc;

import org.springframework.beans.factory.annotation.Autowired;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.biz.LinkBizAdminService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.enums.KitPerfEnum;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.untils.ResponseUntil;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.KrpcMerchantKitLinkAdminServiceGrpc.MerchantKitLinkAdminServiceImplBaseV2;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-01
 */

@Slf4j
@KrpcService(supportDebug = true, registry = "custom-registry-config-kess")
public class MerchantKitLinkAdminServiceImpl extends MerchantKitLinkAdminServiceImplBaseV2 {

    @Autowired
    private LinkBizAdminService linkBizAdminService;

    @Override
    public AddKitLinkResponse addKitLink(AddKitLinkRequest request) {
        StopWatch sw = new StopWatch();
        try {
            return linkBizAdminService.addKitLink(request);
        } catch (Exception e) {
            log.error("[短链接平台服务] addKitLink exception,request:{}",
                    ObjectMapperUtils.toJSON(request), e);
            int code = ResponseUntil.parseCode(e);
            String message = ResponseUntil.parseMessage(e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.ADD_SHORT_URL, String.valueOf(code), message, sw);

            return AddKitLinkResponse.newBuilder()
                    .setResult(code)
                    .setErrorMsg(message)
                    .build();
        }
    }

    @Override
    public QueryKitLinkResponse queryKitLink(QueryKitLinkRequest request) {
        StopWatch sw = new StopWatch();
        try {
            return linkBizAdminService.queryKitLink(request);
        } catch (Exception e) {
            log.error("[短链接平台服务] queryKitLink exception,request:{}",
                    ObjectMapperUtils.toJSON(request), e);
            int code = ResponseUntil.parseCode(e);
            String message = ResponseUntil.parseMessage(e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.QUERY_SHORT_URL, String.valueOf(code), message, sw);

            return QueryKitLinkResponse.newBuilder()
                    .setResult(code)
                    .setErrorMsg(message)
                    .build();
        }
    }

    @Override
    public DeleteKitLinkResponse deleteKitLink(DeleteKitLinkRequest request) {
        StopWatch sw = new StopWatch();
        try {
            return linkBizAdminService.deleteKitLink(request);
        } catch (Exception e) {
            log.error("[短链接平台服务] deleteKitLink exception,request:{}",
                    ObjectMapperUtils.toJSON(request), e);
            int code = ResponseUntil.parseCode(e);
            String message = ResponseUntil.parseMessage(e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.DELETE_SHORT_URL, String.valueOf(code), message, sw);

            return DeleteKitLinkResponse.newBuilder()
                    .setResult(code)
                    .setErrorMsg(message)
                    .build();
        }
    }

    @Override
    public UpdateKitLinkResponse updateKitLink(UpdateKitLinkRequest request) {
        StopWatch sw = new StopWatch();
        try {
            return linkBizAdminService.updateKitLink(request);
        } catch (Exception e) {
            log.error("[短链接平台服务] updateKitLink exception,request:{}",
                    ObjectMapperUtils.toJSON(request), e);
            int code = ResponseUntil.parseCode(e);
            String message = ResponseUntil.parseMessage(e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.UPDATE_SHORT_URL, String.valueOf(code), message, sw);

            return UpdateKitLinkResponse.newBuilder()
                    .setResult(code)
                    .setErrorMsg(message)
                    .build();
        }
    }

    @Override
    public GetBizListResponse getBizList(GetBizListRequest request) {

        StopWatch sw = new StopWatch();
        try {
            return linkBizAdminService.getBizList(request);
        } catch (Exception e) {
            log.error("[短链接平台服务] getBizList exception,request:{}",
                    ObjectMapperUtils.toJSON(request), e);
            int code = ResponseUntil.parseCode(e);
            String message = ResponseUntil.parseMessage(e);
            PerfUtil.perfExceptionWithWatch(KitPerfEnum.GET_BIZ_LIST, String.valueOf(code), message, sw);

            return GetBizListResponse.newBuilder()
                    .setResult(code)
                    .setErrorMsg(message)
                    .build();
        }
    }
}
