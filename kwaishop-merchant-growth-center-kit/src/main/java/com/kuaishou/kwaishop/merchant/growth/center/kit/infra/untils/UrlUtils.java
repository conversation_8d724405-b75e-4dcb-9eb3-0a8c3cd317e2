package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.untils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-16
 */
public class UrlUtils {

    /**
     * longUrl与source的拼接
     *
     * @param url
     * @param params
     * @return
     */

    public static final String SOURCE = "source";

    public static String appendParams(String url, String params) {

        StringBuffer sb = new StringBuffer("");
        sb.append(SOURCE).append("=").append(params);
        url = url.trim();
        int length = url.length();
        int index = url.indexOf("?");
        if (index > -1) { //url说明有问号
            if ((length - 1) == index) { //url最后一个符号为？，如：http://wwww.kuaishou.com?
                url += sb.toString();
            } else { //情况为：http://wwww.kuaishou.com?aa=11
                url += "&" + sb.toString();
            }
        } else { //url后面没有问号，如：http://wwww.kuaishou.com
            url += "?" + sb.toString();
        }
        return url;
    }

    /**
     * 传入的longUrl中是否包含的有source
     *
     * @param url
     * @return
     */
    public static String findSource(String url) {

        url = url.trim();
        int length = url.length();
        int index = url.indexOf("?");
        if (index > -1) { //url后有 ？
            if ((length - 1) == index) {
                return "";
            } else { //问号后面还有别的参数
                String str = url.substring(index + 1);
                String[] split = str.split("&");
                for (String s : split) {
                    if (s.contains("source")) {
                        String[] split1 = s.split("=");
                        return split1[1];
                    }
                }
            }
        }
        return "";
    }
}
