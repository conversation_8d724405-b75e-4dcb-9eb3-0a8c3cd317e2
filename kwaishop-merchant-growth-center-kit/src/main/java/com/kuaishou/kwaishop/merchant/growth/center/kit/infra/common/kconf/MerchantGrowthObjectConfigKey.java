package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.common.kconf;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.test.model.bo.TriggerTaskConfigBO;

/**
 * JsonObject类型ConfigKey
 * kconf类型：json
 */

public enum MerchantGrowthObjectConfigKey implements KconfSupplier<Object> {
    testToolTriggerTaskConfig(TriggerTaskConfigBO.class, new TriggerTaskConfigBO()),
    ;

    private Class<?> objectClass;
    private Object defaultValue;

    <V> MerchantGrowthObjectConfigKey(Class<V> objectClass,
            V defaultValue) {
        this.objectClass = objectClass;
        this.defaultValue = defaultValue;
    }

    @Override
    public String configKey() {
        return name();
    }

    @Override
    public Object defaultValue() {
        return this.defaultValue;
    }

    @Override
    public Kconf<Object> getKConf() {
        return Kconfs.ofJson(getConfigKey(), defaultValue, Object.class).build();
    }


    @Override
    public Object get() {
        return getObject(objectClass);
    }

    @SuppressWarnings("unchecked")
    public <V> V getObject() {
        return (V) getObject(objectClass);
    }

    @SuppressWarnings("unchecked")
    private <V> V getObject(Class<V> objClassArg) {
        Kconf<V> kConf = Kconfs.ofJson(getConfigKey(), (V) defaultValue, objClassArg).build();
        if (kConf == null) {
            return (V) objClassArg;
        }
        return kConf.get();
    }

}
