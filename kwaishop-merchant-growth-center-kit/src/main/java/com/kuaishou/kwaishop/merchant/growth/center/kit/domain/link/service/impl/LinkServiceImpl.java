package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.service.LinkService;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.dao.LinkUrlDAO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.LinkUrlDO;
import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query.LinkUrlQueryCondition;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Slf4j
@Service
public class LinkServiceImpl implements LinkService {

    @Autowired
    private LinkUrlDAO linkUrlDAO;

    @Override
    public long addLinkUrl(LinkUrlDO linkUrlDO) {
        return linkUrlDAO.insert(linkUrlDO);
    }

    @Override
    public PageBO<LinkUrlDO> queryLinkUrl(LinkUrlQueryCondition condition) {
        PageBO<LinkUrlDO> pageBO = linkUrlDAO.pageGetUrlList(condition);

        return pageBO;

    }

    @Override
    public void deleteLinkUrl(long id) {
        linkUrlDAO.logicDelete(id);
    }


    @Override
    public long updateLinkUrl(LinkUrlDO linkUrlDO) {
        return linkUrlDAO.updateSelectiveById(linkUrlDO);
    }

    /**
     * 根据id获取该条记录信息
     *
     * @param id
     */
    @Override
    public LinkUrlDO getLinkUrlById(long id) {
        return linkUrlDAO.queryById(id);
    }
}
