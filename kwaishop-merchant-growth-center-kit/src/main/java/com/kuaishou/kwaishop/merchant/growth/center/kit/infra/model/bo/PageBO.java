package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.dos.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageBO<T extends BaseDO> {

    private long total;

    private int pageNum;

    private int pageSize;

    private List<T> data;
}

