package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.biz;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.AddKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.DeleteKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.GetBizListResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.QueryKitLinkResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.kit.link.UpdateKitLinkResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
public interface LinkBizAdminService {

    /*
    创建短链接
     */
    AddKitLinkResponse addKitLink(AddKitLinkRequest request);

    /*
    查询短链接
     */
    QueryKitLinkResponse queryKitLink(QueryKitLinkRequest request);

    /*
    根据ID删除短链接
     */
    DeleteKitLinkResponse deleteKitLink(DeleteKitLinkRequest request);

    /*
    根据ID更新短链接
     */
    UpdateKitLinkResponse updateKitLink(UpdateKitLinkRequest request);

    /*
    获取Biz配置列表
     */
    GetBizListResponse getBizList(GetBizListRequest request);
}
