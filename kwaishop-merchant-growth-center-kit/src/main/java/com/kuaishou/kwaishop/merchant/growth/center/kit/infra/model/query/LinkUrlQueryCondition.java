package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class LinkUrlQueryCondition extends BaseQueryCondition {

    /*
    业务类型
     */
    private String biz;
    /*
    来源标识
     */
    private String source;
    /*
    创建时间
     */
    private long createTime;
    /*
    更新时间
     */
    private long updateTime;
}
