package com.kuaishou.kwaishop.merchant.growth.center.kit.infra.model.query;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-02
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BaseQueryCondition extends PageQueryCondition {
    private Long id;

    private Collection<Long> ids;

    private Long idNotEqual;

    private String creator;

    private Boolean orderByIdDesc;

    private Boolean orderByIdAsc;

    private Boolean orderByCreateTimeDesc;

    private Boolean orderByUpdateTimeDesc;
    /**
     * 强制读主库
     */
    private Boolean readMaster;

    private Long limit;
}
