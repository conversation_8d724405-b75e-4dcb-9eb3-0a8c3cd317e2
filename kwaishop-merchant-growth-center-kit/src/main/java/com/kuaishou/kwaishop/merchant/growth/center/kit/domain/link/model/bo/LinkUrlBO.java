package com.kuaishou.kwaishop.merchant.growth.center.kit.domain.link.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LinkUrlBO {

    /*
     业务类型
      */
    private String biz;
    /*
    原始链接
     */
    private String longUrl;
    /*
    生成的短链接
     */
    private String shortUrl;
    /*
    来源表示
     */
    private String source;
    /*
    备注
     */
    private String note;
    /*
    创建人
     */
    private String creator;
    /*
    创建时间
     */
    private long createTime;
    /*
    更新时间
     */
    private long updateTime;

}
