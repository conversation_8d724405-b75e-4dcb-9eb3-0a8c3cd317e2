package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
public interface NotificationPeriodConfigService {

    /**
     * 计算出一个最近候选绝对时间
     * @param notificationPeriod 配置对象
     * @param beginTime 起始时间点
     * @return 候选时间
     */
    long parseAbsoluteLatestTime(NotificationPeriodBO notificationPeriod, long beginTime);

    /**
     * 计算出一个最近候选相对时间
     * @param notificationPeriod 配置对象
     * @param beginTime 起始时间点
     * @return 候选时间
     */
    long parseRelativeLatestTime(NotificationPeriodBO notificationPeriod, long beginTime);
}
