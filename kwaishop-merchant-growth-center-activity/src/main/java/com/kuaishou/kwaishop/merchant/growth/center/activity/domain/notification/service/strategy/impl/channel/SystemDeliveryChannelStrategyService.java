package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum.WORK_PLATFORM_POPUP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityBooleanConfigKey.promoterPermissionCheckRpcSwitch;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.DistributeFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OperationFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
@Service
@Slf4j
public class SystemDeliveryChannelStrategyService implements NotificationChannelStrategyService {

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;

    @Autowired
    private DistributeFetchService distributeFetchService;

    @Autowired
    private OperationFetchService operationFetchService;

    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return WORK_PLATFORM_POPUP;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        notificationPushFetchService.systemDeliveryMessagePush(userId, configBO, templateParams);
    }

    public boolean filter(long userId, NotificationPushConfigBO configBO) {
        //查询用户是否能切换达人工作台
        if (promoterPermissionCheckRpcSwitch.get()) {
            // 开关开启 走新逻辑
            return operationFetchService.checkPromoterOperationPermission(userId);
        } else {
            // 走老逻辑
            return distributeFetchService.checkPromoterOperationPermission(userId);
        }
    }
}
