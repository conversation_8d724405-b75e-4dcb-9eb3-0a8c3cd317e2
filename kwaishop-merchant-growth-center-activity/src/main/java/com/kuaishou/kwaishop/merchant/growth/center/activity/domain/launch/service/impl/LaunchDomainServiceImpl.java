package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.launchConfigLoadingCacheExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.launchConfigLoadingCacheRefreshTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.taskConfigLoadingCacheExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.taskConfigLoadingCacheRefreshTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.maxLoadingCacheCount;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.concurrent.AsyncReloadCacheLoader;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter.LaunchConfigFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigCacheKey;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigDiffBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigSceneCacheKey;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.launch.LaunchConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.launch.LaunchConfigDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
@Service
@Slf4j
public class LaunchDomainServiceImpl implements LaunchDomainService {

    @Resource
    private LaunchConfigDAO launchConfigDAO;

    private final LoadingCache<LaunchConfigSceneCacheKey, List<LaunchConfigBO>> launchConfigBySceneCache =
            KsCacheBuilder.newBuilder()
                    .maximumSize(maxLoadingCacheCount.get())
                    .refreshAfterWrite(() -> Duration.ofSeconds(launchConfigLoadingCacheRefreshTime.get()))
                    .expireAfterAccess(() -> Duration.ofSeconds(launchConfigLoadingCacheExpireTime.get()))
                    .concurrencyLevel(5)
                    .enablePerf("growth.task.local.cache.launchConfigBySceneCache")
                    .build(new AsyncReloadCacheLoader<>() {
                        @Override
                        public List<LaunchConfigBO> load(LaunchConfigSceneCacheKey key) {
                            Integer resourceType = key.getResourceType();
                            String channel = key.getChannel();
                            String scene = key.getScene();

                            return queryLaunchConfigListByScene(resourceType, channel, scene, false);
                        }
                    });

    private final LoadingCache<LaunchConfigCacheKey, List<LaunchConfigBO>> launchConfigByActivityCache =
            KsCacheBuilder.newBuilder()
                    .maximumSize(maxLoadingCacheCount.get())
                    .refreshAfterWrite(() -> Duration.ofSeconds(taskConfigLoadingCacheRefreshTime.get()))
                    .expireAfterAccess(() -> Duration.ofSeconds(taskConfigLoadingCacheExpireTime.get()))
                    .concurrencyLevel(5)
                    .enablePerf("growth.task.local.cache.launchConfigByActivityCache")
                    .build(new AsyncReloadCacheLoader<>() {
                        @Override
                        public List<LaunchConfigBO> load(LaunchConfigCacheKey key) {
                            Integer resourceType = key.getResourceType();
                            long activityId = key.getActivityId();
                            String channel = key.getChannel();
                            String scene = key.getScene();

                            if (activityId < 0L || channel == null || scene == null) {
                                return Lists.newArrayList();
                            }

                            return queryLaunchConfigListByScene(resourceType, activityId, channel, scene);
                        }

                        @Override
                        public Map<LaunchConfigCacheKey, List<LaunchConfigBO>> loadAll(
                                Iterable<? extends LaunchConfigCacheKey> keys) {
                            String channel = null;
                            String scene = null;
                            List<Long> activityIds = Lists.newArrayList();

                            for (LaunchConfigCacheKey key : keys) {
                                long activityId = key.getActivityId();
                                channel = key.getChannel();
                                scene = key.getScene();

                                if (activityId < 0L || channel == null || scene == null) {
                                    continue;
                                }

                                activityIds.add(activityId);
                            }

                            if (channel == null || scene == null || CollectionUtils.isEmpty(activityIds)) {
                                return Maps.newHashMap();
                            }

                            List<LaunchConfigBO> records =
                                    queryLaunchConfigListByScene(activityIds, channel, scene, false);
                            if (CollectionUtils.isEmpty(records)) {
                                return Maps.newHashMap();
                            }

                            Map<Long, List<LaunchConfigBO>> recordMap = records.stream()
                                    .collect(Collectors.groupingBy(LaunchConfigBO::getActivityId));

                            Map<LaunchConfigCacheKey, List<LaunchConfigBO>> result = Maps.newHashMap();

                            String finalChannel = channel;
                            String finalScene = scene;
                            recordMap.forEach((activityId, configs) -> {
                                LaunchConfigCacheKey cacheKey = LaunchConfigCacheKey.builder().activityId(activityId)
                                        .channel(finalChannel).scene(finalScene).build();
                                result.put(cacheKey, configs);
                            });

                            return result;
                        }
                    });

    @Override
    public void batchSaveLaunchConfigList(List<LaunchConfigBO> launchConfigBOList) {
        if (CollectionUtils.isEmpty(launchConfigBOList)) {
            return;
        }

        List<LaunchConfigDO> records =
                launchConfigBOList.stream().map(LaunchConfigFactory::convertToDO).collect(Collectors.toList());
        int num = launchConfigDAO.batchInsert(records);
        if (num != launchConfigBOList.size()) {
            log.error("[批量保存投放配置] 保存失败 launchConfigList:{}", toJSON(launchConfigBOList));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "批量保存投放配置失败");
        }
    }

    @Override
    public void deleteLaunchConfigByActivityId(Long activityId) {
        if (activityId == null || activityId <= 0) {
            return;
        }
        launchConfigDAO.deleteByActivityId(activityId);
    }

    @Override
    public List<LaunchConfigBO> getLaunchConfigListByActivityId(Long activityId,
            Integer resourceType, Boolean readMaster) {
        if (activityId == null || activityId <= 0L) {
            return Lists.newArrayList();
        }

        List<LaunchConfigDO> records = launchConfigDAO.getByActivityId(activityId, readMaster);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        // 根据resourceType过滤
        records = filterLaunchConfigByResourceType(resourceType, records);

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream()
                .map(LaunchConfigFactory::convertToBO).collect(Collectors.toList());
    }

    @Override
    public List<LaunchConfigBO> queryLaunchConfigList(List<Long> activityIds) {
        return null;
    }

    @Override
    public List<LaunchConfigBO> queryLaunchConfigListByScene(List<Long> activityIds, String channel,
            String scene, boolean withCache) {
        if (CollectionUtils.isEmpty(activityIds) || channel == null || scene == null) {
            return Lists.newArrayList();
        }

        // cache
        if (withCache) {
            List<LaunchConfigCacheKey> keys = activityIds.stream().map(activityId ->
                    LaunchConfigCacheKey.builder().activityId(activityId).channel(channel)
                            .scene(scene).build()).collect(Collectors.toList());

            ImmutableMap<LaunchConfigCacheKey, List<LaunchConfigBO>> cacheResult;
            try {
                cacheResult = launchConfigByActivityCache.getAll(keys);
            } catch (ExecutionException e) {
                log.error("[查询投放配置缓存] 查询失败 activityId:{}, channel:{}, scene:{}",
                        activityIds, channel, scene);
                return queryLaunchConfigListByScene(activityIds, channel, scene, false);
            }

            if (MapUtils.isEmpty(cacheResult)) {
                return Lists.newArrayList();
            }
            return cacheResult.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        }

        List<LaunchConfigDO> records =
                launchConfigDAO.getByActivityIds(activityIds, channel, scene);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(LaunchConfigFactory::convertToBO).collect(Collectors.toList());
    }

    @Override
    public List<LaunchConfigBO> queryLaunchConfigListByScene(Integer resourceType, Long activityId, String channel,
            String scene) {
        if (activityId == null || activityId <= 0L || StringUtils.isBlank(channel) || StringUtils.isBlank(scene)) {
            return Lists.newArrayList();
        }

        List<LaunchConfigDO> records =
                launchConfigDAO.getByActivityId(activityId, channel, scene);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        records = filterLaunchConfigByResourceType(resourceType, records);

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(LaunchConfigFactory::convertToBO).collect(Collectors.toList());
    }

    @Override
    public List<LaunchConfigBO> queryLaunchConfigListByScene(Integer resourceType, String channel, String scene,
            boolean withCache) {
        if (channel == null || scene == null || resourceType == null) {
            return Lists.newArrayList();
        }

        // 本地缓存
        if (withCache) {
            LaunchConfigSceneCacheKey key = LaunchConfigSceneCacheKey.buildKey(resourceType, channel, scene);
            List<LaunchConfigBO> result;
            try {
                result = launchConfigBySceneCache.get(key);
            } catch (ExecutionException e) {
                log.error("[根据场景查询投放配置] 缓存查询失败 channel:{}, scene:{}", channel, scene, e);
                return queryLaunchConfigListByScene(resourceType, channel, scene, false);
            }
            return result;
        }
        List<LaunchConfigDO> records = launchConfigDAO.getByScene(resourceType, channel, scene);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(LaunchConfigFactory::convertToBO).collect(Collectors.toList());
    }

    @Override
    public LaunchConfigBO queryLaunchConfigById(Long configId) {
        if (configId <= 0) {
            return null;
        }
        LaunchConfigDO record = launchConfigDAO.getById(configId);
        if (null == record) {
            return null;
        }
        return LaunchConfigFactory.convertToBO(record);
    }

    @Override
    public void handleLaunchConfigChangeCache(LaunchConfigDiffBO configDiff) {
        // todo lk
    }

    @Override
    public void deleteLaunchConfigsByIds(List<LaunchConfigBO> launchConfigs) {
        if (CollectionUtils.isEmpty(launchConfigs)) {
            return;
        }

        launchConfigDAO.deleteByIds(launchConfigs.stream().map(LaunchConfigBO::getId).collect(Collectors.toList()));
    }

    @Override
    public void updateLaunchConfigsSelectiveByIds(List<LaunchConfigBO> launchConfigs) {
        if (CollectionUtils.isEmpty(launchConfigs)) {
            return;
        }

        List<LaunchConfigDO> records =
                launchConfigs.stream().map(LaunchConfigFactory::convertToDO).collect(Collectors.toList());
        launchConfigDAO.batchUpdateSelectiveByIds(records);
    }

    @Override
    public List<LaunchConfigBO> queryLaunchConfigListByAuditBizKey(String auditBizKey) {
        if (StringUtils.isBlank(auditBizKey)) {
            return Lists.newArrayList();
        }

        List<LaunchConfigDO> records = launchConfigDAO.getByAuditBizKey(auditBizKey);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(LaunchConfigFactory::convertToBO).collect(Collectors.toList());
    }

    private List<LaunchConfigDO> filterLaunchConfigByResourceType(Integer resourceType, List<LaunchConfigDO> records) {
        // 根据resourceType过滤
        if (Objects.requireNonNull(resourceType) == LaunchConfigResourceTypeEnum.STRATEGY_ACTIVITY.getType()) {
            records = records.stream()
                    .filter(e -> null == e.getResourceType() || resourceType.equals(e.getResourceType()))
                    .collect(Collectors.toList());
        } else {
            records = records.stream()
                    .filter(e -> resourceType.equals(e.getResourceType()))
                    .collect(Collectors.toList());
        }

        return records;
    }
}
