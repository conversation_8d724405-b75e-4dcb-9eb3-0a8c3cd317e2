package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityType.ACTIVITY_TYPE_TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.isLiteActivity;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.ManageParamValidator.checkEndTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.ManageParamValidator.checkParam;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_INDUSTRY_JOIN_CANCEL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_INDUSTRY_TASK_JOIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_RESOURCE_ACTIVITY_CREATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_RESOURCE_TASK_CREATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_RESOURCE_TASK_JOIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManagePerfEnum.MANAGE_SEND_USER_RESOURCE_ACTIVITY_AWARD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ManageRedisKeyEnum.MANAGE_INTEREST_CREATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.RESOURCE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.ACTIVITY_NOT_EXIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.ACTIVITY_RESOURCE_RULE_NOT_EXIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_ACTIVITY_ALREADY_DRAW;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_ACTIVITY_DRAW_TASK_COUNT_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_ACTIVITY_TASK_NOT_EXIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.industryReportConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.investAwardConfigCreateExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.resourceInterveneTimeBuffer;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.autoTestAccountUserId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.templateIdNeedDynamicCreateInterestConfig;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSceneWithWatch;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;
import static java.util.Collections.singletonList;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.google.api.client.util.Lists;
import com.google.common.base.Joiner;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardExecuteService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.ManageForResourceBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.convert.ManageAdminConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.IndustryReportConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.entity.ActivityTaskEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.entity.UserRecordEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.enums.ReportStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.service.ActivityManageTransactionService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.service.TaskTemplateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.DrawTaskParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.CancelUserActivityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityDrawService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.cache.RedisDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.manage.ReportDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.InterestFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.MerchantShopFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.PageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.manage.ReportDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CancelUserJoinRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.DeleteUserReportRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.DrawTaskVariableDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.InterveneUserTimeRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinIndustryTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryAllReportRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryAllReportRecordResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryReportOperatorIndustryRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.ReportRecordDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.SendUserResourceActivityAwardRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.TerminateUserResourceRuleRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.interest.center.admin.protobuf.InterestConfigAdminDTO;

import io.lettuce.core.SetArgs;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-21
 */
@Service
@Lazy
@Slf4j
public class ManageForResourceBizServiceImpl implements ManageForResourceBizService {

    @Autowired
    private ReportDAO reportDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private IndicatorRecordDAO indicatorRecordDAO;

    @Autowired
    private UserAwardRecordDAO userAwardRecordDAO;

    @Autowired
    private TaskTemplateService taskTemplateService;

    @Autowired
    private AwardExecuteService awardExecuteService;

    @Autowired
    private InterestFetchService interestFetchService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private UserActivityDrawService userActivityDrawService;

    @Autowired
    private MerchantShopFetchService merchantShopFetchService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private CancelUserActivityService cancelUserActivityService;

    @Autowired
    private ActivityManageTransactionService activityManageTransactionService;

    @Override
    public long createResourceActivity(CreateResourceActivityRequest request) {
        StopWatch cost = new StopWatch();
        checkParam(request);
        //插入模版变量数据
        Map<String, Object> variables = buildResourceActivityVariables(request);

        long resourceActivityId = request.getResourceActivityId();
        String templateId = request.getTemplateId();
        //幂等校验
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO != null) {
            perfSceneWithWatch(MANAGE_RESOURCE_ACTIVITY_CREATE, "活动已创建", "资源活动_" + resourceActivityId, cost);
            return activityDO.getId();
        }
        // 实时创建权益配置
        long activityId = taskTemplateService.createOnlyActivityByTemplateId(templateId, variables);
        perfSuccessWithWatch(MANAGE_RESOURCE_ACTIVITY_CREATE, cost);
        return activityId;
    }

    @Override
    public List<Long> createResourceTask(CreateResourceTaskRequest request) {
        StopWatch cost = new StopWatch();
        checkParam(request);
        //插入模版变量数据
        Map<String, Object> variables = buildResourceTaskVariables(request);

        long resourceActivityId = request.getResourceActivityId();
        long resourceRuleId = request.getResourceRuleId();
        String templateId = request.getTemplateId();
        // 反查活动id
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO == null) {
            throw new BizException(ACTIVITY_NOT_EXIST, "活动不存在");
        }
        // 暂时只用于对投，之后放开
        if (activityDO.getSeriesType() != ActivitySeriesType.ACTIVITY_REALTIME_INVEST.getValue()) {
            throw new BizException(ACTIVITY_NOT_EXIST, "关联活动非实时对投");
        }
        long activityId = activityDO.getId();
        //幂等校验
        List<TaskDO> taskDOList = taskLocalCacheService.getTaskListByResourceRuleIdAndActivityId(
                resourceRuleId, activityId);
        if (CollectionUtils.isNotEmpty(taskDOList)) {
            perfSceneWithWatch(MANAGE_RESOURCE_TASK_CREATE, "任务已创建", "资源活动_" + resourceActivityId,
                    "资源规则_" + resourceRuleId, cost);
            return taskDOList.stream().map(TaskDO::getId).collect(Collectors.toList());
        }
        // 需要动态创建权益配置id
        List<String> configIds = templateIdNeedDynamicCreateInterestConfig.get();
        if (configIds.contains(templateId)) {
            int awardType = request.getVariable().getAwardType();
            long awardExpireTime = request.getVariable().getAwardExpireTime();
            String fundBudgetId = request.getVariable().getFundBudgetId();
            long interestConfigId = queryAndCreateInterestConfig(awardType, awardExpireTime, fundBudgetId);
            variables.put("interestConfigId", interestConfigId);
        }
        List<Long> taskIds = taskTemplateService.createOnlyTaskByTemplateId(templateId, activityId, variables);
        perfSuccessWithWatch(MANAGE_RESOURCE_TASK_CREATE, "资源活动_" + resourceActivityId, cost);
        return taskIds;
    }

    @Override
    public void joinResourceTask(JoinResourceTaskRequest request) {
        StopWatch cost = new StopWatch();
        checkParam(request);

        long resourceActivityId = request.getResourceActivityId();
        long resourceRuleId = request.getResourceRuleId();
        long accountId = request.getAccountId();
        long userId = request.getUserId();
        long startTime = request.getStartTime();
        long endTime = request.getEndTime();
        String operator = request.getOperator();
        String bizCode = request.getBizCode();
        List<DrawTaskVariableDTO> variableList = request.getVariableList();
        // 反查活动id
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO == null) {
            throw new BizException(ACTIVITY_NOT_EXIST, "活动不存在");
        }
        long activityId = activityDO.getId();
        long seriesType = activityDO.getSeriesType();

        // 实时对投参与时间校验
        checkEndTime(seriesType, endTime);
        if (isLiteActivity(activityDO)) {
            // 极速版活动
            activityManageTransactionService.joinResourceActivity(userId, activityId, operator);
            batchDrawResourceActivity(userId, activityId, null, startTime, endTime);
        } else {
            // 查规则下的任务列表
            List<TaskDO> taskList =
                    taskLocalCacheService.getTaskListByResourceRuleIdAndActivityId(resourceRuleId, activityId);
            List<Long> parentTaskIdList = getCanJoinParentTaskIdList(userId, activityId, seriesType, startTime,
                    request, taskList);
            // 报名领取活动和任务
            activityManageTransactionService
                    .joinResourceActivity(userId, activityId, operator, parentTaskIdList, accountId, variableList, bizCode);
            // 领取活动
            batchDrawResourceActivity(userId, activityId, parentTaskIdList, startTime, endTime);
        }
        perfSuccessWithWatch(MANAGE_RESOURCE_TASK_JOIN, String.valueOf(seriesType), "资源活动_" + resourceActivityId,
                "资源规则_" + resourceRuleId, cost);
    }

    @Override
    public void joinIndustryTask(JoinIndustryTaskRequest request) {
        checkParam(request);

        String industry = request.getIndustry();
        String operator = request.getOperator();
        long startTime = request.getStartTime();
        List<Long> userIdList = request.getUserIdList();
        IndustryReportConfigBO reportConfig =
                industryReportConfig.getMap(String.class, IndustryReportConfigBO.class).get(industry);
        if (reportConfig == null) {
            log.error("[重点招商提报] 定位不到提报配置，industry:{}", industry);
            throw new BizException(SERVER_ERROR);
        }
        if (!reportConfig.getOwners().contains(operator)) {
            log.error("[重点招商提报] 提报人无该行业权限，industry:{}, operator:{}", industry, operator);
            throw new BizException(PARAM_INVALID, "提报人无该行业权限");
        }
        for (long userId : userIdList) {
            if (!merchantShopFetchService.checkUserAccount(userId)) {
                throw new BizException(PARAM_INVALID, String.format("请填写正确的商家ID！%s", userId));
            }
            handleOneUserReport(userId, startTime, operator, industry, reportConfig);
        }
    }

    private void handleOneUserReport(long userId, long startTime, String operator, String industry,
            IndustryReportConfigBO reportConfig) {
        // 不能跨行业参与
        List<ReportDO> userReportRecord = reportDAO.listReportByUser(userId);
        if (CollectionUtils.isNotEmpty(userReportRecord)) {
            List<String> industryList =
                    userReportRecord.stream().map(ReportDO::getIndustry).distinct().collect(Collectors.toList());
            if (industryList.size() != 1 || !industryList.get(0).equals(industry)) {
                throw new BizException(SERVER_ERROR,
                        String.format("%s已参加%s重点招商活动", userId, industryList.get(0)));
            }
        }
        // 获取行业活动对应的资源活动ID和规则ID
        long resourceActivityId = reportConfig.getResourceActivityId();
        long resourceRuleId = reportConfig.getResourceRuleId();
        // 插入记录
        ReportDO reportDO = ReportDO.builder()
                .userId(userId)
                .reportParam(String.valueOf(startTime))
                .reportTime(System.currentTimeMillis())
                .reportActivity(reportConfig.getActivityName())
                .industry(industry)
                .creator(operator)
                .status(ReportStatusEnum.SUCCESS.getValue())
                .version(1L)
                .build();
        long reportId;
        try {
            reportId = reportDAO.insert(reportDO);
        } catch (DuplicateKeyException e) {
            throw new BizException(SERVER_ERROR, String.format("%s已参加重点招商活动", userId));
        }
        // 提报
        try {
            JoinResourceTaskRequest joinRequest = JoinResourceTaskRequest.newBuilder()
                    .setUserId(userId)
                    .setStartTime(startTime)
                    .setResourceActivityId(resourceActivityId)
                    .setResourceRuleId(resourceRuleId)
                    .setOperator(operator)
                    .build();
            joinResourceTask(joinRequest);
            // 日志打点
            log.info("[重点招商提报] 提报用户成功，userId:{}, operator:{}, industry:{}", userId, operator, industry);
            perfSuccess(MANAGE_INDUSTRY_TASK_JOIN, industry);
        } catch (Exception e) {
            log.error("[重点招商提报] 提报用户失败，userId:{}, operator:{}, industry:{}", userId, operator, industry, e);
            perfException(MANAGE_INDUSTRY_TASK_JOIN, industry, e.getClass().getSimpleName());
            ReportDO updateParam =
                    ReportDO.builder().id(reportId).status(ReportStatusEnum.FAIL.getValue()).version(1L).build();
            reportDAO.updateSelectiveById(updateParam);
        }
    }

    @SuppressWarnings("checkstyle:MagicNumber")
    @Override
    public QueryAllReportRecordResponse queryJoinRecord(QueryAllReportRecordRequest request) {
        if (request.getPageNo() == 0 || request.getPageSize() == 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID);
        }
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        String industry = request.getIndustry();
        String reporter = request.getReporter();
        PageBO<ReportDO> pageReportDO = reportDAO.listReport(pageNo, pageSize, industry, reporter);
        List<ReportDO> reportList = pageReportDO.getData();
        List<ReportRecordDTO> reportDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reportList)) {
            reportDTOList =
                    reportList.stream().map(ManageAdminConverter::buildReportDTO).collect(Collectors.toList());
        }
        return QueryAllReportRecordResponse.newBuilder()
                .setTotal(pageReportDO.getTotal())
                .addAllData(reportDTOList)
                .build();
    }

    @Override
    public void cancelIndustryTask(CancelUserJoinRecordRequest request) {
        checkParam(request);

        long id = request.getId();
        long userId = request.getUserId();
        String operator = request.getOperator();
        String industry = request.getIndustry();

        // id对应提报记录校验
        ReportDO reportDO = reportDAO.queryById(id);
        if (reportDO == null) {
            log.error("[重点招商提报取消] 无提报记录数据, req:{}", toJSON(request));
            throw new BizException(SERVER_ERROR, "无提报记录数据");
        }
        if (!reportDO.getIndustry().equals(industry)) {
            log.error("[重点招商提报取消] 记录行业与输入无法对应，record:{}, req:{}", toJSON(reportDO), toJSON(request));
            throw new BizException(SERVER_ERROR, "记录行业与输入无法对应");
        }
        if (reportDO.getStatus() != ReportStatusEnum.SUCCESS.getValue()) {
            log.error("[重点招商提报取消] 非成功记录不允许取消，record:{}, req:{}", toJSON(reportDO), toJSON(request));
            throw new BizException(SERVER_ERROR, "非成功记录不允许取消");
        }
        // 获取提报配置
        IndustryReportConfigBO reportConfig =
                industryReportConfig.getMap(String.class, IndustryReportConfigBO.class).get(industry);
        if (reportConfig == null) {
            log.error("[重点招商提报取消] 定位不到提报配置，industry:{}", industry);
            throw new BizException(SERVER_ERROR);
        }
        if (!reportConfig.getOwners().contains(operator)) {
            log.error("[重点招商提报取消] 提报人无该行业权限，industry:{}, operator:{}", industry, operator);
            throw new BizException(PARAM_INVALID, "提报人无该行业权限");
        }
        long resourceActivityId = reportConfig.getResourceActivityId();
        // 反查活动id
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO == null) {
            throw new BizException(ACTIVITY_NOT_EXIST, "活动不存在");
        }
        long activityId = activityDO.getId();
        long seriesType = activityDO.getSeriesType();
        if (seriesType != ActivitySeriesType.ACTIVITY_INDUSTRY.getValue()) {
            log.error("[重点招商提报取消] 提报取消活动非重点招商类型，industry:{}, operator:{}", industry, operator);
            throw new BizException(PARAM_INVALID, "非重点招商活动");
        }
        // 用户数据删除
        cancelUserActivityService.cancelUserActivity(userId, activityId);
        // 提报记录更新状态
        ReportDO updateParam = ReportDO.builder()
                .id(id)
                .status(ReportStatusEnum.STOP.getValue())
                .build();
        reportDAO.updateSelectiveById(updateParam);
        // 日志打点
        log.info("[重点招商提报取消] 提报取消成功，userId:{}, operator:{}, industry:{}", userId, operator, industry);
        perfSuccess(MANAGE_INDUSTRY_JOIN_CANCEL, industry);
    }

    @Override
    public void deleteReportRecord(DeleteUserReportRecordRequest request) {
        if (request.getId() <= 0 || request.getUserId() <= 0) {
            throw new BizException(PARAM_INVALID, "参数不合法");
        }
        if (!autoTestAccountUserId.get().contains(request.getUserId())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "非可操作ID");
        }
        reportDAO.deleteByIdAndUserId(request.getId(), request.getUserId());
    }

    @Override
    public IndustryReportConfigBO queryOperatorIndustry(QueryReportOperatorIndustryRequest request) {
        String operator = request.getOperator();
        if (StringUtils.isBlank(operator)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报操作人为空");
        }
        // 获取提报配置
        Map<String, IndustryReportConfigBO> reportConfigMap =
                industryReportConfig.getMap(String.class, IndustryReportConfigBO.class);
        if (reportConfigMap == null) {
            log.error("[查询重点招商提报人对应行业] 定位不到提报配置");
            throw new BizException(SERVER_ERROR);
        }
        // 遍历查询
        List<IndustryReportConfigBO> res = Lists.newArrayList();
        reportConfigMap.forEach((industry, config) -> {
            if (config.getOwners().contains(operator)) {
                res.add(config);
            }
        });
        return CollectionUtils.isEmpty(res) ? null : res.get(0);
    }

    @Override
    public void interveneUserTime(InterveneUserTimeRequest request) {
        checkParam(request);

        long userId = request.getUserId();
        long resourceActivityId = request.getResourceActivityId();
        long resourceRuleId = request.getResourceRuleId();
        long originBeginTime = request.getOriginBeginTime();
        long originEndTime = request.getOriginEndTime();
        long interveneBeginTime = request.getInterveneBeginTime();
        long interveneEndTime = request.getInterveneEndTime();

        // 反查任务系统信息
        ActivityTaskEntity activityTaskEntity = getActivityTaskEntityByResourceInfo(resourceActivityId, resourceRuleId);
        // 干预时间校验
        checkInterveneTimeRange(originBeginTime, interveneBeginTime, interveneEndTime,
                activityTaskEntity.getSeriesType());
        // 反查用户任务和指标奖励记录
        UserRecordEntity userRecordEntity = getUserRecordEntity(userId, activityTaskEntity.getActivityId(),
                activityTaskEntity.getParentTaskIdList());
        // 事务更新
        activityManageTransactionService.interveneUserRecordTime(userRecordEntity, originBeginTime, originEndTime,
                interveneBeginTime, interveneEndTime);
    }

    /**
     * 干预时间校验
     */
    private void checkInterveneTimeRange(long originBeginTime, long interveneBeginTime, long interveneEndTime,
            long seriesType) {
        if (seriesType != ActivitySeriesType.ACTIVITY_REALTIME_INVEST.getValue()) {
            throw new BizException(PARAM_INVALID, "目前仅支持实时对投类型活动");
        }
        long currentTime = System.currentTimeMillis();
        if (interveneBeginTime > 0 && originBeginTime <= currentTime) {
            throw new BizException(PARAM_INVALID, "活动已经开始,不允许修改开始时间");
        }
        if (interveneBeginTime > 0 && interveneBeginTime <= currentTime + resourceInterveneTimeBuffer.get()) {
            throw new BizException(PARAM_INVALID, "干预开始时间不能过于接近当前时间");
        }
        if (interveneEndTime > 0 && interveneEndTime <= currentTime + resourceInterveneTimeBuffer.get()) {
            throw new BizException(PARAM_INVALID, "干预结束时间不能过于接近当前时间");
        }
        if (interveneEndTime > 0 && interveneBeginTime >= interveneEndTime) {
            throw new BizException(PARAM_INVALID, "干预开始时间大于等于干预结束时间");
        }
    }

    /**
     * 获取用户记录实体
     */
    private UserRecordEntity getUserRecordEntity(long userId, long activityId, List<Long> resourceParentIdList) {
        // 用户活动记录
        UserActivityRecordDO userActivityRecord =
                userActivityRecordDAO.queryUserActivityRecord(userId, activityId, true);
        // 用户当前只能参与一个活动下的一个和规则绑定的父任务
        List<UserTaskRecordDO> activityUserTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, true);
        // 确认参与父任务ID
        List<UserTaskRecordDO> resourceUserParentTaskRecordList = activityUserTaskRecordList.stream()
                .filter(e -> resourceParentIdList.contains(e.getTaskId())).collect(Collectors.toList());
        if (resourceUserParentTaskRecordList.size() != 1) {
            log.error("[获取用户补贴关联记录][补贴规则下用户记录数量异常] size:{}",
                    resourceUserParentTaskRecordList.size());
            throw new BizException(USER_ACTIVITY_DRAW_TASK_COUNT_ERROR);
        }
        long userJoinParentId = resourceUserParentTaskRecordList.get(0).getTaskId();
        // 捞出父任务记录和子任务记录
        List<UserTaskRecordDO> userTaskRecordList = activityUserTaskRecordList.stream()
                .filter(e -> e.getTaskId() == userJoinParentId || e.getParentId() == userJoinParentId)
                .collect(Collectors.toList());
        // 任务ID列表
        List<Long> userJoinTaskIdList = userTaskRecordList.stream()
                .map(UserTaskRecordDO::getTaskId).collect(Collectors.toList());
        // 用户指标记录
        List<IndicatorRecordDO> activityUserIndicatorRecordList =
                indicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        // 用户规则指标记录
        List<IndicatorRecordDO> userIndicatorRecordList = activityUserIndicatorRecordList.stream()
                .filter(e -> userJoinTaskIdList.contains(e.getEntityId()))
                .collect(Collectors.toList());
        // 用户奖励记录
        List<UserAwardRecordDO> activityUserAwardRecordList =
                userAwardRecordDAO.listUserActivityRecord(userId, activityId, true);
        // 用户规则奖励记录
        List<UserAwardRecordDO> userAwardRecordList = activityUserAwardRecordList.stream()
                .filter(e -> userJoinTaskIdList.contains(e.getEntityId()))
                .collect(Collectors.toList());

        return UserRecordEntity.builder()
                .userActivityRecord(userActivityRecord)
                .userTaskRecordList(userTaskRecordList)
                .userIndicatorRecordList(userIndicatorRecordList)
                .userAwardRecordList(userAwardRecordList)
                .build();
    }

    /**
     * 根据补贴活动规则信息反查任务系统实体
     */
    private ActivityTaskEntity getActivityTaskEntityByResourceInfo(long resourceActivityId, long resourceRuleId) {
        // 反查活动id
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO == null) {
            throw new BizException(ACTIVITY_NOT_EXIST, "补贴关联活动不存在");
        }
        long activityId = activityDO.getId();
        long seriesType = activityDO.getSeriesType();

        // 查规则下的任务列表
        List<TaskDO> taskList =
                taskLocalCacheService.getTaskListByResourceRuleIdAndActivityId(resourceRuleId, activityId);
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BizException(ACTIVITY_RESOURCE_RULE_NOT_EXIST, "补贴规则关联任务不存在");
        }
        // 父任务列表
        List<Long> parentTaskIdList = taskList.stream()
                .filter(e -> e.getParentTask() == 0).map(BaseDO::getId).collect(Collectors.toList());
        // 子任务列表
        List<Long> childTaskIdList = taskList.stream()
                .filter(e -> e.getParentTask() != 0).map(BaseDO::getId).collect(Collectors.toList());

        return ActivityTaskEntity.builder()
                .activityId(activityId)
                .seriesType(seriesType)
                .parentTaskIdList(parentTaskIdList)
                .childTaskIdList(childTaskIdList)
                .build();
    }

    @Override
    public void terminateUserResourceRule(TerminateUserResourceRuleRequest request) {
        checkParam(request);

        long userId = request.getUserId();
        long resourceActivityId = request.getResourceActivityId();
        long resourceRuleId = request.getResourceRuleId();

        // 反查任务系统信息
        ActivityTaskEntity activityTaskEntity = getActivityTaskEntityByResourceInfo(resourceActivityId, resourceRuleId);
        if (activityTaskEntity.getSeriesType() != ActivitySeriesType.ACTIVITY_REALTIME_INVEST.getValue()) {
            log.error("[用户规则终止] 目前仅支持实时对投类型活动，request:{}", toJSON(request));
            throw new BizException(PARAM_INVALID, "目前仅支持实时对投类型活动");
        }
        // 反查用户任务和指标奖励记录
        UserRecordEntity userRecordEntity = getUserRecordEntity(userId, activityTaskEntity.getActivityId(),
                activityTaskEntity.getParentTaskIdList());
        // 事务终止
        activityManageTransactionService.terminateUserResourceRule(userRecordEntity);
    }

    @Override
    public void terminateUserRecordByParentId(long userId, long activityId, long parentTaskId) {
        // 反查用户任务和指标奖励记录
        UserRecordEntity userRecordEntity = getUserRecordEntity(userId, activityId, singletonList(parentTaskId));
        // 事务终止
        activityManageTransactionService.terminateUserResourceRule(userRecordEntity);
    }

    private List<Long> getCanJoinParentTaskIdList(long userId, long activityId, long seriesType, long startTime,
            JoinResourceTaskRequest request, List<TaskDO> taskList) {
        // 对于实时对投活动，用户只能参与一个父任务
        // 除了实时对投，其余活动不能参与一次以上
        List<Long> parentTaskIdList;
        if (seriesType == ActivitySeriesType.ACTIVITY_REALTIME_INVEST.getValue()) {
            // 实时对投计算当次alias
            String taskAlias = getUserRealtimeInvestTaskAlias(request);
            parentTaskIdList = taskList.stream().filter(e -> e.getParentTask() == 0)
                    .filter(e -> e.getEndTime() > startTime)
                    .filter(e -> taskAlias.equals(e.getAlias()))
                    .map(TaskDO::getId).collect(Collectors.toList());
            if (parentTaskIdList.size() != 1) {
                log.error("[参与补贴规则] 对投活动参与父任务数量不对，parentTaskIdList:{}-{}", parentTaskIdList,
                        taskAlias);
                throw new BizException(USER_ACTIVITY_DRAW_TASK_COUNT_ERROR);
            }
            // 查用户是否已经参与了父任务
            UserTaskRecordDO userTaskRecordDO =
                    userTaskRecordDAO.queryUserTaskRecord(userId, activityId, parentTaskIdList.get(0), true);
            if (userTaskRecordDO != null) {
                log.error("[参与补贴规则] 用户已经参与该规则，parentTaskIdList:{}-{}", parentTaskIdList, taskAlias);
                throw new BizException(USER_ACTIVITY_ALREADY_DRAW, "用户已参与规则");
            }
            return parentTaskIdList;
        }
        // 查用户是否已经参与了该补贴活动
        UserActivityRecordDO userActivityRecordDO =
                userActivityRecordDAO.queryUserActivityRecord(userId, activityId, true);
        if (userActivityRecordDO != null) {

            throw new BizException(USER_ACTIVITY_ALREADY_DRAW, "用户已参与活动");
        }
        // 可参与父任务ID
        parentTaskIdList = taskList.stream().filter(e -> e.getParentTask() == 0)
                .filter(e -> e.getEndTime() > startTime)
                .map(TaskDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentTaskIdList)) {
            log.error("[参与补贴规则] 无领取任务，activityId:{}, parentIdList:{}", activityId, toJSON(taskList));
            throw new BizException(USER_ACTIVITY_TASK_NOT_EXIST, "该活动规则下不存在可领取的任务");
        }
        return parentTaskIdList;
    }

    /**
     * 获取用户可参与任务alias
     */
    private String getUserRealtimeInvestTaskAlias(JoinResourceTaskRequest request) {
        long resourceActivityId = request.getResourceActivityId();
        long resourceRuleId = request.getResourceRuleId();
        if (CollectionUtils.isEmpty(request.getVariableList())) {
            log.error("[实时对投alias获取] 传入变量列表不能为空！request:{}", toJSON(request));
            throw new BizException(PARAM_INVALID, "变量列表为空");
        }
        long indicatorId = request.getVariableList().get(0).getIndicatorId();
        long stageNum = request.getVariableList().stream().filter(e -> e.getIndicatorId() == indicatorId).count();
        int awardType = request.getAwardType();
        String prefix = "invest";
        List<Long> indicatorList = request.getVariableList().stream()
                .map(DrawTaskVariableDTO::getIndicatorId)
                .distinct()
                .sorted(Comparator.comparing(e -> e))
                .collect(Collectors.toList());
        String indicatorInfo = Joiner.on("-").join(indicatorList);
        return Joiner.on("_").join(prefix, resourceActivityId, resourceRuleId, stageNum, awardType, indicatorInfo);
    }

    /**
     * 领取补贴活动
     */
    private void batchDrawResourceActivity(long userId, long activityId, List<Long> taskIdList,
            long startTime, long endTime) {
        DrawTaskParam drawTaskParam = DrawTaskParam.builder()
                .startTime(startTime)
                .endTime(endTime)
                .build();
        userActivityDrawService.batchDrawTaskWithParam(userId, activityId, taskIdList,
                RESOURCE, StringUtils.EMPTY, drawTaskParam);
    }

    @Override
    public void sendUserResourceActivityAward(SendUserResourceActivityAwardRequest request) {
        StopWatch cost = new StopWatch();
        checkParam(request);

        // 反查活动id
        long resourceActivityId = request.getResourceActivityId();
        long interestConfigId = request.getInterestConfigId();
        List<Long> userIdList = request.getUserIdList();
        ActivityDO activityDO = activityLocalCacheService.queryActivityByResourceActivityIdAndType(
                resourceActivityId, ACTIVITY_TYPE_TASK.getValue());
        if (activityDO == null) {
            throw new BizException(ACTIVITY_NOT_EXIST, "活动不存在");
        }
        long activityId = activityDO.getId();

        // todo 查询权益配置id是否存在

        //发放奖励
        awardExecuteService.batchSendAwardWithExtParam(TASK, interestConfigId, activityId, userIdList);
        perfSuccessWithWatch(MANAGE_SEND_USER_RESOURCE_ACTIVITY_AWARD, cost);

    }

    Map<String, Object> buildResourceActivityVariables(CreateResourceActivityRequest request) {
        long resourceActivityId = request.getResourceActivityId();
        String activityName = request.getActivityName();
        long startTime = request.getStartTime();
        long endTime = request.getEndTime();
        //插入模版变量数据
        Map<String, Object> variables = new HashMap<>();
        variables.put("resourceActivityId", resourceActivityId);
        variables.put("activityName", activityName);
        variables.put("startTime", startTime);
        variables.put("endTime", endTime);
        variables.put("currentTime", System.currentTimeMillis());
        return variables;
    }

    Map<String, Object> buildResourceTaskVariables(CreateResourceTaskRequest request) {
        List<Long> indicatorIdList = request.getVariable().getIndicatorIdList();
        // 排序
        List<Long> sortedIndicatorList = indicatorIdList.stream()
                .sorted(Comparator.comparing(e -> e)).collect(Collectors.toList());
        // 组装
        String indicatorList = Joiner.on("-").join(sortedIndicatorList);
        // 插入模版变量数据
        Map<String, Object> variables = new HashMap<>();
        variables.put("resourceActivityId", request.getResourceActivityId());
        variables.put("resourceRuleId", request.getResourceRuleId());
        variables.put("taskName", request.getTaskName());
        variables.put("startTime", request.getStartTime());
        variables.put("endTime", request.getEndTime());
        variables.put("stageNum", request.getVariable().getStageNum());
        variables.put("awardType", request.getVariable().getAwardType());
        variables.put("indicatorList", indicatorList);
        return variables;
    }

    @Override
    public long queryAndCreateInterestConfig(int awardType, long awardExpireTime, String fundBudgetId) {
        // 查询权益配置是否存在
        InterestConfigAdminDTO interestConfigAdminDTO =
                interestFetchService.queryInterestConfig(TASK, awardType, fundBudgetId, awardExpireTime);
        if (interestConfigAdminDTO != null && interestConfigAdminDTO.getId() > 0) {
            // 权益配置已存在，返回权益配置id
            return interestConfigAdminDTO.getId();
        }
        // redisKey拼接
        String redisKey = MANAGE_INTEREST_CREATE.getFullKeyJoinWithColon(TASK, String.valueOf(awardType), fundBudgetId,
                String.valueOf(awardExpireTime));
        // 先查一次redis
        String redisValue = RedisDataSource.getGrowthRedisCommands().get(redisKey);
        if (StringUtils.isNotBlank(redisValue)) {
            log.warn("[实时对投奖励配置创建] 查询到已创建有奖励配置，使用缓存的配置！redisKey:{}, redisVal:{}", redisKey,
                    redisValue);
            return Long.parseLong(redisValue);
        }
        // 创建权益
        long interestConfigId =
                interestFetchService.createInterestConfig(TASK, awardType, fundBudgetId, awardExpireTime);
        // 尝试setNx,过期时间为1小时
        String res = RedisDataSource.getGrowthRedisCommands().set(redisKey, String.valueOf(interestConfigId),
                SetArgs.Builder.nx().ex(investAwardConfigCreateExpireTime.get()));
        if ("OK".equals(res)) {
            return interestConfigId;
        }
        // 不然就说明被创建过ID, 抛出去让上游重试
        redisValue = RedisDataSource.getGrowthRedisCommands().get(redisKey);
        if (!redisValue.equals(String.valueOf(interestConfigId))) {
            log.error("[实时对投奖励配置创建] 奖励配置创建前后不一！！！redisKey:{}, redisVal:{}，interestConfigId:{}",
                    redisKey, redisValue,
                    interestConfigId);
            throw new BizException(SERVER_ERROR, "实时对投创建失败");
        }
        // 创建权益配置
        return interestConfigId;
    }
}
