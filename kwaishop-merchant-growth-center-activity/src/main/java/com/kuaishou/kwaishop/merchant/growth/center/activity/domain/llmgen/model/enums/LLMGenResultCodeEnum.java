package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-23
 */
@Getter
@AllArgsConstructor
public enum LLMGenResultCodeEnum {
    UNKNOWN(0, "未知"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    ;

    private final int code;

    private final String desc;

    public static LLMGenResultCodeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(UNKNOWN);
    }
}
