package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.convert.ConverterUtil.convertNotNull;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.AGG_BASIC_FORMAT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.AGG_CARDINALITY_FORMAT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.AGG_FILTER_FORMAT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.AGG_NESTED_FORMAT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.AGG_TERMS_FORMAT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.CUSTOMIZE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter.replaceKey;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory.mergeSamePathNestedCondition;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityIntegerConfigKey.cardinalityPrecisionThreshold;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ActivityRiskStatisticSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerDimStatisticSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerPolicySignUpSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.EsAggregationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.TermsAggData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.TermsBucket;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Slf4j
public class AggBuildFactory {

    private static final String DRAW = "1";
    private static final String NO_DRAW = "0";

    private static final String RISK = "1";
    private static final String UN_RISK = "0";


    /**
     * 根据字段构建AggJson
     */
    public static String buildAggJson(String aggType, String aggField, String aggJson, String field,
                                      boolean nestedQuery,
                                      List<StatisticsConditionBO> conditions, Map<String, Object> entityParam) {
        if (CUSTOMIZE.equals(aggType)) {
            return aggJson;
        }
        // nested，相同path下的condition合并
        conditions = mergeSamePathNestedCondition(conditions);
        // 需要在agg的filter里过滤的nested条件
        StatisticsConditionBO aggFilterNestedCondition = conditions.stream()
                .filter(e -> e.getConditionType().equals(ConditionTypeEnum.NESTED.getCode()))
                .findFirst().orElse(null);
        String aggStr = buildAggJson(EsAggregationTypeEnum.getByType(aggType), aggField, field);
        if (aggFilterNestedCondition == null || !nestedQuery) {
            return aggStr;
        }
        for (StatisticsConditionBO subCondition : aggFilterNestedCondition.getConditions()) {
            String filterField = subCondition.getFieldName();
            String filterType = subCondition.getConditionType();
            String filterValue = String.valueOf(replaceKey(subCondition.getConditionValue(), entityParam));
            aggStr = String.format(AGG_FILTER_FORMAT, field, filterType, filterField, filterValue, aggStr);
        }
        String path = aggFilterNestedCondition.getFieldName();
        aggStr = String.format(AGG_NESTED_FORMAT, field, path, aggStr);
        return aggStr;
    }

    public static String buildAggJson(EsAggregationTypeEnum aggType, String aggField, String aggName) {
        if (EsAggregationTypeEnum.CUSTOMIZE.equals(aggType) || aggType == null) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID.getCode(), "aggType参数错误");
        }
        // terms agg
        if (EsAggregationTypeEnum.TERMS.equals(aggType)) {
            return String.format(AGG_TERMS_FORMAT, aggName, EsAggregationTypeEnum.TERMS.getType(), aggField);
        }
        // cardinality agg
        if (EsAggregationTypeEnum.CARDINALITY.equals(aggType)) {
            return String.format(AGG_CARDINALITY_FORMAT, aggName, EsAggregationTypeEnum.CARDINALITY.getType(), aggField,
                    cardinalityPrecisionThreshold.get());
        }
        return String.format(AGG_BASIC_FORMAT, aggName, aggType.getType(), aggField);
    }

    public static String buildAggJson(List<TermsAggregationBuild> aggregationBuildList) {
        if (CollectionUtils.isEmpty(aggregationBuildList)) {
            return null;
        }
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode rootNode = mapper.createObjectNode();
        checkAggBuild(aggregationBuildList);
        try {
            for (TermsAggregationBuild termsAggregationBuild : aggregationBuildList) {
                ObjectNode aggregationNode = mapper.createObjectNode();
                EsAggregationTypeEnum type = termsAggregationBuild.getType();
                String name = termsAggregationBuild.getName();
                String field = termsAggregationBuild.getField();
                List<TermsAggregationBuild> aggregations = termsAggregationBuild.getAggregations();
                ObjectNode node = mapper.createObjectNode();
                node.put("field", field);
                aggregationNode.set(type.getType(), node);

                // 处理子聚合
                if (CollectionUtils.isNotEmpty(aggregations)) {
                    ObjectNode subAggNode = mapper.createObjectNode();
                    for (TermsAggregationBuild subAgg : aggregations) {
                        String subName = subAgg.getName();
                        String subAggJson = buildAggJson(List.of(subAgg));
                        if (subAggJson != null) {
                            // 直接将子聚合的内容解析为节点
                            subAggNode.set(subName, mapper.readTree(subAggJson).get(subName));
                        }
                    }
                    aggregationNode.set("aggregations", subAggNode);
                }
                rootNode.set(name, aggregationNode);
            }
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
        } catch (Exception e) {
            log.error("buildAggJson happen error build {}", toJSON(aggregationBuildList), e);
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID.getCode(), "聚合json解析错误");
        }
    }

    public static Long parseTermsResultByAggType(EsAggregationTypeEnum aggregationType, Map<String,
            TermsAggData> aggregationsMap, String saveField) {
        if (EsAggregationTypeEnum.CARDINALITY.equals(aggregationType)) {
            return aggregationsMap.get(saveField).getValue();
        } else if (EsAggregationTypeEnum.TERMS.equals(aggregationType)) {
            Long sumOtherDocCount = aggregationsMap.get(saveField).getSumOtherDocCount();
            if (sumOtherDocCount == null) {
                return null;
            }
            // terms size:1 这里需要+1
            return sumOtherDocCount + 1;
        }
        throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "聚合类型暂时不支持");
    }

    public static Map<Long, SellerPolicySignUpSummary> parseTermsResult(String policySignUpTermsAggPath, Map<String,
            TermsAggData> aggregationsMap) {
        String[] pathArr = policySignUpTermsAggPath.split("\\.");
        if (pathArr.length != 2) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "小二政策库配置path异常");
        }
        String rootPath = pathArr[0];
        TermsAggData terms = aggregationsMap.get(rootPath);
        if (terms == null) {
            return Maps.newHashMap();
        }
        Map<Long, SellerPolicySignUpSummary> sellerSignUpSummaryMap = Maps.newHashMap();
        for (TermsBucket bucket : terms.getBuckets()) {
            Long activityId = Long.valueOf(bucket.getKey());
            TermsAggData agg = bucket.getAgg();
            SellerPolicySignUpSummary sellerPolicySignUpSummary = new SellerPolicySignUpSummary();
            sellerPolicySignUpSummary.setActivityId(activityId);
            Map<Object, Long> bucketMap = agg.getBuckets().stream()
                    .collect(Collectors.toMap(TermsBucket::getKey, TermsBucket::getDocCount, (o1, o2) -> o1));
            sellerPolicySignUpSummary.setNoSignUpCount(convertNotNull(bucketMap.get(NO_DRAW)));
            sellerPolicySignUpSummary.setSignedUpCount(convertNotNull(bucketMap.get(DRAW)));
            sellerSignUpSummaryMap.put(activityId, sellerPolicySignUpSummary);
        }
        return sellerSignUpSummaryMap;
    }

    /**
     * 解析活动风控聚合结果
     * @param riskTermsAggPath
     * @param aggregationsMap
     * @return
     */
    public static Map<Long, ActivityRiskStatisticSummary> parseActivityRiskTermsResult(String riskTermsAggPath,
                                                                                       Map<String,
                                                                                               TermsAggData> aggregationsMap) {
        String[] pathArr = riskTermsAggPath.split("\\.");
        if (pathArr.length != 2) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "小二政策库配置path异常");
        }
        String rootPath = pathArr[0];
        TermsAggData terms = aggregationsMap.get(rootPath);
        if (terms == null) {
            return Maps.newHashMap();
        }
        Map<Long, ActivityRiskStatisticSummary> riskStatisticSummaryMap = Maps.newHashMap();
        for (TermsBucket bucket : terms.getBuckets()) {
            Long activityId = Long.valueOf(bucket.getKey());
            TermsAggData agg = bucket.getAgg();
            ActivityRiskStatisticSummary activityRiskStatisticSummary = new ActivityRiskStatisticSummary();
            activityRiskStatisticSummary.setActivityId(activityId);
            Map<Object, Long> bucketMap = agg.getBuckets().stream()
                    .collect(Collectors.toMap(TermsBucket::getKey, TermsBucket::getDocCount, (o1, o2) -> o1));
            activityRiskStatisticSummary.setRiskSellerNum(convertNotNull(bucketMap.get(RISK)));
            activityRiskStatisticSummary.setUnRiskSellerNum(convertNotNull(bucketMap.get(UN_RISK)));
            riskStatisticSummaryMap.put(activityId, activityRiskStatisticSummary);
        }
        return riskStatisticSummaryMap;
    }

    public static Map<Long, SellerDimStatisticSummary> parseTermsResult(String sellerSignUpTermsAggPath, Map<String,
            TermsAggData> sellerSignUpAggregationsMap, String sellerSignUpFinishTermsAggPath, Map<String, TermsAggData>
                                                                                sellerSignUpFinishAggregationsMap,
                                                                        Long progressActivityCnt) {
        Map<Long, SellerDimStatisticSummary> sellerDimStatisticSummaryMap = Maps.newHashMap();

        try {
            String[] pathArr = sellerSignUpTermsAggPath.split("\\.");
            if (pathArr.length != 2) {
                throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "小二政策库配置sellerSignUpTermsAggPath异常");
            }
            String rootPath = pathArr[0];
            TermsAggData terms = sellerSignUpAggregationsMap.get(rootPath);
            if (terms == null) {
                return Maps.newHashMap();
            }
            String[] finishPathArr = sellerSignUpFinishTermsAggPath.split("\\.");
            TermsAggData finishTerm = sellerSignUpFinishAggregationsMap.get(finishPathArr[0]);
            Map<Object, Long> userFinishResMap = Maps.newHashMap();
            if (finishTerm != null && CollectionUtils.isNotEmpty(finishTerm.getBuckets())) {
                userFinishResMap = finishTerm.getBuckets().stream()
                        .collect(Collectors.toMap(TermsBucket::getKey, TermsBucket::getDocCount, (o1, o2) -> o1));
            }
            for (TermsBucket bucket : terms.getBuckets()) {
                Object userIdObj = bucket.getKey();
                Long userId = Long.valueOf(userIdObj.toString());
                Long userFinishCnt = userFinishResMap.get(userIdObj) == null ? 0L : userFinishResMap.get(userIdObj);
                List<TermsBucket> buckets = bucket.getAgg().getBuckets();
                //            Terms subTerm = (Terms) bucket.getAggregations().asMap().get(pathArr[1]);
                SellerDimStatisticSummary sellerDimStatisticSummary = new SellerDimStatisticSummary();
                sellerDimStatisticSummary.setUserId(userId);
                Map<Object, Long> bucketMap = buckets.stream()
                        .collect(Collectors.toMap(TermsBucket::getKey, TermsBucket::getDocCount, (o1, o2) -> o1));
                sellerDimStatisticSummary.setWaitSignUpActivityCnt(convertNotNull(bucketMap.get(NO_DRAW)));
                Long drawActivityCnt = convertNotNull(bucketMap.get(DRAW));
                sellerDimStatisticSummary.setHadSignUpActivityCnt(drawActivityCnt);
                sellerDimStatisticSummary.setProgressActivityCnt(drawActivityCnt + sellerDimStatisticSummary.getWaitSignUpActivityCnt());
                if (progressActivityCnt != null) {
                    sellerDimStatisticSummary.setProgressActivityCnt(progressActivityCnt);
                }
                // 待完成活动数 = 已参与活动数 - 已完成活动数
                long waitFinishActivityCnt = drawActivityCnt - userFinishCnt;
                sellerDimStatisticSummary.setWaitFinishActivityCnt(waitFinishActivityCnt > 0 ? waitFinishActivityCnt
                        : 0);
                sellerDimStatisticSummaryMap.put(userId, sellerDimStatisticSummary);
            }
        } catch (Exception e) {
            log.error("parseTermsResult error", e);
        }
        return sellerDimStatisticSummaryMap;
    }

    public static void main(String[] args) {
    }

    /**
     * 校验聚合参数
     * 1. name、field、type不能为空
     *
     * @param aggregationBuildList
     */
    private static void checkAggBuild(List<TermsAggregationBuild> aggregationBuildList) {
        long invalidCnt = aggregationBuildList.stream()
                .filter(TermsAggregationBuild::invalid)
                .count();
        if (invalidCnt > 0) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID.getCode(), "聚合参数错误");
        }
    }
}
