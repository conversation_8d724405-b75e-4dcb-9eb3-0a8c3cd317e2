package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.AwardResolver.multiAwardConfigContainsAwardTrReturnType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.RoiConvert.buildStepDimensionData;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.RoiConvert.convertBaseInfo;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.roiCalcCrowdPartitionSize;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.IndustryActivityBuildService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ImmediateRoiExecuteConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.ImmediateRoiBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiLevelAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRoiService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.InterestFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.DistributorDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.BaseInfo;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerSingleDimensionData;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-16
 */
@Service
@Slf4j
@Lazy
public class ImmediateRoiExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private InterestFetchService interestFetchService;

    @Autowired
    private StatisticsRoiService statisticsRoiService;

    @Autowired
    private StatisticsCacheService statisticsCacheService;

    @Autowired
    private StatisticsMsgProduceService msgProduceService;

    @Autowired
    private IndustryActivityBuildService industryActivityBuildService;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        // 事件强转
        ImmediateRoiBatchExecuteEvent immediateRoiEvent = (ImmediateRoiBatchExecuteEvent) event;
        String eventId = immediateRoiEvent.getEventId();
        // 分层配置信息
        RoiActivityLevelConfigBO layerConfig = immediateRoiEvent.getLayerConfig();
        // 分层人群构建
        TaskDO layerParentTask = layerConfig.getLayerParentTask();
        Set<Long> layerCrowd = industryActivityBuildService
                .getSellerByCrowdTypeAndAb(layerParentTask.getCrowdType(), layerParentTask.getCrowdConfig());
        // 分层缓存构建
        statisticsRoiService.buildRoiLayerCache(layerConfig, eventId);
        return layerCrowd;
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        ImmediateRoiExecuteConfigBO executeConfigBO = fromJSON(executeConfig, ImmediateRoiExecuteConfigBO.class);
        ExecuteHandleResult result = new ExecuteHandleResult();
        boolean stop = interestFetchService.queryRoiCalcIsStop(eventId);
        if (stop) {
            log.info("[即时ROI计算] 计算已被终止！eventId:{}, userIdList:{}", eventId, userIdList);
            return result;
        }
        // 总人数
        long totalNum = statisticsCacheService.getBatchExecuteTotalNum(eventId);
        // 批量查询基值
        Map<Long, Map<String, Object>> userSystemCalcBaseMap =
                statisticsRoiService.batchGetLayerUserBaseValue(userIdList, executeConfigBO.getLayerConfig());
        // 如果奖励返点涉及按TR增量返点，需要额外查询TR和退款率
        Map<Long, DistributorDataBO> userTrReturnBaseData = Maps.newHashMap();
        if (multiAwardConfigContainsAwardTrReturnType(executeConfigBO.getLayerConfig().getRoiAwardConfigs())) {
            userTrReturnBaseData = statisticsRoiService
                    .batchGetLayerUserTrReturnBaseValue(userIdList, executeConfigBO.getLayerConfig());
        }
        // 对于每个用户
        Map<Long, DistributorDataBO> finalUserTrReturnBaseData = userTrReturnBaseData;
        userIdList.forEach(userId -> singleUserExecute(userId, eventId, executeConfigBO.getLayerConfig(), totalNum,
                userSystemCalcBaseMap, finalUserTrReturnBaseData));
        // 处理结果构建
        result.setSuccessUserList(userIdList);
        return result;
    }

    private void singleUserExecute(Long userId, String eventId, RoiActivityLevelConfigBO layerConfig, long totalNum,
            Map<Long, Map<String, Object>> userSystemCalcBaseMap, Map<Long, DistributorDataBO> userTrReturnBaseData) {
        // 用户分层信息
        UserRoiLevelAggregationInfoBO levelAggregationInfo = statisticsRoiService
                .getUserRoiLevelAggregationInfo(userId, eventId, layerConfig, userSystemCalcBaseMap.get(userId),
                        userTrReturnBaseData.get(userId));
        // 对象转换发送消息
        List<SellerSingleDimensionData> singleDimensionData =
                buildSellerSingleDimensionData(levelAggregationInfo);
        msgProduceService.sendUserRoiCalcMsg(userId, eventId, totalNum, singleDimensionData);
    }

    private List<SellerSingleDimensionData> buildSellerSingleDimensionData(UserRoiLevelAggregationInfoBO levelInfo) {
        List<SellerSingleDimensionData> res = Lists.newArrayList();
        // 分层基值配置
        BaseInfo layerBasicConfig = convertBaseInfo(levelInfo.getBasicConfig());
        // 各阶段
        levelInfo.getUserRoiStepAggregationInfos().forEach(stepInfo -> res
                .add(buildStepDimensionData(layerBasicConfig, StringUtils.EMPTY, stepInfo, levelInfo)));
        return res;
    }

    @Override
    protected String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 事件强转
        ImmediateRoiBatchExecuteEvent immediateRoiEvent = (ImmediateRoiBatchExecuteEvent) event;
        // 数据组装
        ImmediateRoiExecuteConfigBO executeConfig = new ImmediateRoiExecuteConfigBO();
        executeConfig.setLayerConfig(immediateRoiEvent.getLayerConfig());
        return toJSON(executeConfig);
    }

    @Override
    protected int getPartitionSize() {
        return roiCalcCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.IMMEDIATE_ROI;
    }
}
