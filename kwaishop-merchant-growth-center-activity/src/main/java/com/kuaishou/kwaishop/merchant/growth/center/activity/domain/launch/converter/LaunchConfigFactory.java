package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.enums.ExpSceneTypeEnum.LAUNCH_EXP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigFrontStatusEnum.aggFrontSceneStatus;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigFrontStatusEnum.resolveSceneReleaseStatus;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigFrontStatusEnum.resolveSceneStatus;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCommonConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.biz.ExperimentBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.bo.ExpBucketConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.bo.ExpBucketItemConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.bo.ExpDiversionRatioBucketRuleBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.bo.ExpRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.bo.ExperimentConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.enums.ExpBucketTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.enums.ExperimentRuleTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.enums.ExperimentStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.param.ExpKeyGenerateParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.experiment.model.param.RatioExpConfigCreateParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFixContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigGrayConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigPriorityConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldsWithExpConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchExpConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchRuleItemConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigFrontStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigReleaseStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchContentTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchTimeRangeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchChannelAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchExperimentAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFieldContentAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFormAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFormFieldAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFormWithExpAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchOperateAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchRuleAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchSceneAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchStatusExtAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchTimeRangeAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.rule.module.LaunchEntityFilterRuleContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.rule.module.LaunchRuleExecuteOperatorTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.rule.module.LaunchRuleTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.rule.module.LaunchTimeRangeFilterRuleContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.launch.LaunchConfigDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-18
 */
@Component
@Slf4j
public class LaunchConfigFactory {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private ExperimentBizService experimentBizService;

    private static final Integer DEFAULT_CHANNEL_PRIORITY = 99;

    // 时间戳位数（毫秒级）
    private static final int TIMESTAMP_BITS = 42; // 约139年范围
    // 随机数位数
    private static final int RANDOM_BITS = 22; // 最多约400万随机值

    public List<LaunchConfigBO> buildLaunchConfig(Long activityId,
            LaunchConfigResourceTypeEnum launchConfigResourceTypeEnum,
            LaunchAdminProtocol protocol, String operator) {
        if (protocol == null) {
            return Lists.newArrayList();
        }

        LaunchOperateAdminProtocol opeAdminProtocol = protocol.getLaunchOperateAdminProtocol();

        List<LaunchConfigBO> result = Lists.newArrayList();

        // 遍历渠道
        CollectionUtils.emptyIfNull(protocol.getLaunchChannelAdminProtocols()).forEach(channelProtocol -> {
            if (channelProtocol == null) {
                return;
            }
            String channelCode = channelProtocol.getChannelCode();
            List<Long> selectedEntityIds = channelProtocol.getSelectedEntityIds();

            // 渠道维度开关
            Boolean channelEnableStatus = channelProtocol.getEnableStatus();
            if (BooleanUtils.isNotTrue(channelEnableStatus)
                    && CollectionUtils.isEmpty(channelProtocol.getSelectedEntityIds())) {
                return;
            }

            Integer priority =
                    channelProtocol.getPriority() == null ? DEFAULT_CHANNEL_PRIORITY : channelProtocol.getPriority();

            // 遍历场景
            CollectionUtils.emptyIfNull(channelProtocol.getLaunchSceneAdminProtocols()).forEach(sceneProtocol -> {
                processSingleScene(activityId, launchConfigResourceTypeEnum, operator, result, channelProtocol,
                        channelCode, selectedEntityIds, channelEnableStatus, priority, sceneProtocol, opeAdminProtocol);
            });
        });

        return result;
    }

    @SuppressWarnings("checkstyle:ParameterNumber")
    private void processSingleScene(Long activityId, LaunchConfigResourceTypeEnum launchConfigResourceTypeEnum,
            String operator, List<LaunchConfigBO> result, LaunchChannelAdminProtocol channelProtocol,
            String channelCode, List<Long> selectedEntityIds, Boolean channelEnableStatus, Integer priority,
            LaunchSceneAdminProtocol sceneProtocol, LaunchOperateAdminProtocol opeAdminProtocol) {
        String sceneCode = sceneProtocol.getSceneCode();
        EntityTypeEnum entityTypeEnum = launchResolveService.resolveEntityTypeByScene(channelCode, sceneCode);

        Boolean sceneEnableStatus = sceneProtocol.getEnableStatus();
        LaunchConfigStatusEnum status;
        if (BooleanUtils.isNotTrue(channelEnableStatus)) {
            status = LaunchConfigStatusEnum.DISABLE;
        } else {
            status = sceneEnableStatus == null || !sceneEnableStatus ? LaunchConfigStatusEnum.DISABLE
                                                                     : LaunchConfigStatusEnum.ENABLE;
        }
        LaunchConfigReleaseStatusEnum releaseStatus = LaunchConfigReleaseStatusEnum.AUDIT_SUCCEED;

        LaunchConfigExtBO ext = LaunchConfigExtBO.builder()
                .selected(BooleanUtils.isTrue(sceneEnableStatus))
                .channelSelected(channelEnableStatus)
                .priorityConfig(LaunchConfigPriorityConfigBO.builder().priority(priority).build())
                .build();

        List<LaunchRuleItemConfigBO> ruleItemConfigs = Lists.newArrayList();

        // 时间范围规则
        LaunchTimeRangeAdminProtocol launchTimeRangeAdminProtocol =
                channelProtocol.getLaunchTimeRangeAdminProtocol();
        if (launchTimeRangeAdminProtocol != null) {
            LaunchTimeRangeFilterRuleContentBO timeRangeFilterRuleContent =
                    LaunchTimeRangeFilterRuleContentBO.builder()
                            .timeRangeType(launchTimeRangeAdminProtocol.getTimeRangeType())
                            .customizeLaunchEndTime(launchTimeRangeAdminProtocol.getCustomizeLaunchEndTime())
                            .build();
            LaunchRuleItemConfigBO launchRuleItemConfig = LaunchRuleItemConfigBO.builder()
                    .ruleType(LaunchRuleTypeEnum.TIME_RANGE_FILTER_RULE.getType())
                    .ruleContent(toJSON(timeRangeFilterRuleContent))
                    .operatorType(LaunchRuleExecuteOperatorTypeEnum.AND.getType())
                    .build();
            ruleItemConfigs.add(launchRuleItemConfig);
        }

        LaunchExperimentAdminProtocol launchExperimentAdminProtocol =
                sceneProtocol.getLaunchExperimentAdminProtocol();
        ExperimentConfigBO experimentConfig = null;
        if (launchExperimentAdminProtocol != null && launchExperimentAdminProtocol.getExperimentSwitch()) {
            experimentConfig = saveExperimentConfig(activityId,
                    channelCode, sceneCode, launchExperimentAdminProtocol, operator, opeAdminProtocol);
            LaunchExpConfigBO launchExpConfig = LaunchExpConfigBO.builder()
                    .experimentKey(experimentConfig.getExperimentKey())
                    .build();
            LaunchRuleItemConfigBO launchRuleItemConfig = LaunchRuleItemConfigBO.builder()
                    .ruleType(LaunchRuleTypeEnum.EXPERIMENT_RULE.getType())
                    .ruleContent(toJSON(launchExpConfig))
                    .operatorType(LaunchRuleExecuteOperatorTypeEnum.AND.getType())
                    .build();
            ruleItemConfigs.add(launchRuleItemConfig);
        }

        // 创建投放内容配置
        LaunchContentConfigBO contentConfig = buildContentConfig(sceneProtocol, experimentConfig);

        // 新逻辑：根据不同的配置的实体类型，组装launchConfig
        switch (entityTypeEnum) {
            case ACTIVITY:

                LaunchEntityFilterRuleContentBO entityFilterRuleContent =
                        LaunchEntityFilterRuleContentBO.builder()
                                .matchEntityType(EntityTypeEnum.SUB_ACTIVITY.getCode())
                                .matchEntityIds(selectedEntityIds)
                                .build();
                ruleItemConfigs.add(LaunchRuleItemConfigBO.builder()
                        .ruleType(LaunchRuleTypeEnum.ENTITY_FILTER_RULE.getType())
                        .ruleContent(toJSON(entityFilterRuleContent))
                        .operatorType(LaunchRuleExecuteOperatorTypeEnum.AND.getType())
                        .build());
                LaunchRuleConfigBO launchRuleConfig = LaunchRuleConfigBO.builder()
                        .ruleItemConfigList(ruleItemConfigs)
                        .build();

                LaunchConfigBO launchConfig = LaunchConfigBO.builder()
                        .activityId(activityId)
                        .entityType(entityTypeEnum.getCode())
                        .entityId(activityId)
                        .channel(channelCode)
                        .scene(sceneCode)
                        .status(status)
                        .resourceType(launchConfigResourceTypeEnum)
                        .releaseStatus(releaseStatus)
                        .contentConfig(contentConfig)
                        .ruleConfig(launchRuleConfig)
                        .ext(convertExt(ext))
                        .creator(operator)
                        .modifier(operator)
                        .build();
                result.add(launchConfig);
                break;

            case SUB_ACTIVITY:
                selectedEntityIds.forEach(entityId -> {
                    // 子活动实体维度过滤规则
                    List<LaunchRuleItemConfigBO> entityRuleItemConfigs = Lists.newArrayList(ruleItemConfigs);
                    LaunchEntityFilterRuleContentBO subActivityEntityFilterRuleContent =
                            LaunchEntityFilterRuleContentBO.builder()
                                    .matchEntityType(EntityTypeEnum.SUB_ACTIVITY.getCode())
                                    .matchEntityIds(Lists.newArrayList(entityId))
                                    .build();
                    entityRuleItemConfigs.add(LaunchRuleItemConfigBO.builder()
                            .ruleType(LaunchRuleTypeEnum.ENTITY_FILTER_RULE.getType())
                            .ruleContent(toJSON(subActivityEntityFilterRuleContent))
                            .operatorType(LaunchRuleExecuteOperatorTypeEnum.AND.getType())
                            .build());
                    LaunchRuleConfigBO subActivityLaunchRuleConfig = LaunchRuleConfigBO.builder()
                            .ruleItemConfigList(entityRuleItemConfigs)
                            .build();

                    LaunchConfigBO subActivityLaunchConfig = LaunchConfigBO.builder()
                            .activityId(activityId)
                            .entityType(entityTypeEnum.getCode())
                            .entityId(entityId)
                            .channel(channelCode)
                            .scene(sceneCode)
                            .status(status)
                            .releaseStatus(releaseStatus)
                            .contentConfig(contentConfig)
                            .ruleConfig(subActivityLaunchRuleConfig)
                            .ext(convertExt(ext))
                            .creator(operator)
                            .resourceType(launchConfigResourceTypeEnum)
                            .modifier(operator)
                            .build();
                    result.add(subActivityLaunchConfig);
                });
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放实体类型");
        }
    }

    private ExperimentConfigBO saveExperimentConfig(Long activityId, String channel, String scene,
            LaunchExperimentAdminProtocol protocol, String operator, LaunchOperateAdminProtocol opeAdminProtocol) {
        if (protocol == null) {
            return null;
        }

        String experimentKey = protocol.getExperimentKey();
        if (StringUtils.isBlank(experimentKey)) {
            ExpKeyGenerateParamBO expKeyGenParam = ExpKeyGenerateParamBO.builder()
                    .expScene(LAUNCH_EXP.getScene())
                    .bizKey(Joiner.on("_").join(activityId, channel, scene, generateId()))
                    .build();
            experimentKey = experimentBizService.generateExpKey(expKeyGenParam);
        }

        RatioExpConfigCreateParamBO param = RatioExpConfigCreateParamBO.builder()
                .baseBucketFlowRatio(protocol.getBaseBucketFlowRatio())
                .expBucketFlowRatio(protocol.getExpBucketFlowRatio())
                .expBucketCount(protocol.getExpBucketCount())
                .isBaseBucketEmpty(protocol.getIsBaseBucketEmpty())
                .experimentRuleType(ExperimentRuleTypeEnum.DIVERSION_RATIO_BASED.getType())
                .experimentScene(LAUNCH_EXP.getScene())
                .experimentKey(experimentKey)
                .isEffectiveDirectly(true)
                .operator(operator)
                .build();

        ExperimentConfigBO experimentConfig = experimentBizService.saveExperimentConfig(param);
        if (experimentConfig == null) {
            log.error("[投放实体生成] 保存实验配置失败 activityId:{}, channel:{}, scene:{}, protocol:{}", activityId,
                    channel, scene, toJSON(protocol));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "保存实验配置失败");
        }

        return experimentConfig;
    }

    public static long generateId() {
        long timestamp = System.currentTimeMillis();
        long random = ThreadLocalRandom.current().nextLong(1 << RANDOM_BITS);
        return (timestamp << RANDOM_BITS) | random;
    }

    private static LaunchContentConfigBO buildContentConfig(LaunchSceneAdminProtocol sceneProtocol,
            ExperimentConfigBO experimentConfig) {
        if (experimentConfig == null) {
            // 如果实验配置为空/没有开启 存量方式创建
            LaunchFormAdminProtocol formProtocol = sceneProtocol.getLaunchFormAdminProtocol();
            if (formProtocol == null) {
                return null;
            }
            List<LaunchContentFieldConfigBO> fieldConfigList =
                    getFieldConfigs(formProtocol.getLaunchFormFieldAdminProtocols());

            return LaunchContentConfigBO.builder().fieldConfigList(fieldConfigList)
                    .fieldConfigListWithExp(Lists.newArrayList()).build();
        } else {
            // 如果实验配置存在 创建实验关联
            List<LaunchFormWithExpAdminProtocol> launchFormWithExpAdminProtocols =
                    sceneProtocol.getLaunchFormWithExpAdminProtocols();
            if (CollectionUtils.isEmpty(launchFormWithExpAdminProtocols)) {
                return null;
            }

            String experimentKey = experimentConfig.getExperimentKey();
            ExpBucketConfigBO bucketConfig = experimentConfig.getBucketConfig();
            List<ExpBucketItemConfigBO> bucketConfigs = bucketConfig.getBucketItemConfigs();

            // 有序-分桶配置
            Map<Integer, List<ExpBucketItemConfigBO>> bucketTypeMap =
                    bucketConfigs.stream().collect(Collectors.groupingBy(ExpBucketItemConfigBO::getBucketType));
            // 实验组分桶配置
            List<ExpBucketItemConfigBO> expBucketConfigs =
                    bucketTypeMap.get(ExpBucketTypeEnum.EXPERIMENT.getType());
            // 对照组分桶配置
            List<ExpBucketItemConfigBO> baseBucketConfigs = bucketTypeMap.get(ExpBucketTypeEnum.BASE.getType());

            if (CollectionUtils.isEmpty(expBucketConfigs) || CollectionUtils.isEmpty(baseBucketConfigs)) {
                return null;
            }

            int totalCount = expBucketConfigs.size() + baseBucketConfigs.size();
            checkArgument(Objects.equals(totalCount, launchFormWithExpAdminProtocols.size()), "投放实验内容配置异常");

            // 有序-分桶实验配置
            Map<Integer, List<LaunchFormWithExpAdminProtocol>> formBucketTypeMap =
                    launchFormWithExpAdminProtocols.stream()
                            .collect(Collectors.groupingBy(LaunchFormWithExpAdminProtocol::getBucketType));
            // 实验组分桶内容配置
            List<LaunchFormWithExpAdminProtocol> formExpBucketConfigs =
                    formBucketTypeMap.get(ExpBucketTypeEnum.EXPERIMENT.getType());
            // 对照组分桶内容配置
            List<LaunchFormWithExpAdminProtocol> formBaseBucketConfigs =
                    formBucketTypeMap.get(ExpBucketTypeEnum.BASE.getType());

            checkArgument(CollectionUtils.isNotEmpty(formExpBucketConfigs)
                    && formExpBucketConfigs.size() == expBucketConfigs.size(), "投放实验组配置异常");
            checkArgument(CollectionUtils.isNotEmpty(formBaseBucketConfigs)
                    && formBaseBucketConfigs.size() == baseBucketConfigs.size(), "投放对照组配置异常");

            List<LaunchContentFieldsWithExpConfigBO> fieldConfigListWithExp = Lists.newArrayList();
            // 实验组
            List<LaunchContentFieldsWithExpConfigBO> expFieldConfigs =
                    buildFieldConfigList(experimentKey, expBucketConfigs, formExpBucketConfigs);
            // 对照组
            List<LaunchContentFieldsWithExpConfigBO> baseFieldConfigs =
                    buildFieldConfigList(experimentKey, baseBucketConfigs, formBaseBucketConfigs);

            checkArgument(CollectionUtils.isNotEmpty(expFieldConfigs) && CollectionUtils.isNotEmpty(baseFieldConfigs),
                    "实验配置异常");

            // 有序-对照组实验组
            fieldConfigListWithExp.addAll(baseFieldConfigs);
            fieldConfigListWithExp.addAll(expFieldConfigs);

            return LaunchContentConfigBO.builder()
                    .fieldConfigList(baseFieldConfigs.get(0).getFieldConfigList())
                    .fieldConfigListWithExp(fieldConfigListWithExp).build();
        }
    }

    private static List<LaunchContentFieldsWithExpConfigBO> buildFieldConfigList(String experimentId,
            List<ExpBucketItemConfigBO> bucketConfigs, List<LaunchFormWithExpAdminProtocol> formBucketConfigs) {
        List<LaunchContentFieldsWithExpConfigBO> fieldConfigListWithExp = Lists.newArrayList();
        // 遍历分桶内容配置
        for (int i = 0; i < formBucketConfigs.size(); i++) {
            LaunchFormWithExpAdminProtocol formBucketConfig = formBucketConfigs.get(i);
            ExpBucketItemConfigBO bucketConfig = bucketConfigs.get(i);
            String bucketId = bucketConfig.getBucketId();
            Integer bucketType = bucketConfig.getBucketType();
            Boolean isEmptyBucket = bucketConfig.getIsEmptyBucket();

            List<LaunchContentFieldConfigBO> fieldConfigs = Lists.newArrayList();
            if (!isEmptyBucket) {
                fieldConfigs = getFieldConfigs(formBucketConfig.getLaunchFormFieldAdminProtocols());
            }

            LaunchContentFieldsWithExpConfigBO fieldsWithExpConfig =
                    LaunchContentFieldsWithExpConfigBO.builder().fieldConfigList(fieldConfigs)
                            .experimentId(experimentId).bucketId(bucketId)
                            .bucketType(bucketType).build();

            fieldConfigListWithExp.add(fieldsWithExpConfig);
        }
        return fieldConfigListWithExp;
    }

    private static List<LaunchContentFieldConfigBO> getFieldConfigs(
            List<LaunchFormFieldAdminProtocol> launchFormFieldAdminProtocols) {
        List<LaunchContentFieldConfigBO> fieldConfigList = Lists.newArrayList();
        CollectionUtils.emptyIfNull(launchFormFieldAdminProtocols).forEach(fieldProtocol -> {
            LaunchContentTypeEnum contentType =
                    LaunchContentTypeEnum.getByType(fieldProtocol.getSelectedContentType());
            String contentValue = null;
            switch (contentType) {
                case PLAIN_TEXT:
                case LINK:
                case PHOTO:
                case TEMPLATE:
                case SELECT_LIST:
                    LaunchFieldContentAdminProtocol content = fieldProtocol.getContent();
                    LaunchFixContentBO launchFixContent =
                            LaunchFixContentBO.builder().fixContent(content.getFixContent()).build();
                    contentValue = toJSON(launchFixContent);
                    break;
                case CUSTOMIZE:
                    break;
                default:
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放内容类型");
            }
            LaunchContentFieldConfigBO fieldConfig = LaunchContentFieldConfigBO.builder()
                    .fieldCode(fieldProtocol.getFieldCode())
                    .contentType(fieldProtocol.getSelectedContentType())
                    .contentValue(contentValue)
                    .build();
            fieldConfigList.add(fieldConfig);
        });
        return fieldConfigList;
    }

    private static LaunchRuleConfigBO convertRuleConfig(LaunchRuleAdminProtocol ruleProtocol) {
        // todo lk 后续扩展
        return null;
    }

    private static String convertExt(LaunchConfigExtBO ext) {
        return toJSON(ext);
    }

    public LaunchAdminProtocol convertToLaunchProtocol(List<LaunchConfigBO> launchConfigList) {
        if (CollectionUtils.isEmpty(launchConfigList)) {
            return LaunchAdminProtocol.builder().launchChannelAdminProtocols(Lists.newArrayList()).build();
        }

        Map<String, List<LaunchConfigBO>> channelConfigMap =
                launchConfigList.stream().collect(Collectors.groupingBy(LaunchConfigBO::getChannel));

        List<LaunchChannelAdminProtocol> channelProtocolList = Lists.newArrayList();
        channelConfigMap.forEach((channel, configListOfCertainChannel) -> {
            convertChannelLaunchProtocol(channelProtocolList, channel, configListOfCertainChannel);
        });

        return LaunchAdminProtocol.builder().launchChannelAdminProtocols(channelProtocolList).build();
    }

    private void convertChannelLaunchProtocol(List<LaunchChannelAdminProtocol> channelProtocolList, String channel,
            List<LaunchConfigBO> configListOfCertainChannel) {
        if (CollectionUtils.isEmpty(configListOfCertainChannel)) {
            return;
        }

        List<Long> selectEntityIds = Lists.newArrayList();

        // 兼容逻辑：如果ruleConfig为空，则为历史配置数据
        LaunchConfigBO firstChannelLaunchConfig = configListOfCertainChannel.get(0);
        if (firstChannelLaunchConfig.getRuleConfig() == null
                || CollectionUtils.isEmpty(firstChannelLaunchConfig.getRuleConfig().getRuleItemConfigList())) {
            selectEntityIds = configListOfCertainChannel.stream().map(LaunchConfigBO::getEntityId)
                    .distinct().collect(Collectors.toList());
        }

        Integer entityType = configListOfCertainChannel.stream().findFirst().map(LaunchConfigBO::getEntityType)
                .orElse(null);
        Map<String, List<LaunchConfigBO>> sceneConfigMap =
                configListOfCertainChannel.stream().collect(Collectors.groupingBy(LaunchConfigBO::getScene));

        List<LaunchSceneAdminProtocol> sceneProtocolList = Lists.newArrayList();
        AtomicBoolean channelEnableStatus = new AtomicBoolean(false);
        List<Long> finalSelectEntityIds = selectEntityIds;

        sceneConfigMap.forEach((scene, configListOfCertainScene) -> {
            convertSceneLaunchProtocol(sceneProtocolList,
                    channelEnableStatus, finalSelectEntityIds, scene, configListOfCertainScene);
        });

        LaunchStatusExtAdminProtocol statusExtAdminProtocol =
                buildStatusExtAdminProtocol(configListOfCertainChannel);

        firstChannelLaunchConfig = launchResolveService.resolveLatestSnapshotConfig(firstChannelLaunchConfig);

        LaunchTimeRangeAdminProtocol launchTimeRangeAdminProtocol =
                resolveTimeRangeProtocol(firstChannelLaunchConfig);

        Integer priority = launchResolveService.resolveLaunchPriority(firstChannelLaunchConfig);

        List<LaunchConfigFrontStatusEnum> sceneStatusList = sceneProtocolList.stream()
                .map(sceneProtocol -> LaunchConfigFrontStatusEnum.getByStatus(sceneProtocol.getStatus()))
                .collect(Collectors.toList());

        LaunchConfigFrontStatusEnum channelStatus = aggFrontSceneStatus(sceneStatusList);

        LaunchChannelAdminProtocol channelProtocol = LaunchChannelAdminProtocol.builder()
                .channelCode(channel).selectEntityType(entityType).enableStatus(channelEnableStatus.get())
                .selectedEntityIds(selectEntityIds.stream().distinct().collect(Collectors.toList()))
                .priority(priority)
                .status(channelStatus.getStatus())
                .launchSceneAdminProtocols(sceneProtocolList)
                .launchTimeRangeAdminProtocol(launchTimeRangeAdminProtocol)
                .statusExtAdminProtocol(statusExtAdminProtocol)
                .build();
        channelProtocolList.add(channelProtocol);
    }

    private void convertSceneLaunchProtocol(List<LaunchSceneAdminProtocol> sceneProtocolList,
            AtomicBoolean channelEnableStatus, List<Long> finalSelectEntityIds, String scene,
            List<LaunchConfigBO> configListOfCertainScene) {
        if (CollectionUtils.isEmpty(configListOfCertainScene)) {
            return;
        }

        LaunchConfigBO firstLaunchConfig = configListOfCertainScene.stream().findFirst().orElse(null);
        if (firstLaunchConfig == null) {
            return;
        }

        // 投放配置状态兼容
        Boolean releaseStatusEnableFlag =
                launchResolveService.resolveReleaseStatusEnableFlag(firstLaunchConfig);

        LaunchConfigFrontStatusEnum sceneStatus;
        if (!releaseStatusEnableFlag) {
            sceneStatus =
                    resolveSceneStatus(configListOfCertainScene.stream().map(LaunchConfigBO::getStatus)
                            .collect(Collectors.toList()));
        } else {
            sceneStatus = resolveSceneReleaseStatus(configListOfCertainScene.stream()
                    .map(LaunchConfigBO::getReleaseStatus).collect(Collectors.toList()));
        }

        // 解析投放快照
        firstLaunchConfig = launchResolveService.resolveLatestSnapshotConfig(firstLaunchConfig);

        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(firstLaunchConfig.getEntityType());

        // 新逻辑：ruleConfig 不为空，从 ruleConfig 中获取 entityIds
        if (firstLaunchConfig.getRuleConfig() != null && CollectionUtils
                .isNotEmpty(firstLaunchConfig.getRuleConfig().getRuleItemConfigList())) {
            switch (entityTypeEnum) {
                case ACTIVITY:
                    LaunchRuleConfigBO ruleConfig = firstLaunchConfig.getRuleConfig();
                    resolveSelectEntityIds(finalSelectEntityIds, ruleConfig);
                    break;
                case SUB_ACTIVITY:
                    configListOfCertainScene.forEach(launchConfig -> {
                        LaunchRuleConfigBO subActivityRuleConfig = launchConfig.getRuleConfig();
                        resolveSelectEntityIds(finalSelectEntityIds, subActivityRuleConfig);
                    });
                    break;
                default:
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放实体类型");
            }
        }

        // 对于相同场景，实体配置没有区分，取第一个
        LaunchConfigStatusEnum status = firstLaunchConfig.getStatus();
        LaunchContentConfigBO contentConfig = firstLaunchConfig.getContentConfig();
        LaunchRuleConfigBO ruleConfig = firstLaunchConfig.getRuleConfig();
        LaunchRuleAdminProtocol launchRuleAdminProtocol = convertToRuleProtocol(ruleConfig);

        List<LaunchFormFieldAdminProtocol> fieldProtocolList =
                convertFieldProtocolList(contentConfig.getFieldConfigList());

        LaunchFormAdminProtocol formProtocol =
                LaunchFormAdminProtocol.builder().launchFormFieldAdminProtocols(fieldProtocolList).build();

        // sceneSelected 为空兼容历史场景 通过历史状态字段判断
        Boolean sceneSelected = launchResolveService.resolveSceneSelected(firstLaunchConfig);
        boolean sceneEnableStatus =
                sceneSelected == null ? LaunchConfigStatusEnum.isRunningStatus(status) : sceneSelected;

        Boolean channelSelected = launchResolveService.resolveChannelSelected(firstLaunchConfig);
        if (channelSelected != null) {
            channelEnableStatus.set(channelSelected);
        } else {
            // 渠道状态通过实际配置状态
            channelEnableStatus.set(channelEnableStatus.get() || sceneEnableStatus);
        }

        // 通过ext中是否勾选覆盖场景状态
        String ext = firstLaunchConfig.getExt();
        if (StringUtils.isNotBlank(ext)) {
            LaunchConfigExtBO launchConfigExt = fromJSON(ext, LaunchConfigExtBO.class);
            if (launchConfigExt != null) {
                sceneEnableStatus = launchConfigExt.getSelected();
            }
        }

        LaunchExperimentAdminProtocol launchExperimentAdminProtocol = convertToExpAdminProtocol(ruleConfig);
        List<LaunchFormWithExpAdminProtocol> launchFormWithExpAdminProtocols = convertToExpAdminProtocol(contentConfig);

        LaunchSceneAdminProtocol sceneProtocol = LaunchSceneAdminProtocol.builder()
                .sceneCode(scene)
                .enableStatus(sceneEnableStatus)
                .launchFormAdminProtocol(formProtocol)
                .launchRuleAdminProtocol(launchRuleAdminProtocol)
                .status(sceneStatus.getStatus())
                .launchExperimentAdminProtocol(launchExperimentAdminProtocol)
                .launchFormWithExpAdminProtocols(launchFormWithExpAdminProtocols)
                .build();
        sceneProtocolList.add(sceneProtocol);
    }

    private LaunchExperimentAdminProtocol convertToExpAdminProtocol(LaunchRuleConfigBO ruleConfig) {
        if (ruleConfig == null || CollectionUtils.isEmpty(ruleConfig.getRuleItemConfigList())) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        // 如果规则实验配置为空 直接返回
        List<LaunchRuleItemConfigBO> ruleItemConfigList = ruleConfig.getRuleItemConfigList();
        LaunchRuleItemConfigBO expRuleItemConfig = ruleItemConfigList.stream()
                .filter(ruleItemConfig -> Objects.equals(ruleItemConfig.getRuleType(),
                        LaunchRuleTypeEnum.EXPERIMENT_RULE.getType())).findFirst().orElse(null);
        if (expRuleItemConfig == null) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        // 解析规则实验配置
        LaunchExpConfigBO launchExpConfig = fromJSON(expRuleItemConfig.getRuleContent(), LaunchExpConfigBO.class);
        if (launchExpConfig == null) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        String experimentKey = launchExpConfig.getExperimentKey();
        ExperimentConfigBO experimentConfig = experimentBizService.queryExperimentConfigByKey(experimentKey);
        if (experimentConfig == null) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        ExpRuleConfigBO expRuleConfig = experimentConfig.getRuleConfig();

        // 实验规则类型过滤
        ExperimentRuleTypeEnum ruleType =
                ExperimentRuleTypeEnum.getByType(expRuleConfig.getRuleType());
        if (!Objects.equals(ruleType, ExperimentRuleTypeEnum.DIVERSION_RATIO_BASED)) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        Integer experimentStatus = experimentConfig.getStatus();
        boolean experimentSwitch = Objects.equals(experimentStatus, ExperimentStatusEnum.VALID.getStatus());
        // 如果实验开关关闭 直接返回
        if (!experimentSwitch) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        String experimentId = experimentConfig.getExperimentKey();

        ExpBucketConfigBO bucketConfig = experimentConfig.getBucketConfig();
        List<ExpBucketItemConfigBO> bucketConfigs = bucketConfig.getBucketItemConfigs();
        if (CollectionUtils.isEmpty(bucketConfigs)) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        // 有序-分桶配置
        Map<Integer, List<ExpBucketItemConfigBO>> bucketTypeMap =
                bucketConfigs.stream().collect(Collectors.groupingBy(ExpBucketItemConfigBO::getBucketType));
        // 实验组分桶配置
        List<ExpBucketItemConfigBO> expBucketConfigs =
                bucketTypeMap.get(ExpBucketTypeEnum.EXPERIMENT.getType());
        // 对照组分桶配置
        List<ExpBucketItemConfigBO> baseBucketConfigs = bucketTypeMap.get(ExpBucketTypeEnum.BASE.getType());

        if (CollectionUtils.isEmpty(expBucketConfigs) || CollectionUtils.isEmpty(baseBucketConfigs)) {
            return LaunchExperimentAdminProtocol.builder()
                    .experimentSwitch(false)
                    .build();
        }

        // 实验组
        Integer expBucketCount = expBucketConfigs.size();
        ExpBucketItemConfigBO firstExpBucketConfig = expBucketConfigs.get(0);
        ExpDiversionRatioBucketRuleBO expDiversionRatioBucketRule =
                fromJSON(firstExpBucketConfig.getBucketRuleContent(), ExpDiversionRatioBucketRuleBO.class);
        Long expBucketFlowRatio = expDiversionRatioBucketRule.getDiversionRatio();

        // 对照组
        ExpBucketItemConfigBO firstBaseBucketConfig = baseBucketConfigs.get(0);
        ExpDiversionRatioBucketRuleBO baseDiversionRatioBucketRule =
                fromJSON(firstBaseBucketConfig.getBucketRuleContent(), ExpDiversionRatioBucketRuleBO.class);
        Long baseBucketFlowRatio = baseDiversionRatioBucketRule.getDiversionRatio();
        Boolean isBaseBucketEmpty = firstBaseBucketConfig.getIsEmptyBucket();

        boolean modifiable = !Objects.equals(experimentConfig.getStatus(), ExperimentStatusEnum.VALID.getStatus());

        return LaunchExperimentAdminProtocol.builder()
                .experimentSwitch(experimentSwitch)
                .baseBucketFlowRatio(baseBucketFlowRatio)
                .expBucketFlowRatio(expBucketFlowRatio)
                .expBucketCount(expBucketCount)
                .isBaseBucketEmpty(isBaseBucketEmpty)
                .experimentKey(experimentId)
                .modifiable(modifiable)
                .build();
    }

    private List<LaunchFormWithExpAdminProtocol> convertToExpAdminProtocol(LaunchContentConfigBO launchContentConfig) {
        List<LaunchContentFieldsWithExpConfigBO> fieldConfigListWithExp =
                launchContentConfig.getFieldConfigListWithExp();
        if (CollectionUtils.isEmpty(fieldConfigListWithExp)) {
            return Lists.newArrayList();
        }

        // 有序-投放内容实验配置
        return fieldConfigListWithExp.stream().map(fieldConfigWithExp -> {
            String experimentId = fieldConfigWithExp.getExperimentId();
            String bucketId = fieldConfigWithExp.getBucketId();
            Integer bucketType = fieldConfigWithExp.getBucketType();
            List<LaunchFormFieldAdminProtocol> launchFormFieldAdminProtocols =
                    convertFieldProtocolList(fieldConfigWithExp.getFieldConfigList());

            return LaunchFormWithExpAdminProtocol.builder()
                    .experimentId(experimentId).bucketId(bucketId).bucketType(bucketType)
                    .launchFormFieldAdminProtocols(launchFormFieldAdminProtocols).build();
        }).collect(Collectors.toList());
    }

    private LaunchStatusExtAdminProtocol buildStatusExtAdminProtocol(List<LaunchConfigBO> configListOfCertainChannel) {
        if (CollectionUtils.isEmpty(configListOfCertainChannel)) {
            return LaunchStatusExtAdminProtocol.builder().auditJumpUrl("").grayUserIds("").build();
        }

        StringBuilder grayUserIds = new StringBuilder();
        LaunchConfigBO firstGrayingConfig = configListOfCertainChannel.stream()
                .filter(config -> Objects.equals(config.getStatus(), LaunchConfigStatusEnum.GRAYING)
                        || Objects.equals(config.getReleaseStatus(), LaunchConfigReleaseStatusEnum.GRAYING))
                .findFirst().orElse(null);
        if (firstGrayingConfig != null) {
            LaunchConfigGrayConfigBO grayConfig = launchResolveService.resolveGrayConfig(firstGrayingConfig);
            if (grayConfig != null && CollectionUtils.isNotEmpty(grayConfig.getGrayUserIds())) {
                int grayUserIdsSize = grayConfig.getGrayUserIds().size();
                for (int i = 0; i < grayUserIdsSize - 1; i++) {
                    grayUserIds.append(grayConfig.getGrayUserIds().get(i)).append(",");
                }
                grayUserIds.append(grayConfig.getGrayUserIds().get(grayUserIdsSize - 1));
            }
        }

        String auditJumpUrl = "";
        LaunchCommonConfigBO config = launchCommonConfig.getObject();
        if (config != null && config.getLaunchAuditGrayConfig() != null
                && StringUtils.isNotBlank(config.getLaunchAuditGrayConfig().getFlowJumpUrl())) {
            auditJumpUrl = config.getLaunchAuditGrayConfig().getFlowJumpUrl();
        }

        return LaunchStatusExtAdminProtocol.builder()
                .auditJumpUrl(auditJumpUrl)
                .grayUserIds(grayUserIds.toString())
                .build();
    }

    private static List<LaunchFormFieldAdminProtocol> convertFieldProtocolList(
            List<LaunchContentFieldConfigBO> fieldConfigList) {
        List<LaunchFormFieldAdminProtocol> fieldProtocolList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(fieldConfigList)) {
            fieldProtocolList = fieldConfigList.stream()
                    .map(fieldConfig -> {
                        LaunchFieldContentAdminProtocol content = null;
                        LaunchContentTypeEnum contentTypeEnum =
                                LaunchContentTypeEnum.getByType(fieldConfig.getContentType());
                        switch (contentTypeEnum) {
                            case PLAIN_TEXT:
                            case LINK:
                            case PHOTO:
                            case TEMPLATE:
                            case SELECT_LIST:
                                LaunchFixContentBO launchFixContent =
                                        ObjectMapperUtils.fromJSON(fieldConfig.getContentValue(),
                                                LaunchFixContentBO.class);
                                content = LaunchFieldContentAdminProtocol.builder()
                                        .fixContent(launchFixContent.getFixContent()).build();
                                break;
                            case CUSTOMIZE:
                                break;
                            default:
                                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放内容类型");
                        }
                        return LaunchFormFieldAdminProtocol.builder()
                                .fieldCode(fieldConfig.getFieldCode())
                                .selectedContentType(fieldConfig.getContentType())
                                .content(content)
                                .build();
                    }).collect(Collectors.toList());
        }
        return fieldProtocolList;
    }

    private static void resolveSelectEntityIds(List<Long> selectEntityIds, LaunchRuleConfigBO ruleConfig) {
        if (ruleConfig != null && CollectionUtils.isNotEmpty(ruleConfig.getRuleItemConfigList())) {
            ruleConfig.getRuleItemConfigList().forEach(ruleItemConfig -> {
                LaunchRuleTypeEnum ruleTypeEnum =
                        LaunchRuleTypeEnum.getByType(ruleItemConfig.getRuleType());
                if (!Objects.equals(ruleTypeEnum, LaunchRuleTypeEnum.ENTITY_FILTER_RULE)) {
                    return;
                }
                LaunchEntityFilterRuleContentBO entityFilterRule = fromJSON(
                        ruleItemConfig.getRuleContent(), LaunchEntityFilterRuleContentBO.class);
                if (entityFilterRule == null
                        || CollectionUtils.isEmpty(entityFilterRule.getMatchEntityIds())) {
                    return;
                }
                selectEntityIds.addAll(entityFilterRule.getMatchEntityIds());
            });
        }
    }

    private static LaunchTimeRangeAdminProtocol resolveTimeRangeProtocol(LaunchConfigBO firstChannelLaunchConfig) {
        if (firstChannelLaunchConfig.getRuleConfig() == null
                || CollectionUtils.isEmpty(firstChannelLaunchConfig.getRuleConfig().getRuleItemConfigList())) {
            return LaunchTimeRangeAdminProtocol.builder()
                    .timeRangeType(LaunchTimeRangeTypeEnum.ACTIVITY_SHOW_END_TIME.getType()).build();
        }

        List<LaunchRuleItemConfigBO> firstLaunchRuleConfigList =
                firstChannelLaunchConfig.getRuleConfig().getRuleItemConfigList();

        LaunchRuleItemConfigBO timeRangeRuleConfig =
                firstLaunchRuleConfigList.stream().filter(ruleConfig -> Objects.equals(ruleConfig.getRuleType(),
                        LaunchRuleTypeEnum.TIME_RANGE_FILTER_RULE.getType())).findFirst().orElse(null);
        if (timeRangeRuleConfig == null) {
            return LaunchTimeRangeAdminProtocol.builder()
                    .timeRangeType(LaunchTimeRangeTypeEnum.ACTIVITY_SHOW_END_TIME.getType()).build();
        }

        LaunchTimeRangeFilterRuleContentBO timeRangeFilterRuleContent =
                fromJSON(timeRangeRuleConfig.getRuleContent(), LaunchTimeRangeFilterRuleContentBO.class);
        if (timeRangeFilterRuleContent == null) {
            return LaunchTimeRangeAdminProtocol.builder()
                    .timeRangeType(LaunchTimeRangeTypeEnum.ACTIVITY_SHOW_END_TIME.getType()).build();
        }

        return LaunchTimeRangeAdminProtocol.builder()
                .timeRangeType(timeRangeFilterRuleContent.getTimeRangeType())
                .customizeLaunchEndTime(timeRangeFilterRuleContent.getCustomizeLaunchEndTime())
                .build();
    }

    private static LaunchRuleAdminProtocol convertToRuleProtocol(LaunchRuleConfigBO ruleConfig) {
        return LaunchRuleAdminProtocol.builder().build();
    }

    public static LaunchConfigDO convertToDO(LaunchConfigBO launchConfig) {
        if (launchConfig == null) {
            return null;
        }

        return LaunchConfigDO.builder()
                .id(launchConfig.getId())
                .activityId(launchConfig.getActivityId())
                .entityType(launchConfig.getEntityType())
                .entityId(launchConfig.getEntityId())
                .channel(launchConfig.getChannel())
                .scene(launchConfig.getScene())
                .status(launchConfig.getStatus().getStatus())
                .releaseStatus(launchConfig.getReleaseStatus().getStatus())
                .contentConfig(toJSON(launchConfig.getContentConfig()))
                .ruleConfig(toJSON(launchConfig.getRuleConfig()))
                .auditBizKey(launchConfig.getAuditBizKey())
                .ext(launchConfig.getExt())
                .creator(launchConfig.getCreator())
                .modifier(launchConfig.getModifier())
                .createTime(launchConfig.getCreateTime())
                .updateTime(launchConfig.getUpdateTime())
                .version(launchConfig.getVersion())
                .deleted(launchConfig.getDeleted())
                .resourceType(launchConfig.getResourceType().getType())
                .build();
    }

    public static LaunchConfigBO convertToBO(LaunchConfigDO launchConfig) {
        if (launchConfig == null) {
            return null;
        }

        LaunchContentConfigBO launchContentConfig = null;
        if (StringUtils.isNotBlank(launchConfig.getContentConfig())) {
            launchContentConfig =
                    ObjectMapperUtils.fromJSON(launchConfig.getContentConfig(), LaunchContentConfigBO.class);
        }

        LaunchRuleConfigBO launchRuleConfig = null;
        if (StringUtils.isNotBlank(launchConfig.getRuleConfig())) {
            launchRuleConfig = ObjectMapperUtils.fromJSON(launchConfig.getRuleConfig(), LaunchRuleConfigBO.class);
        }

        Integer resourceType = launchConfig.getResourceType();
        LaunchConfigResourceTypeEnum resourceTypeEnum =
                null == resourceType ? null : LaunchConfigResourceTypeEnum.getByType(resourceType);

        return LaunchConfigBO.builder()
                .id(launchConfig.getId())
                .activityId(launchConfig.getActivityId())
                .entityType(launchConfig.getEntityType())
                .entityId(launchConfig.getEntityId())
                .channel(launchConfig.getChannel())
                .scene(launchConfig.getScene())
                .status(LaunchConfigStatusEnum.getByStatus(launchConfig.getStatus()))
                .releaseStatus(LaunchConfigReleaseStatusEnum.getByStatus(launchConfig.getReleaseStatus()))
                .contentConfig(launchContentConfig)
                .ruleConfig(launchRuleConfig)
                .auditBizKey(launchConfig.getAuditBizKey())
                .ext(launchConfig.getExt())
                .creator(launchConfig.getCreator())
                .modifier(launchConfig.getModifier())
                .createTime(launchConfig.getCreateTime())
                .updateTime(launchConfig.getUpdateTime())
                .version(launchConfig.getVersion())
                .deleted(launchConfig.getDeleted())
                .resourceType(resourceTypeEnum)
                .build();
    }
}
