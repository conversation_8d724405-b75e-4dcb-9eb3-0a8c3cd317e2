package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchDataSourceFetchParamBO {

    private Long userId;

    private Long activityId;

    private String channel;

    private String scene;

    private Integer entityType;

    private List<Long> entityIds;

    private Map<String, Object> customizeParamMap;

    private List<LaunchDataSourceTypeEnum> dataSourceTypeList;

    private LaunchConfigBO launchConfigBO;
}
