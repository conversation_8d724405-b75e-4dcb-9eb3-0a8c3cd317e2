package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodConfigBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
public interface OffsetEventService {
    /**
     * 获取偏移事件配置
     */
    OffsetEventConfig getOffsetEventConfig(String offsetEventType);

    /**
     * 获取偏移事件发生时间
     */
    Long getOffsetEventHappenTime(String offsetEventType, Long userId);

    /**
     * 计算偏移周期
     */
    OffsetPeriodBO calculateOffsetPeriod(OffsetPeriodConfigBO offsetPeriodConfig, Long userId);
}
