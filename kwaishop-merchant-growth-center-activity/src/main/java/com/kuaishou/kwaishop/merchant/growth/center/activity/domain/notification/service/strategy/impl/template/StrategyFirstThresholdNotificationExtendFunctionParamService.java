package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.SingleValueBeyondSendRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.SingleValueBeyondSendRule.SingleValueBeyondRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.combine.service.LianHeLaXinService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.StrategyAwardService;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取第一个门槛值、第一个门槛奖励
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-04
 */
@Service
@Slf4j
public class StrategyFirstThresholdNotificationExtendFunctionParamService
        extends AbstractNotificationExtendFunctionParamService {


    @Autowired
    private LianHeLaXinService combineService;

    @Autowired
    private StrategyAwardService strategyAwardService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.STRATEGY_FIRST_THRESHOLD,
                TemplateParamTypeEnum.STRATEGY_FIRST_THRESHOLD_AWARD);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        // 降级、黑名单校验
        NotificationExtendFunctionParamBO checkResult = checkDegradationAndBlackList(userId, configBO);
        if (checkResult != null) {
            return checkResult;
        }
        // 获取策略奖励配置
        AwardConfigBO awardConfigBO = getAwardConfigBO(userId, configBO);
        if (awardConfigBO == null) {
            result.setExecuteStatus(STOP);
            return result;
        }
        // 获取策略第一个门槛的发奖规则
        SingleValueBeyondRule minThresholdSendRule = getMinThresholdSendRule(userId, configBO, awardConfigBO);
        if (minThresholdSendRule == null) {
            result.setExecuteStatus(STOP);
            return result;
        }
        // 策略第一个门槛
        params.put(TemplateParamTypeEnum.STRATEGY_FIRST_THRESHOLD.getName(),
                String.valueOf(minThresholdSendRule.getBeyond()));
        // 策略第一个门槛奖励
        params.put(TemplateParamTypeEnum.STRATEGY_FIRST_THRESHOLD_AWARD.getName(),
                getStrategyFirstThresholdAward(minThresholdSendRule, awardConfigBO));
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    private AwardConfigBO getAwardConfigBO(long userId, NotificationPushConfigBO configBO) {
        AwardConfigBO awardConfigBO =
                strategyAwardService.getStrategyMainAwardRuleConfigWithCache(configBO.getEntityId());
        if (awardConfigBO == null || awardConfigBO.getSendRule() == null) {
            log.error("[获取模板参数失败][获取奖励配置失败] Fail to query awardConfig, userId is :{}, "
                    + "notificationPushConfigBO is :{}", userId, configBO);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            return null;
        }
        return awardConfigBO;
    }

    private SingleValueBeyondRule getMinThresholdSendRule(long userId, NotificationPushConfigBO configBO,
            AwardConfigBO awardConfigBO) {
        // 同时设置了拉新和拉回目标时，按照拉新来计算用户是否达标
        List<SingleValueBeyondSendRule> beyondSendRules =
                strategyAwardService.getUserStrategyAwardSendRule(userId, awardConfigBO);
        if (CollectionUtils.isEmpty(beyondSendRules)) {
            return null;
        }
        SingleValueBeyondSendRule sendRule = beyondSendRules.get(0);
        List<SingleValueBeyondRule> rules = sendRule.getRules();
        SingleValueBeyondRule minThreshSendRule = CollectionUtils.isEmpty(rules) ? null : rules.stream()
                .min(Comparator.comparing(SingleValueBeyondRule::getBeyond))
                .orElse(null);
        if (minThreshSendRule == null) {
            log.error("[获取模板参数失败][获取奖励发放规则失败] Fail to get sendRule, userId is :{}, "
                    + "notificationPushConfigBO is :{}, awardConfigBO is :{}", userId, configBO, awardConfigBO);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            return null;
        }
        return minThreshSendRule;
    }

    // 获取第一个门槛的奖励：门槛值 * 单位用户奖励 + 赠送奖励
    private String getStrategyFirstThresholdAward(SingleValueBeyondRule minThreshSendRule,
            AwardConfigBO awardConfigBO) {
        long award = minThreshSendRule.getBeyond() * Long.parseLong(minThreshSendRule.getAwardValue())
                + (StringUtils.isBlank(minThreshSendRule.getPresentAwardValue()) ? 0 : Long.parseLong(
                minThreshSendRule.getPresentAwardValue()));
        String awardValue = AwardUnitUtils.changeFenUnitAwardValueToYuan(awardConfigBO.getAwardType(), award);
        String awardUnitDesc = combineService.buildAwardUnitDesc(awardConfigBO.getAwardType(), false);
        // 奖励拼接：奖励 + 单位
        return awardValue + awardUnitDesc;
    }
}
