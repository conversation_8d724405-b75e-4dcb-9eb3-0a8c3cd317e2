package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CsShopGroupMessageConfigBO {
    /**
     * 成长中心PC端链接
     */
    private String growthCenterPcJumpUrl;
    /**
     * 成长中心商家端跳转链接
     */
    private String growthCenterJumpUrl;
    /**
     * 默认行动点名称
     */
    private String actionName;
    /**
     * 查看全部任务按钮名称
     */
    private String allTaskButtonName;
}
