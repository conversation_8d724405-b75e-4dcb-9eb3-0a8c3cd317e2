package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserTaskTimeRangeBO {

    /**
     * 用户活动开始时间
     */
    private Long taskStartTime;

    /**
     * 用户活动结束时间
     */
    private Long taskEndTime;

    public boolean invalid() {
        return !taskEndTimeValid() || !taskStartTimeValid();
    }

    public boolean taskEndTimeValid() {
        return taskEndTime != null && taskEndTime > 0;
    }

    public boolean taskStartTimeValid() {
        return taskStartTime != null && taskStartTime > 0;
    }
}
