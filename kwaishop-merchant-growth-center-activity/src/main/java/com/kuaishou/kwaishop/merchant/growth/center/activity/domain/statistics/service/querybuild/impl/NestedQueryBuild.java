package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.impl;

import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildHandler;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-26
 */
@Slf4j
@Lazy
@Service
public class NestedQueryBuild implements QueryBuildHandler {

    @Autowired
    private QueryBuildFactory queryBuildFactory;

    @Override
    public QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam) {
        // 嵌套查询，子条件不能为空
        if (CollectionUtils.isEmpty(condition.getConditions())) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "嵌套查询，子条件不能为空");
        }
        QueryBuilder childQuery = queryBuildFactory.buildQueryBuilder(condition.getConditions(), entityParam);
        return QueryBuilders.nestedQuery(condition.getFieldName(), childQuery);
    }

    @Override
    public ConditionTypeEnum conditionType() {
        return ConditionTypeEnum.NESTED;
    }
}
