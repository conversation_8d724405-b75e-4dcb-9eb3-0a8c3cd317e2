package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.galaxycomponent.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.ACTIVITY_SINGLE_DATA_SOURCE_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.SAVE_AND_ONLINE_PAGE_INSTANCE_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.enableGalaxyPageCreate;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.galaxyPageComponentConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.common.model.enums.MetricItemEnum.CREATE_AND_ONLINE_GALAXY_PAGE;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.PageResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivityPatternTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.UpdateActivityBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.service.ActivityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.GalaxyPageConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.ShowChannelConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.service.industry.IndustryActivityResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.galaxycomponent.config.GalaxyComponentConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.galaxycomponent.config.GalaxyComponentConfig.ModuleTemplateConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.galaxycomponent.config.GalaxyComponentConfig.PageTemplateConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.galaxycomponent.service.GalaxyComponentService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.TaskConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.GalaxyComponentFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.GalaxyPageInstanceSaveBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.GalaxyPageInstanceSaveBO.ModuleInstanceSaveBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.model.enums.BizEnum;
import com.kuaishou.kwaishop.merchant.growth.center.common.monitor.MonitorEvent;
import com.kuaishou.kwaishop.merchant.growth.center.common.monitor.Monitors;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-18
 */
@Service
@Slf4j
public class GalaxyComponentServiceImpl implements GalaxyComponentService {
    @Resource
    private GalaxyComponentFetchService galaxyComponentFetchService;
    @Resource
    private TaskConfigDAO taskConfigDAO;
    @Resource
    private ActivityDAO activityDAO;
    @Resource
    private ActivityService activityService;
    @Resource
    private IndustryActivityResolveService industryActivityResolveService;

    private static final String BASE_INFO_MODULE_KEY = "base_info";

    private static final String TASK_DETAIL_MODULE_KEY = "task_detail";

    private static final String PAGE_CODE_PREFIX = "broker_page";

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SINGLE_DATA_SOURCE_NAME)
    public Long createAndOnlineGalaxyComponentPage(Long activityId, String operator) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Long galaxyPageId;
        try {
            if (!enableGalaxyPageCreate.get()) {
                log.warn("createAndOnlineGalaxyComponentPage. 未开启天河页面创建功能, activityId: {}", activityId);
                return null;
            }
            ActivityDO activityDO = activityDAO.queryById(activityId);
            String showConfig = activityDO.getShowConfig();
            if (StringUtils.isBlank(showConfig)) {
                log.warn("createAndOnlineGalaxyComponentPage. 未配置活动展示配置, activityId: {}", activityId);
                return null;
            }
            Map<String, Object> showChannelConfigBOMap = fromJson(activityDO.getShowConfig());
            if (MapUtils.isEmpty(showChannelConfigBOMap) || !showChannelConfigBOMap.containsKey(
                    PageResourceTypeEnum.BROKER_WORKBENCH.getType())) {
                log.warn("createAndOnlineGalaxyComponentPage. 未配置达人工作台活动展示配置, activityId: {}",
                        activityId);
                return null;
            }
            galaxyPageId = createGalaxyComponentPage(activityDO, operator, PageResourceTypeEnum.BROKER_WORKBENCH);
            galaxyComponentFetchService.onlineGalaxyPageInstance(galaxyPageId, operator);
        } catch (Exception e) {
            // 异常打印
            log.error("createAndOnlineGalaxyComponentPage. 创建并上线天河页面异常, activityId: {}", activityId, e);
            // 监控
            Monitors.push(MonitorEvent.builder()
                    .biz(BizEnum.STRATEGY_CENTER.name())
                    .success(false)
                    .metricName(CREATE_AND_ONLINE_GALAXY_PAGE.name())
                    .rt(stopwatch.elapsed(TimeUnit.MILLISECONDS))
                    .errorCode(String.valueOf(SAVE_AND_ONLINE_PAGE_INSTANCE_ERROR.getCode()))
                    .errorMsg(SAVE_AND_ONLINE_PAGE_INSTANCE_ERROR.getMessage())
                    .bizId(activityId.toString())
                    .build());
            throw new BizException(SAVE_AND_ONLINE_PAGE_INSTANCE_ERROR);
        }
        log.info(
                "createAndOnlineGalaxyComponentPage. 创建并上线天河页面成功, activityId: {}, galaxyPageId: {}, rt:{}ms",
                activityId, galaxyPageId, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return galaxyPageId;
    }

    private Long createGalaxyComponentPage(ActivityDO activityDO, String operator,
            PageResourceTypeEnum pageResourceType) {
        Long activityId = activityDO.getId();
        // 获取活动展示配置
        ShowChannelConfigBO commonShowChannel =
                ActivityResolver.getCommonShowChannel(activityDO, pageResourceType.getType());
        if (null == commonShowChannel) {
            log.warn("createAndOnlineGalaxyComponentPage. 未找到活动展示配置, activityId: {}, pageResourceType:{}",
                    activityDO.getId(), pageResourceType.getName());
            return null;
        }
        // 获取天河组件化配置
        ActivityPatternTypeEnum activityPatternType =
                industryActivityResolveService.resolveActivityPatternType(activityId);
        List<GalaxyComponentConfig> configList = galaxyPageComponentConfig.getList(GalaxyComponentConfig.class);

        // 过滤满足展示渠道和活动玩法的配置
        GalaxyComponentConfig componentConfig = CollectionUtils.emptyIfNull(configList).stream()
                .filter(config -> pageResourceType.getType().equals(config.getShowChannel()))
                .filter(config -> Objects.equals(activityPatternType.getType(), config.getActivityPatternType()))
                .findFirst()
                .orElse(null);
        // 兜底兼容逻辑
        if (componentConfig == null) {
            componentConfig = CollectionUtils.emptyIfNull(configList).stream()
                    .filter(config -> pageResourceType.getType().equals(config.getShowChannel()))
                    .filter(config -> Objects.equals(config.getActivityPatternType(), "default"))
                    .findFirst()
                    .orElseThrow(() -> new BizException(BasicErrorCode.SERVER_ERROR, "未配置任何组件"));
        }

        List<TaskDO> parentTaskList = taskConfigDAO.getParentTaskListByActivityId(activityId);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(parentTaskList), "未找到活动下的父任务");
        List<ModuleInstanceSaveBO> moduleLists = Lists.newArrayList();
        // 组装基础信息组件
        assembleBaseInfoModule(activityId, componentConfig, moduleLists);
        // 组装任务详情组件
        GalaxyComponentConfig finalComponentConfig = componentConfig;
        parentTaskList.forEach(
                parentTask -> assembleTaskDetailModule(activityDO, parentTask, finalComponentConfig, moduleLists));
        // 组装页面信息
        GalaxyPageInstanceSaveBO galaxyPageInstanceSaveBO =
                assembleGalaxyPageInstanceSaveBO(activityDO, componentConfig, operator, pageResourceType, moduleLists);
        // 保存天河页面
        Long pageInstanceId = galaxyComponentFetchService.saveGalaxyPageInstance(galaxyPageInstanceSaveBO);
        Preconditions.checkArgument(pageInstanceId != null, "创建页面失败");
        // 更新天河页面实例id
        updateGalaxyPageId(activityDO, pageResourceType, pageInstanceId);
        return pageInstanceId;
    }

    /**
     * 组装基础信息组件
     */
    private void assembleBaseInfoModule(Long activityId, GalaxyComponentConfig componentConfig,
            List<ModuleInstanceSaveBO> moduleLists) {
        List<ModuleTemplateConfig> moduleTemplateConfigs = componentConfig.getModuleTemplateConfigs();
        ModuleTemplateConfig baseInfoModuleConfig =
                moduleTemplateConfigs.stream().filter(config -> BASE_INFO_MODULE_KEY.equals(config.getModuleKey()))
                        .findFirst()
                        .orElseThrow(() -> new BizException(BasicErrorCode.SERVER_ERROR, "未配置基础信息组件"));
        moduleLists.add(ModuleInstanceSaveBO.builder().moduleInstanceCode(
                        String.join("_", componentConfig.getAppKey(), BASE_INFO_MODULE_KEY, activityId.toString()))
                .moduleTemplateCode(baseInfoModuleConfig.getModuleTemplateCode())
                .parentContainerId(baseInfoModuleConfig.getContainerId()).build());
    }

    /**
     * 组装任务详情组件
     */
    private void assembleTaskDetailModule(ActivityDO activityDO, TaskDO parentTaskDO,
            GalaxyComponentConfig componentConfig, List<ModuleInstanceSaveBO> moduleLists) {
        List<ModuleTemplateConfig> moduleTemplateConfigs = componentConfig.getModuleTemplateConfigs();
        ModuleTemplateConfig taskDetailModuleConfig =
                moduleTemplateConfigs.stream().filter(config -> TASK_DETAIL_MODULE_KEY.equals(config.getModuleKey()))
                        .findFirst()
                        .orElseThrow(() -> new BizException(BasicErrorCode.SERVER_ERROR, "未配置任务详情组件"));
        Long activityId = activityDO.getId();
        Long taskId = parentTaskDO.getId();
        Map<String, Object> extParam = new HashMap<>();
        extParam.put("taskId", taskId);
        moduleLists.add(ModuleInstanceSaveBO.builder().moduleInstanceCode(
                        String.join("_", componentConfig.getAppKey(), TASK_DETAIL_MODULE_KEY, activityId.toString(),
                                taskId.toString())).moduleTemplateCode(taskDetailModuleConfig.getModuleTemplateCode())
                .parentContainerId(taskDetailModuleConfig.getContainerId()).extParam(extParam)
                .moduleBizKey(taskId.toString()).build());
    }

    /**
     * 组装页面信息
     */
    private GalaxyPageInstanceSaveBO assembleGalaxyPageInstanceSaveBO(ActivityDO activityDO,
            GalaxyComponentConfig componentConfig, String operator, PageResourceTypeEnum pageResourceType,
            List<ModuleInstanceSaveBO> moduleInstanceSaveBOList) {
        GalaxyPageConfigBO galaxyPageConfig =
                ActivityResolver.getGalaxyPageConfig(activityDO, pageResourceType.getType());
        Long pageInstanceId = galaxyPageConfig == null ? null : galaxyPageConfig.getGalaxyPageId();
        String activityName = activityDO.getName();
        String activityId = activityDO.getId().toString();
        PageTemplateConfig pageTemplateConfig = componentConfig.getPageTemplateConfig();
        return GalaxyPageInstanceSaveBO.builder().appKey(componentConfig.getAppKey())
                .pageCode(String.join("_", componentConfig.getAppKey(), PAGE_CODE_PREFIX, activityId))
                .pageName(String.join("_", activityName, activityId)).desc(activityName).pageInstanceId(pageInstanceId)
                .pageTemplateCode(pageTemplateConfig.getPageTemplateCode()).creator(operator)
                .moduleInstanceSaveBOList(moduleInstanceSaveBOList).build();
    }

    /**
     * 更新开发中天河页面实例id
     */
    private void updateGalaxyPageId(ActivityDO activityDO, PageResourceTypeEnum pageResourceType, Long galaxyPageId) {
        GalaxyPageConfigBO galaxyPageConfig =
                ActivityResolver.getGalaxyPageConfig(activityDO, pageResourceType.getType());
        galaxyPageConfig.setGalaxyPageId(galaxyPageId);
        String galaxyPageConfigStr =
                ActivityResolver.rebuildGalaxyPageConfig(activityDO, pageResourceType.getType(), galaxyPageConfig);
        UpdateActivityBO updateActivityBO = UpdateActivityBO.builder().galaxyPageConfig(galaxyPageConfigStr).build();
        activityService.updateActivity(activityDO.getId(), updateActivityBO);
    }

}
