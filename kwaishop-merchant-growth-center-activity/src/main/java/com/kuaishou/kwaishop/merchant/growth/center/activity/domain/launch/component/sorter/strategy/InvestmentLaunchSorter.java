package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.sorter.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType.SORTER_INVESTMENT_STRATEGY;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.sorter.LaunchSorter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.InvestmentLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.InvestmentLaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-08
 */
@Component
@Slf4j
public class InvestmentLaunchSorter implements LaunchSorter<InvestmentLaunchInfoFetchContext, InvestmentLaunchInfoResult> {

    @Override
    public void sort(InvestmentLaunchInfoFetchContext context) {

    }

    @Override
    public LaunchPipeHandlerType getType() {
        return SORTER_INVESTMENT_STRATEGY;
    }
}
