package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Component
@Slf4j
public class BaseDataSourceFetcher {

    @Autowired
    private List<BaseDataSourceFetchStrategy> strategies;

    private Map<BaseDataSourceFetchTypeEnum, BaseDataSourceFetchStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = strategies.stream().collect(
                Collectors.toMap(BaseDataSourceFetchStrategy::getType, Function.identity(), (k1, k2) -> k1));
    }

    public BaseDataSourceFetchResult fetch(BaseDataSourceFetchParam param) {
        check(param);
        BaseDataSourceFetchTypeEnum baseDataSourceFetchType = param.getBaseDataSourceFetchType();

        if (MapUtils.isEmpty(strategyMap)) {
            return BaseDataSourceFetchResult.builder()
                    .rawIndicatorBaseValues(Lists.newArrayList())
                    .multiSegmentMap(Maps.newHashMap())
                    .build();
        }

        BaseDataSourceFetchStrategy strategy = strategyMap.get(baseDataSourceFetchType);
        if (Objects.isNull(strategy)) {
            return BaseDataSourceFetchResult.builder()
                    .rawIndicatorBaseValues(Lists.newArrayList())
                    .multiSegmentMap(Maps.newHashMap())
                    .build();
        }

        return strategy.fetch(param);
    }

    private void check(BaseDataSourceFetchParam param) {
        checkArgument(param != null, "参数不能为空");
        checkArgument(param.getUserId() > 0L, "用户ID异常");
        checkArgument(param.getIndicatorConfig() != null || param.getCycleDuration() != null, "指标配置不能为空");
        checkArgument(param.getIndicator() != null, "指标元数据不能为空");
        checkArgument(CollectionUtils.isNotEmpty(param.getDateList()), "基期查询时间不能为空");
        checkArgument(param.getBaseDataSourceFetchType() != null, "基期查询类型不能为空");
    }
}
