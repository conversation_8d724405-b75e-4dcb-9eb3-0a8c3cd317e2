package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchContentAssembleConstantBO.LAUNCH_RULE_CONTENT_EXECUTE_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchContentAssembleConstantBO.LAUNCH_RULE_CONTENT_EXECUTE_SUCCESS;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchRuleContentExecuteResult {

    /**
     * 返回值，1-成功，其它-失败
     */
    private int result;

    /**
     * 返回值说明
     */
    private String message;

    /**
     * resMap
     */
    private Map<String, Object> resMap;

    public static LaunchRuleContentExecuteResult success(Map<String, Object> resMap) {
        return LaunchRuleContentExecuteResult.builder().result(LAUNCH_RULE_CONTENT_EXECUTE_SUCCESS).resMap(resMap)
                .message("").build();
    }

    public static LaunchRuleContentExecuteResult error(String errorMessage) {
        return LaunchRuleContentExecuteResult.builder().result(LAUNCH_RULE_CONTENT_EXECUTE_ERROR)
                .resMap(Maps.newHashMap()).message(errorMessage).build();
    }
}
