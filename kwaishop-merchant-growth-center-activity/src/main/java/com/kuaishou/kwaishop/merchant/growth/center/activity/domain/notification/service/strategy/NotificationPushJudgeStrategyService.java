package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-26
 */
public interface NotificationPushJudgeStrategyService {

    /**
     * 触达推送决策条件Code
     * @return
     */
    String judgeConditionCode();

    /**
     * 决策是否推送
     */
    boolean judgePush(long userId, NotificationPushConfigBO configBO);
}
