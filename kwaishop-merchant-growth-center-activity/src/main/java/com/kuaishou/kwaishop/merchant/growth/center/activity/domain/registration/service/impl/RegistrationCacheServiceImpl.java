package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.riskUserRegistrationReDrawDataExpireTime;

import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RiskUserRegistrationReDrawResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.RegistrationCacheService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-14
 */
@Service
@Slf4j
@Lazy
public class RegistrationCacheServiceImpl implements RegistrationCacheService {

    @Override
    public void addShardIndexToReDrawSet(List<Integer> shards, String key) {
        if (CollectionUtils.isEmpty(shards)) {
            return;
        }
        String redisKey = RegistrationRedisKeyEnum.RISK_USER_REGISTRATION_RE_DRAW_SHARD.getFullKey(key);
        getGrowthRedisCommands().sadd(redisKey, shards.stream().map(String::valueOf).toArray(String[]::new));
    }

    @Override
    public void addActivityIdToReDrawSet(List<Long> activityIds, String key) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return;
        }
        String redisKey = RegistrationRedisKeyEnum.RISK_USER_REGISTRATION_RE_DRAW_ACTIVITY.getFullKey(key);
        getGrowthRedisCommands().sadd(redisKey, activityIds.stream().map(String::valueOf).toArray(String[]::new));
    }

    @Override
    public void addUserIdToReDrawSuccessSet(List<Long> userIds, String key) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        String redisKey = RegistrationRedisKeyEnum.RISK_USER_REGISTRATION_RE_DRAW_USER.getFullKey(key);
        getGrowthRedisCommands().sadd(redisKey, userIds.stream().map(String::valueOf).toArray(String[]::new));
    }

    @Override
    public List<RiskUserRegistrationReDrawResult> buildRegistrationReDrawResult(String key) {
        List<RiskUserRegistrationReDrawResult> res = Lists.newArrayList();
        String activityIdRedisKey = RegistrationRedisKeyEnum.RISK_USER_REGISTRATION_RE_DRAW_ACTIVITY.getFullKey(key);
        Set<String> activityIdSet = getGrowthRedisCommands().smembers(activityIdRedisKey);
        //为记录活动Id的Redis数据设置过期时间
        getGrowthRedisCommands().expire(activityIdRedisKey, riskUserRegistrationReDrawDataExpireTime.get());
        if (CollectionUtils.isEmpty(activityIdSet)) {
            return res;
        }
        activityIdSet.forEach(activityId -> {
            // 拼接活动ID对应资格下发成功用户的redisKey
            String userRedisKey = RegistrationRedisKeyEnum.RISK_USER_REGISTRATION_RE_DRAW_USER.getFullKey(key + activityId);
            Set<String> userIdSet = getGrowthRedisCommands().smembers(userRedisKey);
            if (CollectionUtils.isEmpty(userIdSet)) {
                return;
            }
            RiskUserRegistrationReDrawResult result = RiskUserRegistrationReDrawResult.builder()
                    .activityId(activityId).userIdSet(userIdSet).build();
            res.add(result);
            //为记录活动Id对应资格重新领取用户的Redis数据设置过期时间
            getGrowthRedisCommands().expire(userRedisKey, riskUserRegistrationReDrawDataExpireTime.get());
        });
        return res;
    }
}
