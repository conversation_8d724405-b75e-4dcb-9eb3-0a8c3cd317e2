package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.processor;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
public interface NotificationChangeProcessStrategy {

    /**
     * 执行存量处理
     */
    void process(long activityId, List<NotificationPushConfigBO> configs, long eventTime);

    /**
     * 获取实体类型
     */
    NotificationEntityTypeEnum getType();
}
