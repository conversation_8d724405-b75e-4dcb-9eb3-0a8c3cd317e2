package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomBaseCalcItemConfigBO {

    /**
     * 支持规则Code
     */
    private String customBaseCalcRuleCode;

    /**
     * 支持规则名称
     */
    private String customBaseCalcRuleName;

    /**
     * 基期数据查询方式
     */
    private Integer dataSourceFetchType;

    /**
     * 需要额外数据类型列表
     */
    private List<String> requiredExtDataTypeList;

    /**
     * 是否需要活动时间校验
     */
    private Boolean withCycleDurationCheck;
}
