package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
public enum NotificationChannelEnum {
    UNKNOWN_CHANNEL(0, "未知", "unknownChannel", true, false),
    MERCHANT_NOTIFICATION_CENTER(1, "商家通知中心", "MerchantNotificationCenter", true, true),
    FLOW_CENTER(2, "流量中台", "FlowCenter", true, true),
    CS_SHOP_GROUP(3, "店铺群消息", "CsShopGroup", true, true),
    DAREN_WORK_PLATFORM_MESSAGE(4, "达人工作台消息", "DarenWorkPlatformMessage", false, true),
    KUAISHOU_PRIVATE_MESSAGE(5, "快手小店私信", "KuaishouPrivateMessage", false, true),
    WORK_PLATFORM_TIPS(6, "工作台提示栏", "WorkPlatformTips", false, true),
    WORK_PLATFORM_POPUP(7, "工作台弹窗", "WorkPlatformPopup", false, true),
    MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE(8, "选品中心决策页", "merchant_hwlm_item_decisioninfo_page", false, false);;
    private int val;
    private String desc;

    private String name;

    private boolean protoHeader;

    private boolean withNotification;

    NotificationChannelEnum(int val, String desc, String name, boolean protoHeader, boolean withNotification) {
        this.val = val;
        this.desc = desc;
        this.name = name;
        this.protoHeader = protoHeader;
        this.withNotification = withNotification;
    }

    public static NotificationChannelEnum of(int channel) {
        for (NotificationChannelEnum channelEnum : NotificationChannelEnum.values()) {
            if (channelEnum.getVal() == channel) {
                return channelEnum;
            }
        }
        return UNKNOWN_CHANNEL;
    }

    public static List<Integer> getAllValidNotificationChannelValues() {
        return Arrays.stream(NotificationChannelEnum.values())
                .map(NotificationChannelEnum::getVal)
                .filter(val -> val > 0)
                .collect(Collectors.toList());
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getProtoHeader() {
        return protoHeader;
    }

    public void setProtoHeader(boolean protoHeader) {
        this.protoHeader = protoHeader;
    }

    public boolean getWithNotification() {
        return withNotification;
    }

    public void setWithNotification(boolean withNotification) {
        this.withNotification = withNotification;
    }
}
