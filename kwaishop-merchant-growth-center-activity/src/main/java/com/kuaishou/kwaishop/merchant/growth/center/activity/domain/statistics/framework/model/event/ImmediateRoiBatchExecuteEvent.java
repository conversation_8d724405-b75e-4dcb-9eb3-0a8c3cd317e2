package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImmediateRoiBatchExecuteEvent extends BatchExecuteEvent {
    /**
     * 分层配置
     */
    private RoiActivityLevelConfigBO layerConfig;
}
