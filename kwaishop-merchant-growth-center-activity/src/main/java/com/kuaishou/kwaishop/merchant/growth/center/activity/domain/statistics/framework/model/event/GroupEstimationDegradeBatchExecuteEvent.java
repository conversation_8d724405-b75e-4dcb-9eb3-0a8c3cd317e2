package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyGroupAggregateRoot;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class GroupEstimationDegradeBatchExecuteEvent extends BatchExecuteEvent {

    /**
     * 策略ID
     */
    private Long strategyId;

    private String strategyVersion;


    private Long preSuccessStrategyId;

    private String preSuccessStrategyVersion;


    /**
     * 历史测算完成策略组子策略id和当前子策略的映射关系
     */
    private Map<Long, Long> preSubStrategyIdMap;

    private EstimationStrategyGroupAggregateRoot groupAggregateRoot;

    /**
     * 被降级的子策略
     */
    private EstimationStrategyAggregateRoot degradeStrategy;

    /**
     * 历史成功的子策略（平替）
     */
    private EstimationStrategyAggregateRoot preSuccessStrategy;

    public GroupEstimationDegradeBatchExecuteEvent(Long strategyId, String strategyVersion,
                                                   EstimationStrategyAggregateRoot degradeStrategy,
                                                   EstimationStrategyAggregateRoot preSuccessStrategy) {
        this.strategyId = strategyId;
        this.strategyVersion = strategyVersion;
        this.degradeStrategy = degradeStrategy;
        this.preSuccessStrategy = preSuccessStrategy;
    }
}
