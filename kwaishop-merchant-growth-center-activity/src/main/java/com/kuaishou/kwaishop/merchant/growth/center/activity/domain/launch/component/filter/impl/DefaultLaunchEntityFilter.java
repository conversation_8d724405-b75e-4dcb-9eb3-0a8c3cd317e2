package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.impl.LaunchResolveServiceImpl.resolveStageFromSubActivityId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.launchEntityFilterParentTaskTempCheckSwitch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.LaunchFilter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model.LaunchFilterTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchFilterParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-22
 */
@Component
@Slf4j
public class DefaultLaunchEntityFilter implements LaunchFilter<DefaultLaunchInfoFetchContext> {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private UserTaskRecordDAO userTaskRecordDAO;

    @Resource
    private UserActivityRecordDAO userActivityRecordDAO;

    @Resource
    private ActivityLocalCacheService activityLocalCacheService;

    @Resource
    private UserRegistrationService userRegistrationService;

    @Override
    public boolean match(DefaultLaunchInfoFetchContext context) {
        DefaultLaunchFilterParamBO filterParam = context.getFilterParam();
        return filterParam != null
                && (CollectionUtils.isNotEmpty(filterParam.getEntityTagList())
                || CollectionUtils.isNotEmpty(filterParam.getUserEntityStatusList()));
    }

    @Override
    public void filter(DefaultLaunchInfoFetchContext context) {

        // 获取投放活动配置
        Map<Long, List<LaunchConfigBO>> launchConfigActivityMap = context.getLaunchConfigActivityMap();
        List<Long> launchActivityIds = context.getLaunchActivityIds();

        if (MapUtils.isEmpty(launchConfigActivityMap) || CollectionUtils.isEmpty(launchActivityIds)) {
            return;
        }

        Long userId = context.getUserId();
        DefaultLaunchFilterParamBO filterParam = context.getFilterParam();

        // 过滤用户实体状态
        launchConfigActivityMap =
                filterByUserEntityStatus(userId, launchActivityIds, launchConfigActivityMap, filterParam);
        launchActivityIds = new ArrayList<>(launchConfigActivityMap.keySet());
        if (!EnvUtils.isProd()) {
            Map<Long, List<Long>> activityLaunchConfigIdMap = Maps.newHashMap();
            MapUtils.emptyIfNull(launchConfigActivityMap).forEach((activityId, launchConfigs) -> {
                if (CollectionUtils.isEmpty(launchConfigs)) {
                    return;
                }
                activityLaunchConfigIdMap.put(activityId, launchConfigs.stream().map(LaunchConfigBO::getId)
                        .collect(Collectors.toList()));
            });
            log.info("[投放过滤器] 用户实体状态过滤完成 userId:{}, scene:{}, activityLaunchConfigIdMap:{}",
                    userId, context.getScene(), toJSON(MapUtils.emptyIfNull(activityLaunchConfigIdMap)));
        }

        // 过滤实体标签
        launchConfigActivityMap = filterByEntityTags(userId, launchActivityIds, launchConfigActivityMap, filterParam);
        launchActivityIds = new ArrayList<>(launchConfigActivityMap.keySet());
        if (!EnvUtils.isProd()) {
            Map<Long, List<Long>> activityLaunchConfigIdMap = Maps.newHashMap();
            MapUtils.emptyIfNull(launchConfigActivityMap).forEach((activityId, launchConfigs) -> {
                if (CollectionUtils.isEmpty(launchConfigs)) {
                    return;
                }
                activityLaunchConfigIdMap.put(activityId, launchConfigs.stream().map(LaunchConfigBO::getId)
                        .collect(Collectors.toList()));
            });
            log.info("[投放过滤器] 实体标签过滤完成 userId:{}, scene:{}, activityLaunchConfigIdMap:{}",
                    userId, context.getScene(), toJSON(MapUtils.emptyIfNull(activityLaunchConfigIdMap)));
        }

        // 覆盖投放数据
        context.setLaunchActivityIds(launchActivityIds);
        context.setLaunchConfigActivityMap(launchConfigActivityMap);
    }

    private Map<Long, List<LaunchConfigBO>> filterByUserEntityStatus(Long userId, List<Long> launchActivityIds,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap, DefaultLaunchFilterParamBO filterParam) {

        // 获取用户实体状态过滤参数
        List<Integer> userEntityStatusList = filterParam.getUserEntityStatusList();
        if (CollectionUtils.isEmpty(userEntityStatusList)) {
            return launchConfigActivityMap;
        }

        Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap = Maps.newHashMap();

        // 活动维度遍历
        launchActivityIds.forEach(launchActivityId -> {
            filterSingleActivityStatus(userId, launchActivityId, userEntityStatusList, launchConfigActivityMap,
                    filteredLaunchConfigActivityMap);
        });

        return filteredLaunchConfigActivityMap;
    }

    private void filterSingleActivityStatus(Long userId, Long launchActivityId, List<Integer> userEntityStatusList,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap,
            Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap) {

        List<LaunchConfigBO> filteredLaunchConfigs;

        // 获取活动投放配置
        List<LaunchConfigBO> launchConfigs = launchConfigActivityMap.get(launchActivityId);
        if (CollectionUtils.isEmpty(launchConfigs)) {
            return;
        }

        // 获取实体类型
        LaunchConfigBO firstLaunchConfig = launchConfigs.get(0);
        EntityTypeEnum entityType = EntityTypeEnum.getByCode(firstLaunchConfig.getEntityType());

        switch (entityType) {
            case SUB_ACTIVITY:
                filteredLaunchConfigs = filterSubActivityStatus(
                        userId, launchActivityId, userEntityStatusList, launchConfigs);
                break;
            case ACTIVITY:
                filteredLaunchConfigs = filterActivityStatus(
                        userId, launchActivityId, userEntityStatusList, launchConfigs);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放实体类型");
        }

        if (CollectionUtils.isNotEmpty(filteredLaunchConfigs)) {
            filteredLaunchConfigActivityMap.put(launchActivityId, filteredLaunchConfigs);
        }
    }

    private List<LaunchConfigBO> filterActivityStatus(Long userId, Long launchActivityId,
            List<Integer> userEntityStatusList, List<LaunchConfigBO> launchConfigs) {

        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        List<UserActivityStatusEnum> statusList = userEntityStatusList.stream()
                .map(UserActivityStatusEnum::of).collect(Collectors.toList());
        if (statusList.stream().allMatch(status -> Objects.equals(status, UserActivityStatusEnum.UNKNOWN))) {
            return filteredLaunchConfigs;
        }

        // 过滤用户活动资格
        UserRegistrationRecordBO userActivityRegistrationRecord = userRegistrationService
                .queryUserRegistrationRecord(userId, launchActivityId, EntityTypeEnum.ACTIVITY, launchActivityId);
        if (userActivityRegistrationRecord == null) {
            return filteredLaunchConfigs;
        }

        UserActivityRecordDO userActivityRecord =
                userActivityRecordDAO.queryUserActivityRecord(userId, launchActivityId, false);

        boolean containsStatus = statusList.stream().anyMatch(status -> {
            switch (status) {
                case DRAWING:
                    return userActivityRecord == null;
                default:
                    return userActivityRecord != null && Objects.equals(
                            userActivityRecord.getStatus(), status.getValue());
            }
        });
        if (containsStatus) {
            filteredLaunchConfigs.addAll(launchConfigs);
        }

        return filteredLaunchConfigs;
    }

    private List<LaunchConfigBO> filterSubActivityStatus(Long userId, Long launchActivityId,
            List<Integer> userEntityStatusList, List<LaunchConfigBO> launchConfigs) {

        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        // 获取任务记录
        List<Long> subActivityIds =
                launchConfigs.stream().map(LaunchConfigBO::getEntityId).collect(Collectors.toList());
        List<TaskDO> tasks =
                launchResolveService.resolveTaskConfigBySubActivityId(launchActivityId, subActivityIds);
        List<TaskDO> parentTasks = ListUtils.emptyIfNull(tasks).stream()
                .filter(task -> Objects.equals(task.getParentTask(), 0L)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentTasks)) {
            return filteredLaunchConfigs;
        }

        // 构建任务映射
        Map<Integer/*stage*/, TaskDO> parentTaskStageMap = parentTasks.stream()
                .collect(Collectors.toMap(TaskDO::getStage, Function.identity(), (k1, k2) -> k1));
        List<Long> parentTaskIds = parentTasks.stream().map(TaskDO::getId).collect(Collectors.toList());

        Map<Long, UserRegistrationRecordBO> userRegistrationRecordMap = userRegistrationService
                .batchQueryUserRegistrationRecords(userId, launchActivityId, EntityTypeEnum.TASK, parentTaskIds);
        if (MapUtils.isEmpty(userRegistrationRecordMap)) {
            return filteredLaunchConfigs;
        }

        // 获取用户任务状态
        List<UserTaskStatusEnum> statusList =
                userEntityStatusList.stream().map(UserTaskStatusEnum::of).collect(Collectors.toList());
        if (statusList.stream().allMatch(status -> Objects.equals(status, UserTaskStatusEnum.UNKNOWN))) {
            return filteredLaunchConfigs;
        }

        // 查询用户任务记录
        List<UserTaskRecordDO> userParentTaskRecords =
                userTaskRecordDAO.queryUserTaskRecordList(userId, launchActivityId, parentTaskIds);
        if (launchEntityFilterParentTaskTempCheckSwitch.get() && CollectionUtils.isEmpty(userParentTaskRecords)) {
            return filteredLaunchConfigs;
        }

        Map<Long, UserTaskRecordDO> userParentTaskIdMap =
                ListUtils.emptyIfNull(userParentTaskRecords).stream().collect(
                        Collectors.toMap(UserTaskRecordDO::getTaskId, Function.identity(), (k1, k2) -> k1));

        // 遍历投放配置
        launchConfigs.forEach(launchConfig -> {
            Integer stage = resolveStageFromSubActivityId(launchConfig.getEntityId());
            TaskDO parentTask = parentTaskStageMap.get(stage);
            if (parentTask == null) {
                return;
            }

            // 过滤用户父任务资格
            if (!userRegistrationRecordMap.containsKey(parentTask.getId())) {
                return;
            }

            UserTaskRecordDO userParentTaskRecord = userParentTaskIdMap.get(parentTask.getId());

            boolean containsStatus = statusList.stream().anyMatch(status -> {
                switch (status) {
                    case DRAWING:
                        return userParentTaskRecord == null;
                    default:
                        return userParentTaskRecord != null && Objects.equals(
                                userParentTaskRecord.getStatus(), status.getValue());
                }
            });

            if (containsStatus) {
                filteredLaunchConfigs.add(launchConfig);
            }
        });

        return filteredLaunchConfigs;
    }

    private Map<Long, List<LaunchConfigBO>> filterByEntityTags(Long userId, List<Long> launchActivityIds,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap, DefaultLaunchFilterParamBO filterParam) {
        List<String> entityTagList = filterParam.getEntityTagList();
        if (CollectionUtils.isEmpty(entityTagList)) {
            return launchConfigActivityMap;
        }

        Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap = Maps.newHashMap();

        launchActivityIds.forEach(launchActivityId -> {
            filterSingleActivityEntityTags(launchActivityId, entityTagList,
                    launchConfigActivityMap, filteredLaunchConfigActivityMap);
        });

        return filteredLaunchConfigActivityMap;
    }

    private void filterSingleActivityEntityTags(Long launchActivityId, List<String> entityTagList,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap,
            Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap) {
        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        List<LaunchConfigBO> launchConfigs = launchConfigActivityMap.get(launchActivityId);
        if (CollectionUtils.isEmpty(launchConfigs)) {
            return;
        }

        LaunchConfigBO firstLaunchConfig = launchConfigs.get(0);
        EntityTypeEnum entityType = EntityTypeEnum.getByCode(firstLaunchConfig.getEntityType());

        switch (entityType) {
            case SUB_ACTIVITY:
                filteredLaunchConfigs = filterSubActivityTags(launchActivityId, entityTagList, launchConfigs);
                break;
            case ACTIVITY:
                filteredLaunchConfigs = filterActivityTags(launchActivityId, entityTagList, launchConfigs);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的投放实体类型");
        }

        if (CollectionUtils.isNotEmpty(filteredLaunchConfigs)) {
            filteredLaunchConfigActivityMap.put(launchActivityId, filteredLaunchConfigs);
        }
    }

    private List<LaunchConfigBO> filterActivityTags(Long launchActivityId, List<String> entityTagList,
            List<LaunchConfigBO> launchConfigs) {
        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        ActivityDO activity = activityLocalCacheService.queryActivityInfo(launchActivityId);
        String showTag = activity.getShowTag();
        if (StringUtils.isNotBlank(showTag) && entityTagList.contains(showTag)) {
            filteredLaunchConfigs.addAll(launchConfigs);
        }

        return filteredLaunchConfigs;
    }

    private List<LaunchConfigBO> filterSubActivityTags(Long launchActivityId, List<String> entityTagList,
            List<LaunchConfigBO> launchConfigs) {

        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        List<Long> subActivityIds =
                launchConfigs.stream().map(LaunchConfigBO::getEntityId).collect(Collectors.toList());
        List<TaskDO> tasks =
                launchResolveService.resolveTaskConfigBySubActivityId(launchActivityId, subActivityIds);
        List<TaskDO> parentTasks = ListUtils.emptyIfNull(tasks).stream()
                .filter(task -> Objects.equals(task.getParentTask(), 0L)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentTasks)) {
            return filteredLaunchConfigs;
        }

        Map<Integer/*stage*/, TaskDO> parentTaskStageMap = parentTasks.stream()
                .collect(Collectors.toMap(TaskDO::getStage, Function.identity(), (k1, k2) -> k1));

        launchConfigs.forEach(launchConfig -> {
            Integer stage = resolveStageFromSubActivityId(launchConfig.getEntityId());
            TaskDO parentTask = parentTaskStageMap.get(stage);
            if (parentTask == null) {
                return;
            }

            List<String> tags = Lists.newArrayList();
            if (StringUtils.isNotEmpty(parentTask.getTags())) {
                tags = fromJSON(parentTask.getTags(), List.class, String.class);
            }

            if (CollectionUtils.isNotEmpty(tags) && CollectionUtils.containsAny(tags, entityTagList)) {
                filteredLaunchConfigs.add(launchConfig);
            }
        });

        return filteredLaunchConfigs;
    }

    @Override
    public int order() {
        return 0;
    }

    @Override
    public LaunchFilterTypeEnum getType() {
        return LaunchFilterTypeEnum.ENTITY_FILTER;
    }
}
