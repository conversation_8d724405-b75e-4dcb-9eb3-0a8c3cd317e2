package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-17
 */
@Service
@Slf4j
@Lazy
public class BatchExecuteServiceImpl implements BatchExecuteService {

    @Autowired
    private BatchExecuteFactory batchExecuteFactory;

    @Override
    public void
    submitBatchExecuteEvent(BatchExecuteEvent event) {
        int executeType = event.getExecuteType();
        // 获取处理类
        BatchExecuteFramework batchExecuteFramework = batchExecuteFactory.getBatchExecuteByType(executeType);
        // 幂等锁逻辑
        // 初始化人群和共用信息缓存
        Set<Long> totalCrowd = batchExecuteFramework.initExecuteCrowdAndCache(event);
        // 人群切分&消息发送
        batchExecuteFramework.partitionCrowdSendMsg(totalCrowd, event);
    }
}
