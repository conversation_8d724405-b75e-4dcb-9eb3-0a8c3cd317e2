package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.AdmissionRuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.CustomizePlanSceneEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
public interface CustomizePlanHandlerService {

    /**
     * 过滤
     * @param userId 用户ID
     * @param customizePlanConfig 定制计划配置
     * @return 是否过滤
     */
    boolean filter(long userId, CustomizePlanConfig customizePlanConfig);

    /**
     * 定制计划准入判断
     * @param userId 用户ID
     * @param admissionRuleConfig 准入规则配置
     * @return 是否准入
     */
    boolean hitAdmissionRule(long userId, AdmissionRuleConfig admissionRuleConfig);

    /**
     * 构建定制计划
     * @param userId 用户ID
     * @param customizePlanConfig 定制计划配置
     * @return 定制计划
     */
    CustomizePlan buildCustomizePlan(long userId, CustomizePlanConfig customizePlanConfig);

    /**
     * 定制计划场景
     * @return 定制计划场景对应枚举
     */
    CustomizePlanSceneEnum getScene();
}
