package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectionDecisionInfoConfig {

    private Map<String/*activityStatus*/, Map<String/*drawStatus*/, SelectionDecisionDataBO>> config;
}
