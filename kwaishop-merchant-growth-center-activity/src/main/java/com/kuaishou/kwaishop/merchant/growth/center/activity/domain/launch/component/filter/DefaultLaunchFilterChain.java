package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType.FILTER_DEFAULT_STRATEGY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchRuntimeConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model.LaunchFilterTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.DefaultLaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchRuntimeConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchSceneFilterChainConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Component
@Slf4j
public class DefaultLaunchFilterChain
        extends AbstractLaunchFilterChain<DefaultLaunchInfoFetchContext, DefaultLaunchInfoResult> {

    @Resource
    private List<LaunchFilter<DefaultLaunchInfoFetchContext>> filters;

    @Override
    protected List<LaunchFilter<DefaultLaunchInfoFetchContext>> getFilters() {
        LaunchRuntimeConfigBO config = launchRuntimeConfig.getObject();
        if (config == null || MapUtils.isEmpty(config.getFilterChainConfigMap())) {
            return Lists.newArrayList();
        }

        Map<String, List<LaunchSceneFilterChainConfigBO>> filterChainConfigMap = config.getFilterChainConfigMap();
        List<LaunchSceneFilterChainConfigBO> filterChainConfigList = filterChainConfigMap.get(getType().getType());
        if (CollectionUtils.isEmpty(filterChainConfigList)) {
            return Lists.newArrayList();
        }

        List<String> filterTypeList = filterChainConfigList.stream().map(LaunchSceneFilterChainConfigBO::getFilterType)
                .collect(Collectors.toList());

        Map<LaunchFilterTypeEnum, LaunchFilter<DefaultLaunchInfoFetchContext>> filterTypeMap =
                ListUtils.emptyIfNull(filters).stream()
                        .collect(Collectors.toMap(LaunchFilter::getType, Function.identity(), (k1, k2) -> k1));

        return filterTypeList.stream().map(filterType -> {
            LaunchFilterTypeEnum filterTypeEnum = LaunchFilterTypeEnum.getByType(filterType);
            if (filterTypeEnum == null) {
                return null;
            }

            return filterTypeMap.get(filterTypeEnum);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public LaunchPipeHandlerType getType() {
        return FILTER_DEFAULT_STRATEGY;
    }
}
