package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.handler;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ManualTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.excel.SnowballModel;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.ActivityCustomRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.ActivityRegistrationHandleService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityDrawService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.RegistrationConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.TaskCrowdTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.KimReportUtil;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.RegistrationConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.UserHandlerResBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.registration.RegistrationConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.custom.ManualDrawSignUpActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.excel.KExcel;
import com.kuaishou.kwaishop.merchant.growth.utils.excel.read.KExcelCheckModel;
import com.kuaishou.kwaishop.merchant.growth.utils.excel.read.KExcelCheckRsp;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.http.SecurityHttpUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-03
 */
@Slf4j
@Lazy
@Service
public class SnowballRegistrationHandlerService implements ActivityRegistrationHandleService {

    @Autowired
    private ActivityCustomRegistrationService activityCustomRegistrationService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private UserActivityDrawService userActivityDrawService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private RegistrationConfigDAO registrationConfigDAO;

    @Override
    public void handleManualDrawSignUp(ManualDrawSignUpActivityRequest request) {
        if (request.getManualType() != ManualTypeEnum.EXCEL.getCode()) {
            return;
        }
        // 校验Excel内容并返回数据
        List<SnowballModel> excelData = checkAndGetExcelData(request.getManualEntity());
        // 业务校验
        long activityId = request.getActivityId();
        List<Long> taskIdList = request.getTaskIdList();
        List<TaskDO> taskList = taskLocalCacheService.batchGetTaskByTaskId(taskIdList);
        checkRegistrationValid(activityId, taskList);
        // 执行报名
        log.info("[雪球活动报名] 报名开始，表格记录数量:{}", excelData.size());
        List<UserHandlerResBO> resList = Lists.newArrayList();
        if (request.getSignUp()) {
            excelData.forEach(e -> resList.add(handleSingleUserDraw(e, activityId)));
        } else {
            excelData.forEach(e -> resList.add(handleSingleUserRegistration(e, activityId, taskList)));
        }
        // kim播报
        KimReportUtil.kimNoticeHandleResult("SnowballRegistration", resList, activityId, "雪球活动",
                request.getOperator());
    }

    private UserHandlerResBO handleSingleUserDraw(SnowballModel snowballModel, long activityId) {
        long userId = snowballModel.getUserId();
        UserHandlerResBO userHandlerResBO = new UserHandlerResBO();
        userHandlerResBO.setUserId(userId);
        // 幂等校验
        // 判断是否已有活动记录
        UserActivityRecordDO recordDO = userActivityRecordDAO.queryUserActivityRecord(userId, activityId, true);
        if (recordDO != null) {
            userHandlerResBO.setSuccess(false);
            userHandlerResBO.setExecuteRes("用户已领取任务，无需重复领取！");
            return userHandlerResBO;
        }
        try {
            userActivityDrawService.drawActivityByActivityId(userId, activityId, "manual", null);
        } catch (Exception e) {
            log.error("[雪球活动报名] 用户领取活动异常！userId:{}, activityId:{}", userId, activityId, e);
            userHandlerResBO.setSuccess(false);
            userHandlerResBO.setExecuteRes("领取活动失败！");
            return userHandlerResBO;
        }
        userHandlerResBO.setSuccess(true);
        userHandlerResBO.setExecuteRes("执行成功！");
        return userHandlerResBO;
    }

    private UserHandlerResBO handleSingleUserRegistration(SnowballModel snowballModel, long activityId,
            List<TaskDO> taskList) {
        long userId = snowballModel.getUserId();
        UserHandlerResBO userHandlerResBO = new UserHandlerResBO();
        userHandlerResBO.setUserId(userId);
        // 幂等校验
        // 判断是否已有活动资格记录
        UserRegistrationRecordBO activityRegistrationRecord = userRegistrationService
                .queryUserRegistrationRecord(userId, activityId, EntityTypeEnum.ACTIVITY, activityId);
        if (activityRegistrationRecord != null) {
            userHandlerResBO.setSuccess(false);
            userHandlerResBO.setExecuteRes("用户已有资格，无需重复下发！");
            return userHandlerResBO;
        }
        Map<String, Object> jsonMap = convertModel2BasicJsonMap(snowballModel);
        try {
            activityCustomRegistrationService.registrationActivityAndTask(userId, activityId, taskList, jsonMap);
        } catch (Exception e) {
            log.error("[雪球活动报名] 用户下发资格异常！userId:{}, activityId:{}", userId, activityId, e);
            userHandlerResBO.setSuccess(false);
            userHandlerResBO.setExecuteRes("下发资格失败！");
            return userHandlerResBO;
        }
        userHandlerResBO.setSuccess(true);
        userHandlerResBO.setExecuteRes("执行成功！");
        return userHandlerResBO;
    }

    // 将表格数据转为jsonData
    private Map<String, Object> convertModel2BasicJsonMap(SnowballModel snowballModel) {
        Map<String, Object> jsonMap = Maps.newHashMap();
        jsonMap.put("basicNewFans", snowballModel.getBasicNewFans());
        jsonMap.put("basicCost", snowballModel.getBasicCost());
        jsonMap.put("basicNewFansLtv", snowballModel.getBasicNewFansLtv());
        jsonMap.put("basicNewFansRiskGmv", snowballModel.getBasicNewFansRiskGmv());

        jsonMap.put("newFansTargetLevel1", snowballModel.getNewFansTargetLevel1());
        jsonMap.put("newFansTargetLevel2", snowballModel.getNewFansTargetLevel2());
        jsonMap.put("newFansTargetLevel3", snowballModel.getNewFansTargetLevel3());
        jsonMap.put("newFansTargetLevel4", snowballModel.getNewFansTargetLevel4());

        jsonMap.put("costTargetLevel1", snowballModel.getCostTargetLevel1());
        jsonMap.put("costTargetLevel2", snowballModel.getCostTargetLevel2());
        jsonMap.put("costTargetLevel3", snowballModel.getCostTargetLevel3());
        jsonMap.put("costTargetLevel4", snowballModel.getCostTargetLevel4());

        jsonMap.put("newFansLtvTargetLevel1", snowballModel.getNewFansLtvTargetLevel1());
        jsonMap.put("newFansLtvTargetLevel2", snowballModel.getNewFansLtvTargetLevel2());
        jsonMap.put("newFansLtvTargetLevel3", snowballModel.getNewFansLtvTargetLevel3());
        jsonMap.put("newFansLtvTargetLevel4", snowballModel.getNewFansLtvTargetLevel4());

        jsonMap.put("predictAwardLevel1", snowballModel.getPredictAwardLevel1());
        jsonMap.put("predictAwardLevel2", snowballModel.getPredictAwardLevel2());
        jsonMap.put("predictAwardLevel3", snowballModel.getPredictAwardLevel3());
        jsonMap.put("predictAwardLevel4", snowballModel.getPredictAwardLevel4());

        return jsonMap;
    }

    // 业务校验
    private void checkRegistrationValid(long activityId, List<TaskDO> taskList) {
        // 存在性
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        checkArgument(activityDO != null, "活动不能为空");
        // 关联性
        taskList.forEach(e -> checkArgument(e.getActivityId().equals(activityId), "活动任务ID不匹配-" + e.getId()));
        // 合理性
        List<RegistrationConfigDO> registrationConfigList =
                registrationConfigDAO.queryRegistrationConfigByActivityId(activityId);
        checkArgument(CollectionUtils.isNotEmpty(registrationConfigList), "报名配置不能为空");
        Map<Long, RegistrationConfigDO> registrationConfigMap = registrationConfigList.stream()
                .filter(e -> e.getStatus().equals(RegistrationConfigStatusEnum.EFFECT.getCode()))
                .filter(e -> e.getType().equals(TaskCrowdTypeEnum.EXCEL.getCode()))
                .filter(e -> e.getEntityType().equals(EntityTypeEnum.TASK.getCode()))
                .filter(e -> e.getEffectiveEndTime() >= System.currentTimeMillis()
                        && e.getEffectiveStartTime() <= System.currentTimeMillis())
                .collect(Collectors.toMap(RegistrationConfigDO::getEntityId, e -> e));
        taskList.forEach(
                e -> checkArgument(registrationConfigMap.get(e.getId()) != null, "任务不能通过excel报名-" + e.getId()));

    }

    // 校验Excel内容并返回数据
    private List<SnowballModel> checkAndGetExcelData(String cdnUrl) {
        byte[] excelFileBytes = SecurityHttpUtils.securityGet(cdnUrl, null);
        // excel内容校验
        KExcelCheckRsp<SnowballModel> snowballModeKExcelCheckRsp =
                KExcel.read(new ByteArrayInputStream(excelFileBytes), SnowballModel.class, null) //这里的path也可以改成直接读文件对象
                        .sheet()
                        .rowLimit(1000) //行数限制
                        .doReadSyncAndCheck(); //同步执行校验
        if (CollectionUtils.isNotEmpty(snowballModeKExcelCheckRsp.getFailList())) {
            List<KExcelCheckModel<SnowballModel>> failList = snowballModeKExcelCheckRsp.getFailList();
            // kim通知
            List<String> list = failList.stream()
                    .map(e -> Joiner.on("-").join(e.getData().getUserId(), e.getMsg()))
                    .collect(Collectors.toList());
            KimReportUtil.kimNoticeMsg("SnowballCheck", "excel内容非预期-" + list, null);
            throw new BizException(BasicErrorCode.PARAM_INVALID, "excel内容非预期-" + failList.size());
        }
        return snowballModeKExcelCheckRsp.getSuccessList().stream().map(KExcelCheckModel::getData)
                .collect(Collectors.toList());
    }

    @Override
    public boolean handleAddUserRegistration(long userId, long activityId, List<Long> parentTaskIds, String source,
            Map<Long, AddRegistrationOptionBO> optionMap) {
        return true;
    }

    @Override
    public void handleRiskInterruptRegistration(Long userId, Long activityId, String riskReason) {
    }

    @Override
    public ActivitySeriesType getActivitySeriesType() {
        return ActivitySeriesType.ACTIVITY_SNOWBALL;
    }
}
