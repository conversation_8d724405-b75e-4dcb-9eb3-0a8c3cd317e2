package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.fixcontent;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldAssembleContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchContentTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Component
@Slf4j
public class LaunchLinkContentResolveStrategy extends LaunchDefaultFixContentResolveStrategy {

    @Resource
    private LaunchResolveService launchResolveService;

    @Override
    protected String postHandle(LaunchContentFieldConfigBO fieldConfig, LaunchFieldAssembleContext context,
            String content) {
        String fieldCode = fieldConfig.getFieldCode();
        LaunchConfigBO launchConfig = context.getLaunchConfig();
        String channel = launchConfig.getChannel();
        String scene = launchConfig.getScene();

        // 添加 suffix
        if (StringUtils.isNotBlank(content)) {
            String suffix = launchResolveService
                    .resolveLinkFieldSuffixByScene(channel, scene, fieldCode);
            if (StringUtils.isNotBlank(suffix)) {
                content = content + suffix;
            }
        }

        return content;
    }

    @Override
    public LaunchContentTypeEnum getContentType() {
        return LaunchContentTypeEnum.LINK;
    }
}
