package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum.MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
@Service
@Slf4j
public class SelectionDecisionChannelStrategyService implements NotificationChannelStrategyService {
    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        // do nothing
    }

    @Override
    public boolean pushSwitch() {
        return false;
    }
}
