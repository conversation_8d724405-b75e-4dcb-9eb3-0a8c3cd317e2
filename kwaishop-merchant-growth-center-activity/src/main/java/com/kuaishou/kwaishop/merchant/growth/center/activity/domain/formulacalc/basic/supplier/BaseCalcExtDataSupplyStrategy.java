package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.supplier;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.BaseCalcExtDataSupplyParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums.BaseCalcExtDataTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
public interface BaseCalcExtDataSupplyStrategy {

    /**
     * 补充基期计算附加信息
     */
    Map<String, Object> supply(BaseCalcExtDataSupplyParam param);

    /**
     * 获取基期附加数据类型
     */
    BaseCalcExtDataTypeEnum getType();
}
