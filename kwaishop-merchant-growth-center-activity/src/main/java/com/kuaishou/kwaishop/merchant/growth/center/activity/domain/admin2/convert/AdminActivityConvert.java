package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.convert;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum.AUDIT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum.ONLINE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum.ONLINE_END;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum.ONLINE_STARTING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryIndicatorValueTypeEnum.INCR_FIXED;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryIndicatorValueTypeEnum.INCR_PERCENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryIndicatorValueTypeEnum.NO_MORE_THAN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityBizType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getBusinessSupplyFromActivityExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getSignTypeEnum;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.isLiteActivity;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.isLiteDraftActivity;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveCategoryIdList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveIndicatorStepConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveItemGroupId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveSingleIndicatorTargetValue;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveTopic;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorResolver.resolveNecessaryIncBaseIndicatorList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.checkTaskAwardInAdvance;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveBasicConfigFromTaskExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveCycleBudgetConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveRoiCalcTimeType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum.DAREN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.resolver.StatisticsResolver.getLongFromAggregationWithPrefixKey;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeFenUnitAwardValueToYuan;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeYuanUnitAwardValueToFenWithCheck;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.resolver.BasicConfigResolver.getAllBasicFactorConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.resolver.BasicConfigResolver.getAllBasicFactorConfigSort;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.ACTIVITY_LIST_SHOW_DEFAULT_CONFIG_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.ACTIVITY_LIST_SHOW_LITE_CONFIG_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.INIT_SELLER_NUM_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.INIT_SELLER_NUM_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.SYSTEM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.StatisticsConstants.ACTIVITY_AWARD_SEND_BTN_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.StatisticsConstants.BEFORE_RISK_AWARD_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.StatisticsConstants.INIT_AWARD_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants.UNDER_LINE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.RegistrationConfigStatusEnum.EFFECT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.TaskCrowdTypeEnum.EVENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.activityListWithLaunchInfoSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.noNeedAggFinalAwardByMysqlSum;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.showLaunchConfigButtonActivityStatusList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.showNotificationButtonActivityStatusList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityDynamicShowIndicatorConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.resourceRuleTimeRangeConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.statisticsReviewConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.strategyAdminAllSceneConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.strategyAdminActivityDetailUrlTemplate;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.strategyAdminActivityListUrlTemplate;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.adminUserNameList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.enableAddSubActivityPatternType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.showLaunchConfigButtonActivityBizList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.showLaunchConfigButtonActivityPatternList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.showNotificationButtonActivityPatternList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.userAwardLimitConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.IndicatorHandlerUtil.parseCategoryLevelListToParamV2;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.NumberFormat.compareMoreWithZero;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkCollectionNotNull;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkNotNull;
import static com.kuaishou.kwaishop.merchant.growth.utils.converter.IndicatorUnitUtils.convertToStorageValue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.id.sequence.IdSequenceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.ActivityExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityCreateTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.IndicatorStepConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.StrategyActivityListBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.StrategyActivityListBO.ShowIndicatorBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.common.StrategyActivityQueryParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.SignTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.FixAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.FixTargetBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.RecordDiffBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.StrategyAdminLayerCrowdBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.AdminAllSceneConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ResourceRuleTimeRangeConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.UserAwardLimitConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.StrategyRelateConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.SubActivitySourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.RoiCalcTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorCalcRangeConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.IndicatorManageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.TaskManageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.AwardShowBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsReviewConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.CycleBudgetConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.TaskExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.CycleBudgetLimitType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskExternalSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityIdBizType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.TailNumberUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.RaceInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.AuditConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.registration.RegistrationConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.query.ActivityQueryCondition;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.aggregation.BaseInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.interest.center.resource.rule.ResourceRuleDTO;
import com.kuaishou.kwaishop.merchant.interest.center.resource.rule.RuleExtraDTO;
import com.kuaishou.kwaishop.merchant.resource.center.client.enums.RuleTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-03
 */
@Service
@Slf4j
@Lazy
public class AdminActivityConvert {

    @Autowired
    private IdSequenceService idSequenceService;

    /**
     * 最终奖励指标前缀key
     */
    private static final String AWARD_FINAL_TOTAL_PREFIX = "awardFinalTotal_";

    /**
     * 将前端请求筛选条件，转换成查询条件
     */
    public static ActivityQueryCondition buildActivityQueryCondition(StrategyActivityQueryParamBO queryParam) {
        ActivityQueryCondition queryCondition = new ActivityQueryCondition();
        queryCondition.setPageNo(queryParam.getPageNo());
        queryCondition.setPageSize(queryParam.getPageSize());
        queryCondition.setName(queryParam.getName());
        queryCondition.setStatus(queryParam.getStatus());
        queryCondition.setId(queryParam.getId());
        queryCondition.setApplication(queryParam.getApplication());
        queryCondition.setCreator(queryParam.getCreator());
        queryCondition.setLiteFlag(queryParam.getLiteFlag());
        queryCondition.setIndustryCodeList(queryParam.getIndustryCode());
        queryCondition.setSeriesType(ActivitySeriesType.ACTIVITY_STRATEGY_ADMIN.getValue());
        queryCondition.setCreateType(ActivityCreateTypeEnum.TASK_ADMIN.getCode());
        queryCondition.setOrderByIdDesc(true);
        if (StringUtils.isNotBlank(queryParam.getBizType())) {
            ActivityBizTypeEnum bizTypeEnum = ActivityBizTypeEnum.getByCode(queryParam.getBizType());
            queryCondition.setBizTypeEnum(bizTypeEnum);
        }
        queryCondition.setGeDrawEndTime(queryParam.getGeDrawEndTime());
        queryCondition.setOrderByCreateTimeDesc(queryParam.getOrderByCreateTimeDesc());
        queryCondition.setShow(queryParam.getShow());
        queryCondition.setPromotion(queryParam.getPromotion());
        // 筛选状态映射
        int status = queryParam.getStatus() == null ? 0 : queryParam.getStatus();
        ActivityStatusEnum statusEnum = ActivityStatusEnum.of(status);
        long now = System.currentTimeMillis();
        switch (statusEnum) {
            case ONLINE_UN_START:
                queryCondition.setStatus(ONLINE.getCode());
                queryCondition.setStartTime(now);
                break;
            case ONLINE_STARTING:
                queryCondition.setStatus(ONLINE.getCode());
                queryCondition.setLeStartTime(now);
                queryCondition.setGeEndTime(now);
                break;
            case ONLINE_END:
                queryCondition.setStatus(ONLINE.getCode());
                queryCondition.setEndTime(now);
                break;
            default:
                queryCondition.setStartTime(queryParam.getStartTime());
                queryCondition.setEndTime(queryParam.getEndTime());
        }
        return queryCondition;
    }

    /**
     * 构建列表对象
     */
    public static StrategyActivityListBO convertToActivityList(ActivityDO activityDO,
            List<StatisticsDO> statisticsDOList, List<RegistrationConfigDO> registrationConfigs, String operator) {
        // 统计记录
        StatisticsDO statisticsDO = statisticsDOList.stream()
                .filter(e -> e.getActivityId().equals(activityDO.getId()))
                .findFirst().orElse(null);
        ForecastDataBO forecastDataBO = ForecastDataBO.builder().build();
        Map<String, Object> aggregationMap = new HashMap<>();
        if (statisticsDO != null && StringUtils.isNotEmpty(statisticsDO.getForecastData())) {
            forecastDataBO = fromJSON(statisticsDO.getForecastData(), ForecastDataBO.class);
        }
        if (statisticsDO != null && StringUtils.isNotEmpty(statisticsDO.getAggregationInfo())) {
            aggregationMap = ObjectMapperUtils.fromJson(statisticsDO.getAggregationInfo());
        }
        ActivityExtBO activityExtBO = ActivityExtBO.builder().build();
        if (StringUtils.isNotEmpty(activityDO.getExt())) {
            activityExtBO = fromJSON(activityDO.getExt(), ActivityExtBO.class);
        }
        // 展示状态
        Integer showStatus = convertStatus(activityDO);
        // 复盘详情按钮
        boolean dataReviewButton = getDataReviewButton(statisticsDOList, activityDO);
        // 仅管理员可见按钮
        boolean addNewSubActivityButton = getAddNewSubActivityButton(activityDO, operator, showStatus, activityExtBO);
        // 触达按钮
        boolean notificationButton = getNotificationButton(activityDO, activityExtBO);
        // 投放按钮
        boolean launchConfigButton = getLaunchConfigButton(activityDO, activityExtBO);
        // 用户奖励上限按钮
        boolean userRewardLimitButton = getUserRewardLimitButton(activityDO, activityExtBO, operator);
        AdminAllSceneConfig adminAllSceneConfig = strategyAdminAllSceneConfig.getObject();
        // 按钮控制配置
        Map<String, Boolean> buttonControlConfig = adminAllSceneConfig.getButtonControlConfig();
        // 仅本人可见按钮
        boolean editButton = false;
        boolean stopEventButton = false;
        boolean startEventButton = false;
        boolean auditButton = false;
        boolean applyBudgetButton = false;
        if (activityDO.getCreator().equals(operator)) {
            // 编辑按钮
            editButton = true;
            // 终止报名/开启报名按钮
            List<RegistrationConfigDO> dynamicEventConfigs = registrationConfigs.stream()
                    .filter(e -> e.getActivityId().equals(activityDO.getId()))
                    .filter(e -> e.getType().equals(EVENT.getCode()))
                    .collect(Collectors.toList());
            if (showStatus.equals(ONLINE_STARTING.getCode()) && CollectionUtils.isNotEmpty(dynamicEventConfigs)) {
                boolean hasEffectiveConfig = dynamicEventConfigs.stream()
                        .anyMatch(e -> e.getStatus().equals(EFFECT.getCode()));
                if (hasEffectiveConfig) {
                    stopEventButton = true;
                } else {
                    startEventButton = true;
                }
            }
            // 提审按钮
            Boolean businessSupply = getBusinessSupplyFromActivityExt(activityDO);
            if (businessSupply != null && !businessSupply && activityDO.getStatus() == AUDIT.getCode()) {
                auditButton = true;
            }
            // 去申请预算按钮
            if (businessSupply != null && businessSupply && activityDO.getResourceActivityId() == 0) {
                applyBudgetButton = true;
            }
        }
        // 奖励发放按钮
        boolean awardToastButton = false;
        boolean awardSendButton = false;
        if (activityDO.getResourceActivityId() != 0) {
            boolean beforeRiskAward = getLongFromAggregationWithPrefixKey(aggregationMap, BEFORE_RISK_AWARD_PREFIX) > 0;
            boolean afterRisk = getLongFromAggregationWithPrefixKey(aggregationMap, INIT_AWARD_PREFIX) > 0;
            if (buttonControlConfig != null && BooleanUtils.isTrue(buttonControlConfig.get(String.format(ACTIVITY_AWARD_SEND_BTN_KEY,
                    activityDO.getId())))) {
                afterRisk = true;
            }
            // 奖励toast按钮，风控前可发人数大于0 && 风控后初始奖励金额 == 0
            awardToastButton = beforeRiskAward && !afterRisk;
            awardSendButton = afterRisk;
        }
        // 领取人数
        Long drawCount = Optional.ofNullable(forecastDataBO.getDrawCount()).orElse(0L);
        if (forecastDataBO.getDrawCountV2() != null) {
            drawCount = forecastDataBO.getDrawCountV2();
        }
        // 组装
        StrategyActivityListBO res = new StrategyActivityListBO();
        res.setAddDrawLimitButton(activityExtBO != null && activityExtBO.isLimitDrawCount()
                && activityDO.getStatus().equals(ActivityStatusEnum.ONLINE.getCode()));
        res.setActivityId(activityDO.getId());
        res.setResourceActivityId(activityDO.getResourceActivityId());
        res.setName(activityDO.getName());
        res.setActivityApplication(Objects.requireNonNull(activityExtBO).getApplication());
        res.setStatus(showStatus);
        res.setStartTime(activityDO.getStartTime());
        res.setEndTime(activityDO.getEndTime());
        res.setCreator(activityDO.getCreator());
        res.setIndustryCode(activityDO.getBizName());
        res.setCreateTime(activityDO.getCreateTime());
        res.setActivityPatternType(Optional.ofNullable(activityExtBO.getActivityPatternType()).orElse("-"));
        // 这四个字段改为动态列表展示
        res.setRegistrationCount(Optional.ofNullable(forecastDataBO.getRegistrationCount()).orElse(0L));
        res.setDrawCount(drawCount);
        res.setCompleteCount(Optional.ofNullable(forecastDataBO.getCompleteCount()).orElse(0L));
        res.setAwardCount(Optional.ofNullable(forecastDataBO.getAwardCount()).orElse(0L));
        // 以上四个字段
        // 通过用户活动statistic表来聚合发放奖励情况容易造成OOM，这里通过开关来控制读取，此开关会在前置链路控制是否通过mysql聚合发放奖励情况
        if (noNeedAggFinalAwardByMysqlSum.get()) {
            res.setAwardValueList(convertAwardValueListFromEsAgg(aggregationMap,
                    forecastDataBO.getAlreadySendAwardValue()));
        } else {
            res.setAwardValueList(convertAwardValueList(forecastDataBO.getAlreadySendAwardValue()));
        }
        res.setUserRewardLimitButton(userRewardLimitButton);
        res.setEditButton(editButton);
        res.setAuditButton(auditButton);
        res.setApplyBudgetButton(applyBudgetButton);
        res.setStopEventButton(stopEventButton);
        res.setStartEventButton(startEventButton);
        res.setDataReviewButton(dataReviewButton);
        res.setAddNewSubActivityButton(addNewSubActivityButton);
        res.setAwardToastButton(awardToastButton);
        res.setAwardSendButton(awardSendButton);
        res.setNotificationButton(notificationButton);
        res.setLaunchConfigButton(launchConfigButton);
        res.setIndicatorList(convertIndicatorList(aggregationMap, activityDO));
        res.setLiteFlag(isLiteActivity(activityDO) || isLiteDraftActivity(activityDO));
        ActivityBizTypeEnum activityBizType = getActivityBizType(activityDO);
        if (activityListWithLaunchInfoSwitch.get() && DAREN.equals(activityBizType)
                && activityDO.getStatus() == ONLINE.getCode()) {
            res.setActivityDetailUrl(String.format(strategyAdminActivityDetailUrlTemplate.get(), activityDO.getId()));
            res.setActivityListUrl(strategyAdminActivityListUrlTemplate.get());
        }
        return res;
    }

    private static boolean getUserRewardLimitButton(ActivityDO activityDO, ActivityExtBO activityExtBO, String operator) {
        UserAwardLimitConfig config = userAwardLimitConfig.getObject();
        if (config == null) {
            return false;
        }
        // 仅有预算绑定的活动在上线后，可设置容量限制
        if (!activityExtBO.getBusinessSupply()) {
            return false;
        }
        // 活动状态
        Set<Integer> buttonActivityStatus = config.getShowUserRewardLimitButtonActivityStatus();
        if (CollectionUtils.isEmpty(buttonActivityStatus) || !buttonActivityStatus.contains(activityDO.getStatus())) {
            return false;
        }

        //仅下发：任务、目标达成、新粉类型活动类型
        Set<String> activityPatternType = config.getShowUserRewardLimitButtonActivityPatternType();
        if (CollectionUtils.isEmpty(activityPatternType) || !activityPatternType.contains(activityExtBO.getActivityPatternType())) {
            return false;
        }

        // 仅活动创建人&产研可操作「用户奖励上限」
        Set<String> userWhiteList = config.getUserWhiteList();
        String creator = activityDO.getCreator();
        return operator.equals(creator) || (CollectionUtils.isNotEmpty(userWhiteList) && userWhiteList.contains(creator));
    }

    private static boolean getLaunchConfigButton(ActivityDO activityDO, ActivityExtBO activityExtBO) {
        return showLaunchConfigButtonActivityStatusList.get().contains(activityDO.getStatus())
                && showLaunchConfigButtonActivityPatternList.get().contains(activityExtBO.getActivityPatternType())
                && showLaunchConfigButtonActivityBizList.get().contains(activityExtBO.getBizType());
    }

    private static boolean getNotificationButton(ActivityDO activityDO, ActivityExtBO activityExtBO) {
        return showNotificationButtonActivityStatusList.get().contains(activityDO.getStatus())
                && showNotificationButtonActivityPatternList.get().contains(activityExtBO.getActivityPatternType());
    }

    /**
     * 仅管理员可见按钮
     */
    private static boolean getAddNewSubActivityButton(ActivityDO activityDO, String operator,
            Integer showStatus, ActivityExtBO activityExtBO) {
        boolean addNewSubActivityButton = false;
        if (adminUserNameList.get().contains(operator) && !isLiteActivity(activityDO)) {
            // 新增子活动按钮
            if (activityDO.getStatus().equals(ONLINE.getCode()) && !showStatus.equals(ONLINE_END.getCode())
                    && enableAddSubActivityPatternType.get().contains(activityExtBO.getActivityPatternType())) {
                addNewSubActivityButton = true;
            }
        }
        return addNewSubActivityButton;
    }

    /**
     * 复盘详情按钮
     */
    private static boolean getDataReviewButton(List<StatisticsDO> statisticsDOList,
            ActivityDO activityDO) {
        // 复盘详情按钮
        boolean dataReviewButton = false;
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        if (activityDO.getStatus().equals(ONLINE.getCode())) {
            StatisticsDO statistics = statisticsDOList.stream()
                    .filter(e -> e.getActivityId().equals(activityDO.getId())).findFirst().orElse(null);
            if (statistics != null && StringUtils.isNotBlank(statistics.getAggregationInfo())) {
                if (TailNumberUtils.isOnFor(activityDO.getId(), config.getReviewButtonGreyTailNumber())) {
                    dataReviewButton = true;
                }
            }
        }
        return dataReviewButton;
    }

    /**
     * 从前置es聚合出的最终奖励发放情况中获取数据
     */
    public static List<AwardShowBO> convertAwardValueListFromEsAgg(Map<String, Object> aggregationMap, Map<Integer,
            Long> alreadySendAwardValue) {
        // 如果聚合map为空，则尝试从之前的forecastDataBO中获取数据
        if (MapUtils.isEmpty(aggregationMap)) {
            return convertAwardValueList(alreadySendAwardValue);
        }
        try {
            Map<String, Object> filterAggregationMap = aggregationMap.entrySet().stream()
                    // 过滤出最终奖励发放指标
                    .filter(entry -> {
                        String key = entry.getKey();
                        return key.startsWith(AWARD_FINAL_TOTAL_PREFIX);
                    })
                    .collect(HashMap::new, (m, entry) -> m.put(entry.getKey(), entry.getValue()), HashMap::putAll);
            // 如果过滤map为空（没有最终奖励指标），则尝试从之前的forecastDataBO中获取数据
            if (MapUtils.isEmpty(filterAggregationMap)) {
                return convertAwardValueList(alreadySendAwardValue);
            }
            // 解析出对应的奖励类型以及奖励发放值塞到map中
            Map<Integer, Long> finalAwardMap = filterAggregationMap.entrySet().stream()
                    .collect(HashMap::new, (map, entry) -> {
                        String originKey = entry.getKey();
                        String[] split = originKey.split(UNDER_LINE);
                        if (split.length == 2) {
                            // 最终奖励key模板：awardFinalTotal_xxx，split后第二个元素为奖励类型
                            Integer awardType = Integer.valueOf(split[1]);
                            Object awardObj = entry.getValue();
                            // 发放奖励小于等于0，则不展示
                            if (awardObj != null && compareMoreWithZero(awardObj.toString())) {
                                map.put(awardType, Long.valueOf(String.valueOf(awardObj)));
                            }
                        }
                    }, HashMap::putAll);
            return convertAwardValueList(finalAwardMap);
        } catch (Exception e) {
            log.error("convertAwardValueListFromEsAgg happen error", e);
            return convertAwardValueList(alreadySendAwardValue);
        }
    }

    public static List<ShowIndicatorBO> convertIndicatorList(Map<String, Object> aggregationMap,
            ActivityDO activityDO) {
        // 获取展示的kconf
        Map<String, Object> showIndicatorMap = activityDynamicShowIndicatorConfig.getMap();
        if (MapUtils.isEmpty(showIndicatorMap)) {
            return new ArrayList<>();
        }
        SignTypeEnum signTypeEnum = getSignTypeEnum(activityDO);
        String showConfigKey = signTypeEnum.name();
        if (!showIndicatorMap.containsKey(showConfigKey)) {
            showConfigKey = ACTIVITY_LIST_SHOW_DEFAULT_CONFIG_KEY;
        }
        if (ActivityResolver.isLiteActivity(activityDO)) {
            showConfigKey = ACTIVITY_LIST_SHOW_LITE_CONFIG_KEY;
        }
        List<ShowIndicatorBO> indicatorConfig =
                ObjectMapperUtils.fromJSON(ObjectMapperUtils.toJSON(showIndicatorMap.get(showConfigKey)), List.class,
                        ShowIndicatorBO.class);
        List<ShowIndicatorBO> showIndicatorBOS = new ArrayList<>();
        // 添加预备下发人数
        ActivityExtBO activityExtBO = getActivityExt(activityDO);
        if (activityExtBO.getInitSellerNum() != null && activityDO.getStatus() != ONLINE.getCode()) {
            ShowIndicatorBO initIndicatorBO = new ShowIndicatorBO();
            initIndicatorBO.setKey(INIT_SELLER_NUM_KEY);
            initIndicatorBO.setName(INIT_SELLER_NUM_NAME);
            initIndicatorBO.setValue(activityExtBO.getInitSellerNum());
            showIndicatorBOS.add(initIndicatorBO);
        }
        for (ShowIndicatorBO config : indicatorConfig) {
            ShowIndicatorBO showIndicatorBO = new ShowIndicatorBO();
            showIndicatorBO.setKey(config.getKey());
            showIndicatorBO.setName(config.getName());
            showIndicatorBO.setValue(MapUtils.getLongValue(aggregationMap, config.getKey(), 0L));
            showIndicatorBOS.add(showIndicatorBO);
        }
        return showIndicatorBOS;
    }

    /**
     * 根据活动状态和时间转换成前端展示状态
     */
    public static Integer convertStatus(ActivityDO activityDO) {
        Integer status = activityDO.getStatus();
        ActivityStatusEnum statusEnum = ActivityStatusEnum.of(status);
        if (statusEnum != ONLINE) {
            return status;
        }
        long now = System.currentTimeMillis();
        if (activityDO.getStartTime() > now) {
            return ActivityStatusEnum.ONLINE_UN_START.getCode();
        } else if (activityDO.getEndTime() < now) {
            return ONLINE_END.getCode();
        } else {
            return ONLINE_STARTING.getCode();
        }
    }

    public static List<AwardShowBO> convertAwardValueList(Map<Integer, Long> alreadySendAwardValue) {
        List<AwardShowBO> awardShowBOList = Lists.newArrayList();
        if (MapUtils.isEmpty(alreadySendAwardValue)) {
            return awardShowBOList;
        }
        alreadySendAwardValue.forEach((awardType, awardValue) -> {
            AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
            String showAwardValue = changeFenUnitAwardValueToYuan(awardTypeEnum, awardValue);
            AwardShowBO awardShowBO = AwardShowBO.builder()
                    .awardType(awardType)
                    .awardName(awardTypeEnum.getShowName())
                    .awardValue(awardValue)
                    .awardShowValue(showAwardValue)
                    .unit(awardTypeEnum.getUnit()).build();
            awardShowBOList.add(awardShowBO);
        });
        return awardShowBOList;
    }

    /**
     * 将各个阶段的某个指标配置聚合成一个阶梯对象
     */
    public static List<IndicatorStepConfigBO> buildMultiStageStepConfig(long indicatorId,
            Map<Integer, List<IndicatorConfigDO>> multiTaskIndicator) {
        List<IndicatorStepConfigBO> res = Lists.newArrayList();
        multiTaskIndicator.forEach((stepOrder, list) -> {
            IndicatorConfigDO targetConfig =
                    list.stream().filter(e -> e.getIndicatorId().equals(indicatorId)).findFirst().orElse(null);
            checkNotNull(targetConfig, String.format("多阶段任务无法定位到指标%s", indicatorId));
            List<IndicatorStepConfigBO> stepConfigs = resolveIndicatorStepConfig(targetConfig);
            if (CollectionUtils.isEmpty(stepConfigs) || stepConfigs.size() != 1) {
                throw new BizException(BasicErrorCode.PARAM_INVALID,
                        String.format("任务阶段%s阶梯配置非预期", stepOrder));
            }
            IndicatorStepConfigBO stepConfig = new IndicatorStepConfigBO();
            stepConfig.setStep(stepOrder);
            stepConfig.setType(stepConfigs.get(0).getType());
            stepConfig.setValue(stepConfigs.get(0).getValue());
            res.add(stepConfig);
        });
        return res.stream().sorted(Comparator.comparing(IndicatorStepConfigBO::getStep)).collect(Collectors.toList());
    }

    /**
     * 转换人群对象
     */
    public static StrategyAdminLayerCrowdBO convertStrategyAdminLayerCrowdBO(TaskDO layerTask, Set<Long> sellerIds,
            StrategyRelateConfig strategyConfig) {
        // 子活动名称
        String subActivityName = layerTask.getName();
        // 子活动顺序
        int subActivityOrder = layerTask.getStage();
        // 子活动分层顺序
        int layerOrder = layerTask.getPriority();
        if (strategyConfig == null || strategyConfig.needCheckCrowd()) {
            checkCollectionNotNull(sellerIds, String.format("子活动%s分层%s的商家数为0", subActivityName, layerOrder));
        }
        // 组装
        StrategyAdminLayerCrowdBO layerCrowdBO = new StrategyAdminLayerCrowdBO();
        layerCrowdBO.setSubActivityOrder(subActivityOrder);
        layerCrowdBO.setLevelDesc(String.format("子活动%s(%s)的分层%s", subActivityOrder, subActivityName, layerOrder));
        layerCrowdBO.setLevelOrder(layerOrder);
        layerCrowdBO.setSellerIdList(sellerIds);
        return layerCrowdBO;
    }

    /**
     * 将指标配置列表转换成需要基值的指标列表
     */
    public Set<Long> convertNeedBaseIndicatorList(Map<Long, IndicatorDO> incReturnIndicatorMap,
            List<IndicatorConfigDO> indicatorConfigList) {
        // 所有targetValue是增量/增幅/原值的指标
        List<String> targetTypeList =
                Arrays.asList(INCR_FIXED.getType(), INCR_PERCENT.getType(), NO_MORE_THAN.getType());
        List<Long> processNeedBaseIndicatorList = indicatorConfigList.stream()
                .filter(e -> !e.getType().equals(EntityTypeEnum.AWARD.getCode()))
                .filter(e -> targetTypeList.contains(resolveSingleIndicatorTargetValue(e)))
                .map(IndicatorConfigDO::getIndicatorId)
                .collect(Collectors.toList());

        // 返点指标必需包含的基期指标
        List<Long> necessaryIncBaseIndicatorIds = incReturnIndicatorMap.values().stream()
                .flatMap(indicator -> resolveNecessaryIncBaseIndicatorList(indicator).stream())
                .distinct().collect(Collectors.toList());

        // 应该有的基值指标集合
        Set<Long> needBaseIndicatorList = Sets.newHashSet();
        needBaseIndicatorList.addAll(processNeedBaseIndicatorList);
        needBaseIndicatorList.addAll(incReturnIndicatorMap.keySet());
        needBaseIndicatorList.addAll(necessaryIncBaseIndicatorIds);
        return needBaseIndicatorList;
    }

    /**
     * 将指标配置列表，转换成<id : itemGroupId>的map
     */
    public static Map<Long, Long> convertItemGroupIdMap(TaskManageBO layerTaskManage) {
        Map<Long, Long> itemGroupMap = Maps.newHashMap();
        List<IndicatorConfigDO> subTaskIndicators = layerTaskManage.getSubTask().stream()
                .map(TaskManageBO::getIndicatorConfigList)
                .flatMap(List::stream)
                .map(IndicatorManageBO::getIndicatorConfig).collect(Collectors.toList());
        subTaskIndicators.forEach(subTaskIndicatorConfig -> {
            // 筛片筛选包信息提取
            Long itemGroupId = resolveItemGroupId(subTaskIndicatorConfig);
            if (itemGroupId != null) {
                itemGroupMap.put(subTaskIndicatorConfig.getIndicatorId(), itemGroupId);
            }
        });
        return itemGroupMap;
    }

    /**
     * 将指标配置列表，转换成<id : topic>的map
     */
    public static Map<Long, String> convertTopicMap(TaskManageBO layerTaskManage) {
        Map<Long, String> topicMap = Maps.newHashMap();
        List<IndicatorConfigDO> subTaskIndicators = layerTaskManage.getSubTask().stream()
                .map(TaskManageBO::getIndicatorConfigList)
                .flatMap(List::stream)
                .map(IndicatorManageBO::getIndicatorConfig).collect(Collectors.toList());
        subTaskIndicators.forEach(subTaskIndicatorConfig -> {
            // 话题信息提取
            String topic = resolveTopic(subTaskIndicatorConfig);
            if (StringUtils.isNotBlank(topic)) {
                topicMap.put(subTaskIndicatorConfig.getIndicatorId(), topic);
            }
        });
        return topicMap;
    }

    /**
     * 将指标配置列表，转换成<id : itemGroupId>的map
     */
    public static Map<Long, List<Long>> convertCategoryIdMap(TaskManageBO layerTaskManage) {
        // 类目信息
        Map<Long, List<Long>> categoryMap = Maps.newHashMap();
        List<IndicatorConfigDO> subTaskIndicators = layerTaskManage.getSubTask().stream()
                .map(TaskManageBO::getIndicatorConfigList)
                .flatMap(List::stream)
                .map(IndicatorManageBO::getIndicatorConfig).collect(Collectors.toList());
        subTaskIndicators.forEach(subTaskIndicatorConfig -> {
            // 类目信息提取
            List<Long> categoryIds = resolveCategoryIdList(subTaskIndicatorConfig);
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                categoryMap.put(subTaskIndicatorConfig.getIndicatorId(), categoryIds);
            }
        });
        return categoryMap;
    }

    /**
     * 将指标配置转换成查询DM的extraParams
     */
    public static Map<String, List<String>> buildDMQueryExtraParams(IndicatorConfigDO indicatorConfig) {
        Map<String, List<String>> extraParams = Maps.newHashMap();
        if (indicatorConfig == null) {
            return extraParams;
        }
        if (StringUtils.isNotBlank(indicatorConfig.getCalcRangeConfig())) {
            IndicatorCalcRangeConfig calcRangeConfig =
                    fromJSON(indicatorConfig.getCalcRangeConfig(), IndicatorCalcRangeConfig.class);
            Map<String, List<String>> categoryLevelListParam =
                    parseCategoryLevelListToParamV2(calcRangeConfig.getCategoryList());
            extraParams.putAll(categoryLevelListParam);
        }
        return extraParams;
    }

    public UserRegistrationRecordDO buildRegistrationTaskRecord(long userId, long activityId,
            long taskId, Map<String, Object> jsonMap,
            String source, AddRegistrationOptionBO option, List<String> tags) {
        UserRegistrationRecordDO taskRecordDO = new UserRegistrationRecordDO();
        // 生成主键ID
        if (taskRecordDO.getId() == null || taskRecordDO.getId() <= 0) {
            long id = idSequenceService.getId(ActivityIdBizType.USER_REGISTRATION_RECORD_ID_TYPE);
            taskRecordDO.setId(id);
        }
        UserRegistrationRecordExtBO extBO = UserRegistrationRecordExtBO.builder().build();
        if (StringUtils.isNotBlank(source)) {
            extBO.setSource(source);
        }
        if (option != null) {
            extBO.setOption(option);
        }
        if (CollectionUtils.isNotEmpty(tags)) {
            extBO.setTags(tags);
        }
        taskRecordDO.setExt(toJSON(extBO));
        taskRecordDO.setActivityId(activityId);
        taskRecordDO.setUserId(userId);
        taskRecordDO.setEntityType(TASK.getCode());
        taskRecordDO.setEntityId(taskId);
        taskRecordDO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        taskRecordDO.setJsonData(toJSON(jsonMap));
        taskRecordDO.setRegistrationTime(System.currentTimeMillis());
        taskRecordDO.setCreateTime(System.currentTimeMillis());
        taskRecordDO.setUpdateTime(System.currentTimeMillis());
        taskRecordDO.setCreator(SYSTEM);
        taskRecordDO.setModifier(SYSTEM);
        return taskRecordDO;
    }

    public UserRegistrationRecordDO buildRiskRegistrationTaskRecord(long userId, long activityId,
            long taskId, Map<String, Object> jsonMap,
            String riskReason, AddRegistrationOptionBO option, List<String> tags) {
        UserRegistrationRecordDO taskRecordDO =
                buildRegistrationTaskRecord(userId, activityId, taskId, jsonMap, "online", option, tags);
        taskRecordDO.setStatus(UserRegistrationStatusEnum.RISK.getCode());
        // 回写风控原因
        if (StringUtils.isNotBlank(riskReason)) {
            UserRegistrationRecordExtBO recordExtBO = fromJSON(taskRecordDO.getExt(),
                    UserRegistrationRecordExtBO.class);
            recordExtBO.setRiskReason(riskReason);
            taskRecordDO.setExt(toJSON(recordExtBO));
        }
        return taskRecordDO;
    }

    public UserRegistrationRecordDO buildRegistrationActivityRecord(long userId, long activityId, String source) {
        UserRegistrationRecordDO activityRecordDO = new UserRegistrationRecordDO();
        // 生成主键ID
        if (activityRecordDO.getId() == null || activityRecordDO.getId() <= 0) {
            long id = idSequenceService.getId(ActivityIdBizType.USER_REGISTRATION_RECORD_ID_TYPE);
            activityRecordDO.setId(id);
        }
        if (StringUtils.isNotBlank(source)) {
            UserRegistrationRecordExtBO extBO = UserRegistrationRecordExtBO.builder().source(source).build();
            activityRecordDO.setExt(toJSON(extBO));
        }
        activityRecordDO.setUserId(userId);
        activityRecordDO.setActivityId(activityId);
        activityRecordDO.setEntityType(EntityTypeEnum.ACTIVITY.getCode());
        activityRecordDO.setEntityId(activityId);
        activityRecordDO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        activityRecordDO.setRegistrationTime(System.currentTimeMillis());
        activityRecordDO.setCreateTime(System.currentTimeMillis());
        activityRecordDO.setUpdateTime(System.currentTimeMillis());
        activityRecordDO.setCreator(SYSTEM);
        activityRecordDO.setModifier(SYSTEM);
        return activityRecordDO;
    }

    public UserRegistrationRecordDO buildRiskRegistrationActivityRecord(long userId, long activityId, String source) {
        UserRegistrationRecordDO activityRecordDO = buildRegistrationActivityRecord(userId, activityId, source);
        activityRecordDO.setStatus(UserRegistrationStatusEnum.RISK.getCode());
        return activityRecordDO;
    }

    public static AuditConfigDO convertAuditConfigUpdateParam(AuditConfigDO auditConfigDO, String riskCode,
            String operator) {
        AuditConfigDO updateParam = new AuditConfigDO();
        updateParam.setId(auditConfigDO.getId());
        updateParam.setVersion(auditConfigDO.getVersion());
        updateParam.setRiskCode(riskCode);
        updateParam.setModifier(operator);
        return updateParam;
    }

    public static TaskDO convertNewAddSubActivityTaskUpdateParam(TaskDO taskDO) {
        TaskExtBO taskExtBO = StringUtils.isBlank(taskDO.getExt()) ? null : fromJSON(taskDO.getExt(), TaskExtBO.class);
        if (taskExtBO == null) {
            taskExtBO = new TaskExtBO();
        }
        taskExtBO.setAddFlag(SubActivitySourceEnum.ONLINE_IN_PROGRESS.getCode());
        TaskDO updateParam = new TaskDO();
        updateParam.setId(taskDO.getId());
        updateParam.setVersion(taskDO.getVersion());
        updateParam.setResourceRuleId(taskDO.getResourceRuleId());
        updateParam.setStage(taskDO.getStage());
        updateParam.setExt(toJSON(taskExtBO));
        return updateParam;
    }

    public static RegistrationConfigDO convertRegistrationConfigUpdateParam(RegistrationConfigDO auditConfigDO,
            String operator) {
        RegistrationConfigDO updateParam = new RegistrationConfigDO();
        updateParam.setId(auditConfigDO.getId());
        updateParam.setVersion(auditConfigDO.getVersion());
        updateParam.setStatus(EFFECT.getCode());
        updateParam.setModifier(operator);
        return updateParam;
    }

    public static FixTargetBO buildFixTargetBO(long userId, long indicatorId, int step, long value, String unit) {
        long targetValue = convertToStorageValue(value, unit);
        FixTargetBO fixTargetBO = new FixTargetBO();
        fixTargetBO.setUserId(userId);
        fixTargetBO.setStep(step);
        fixTargetBO.setIndicatorId(indicatorId);
        fixTargetBO.setValue(targetValue);
        return fixTargetBO;
    }

    public static FixAwardBO buildFixAwardBO(long userId, int awardType, int step, long value) {
        AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
        String awardCount = String.valueOf(value);
        long awardValue = changeYuanUnitAwardValueToFenWithCheck(awardTypeEnum, awardCount, true);
        FixAwardBO fixAwardBO = new FixAwardBO();
        fixAwardBO.setUserId(userId);
        fixAwardBO.setStep(step);
        fixAwardBO.setAwardType(awardType);
        fixAwardBO.setValue(awardValue);
        return fixAwardBO;
    }

    public static RecordDiffBO buildDiff(String field, String before, String after) {
        String beforeDesc = Optional.ofNullable(before).orElse("NULL");
        String afterDesc = Optional.ofNullable(after).orElse("NULL");
        return RecordDiffBO.builder()
                .field(field).before(beforeDesc).after(afterDesc).build();
    }

    /**
     * 构造赛道列表
     */
    public static List<RaceInfoBO> buildTraceInfoList(List<TaskDO> parentTaskList) {
        return parentTaskList.stream().map(parentTask -> {
            RaceInfoBO raceInfoBO = new RaceInfoBO();
            raceInfoBO.setRaceNumber(parentTask.getPriority());
            raceInfoBO.setRaceName(parentTask.getName());
            return raceInfoBO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建排行榜任务更新参数
     */
    public static TaskDO buildRankBindTaskParam(TaskDO bindTask, Long selectionGroup, String externalId) {
        TaskExtBO extBO = new TaskExtBO();
        if (StringUtils.isNotBlank(bindTask.getExt())) {
            extBO = fromJSON(bindTask.getExt(), TaskExtBO.class);
        }
        extBO.setDeliveryCrowdId(selectionGroup);
        TaskDO updateParam = new TaskDO();
        updateParam.setId(bindTask.getId());
        updateParam.setExt(toJSON(extBO));
        updateParam.setExternalId(externalId);
        updateParam.setExternalSource(TaskExternalSourceEnum.RANK.getType());
        updateParam.setVersion(bindTask.getVersion());
        return updateParam;
    }

    public static BaseInfoDTO buildBaseInfoDTO(BasicConfigBO basicConfig) {
        BaseInfoDTO.Builder baseInfoBuild = BaseInfoDTO.newBuilder();
        if (basicConfig != null) {
            List<BasicFactorConfigBO> basicFactorConfigBOList = getAllBasicFactorConfigSort(basicConfig);
            BasicFactorConfigBO basicFactorConfigBO =
                    CollectionUtils.emptyIfNull(basicFactorConfigBOList).stream().findFirst().orElse(null);
            if (null == basicFactorConfigBO) {
                return baseInfoBuild.build();
            }
            if (null != basicFactorConfigBO.getFixedStartTime()) {
                baseInfoBuild.setFixedStartTime(basicFactorConfigBO.getFixedStartTime());
            }
            if (null != basicFactorConfigBO.getFixedEndTime()) {
                baseInfoBuild.setFixedEndTime(basicFactorConfigBO.getFixedEndTime());
            }
            baseInfoBuild.setBaseAlgorithm(basicFactorConfigBO.getBaseAlgorithm().getType());
            baseInfoBuild.setIndicatorTimeType(basicFactorConfigBO.getIndicatorTimeType().getType());
        }
        return baseInfoBuild.build();
    }

    /**
     * 构建横向发奖单对象
     */
    public static ResourceRuleDTO buildResourceRule(String ruleBizId, TaskDO parentTask, TaskDO childTask,
            Long resourceActivityId, String operator) {
        // 绑定额外信息
        List<RuleExtraDTO> ruleExtras = buildResourceRuleExtra(parentTask);
        // 发奖单名称
        String name = String.format("子活动「%s」分层%s周期%s发奖单", childTask.getName(),
                parentTask.getPriority(), childTask.getPriority());

        // 获取发奖单时间范围
        ResourceRuleTimeRange resourceRuleTimeRange = getResourceRuleTimeRange(parentTask, childTask);

        ResourceRuleDTO.Builder resBuilder = ResourceRuleDTO.newBuilder()
                .setName(name)
                .setActivityId(resourceActivityId)
                .setBizId(ruleBizId)
                .setType(RuleTypeEnum.PORTRAIT_STRATEGY_PLATFORM.getId())
                .setStartTime(resourceRuleTimeRange.startTime)
                .setEndTime(resourceRuleTimeRange.endTime)
                .setOperator(operator)
                .addAllRuleExtraDto(ruleExtras);
        CycleBudgetConfigBO cycleBudgetConfigBO = resolveCycleBudgetConfig(childTask);
        // 获取任务管控预算
        if (cycleBudgetConfigBO != null && cycleBudgetConfigBO.getBudgetLimitType() != null
                && CycleBudgetLimitType.INDICATOR_SEQUENCE.getType() == cycleBudgetConfigBO.getBudgetLimitType()
                && cycleBudgetConfigBO.valid()) {
            resBuilder.setCapacity(cycleBudgetConfigBO.getBudgetLimit());
        }
        return resBuilder.build();
    }

    private static ResourceRuleTimeRange getResourceRuleTimeRange(TaskDO parentTask, TaskDO childTask) {
        Long startTime = childTask.getStartTime();
        Long endTime = childTask.getEndTime();

        // 使用父任务起止时间白名单
        Long parentTaskId = parentTask.getId();
        ResourceRuleTimeRangeConfig timeRangeConfig = resourceRuleTimeRangeConfig.getObject();
        List<String> startTimeWithParentTaskIds = timeRangeConfig.getStartTimeWithParentTaskIds();
        List<String> endTimeWithParentTaskIds = timeRangeConfig.getEndTimeWithParentTaskIds();
        if (CollectionUtils.isNotEmpty(startTimeWithParentTaskIds) && startTimeWithParentTaskIds.contains(
                String.valueOf(parentTaskId))) {
            startTime = parentTask.getStartTime();
        }
        if (CollectionUtils.isNotEmpty(endTimeWithParentTaskIds) && endTimeWithParentTaskIds.contains(
                String.valueOf(parentTaskId))) {
            endTime = parentTask.getEndTime();
        }
        return new ResourceRuleTimeRange(startTime, endTime);
    }

    /**
     * 构建发奖单额外参数
     */
    private static List<RuleExtraDTO> buildResourceRuleExtra(TaskDO parentTask) {
        List<RuleExtraDTO> ruleExtraList = Lists.newArrayList();
        RuleExtraDTO parentTaskId = RuleExtraDTO.newBuilder()
                .setName("parentTaskId")
                .setValue(String.valueOf(parentTask.getId()))
                .build();
        RuleExtraDTO subActivityName = RuleExtraDTO.newBuilder()
                .setName("subActivityName")
                .setValue(parentTask.getName())
                .build();
        ruleExtraList.add(parentTaskId);
        ruleExtraList.add(subActivityName);
        // 标签
        List<String> ruleTags = Lists.newArrayList();
        if (checkTaskAwardInAdvance(parentTask)) {
            ruleTags.add("needDeduct");
        }
        RoiCalcTimeTypeEnum roiCalcTimeTypeEnum = resolveRoiCalcTimeType(parentTask);
        if (roiCalcTimeTypeEnum == RoiCalcTimeTypeEnum.TASK_TIME) {
            ruleTags.add("roiUseTaskTime");
        }
        if (CollectionUtils.isNotEmpty(ruleTags)) {
            RuleExtraDTO ruleTagsDTO = RuleExtraDTO.newBuilder()
                    .setName("ruleTags")
                    .setValue(StringUtils.join(ruleTags, ","))
                    .build();
            ruleExtraList.add(ruleTagsDTO);
        }
        BasicConfigBO basicConfigBO = resolveBasicConfigFromTaskExt(parentTask);
        if (basicConfigBO == null) {
            return ruleExtraList;
        }
        List<BasicFactorConfigBO> basicFactorConfigBOList = getAllBasicFactorConfig(basicConfigBO);
        BasicFactorConfigBO basicFactorConfigBO =
                CollectionUtils.emptyIfNull(basicFactorConfigBOList).stream().findFirst().orElse(null);
        if (null == basicFactorConfigBO || null == basicFactorConfigBO.getFixedStartTime()
                || null == basicFactorConfigBO.getFixedEndTime()) {
            return ruleExtraList;
        }
        RuleExtraDTO fixedStartTime = RuleExtraDTO.newBuilder()
                .setName("fixedStartTime")
                .setValue(String.valueOf(basicFactorConfigBO.getFixedStartTime()))
                .build();
        RuleExtraDTO fixedEndTime = RuleExtraDTO.newBuilder()
                .setName("fixedEndTime")
                .setValue(String.valueOf(basicFactorConfigBO.getFixedEndTime()))
                .build();
        ruleExtraList.add(fixedStartTime);
        ruleExtraList.add(fixedEndTime);
        return ruleExtraList;
    }

    @Getter
    @AllArgsConstructor
    private static class ResourceRuleTimeRange {
        /**
         * 发奖单开始时间
         */
        private final Long startTime;
        /**
         * 发奖单结束时间
         */
        private final Long endTime;
    }
}
