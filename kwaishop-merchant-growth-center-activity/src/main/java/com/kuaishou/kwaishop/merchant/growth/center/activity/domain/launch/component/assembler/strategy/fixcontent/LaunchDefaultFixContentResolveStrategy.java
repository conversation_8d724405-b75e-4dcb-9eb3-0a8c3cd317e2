package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.fixcontent;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldAssembleContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldResolveContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldResolveResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFixContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.LaunchFieldResolveStrategy;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldConfigBO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Slf4j
public abstract class LaunchDefaultFixContentResolveStrategy implements LaunchFieldResolveStrategy {

    @Override
    public LaunchFieldResolveResultBO resolve(LaunchFieldResolveContextBO context) {
        LaunchContentFieldConfigBO fieldConfig = context.getFieldConfig();
        LaunchFieldAssembleContext fieldAssembleContext = context.getFieldAssembleContext();

        Map<String, Object> result = new HashMap<>();
        String fieldCode = fieldConfig.getFieldCode();
        String contentValue = fieldConfig.getContentValue();
        if (StringUtils.isBlank(contentValue)) {
            result.put(fieldCode, StringUtils.EMPTY);
            return LaunchFieldResolveResultBO.getDefaultResult();
        }

        LaunchFixContentBO launchFixContent =
                ObjectMapperUtils.fromJSON(contentValue, LaunchFixContentBO.class);

        String content = StringUtils.defaultString(launchFixContent.getFixContent(), "");
        // 子类处理
        content = postHandle(fieldConfig, fieldAssembleContext, content);

        result.put(fieldCode, content);

        return LaunchFieldResolveResultBO.builder().resMap(result).build();
    }

    protected String postHandle(LaunchContentFieldConfigBO fieldConfig, LaunchFieldAssembleContext context,
            String content) {
        return content;
    }
}
