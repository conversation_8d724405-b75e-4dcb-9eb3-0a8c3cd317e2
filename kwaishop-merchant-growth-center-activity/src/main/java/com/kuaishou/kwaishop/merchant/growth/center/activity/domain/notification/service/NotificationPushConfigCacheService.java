package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.GetNotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
public interface NotificationPushConfigCacheService {
    List<NotificationPushConfigBO> getNotificationPushConfigByIdxLoadingCache(
            GetNotificationPushConfigBO getNotificationPushConfigBO);

    NotificationPushConfigBO getNotificationPushConfigByIdLoadingCache(long id);
}
