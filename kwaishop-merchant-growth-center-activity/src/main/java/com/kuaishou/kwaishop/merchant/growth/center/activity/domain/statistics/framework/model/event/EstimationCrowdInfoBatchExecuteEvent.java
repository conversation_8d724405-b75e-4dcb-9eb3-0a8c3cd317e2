package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EstimationCrowdInfoBatchExecuteEvent extends BatchExecuteEvent {

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略版本
     */
    private String strategyVersion;

    /**
     * 策略准备同步版本
     */
    private String prepareSyncVersion;

    /***
     * 策略聚合根
     */
    private EstimationStrategyAggregateRoot strategyAggregateRoot;

    /**
     * 自定义基期数据上传
     */
    private Boolean customizeBasic;

    /**
     * 基期指标config
     */
    private Map<Long, IndicatorDO> baseIndicatorMap;
    /**
     * 指标id -> jsonKey信息
     */
    private Map<Long, String> jsonKeyMap;

}
