package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskOperationConfigBO {
    /**
     * 管控活动集合
     */
    private List<Long> blackActivityId;
    /**
     * 管控任务集合
     */
    private List<Long> blackTaskId;
}
