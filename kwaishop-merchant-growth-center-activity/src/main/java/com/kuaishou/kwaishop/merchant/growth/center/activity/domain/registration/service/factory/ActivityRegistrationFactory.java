package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.frame.ActivityRegistrationAbstract;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-23
 */
@Slf4j
@Service
@Lazy
public class ActivityRegistrationFactory {

    private Map<String, ActivityRegistrationAbstract> registrationAbstractMap;

    @Autowired
    private List<ActivityRegistrationAbstract> registrationAbstractList;

    @PostConstruct
    private void init() {
        registrationAbstractMap = new HashMap<>();
        registrationAbstractList
                .forEach(p -> registrationAbstractMap.put(p.getTag(), p));
    }

    /**
     * 根据任务组聚合类型获取对应的处理类
     */
    public ActivityRegistrationAbstract getRegistrationService(String tag) {
        return registrationAbstractMap.get(tag);
    }
}
