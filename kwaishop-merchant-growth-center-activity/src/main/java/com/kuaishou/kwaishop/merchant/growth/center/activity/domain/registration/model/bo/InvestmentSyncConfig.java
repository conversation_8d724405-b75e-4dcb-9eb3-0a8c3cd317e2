package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-16
 */
@Data
public class InvestmentSyncConfig {
    /**
     * 是否领取全父任务
     */
    private boolean allDraw;
    /**
     * 指定父任务集合
     */
    private List<String> parentAliasList;
    /**
     * 过滤人群不需要资格
     */
    private Long filterCrowdId;
    /**
     * 兜底商家任务
     */
    private List<String> defaultAliasList;
}
