package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveCycleDuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Component
@Slf4j
public class PreSegmentFetchStrategy implements BaseDataSourceFetchStrategy {

    @Resource
    private AdminActivityOnlineService adminActivityOnlineService;

    @Resource
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    public BaseDataSourceFetchResult fetch(BaseDataSourceFetchParam param) {

        long userId = param.getUserId();
        IndicatorConfigDO indicatorConfig = param.getIndicatorConfig();
        IndicatorDO indicator = param.getIndicator();
        List<String> dateList = param.getDateList();

        long cycleDuration = 0L;
        if (indicatorConfig != null) {
            Long parentTaskId = indicatorConfig.getEntityId();
            Long activityId = indicatorConfig.getActivityId();

            cycleDuration = getCycleDuration(userId, activityId, parentTaskId);
        } else {
            cycleDuration = param.getCycleDuration();
        }

        if (dateList.size() < cycleDuration) {
            log.warn("[前置拆分基期DM查询] 基期时间小于活动期时间 param:{}", toJSON(param));
            return BaseDataSourceFetchResult.builder()
                    .rawIndicatorBaseValues(Lists.newArrayList())
                    .multiSegmentMap(Maps.newHashMap())
                    .build();
        }

        List<List<String>> datePartitions = Lists.newArrayList();

        List<String> datePartition = Lists.newArrayList();
        for (String date : dateList) {
            datePartition.add(date);
            if (datePartition.size() == cycleDuration) {
                datePartitions.add(Lists.newArrayList(datePartition));
                datePartition = Lists.newArrayList();
            }
        }

        if (CollectionUtils.isEmpty(datePartitions)) {
            log.warn("[前置拆分基期DM查询] 拆分基期时间为空 param:{}", toJSON(param));
            return BaseDataSourceFetchResult.builder()
                    .rawIndicatorBaseValues(Lists.newArrayList())
                    .multiSegmentMap(Maps.newHashMap())
                    .build();
        }


        Map<String, List<Map<String, Object>>> multiSegmentRawDataMap = new HashMap<>();
        datePartitions.forEach(partition -> {
            if (CollectionUtils.isEmpty(partition)) {
                return;
            }
            List<Map<String, Object>> rawDataList = adminActivityOnlineService.querySingleBaseIndicatorRawValue(
                    userId, indicatorConfig, indicator, partition);
            String segmentKey = Joiner.on("_").join(partition.get(0), partition.get(partition.size() - 1));
            multiSegmentRawDataMap.put(segmentKey, rawDataList);
        });

        log.info("[前置拆分基期查询策略] 查询成功 param:{}, result:{}", toJSON(param), toJSON(multiSegmentRawDataMap));

        return BaseDataSourceFetchResult.builder().multiSegmentMap(multiSegmentRawDataMap).build();
    }

    private long getCycleDuration(long userId, long activityId, long parentTaskId) {
        TaskDO parentTask = taskLocalCacheService.getTaskByTaskId(parentTaskId);
        if (parentTask == null) {
            log.error("[前置拆分基期查询策略] 子任务查询为空 userId:{}, activityId:{}, parentTaskId:{}",
                    userId, activityId, parentTaskId);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "子任务查询为空");
        }

        return resolveCycleDuration(parentTask);
    }

    @Override
    public BaseDataSourceFetchTypeEnum getType() {
        return BaseDataSourceFetchTypeEnum.PRE_SEGMENT;
    }
}
