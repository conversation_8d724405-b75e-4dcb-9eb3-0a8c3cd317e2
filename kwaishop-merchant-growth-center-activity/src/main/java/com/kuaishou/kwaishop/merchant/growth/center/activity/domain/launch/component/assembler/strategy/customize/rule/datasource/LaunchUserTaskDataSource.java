package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchAssemblerConstantBO.LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchUserTaskDataSource implements LaunchDataSource {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private UserTaskRecordDAO userTaskRecordDAO;

    @Resource
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    @SuppressWarnings("unchecked")
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();
        Long userId = param.getUserId();
        Integer entityType = param.getEntityType();
        List<Long> entityIds = param.getEntityIds();

        List<TaskDO> taskConfigs = Lists.newArrayList();
        List<UserTaskRecordDO> userTaskRecords = Lists.newArrayList();

        Map<String, Object> resMap = Maps.newHashMap();

        // 获取用户资格信息
        Map<String, Object> customizeParamMap = param.getCustomizeParamMap();
        List<UserRegistrationRecordBO> userRegistrationRecords = (List<UserRegistrationRecordBO>)
                MapUtils.getObject(customizeParamMap, LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS);

        // 资格为空直接返回
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchUserTaskData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(entityType);
        switch (entityTypeEnum) {
            case SUB_ACTIVITY:
                // 根据子活动ID拉取任务配置
                taskConfigs = launchResolveService
                        .resolveRegistrationTaskConfigBySubActivityId(activityId, entityIds, userRegistrationRecords);
                if (CollectionUtils.isEmpty(taskConfigs)) {
                    break;
                }

                userTaskRecords = userTaskRecordDAO.queryUserTaskRecordList(userId, activityId,
                        taskConfigs.stream().map(TaskDO::getId).collect(Collectors.toList()));

                break;
            default:
                taskConfigs = taskLocalCacheService.getTaskListByActivityId(activityId);
                userTaskRecords = userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false);
        }

        LaunchUserTaskData data =
                LaunchUserTaskData.builder().taskConfigs(taskConfigs).userTaskRecords(userTaskRecords).build();

        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.USER_TASK_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchUserTaskData {

        /**
         * 任务配置列表
         */
        private List<TaskDO> taskConfigs;

        /**
         * 用户任务记录列表
         */
        private List<UserTaskRecordDO> userTaskRecords;

        public static LaunchUserTaskData empty() {
            return LaunchUserTaskData.builder()
                    .taskConfigs(Lists.newArrayList())
                    .userTaskRecords(Lists.newArrayList())
                    .build();
        }
    }
}
