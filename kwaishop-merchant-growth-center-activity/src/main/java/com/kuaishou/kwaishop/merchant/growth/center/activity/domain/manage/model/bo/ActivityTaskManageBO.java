package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTaskManageBO {
    /**
     * 活动模板
     */
    private ActivityManageBO activityManageBO;
    /**
     * 活动下的任务模板
     */
    private List<TaskManageBO> taskList;
}
