package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.STEP_COMPLETE_NUM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.progressNotificationPushLimitSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.StepProgressPushBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.StepShowProgressResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.TaskShowProgressCalculator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-22
 */
@Service
@Slf4j
public class StepProgressParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private TaskShowProgressCalculator taskShowProgressCalculator;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(STEP_COMPLETE_NUM);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {

        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();

        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[阶梯进度模板参数]触达控制终止，触达停止，userId:{}, configId:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }

        long activityId = configBO.getActivityId();
        long taskId = configBO.getEntityId();
        long configId = configBO.getId();

        StepShowProgressResultBO stepShowProgressResult = taskShowProgressCalculator
                .calcStepShowProgressResult(userId, activityId, taskId);

        Integer completeStepNum = stepShowProgressResult.getCompleteStepNum();
        if (completeStepNum == 0) {
            log.info("[阶梯进度模板参数] 没有完成阶梯 userId:{}, config:{}", userId, toJSON(configBO));
            result.setExecuteStatus(SKIP);
            return result;
        }

        // 是否已经推送进度
        String redisKey = NotificationRedisKeyEnum.STEP_PROGRESS_NOTIFICATION_PUSH
                .getFullKeyJoinWithColon(userId, configId);
        String redisValue = getGrowthRedisCommands().get(redisKey);

        if (progressNotificationPushLimitSwitch.get() && StringUtils.isNotBlank(redisValue)) {
            StepProgressPushBO stepProgressPushBO = fromJSON(redisValue, StepProgressPushBO.class);
            Integer pushedCompleteStepNum = stepProgressPushBO.getPushedCompleteStepNum();

            if (Objects.nonNull(pushedCompleteStepNum) && pushedCompleteStepNum > completeStepNum) {
                result.setExecuteStatus(SKIP);
                log.info("[任务进度模板参数] 进度已经重复推送，本次推送跳过 userId:{}, config:{}, completeStepNum:{}",
                        userId, toJSON(configBO), completeStepNum);
                return result;
            }
        }

        UserTaskRecordDO userTaskRecord = userTaskRecordDAO.queryUserTaskRecord(userId, activityId, taskId, true);
        long expireTime = userTaskRecord.getEndTime() - System.currentTimeMillis();
        StepProgressPushBO stepProgressPushBO = StepProgressPushBO.builder()
                .pushedCompleteStepNum(completeStepNum).build();
        getGrowthRedisCommands().psetex(redisKey, expireTime, toJSON(stepProgressPushBO));

        params.put(STEP_COMPLETE_NUM.getName(), String.valueOf(completeStepNum));
        log.info("[阶梯进度模板参数] 完成阶梯 userId:{}, config:{}, param:{}",
                userId, toJSON(configBO), toJSON(params));
        return result;
    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals(STEP_COMPLETE_NUM.getName()))
                .findFirst().orElse(null);
    }
}
