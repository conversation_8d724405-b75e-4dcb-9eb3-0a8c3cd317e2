package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.darenWorkbenchAwardCarouselConfig;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.bo.config.DarenWorkbenchAwardCarouselConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.bo.enums.CarouselSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.bo.item.DarenWorkbenchAwardCarouselItem;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.datesource.DefaultScorePriorityDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.datesource.ScorePriorityDataSource;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-12
 */
@Component
@Slf4j
public class DarenWorkBenchAwardCarousel extends AbstractScorePriorityCarousel<DarenWorkbenchAwardCarouselItem> {

    @Autowired
    private DefaultScorePriorityDataSource<DarenWorkbenchAwardCarouselItem> defaultScorePriorityDataSource;

    @Override
    public int capacity() {
        DarenWorkbenchAwardCarouselConfigBO config = darenWorkbenchAwardCarouselConfig.getObject();
        if (config != null && config.getCapacity() != null) {
            return config.getCapacity();
        }
        return 0;
    }

    @Override
    public boolean filter(DarenWorkbenchAwardCarouselItem item) {
        long minAwardValue = 0L;
        long maxAwardValue = Long.MAX_VALUE;
        DarenWorkbenchAwardCarouselConfigBO config = darenWorkbenchAwardCarouselConfig.getObject();
        if (config != null) {
            minAwardValue = config.getMinAwardValue() != null ? config.getMinAwardValue() : minAwardValue;
            maxAwardValue = config.getMaxAwardValue() != null ? config.getMaxAwardValue() : maxAwardValue;
        }
        return item.getAwardValue() >= minAwardValue && item.getAwardValue() <= maxAwardValue;
    }

    @Override
    public void expire() {

    }

    @Override
    public CarouselSceneEnum getScene() {
        return CarouselSceneEnum.DAREN_WORKBENCH_AWARD;
    }

    @Override
    protected List<DarenWorkbenchAwardCarouselItem> getInitCollection() {
        return null;
    }

    @Override
    protected double calcAndSupplyScore(DarenWorkbenchAwardCarouselItem item) {
        long awardValue = item.getAwardValue();
        item.setScore(awardValue);
        return awardValue;
    }

    @Override
    protected void preProcess(DarenWorkbenchAwardCarouselItem item) {

    }

    @Override
    protected List<DarenWorkbenchAwardCarouselItem> postProcess(List<DarenWorkbenchAwardCarouselItem> items) {
        return items;
    }

    @Override
    protected ScorePriorityDataSource<DarenWorkbenchAwardCarouselItem> getDataSource() {
        return defaultScorePriorityDataSource;
    }
}
