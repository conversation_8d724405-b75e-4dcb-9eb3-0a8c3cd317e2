package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.StrategyEstimationSourceEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户报名记录扩展数据
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationRecordExtBO {
    /**
     * 报名的来源
     */
    private String source;

    /**
     * 退出活动的来源
     */
    private String quitSource;

    /**
     * 原始基值
     */
    private Map<String, Object> originBasicData;
    /**
     * 自定义准入条件
     */
    private AddRegistrationOptionBO option;

    /**
     * 下发被风控原因
     */
    private String riskReason;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 测算来源
     */
    private StrategyEstimationSourceEnum estimationSource;
    /**
     * 是否二次风控
     */
    private boolean riskInterrupt;
}
