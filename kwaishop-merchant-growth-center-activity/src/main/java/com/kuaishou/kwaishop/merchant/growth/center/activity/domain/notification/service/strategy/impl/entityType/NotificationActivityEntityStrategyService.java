package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.entityType;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.DELAY_NOTIFICATION_PUSH_CONSUMER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.DELAY_NOTIFICATION_PUSH_CONSUMER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_REGISTRATION_CONSUMER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationEntityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 活动维度推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Slf4j
@Service
public class NotificationActivityEntityStrategyService extends AbstractNotificationEntityService {

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private RiskControlFetchService riskControlFetchService;

    @Override
    public NotificationEntityTypeEnum getNotificationStrategyEntityType() {
        return NotificationEntityTypeEnum.ACTIVITY;
    }

    @Override
    public boolean checkEntityStatus(long userId, NotificationPushConfigBO configBO) {
        int currentStatus = getCurrentStatus(userId, configBO);
        boolean flag = currentStatus == configBO.getEntityStatus();
        if (!flag) {
            log.info("[活动维度推送] 当前用户活动状态发生改变，取消推送 userId:{},"
                            + " configId:{}, currentStatus:{}, configStatus:{}",
                    userId, configBO.getId(), currentStatus, configBO.getEntityStatus());
        }
        return flag;
    }

    @Override
    public boolean checkEndTime(long userId, NotificationPushConfigBO configBO) {
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(configBO.getActivityId());
        if (activityDO == null) {
            log.error("[活动维度推送] activityDO is null, msg:{}", toJSON(configBO));
            throw new BizException(NOTIFICATION_REGISTRATION_CONSUMER_ERROR, "strategyBO is null");
        }
        // 当前时间大于活动结束时间
        if (System.currentTimeMillis() > activityDO.getEndTime()) {
            log.info("[活动维度推送] 当前活动已结束，推送取消, userId:{}, configBO:{}", userId, toJSON(configBO));
            return true;
        }
        if (configBO.getEntityStatus() == UserActivityEventTypeEnum.REGISTRATION.getValue()) {
            // 当前时间大于活动领取结束时间
            if (System.currentTimeMillis() > activityDO.getDrawEndTime()) {
                log.info("[活动维度推送] 当前活动领取时间已结束，推送取消, userId:{}, configBO:{}", userId, toJSON(configBO));
                return true;
            }
        }
        return false;
    }

    private int getCurrentStatus(long userId, NotificationPushConfigBO configBO) {
        UserActivityRecordDO userActivityRecordDO =
                userActivityRecordDAO.queryUserActivityRecord(userId, configBO.getEntityId(), false);
        // 报名态校验
        if (UserActivityEventTypeEnum.of(configBO.getEntityStatus()) == UserActivityEventTypeEnum.REGISTRATION) {
            return checkRegisterStatus(userId, configBO, userActivityRecordDO);
        }
        if (userActivityRecordDO == null) {
            perfFail(DELAY_NOTIFICATION_PUSH_CONSUMER, "userActivityRecordDO.invalid");
            throw new BizException(DELAY_NOTIFICATION_PUSH_CONSUMER_ERROR, "用户活动记录获取失败-重试");
        }
        return convertActivityStatus(UserActivityStatusEnum.of(userActivityRecordDO.getStatus()));
    }

    private int checkRegisterStatus(long userId, NotificationPushConfigBO configBO,
            UserActivityRecordDO userActivityRecordDO) {
        UserRegistrationRecordBO userRegistrationRecordBOS = userRegistrationService
                .queryUserRegistrationRecords(userId, configBO.getActivityId(), EntityTypeEnum.ACTIVITY,
                        UserRegistrationStatusEnum.VALID).stream().findFirst().orElse(null);
        // 无报名记录
        if (userRegistrationRecordBOS == null) {
            log.warn("[活动报名维度推送] 用户-{}，无报名记录", userId);
            return UserActivityStatusEnum.UNKNOWN.getValue();
        }

        // 报名态无效
        if (!UserRegistrationStatusEnum.VALID.getCode().equals(userRegistrationRecordBOS.getStatus())) {
            log.warn("[活动报名维度推送] 用户-{}，报名记录状态不生效", userId);
            return UserActivityEventTypeEnum.UNKNOWN.getValue();
        }

        // 被风控
        if (riskControlFetchService.inRiskBlackList(userId)) {
            log.info("[活动报名维度推送] 用户-{}，命中风控黑名单", userId);
            return UserActivityEventTypeEnum.UNKNOWN.getValue();
        }

        // 有领取记录
        if (userActivityRecordDO != null) {
            log.warn("[活动报名维度推送] 用户-{}，用户已经领取任务", userId);
            return convertActivityStatus(UserActivityStatusEnum.of(userActivityRecordDO.getStatus()));
        }

        return UserActivityEventTypeEnum.REGISTRATION.getValue();
    }

    private int convertActivityStatus(UserActivityStatusEnum activityStatus) {
        switch (activityStatus) {
            case PROCESSING:
                return UserActivityEventTypeEnum.DRAW.getValue();
            default:
                return UserActivityEventTypeEnum.UNKNOWN.getValue();
        }
    }
}
