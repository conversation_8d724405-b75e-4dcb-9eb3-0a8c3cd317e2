package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.sorter;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.LaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.LaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.LaunchPipeHandler;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
public interface LaunchSorter<CTX extends LaunchInfoFetchContext<R>,
        R extends LaunchInfoResult<?>> extends LaunchPipeHandler<CTX, R> {

    void sort(CTX context);

    @Override
    default void doHandle(CTX context) {
        sort(context);
    }
}
