package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getBizType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.NotificationConfigResolver.getMerchantCenterMessageTemplateConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.needAuditChannelList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.portraitNotificationTemplateConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.notificationRuleConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.api.client.util.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationRuleConfigMapBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.MerchantCenterMessageTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-27
 */
@Slf4j
public abstract class AbstractNotificationCodeStrategyService implements NotificationCodeStrategyService {

    public abstract Set<NotificationCodeEnum> getNotificationCodeSet();

    /**
     * 构建NotificationPushConfig
     */
    public abstract List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList,
            List<AwardConfigDO> awardConfigList, NotificationConfigBO notificationConfig);

    public NotificationPushConfigBO buildNotificationPushConfigTemplate(NotificationAdminDomainBO notificationAdminDomainBO,
           ActivityDO activityDO, NotificationConfigBO notificationConfig, long entityId) {
        int status;
        if (needAuditChannelList.get().contains(notificationConfig.getChannel())) {
            status = NotificationStatusEnum.NEED_AUDIT.getVal();
        } else {
            status = NotificationStatusEnum.VALID.getVal();
        }
        String templateConfig = notificationAdminDomainBO.getTemplateConfig();
        if (StringUtils.isNotBlank(notificationConfig.getTemplateCode())) {
            MerchantCenterMessageTemplateConfigBO templateConfigBO =
                    getMerchantCenterMessageTemplateConfig(notificationAdminDomainBO.getTemplateConfig());
            if (null == templateConfigBO) {
                throw new BizException(BasicErrorCode.SERVER_ERROR, String.format("%s模板参数配置不存在",
                        notificationConfig.getNotificationCode()));
            }
            if (StringUtils.isNotBlank(notificationConfig.getTemplateCode())) {
                templateConfigBO.setCode(notificationConfig.getTemplateCode());
            }
            templateConfig = ObjectMapperUtils.toJSON(templateConfigBO);
        }

        return NotificationPushConfigBO.builder()
                .activityId(activityDO.getId())
                .entityId(entityId)
                .entityType(notificationAdminDomainBO.getEntityType())
                .entityStatus(notificationAdminDomainBO.getEntityStatus())
                .channel(notificationConfig.getChannel())
                .templateConfig(templateConfig)
                .occasion(notificationAdminDomainBO.getOccasion())
                .creator(SymbolConstants.DEFAULT_USER_SYMBOL)
                .modifier(SymbolConstants.DEFAULT_USER_SYMBOL)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .status(status)
                .scene(notificationAdminDomainBO.getCode())
                .periodConfig(ObjectMapperUtils.fromJSON(notificationAdminDomainBO.getPeriodConfig(), PeriodConfigBO.class))
                .scene(notificationConfig.getNotificationCode())
                .build();
    }
    /**
     * 从KConf上拉取触达配置
     */
    public NotificationAdminDomainBO getNotificationAdminDomainBO(ActivityDO activity, NotificationConfigBO notificationConfig) {
        if (Objects.isNull(activity) || Objects.isNull(notificationConfig)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动信息或触达配置不存在");
        }
        List<NotificationAdminDomainBO> notificationTemplateConfigList = Lists.newArrayList();
        String bizType = getBizType(activity);
        ActivityBizTypeEnum bizTypeEnum = ActivityBizTypeEnum.getByCode(bizType);
        NotificationRuleConfigMapBO ruleConfigMapBO =
                (NotificationRuleConfigMapBO) notificationRuleConfig.get();
        switch (bizTypeEnum) {
            case DAREN:
                if (Objects.isNull(ruleConfigMapBO) || CollectionUtils.isEmpty(ruleConfigMapBO.getDarenConfigs())) {
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "kconf模版配置为空，请联系管理员");
                }
                List<NotificationRuleConfigBO> darenConfigs = ruleConfigMapBO.getDarenConfigs();
                Map<Integer, NotificationRuleConfigBO> darenRuleConfigMap =
                        darenConfigs.stream().collect(Collectors.toMap(NotificationRuleConfigBO::getChannel,
                                Function.identity()));
                NotificationRuleConfigBO darenNotificationRuleConfigBO = darenRuleConfigMap.get(notificationConfig.getChannel());
                if (Objects.isNull(darenNotificationRuleConfigBO) || CollectionUtils.isEmpty(
                        darenNotificationRuleConfigBO.getNotificationList())) {
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "kconf模版配置为空，请联系管理员");
                }
                notificationTemplateConfigList = darenNotificationRuleConfigBO.getNotificationList();
                break;
            case SELLER:
                if (Objects.isNull(ruleConfigMapBO) || CollectionUtils.isEmpty(ruleConfigMapBO.getSellerConfigs())) {
                    notificationTemplateConfigList = portraitNotificationTemplateConfig.getList();
                } else {
                    List<NotificationRuleConfigBO> sellerConfigs = ruleConfigMapBO.getSellerConfigs();
                    Map<Integer, NotificationRuleConfigBO> sellerRuleConfigMap =
                            sellerConfigs.stream().collect(Collectors.toMap(NotificationRuleConfigBO::getChannel,
                                    Function.identity()));
                    NotificationRuleConfigBO sellerNotificationRuleConfigBO = sellerRuleConfigMap.get(notificationConfig.getChannel());
                    if (Objects.isNull(sellerNotificationRuleConfigBO) || CollectionUtils.isEmpty(
                            sellerNotificationRuleConfigBO.getNotificationList())) {
                        notificationTemplateConfigList = portraitNotificationTemplateConfig.getList();
                    } else {
                        notificationTemplateConfigList = sellerNotificationRuleConfigBO.getNotificationList();
                    }
                }

                break;
            default:
                break;
        }
        if (CollectionUtils.isEmpty(notificationTemplateConfigList)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "kconf模版配置为空，请联系管理员");
        }

        // 通过模版code获取模版配置
        List<NotificationAdminDomainBO> notificationAdminDomainList = notificationTemplateConfigList.stream()
                .filter(notification -> notification.getCode().equals(notificationConfig.getNotificationCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notificationAdminDomainList)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, String.format("%s模版不存在，请联系管理员",
                    notificationConfig.getNotificationCode()));
        }

        return notificationAdminDomainList.get(0);
    }
}
