package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.RangeCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.RangeQueryTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildHandler;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@Slf4j
@Lazy
@Service
public class RangeQueryBuild implements QueryBuildHandler {
    @Override
    public QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam) {
        // match查询
        if (StringUtils.isBlank(condition.getFieldName())) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "range查询条件字段不能为空");
        }
        ConditionValueTypeEnum valueTypeEnum = ConditionValueTypeEnum.of(condition.getConditionValueType());
        if (valueTypeEnum.equals(ConditionValueTypeEnum.UNKNOWN)) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别range查询条件值类型");
        }
        Pair<RangeCondition, RangeCondition> rangeConditionPair = condition.getRangeConditionPair();
        if (rangeConditionPair == null) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "range查询条件值不能为空");
        }
        RangeCondition left = rangeConditionPair.getLeft();
        RangeCondition right = rangeConditionPair.getRight();
        if (left == null && right == null) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "range查询条件值不能为空");
        }
        RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder(condition.getFieldName());
        if (left != null) {
            RangeQueryTypeEnum rangeQueryType = left.getRangeQueryType();
            if (rangeQueryType == null) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别range查询条件类型");
            }
            String rangeValue = left.getRangeValue();
            switch (rangeQueryType) {
                case GT:
                    if (valueTypeEnum.equals(ConditionValueTypeEnum.NUMBER)) {
                        rangeQueryBuilder.gt(Long.parseLong(rangeValue));
                    } else {
                        rangeQueryBuilder.gt(rangeValue);
                    }
                    break;
                case GTE:
                    if (valueTypeEnum.equals(ConditionValueTypeEnum.NUMBER)) {
                        rangeQueryBuilder.gte(Long.parseLong(rangeValue));
                    } else {
                        rangeQueryBuilder.gte(rangeValue);
                    }
                    break;
                default:
                    throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别range查询条件类型");
            }
        }
        if (right != null) {
            RangeQueryTypeEnum rangeQueryType = right.getRangeQueryType();
            if (rangeQueryType == null) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别range查询条件类型");
            }
            String rangeValue = right.getRangeValue();
            switch (rangeQueryType) {
                case LT:
                    if (valueTypeEnum.equals(ConditionValueTypeEnum.NUMBER)) {
                        rangeQueryBuilder.lt(Long.parseLong(rangeValue));
                    } else {
                        rangeQueryBuilder.lt(rangeValue);
                    }
                    break;
                case LTE:
                    if (valueTypeEnum.equals(ConditionValueTypeEnum.NUMBER)) {
                        rangeQueryBuilder.lte(Long.parseLong(rangeValue));
                    } else {
                        rangeQueryBuilder.lte(rangeValue);
                    }
                    break;
                default:
                    throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别range查询条件类型");
            }
        }
        return rangeQueryBuilder;
    }

    @Override
    public ConditionTypeEnum conditionType() {
        return ConditionTypeEnum.RANGE;
    }
}
