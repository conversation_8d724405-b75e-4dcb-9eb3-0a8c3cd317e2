package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.github.rholder.retry.WaitStrategies.fixedWait;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.converter.CrowdBOConverter.generateDmpCrowdConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_DISTRIBUTOR_TYPE_CHECK_CONSUMER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.defaultRetryTimes;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.distributorTypeValidPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.userInCrowdPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.defaultRetryTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.goodMatchDistributorCrowdId;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.IndustryActivityBuildService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.DistributorTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.client.CrowdFetchClient;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.DistributorTypeValidExecuteConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.DistributorTypeValidBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-22
 */
@Service
@Slf4j
@Lazy
public class DistributorTypeCheckExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private IndustryActivityBuildService industryActivityBuildService;

    @Autowired
    private CrowdFetchClient crowdFetchClient;

    // redis重试次数
    private static final Retryer<Map<Long, Boolean>> RETRY =
            RetryerBuilder.<Map<Long, Boolean>> newBuilder()
            .retryIfExceptionOfType(Exception.class)
            .withStopStrategy(StopStrategies.stopAfterAttempt(defaultRetryTimes.get()))
            .withWaitStrategy(fixedWait(defaultRetryTime.get(), TimeUnit.MILLISECONDS))
            .build();

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        // 事件强转
        DistributorTypeValidBatchExecuteEvent executeEvent = (DistributorTypeValidBatchExecuteEvent) event;
        String eventId = executeEvent.getEventId();
        // 人群配置信息
        CrowdConfigBO crowdConfig = executeEvent.getCrowdConfig();
        // 解析活动人群
        Integer crowdType = crowdConfig.getCrowdType();
        return industryActivityBuildService.getSellerByCrowdType(crowdType,
                toJSON(crowdConfig));
    }

    @Override
    protected String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 事件强转
        DistributorTypeValidBatchExecuteEvent executeEvent = (DistributorTypeValidBatchExecuteEvent) event;
        // 数据组装
        DistributorTypeValidExecuteConfigBO executeConfig = new DistributorTypeValidExecuteConfigBO();
        CrowdConfigBO crowdConfig = executeEvent.getCrowdConfig();
        executeConfig.setCrowdConfig(crowdConfig);
        return toJSON(executeConfig);
    }

    @Override
    public ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        DistributorTypeValidExecuteConfigBO executeConfigBO = fromJSON(executeConfig, DistributorTypeValidExecuteConfigBO.class);
        ExecuteHandleResult result = new ExecuteHandleResult();
        List<Long> successUserIdList = Lists.newArrayList();
        List<Long> riskUserIdList = Lists.newArrayList();
        List<List<Long>> userIds = Lists.partition(userIdList, userInCrowdPartitionSize.get());

        // 取出货主类型
        CrowdConfigBO crowdConfig = executeConfigBO.getCrowdConfig();
        Integer distributorType = crowdConfig.getDistributorType();

        // 对于每个用户
        for (List<Long> user : userIds) {
            Map<Long, Boolean> userMap = Maps.newHashMap();
            try {
                userMap = RETRY.call(() -> crowdFetchClient.checkUsersInCrowd(user,
                        generateDmpCrowdConfig(goodMatchDistributorCrowdId.get())));
            } catch (Exception e) {
                log.warn("[货主类型判断重试失败] Exception userIds:{}, distributorType:{}", userIds,
                        distributorType, e);
                perfException(MQ_DISTRIBUTOR_TYPE_CHECK_CONSUMER, e);
            }
            for (Long userId : user) {
                log.info("[货主类型判断] userId:{}, userMap:{}", userId, userMap);
                Boolean inCrowd = userMap.getOrDefault(userId, false);
                if (distributorTypeMatch(userId, inCrowd, distributorType)) {
                    successUserIdList.add(userId);
                } else {
                    riskUserIdList.add(userId);
                }
            }
        }
        // 处理结果构建
        result.setSuccessUserList(successUserIdList);
        result.setFailUserList(riskUserIdList);
        return result;
    }

    /**
     * 单用户货主类型校验
     */
    private boolean distributorTypeMatch(Long userId, Boolean inCrowd, Integer distributorType) {
        // 取出货主类型
        DistributorTypeEnum distributorTypeEnum = DistributorTypeEnum.getByCode(distributorType);
        if (inCrowd == null) {
            return false;
        }
        boolean distributorMatch = false;
        switch (distributorTypeEnum) {
            case SELF_BROADCAST:
                distributorMatch = inCrowd;
                break;
            case UN_SELF_BROADCAST:
                distributorMatch = !inCrowd;
                break;
            case UNKNOWN:
            default:
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "非预期货主类型");
        }
        return distributorMatch;
    }

    @Override
    protected int getPartitionSize() {
        return distributorTypeValidPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.DISTRIBUTOR_TYPE_VALID;
    }
}
