package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFieldContentAdminProtocol;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.protocol.LaunchFormFieldAdminProtocol;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-04-02
 */
public class LaunchFormFieldConverter {

    public static LaunchFormFieldAdminProtocol converter(String fieldCode, Integer contentType,
            String content) {
        return LaunchFormFieldAdminProtocol.builder()
                .fieldCode(fieldCode)
                .selectedContentType(contentType)
                .content(LaunchFieldContentAdminProtocol.builder()
                        .fixContent(content)
                        .build())
                .build();
    }
}
