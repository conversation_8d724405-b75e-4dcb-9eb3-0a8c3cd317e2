package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.blobstore.common.utils.StringUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.converter.GenDataRecordConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.enums.GenDataStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenDataDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.gendata.GenDataRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.gendata.GenDataRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-22
 */
@Service
@Slf4j
public class LLMGenDataDomainServiceImpl implements LLMGenDataDomainService {

    @Resource
    private GenDataRecordDAO genDataRecordDAO;

    @Override
    public void createGenDataRecord(GenDataRecordBO genDataRecord) {
        checkArgument(genDataRecord != null, "生成记录不能为空");
        checkArgument(StringUtils.isNotBlank(genDataRecord.getUniqueKey()), "生成记录唯一键不能为空");
        String uniqueKey = genDataRecord.getUniqueKey();

        GenDataRecordBO originRecord = queryByUniqueKey(uniqueKey);
        if (originRecord != null) {
            log.error("[llm物料异步生成] 生成记录已存在 genDataRecord:{}", toJSON(genDataRecord));
            throw new BizException(SERVER_ERROR, "生成记录已存在");
        }

        GenDataRecordDO record = GenDataRecordConverter.convertToDO(genDataRecord);
        long insert = genDataRecordDAO.insert(record);
        if (insert <= 0) {
            log.error("[创建生成记录保存] 保存失败 genDataRecord:{}", toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "生成记录创建失败");
        }
    }

    @Override
    public void successGenDataRecord(String uniqueKey, Map<String, Object> contentMap) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "生成记录唯一键不能为空");
        GenDataRecordBO genDataRecord = queryByUniqueKey(uniqueKey);

        if (genDataRecord == null) {
            log.error("[生成数据记录设置成功] 记录不存在 uniqueKey:{}, contentMap:{}", uniqueKey, toJSON(contentMap));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录不存在");
        }

        if (!Objects.equals(genDataRecord.getStatus(), GenDataStatusEnum.GENERATING.getStatus())) {
            log.error("[生成数据记录设置成功] 记录状态异常 uniqueKey:{}, contentMap:{}, genDataRecord:{}",
                    uniqueKey, toJSON(contentMap), toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录状态异常");
        }

        GenDataRecordContentBO content = genDataRecord.getContent();
        if (content == null) {
            log.error("[生成数据记录设置成功] 记录内容异常 uniqueKey:{}, contentMap:{}, genDataRecord:{}",
                    uniqueKey, toJSON(contentMap), toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录内容异常");
        }

        content.setContent(toJSON(contentMap));
        genDataRecord.setStatus(GenDataStatusEnum.GENERATE_SUCCESS.getStatus());

        // 更新记录
        updateGenDataRecordByUniqueKey(genDataRecord);
    }

    @Override
    public void failGenDataRecord(String uniqueKey, String errorMsg) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "生成记录唯一键不能为空");
        GenDataRecordBO genDataRecord = queryByUniqueKey(uniqueKey);

        if (genDataRecord == null) {
            log.error("[生成数据记录设置失败] 记录不存在 uniqueKey:{}, errorMsg:{}", uniqueKey, errorMsg);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录不存在");
        }

        if (!Objects.equals(genDataRecord.getStatus(), GenDataStatusEnum.GENERATING.getStatus())) {
            log.error("[生成数据记录设置失败] 记录状态异常 uniqueKey:{}, errorMsg:{}, genDataRecord:{}",
                    uniqueKey, errorMsg, toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录状态异常");
        }

        GenDataRecordContentBO content = genDataRecord.getContent();
        if (content == null) {
            log.error("[生成数据记录设置失败] 记录内容异常 uniqueKey:{}, errorMsg:{}, genDataRecord:{}",
                    uniqueKey, errorMsg, toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "记录内容异常");
        }

        content.setErrorMsg(errorMsg);
        genDataRecord.setStatus(GenDataStatusEnum.GENERATE_FAILED.getStatus());

        // 更新记录
        updateGenDataRecordByUniqueKey(genDataRecord);
    }

    @Override
    public void updateGenDataRecordByUniqueKey(GenDataRecordBO genDataRecord) {
        checkArgument(genDataRecord != null, "生成记录不能为空");
        checkArgument(StringUtils.isNotBlank(genDataRecord.getUniqueKey()), "生成记录唯一键不能为空");

        GenDataRecordDO record = GenDataRecordConverter.convertToDO(genDataRecord);
        int update = genDataRecordDAO.updateSelectiveByUniqueKey(record);
        if (update <= 0) {
            log.error("[更新生成记录保存] 更新失败 genDataRecord:{}", toJSON(genDataRecord));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "生成记录更新失败");
        }
    }

    @Override
    public GenDataRecordBO queryByUniqueKey(String uniqueKey) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "生成记录唯一键不能为空");
        GenDataRecordDO record = genDataRecordDAO.queryByUniqueKey(uniqueKey);
        if (record != null) {
            return GenDataRecordConverter.convertFromDO(record);
        }

        return null;
    }
}
