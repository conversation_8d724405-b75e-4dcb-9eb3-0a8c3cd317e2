package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum.KUAISHOU_PRIVATE_MESSAGE;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-25
 */
@Service
@Lazy
@Slf4j
public class MerchantNotificationImChannelStrategyService implements NotificationChannelStrategyService {

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;

    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return KUAISHOU_PRIVATE_MESSAGE;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        notificationPushFetchService.imPrivateMessagePush(userId, configBO, templateParams);
    }
}
