package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.BaseExportParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-06
 */
public interface FileExportService {

    /**
     * 获取文件导出的业务类型
     */
    ExportFileBizTypeEnum getBizType();

    /**
     * 制作excel
     */
    List<String> doXls(BaseExportParam exportParam);

    /**
     * 此处以字符串传参，内部需要通过
     */
    String export(BaseExportParam exportParam, List<String> excels);
}
