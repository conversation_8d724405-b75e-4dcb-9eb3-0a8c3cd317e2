package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.common.base.Joiner;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-02
 */
public enum NotificationRedisKeyEnum {
    CREATE_NOTIFICATION_CONFIG("create:notification:config:"), // 创建推送配置
    SEND_DELAY_NOTIFICATION_PUSH("send:delay:notification:push:"), // 发送延迟推送消息
    RISK_WARM_NOTIFICATION_PUSH("risk:warm:notification:push:"), // 风控警告活动纬度推送锁
    TASK_PROGRESS_NOTIFICATION_PUSH("task:progress:notification:push:"), // 任务进度通知幂等
    STEP_PROGRESS_NOTIFICATION_PUSH("step:progress:notification:push:"), // 阶梯进度通知幂等
    USER_NOTIFICATION_INFO_CACHE("user:notification:info:cache:"), // 用户触达信息查询缓存
    ;

    private final String prefix;
    private static final String SEPARATOR = "_";
    private static final String COLON = ":";

    static {
        Map<String, NotificationRedisKeyEnum> map = new HashMap<>();
        for (NotificationRedisKeyEnum value : values()) {
            if (map.containsKey(value.getPrefix())) {
                throw new IllegalArgumentException(
                        "！！！！严重错误！！！！RedisKey prefix 冲突:" + value.getPrefix());
            }
            map.put(value.getPrefix(), value);
        }
    }

    NotificationRedisKeyEnum(String prefix) {
        this.prefix = prefix;
    }


    public String getFullKey(long value) {
        return this.prefix + value;
    }

    public String getFullKey(String value) {
        return this.prefix + value;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getFullKeyJoinWithUnderline(CharSequence... keySuffix) {
        return this.prefix + String.join(SEPARATOR, keySuffix);
    }

    public String getFullKeyJoinWithUnderline(Long... keySuffix) {
        return this.prefix + Arrays.stream(keySuffix).map(String::valueOf).collect(Collectors.joining(SEPARATOR));
    }

    public String getFullKeyJoinWithColon(CharSequence... keySuffix) {
        return this.prefix + String.join(COLON, keySuffix);
    }
    public String getFullKeyJoinWithColon(Long... keySuffix) {
        return this.prefix + Arrays.stream(keySuffix).map(String::valueOf).collect(Collectors.joining(COLON));
    }
    public String joinKey(Object...args) {
        return this.prefix + Joiner.on("_").join(args);
    }
}
