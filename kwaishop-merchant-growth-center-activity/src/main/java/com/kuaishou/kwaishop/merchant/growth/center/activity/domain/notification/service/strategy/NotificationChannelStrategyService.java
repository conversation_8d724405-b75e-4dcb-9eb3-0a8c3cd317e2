package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
public interface NotificationChannelStrategyService {
    NotificationChannelEnum getNotificationChannel();

    /**
     * 执行推送
     */
    void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams);

    /**
     * 是否执行推送
     */
    default boolean pushSwitch() {
        return true;
    }

    /**
     * 前置过滤
     */
    default boolean filter(long userId, NotificationPushConfigBO configBO) {
        return true;
    }
}
