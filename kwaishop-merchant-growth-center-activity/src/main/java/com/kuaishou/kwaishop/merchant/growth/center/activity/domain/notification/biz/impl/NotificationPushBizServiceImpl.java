package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_PERMISSION_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_NOTIFICATION_INFO_QUERY_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.notificationConfigDeleteSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.notificationAdminPermission;
import static java.util.Collections.emptyList;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz.NotificationPushBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationOccasionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushCreateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo.UserNotificationInfoProvider;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ActivityRedisUtils;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.BatchCreateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.BatchUpdateNotificationTemplateAndPeriodConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.CreateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.DelUserActivityPushLockRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigDetailByConfigIdDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigDetailByConfigIdRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigsByConditionsDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigsByConditionsRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.HardDeleteNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.InvalidNotificationByConditionRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.InvalidNotificationByConfigIdRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.NotificationConfigDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.QueryUserNotificationInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.TriggerNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UpdateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UserNotificationShowConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.ValidNotificationByConditionRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.ValidNotificationByConfigIdRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-11
 */
@Lazy
@Slf4j
@Service
public class NotificationPushBizServiceImpl implements NotificationPushBizService {

    private static final String SYSTEM_MODIFIER = "system";

    @Autowired
    private NotificationPushBasicService notificationPushBasicService;

    @Autowired
    private NotificationPushCreateService notificationPushCreateService;

    @Autowired
    private UserNotificationInfoProvider userNotificationInfoProvider;

    @Override
    public boolean createNotificationPushConfig(CreateNotificationConfigRequest request) {
        if (StringUtils.isBlank(request.getCreator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "creator invalid");
        }
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId invalid");
        }
        if (request.getEntityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityId invalid");
        }
        if (!NotificationEntityTypeEnum.getAllNotificationValidEntityTypes().contains(request.getEntityType())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType invalid");
        }
        if (!NotificationChannelEnum.getAllValidNotificationChannelValues().contains(request.getChannel())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "channel invalid");
        }
        if (!NotificationOccasionEnum.getAllValidNotificationPushOccasionValues().contains(request.getOccasion())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "occasion invalid");
        }
        if (StringUtils.isBlank(request.getTemplateConfig())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "templateConfig invalid");
        }
        if (NotificationOccasionEnum.IMMEDIATE.getVal() != request.getOccasion() && StringUtils.isBlank(request.getPeriodConfig())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "periodConfig invalid");
        }
        return notificationPushBasicService.createOrUpdateNotificationPushConfig(transferNotificationBO(request));
    }

    @Override
    public boolean batchCreateNotificationPushConfig(BatchCreateNotificationConfigRequest request) {
        if (StringUtils.isBlank(request.getCreator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "creator invalid");
        }
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId invalid");
        }
        if (CollectionUtils.isEmpty(request.getEntityIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityIdList invalid");
        }
        if (!NotificationEntityTypeEnum.getAllNotificationValidEntityTypes().contains(request.getEntityType())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType invalid");
        }
        if (!NotificationChannelEnum.getAllValidNotificationChannelValues().contains(request.getChannel())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "channel invalid");
        }
        if (!NotificationOccasionEnum.getAllValidNotificationPushOccasionValues().contains(request.getOccasion())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "occasion invalid");
        }
        if (StringUtils.isBlank(request.getTemplateConfig())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "templateConfig invalid");
        }
        if (NotificationOccasionEnum.IMMEDIATE.getVal() != request.getOccasion() && StringUtils.isBlank(request.getPeriodConfig())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "periodConfig invalid");
        }
        List<NotificationPushConfigBO> notificationPushConfigBOList = transferNotificationBO(request);
        boolean flag = true;
        if (CollectionUtils.isNotEmpty(notificationPushConfigBOList)) {
            for (NotificationPushConfigBO configBO : notificationPushConfigBOList) {
                boolean res =
                        notificationPushBasicService.createOrUpdateNotificationPushConfig(configBO);
                if (!res) {
                    flag = false;
                }
            }
        }
        return flag;
    }

    @Override
    public void triggerNotificationConfig(TriggerNotificationConfigRequest request) {
        checkTriggerNotificationConfigRequest(request);
        List<Long> configIdList = request.getConfigIdList();
        long userId = request.getUserId();
        long eventTime = request.getEventTime() > 0 ? request.getEventTime() : System.currentTimeMillis();
        for (Long configId : configIdList) {
            notificationPushCreateService.triggerNotificationConfig(userId, configId, eventTime);
        }
    }

    @Override
    public boolean updateNotificationConfig(UpdateNotificationConfigRequest request) {
        if (request.getConfigId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configId invalid");
        }
        if (StringUtils.isBlank(request.getModifier())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "modifier invalid");
        }
        NotificationPushConfigBO currentNotificationConfig =
                notificationPushBasicService.getNotificationPushConfigById(request.getConfigId(), true);
        if (currentNotificationConfig == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configId not exists");
        }
        String modifier = request.getModifier();
        if (StringUtils.isBlank(modifier)) {
            modifier = SYSTEM_MODIFIER;
        }
        NotificationPushConfigBO.NotificationPushConfigBOBuilder builder = NotificationPushConfigBO.builder();
        builder.id(request.getConfigId());
        builder.modifier(modifier);
        builder.version(currentNotificationConfig.getVersion());
        builder.activityId(request.getActivityId());
        builder.templateConfig(request.getTemplateConfig());
        builder.entityId(request.getEntityId());
        builder.entityStatus(request.getEntityStatus());
        if (StringUtils.isNotBlank(request.getPeriodConfig())) {
            builder.periodConfig(ObjectMapperUtils.fromJSON(request.getPeriodConfig(), PeriodConfigBO.class));
        }
        if (request.getStatus() > 0 && NotificationStatusEnum.getAllValidNotificationStatus()
                .contains(request.getStatus())) {
            builder.status(request.getStatus());
        }
        if (request.getEntityType() > 0 && NotificationEntityTypeEnum.getAllNotificationValidEntityTypes()
                .contains(request.getEntityType())) {
            builder.entityType(request.getEntityType());
        }
        if (request.getOccasion() > 0 && NotificationOccasionEnum.getAllValidNotificationPushOccasionValues()
                .contains(request.getOccasion())) {
            builder.occasion(request.getOccasion());
        }
        if (request.getChannel() > 0 && NotificationChannelEnum.getAllValidNotificationChannelValues()
                .contains(request.getChannel())) {
            builder.channel(request.getChannel());
        }
        NotificationPushConfigBO configBO = builder.build();
        return notificationPushBasicService.updateNotificationPushConfig(configBO);
    }

    @Override
    public boolean invalidNotificationConfigById(InvalidNotificationByConfigIdRequest request) {
        String modifier = request.getModifier();
        if (StringUtils.isBlank(modifier)) {
            modifier = SYSTEM_MODIFIER;
        }
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configIds invalid");
        }
        return notificationPushBasicService
                .batchUpdateNotificationPushConfigStatus(request.getConfigIdList(), NotificationStatusEnum.INVALID,
                        modifier);
    }

    @Override
    public boolean validNotificationConfigById(ValidNotificationByConfigIdRequest request) {
        String modifier = request.getModifier();
        if (StringUtils.isBlank(modifier)) {
            modifier = SYSTEM_MODIFIER;
        }
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configIds invalid");
        }
        return notificationPushBasicService
                .batchUpdateNotificationPushConfigStatus(request.getConfigIdList(), NotificationStatusEnum.VALID, modifier);
    }

    @Override
    public boolean invalidNotificationConfigByCondition(InvalidNotificationByConditionRequest request) {
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId.invalid");
        }
        if (request.getEntityType() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType.invalid");
        }
        if (StringUtils.isBlank(request.getModifier())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "modifier.invalid");
        }
        return notificationPushBasicService.updateNotificationConfigStatusByConditions(request.getActivityId(),
                NotificationEntityTypeEnum.of(request.getEntityType()), request.getEntityStatus(),
                NotificationStatusEnum.INVALID, request.getModifier());
    }

    @Override
    public boolean validNotificationConfigByCondition(ValidNotificationByConditionRequest request) {
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId.invalid");
        }
        if (request.getEntityType() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType.invalid");
        }
        if (StringUtils.isBlank(request.getModifier())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "modifier.invalid");
        }
        return notificationPushBasicService.updateNotificationConfigStatusByConditions(request.getActivityId(),
                NotificationEntityTypeEnum.of(request.getEntityType()), request.getEntityStatus(),
                NotificationStatusEnum.VALID, request.getModifier());
    }

    @Override
    public GetNotificationConfigDetailByConfigIdDTO getNotificationConfigDetailById(
            GetNotificationConfigDetailByConfigIdRequest request) {
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configIds invalid");
        }
        List<NotificationPushConfigBO> notificationPushConfigList =
                notificationPushBasicService.getNotificationPushConfigsByIds(request.getConfigIdList());
        return assembleGetNotificationConfigDetailByConfigIdDTO(notificationPushConfigList);
    }

    @Override
    public GetNotificationConfigsByConditionsDTO getNotificationConfigsByConditions(GetNotificationConfigsByConditionsRequest request) {
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId invalid");
        }
        if (request.getEntityType() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType invalid");
        }
        NotificationEntityTypeEnum entityType = NotificationEntityTypeEnum.of(request.getEntityType());
        List<NotificationPushConfigBO> notificationPushConfigList = notificationPushBasicService
                .getNotificationPushConfigByConditions(request.getActivityId(), entityType, request.getEntityStatus(),
                        request.getEntityIdList());
        return assembleGetNotificationConfigsByConditionsDTO(notificationPushConfigList);
    }

    @Override
    public boolean hardDeleteNotificationConfigs(HardDeleteNotificationConfigRequest request) {
        // 操作权限&删除功能开关校验
        if (!notificationAdminPermission.get().contains(request.getModifier())
                || !notificationConfigDeleteSwitch.get()) {
            throw new BizException(NOTIFICATION_PERMISSION_ERROR.getCode(), "无操作权限");
        }
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "activityId invalid");
        }
        if (request.getEntityType() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityType invalid");
        }
        if (NotificationEntityTypeEnum.of(request.getEntityType()) != NotificationEntityTypeEnum.ACTIVITY
                && CollectionUtils.isEmpty(request.getEntityIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "entityIdList invalid");
        }
        if (request.getChannel() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "channel invalid");
        }
        return notificationPushBasicService.hardDeleteNotificationPushConfigs(request.getActivityId(),
                NotificationEntityTypeEnum.of(request.getEntityType()), request.getEntityIdList(), request.getChannel());
    }

    @Override
    public boolean updateNotificationTemplateAndPeriodConfig(
            BatchUpdateNotificationTemplateAndPeriodConfigRequest request) {
        if (StringUtils.isBlank(request.getModifier())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "modifier.invalid");
        }
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configIdList.invalid");
        }
        return notificationPushBasicService
                .batchUpdateNotificationTemplateAndPeriodConfig(request.getConfigIdList(), request.getTemplateConfig(),
                        request.getPeriodConfig(), request.getModifier());
    }

    @Override
    public void delUserActivityNotificationPushLock(DelUserActivityPushLockRequest request) {
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "activityId.invalid");
        }
        if (request.getUserId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "userId.invalid");
        }
        if (request.getEntityType() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "entityType.invalid");
        }

        NotificationEntityTypeEnum entityType = NotificationEntityTypeEnum.of(request.getEntityType());
        if (!NotificationEntityTypeEnum.getAllNotificationValidEntityTypes().contains(entityType.getVal())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "entityType.invalid");
        }
        // 获取需要解锁的配置id
        List<Long> configIds = getConfigIds(request.getActivityId(), entityType);
        if (CollectionUtils.isEmpty(configIds)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "没有对应推送配置");
        }
        log.info("[待解锁推送配置] userId:{}, configIds:{}", request.getUserId(), configIds);

        for (long configId : configIds) {
            // 解锁
            String redisKey = NotificationRedisKeyEnum.SEND_DELAY_NOTIFICATION_PUSH
                    .getFullKeyJoinWithColon(request.getUserId(), configId);
            ActivityRedisUtils.delRedisKey(redisKey);
        }
    }

    private List<Long> getConfigIds(long activityId, NotificationEntityTypeEnum entityType) {
        return notificationPushBasicService
                .getNotificationPushConfigByConditions(activityId, entityType, 0, emptyList())
                .stream()
                .map(NotificationPushConfigBO::getId)
                .collect(Collectors.toList());
    }

    private GetNotificationConfigDetailByConfigIdDTO assembleGetNotificationConfigDetailByConfigIdDTO(
            List<NotificationPushConfigBO> notificationPushConfigList) {
        GetNotificationConfigDetailByConfigIdDTO.Builder builder = GetNotificationConfigDetailByConfigIdDTO.newBuilder();
        if (CollectionUtils.isEmpty(notificationPushConfigList)) {
            return builder.build();
        }
        List<NotificationConfigDTO> notificationConfigDTOList = new ArrayList<>();
        for (NotificationPushConfigBO configBO : notificationPushConfigList) {
            NotificationConfigDTO dto = NotificationConfigDTO.newBuilder()
                    .setOccasion(configBO.getOccasion())
                    .setActivityId(configBO.getActivityId())
                    .setExt(configBO.getExt())
                    .setTemplateConfig(configBO.getTemplateConfig())
                    .setChannel(configBO.getChannel())
                    .setEntityType(configBO.getEntityType())
                    .setEntityId(configBO.getEntityId())
                    .setPeriodConfig(toJSON(configBO.getPeriodConfig()))
                    .setEntityStatus(configBO.getEntityStatus())
                    .setCreator(configBO.getCreator())
                    .setModifier(configBO.getModifier())
                    .setCreateTime(configBO.getCreateTime())
                    .setConfigId(configBO.getId())
                    .setUpdateTime(configBO.getUpdateTime())
                    .build();
            notificationConfigDTOList.add(dto);
        }
        builder.addAllNotificationConfig(notificationConfigDTOList);
        return builder.build();
    }

    private GetNotificationConfigsByConditionsDTO assembleGetNotificationConfigsByConditionsDTO(
            List<NotificationPushConfigBO> notificationPushConfigList) {
        GetNotificationConfigsByConditionsDTO.Builder builder = GetNotificationConfigsByConditionsDTO.newBuilder();
        if (CollectionUtils.isEmpty(notificationPushConfigList)) {
            return builder.build();
        }
        List<Long> configIds = notificationPushConfigList.stream()
                .map(NotificationPushConfigBO::getId)
                .collect(Collectors.toList());
        builder.addAllConfigId(configIds);
        builder.setSize(configIds.size());
        return builder.build();
    }

    private void checkTriggerNotificationConfigRequest(TriggerNotificationConfigRequest request) {
        if (request.getUserId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "userId invalid");
        }
        if (CollectionUtils.isEmpty(request.getConfigIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "configIds invalid");
        }
    }

    private NotificationPushConfigBO transferNotificationBO(CreateNotificationConfigRequest request) {
        PeriodConfigBO periodConfig = null;
        if (StringUtils.isNotBlank(request.getPeriodConfig())) {
            try {
                periodConfig = ObjectMapperUtils.fromJSON(request.getPeriodConfig(), PeriodConfigBO.class);
            } catch (Exception e) {
                throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "periodConfig parse error");
            }
        }
        long currentTime = System.currentTimeMillis();
        return NotificationPushConfigBO.builder()
                .activityId(request.getActivityId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .entityStatus(request.getEntityStatus())
                .channel(request.getChannel())
                .occasion(request.getOccasion())
                .status(NotificationStatusEnum.VALID.getVal())
                .templateConfig(request.getTemplateConfig())
                .periodConfig(periodConfig)
                .ext(request.getExt())
                .createTime(currentTime)
                .updateTime(currentTime)
                .creator(request.getCreator())
                .modifier(request.getCreator())
                .version(0L)
                .deleted(0)
                .build();
    }

    private List<NotificationPushConfigBO> transferNotificationBO(BatchCreateNotificationConfigRequest request) {
        List<NotificationPushConfigBO> notificationPushConfigBOList = Lists.newArrayList();
        PeriodConfigBO periodConfig = null;
        if (StringUtils.isNotBlank(request.getPeriodConfig())) {
            try {
                periodConfig = ObjectMapperUtils.fromJSON(request.getPeriodConfig(), PeriodConfigBO.class);
            } catch (Exception e) {
                throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "periodConfig parse error");
            }
        }
        long currentTime = System.currentTimeMillis();
        List<Long> entityIdList = request.getEntityIdList();
        for (Long entityId : entityIdList) {
            if (entityId <= 0) {
                log.error("[批量创建触达配置] entityId不合法");
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            NotificationPushConfigBO notificationPushConfigBO = NotificationPushConfigBO.builder()
                    .activityId(request.getActivityId())
                    .entityId(entityId)
                    .entityType(request.getEntityType())
                    .entityStatus(request.getEntityStatus())
                    .channel(request.getChannel())
                    .occasion(request.getOccasion())
                    .status(NotificationStatusEnum.VALID.getVal())
                    .templateConfig(request.getTemplateConfig())
                    .periodConfig(periodConfig)
                    .ext(request.getExt())
                    .createTime(currentTime)
                    .updateTime(currentTime)
                    .creator(request.getCreator())
                    .modifier(request.getCreator())
                    .version(0L)
                    .deleted(0)
                    .build();
            notificationPushConfigBOList.add(notificationPushConfigBO);
        }
        return notificationPushConfigBOList;
    }

    @Override
    public UserNotificationShowConfig queryUserNotificationInfo(QueryUserNotificationInfoRequest request) {
        long userId = request.getUserId();
        String params = request.getParams();
        String source = request.getSource();
        if (userId < 0 || StringUtils.isEmpty(params) || StringUtils.isEmpty(source)) {
            log.error("[查询用户触达信息] 参数异常 request:{}", toJSON(request));
            throw new BizException(USER_NOTIFICATION_INFO_QUERY_ERROR, "参数异常");
        }

        return userNotificationInfoProvider.query(userId, params, source);
    }
}
