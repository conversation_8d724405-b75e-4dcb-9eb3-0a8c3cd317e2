package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.datesource;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.carousel.bo.item.ScorePriorityCarouselItem;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-12
 */
public interface ScorePriorityDataSource<T extends ScorePriorityCarouselItem> {

    Double troughScore();

    long offer(T item);

    long count();

    void remove(T item);

    void removeRedundantItems(int capacity);

    List<T> rangeAndExpire(int start, int stop);
}
