package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.impl;

import static com.kuaishou.kconf.common.json.JsonMapperUtils.toJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.BasicValueTypeEnum.SYSTEM_CALC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorResolver.getBaseFormulaResultType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorResolver.resolveIndicatorBaseDataScale;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_AVG_DATA;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_SUM_CALC_FLAG;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_SUM_DATA;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.resolver.BasicConfigResolver.generateFormula;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.resolver.BasicConfigResolver.getAllBasicFactorConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationPerfEnum.ESTIMATION_DM_QUERY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.utils.IndicatorUnitUtils.getEstimationBasicDataScale;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.INDICATOR_META_NOT_FOUND;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.basicFormulaCalcRoundMode;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.basicFormulaCalcScale;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.calculateAllDateBetween;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.getDaysOfMillionSecondInterval;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.BASIC_INDICATOR_CONFIG;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.CYCLE_DURATION_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.INDICATOR_CONFIG_DO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.INDICATOR_SCALE_SCENE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.USER_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.ArithUtil.limitValue;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicSystemCalcConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndicatorTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BaseFormulaResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.LayerTaskBaseData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums.BaseFormulaResultTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.OffsetEventService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.limiter.LocalRateLimiter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.limiter.config.LimitSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.PortraitActivityIndicatorFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.client.FormulaCalculateClient;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.bo.CalculateResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.bo.FormulaCalculateBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.BizSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-21
 */
@Service
@Slf4j
public class IndicatorBasicNewServiceImpl implements IndicatorBasicNewService {
    @Resource
    private PortraitActivityIndicatorFetchService baseIndicatorFetchService;
    @Resource
    private FormulaCalculateClient formulaCalculateClient;
    @Resource
    private LocalRateLimiter localRateLimiter;
    @Resource
    private OffsetEventService offsetEventService;
    @Resource
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Override
    public Integer resolveBasicFactorEffectiveDays(BasicFactorConfigBO basicFactorConfigBO) {
        IndicatorTimeTypeEnum indicatorTimeType = basicFactorConfigBO.getIndicatorTimeType();
        if (null == indicatorTimeType) {
            return 0;
        }
        int effectiveDays;
        switch (indicatorTimeType) {
            case CUSTOM_TIME:
                List<String> basicDateList = calculateAllDateBetween(basicFactorConfigBO.getFixedStartTime(),
                        basicFactorConfigBO.getFixedEndTime());
                // 过滤大促节假日
                if (basicFactorConfigBO.isExclusiveFestivalDays()) {
                    Map<String, Boolean> holidayAndSaleDayMap =
                            baseIndicatorFetchService.queryDateIsFestivalWithCache(basicDateList);
                    basicDateList = basicDateList.stream()
                            .filter(date -> !holidayAndSaleDayMap.getOrDefault(date, false))
                            .collect(Collectors.toList());
                }
                effectiveDays = basicDateList.size();
                break;
            case RELATIVE_TIME:
                effectiveDays =
                        Long.valueOf(getDaysOfMillionSecondInterval(basicFactorConfigBO.getPeriodTime())).intValue();
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的时间类型");
        }
        log.info("resolveBasicFactorEffectiveDays, effectiveDays:{}, config:{}", effectiveDays,
                toJson(basicFactorConfigBO));
        return effectiveDays;
    }

    @Override
    public List<String> resolveSingleFactorUserEffectiveBasicDate(Long userId,
            BasicFactorConfigBO basicFactorConfigBO) {
        Pair<Long, Long> timeRange = resolveSingleFactorUserEffectiveBasicTimeRange(userId, basicFactorConfigBO);
        if (null == timeRange || timeRange.getLeft() == null || timeRange.getRight() == null) {
            return Lists.newArrayList();
        }
        List<String> basicDateList = calculateAllDateBetween(timeRange.getLeft(), timeRange.getRight());
        // 过滤大促节假日
        if (basicFactorConfigBO.isExclusiveFestivalDays()) {
            Map<String, Boolean> holidayAndSaleDayMap =
                    baseIndicatorFetchService.queryDateIsFestivalWithCache(basicDateList);
            basicDateList = basicDateList.stream()
                    .filter(date -> !holidayAndSaleDayMap.getOrDefault(date, false))
                    .collect(Collectors.toList());
        }
        // 剔除非有效直播日期
        if (basicFactorConfigBO.isExclusiveInvalidLiveDays()) {
            Map<String, Boolean> sellerIsValidLiveDayMap =
                    baseIndicatorFetchService.querySellerIsValidLiveDay(userId, basicDateList,
                            basicFactorConfigBO.getValidLiveTime());
            // 如果过滤出有效开播天数=0
            basicDateList = basicDateList.stream()
                    .filter(date -> sellerIsValidLiveDayMap.getOrDefault(date, false))
                    .collect(Collectors.toList());
        }
        return basicDateList;
    }

    @Override
    public BasicTimeRange getBasicTimeRange(Long userId, BasicIndicatorConfigBO basicIndicatorConfigBO) {
        List<BasicFactorConfigBO> basicFactorConfigList = getAllBasicFactorConfig(basicIndicatorConfigBO);
        Long earliestStartTime = null;
        Long latestEndTime = null;
        for (BasicFactorConfigBO basicFactorConfigBO : basicFactorConfigList) {
            Pair<Long, Long> timeRange = resolveSingleFactorUserEffectiveBasicTimeRange(userId, basicFactorConfigBO);
            if (null == timeRange) {
                continue;
            }
            Long startTime = timeRange.getLeft();
            Long endTime = timeRange.getRight();
            if (null == startTime || null == endTime) {
                continue;
            }
            if (null == earliestStartTime || startTime < earliestStartTime) {
                earliestStartTime = startTime;
            }
            if (null == latestEndTime || endTime > latestEndTime) {
                latestEndTime = endTime;
            }
        }
        return BasicTimeRange.builder()
                .earliestStartTime(earliestStartTime)
                .latestEndTime(latestEndTime)
                .build();
    }

    @Override
    public BasicTimeRange getBasicTimeRange(Long userId, BasicConfigBO basicConfigBO) {
        if (null == basicConfigBO) {
            return BasicTimeRange.builder().build();
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        if (CollectionUtils.isEmpty(basicIndicatorConfigList)) {
            return BasicTimeRange.builder().build();
        }
        Long earliestStartTime = null;
        Long latestEndTime = null;
        for (BasicIndicatorConfigBO basicIndicatorConfigBO : basicIndicatorConfigList) {
            BasicTimeRange basicTimeRange = getBasicTimeRange(userId, basicIndicatorConfigBO);
            if (null == basicTimeRange) {
                continue;
            }
            Long startTime = basicTimeRange.getEarliestStartTime();
            Long endTime = basicTimeRange.getLatestEndTime();
            if (null == startTime || null == endTime) {
                continue;
            }
            if (null == earliestStartTime || startTime < earliestStartTime) {
                earliestStartTime = startTime;
            }
            if (null == latestEndTime || endTime > latestEndTime) {
                latestEndTime = endTime;
            }
        }
        return BasicTimeRange.builder()
                .earliestStartTime(earliestStartTime)
                .latestEndTime(latestEndTime)
                .build();
    }

    @Override
    public Pair<Long, Long> resolveSingleFactorUserEffectiveBasicTimeRange(Long userId,
            BasicFactorConfigBO basicFactorConfigBO) {
        IndicatorTimeTypeEnum indicatorTimeType = basicFactorConfigBO.getIndicatorTimeType();
        switch (indicatorTimeType) {
            case CUSTOM_TIME:
                return Pair.of(basicFactorConfigBO.getFixedStartTime(), basicFactorConfigBO.getFixedEndTime());
            case RELATIVE_TIME:
                // 兼容不传userId的情况
                if (null == userId) {
                    return Pair.of(null, null);
                }
                OffsetPeriodConfigBO offsetPeriodConfigBO = OffsetPeriodConfigBO.builder()
                        .offsetEventType(basicFactorConfigBO.getOffsetEventType())
                        .periodTime(basicFactorConfigBO.getPeriodTime())
                        .relativeTime(basicFactorConfigBO.getRelativeTime())
                        .build();
                OffsetPeriodBO offsetPeriodBO =
                        offsetEventService.calculateOffsetPeriod(offsetPeriodConfigBO, userId);
                return Pair.of(offsetPeriodBO.getStartTime(), offsetPeriodBO.getEndTime());
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "未知时间基期类型");
        }
    }

    @Override
    public LayerTaskBaseData queryUserSingleBasicIndicatorDataFromDM(long userId,
            BasicIndicatorConfigBO basicIndicatorConfigBO, IndicatorConfigDO indicatorConfigDO,
            Map<String, Object> extraData) {

        // [cb] 从dm获取单一基期指标的基期值
        Map<String, Object> layerTaskBaseData = Maps.newHashMap();
        // 根据指标配置取精度
        Integer scale = null;
        if (indicatorConfigDO != null) {
            scale = getIndicatorBaseDataScale(indicatorConfigDO.getIndicatorId());
        }
        BaseFormulaResult baseFormulaResult = getFormulaResult(userId, basicIndicatorConfigBO, indicatorConfigDO,
                null == scale ? basicFormulaCalcScale.get() : scale, extraData);
        BigDecimal formulaResult = baseFormulaResult.getResultValue();

        BasicSystemCalcConfigBO basicSystemCalcConfig = basicIndicatorConfigBO.getBasicSystemCalcConfig();
        String basicCalcResult = limitValue(formulaResult, basicSystemCalcConfig.getFixedMaxValue(),
                basicSystemCalcConfig.getFixedMinValue());

        BaseFormulaResultTypeEnum baseFormulaResultType = baseFormulaResult.getBaseFormulaResultType();
        LayerTaskBaseData result = LayerTaskBaseData.builder()
                .type(baseFormulaResultType).baseDataMap(layerTaskBaseData).build();
        switch (baseFormulaResultType) {
            case AVG:
                layerTaskBaseData.put(BASE_INDICATOR_AVG_DATA.format(basicIndicatorConfigBO.getIndicatorId()), basicCalcResult);
                break;
            case TOTAL:
                layerTaskBaseData.put(BASE_INDICATOR_SUM_DATA.format(basicIndicatorConfigBO.getIndicatorId()), basicCalcResult);
                layerTaskBaseData.put(BASE_INDICATOR_SUM_CALC_FLAG.format(basicIndicatorConfigBO.getIndicatorId()), true);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "基期结果计算类型异常");
        }

        return result;
    }

    @Override
    public LayerTaskBaseData queryUserSingleBasicIndicatorDataFromDM(long userId,
            BasicIndicatorConfigBO basicIndicatorConfigBO, IndicatorConfigDO indicatorConfigDO, Integer scale,
            Map<String, Object> extraData) {

        // [cb] 从dm获取单一基期指标的基期值-指定精度
        Map<String, Object> layerTaskBaseData = Maps.newHashMap();
        BaseFormulaResult baseFormulaResult =
                getFormulaResult(userId, basicIndicatorConfigBO, indicatorConfigDO, scale, extraData);
        BigDecimal formulaResult = baseFormulaResult.getResultValue();

        BasicSystemCalcConfigBO basicSystemCalcConfig = basicIndicatorConfigBO.getBasicSystemCalcConfig();
        String basicCalcResult = limitValue(formulaResult, basicSystemCalcConfig.getFixedMaxValue(),
                basicSystemCalcConfig.getFixedMinValue());

        BaseFormulaResultTypeEnum baseFormulaResultType = baseFormulaResult.getBaseFormulaResultType();
        LayerTaskBaseData result = LayerTaskBaseData.builder()
                .type(baseFormulaResultType).baseDataMap(layerTaskBaseData).build();
        switch (baseFormulaResultType) {
            case AVG:
                layerTaskBaseData.put(BASE_INDICATOR_AVG_DATA.format(basicIndicatorConfigBO.getIndicatorId()),
                        basicCalcResult);
                break;
            case TOTAL:
                layerTaskBaseData.put(BASE_INDICATOR_SUM_DATA.format(basicIndicatorConfigBO.getIndicatorId()),
                        basicCalcResult);
                layerTaskBaseData.put(BASE_INDICATOR_SUM_CALC_FLAG.format(basicIndicatorConfigBO.getIndicatorId()),
                        true);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "基期结果计算类型异常");
        }

        return result;
        // [ce]
    }

    /**
     * 获取基期公式计算结果
     *
     * @param userId
     * @param basicIndicatorConfigBO
     * @param indicatorConfigDO
     * @param scale
     * @return
     */
    private BaseFormulaResult getFormulaResult(long userId, BasicIndicatorConfigBO basicIndicatorConfigBO,
            IndicatorConfigDO indicatorConfigDO, Integer scale, Map<String, Object> extraData) {
        // [cb] 获取基期公式计算结果
        Preconditions.checkNotNull(basicIndicatorConfigBO, "基期配置不能为空");
        Preconditions.checkArgument(
                basicIndicatorConfigBO.getBasicValueType().equals(SYSTEM_CALC),
                "基期配置非系统计算类型");
        BasicSystemCalcConfigBO basicSystemCalcConfig = basicIndicatorConfigBO.getBasicSystemCalcConfig();
        Preconditions.checkNotNull(basicSystemCalcConfig, "系统计算配置不能为空");
        List<BasicFactorConfigBO> basicFactorConfigList = basicSystemCalcConfig.getBasicFactorConfigList();
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(basicFactorConfigList), "系统计算基期因子不能为空");

        // 当前多个基期因子计算方式保持一致
        BasicFactorConfigBO basicFactorConfigBO = basicFactorConfigList.stream()
                .findAny().orElseThrow(() -> new BizException(BasicErrorCode.SERVER_ERROR, "系统计算基期因子不存在"));
        String formula = generateFormula(basicIndicatorConfigBO.getBasicSystemCalcConfig());
        if (StringUtils.isEmpty(formula)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "基期公式生成异常");
        }
        Long cycleDuration = null;
        if (MapUtils.isNotEmpty(extraData)) {
            cycleDuration = MapUtils.getLong(extraData, CYCLE_DURATION_KEY);
        }
        Map<String, Object> bizParam = new HashMap<>(4);
        bizParam.put(USER_ID, userId);
        bizParam.put(BASIC_INDICATOR_CONFIG, basicIndicatorConfigBO);
        bizParam.put(INDICATOR_CONFIG_DO, indicatorConfigDO);
        bizParam.put(INDICATOR_SCALE_SCENE, scale);
        bizParam.put(CYCLE_DURATION_KEY, cycleDuration);
        FormulaCalculateBO formulaCalculateBO = FormulaCalculateBO.builder()
                .bizScene(BizSceneEnum.BASIC_CALC)
                .formula(formula)
                .scale(scale == null ? basicFormulaCalcScale.get() : scale)
                .roundingMode(RoundingMode.valueOf(basicFormulaCalcRoundMode.get()))
                .bizParam(bizParam)
                .build();

        Long indicatorId = basicIndicatorConfigBO.getIndicatorId();
        BaseFormulaResultTypeEnum baseFormulaResultType = getBaseFormulaResultType(basicFactorConfigBO.getBaseAlgorithm(), indicatorId);

        CalculateResult calculateResult = formulaCalculateClient.calculate(formulaCalculateBO);

        return BaseFormulaResult.builder()
                .resultValue(calculateResult.getResult())
                .baseFormulaResultType(baseFormulaResultType)
                .build();
        // [ce]
    }



    @Override
    public Map<String, Object> queryUserTotalBasicIndicatorDataFromDM(long userId, BasicConfigBO basicConfigBO,
            List<IndicatorConfigDO> indicatorConfigDOList) {
        Map<String, Object> basicValueMap = new HashMap<>();
        Map<Long, IndicatorConfigDO> indicatorConfigDOMap = CollectionUtils.emptyIfNull(indicatorConfigDOList).stream()
                .collect(Collectors.toMap(IndicatorConfigDO::getIndicatorId, Function.identity(), (o1, o2) -> o2));
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        basicIndicatorConfigList.forEach(basicIndicatorConfigBO -> {
            if (basicIndicatorConfigBO.getBasicValueType() == SYSTEM_CALC) {
                IndicatorConfigDO indicatorConfigDO = indicatorConfigDOMap.get(basicIndicatorConfigBO.getIndicatorId());
                basicValueMap.putAll(queryUserSingleBasicIndicatorDataFromDM(userId, basicIndicatorConfigBO,
                                indicatorConfigDO, new HashMap<>()).getBaseDataMap());
            }
        });
        return basicValueMap;
    }

    @Override
    public Map<Long, Map<String, Object>> batchQueryUserTotalBasicIndicatorDataFromDM(List<Long> userIdList,
            BasicConfigBO basicConfigBO, List<IndicatorConfigDO> indicatorConfigDOList) {
        Map<Long, Map<String, Object>> userBasicValueMap = new HashMap<>();
        userIdList.forEach(userId -> userBasicValueMap.put(userId,
                queryUserTotalBasicIndicatorDataFromDM(userId, basicConfigBO, indicatorConfigDOList)));
        return userBasicValueMap;
    }

    @Override
    public Map<Long, LayerTaskBaseData> queryUserBasicIndicatorDataFromDMForDap(Long userId,
                                                                                BasicConfigBO basicConfigBO,
                                                                                Integer activityDays, Map<Long,
            IndicatorConfigDO> estimateIndicatorConfigMap) {
        Map<Long, LayerTaskBaseData> map = new HashMap<>();
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        basicIndicatorConfigList.forEach(basicIndicatorConfigBO -> {
            Long indicatorId = basicIndicatorConfigBO.getIndicatorId();
            try {
                if (!basicIndicatorConfigBO.getBasicValueType().equals(SYSTEM_CALC)) {
                    return;
                }
                IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
                if (null == indicatorDO) {
                    throw new BizException(INDICATOR_META_NOT_FOUND);
                }
                IndicatorConfigDO estimationIndicatorConfig = null;
                // 获取测算指标对应的指标配置
                if (MapUtils.isNotEmpty(estimateIndicatorConfigMap)) {
                    estimationIndicatorConfig = estimateIndicatorConfigMap.get(indicatorId);
                }

                Integer scale = getEstimationBasicDataScale(indicatorDO, null);

                // 前置传入活动周期
                Map<String, Object> extraData = new HashMap<>();
                extraData.put(CYCLE_DURATION_KEY, activityDays);

                // [cb] 策略实验室查询用户基期数据
                // 单机-指标维度的并发控制
                localRateLimiter.acquire(LimitSceneEnum.DM_QUERY, String.valueOf(indicatorId));
                perfScene(ESTIMATION_DM_QUERY, String.valueOf(indicatorId));
                LayerTaskBaseData result;
                if (scale == null) {
                    result = queryUserSingleBasicIndicatorDataFromDM(userId,
                            basicIndicatorConfigBO, estimationIndicatorConfig, extraData);
                } else {
                    result = queryUserSingleBasicIndicatorDataFromDM(userId,
                            basicIndicatorConfigBO, estimationIndicatorConfig, scale, extraData);
                }
                // [ce]
                map.put(indicatorId, result);
                perfSuccess(ESTIMATION_DM_QUERY, String.valueOf(indicatorId));
            } catch (Exception e) {
                perfException(ESTIMATION_DM_QUERY, String.valueOf(indicatorId));
                throw e;
            }
        });
        return map;
    }

    private Integer getIndicatorBaseDataScale(long indicatorId) {
        IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
        if (null == indicatorDO) {
            throw new BizException(INDICATOR_META_NOT_FOUND);
        }
        return resolveIndicatorBaseDataScale(indicatorDO);
    }
}
