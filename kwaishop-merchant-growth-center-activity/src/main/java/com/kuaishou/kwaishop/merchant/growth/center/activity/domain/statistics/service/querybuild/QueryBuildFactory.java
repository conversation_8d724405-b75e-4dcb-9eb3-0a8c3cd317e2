package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.base.Joiner;
import com.kuaishou.framework.elasticsearch.ElasticSearchSort;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.RangeCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-26
 */
@Slf4j
@Service
@Lazy
public class QueryBuildFactory {
    private Map<String, QueryBuildHandler> queryBuildHandlerMap;

    @Autowired
    private List<QueryBuildHandler> queryBuildHandlers;

    /**
     * 最小需要匹配的 should 子句数量
     */
    private static final String MIN_SHOULD_NUM = "1";


    @PostConstruct
    private void init() {
        queryBuildHandlerMap = new HashMap<>();
        queryBuildHandlers
                .forEach(i -> queryBuildHandlerMap.put(i.conditionType().getCode(), i));
    }

    /**
     * 根据条件类型获取对应的处理类
     */
    public QueryBuildHandler getBatchExecuteByType(String conditionType) {
        QueryBuildHandler queryBuildHandler = queryBuildHandlerMap.get(conditionType);
        if (queryBuildHandler == null) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "ES查询无法获取处理器-" + conditionType);
        }
        return queryBuildHandlerMap.get(conditionType);
    }

    /**
     * 根据条件列表构建查询最终QueryBuild
     */
    public QueryBuilder buildQueryBuilder(List<StatisticsConditionBO> conditions, Map<String, Object> entityParam) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // nested，相同path下的condition合并
        conditions = mergeSamePathNestedCondition(conditions);
        for (StatisticsConditionBO condition : conditions) {
            QueryBuildHandler handler = getBatchExecuteByType(condition.getConditionType());
            boolQueryBuilder.must(handler.buildConditionQueryBuild(condition, entityParam));
        }
        return boolQueryBuilder;
    }

    /**
     * 根据条件列表构建查询最终QueryBuild
     * 包含notMust条件的查询
     *
     * @param conditions
     * @param entityParam
     * @return
     */
    public QueryBuilder buildQueryBuilderNotMust(List<StatisticsConditionBO> conditions,
                                                 Map<String, Object> entityParam) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // nested，相同path下的condition合并
        conditions = mergeSamePathNestedCondition(conditions);
        List<StatisticsConditionBO> noMustConditionList = conditions.stream()
                .filter(condition -> BooleanUtils.isTrue(condition.getNoMust()))
                .collect(Collectors.toList());
        // 过滤出notMust 条件
        if (CollectionUtils.isNotEmpty(noMustConditionList)) {
            for (StatisticsConditionBO condition : noMustConditionList) {
                QueryBuildHandler handler = getBatchExecuteByType(condition.getConditionType());
                boolQueryBuilder.mustNot(handler.buildConditionQueryBuild(condition, entityParam));
            }
        }
        List<StatisticsConditionBO> mustConditionList = conditions.stream()
                .filter(condition -> BooleanUtils.isNotTrue(condition.getNoMust()))
                .collect(Collectors.toList());
        // 过滤出包含可匹配条件的condition
        List<StatisticsConditionBO> shouldConditions = mustConditionList.stream()
                .filter(e -> e.getShould() != null && e.getShould())
                .collect(Collectors.toList());
        // 移除可匹配条件的condition
        mustConditionList.removeAll(shouldConditions);
        for (StatisticsConditionBO condition : mustConditionList) {
            QueryBuildHandler handler = getBatchExecuteByType(condition.getConditionType());
            boolQueryBuilder.must(handler.buildConditionQueryBuild(condition, entityParam));
        }
        // 拼接should condition
        if (CollectionUtils.isNotEmpty(shouldConditions)) {
            shouldConditions.forEach(
                    c -> {
                        QueryBuildHandler handler = getBatchExecuteByType(c.getConditionType());
                        boolQueryBuilder.should(handler.buildConditionQueryBuild(c, entityParam));
                    });
            boolQueryBuilder.minimumShouldMatch(MIN_SHOULD_NUM);
        }
        return boolQueryBuilder;
    }

    /**
     * 根据条件列表构建查询最终QueryBuild（包含should匹配子句）
     */
    public QueryBuilder buildQueryBuilderContainsShould(List<StatisticsConditionBO> conditions,
                                                        Map<String, Object> entityParam) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // nested，相同path下的condition合并
        conditions = mergeSamePathNestedCondition(conditions);
        // 过滤出包含可匹配条件的condition
        List<StatisticsConditionBO> shouldConditions = conditions.stream()
                .filter(e -> e.getShould() != null && e.getShould())
                .collect(Collectors.toList());
        // 移除可匹配条件的condition
        conditions.removeAll(shouldConditions);
        for (StatisticsConditionBO condition : conditions) {
            QueryBuildHandler handler = getBatchExecuteByType(condition.getConditionType());
            boolQueryBuilder.must(handler.buildConditionQueryBuild(condition, entityParam));
        }
        // 拼接should condition
        if (CollectionUtils.isNotEmpty(shouldConditions)) {
            shouldConditions.forEach(
                    c -> {
                        QueryBuildHandler handler = getBatchExecuteByType(c.getConditionType());
                        boolQueryBuilder.should(handler.buildConditionQueryBuild(c, entityParam));
                    });
            boolQueryBuilder.minimumShouldMatch(MIN_SHOULD_NUM);
        }
        return boolQueryBuilder;
    }

    /**
     * nested，相同path下的condition合并（逻辑且）
     */
    public static List<StatisticsConditionBO> mergeSamePathNestedCondition(List<StatisticsConditionBO> conditions) {
        List<StatisticsConditionBO> nestedCondition = conditions.stream()
                .filter(e -> e.getConditionType().equals(ConditionTypeEnum.NESTED.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nestedCondition)) {
            return conditions;
        }
        Map<String, List<StatisticsConditionBO>> conditionsMap = nestedCondition.stream()
                .collect(Collectors.groupingBy(StatisticsConditionBO::getFieldName));
        List<StatisticsConditionBO> mergeConditions = Lists.newArrayList();
        conditionsMap.forEach((path, sameNestedConditions) -> {
            List<StatisticsConditionBO> childConditions = sameNestedConditions.stream()
                    .map(StatisticsConditionBO::getConditions).flatMap(List::stream).collect(Collectors.toList());
            StatisticsConditionBO mergeCondition = new StatisticsConditionBO();
            mergeCondition.setFieldName(path);
            mergeCondition.setConditions(childConditions);
            mergeCondition.setConditionType(ConditionTypeEnum.NESTED.getCode());
            mergeConditions.add(mergeCondition);
        });
        List<StatisticsConditionBO> leftConditions = conditions.stream()
                .filter(e -> !e.getConditionType().equals(ConditionTypeEnum.NESTED.getCode()))
                .collect(Collectors.toList());
        leftConditions.addAll(mergeConditions);
        return leftConditions;
    }

    /**
     * 根据字段构建term类型condition
     */
    public StatisticsConditionBO buildTermCondition(String field, Number value) {
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.TERM.getCode());
        conditionBO.setConditionValue(String.valueOf(value));
        conditionBO.setConditionValueType(ConditionValueTypeEnum.NUMBER.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建range类型condition
     *
     * @param field
     * @param leftCondition
     * @param rightCondition
     * @param conditionValueTypeEnum
     * @return
     */
    public StatisticsConditionBO buildRangeCondition(String field, RangeCondition leftCondition,
                                                     RangeCondition rightCondition,
                                                     ConditionValueTypeEnum conditionValueTypeEnum) {
        if (conditionValueTypeEnum == null) {
            conditionValueTypeEnum = ConditionValueTypeEnum.NUMBER;
        }
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.RANGE.getCode());
        conditionBO.setRangeConditionPair(Pair.of(leftCondition, rightCondition));
        conditionBO.setConditionValueType(conditionValueTypeEnum.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建term类型condition
     */
    public StatisticsConditionBO buildTermCondition(String field, String value) {
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.TERM.getCode());
        conditionBO.setConditionValue(value);
        conditionBO.setConditionValueType(ConditionValueTypeEnum.STRING.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建Wildcard类型condition
     */
    public StatisticsConditionBO buildMatchCondition(String field, String value) {
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.MATCH.getCode());
        conditionBO.setConditionValue(value);
        conditionBO.setConditionValueType(ConditionValueTypeEnum.STRING.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建nested+term类型condition
     */
    public StatisticsConditionBO buildNestedTermCondition(String field, String path, Number value) {
        StatisticsConditionBO subCondition = buildTermCondition(Joiner.on(".").join(path, field), value);
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(path);
        conditionBO.setConditionType(ConditionTypeEnum.NESTED.getCode());
        conditionBO.setConditions(Collections.singletonList(subCondition));
        return conditionBO;
    }

    /**
     * 根据字段构建term类型condition
     */
    public StatisticsConditionBO buildTermsCondition(String field, List<Long> value) {
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.TERMS.getCode());
        conditionBO.setConditionValue(String.valueOf(value));
        conditionBO.setConditionValueType(ConditionValueTypeEnum.NUMBER.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建term类型condition
     *
     * @param field
     * @param value
     * @param noMust
     * @return
     */
    public StatisticsConditionBO buildTermsCondition(String field, List<Long> value, Boolean noMust) {
        StatisticsConditionBO condition = buildTermsCondition(field, value);
        condition.setNoMust(noMust);
        return condition;
    }

    /**
     * 根据字段构建term类型condition
     */
    public StatisticsConditionBO buildStrTermsCondition(String field, List<String> value) {
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(field);
        conditionBO.setConditionType(ConditionTypeEnum.TERMS.getCode());
        conditionBO.setConditionValue(ObjectMapperUtils.toJSON(value));
        conditionBO.setConditionValueType(ConditionValueTypeEnum.STRING.getCode());
        return conditionBO;
    }

    /**
     * 根据字段构建term类型condition-should
     */
    public StatisticsConditionBO buildStrTermsConditionShould(String field, List<String> value) {
        StatisticsConditionBO condition = buildStrTermsCondition(field, value);
        condition.setShould(true);
        return condition;
    }

    /**
     * 根据字段构建nested+terms类型condition
     */
    public StatisticsConditionBO buildNestedTermsCondition(String field, String path, List<Long> value) {
        StatisticsConditionBO subCondition = buildTermsCondition(Joiner.on(".").join(path, field), value);
        StatisticsConditionBO conditionBO = new StatisticsConditionBO();
        conditionBO.setFieldName(path);
        conditionBO.setConditionType(ConditionTypeEnum.NESTED.getCode());
        conditionBO.setConditions(Collections.singletonList(subCondition));
        return conditionBO;
    }


    /**
     * 构建分页排序规则
     */
    private List<Map<String, Object>> getSortMap() {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> defaultSort = new HashMap<>();
        defaultSort.put("userId", ElasticSearchSort.DESC);
        result.add(defaultSort);
        return result;
    }
}
