package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.MQ_NEW_ADD_ONLINE_CHECK_FINISH_EVENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.validate.AdminTemplateValidator.mergeMultiSet;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.onlineNewAddSubActivityPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.IndustryActivityBuildService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.handler.StrategyAdminHandlerService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.BatchExecuteResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.NewAddSubActivityConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.NewAddSubActivityEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.OperateLogUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.localcache.RegistrationLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-21
 */
@Service
@Slf4j
@Lazy
public class NewAddSubActivityImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private AdminKimService adminKimService;

    @Autowired
    private OperateLogUtils operateLogUtils;

    @Autowired
    private AdminActivityOnlineService onlineService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private StrategyAdminHandlerService registrationHandleService;

    @Autowired
    private IndustryActivityBuildService industryActivityBuildService;

    @Autowired
    private RegistrationLocalCacheService registrationLocalCacheService;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        // 事件强转
        NewAddSubActivityEvent newAddSubActivityEvent = (NewAddSubActivityEvent) event;
        // 活动ID
        long onlineActivityId = newAddSubActivityEvent.getActivityId();
        // 任务ID
        List<TaskDO> newAddParentTaskList = newAddSubActivityEvent.getNewAddTaskList();
        // 活动&各分层人群构建
        Map<Long, Set<Long>> newAddLayerTaskCrowd =
                newAddParentTaskList.stream().collect(Collectors.toMap(BaseDO::getId,
                        e -> industryActivityBuildService.getSellerByCrowdType(e.getCrowdType(), e.getCrowdConfig())));
        Set<Long> activityCrowd = mergeMultiSet(newAddLayerTaskCrowd.values());
        // 父任务excel信息缓存
        newAddParentTaskList.forEach(
                parentTask -> onlineService.buildLayerTaskExcelCache(onlineActivityId, parentTask));
        // 策略测算不允许追加
        newAddSubActivityEvent.setLayerTaskCrowd(newAddLayerTaskCrowd);
        // 上线开始播报
        adminKimService.onlineBeginKimReport(newAddSubActivityEvent.getActivityDO(), activityCrowd.size(),
                "newAddOnlineBeginKimReport");
        return activityCrowd;
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.NEW_ADD_SUB_ACTIVITY_ONLINE;
    }

    @Override
    public ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        NewAddSubActivityConfigBO executeConfigBO = fromJSON(executeConfig, NewAddSubActivityConfigBO.class);
        long activityId = executeConfigBO.getActivityId();
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        List<Long> successUserList = Lists.newArrayList();
        List<Long> riskUserList = Lists.newArrayList();
        userIdList.forEach(userId -> {
            boolean success = singleUserExecute(userId, activityDO, executeConfigBO);
            if (!success) {
                riskUserList.add(userId);
            } else {
                successUserList.add(userId);
            }
        });
        ExecuteHandleResult result = new ExecuteHandleResult();
        result.setSuccessUserList(successUserList);
        result.setFailUserList(riskUserList);
        return result;
    }

    private boolean singleUserExecute(long userId, ActivityDO activityDO, NewAddSubActivityConfigBO executeConfigBO) {
        long onlineActivityId = activityDO.getId();
        // 需要报名父任务
        Map<Long, List<Long>> userCanDrawParentTaskMap = executeConfigBO.getUserCanDrawParentTaskMap();
        List<Long> userCanDrawParentTaskIdList = userCanDrawParentTaskMap.get(userId);
        // 报名配置
        Map<Long, AddRegistrationOptionBO> optionMap = registrationLocalCacheService
                .queryDeployPreSubActivityParentTaskListCache(onlineActivityId, userCanDrawParentTaskIdList);
        return registrationHandleService.handleAddUserRegistration(userId, activityDO.getId(),
                userCanDrawParentTaskIdList, "extraAdd", optionMap);
    }

    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 事件强转
        NewAddSubActivityEvent newAddSubActivityEvent = (NewAddSubActivityEvent) event;
        Map<Long, List<Long>> userCanDrawParentMap = onlineService
                .buildUserCanDrawParentMap(newAddSubActivityEvent.getLayerTaskCrowd(), userIdList);
        // 数据组装
        NewAddSubActivityConfigBO executeConfig = new NewAddSubActivityConfigBO();
        executeConfig.setActivityId(newAddSubActivityEvent.getActivityId());
        executeConfig.setUserCanDrawParentTaskMap(userCanDrawParentMap);
        return toJSON(executeConfig);
    }

    @Override
    protected boolean needFinishAction() {
        return true;
    }

    @Override
    protected void batchExecuteResultFinishAction(BatchExecuteResult result) {
        long totalNum = result.getTotalNum();
        long successUserNum = result.getSuccessUserNum();
        Set<String> riskUserList = result.getFailUserList();
        if (!result.isFinish()) {
            return;
        }
        // 查询活动数据
        long activityId = Long.parseLong(result.getEventId().split("_")[0]);
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        // 存量招商活动刷新
        onlineService.investmentOnlineFlush(activityId);
        // 记录操作日志
        operateLogUtils.recordZeusRiskOperateLog(0L, activityId, "NewAddSubActivityOnline",
                activityDO.getModifier(), toJSON(result));
        // 上线播报
        adminKimService
                .onlineResultKimReport(activityDO, totalNum, successUserNum, riskUserList, activityDO.getUpdateTime());
        perfSuccess(MQ_NEW_ADD_ONLINE_CHECK_FINISH_EVENT, result.getEventId());
    }

    @Override
    int getPartitionSize() {
        return onlineNewAddSubActivityPartitionSize.get();
    }
}
