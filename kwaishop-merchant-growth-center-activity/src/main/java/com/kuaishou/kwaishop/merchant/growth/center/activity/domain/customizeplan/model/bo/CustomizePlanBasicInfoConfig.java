package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizePlanBasicInfoConfig {
    /**
     * 任务中心定制计划卡片PC端Banner
     */
    private String taskCenterCardBannerPC;

    /**
     * 任务中心定制计划卡片APP端Banner
     */
    private String taskCenterCardBannerAPP;

    /**
     * 定制计划图片
     */
    private String customizePlanPicture;

    /**
     * 定制计划名称
     */
    private String mainTitle;

    /**
     * 定制计划简介
     */
    private String desc;

    /**
     * 计划开始时间
     */
    private Long startTime;

    /**
     * 计划结束时间
     */
    private Long endTime;

    /**
     * 计划报名截止时间
     */
    private Long drawEndTime;

    /**
     * key: 活动ID value: 名称
     */
    private Map<String, String> activityNameMap;
}
