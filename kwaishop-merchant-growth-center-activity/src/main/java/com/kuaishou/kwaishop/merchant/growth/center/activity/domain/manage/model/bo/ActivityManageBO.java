package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityManageBO {
    /**
     * 活动对象
     */
    private ActivityDO activity;
    /**
     * 通知配置
     */
    private List<NotificationPushConfigBO> noticeConfigList;
    /**
     * 基础指标配置
     */
    private List<IndicatorManageBO> indicatorConfigList;
}
