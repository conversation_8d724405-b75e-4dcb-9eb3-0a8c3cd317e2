package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchAssemblerConstantBO.LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.biz.AwardBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.UserActivityAwardShowInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.UserTaskAwardShowInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.param.AwardRecordQueryParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchUserAwardDataSource implements LaunchDataSource {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Resource
    private UserAwardRecordDAO userAwardRecordDAO;

    @Resource
    private AwardBizService awardBizService;

    @Override
    @SuppressWarnings("unchecked")
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();
        Long userId = param.getUserId();
        Integer entityType = param.getEntityType();
        List<Long> entityIds = param.getEntityIds();

        List<AwardConfigDO> awardConfigs = Lists.newArrayList();
        List<UserAwardRecordDO> userAwardRecords = Lists.newArrayList();
        List<UserTaskAwardShowInfoBO> userTaskAwardShowInfoList = Lists.newArrayList();
        UserActivityAwardShowInfoBO userActivityAwardShowInfo = null;

        Map<String, Object> resMap = Maps.newHashMap();

        // 获取用户资格信息
        Map<String, Object> customizeParamMap = param.getCustomizeParamMap();
        List<UserRegistrationRecordBO> userRegistrationRecords = (List<UserRegistrationRecordBO>)
                MapUtils.getObject(customizeParamMap, LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS);

        // 资格为空直接返回
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchUserAwardData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(entityType);
        switch (entityTypeEnum) {
            case SUB_ACTIVITY:
                // 根据子活动ID拉取任务配置
                List<TaskDO> taskList = launchResolveService
                        .resolveRegistrationTaskConfigBySubActivityId(activityId, entityIds, userRegistrationRecords);
                if (CollectionUtils.isEmpty(taskList)) {
                    break;
                }

                List<Long> taskIdList = taskList.stream().map(TaskDO::getId).collect(Collectors.toList());

                awardConfigs = awardConfigLocalCacheService.queryMultiTaskAwardConfig(activityId, taskIdList);
                AwardRecordQueryParam awardRecordQueryParam = AwardRecordQueryParam.builder().userId(userId)
                        .activityId(activityId).entityIds(taskIdList).build();
                userAwardRecords = userAwardRecordDAO.queryAwardRecord(awardRecordQueryParam);

                List<Long> parentTaskIds =
                        taskList.stream().filter(task -> Objects.equals(task.getParentTask(), 0L))
                                .map(TaskDO::getId).collect(Collectors.toList());
                userTaskAwardShowInfoList = awardBizService
                        .queryUserParentTaskAwardShowInfoList(userId, activityId, parentTaskIds);

                break;
            default:
                awardConfigs = awardConfigLocalCacheService.queryAwardConfigByActivityId(activityId);
                userAwardRecords = userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
                userActivityAwardShowInfo = awardBizService.queryUserActivityAwardShowInfo(userId, activityId);
        }

        LaunchUserAwardData data = LaunchUserAwardData.builder()
                .awardConfigs(awardConfigs)
                .userAwardRecords(userAwardRecords)
                .userTaskAwardShowInfoList(userTaskAwardShowInfoList)
                .userActivityAwardShowInfo(userActivityAwardShowInfo)
                .build();

        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.USER_AWARD_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchUserAwardData {

        /**
         * 奖励配置列表
         */
        private List<AwardConfigDO> awardConfigs;

        /**
         * 用户奖励记录列表
         */
        private List<UserAwardRecordDO> userAwardRecords;

        /**
         * 用户父任务奖励展示信息
         * 子活动实体
         */
        private List<UserTaskAwardShowInfoBO> userTaskAwardShowInfoList;

        /**
         * 用户活动奖励展示信息
         * 活动实体
         */
        private UserActivityAwardShowInfoBO userActivityAwardShowInfo;

        public static LaunchUserAwardData empty() {
            return LaunchUserAwardData.builder()
                    .awardConfigs(Lists.newArrayList())
                    .userAwardRecords(Lists.newArrayList())
                    .userTaskAwardShowInfoList(Lists.newArrayList())
                    .userActivityAwardShowInfo(null)
                    .build();
        }
    }
}
