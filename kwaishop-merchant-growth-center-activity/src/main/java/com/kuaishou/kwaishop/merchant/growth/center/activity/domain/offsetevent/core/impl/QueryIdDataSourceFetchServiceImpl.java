package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorPerfEnum.OFFSET_EVENT_NOT_HAPPEN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.FETCH_DATA_MANAGE_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.enableOffsetEventHappenPerf;
import static java.util.Collections.singletonList;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.OffsetEventHappenTimeFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.QueryIdDataSourceConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.OffsetEventDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.DataManagerFetchService;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
@Service
@Slf4j
public class QueryIdDataSourceFetchServiceImpl implements OffsetEventHappenTimeFetchService<QueryIdDataSourceConfig> {
    @Resource
    private DataManagerFetchService dataManagerFetchService;

    @Override
    public OffsetEventDataSourceTypeEnum getDataSourceType() {
        return OffsetEventDataSourceTypeEnum.QUERY_ID;
    }

    @Override
    public Long fetchOffsetEventHappenTime(OffsetEventConfigBO<QueryIdDataSourceConfig> offsetEventConfigBO,
            Long userId) {
        QueryIdDataSourceConfig dataSourceConfig = offsetEventConfigBO.getDataSourceConfig();
        Preconditions.checkNotNull(dataSourceConfig, "数据源配置不能为空");
        String latestDate = dataManagerFetchService.getDateUpdateVersion();
        if (StringUtils.isBlank(latestDate)) {
            throw new BizException(FETCH_DATA_MANAGE_ERROR);
        }
        List<String> lastestDateList = singletonList(latestDate);
        long result = dataManagerFetchService.getIndicatorValue(userId, dataSourceConfig.getQueryId(),
                dataSourceConfig.getResCode(), lastestDateList, null);
        if (0L == result) {
            log.warn("[fetchOffsetEventHappenTime] 没有查询到数据, userId:{}, queryId:{} ", userId,
                    dataSourceConfig.getQueryId());
            if (enableOffsetEventHappenPerf.get()) {
                PerfUtil.perfException(OFFSET_EVENT_NOT_HAPPEN, offsetEventConfigBO.getEventType(), userId.toString());
            }
            return null;
        }
        // de返回的数据秒级别，转化为毫秒级别
        return result * 1000L;
    }
}
