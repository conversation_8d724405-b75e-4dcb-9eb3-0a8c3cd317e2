package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchFieldResolveResultBO {

    private Map<String, Object> resMap;

    public static LaunchFieldResolveResultBO getDefaultResult() {
        return LaunchFieldResolveResultBO.builder().resMap(Maps.newHashMap()).build();
    }
}
