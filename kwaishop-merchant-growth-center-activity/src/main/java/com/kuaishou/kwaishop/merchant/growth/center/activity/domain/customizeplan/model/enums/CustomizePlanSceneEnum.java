package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Getter
@AllArgsConstructor
public enum CustomizePlanSceneEnum {
    CUSTOMIZE_PLAN("customizePlan", "查询定制计划"),
    BANNER("banner", "查询定制计划banner"),
    UNKNOWN("unknown", "未知")
    ;

    public static CustomizePlanSceneEnum of(String scene) {
        for (CustomizePlanSceneEnum val : CustomizePlanSceneEnum.values()) {
            if (val.getScene().equals(scene)) {
                return val;
            }
        }
        return UNKNOWN;
    }

    /**
     * 定制计划查询场景
     */
    private final String scene;

    /**
     * 描述
     */
    private final String desc;
}
