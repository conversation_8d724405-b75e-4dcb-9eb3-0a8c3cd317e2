package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.AwardResolver.multiAwardConfigContainsAwardTrReturnType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.validate.AdminTemplateValidator.mergeMultiSet;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.RoiConvert.buildStepDimensionData;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.RoiConvert.convertBaseInfo;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.roiCalcCrowdPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkNumberNotNull;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.IndustryActivityBuildService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.AuditRoiExecuteConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.AuditRoiBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiLevelAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRoiService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.InterestFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.DistributorDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.BaseInfo;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerSingleDimensionData;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-15
 */
@Service
@Slf4j
@Lazy
public class AuditRoiExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private InterestFetchService interestFetchService;

    @Autowired
    private StatisticsRoiService statisticsRoiService;

    @Autowired
    private StatisticsCacheService statisticsCacheService;

    @Autowired
    private StatisticsMsgProduceService msgProduceService;

    @Autowired
    private AdminActivityOnlineService adminActivityOnlineService;

    @Autowired
    private IndustryActivityBuildService industryActivityBuildService;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        // 事件强转
        AuditRoiBatchExecuteEvent auditRoiEvent = (AuditRoiBatchExecuteEvent) event;
        checkNumberNotNull(auditRoiEvent.getActivityId(), "提审ROI需要活动ID");
        String eventId = auditRoiEvent.getEventId();
        // 分层配置信息
        List<RoiActivityLevelConfigBO> activityLayerRoiInfo = auditRoiEvent.getActivityLayerRoiInfo();
        List<TaskDO> parentTaskList = activityLayerRoiInfo.stream()
                .map(RoiActivityLevelConfigBO::getLayerParentTask).collect(Collectors.toList());
        // 活动&各分层人群构建
        Map<Long, Set<Long>> layerTaskCrowd = parentTaskList.stream().collect(Collectors.toMap(BaseDO::getId,
                e -> industryActivityBuildService.getSellerByCrowdTypeAndAb(e.getCrowdType(), e.getCrowdConfig())));
        // 融合放置分层人群信息
        auditRoiEvent.setLayerTaskCrowd(layerTaskCrowd);
        Set<Long> activityCrowd = mergeMultiSet(layerTaskCrowd.values());
        // 分层缓存构建
        activityLayerRoiInfo.forEach(e -> statisticsRoiService.buildRoiLayerCache(e, eventId));
        return activityCrowd;
    }

    @Override
    protected String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 事件强转
        AuditRoiBatchExecuteEvent auditRoiEvent = (AuditRoiBatchExecuteEvent) event;
        // 用户可参与父任务判断
        Map<Long, Set<Long>> layerTaskCrowd = auditRoiEvent.getLayerTaskCrowd();
        Map<Long, List<Long>> userCanDrawParentTaskMap =
                adminActivityOnlineService.buildUserCanDrawParentMap(layerTaskCrowd, userIdList);
        // 数据组装
        AuditRoiExecuteConfigBO executeConfig = new AuditRoiExecuteConfigBO();
        executeConfig.setActivityId(auditRoiEvent.getActivityId());
        executeConfig.setUserCanDrawParentTaskMap(userCanDrawParentTaskMap);
        executeConfig.setActivityLayerRoiInfo(auditRoiEvent.getActivityLayerRoiInfo());
        return toJSON(executeConfig);
    }

    @Override
    public ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        AuditRoiExecuteConfigBO executeConfigBO = fromJSON(executeConfig, AuditRoiExecuteConfigBO.class);
        ExecuteHandleResult result = new ExecuteHandleResult();
        boolean stop = interestFetchService.queryRoiCalcIsStop(eventId);
        if (stop) {
            log.info("[提审ROI计算] 计算已被终止！eventId:{}, userIdList:{}", eventId, userIdList);
            return result;
        }
        // 总人数
        long totalNum = statisticsCacheService.getBatchExecuteTotalNum(eventId);
        // 各分层，所有用户系统基值<LayerParentTaskId : <UserId : JsonData>>
        Map<Long, Map<Long, Map<String, Object>>> layerUserSystemCalcBasicData =
                getLayerUserSystemCalcBasicData(userIdList, executeConfigBO);
        // 如果奖励返点涉及按TR增量返点，需要额外查询TR和退款率
        Map<Long, Map<Long, DistributorDataBO>> layerUserTrReturnBaseData =
                getLayerUserTrReturnBasicData(userIdList, executeConfigBO);
        // 对于每个用户
        userIdList.forEach(userId -> singleUserExecute(userId, eventId, totalNum, executeConfigBO,
                layerUserSystemCalcBasicData, layerUserTrReturnBaseData));
        // 处理结果构建
        result.setSuccessUserList(userIdList);
        return result;
    }

    private Map<Long, Map<Long, DistributorDataBO>> getLayerUserTrReturnBasicData(List<Long> userIdList,
            AuditRoiExecuteConfigBO executeConfigBO) {
        // 如果奖励返点涉及按TR增量返点，需要额外查询TR和退款率
        Map<Long, Map<Long, DistributorDataBO>> layerUserTrReturnBaseData = Maps.newHashMap();
        for (RoiActivityLevelConfigBO levelConfigBO : executeConfigBO.getActivityLayerRoiInfo()) {
            // 分层父任务ID
            Long layerParentId = levelConfigBO.getLayerParentTask().getId();
            // 用户过滤
            List<Long> layerUserList = userIdList.stream().filter(userId -> {
                Map<Long, List<Long>> userCanDrawParentTaskMap = executeConfigBO.getUserCanDrawParentTaskMap();
                return userCanDrawParentTaskMap.get(userId).contains(layerParentId);
            }).collect(Collectors.toList());
            // 各用户该分层基值
            Map<Long, DistributorDataBO> userTrReturnBaseDataMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(layerUserList)) {
                // TR增量返点基值
                if (multiAwardConfigContainsAwardTrReturnType(levelConfigBO.getRoiAwardConfigs())) {
                    userTrReturnBaseDataMap =
                            statisticsRoiService.batchGetLayerUserTrReturnBaseValue(userIdList, levelConfigBO);
                }
            }
            layerUserTrReturnBaseData.put(layerParentId, userTrReturnBaseDataMap);
        }
        if (EnvUtils.isStaging()) {
            log.info("[查询TR和退款率] {}", layerUserTrReturnBaseData);
        }
        return layerUserTrReturnBaseData;
    }

    private Map<Long, Map<Long, Map<String, Object>>> getLayerUserSystemCalcBasicData(List<Long> userIdList,
            AuditRoiExecuteConfigBO executeConfigBO) {
        // 各分层，所有用户系统基值
        Map<Long, Map<Long, Map<String, Object>>> layerUserSystemCalcBasicData = Maps.newHashMap();
        for (RoiActivityLevelConfigBO levelConfigBO : executeConfigBO.getActivityLayerRoiInfo()) {
            // 分层父任务ID
            Long layerParentId = levelConfigBO.getLayerParentTask().getId();
            // 用户过滤
            List<Long> layerUserList = userIdList.stream().filter(userId -> {
                Map<Long, List<Long>> userCanDrawParentTaskMap = executeConfigBO.getUserCanDrawParentTaskMap();
                return userCanDrawParentTaskMap.get(userId).contains(layerParentId);
            }).collect(Collectors.toList());
            // 各用户该分层基值
            Map<Long, Map<String, Object>> userSystemCalcBaseMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(layerUserList)) {
                // 批量查询基值
                userSystemCalcBaseMap =
                        statisticsRoiService.batchGetLayerUserBaseValue(userIdList, levelConfigBO);
            }
            layerUserSystemCalcBasicData.put(layerParentId, userSystemCalcBaseMap);
        }
        return layerUserSystemCalcBasicData;
    }

    /**
     * 单用户处理逻辑
     */
    private void singleUserExecute(Long userId, String eventId, long totalNum, AuditRoiExecuteConfigBO executeConfigBO,
            Map<Long, Map<Long, Map<String, Object>>> layerUserSystemCalcBasicData,
            Map<Long, Map<Long, DistributorDataBO>> layerUserTrReturnBaseData) {
        // 取出用户对应的分层信息
        Map<Long, List<Long>> userCanDrawParentTaskMap = executeConfigBO.getUserCanDrawParentTaskMap();
        List<Long> userCanDrawParentList = userCanDrawParentTaskMap.get(userId);
        // 取出分层信息
        List<RoiActivityLevelConfigBO> levelConfigList = executeConfigBO.getActivityLayerRoiInfo().stream()
                .filter(e -> userCanDrawParentList.contains(e.getLayerParentTask().getId()))
                .collect(Collectors.toList());
        // 用户各分层信息
        List<UserRoiLevelAggregationInfoBO> userRoiLevelAggregationInfoList = levelConfigList.stream()
                .map(config -> statisticsRoiService.getUserRoiLevelAggregationInfo(userId, eventId, config,
                        layerUserSystemCalcBasicData.get(config.getLayerParentTask().getId()).get(userId),
                        layerUserTrReturnBaseData.get(config.getLayerParentTask().getId()).get(userId)))
                .collect(Collectors.toList());
        // 对象转换发送消息
        List<SellerSingleDimensionData> singleDimensionData =
                buildSellerSingleDimensionData(userRoiLevelAggregationInfoList);
        msgProduceService.sendUserRoiCalcMsg(userId, eventId, totalNum, singleDimensionData);
    }

    /**
     * 分层数据转换
     */
    private List<SellerSingleDimensionData> buildSellerSingleDimensionData(
            List<UserRoiLevelAggregationInfoBO> userRoiLevelAggregationInfoList) {
        List<SellerSingleDimensionData> res = Lists.newArrayList();
        // 各分层
        for (UserRoiLevelAggregationInfoBO levelInfo : userRoiLevelAggregationInfoList) {
            // 分层基值配置
            BaseInfo layerBasicConfig = convertBaseInfo(levelInfo.getBasicConfig());
            // 发奖单信息
            long activityId = levelInfo.getActivityId();
            long layerParentId = levelInfo.getLayerParentId();
            String ruleBizId = String.format("%s_%s_%s", activityId, layerParentId, 1);
            // 各阶段
            levelInfo.getUserRoiStepAggregationInfos().forEach(stepInfo -> res
                    .add(buildStepDimensionData(layerBasicConfig, ruleBizId, stepInfo, levelInfo)));
        }
        return res;
    }

    @Override
    protected int getPartitionSize() {
        return roiCalcCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.AUDIT_ROI;
    }
}
