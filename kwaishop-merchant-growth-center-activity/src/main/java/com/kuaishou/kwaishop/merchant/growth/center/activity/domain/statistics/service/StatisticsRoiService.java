package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.RoiAccessResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiLevelAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiStepAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRoiTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.DistributorDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.aggregation.UserAwardRoiDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-14
 */
public interface StatisticsRoiService {

    /**
     * 获取奖励ROI聚合信息
     */
    UserAwardRoiDTO getUserAwardRoiInfo(long userId, String awardUniqueId);

    /**
     * 处理即时ROI计算请求
     */
    void handleImmediateRoiCalc(String protoCol, String eventId, String operator);

    /**
     * 即时ROI计算请求准入
     */
    RoiAccessResultBO immediateRoiCalcAccess(String protoCol, String eventId, String operator);

    /**
     * 处理提审ROI计算的请求
     */
    void handleAuditRoiCalc(long resourceActivityId, String eventId, String operator, String param);

    /**
     * 提审ROI计算请求准入
     */
    RoiAccessResultBO auditRoiCalcAccess(long resourceActivityId, long innerActivityId, String eventId, String operator);

    /**
     * 判断一个活动是否可以计算ROI
     */
    RoiAccessResultBO auditRoiCalcAccessWithActivityId(long activityId);

    /**
     * 获取ROI计算需要的活动元数据，以分层维度进行切分
     */
    List<RoiActivityLevelConfigBO> getRoiActivityConfig(long activityId);

    /**
     * 获取一个分层下批量用户的基值数据
     */
    Map<Long, Map<String, Object>> batchGetLayerUserBaseValue(List<Long> userIdList,
            RoiActivityLevelConfigBO roiActivityLevelConfig);

    /**
     * 获取一个分层下批量用户的Tr增量返点基值数据
     */
    Map<Long, DistributorDataBO> batchGetLayerUserTrReturnBaseValue(List<Long> userIdList,
            RoiActivityLevelConfigBO roiActivityLevelConfig);

    /**
     * 计算一个分层下单用户的ROI聚合信息
     *
     * @param roiActivityLevelConfig roi计算所需的活动分层配置
     */
    UserRoiLevelAggregationInfoBO getUserRoiLevelAggregationInfo(long userId, String eventId,
            RoiActivityLevelConfigBO roiActivityLevelConfig, Map<String, Object> userSystemCalcBasicData,
            DistributorDataBO userDistributorData);

    /**
     * 获取多阶段任务的各个阶段目标奖励信息
     */
    List<UserRoiStepAggregationInfoBO> getMultiStageAggregationStepInfo(long userId,
            Map<String, Object> baseDataMap, RoiActivityLevelConfigBO roiActivityLevelConfig);

    /**
     * 获取单阶梯任务的各个档位目标奖励信息
     */
    List<UserRoiStepAggregationInfoBO> getSinglePhaseAggregationStepInfo(long userId,
            Map<String, Object> baseDataMap, RoiActivityLevelConfigBO roiActivityLevelConfig);

    /**
     * 计算单商家在某阶段/层级ROI目标情况
     *
     * @param targetValue 目标值
     * @param indicatorDO 指标元数据
     * @param baseData 分层基值
     */
    RoiIndicatorInfoBO buildRoiIndicatorInfo(Long userId, long targetValue, IndicatorDO indicatorDO,
            Map<String, Object> baseData);

    /**
     * 计算单商家在某阶段/层级ROI奖励情况
     *
     * @param awardConfigs 分层Roi相关奖励配置（非流量/勋章奖励）
     * @param indicatorInfos 分层Roi相关指标达成信息
     * @param baseData 分层基值
     * @param reachLevel 第几层级
     */
    List<RoiAwardInfoBO> getRoiForecastAwardValue(long userId, List<AwardConfigDO> awardConfigs,
            List<RoiIndicatorInfoBO> indicatorInfos, Map<String, Object> baseData, int reachLevel);

    /**
     * 获取ROI固定值基值信息
     */
    Map<String, Object> getRoiBaseFixValue(long userId, String eventId, TaskDO layerParentTask, String activityPattern);

    /**
     * 获取ROI基值指标数据信息
     */
    Map<String, Object> getRoiBaseIndicatorValue(long userId, String eventId, TaskDO layerParentTask,
            List<IndicatorConfigDO> baseIndicatorConfigs, Map<String, Object> userSystemCalcBasicData,
            DistributorDataBO userDistributorData);

    /**
     * 构建某分层ROI缓存
     */
    void buildRoiLayerCache(RoiActivityLevelConfigBO levelConfig, String eventId);


    /**
     * 获取ROI准入结果
     */
    boolean getRoiAccessWithCache(long activityId);

    /**
     * 获取涨粉活动各种roi计算类型所需的单新粉LTV90
     * roi计算公式:(活动期目标涨粉数-基期日均新粉*活动期天数)*单新粉LTV90*TR结算率/奖励金额
     * 即时/申报/预估roi:使用基期单新粉LTV90
     * 发奖roi:(1)返点类:使用奖励记录中保存的活动期预估单新粉LTV90 (2)固定值:使用实时查询的
     * 复盘roi:使用实时查询的活动期预估单新粉LTV90
     */
    Long getRoiCalSingleFanLtv90(StatisticsRoiTypeEnum roiTypeEnum, Long userId, UserAwardRecordDO userAwardRecordDO,
            Map<String, Object> extMap);

}
