package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.converter;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorResolver.getBaseFormulaResultType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.convert.ConverterUtil.convertNotNull;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.utils.IndicatorUnitUtils.convertToShowValueFromLong;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.utils.IndicatorUnitUtils.convertToStorageValueFromDouble;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.BasicConfigConstants.BASIC_FACTOR_CODE_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.getDaysOfMillionSecondInterval;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.getMillionSecondsOfDayInterval;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.hourToMillSeconds;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.millSecondsToHour;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.kuaishou.infra.boot.context.ApplicationContextHolder;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicCustomConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicSystemCalcConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.HiveImportConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.ActivityBaseAlgorithmEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.BasicCalcTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.BasicValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndicatorTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelBasicFactorConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelBasicFactorConfigDetail;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelCustomBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelSingleBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityLevelSystemCalcBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums.BaseFormulaResultTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.BasicFactorConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.BasicFactorConfigDetail;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.CustomBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.HiveExtraColumn;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.HiveImportConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.SingleBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.SystemCalcBasicConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.baseindicator.SystemCalcBasicConfig.Builder;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-01
 */
public class BasicConfigConverter {
    /**
     * 构造前端基期字段
     */
    public static SubActivityLevelBasicConfig convert2SubActivityLevelBasicConfig(BasicConfigBO basicConfigBO) {
        if (null == basicConfigBO) {
            return null;
        }
        if (CollectionUtils.isEmpty(basicConfigBO.getBasicIndicatorConfigList())) {
            return null;
        }
        SubActivityLevelBasicConfig subActivityLevelBasicConfig = new SubActivityLevelBasicConfig();
        List<SubActivityLevelSingleBasicConfig> subActivityLevelSingleBasicConfigs =
                basicConfigBO.getBasicIndicatorConfigList().stream().map(basicIndicatorConfigBO -> {
                    SubActivityLevelSingleBasicConfig subActivityLevelSingleBasicConfig =
                            new SubActivityLevelSingleBasicConfig();
                    subActivityLevelSingleBasicConfig.setIndicatorId(basicIndicatorConfigBO.getIndicatorId());
                    subActivityLevelSingleBasicConfig.setBasicValueType(
                            basicIndicatorConfigBO.getBasicValueType().getType());
                    subActivityLevelSingleBasicConfig.setSystemCalcBasicConfig(
                            buildSubActivityLevelSystemCalcBasicConfig(basicIndicatorConfigBO));
                    subActivityLevelSingleBasicConfig.setCustomizeBasicConfig(
                            buildSubActivityLevelCustomBasicConfig(basicIndicatorConfigBO));
                    return subActivityLevelSingleBasicConfig;
                }).collect(Collectors.toList());
        subActivityLevelBasicConfig.setSingleIndicatorBasicConfigList(subActivityLevelSingleBasicConfigs);

        // 老模型兼容
        fillHistorySubActivityLevelBasicConfig(subActivityLevelBasicConfig);

        return subActivityLevelBasicConfig;
    }

    /**
     * 构造前端基期字段
     * 人工上传
     */
    private static SubActivityLevelCustomBasicConfig buildSubActivityLevelCustomBasicConfig(
            BasicIndicatorConfigBO basicIndicatorConfigBO) {
        BasicCustomConfigBO basicCustomConfig = basicIndicatorConfigBO.getBasicCustomConfig();
        if (null == basicCustomConfig) {
            return null;
        }
        SubActivityLevelCustomBasicConfig subActivityLevelCustomBasicConfig = new SubActivityLevelCustomBasicConfig();
        BasicFactorConfigBO customizeBasicConfig = basicCustomConfig.getCustomizeBasicConfig();
        SubActivityLevelBasicFactorConfigDetail subActivityLevelBasicFactorConfigDetail =
                SubActivityLevelBasicFactorConfigDetail.builder()
                        .indicatorTimeType(customizeBasicConfig.getIndicatorTimeType().getType())
                        .customizeType(customizeBasicConfig.getCustomizeType())
                        .baseAlgorithmCustomizeUrl(customizeBasicConfig.getBaseAlgorithmCustomizeUrl())
                        .hiveImportConfig(customizeBasicConfig.getHiveImportConfig())
                        .fixedStartTime(customizeBasicConfig.getFixedStartTime())
                        .fixedEndTime(customizeBasicConfig.getFixedEndTime())
                        .build();
        String baseAlgorithm = getBasicConfigAlgorithm(basicIndicatorConfigBO.getIndicatorId(), customizeBasicConfig);
        SubActivityLevelBasicFactorConfig subActivityLevelBasicFactorConfig =
                SubActivityLevelBasicFactorConfig.builder()
                        .baseAlgorithm(baseAlgorithm)
                        .factorConfig(subActivityLevelBasicFactorConfigDetail)
                        .build();
        subActivityLevelCustomBasicConfig.setCustomizeBasicConfig(subActivityLevelBasicFactorConfig);
        return subActivityLevelCustomBasicConfig;
    }

    /**
     * 构造前端基期字段
     * 系统计算
     */
    private static SubActivityLevelSystemCalcBasicConfig buildSubActivityLevelSystemCalcBasicConfig(
            BasicIndicatorConfigBO basicIndicatorConfigBO) {
        BasicSystemCalcConfigBO basicSystemCalcConfig = basicIndicatorConfigBO.getBasicSystemCalcConfig();
        if (null == basicSystemCalcConfig) {
            return null;
        }
        SubActivityLevelSystemCalcBasicConfig subActivityLevelSystemCalcBasicConfig =
                new SubActivityLevelSystemCalcBasicConfig();
        List<SubActivityLevelBasicFactorConfig> basicFactorConfigList =
                basicSystemCalcConfig.getBasicFactorConfigList().stream().map((basicFactorConfigBO) -> {
                    SubActivityLevelBasicFactorConfig subActivityLevelBasicFactorConfig =
                            new SubActivityLevelBasicFactorConfig();
                    SubActivityLevelBasicFactorConfigDetail subActivityLevelBasicFactorConfigDetail =
                            new SubActivityLevelBasicFactorConfigDetail();
                    subActivityLevelBasicFactorConfigDetail.setIndicatorTimeType(
                            basicFactorConfigBO.getIndicatorTimeType().getType());
                    subActivityLevelBasicFactorConfigDetail.setFixedStartTime(basicFactorConfigBO.getFixedStartTime());
                    subActivityLevelBasicFactorConfigDetail.setFixedEndTime(basicFactorConfigBO.getFixedEndTime());
                    subActivityLevelBasicFactorConfigDetail.setOffsetEventType(
                            basicFactorConfigBO.getOffsetEventType());
                    subActivityLevelBasicFactorConfigDetail.setRelativeDay(
                            Optional.ofNullable(basicFactorConfigBO.getRelativeTime())
                                    .map(relativeTime -> {
                                        long days = LocalDateUtil.getDaysOfMillionSecondInterval(relativeTime);
                                        return Long.valueOf(days).intValue();
                                    }).orElse(null));
                    subActivityLevelBasicFactorConfigDetail.setPeriodDay(
                            Optional.ofNullable(basicFactorConfigBO.getPeriodTime())
                                    .map(periodTime -> {
                                        long days = LocalDateUtil.getDaysOfMillionSecondInterval(periodTime);
                                        return Long.valueOf(days).intValue();
                                    }).orElse(null));
                    subActivityLevelBasicFactorConfigDetail.setCoefficient(
                            null != basicFactorConfigBO.getCoefficient() ? basicFactorConfigBO.getCoefficient()
                                    .toPlainString() : null);
                    subActivityLevelBasicFactorConfigDetail.setExclusiveFestivalDays(
                            basicFactorConfigBO.isExclusiveFestivalDays());
                    subActivityLevelBasicFactorConfigDetail.setExclusiveMaxMinDays(
                            basicFactorConfigBO.isExclusiveMaxMinDays());
                    subActivityLevelBasicFactorConfigDetail.setExclusiveInvalidLiveDays(
                            basicFactorConfigBO.isExclusiveInvalidLiveDays());
                    if (basicFactorConfigBO.isExclusiveInvalidLiveDays()) {
                        subActivityLevelBasicFactorConfigDetail.setValidLiveTime(
                                String.valueOf(millSecondsToHour(basicFactorConfigBO.getValidLiveTime())));
                    }
                    subActivityLevelBasicFactorConfigDetail
                            .setCustomBaseCalcRuleCode(basicFactorConfigBO.getCustomBaseCalcRuleCode());
                    subActivityLevelBasicFactorConfig.setFactorConfig(subActivityLevelBasicFactorConfigDetail);
                    subActivityLevelBasicFactorConfig.setBaseAlgorithm(
                            basicFactorConfigBO.getBaseAlgorithm().getType());
                    return subActivityLevelBasicFactorConfig;
                }).collect(Collectors.toList());
        subActivityLevelSystemCalcBasicConfig.setBasicFactorConfigList(basicFactorConfigList);
        subActivityLevelSystemCalcBasicConfig.setBasicCalcType(
                basicSystemCalcConfig.getBasicCalcType() != null ? basicSystemCalcConfig.getBasicCalcType().name()
                                                                 : null);
        IndicatorLocalCacheService indicatorLocalCacheService = ApplicationContextHolder.getBean(
                IndicatorLocalCacheService.class);
        IndicatorDO indicatorDO =
                indicatorLocalCacheService.queryTaskIndicator(basicIndicatorConfigBO.getIndicatorId());
        Preconditions.checkNotNull(indicatorDO, "指标元信息不存在");
        if (null != basicSystemCalcConfig.getFixedMaxValue()) {
            subActivityLevelSystemCalcBasicConfig.setFixedMaxValue(
                    convertToShowValueFromLong(basicSystemCalcConfig.getFixedMaxValue(), indicatorDO.getUnit()));
        }
        if (null != basicSystemCalcConfig.getFixedMinValue()) {
            subActivityLevelSystemCalcBasicConfig.setFixedMinValue(
                    convertToShowValueFromLong(basicSystemCalcConfig.getFixedMinValue(), indicatorDO.getUnit()));
        }
        return subActivityLevelSystemCalcBasicConfig;
    }

    /**
     * 构造服务端基期模型
     */
    public static BasicConfigBO convert2BasicConfigBO(SubActivityLevelBasicConfig basicConfig) {
        if (null == basicConfig) {
            return null;
        }
        BasicConfigBO basicConfigBO = new BasicConfigBO();
        List<SubActivityLevelSingleBasicConfig> singleIndicatorBasicConfigList =
                basicConfig.getSingleIndicatorBasicConfigList();
        List<BasicIndicatorConfigBO> basicIndicatorConfigBOS =
                singleIndicatorBasicConfigList.stream().map(singleIndicatorBasicConfig -> {
                    BasicIndicatorConfigBO basicIndicatorConfigBO = new BasicIndicatorConfigBO();
                    basicIndicatorConfigBO.setIndicatorId(singleIndicatorBasicConfig.getIndicatorId());
                    basicIndicatorConfigBO.setBasicValueType(
                            BasicValueTypeEnum.getByType(singleIndicatorBasicConfig.getBasicValueType()));
                    // 自定义上传
                    basicIndicatorConfigBO.setBasicCustomConfig(buildBasicCustomConfigBO(singleIndicatorBasicConfig));
                    // 系统计算
                    basicIndicatorConfigBO.setBasicSystemCalcConfig(
                            buildBasicSystemCalcConfigBO(singleIndicatorBasicConfig));
                    return basicIndicatorConfigBO;
                }).collect(Collectors.toList());
        basicConfigBO.setBasicIndicatorConfigList(basicIndicatorConfigBOS);
        return basicConfigBO;
    }

    /**
     * 构造服务端基期模型
     * 人工上传
     */
    private static BasicCustomConfigBO buildBasicCustomConfigBO(
            SubActivityLevelSingleBasicConfig subActivityLevelSingleBasicConfig) {
        if (null == subActivityLevelSingleBasicConfig.getCustomizeBasicConfig()
                || null == subActivityLevelSingleBasicConfig.getCustomizeBasicConfig().getCustomizeBasicConfig()) {
            return null;
        }
        BasicFactorConfigBO basicFactorConfigBO = new BasicFactorConfigBO();
        SubActivityLevelBasicFactorConfig customizeBasicConfig =
                subActivityLevelSingleBasicConfig.getCustomizeBasicConfig().getCustomizeBasicConfig();
        SubActivityLevelBasicFactorConfigDetail factorConfig = customizeBasicConfig.getFactorConfig();
        ActivityBaseAlgorithmEnum activityBaseAlgorithmEnum = ActivityBaseAlgorithmEnum.getByType(customizeBasicConfig.getBaseAlgorithm());
        basicFactorConfigBO.setBaseAlgorithm(activityBaseAlgorithmEnum);
        basicFactorConfigBO.setIndicatorTimeType(IndicatorTimeTypeEnum.CUSTOM_TIME);
        basicFactorConfigBO.setBaseAlgorithmCustomizeUrl(factorConfig.getBaseAlgorithmCustomizeUrl());
        basicFactorConfigBO.setCustomizeType(factorConfig.getCustomizeType());
        basicFactorConfigBO.setHiveImportConfig(factorConfig.getHiveImportConfig());
        basicFactorConfigBO.setFixedStartTime(factorConfig.getFixedStartTime());
        basicFactorConfigBO.setFixedEndTime(factorConfig.getFixedEndTime());
        return BasicCustomConfigBO.builder().customizeBasicConfig(basicFactorConfigBO).build();
    }

    /**
     * 构造服务端基期模型
     * 系统计算
     */
    private static BasicSystemCalcConfigBO buildBasicSystemCalcConfigBO(
            SubActivityLevelSingleBasicConfig subActivityLevelSingleBasicConfig) {
        if (null == subActivityLevelSingleBasicConfig.getSystemCalcBasicConfig() || CollectionUtils.isEmpty(
                subActivityLevelSingleBasicConfig.getSystemCalcBasicConfig().getBasicFactorConfigList())) {
            return null;
        }
        BasicSystemCalcConfigBO basicSystemCalcConfigBO = new BasicSystemCalcConfigBO();
        SubActivityLevelSystemCalcBasicConfig systemCalcBasicConfig =
                subActivityLevelSingleBasicConfig.getSystemCalcBasicConfig();
        List<SubActivityLevelBasicFactorConfig> basicFactorConfigList =
                systemCalcBasicConfig.getBasicFactorConfigList();
        List<BasicFactorConfigBO> basicFactorConfigBOList = Lists.newArrayList();
        for (int i = 0; i < basicFactorConfigList.size(); i++) {
            SubActivityLevelBasicFactorConfig subActivityLevelBasicFactorConfig = basicFactorConfigList.get(i);
            SubActivityLevelBasicFactorConfigDetail factorConfig = subActivityLevelBasicFactorConfig.getFactorConfig();
            BasicFactorConfigBO basicFactorConfigBO = new BasicFactorConfigBO();
            basicFactorConfigBO.setFactorCode(String.format(BASIC_FACTOR_CODE_PREFIX, i + 1));
            ActivityBaseAlgorithmEnum baseAlgorithmEnum =
                    ActivityBaseAlgorithmEnum.getByType(subActivityLevelBasicFactorConfig.getBaseAlgorithm());
            basicFactorConfigBO.setBaseAlgorithm(baseAlgorithmEnum);
            IndicatorTimeTypeEnum indicatorTimeTypeEnum =
                    IndicatorTimeTypeEnum.getByType(factorConfig.getIndicatorTimeType());
            basicFactorConfigBO.setIndicatorTimeType(indicatorTimeTypeEnum);
            basicFactorConfigBO.setFixedStartTime(factorConfig.getFixedStartTime());
            basicFactorConfigBO.setFixedEndTime(factorConfig.getFixedEndTime());
            if (indicatorTimeTypeEnum == IndicatorTimeTypeEnum.RELATIVE_TIME) {
                basicFactorConfigBO.setOffsetEventType(factorConfig.getOffsetEventType());
                basicFactorConfigBO.setPeriodTime(
                        getMillionSecondsOfDayInterval(factorConfig.getPeriodDay()));
                basicFactorConfigBO.setRelativeTime(
                        getMillionSecondsOfDayInterval(factorConfig.getRelativeDay()));
            }
            if (StringUtils.isNotBlank(factorConfig.getCoefficient())) {
                basicFactorConfigBO.setCoefficient(new BigDecimal(factorConfig.getCoefficient()));
            }
            basicFactorConfigBO.setExclusiveFestivalDays(factorConfig.isExclusiveFestivalDays());
            basicFactorConfigBO.setExclusiveMaxMinDays(factorConfig.isExclusiveMaxMinDays());
            basicFactorConfigBO.setExclusiveInvalidLiveDays(factorConfig.isExclusiveInvalidLiveDays());
            if (factorConfig.isExclusiveInvalidLiveDays()) {
                double validLiveHour =
                        new BigDecimal(factorConfig.getValidLiveTime()).setScale(1, RoundingMode.UP).doubleValue();
                basicFactorConfigBO.setValidLiveTime(hourToMillSeconds(validLiveHour));
            }
            if (StringUtils.isNotBlank(factorConfig.getCustomBaseCalcRuleCode())) {
                basicFactorConfigBO.setCustomBaseCalcRuleCode(factorConfig.getCustomBaseCalcRuleCode());
            }
            basicFactorConfigBOList.add(basicFactorConfigBO);
        }
        if (StringUtils.isNotBlank(systemCalcBasicConfig.getBasicCalcType())) {
            BasicCalcTypeEnum basicCalcTypeEnum = BasicCalcTypeEnum.valueOf(systemCalcBasicConfig.getBasicCalcType());
            basicSystemCalcConfigBO.setBasicCalcType(basicCalcTypeEnum);
        }
        IndicatorLocalCacheService indicatorLocalCacheService = ApplicationContextHolder.getBean(
                IndicatorLocalCacheService.class);
        IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(
                subActivityLevelSingleBasicConfig.getIndicatorId());
        Preconditions.checkNotNull(indicatorDO, "指标元信息不存在");
        if (StringUtils.isNotBlank(systemCalcBasicConfig.getFixedMaxValue())) {
            basicSystemCalcConfigBO.setFixedMaxValue(
                    Long.parseLong(convertToStorageValueFromDouble(systemCalcBasicConfig.getFixedMaxValue(), indicatorDO.getUnit(), null)));
        }
        if (StringUtils.isNotBlank(systemCalcBasicConfig.getFixedMinValue())) {
            basicSystemCalcConfigBO.setFixedMinValue(
                    Long.parseLong(convertToStorageValueFromDouble(systemCalcBasicConfig.getFixedMinValue(), indicatorDO.getUnit(), null)));
        }

        basicSystemCalcConfigBO.setBasicFactorConfigList(basicFactorConfigBOList);
        return basicSystemCalcConfigBO;
    }

    /**
     * 构造一个前端1.0模型的基期
     * 因为排行榜展示还使用老的前端模型
     * 后续等排行榜迁移后，本方法可删除 todo js 待删除
     */
    private static void fillHistorySubActivityLevelBasicConfig(
            SubActivityLevelBasicConfig subActivityLevelBasicConfig) {
        if (null == subActivityLevelBasicConfig) {
            return;
        }
        SubActivityLevelSingleBasicConfig subActivityLevelSingleBasicConfig =
                CollectionUtils.emptyIfNull(subActivityLevelBasicConfig.getSingleIndicatorBasicConfigList()).stream()
                        .findFirst()
                        .orElse(null);
        if (null == subActivityLevelSingleBasicConfig) {
            return;
        }
        List<Long> indicatorIdList = subActivityLevelBasicConfig.getSingleIndicatorBasicConfigList().stream()
                .map(SubActivityLevelSingleBasicConfig::getIndicatorId).collect(
                        Collectors.toList());
        subActivityLevelBasicConfig.setBaseIndicatorList(indicatorIdList);

        String basicValueType = subActivityLevelSingleBasicConfig.getBasicValueType();
        SubActivityLevelBasicFactorConfig basicFactorConfigBO;
        if (BasicValueTypeEnum.CUSTOMIZE.getType().equals(basicValueType)) {
            basicFactorConfigBO = subActivityLevelSingleBasicConfig.getCustomizeBasicConfig().getCustomizeBasicConfig();
        } else {
            basicFactorConfigBO =
                    subActivityLevelSingleBasicConfig.getSystemCalcBasicConfig().getBasicFactorConfigList().stream()
                            .findFirst().orElseThrow(() -> new BizException(
                                    BasicErrorCode.SERVER_ERROR));
        }
        SubActivityLevelBasicFactorConfigDetail factorConfig = basicFactorConfigBO.getFactorConfig();
        subActivityLevelBasicConfig.setIndicatorTimeType(factorConfig.getIndicatorTimeType());
        subActivityLevelBasicConfig.setFixedStartTime(factorConfig.getFixedStartTime());
        subActivityLevelBasicConfig.setFixedEndTime(factorConfig.getFixedEndTime());
        subActivityLevelBasicConfig.setBaseAlgorithm(basicFactorConfigBO.getBaseAlgorithm());
        subActivityLevelBasicConfig.setBaseAlgorithmCustomizeUrl(factorConfig.getBaseAlgorithmCustomizeUrl());
        subActivityLevelBasicConfig.setCustomizeType(factorConfig.getCustomizeType());
        subActivityLevelBasicConfig.setHiveImportConfig(factorConfig.getHiveImportConfig());
        subActivityLevelBasicConfig.setOffsetEventType(factorConfig.getOffsetEventType());
        subActivityLevelBasicConfig.setRelativeDay(factorConfig.getRelativeDay());
        subActivityLevelBasicConfig.setPeriodDay(factorConfig.getPeriodDay());
    }

    public static List<SingleBasicConfig> convert2SingleBasicConfigsProto(BasicConfigBO basicConfigBO) {
        if (null == basicConfigBO) {
            return Lists.newArrayList();
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        if (CollectionUtils.isEmpty(basicIndicatorConfigList)) {
            return Lists.newArrayList();
        }
        return basicIndicatorConfigList.stream().map(basicIndicatorConfigBO -> SingleBasicConfig.newBuilder()
                .setIndicatorId(basicIndicatorConfigBO.getIndicatorId())
                .setBasicValueType(basicIndicatorConfigBO.getBasicValueType() == null ? ""
                        : basicIndicatorConfigBO.getBasicValueType().getType())
                .setCustomizeBasicConfig(convert2CustomBasicConfig(basicIndicatorConfigBO.getIndicatorId(),
                        basicIndicatorConfigBO.getBasicCustomConfig()))
                .setSystemCalcBasicConfig(convert2SystemCalcBasicConfig(basicIndicatorConfigBO.getIndicatorId(),
                        basicIndicatorConfigBO.getBasicSystemCalcConfig()))
                .build()).collect(Collectors.toList());
    }

    private static CustomBasicConfig convert2CustomBasicConfig(Long indicatorId, BasicCustomConfigBO basicCustomConfigBO) {
        if (null == basicCustomConfigBO) {
            return CustomBasicConfig.newBuilder().build();
        }
        BasicFactorConfigBO customizeBasicConfig = basicCustomConfigBO.getCustomizeBasicConfig();
        return CustomBasicConfig.newBuilder()
                .setCustomizeBasicConfig(convert2BasicFactorConfig(indicatorId, customizeBasicConfig))
                .build();
    }

    private static SystemCalcBasicConfig convert2SystemCalcBasicConfig(Long indicatorId,
            BasicSystemCalcConfigBO basicSystemCalcConfigBO) {
        if (null == basicSystemCalcConfigBO) {
            return SystemCalcBasicConfig.newBuilder().build();
        }
        List<BasicFactorConfig> basicFactorConfigs = basicSystemCalcConfigBO.getBasicFactorConfigList().stream()
                .map(e -> convert2BasicFactorConfig(indicatorId, e))
                .collect(Collectors.toList());
        Builder builder = SystemCalcBasicConfig.newBuilder();
        builder.addAllBasicFactorConfigList(basicFactorConfigs);
        builder.setBasicCalcType(null == basicSystemCalcConfigBO.getBasicCalcType()
                                 ? "" : basicSystemCalcConfigBO.getBasicCalcType().name());
        if (null != basicSystemCalcConfigBO.getFixedMinValue() || null != basicSystemCalcConfigBO.getFixedMaxValue()) {
            IndicatorLocalCacheService indicatorLocalCacheService = ApplicationContextHolder.getBean(
                    IndicatorLocalCacheService.class);
            IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
            Preconditions.checkNotNull(indicatorDO, "指标元信息不存在");
            if (null != basicSystemCalcConfigBO.getFixedMinValue()) {
                builder.setFixedMinValue(
                        convertToShowValueFromLong(basicSystemCalcConfigBO.getFixedMinValue(), indicatorDO.getUnit()));
            }
            if (null != basicSystemCalcConfigBO.getFixedMaxValue()) {
                builder.setFixedMaxValue(
                        convertToShowValueFromLong(basicSystemCalcConfigBO.getFixedMaxValue(), indicatorDO.getUnit()));
            }
        }
        return builder.build();
    }


    private static BasicFactorConfig convert2BasicFactorConfig(Long indicatorId, BasicFactorConfigBO basicFactorConfigBO) {
        String validLiveTime = null != basicFactorConfigBO.getValidLiveTime() ? String.valueOf(
                millSecondsToHour(basicFactorConfigBO.getValidLiveTime())) : "";
        BasicFactorConfigDetail basicFactorConfigDetail = BasicFactorConfigDetail.newBuilder()
                .setIndicatorTimeType(basicFactorConfigBO.getIndicatorTimeType().getType())
                .setCustomizeType(convertNotNull(basicFactorConfigBO.getCustomizeType()))
                .setBaseAlgorithmCustomizeUrl(convertNotNull(basicFactorConfigBO.getBaseAlgorithmCustomizeUrl()))
                .setHiveImportConfig(convertHiveImportConfig2DTO(basicFactorConfigBO.getHiveImportConfig()))
                .setFixedStartTime(convertNotNull(basicFactorConfigBO.getFixedStartTime()))
                .setFixedEndTime(convertNotNull(basicFactorConfigBO.getFixedEndTime()))
                .setOffsetEventType(convertNotNull(basicFactorConfigBO.getOffsetEventType()))
                .setRelativeDay(basicFactorConfigBO.getRelativeTime() == null ? 0L : getDaysOfMillionSecondInterval(
                        basicFactorConfigBO.getRelativeTime()))
                .setPeriodDay(basicFactorConfigBO.getPeriodTime() == null ? 0L : getDaysOfMillionSecondInterval(
                        basicFactorConfigBO.getPeriodTime()))
                .setCoefficient(convertNotNull(basicFactorConfigBO.getCoefficient()))
                .setExclusiveFestivalDays(basicFactorConfigBO.isExclusiveFestivalDays())
                .setExclusiveMaxMinDays(basicFactorConfigBO.isExclusiveMaxMinDays())
                .setExclusiveInvalidLiveDays(Boolean.FALSE)
                .setValidLiveTime(validLiveTime)
                .setCustomBaseCalcRuleCode(StringUtils.defaultString(basicFactorConfigBO.getCustomBaseCalcRuleCode()))
                .build();
        return BasicFactorConfig.newBuilder()
                .setBaseAlgorithm(getBasicConfigAlgorithm(indicatorId, basicFactorConfigBO))
                .setFactorConfig(basicFactorConfigDetail)
                .build();
    }

    /**
     * 获取基期配置对应的基期算法
     */
    private static String getBasicConfigAlgorithm(long indicatorId, BasicFactorConfigBO customizeBasicConfig) {
        String baseAlgorithm;
        // 判断该指标是否存在特殊基期规则
        BaseFormulaResultTypeEnum baseFormulaResultType = getBaseFormulaResultType(indicatorId);

        if (Objects.requireNonNull(baseFormulaResultType) == BaseFormulaResultTypeEnum.TOTAL) {
            baseAlgorithm = ActivityBaseAlgorithmEnum.CUSTOM_BASE_CALC_RULE.getType();
        } else {
            ActivityBaseAlgorithmEnum baseAlgorithmEnum = customizeBasicConfig.getBaseAlgorithm();
            if (null == baseAlgorithmEnum || baseAlgorithmEnum == ActivityBaseAlgorithmEnum.CUSTOMIZE) {
                // 历史非特殊规则基期指标 默认日均
                baseAlgorithm = ActivityBaseAlgorithmEnum.AVG_DAY.getType();
            } else {
                baseAlgorithm = baseAlgorithmEnum.getType();
            }
        }
        return baseAlgorithm;
    }

    private static HiveImportConfig convertHiveImportConfig2DTO(HiveImportConfigBO hiveImport) {
        if (hiveImport == null) {
            return HiveImportConfig.newBuilder().build();
        }
        List<HiveExtraColumn> extra = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(hiveImport.getExtraColumnList())) {
            extra = hiveImport.getExtraColumnList().stream()
                    .map(BasicConfigConverter::convertHiveExtra2DTO)
                    .collect(Collectors.toList());
        }
        return HiveImportConfig.newBuilder()
                .setCrowdCondition(convertNotNull(hiveImport.getCrowdCondition()))
                .setDatabase(convertNotNull(hiveImport.getDatabase()))
                .setTable(convertNotNull(hiveImport.getTable()))
                .setPartitionCondition(convertNotNull(hiveImport.getPartitionCondition()))
                .setSellerIdColumnName(convertNotNull(hiveImport.getSellerIdColumnName()))
                .addAllExtraColumnList(extra)
                .build();
    }

    private static HiveExtraColumn convertHiveExtra2DTO(
            com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.HiveExtraColumn extraColumn) {
        if (extraColumn == null) {
            return HiveExtraColumn.newBuilder().build();
        }
        return HiveExtraColumn.newBuilder()
                .setColumnName(convertNotNull(extraColumn.getColumnName()))
                .setEntityId(convertNotNull(extraColumn.getEntityId()))
                .setUnit(convertNotNull(extraColumn.getUnit()))
                .build();
    }

    public static BasicConfigBO convert2BasicConfig(List<SingleBasicConfig> singleBasicConfigs) {
        if (CollectionUtils.isEmpty(singleBasicConfigs)) {
            return null;
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = Lists.newArrayList();

        singleBasicConfigs.forEach(singleBasicConfig -> {
            BasicIndicatorConfigBO basicIndicatorConfig = BasicIndicatorConfigBO.builder()
                    .indicatorId(singleBasicConfig.getIndicatorId())
                    .basicValueType(BasicValueTypeEnum.getByType(singleBasicConfig.getBasicValueType()))
                    .basicCustomConfig(convert2BasicCustomConfig(singleBasicConfig.getCustomizeBasicConfig()))
                    .basicSystemCalcConfig(convert2BasicSystemCalcConfig(singleBasicConfig.getIndicatorId(),
                            singleBasicConfig.getSystemCalcBasicConfig()))
                    .build();

            basicIndicatorConfigList.add(basicIndicatorConfig);
        });

        return BasicConfigBO.builder().basicIndicatorConfigList(basicIndicatorConfigList).build();
    }

    private static BasicCustomConfigBO convert2BasicCustomConfig(CustomBasicConfig customBasicConfig) {
        BasicFactorConfig customizeBasicConfig = customBasicConfig.getCustomizeBasicConfig();
        BasicFactorConfigBO basicFactorConfig = convert2BasicFactorConfig(customizeBasicConfig);
        basicFactorConfig.setBaseAlgorithm(ActivityBaseAlgorithmEnum.getByType(customizeBasicConfig.getBaseAlgorithm()));
        basicFactorConfig.setIndicatorTimeType(IndicatorTimeTypeEnum.CUSTOM_TIME);
        return BasicCustomConfigBO.builder().customizeBasicConfig(basicFactorConfig)
                .build();
    }

    private static BasicSystemCalcConfigBO convert2BasicSystemCalcConfig(Long indicatorId,
            SystemCalcBasicConfig systemCalcBasicConfig) {
        List<BasicFactorConfigBO> basicFactorConfigBOS = systemCalcBasicConfig.getBasicFactorConfigListList().stream()
                .map(BasicConfigConverter::convert2BasicFactorConfig)
                .collect(Collectors.toList());

        String basicCalcType = systemCalcBasicConfig.getBasicCalcType();
        BasicCalcTypeEnum basicCalcTypeEnum = StringUtils.isBlank(systemCalcBasicConfig.getBasicCalcType())
                                              ? null : BasicCalcTypeEnum.valueOf(basicCalcType);
        BasicSystemCalcConfigBO basicSystemCalcConfigBO = BasicSystemCalcConfigBO.builder()
                .basicFactorConfigList(basicFactorConfigBOS)
                .basicCalcType(basicCalcTypeEnum)
                .build();

        String fixedMaxValue = systemCalcBasicConfig.getFixedMaxValue();
        String fixedMinValue = systemCalcBasicConfig.getFixedMinValue();

        if (StringUtils.isNotBlank(fixedMaxValue) || StringUtils.isNotBlank(fixedMinValue)) {
            IndicatorLocalCacheService indicatorLocalCacheService = ApplicationContextHolder.getBean(
                    IndicatorLocalCacheService.class);
            IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
            Preconditions.checkNotNull(indicatorDO, "指标元信息不存在");
            if (StringUtils.isNotBlank(fixedMaxValue)) {
                basicSystemCalcConfigBO.setFixedMaxValue(
                        Long.parseLong(convertToStorageValueFromDouble(fixedMaxValue, indicatorDO.getUnit(), null)));
            }
            if (StringUtils.isNotBlank(fixedMinValue)) {
                basicSystemCalcConfigBO.setFixedMinValue(
                        Long.parseLong(convertToStorageValueFromDouble(fixedMinValue, indicatorDO.getUnit(), null)));
            }
        }

        return basicSystemCalcConfigBO;
    }

    private static BasicFactorConfigBO convert2BasicFactorConfig(BasicFactorConfig customizeBasicConfig) {
        BasicFactorConfigDetail factorConfig = customizeBasicConfig.getFactorConfig();

        String coefficient =
                StringUtils.isNotBlank(factorConfig.getCoefficient()) ? factorConfig.getCoefficient() : "1";

        BasicFactorConfigBO basicFactorConfig = BasicFactorConfigBO.builder()
                .indicatorTimeType(IndicatorTimeTypeEnum.getByType(factorConfig.getIndicatorTimeType()))
                .customizeType(factorConfig.getCustomizeType())
                .baseAlgorithm(ActivityBaseAlgorithmEnum.getByType(customizeBasicConfig.getBaseAlgorithm()))
                .baseAlgorithmCustomizeUrl(factorConfig.getBaseAlgorithmCustomizeUrl())
                .hiveImportConfig(convert2HiveImportConfig(factorConfig.getHiveImportConfig()))
                .fixedStartTime(factorConfig.getFixedStartTime())
                .fixedEndTime(factorConfig.getFixedEndTime())
                .offsetEventType(factorConfig.getOffsetEventType())
                .relativeTime(getMillionSecondsOfDayInterval((int) factorConfig.getRelativeDay()))
                .periodTime(getMillionSecondsOfDayInterval((int) factorConfig.getPeriodDay()))
                .coefficient(new BigDecimal(coefficient))
                .exclusiveFestivalDays(factorConfig.getExclusiveFestivalDays())
                .exclusiveMaxMinDays(factorConfig.getExclusiveMaxMinDays())
                .customBaseCalcRuleCode(factorConfig.getCustomBaseCalcRuleCode())
                .exclusiveInvalidLiveDays(false)
                .build();

        if (factorConfig.getExclusiveInvalidLiveDays()) {
            double validLiveHour =
                    new BigDecimal(factorConfig.getValidLiveTime()).setScale(1, RoundingMode.UP).doubleValue();
            basicFactorConfig.setValidLiveTime(hourToMillSeconds(validLiveHour));
        }

        return basicFactorConfig;
    }

    private static HiveImportConfigBO convert2HiveImportConfig(HiveImportConfig hiveImportConfig) {
        List<com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.HiveExtraColumn>
                extraColumnList = Lists.newArrayList();

        List<HiveExtraColumn> extraColumnListList = hiveImportConfig.getExtraColumnListList();
        if (CollectionUtils.isNotEmpty(extraColumnListList)) {
            extraColumnList = extraColumnListList.stream().map(BasicConfigConverter::convert2HiveExtraColumn)
                    .collect(Collectors.toList());
        }

        return HiveImportConfigBO.builder()
                .crowdCondition(hiveImportConfig.getCrowdCondition())
                .database(hiveImportConfig.getDatabase())
                .table(hiveImportConfig.getTable())
                .partitionCondition(hiveImportConfig.getPartitionCondition())
                .sellerIdColumnName(hiveImportConfig.getSellerIdColumnName())
                .extraColumnList(extraColumnList)
                .build();
    }

    private static com.kuaishou.kwaishop.merchant.growth.center.activity.domain
            .admin.model.bo.industry.activity.HiveExtraColumn convert2HiveExtraColumn(
            HiveExtraColumn hiveExtraColumn) {
        return com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.HiveExtraColumn.builder()
                .columnName(hiveExtraColumn.getColumnName())
                .entityId(hiveExtraColumn.getEntityId())
                .unit(hiveExtraColumn.getUnit())
                .build();
    }
}
