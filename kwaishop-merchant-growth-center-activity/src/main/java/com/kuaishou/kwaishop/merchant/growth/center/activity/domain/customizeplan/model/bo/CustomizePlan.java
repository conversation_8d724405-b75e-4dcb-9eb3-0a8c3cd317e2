package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizePlan {
    /**
     * 计划唯一Code
     */
    private String planCode;

    /**
     * 计划关联的活动ID计划
     */
    private List<Long> activityIdList;

    /**
     * 页面排序优先级
     */
    private int priority;

    /**
     * PC端banner
     */
    private String bannerPC;

    /**
     * 移动端banner
     */
    private String bannerAPP;

    /**
     * 图片
     */
    private String picUrl;

    /**
     * 计划标题 eg: 新商成长计划
     */
    private TextStyleBO mainTitle;

    /**
     * 标签合集 eg: 火热报名中
     */
    private List<TagBO> tagList;

    /**
     * 活动开始时间
     */
    private long startTime;

    /**
     * 活动结束时间
     */
    private long endTime;

    /**
     * 活动简介
     */
    private String desc;

    /**
     * 活动报名结束时间
     */
    private long drawEndTime;

    /**
     * 任务完成度
     */
    private CompleteProgressBO planProgress;

    /**
     * 任务状态 对应planStatusEnum
     */
    private int planStatus;

    /**
     * 用户计划状态 对应UserPlanStatusEnum
     */
    private int userPlanStatus;

    /**
     * 定制计划关联的活动
     */
    private List<CustomizeActivity> activityList;
}
