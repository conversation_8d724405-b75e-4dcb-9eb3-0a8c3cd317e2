package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.DELAY_NOTIFICATION_PUSH_CONSUMER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.UNKNOWN_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_TEMPLATE_PARAM_SERVICE_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfOther;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.BaseMessageTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-24
 */
@Slf4j
public abstract class AbstractNotificationEntityService implements InitializingBean {

    /**
     * 获取策略实体类型
     */
    public abstract NotificationEntityTypeEnum getNotificationStrategyEntityType();

    /**
     * 校验实体状态是否变更
     */
    public abstract boolean checkEntityStatus(long userId, NotificationPushConfigBO configBO);

    /**
     * 校验活动是否结束
     */
    public abstract boolean checkEndTime(long userId, NotificationPushConfigBO configBO);

    public NotificationExtendFunctionParamBO getTemplateParamsMap(long userId, NotificationPushConfigBO configBO) {
        NotificationExecuteStatusEnum executeStatus = EXECUTE;
        BaseMessageTemplateConfigBO templateConfig =
                fromJSON(configBO.getTemplateConfig(), BaseMessageTemplateConfigBO.class);
        // 不需要拼接模版参数，直接返回
        if (CollectionUtils.isEmpty(templateConfig.getTemplateParamNames())) {
            return NotificationExtendFunctionParamBO.builder()
                    .templateParamMap(Collections.emptyMap())
                    .executeStatus(executeStatus)
                    .build();
        }

        // 枚举转换
        List<TemplateParamTypeEnum> templateParams = convertTemplateParam(templateConfig.getTemplateParamNames());

        // 获取对应模版变量生成策略service
        Set<AbstractNotificationExtendFunctionParamService> templateParamStrategyServiceSet =
                NotificationTemplateParamStrategyFactory.getTemplateParamStrategyServiceList(templateParams);
        if (CollectionUtils.isEmpty(templateParamStrategyServiceSet)) {
            log.error("[获取对应模版变量策略失败] configBO:{}", toJSON(configBO));
            perfException(DELAY_NOTIFICATION_PUSH_CONSUMER, "获取对应模版变量策略失败!");
            throw new BizException(NOTIFICATION_TEMPLATE_PARAM_SERVICE_ERROR, "获取对应模版变量策略失败");
        }

        // 模版变量值获取
        Map<String, String> templateParamMap = new HashMap<>();
        for (AbstractNotificationExtendFunctionParamService service : templateParamStrategyServiceSet) {
            NotificationExtendFunctionParamBO extendFunctionParams =
                    service.getExtendFunctionParams(userId, configBO, templateParams);
            NotificationExecuteStatusEnum currentExecuteStatus = extendFunctionParams.getExecuteStatus();
            // 取当前执行状态优先级最高的作为最终执行状态
            if (currentExecuteStatus != null && currentExecuteStatus.getVal() > executeStatus.getVal()) {
                log.info(
                        "[当前推送被置为非正常执行状态] userId:{}, currentExecuteStatus:{}, configBO:{},templateParams:{}",
                        userId,
                        currentExecuteStatus, toJSON(configBO), service.templateParamTypes());
                perfOther(DELAY_NOTIFICATION_PUSH_CONSUMER, ".executeStatus", "置为非正常执行状态");
                executeStatus = currentExecuteStatus;
            }
            if (MapUtils.isNotEmpty(extendFunctionParams.getTemplateParamMap())) {
                templateParamMap.putAll(extendFunctionParams.getTemplateParamMap());
            }
        }

        return NotificationExtendFunctionParamBO.builder()
                .templateParamMap(templateParamMap)
                .executeStatus(executeStatus)
                .build();
    }

    private List<TemplateParamTypeEnum> convertTemplateParam(List<String> templateParamNames) {
        List<TemplateParamTypeEnum> list = templateParamNames.stream()
                .map(TemplateParamTypeEnum::of)
                .collect(Collectors.toList());
        if (list.contains(UNKNOWN_TEMPLATE_PARAM)) {
            log.error("[AbstractNotificationEntityService] 配置了未知模版变量！templateParams:{}", templateParamNames);
            throw new BizException(NOTIFICATION_TEMPLATE_PARAM_SERVICE_ERROR, "配置了未知模版变量");
        }
        return list;
    }

    @Override
    public void afterPropertiesSet() {
        NotificationEntityStrategyFactory.register(this.getNotificationStrategyEntityType(), this);
    }
}
