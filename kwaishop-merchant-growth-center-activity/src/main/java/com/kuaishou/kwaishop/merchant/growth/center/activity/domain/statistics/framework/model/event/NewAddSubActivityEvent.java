package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NewAddSubActivityEvent extends BatchExecuteEvent {
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动对象
     */
    private ActivityDO activityDO;
    /**
     * 新增任务ID
     */
    private List<TaskDO> newAddTaskList;
    /**
     * 分层人群信息
     */
    private Map<Long, Set<Long>> layerTaskCrowd;
}
