package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.supplier.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.converter.impl.AwardItemConverter.buildAwardItemList;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.frontConfig.IndicatorActionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.frontConfig.StrategyComponentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ComponentConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.FrontConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.TaskTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.helper.ComponentDataExpHelper;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardItemBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardValueBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.ButtonInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CompleteProgressBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizeActivity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizeActivityBuildContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizeIndicator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizeTask;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.ConstructorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.UserPlanStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.supplier.CustomizePlanConstructor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.IndicatorStepTargetBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.UserIndicatorStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Service
@Slf4j
public class ActivityCustomizePlanConstructorImpl implements CustomizePlanConstructor {

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Autowired
    private IndicatorTaskService indicatorTaskService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private AwardCalcService awardCalcService;

    @Autowired
    private IndicatorCalcService indicatorCalcService;

    @Autowired
    private ComponentDataExpHelper componentDataExpHelper;

    @Override
    public CustomizePlan initCustomizePlan(long userId, Map<String, Object> configMap) {
        // 获取参数对应的Map key
        String paramMapKey = getParamMapKey();
        // 获取活动ID集合
        List<Long> activityIdList = getActivityIdList(configMap, paramMapKey).stream().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIdList)) {
            return null;
        }
        List<CustomizeActivity> customizeActivityList = Lists.newArrayList();
        activityIdList.forEach(activityId -> {
            CustomizeActivity customizeActivity = buildCustomizeActivity(userId, activityId);
            customizeActivityList.add(customizeActivity);
        });
        CustomizePlan customizePlan = new CustomizePlan();
        customizePlan.setActivityList(customizeActivityList);
        //定制计划完成度
        long currentValue = 0L;
        long targetValue = 0L;
        for (CustomizeActivity customizeActivity : customizeActivityList) {
            CompleteProgressBO activityProgress = customizeActivity.getActivityProgress();
            currentValue += activityProgress.getCurrentValue();
            targetValue += activityProgress.getTargetValue();
        }
        if (currentValue == targetValue) {
            customizePlan.setUserPlanStatus(UserPlanStatusEnum.SUCCESS.getStatus());
        } else {
            //判断定制计划是否未领取
            boolean draw = customizeActivityList.stream()
                    .anyMatch(customizeActivity -> UserActivityStatusEnum.DRAWING.getValue() == customizeActivity.getUserActivityStatus());
            if (draw) {
                customizePlan.setUserPlanStatus(UserPlanStatusEnum.DRAWING.getStatus());
            } else {
                customizePlan.setUserPlanStatus(UserPlanStatusEnum.PROCESSING.getStatus());
            }
        }
        CompleteProgressBO completeProgress = CompleteProgressBO.builder().currentValue(currentValue).targetValue(targetValue).build();
        customizePlan.setPlanProgress(completeProgress);
        customizePlan.setActivityIdList(activityIdList);

        return customizePlan;
    }

    @Override
    public ConstructorEnum getConstructorCode() {
        return ConstructorEnum.ACTIVITY;
    }

    @Override
    public String getParamMapKey() {
        return "activityIdList";
    }

    /**
     * 从参数Map中获取构造器所需参数
     */
    private List<String> getActivityIdList(Map<String, Object> configMap, String paramMapKey) {
        if (MapUtils.isEmpty(configMap) || !configMap.containsKey(paramMapKey)) {
            log.error("[查询定制计划] 定制计划未配置构造参数, constructor:{}, paramMapKey:{}",
                    getConstructorCode().getConstructorCode(), paramMapKey);
            throw new BizException(SERVER_ERROR, "定制计划未配置构造参数");
        }
        Object paramValue = configMap.get(paramMapKey);
        if (!(paramValue instanceof List)) {
            return Lists.newArrayList();
        }
        return (List<String>) paramValue;
    }

    /**
     * 构建定制计划下任务列表中的活动
     */
    private CustomizeActivity buildCustomizeActivity(long userId, long activityId) {
        // 构建context
        CustomizeActivityBuildContextBO context = buildCustomizeActivityContext(userId, activityId);
        List<CustomizeTask> customizeTaskList = Lists.newArrayList();
        Map<Long, TaskDO> parentTaskMap = context.getParentTaskMap();
        // 构建定制计划任务
        parentTaskMap.forEach((parentTaskId, parentTask) -> {
            CustomizeTask customizeTask = buildCustomizeTask(context, parentTaskId);
            customizeTaskList.add(customizeTask);
        });

        CustomizeActivity customizeActivity = new CustomizeActivity();
        if (CollectionUtils.isNotEmpty(customizeTaskList)) {
            customizeActivity.setTaskList(customizeTaskList);
        }
        long currentValue = customizeTaskList.stream().filter(customizeTask ->
                UserTaskStatusEnum.SUCCESS.getValue() == customizeTask.getUserTaskStatus()).count();
        long targetValue = customizeTaskList.size();
        CompleteProgressBO completeProgress = CompleteProgressBO.builder().currentValue(currentValue).targetValue(targetValue).build();
        customizeActivity.setActivityId(activityId);
        customizeActivity.setActivityProgress(completeProgress);
        UserActivityRecordDO userActivityRecord = context.getUserActivityRecord();
        if (userActivityRecord != null) {
            // 已领取
            customizeActivity.setUserActivityStatus(userActivityRecord.getStatus());
        } else {
            customizeActivity.setUserActivityStatus(UserActivityStatusEnum.DRAWING.getValue());
        }
        return customizeActivity;
    }

    private CustomizeActivityBuildContextBO buildCustomizeActivityContext(long userId, long activityId) {
        CustomizeActivityBuildContextBO context = new CustomizeActivityBuildContextBO();
        context.setUserId(userId);
        if (activityId <= 0) {
            log.error("[查询定制计划] 活动ID不合规范, activityId:{}", activityId);
            throw new BizException(SERVER_ERROR, "活动ID不合规范");
        }
        ActivityDO activityInfo = activityLocalCacheService.queryActivityInfo(activityId);
        if (Objects.isNull(activityInfo)) {
            log.error("[查询定制计划] 活动不存在, activityId:{}", activityId);
            throw new BizException(SERVER_ERROR, "活动不存在");
        }
        //活动
        context.setActivity(activityInfo);
        List<FrontConfigBO> frontConfigList = ObjectMapperUtils.fromJSON(activityInfo.getFrontConfig(), List.class, FrontConfigBO.class);
        if (CollectionUtils.isEmpty(frontConfigList) || Objects.isNull(frontConfigList.get(0))
                || CollectionUtils.isEmpty(frontConfigList.get(0).getComponent())) {
            log.error("[查询定制计划] 活动未设置展示配置, activityId:{}", activityId);
            throw new BizException(SERVER_ERROR, "活动未设置展示配置");
        }
        List<ComponentConfigBO> componentList = frontConfigList.get(0).getComponent();
        // 构建任务指标展示配置
        Map<Long, ComponentConfigBO> componentMap =
                componentList.stream().collect(Collectors.toMap(ComponentConfigBO::getEntityId, Function.identity()));
        //展示配置
        context.setComponentMap(componentMap);
        // 获取用户活动信息
        UserActivityRecordDO userActivityRecord = userActivityRecordDAO.queryUserActivityRecord(userId, activityId, false);
        context.setUserActivityRecord(userActivityRecord);
        // 用户任务资格信息
        List<UserRegistrationRecordBO> userRegistrationRecords =
                userRegistrationService.queryUserRegistrationRecords(userId, activityId, EntityTypeEnum.TASK, UserRegistrationStatusEnum.VALID);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            log.error("[查询定制计划] 用户无任务资格记录, userId:{}, activityId;{}", userId, activityId);
            throw new BizException(SERVER_ERROR, "用户无任务资格记录");
        }
        // 父任务对应资格
        Map<Long, UserRegistrationRecordBO> userRegistrationRecordMap =
                userRegistrationRecords.stream().collect(Collectors.toMap(UserRegistrationRecordBO::getEntityId, Function.identity()));
        context.setUserRegistrationRecordMap(userRegistrationRecordMap);
        List<Long> parentTaskIdList;
        if (userActivityRecord != null) {
            // 已领取
            List<UserTaskRecordDO> userTaskRecordList = userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false);
            if (CollectionUtils.isEmpty(userTaskRecordList)) {
                log.error("[查询定制计划] 用户无对应任务记录, userId:{}, activityId:{}", userId, activityId);
                throw new BizException(SERVER_ERROR, "用户无对应任务记录");
            }
            Map<Long, UserTaskRecordDO> userTaskRecordMap =
                    userTaskRecordList.stream().collect(Collectors.toMap(UserTaskRecordDO::getTaskId, Function.identity()));
            context.setUserTaskRecordMap(userTaskRecordMap);
            List<IndicatorRecordBO> activityIndicatorInfoList = indicatorTaskService.listUserRecordOfActivity(userId, activityId);
            if (CollectionUtils.isEmpty(activityIndicatorInfoList)) {
                log.error("[查询定制计划] 用户无对应指标记录，userId:{}, activityId:{}", userId, activityId);
                throw new BizException(SERVER_ERROR, "用户无对应指标记录");
            }
            // 从已领取的任务记录中筛选父任务
            parentTaskIdList = userTaskRecordList.stream().filter(userTaskRecord ->
                    userTaskRecord.getParentId() == 0).map(UserTaskRecordDO::getTaskId).collect(Collectors.toList());
            Map<Long, List<IndicatorRecordBO>> indicatorRecordMap =
                    activityIndicatorInfoList.stream().collect(Collectors.groupingBy(IndicatorRecordBO::getEntityId));
            // 指标记录
            context.setIndicatorRecordMap(indicatorRecordMap);
        } else {
            // 从有资格的任务记录中筛选父任务
            parentTaskIdList = userRegistrationRecords.stream().map(UserRegistrationRecordBO::getEntityId).collect(Collectors.toList());
        }
        List<TaskDO> taskList = taskLocalCacheService.getTaskListByActivityId(activityId);
        if (CollectionUtils.isEmpty(taskList)) {
            log.error("[查询定制计划] 活动下不存在任务, activityId:{}", activityId);
            throw new BizException(SERVER_ERROR, "活动下不存在任务");
        }

        // 构建父任务Map
        Map<Long, TaskDO> parentTaskMap = taskList.stream().filter(task ->
                parentTaskIdList.contains(task.getId())).collect(Collectors.toMap(TaskDO::getId, Function.identity()));
        context.setParentTaskMap(parentTaskMap);
        // 构建子任务Map
        Map<Long, List<TaskDO>> subTaskMap =
                taskList.stream().filter(task -> task.getParentTask() != 0).collect(Collectors.groupingBy(TaskDO::getParentTask));
        context.setSubTaskMap(subTaskMap);

        return context;
    }

    /**
     * 构建定制计划活动下的任务
     */
    private CustomizeTask buildCustomizeTask(CustomizeActivityBuildContextBO context, long parentTaskId) {
        Map<Long, List<TaskDO>> subTaskMap = context.getSubTaskMap();
        Map<Long, TaskDO> parentTaskMap = context.getParentTaskMap();
        List<TaskDO> subTaskList = subTaskMap.get(parentTaskId);
        TaskDO parentTask = parentTaskMap.get(parentTaskId);
        if (CollectionUtils.isEmpty(subTaskList)) {
            log.error("[查询定制计划] 子任务集合为空, parentTaskId:{}", parentTask.getId());
            throw new BizException(SERVER_ERROR, "子任务集合为空");
        }
        Map<Long, ComponentConfigBO> componentMap = context.getComponentMap();
        if (!componentMap.containsKey(parentTask.getId())) {
            log.error("[查询定制计划] 任务无对应展示配置, parentTaskId:{}", parentTask.getId());
            throw new BizException(SERVER_ERROR, "任务无对应展示配置");
        }
        ComponentConfigBO frontConfigComponent = componentMap.get(parentTask.getId());
        String data = componentDataExpHelper
                .getComponentData(frontConfigComponent, context.getActivity().getId(), context.getUserId());
        StrategyComponentBO strategyComponent =
                ObjectMapperUtils.fromJSON(data, StrategyComponentBO.class);
        if (Objects.isNull(strategyComponent)) {
            log.error("[查询定制计划] 策略组件配置为空, parentTaskId:{}", parentTask.getId());
            throw new BizException(SERVER_ERROR, "策略组件配置为空");
        }

        CustomizeTask customizeTask = new CustomizeTask();
        // 标题
        customizeTask.setTitle(parentTask.getName());
        // 任务ID
        customizeTask.setTaskId(parentTask.getId());
        // 填充奖励配置
        List<AwardItemBO> awardList = buildCustomizeAward(context, parentTaskId);
        if (CollectionUtils.isNotEmpty(awardList)) {
            customizeTask.setAwardList(awardList);
        }
        // 用户任务状态
        Map<Long, UserTaskRecordDO> userTaskRecordMap = context.getUserTaskRecordMap();
        if (MapUtils.isNotEmpty(userTaskRecordMap) && userTaskRecordMap.containsKey(parentTaskId)) {
            UserTaskRecordDO userTaskRecord = userTaskRecordMap.get(parentTaskId);
            customizeTask.setUserTaskStatus(userTaskRecord.getStatus());
        } else {
            customizeTask.setUserTaskStatus(UserTaskStatusEnum.DRAWING.getValue());
        }
        // 填充指标配置
        List<CustomizeIndicator> customizeIndicatorList = buildCustomizeIndicator(context, parentTaskId);
        if (CollectionUtils.isNotEmpty(customizeIndicatorList)) {
            customizeTask.setIndicatorList(customizeIndicatorList);
        }

        return customizeTask;
    }

    /**
     * 构建定制计划奖励
     */
    private List<AwardItemBO> buildCustomizeAward(CustomizeActivityBuildContextBO context, long parentTaskId) {
        long userId = context.getUserId();
        ActivityDO activity = context.getActivity();
        Map<Long, UserRegistrationRecordBO> userRegistrationRecordMap = context.getUserRegistrationRecordMap();
        Map<Long, TaskDO> parentTaskMap = context.getParentTaskMap();
        TaskDO parentTask = parentTaskMap.get(parentTaskId);
        if (!userRegistrationRecordMap.containsKey(parentTaskId)) {
            log.error("[查询定制计划] 用户无该任务资格, userId:{}, parentTaskId:{}", userId, parentTaskId);
            throw new BizException(SERVER_ERROR, "用户无该任务资格");
        }
        UserRegistrationRecordBO userRegistrationRecord = userRegistrationRecordMap.get(parentTaskId);
        Map<String, Object> baseDataMap = ObjectMapperUtils.fromJSON(userRegistrationRecord.getJsonData(),
                Map.class, String.class, Object.class);
        List<AwardValueBO> awardValueList = awardCalcService.calcLayerFixedAwardValue(userId, activity, parentTask, baseDataMap);
        if (CollectionUtils.isEmpty(awardValueList)) {
            return Lists.newArrayList();
        }
        if (!parentTask.getType().equals(TaskTypeEnum.MULTI_STAGE.getCode())) {
            // 发最高档 只取最高档
            Integer maxStep = awardValueList.stream().mapToInt(AwardValueBO::getStep).max().orElse(1);
            // 按阶梯聚合
            Map<Integer, List<AwardValueBO>> stepAwardValueMap = awardValueList.stream().collect(Collectors.groupingBy(AwardValueBO::getStep));
            awardValueList = stepAwardValueMap.get(maxStep);
        }

        return buildAwardItemList(awardValueList);
    }

    /**
     * 构建定制计划指标
     */
    private List<CustomizeIndicator> buildCustomizeIndicator(CustomizeActivityBuildContextBO context, long parentTaskId) {
        Map<Long, ComponentConfigBO> componentMap = context.getComponentMap();
        ComponentConfigBO componentConfig = componentMap.get(parentTaskId);
        String data = componentDataExpHelper
                .getComponentData(componentConfig, context.getActivity().getId(), context.getUserId());
        StrategyComponentBO strategyComponent = ObjectMapperUtils.fromJSON(data, StrategyComponentBO.class);
        List<IndicatorActionBO> targetInfo = strategyComponent.getTargetInfo();

        Map<Long, IndicatorActionBO> indicatorActionMap =
                targetInfo.stream().collect(Collectors.toMap(IndicatorActionBO::getIndicatorId, Function.identity()));

        List<CustomizeIndicator> customizeIndicatorList = Lists.newArrayList();
        Map<Long, CompleteProgressBO> indicatorProgressMap = buildCustomizeIndicatorProgress(context, parentTaskId);
        indicatorProgressMap.forEach((indicatorId, indicatorProgress) -> {
            String text;
            String jumpUrl;
            String pcJumpUrl;
            String guideUrl;
            boolean showProgress;
            if (CollectionUtils.isEmpty(targetInfo)) {
                text = strategyComponent.getButtonText();
                jumpUrl = strategyComponent.getButtonJumpUrl();
                pcJumpUrl = strategyComponent.getButtonPcJumpUrl();
                guideUrl = strategyComponent.getStrategyJumpUrl();
                showProgress = strategyComponent.isShowProgress();
            } else {
                IndicatorActionBO indicatorAction = indicatorActionMap.get(indicatorId);
                if (indicatorAction == null) {
                    log.error("[查询定制计划] 指标未配置行动点, parentTaskId:{}, indicatorId:{}", parentTaskId, indicatorId);
                    throw new BizException(SERVER_ERROR, "指标未配置行动点");
                }
                text = indicatorAction.getButtonText();
                jumpUrl = indicatorAction.getButtonJumpUrl();
                pcJumpUrl = indicatorAction.getButtonPcJumpUrl();
                guideUrl = indicatorAction.getStrategyJumpUrl();
                showProgress = indicatorAction.isShowProgress();
            }
            CustomizeIndicator indicator = new CustomizeIndicator();
            ButtonInfo indicatorActionButton = ButtonInfo.builder()
                    .text(text)
                    .jumpUrl(jumpUrl)
                    .pcJumpUrl(pcJumpUrl)
                    .build();
            indicator.setButton(indicatorActionButton);
            indicator.setShowProgress(showProgress);
            indicator.setGuideUrl(guideUrl);
            indicator.setIndicatorId(indicatorId);
            if (showProgress) {
                indicator.setIndicatorProgress(indicatorProgress);
            }
            IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
            indicator.setUnit(indicatorDO.getUnit());
            indicator.setUserIndicatorStatus(indicatorProgress.getStatus());
            customizeIndicatorList.add(indicator);
        });

        return customizeIndicatorList;
    }

    private Map<Long, CompleteProgressBO> buildCustomizeIndicatorProgress(CustomizeActivityBuildContextBO context, long parentTaskId) {
        Map<Long, UserTaskRecordDO> userTaskRecordMap = context.getUserTaskRecordMap();
        Map<Long, CompleteProgressBO> resMap = Maps.newHashMap();

        Map<Long, List<TaskDO>> subTaskMap = context.getSubTaskMap();
        List<TaskDO> subTaskList = subTaskMap.get(parentTaskId);
        Map<Integer, TaskDO> stageTaskMap = subTaskList.stream().collect(Collectors.toMap(TaskDO::getStage, Function.identity()));
        int maxStage = subTaskList.stream().mapToInt(TaskDO::getStage).max().orElse(1);
        TaskDO subTask = stageTaskMap.get(maxStage);

        if (MapUtils.isNotEmpty(userTaskRecordMap) && userTaskRecordMap.containsKey(parentTaskId)) {
            // 任务已领取 通过用户指标记录构建指标
            Map<Long, List<IndicatorRecordBO>> indicatorRecordMap = context.getIndicatorRecordMap();
            List<IndicatorRecordBO> indicatorRecordList = indicatorRecordMap.get(subTask.getId());
            if (CollectionUtils.isEmpty(indicatorRecordList)) {
               throw new BizException(SERVER_ERROR, "用户指标记录不存在");
            }
            for (IndicatorRecordBO indicatorRecord : indicatorRecordList) {
                // FIXME 目前只允许配单阶梯的任务 目标值直接从targetValue里面取 之后分情况从ext里面取
                CompleteProgressBO completeProgress =
                        CompleteProgressBO.builder()
                                .currentValue(indicatorRecord.getCurrentValue())
                                .targetValue(indicatorRecord.getTargetValue())
                                .status(indicatorRecord.getStatus()).build();
                resMap.put(indicatorRecord.getIndicatorId(), completeProgress);
            }
        } else {
            // 任务未领取
            ActivityDO activity = context.getActivity();
            long userId = context.getUserId();
            Map<Long, UserRegistrationRecordBO> userRegistrationRecordMap = context.getUserRegistrationRecordMap();
            UserRegistrationRecordBO userRegistrationRecord = userRegistrationRecordMap.get(parentTaskId);
            Map<String, Object> baseDataMap = ObjectMapperUtils.fromJSON(userRegistrationRecord.getJsonData(),
                    Map.class, String.class, Object.class);
            List<IndicatorConfigDO> indicatorConfigList = indicatorLocalCacheService.queryTaskIndicatorConfig(activity.getId(), subTask.getId());
            for (IndicatorConfigDO indicatorConfig : indicatorConfigList) {
                List<IndicatorStepTargetBO> indicatorStepTargetList =
                        indicatorCalcService.calcStepTargetValue(userId, activity.getId(), indicatorConfig, subTask.getStage(), baseDataMap);
                int maxStep = indicatorStepTargetList.stream().mapToInt(IndicatorStepTargetBO::getStep).max().orElse(1);
                Map<Integer, IndicatorStepTargetBO> stepIndicatorTargetMap =
                        indicatorStepTargetList.stream().collect(Collectors.toMap(IndicatorStepTargetBO::getStep, Function.identity()));
                IndicatorStepTargetBO indicatorStepTarget = stepIndicatorTargetMap.get(maxStep);
                CompleteProgressBO completeProgress =
                        CompleteProgressBO.builder().currentValue(0L)
                                .targetValue(indicatorStepTarget.getTargetValue())
                                .status(UserIndicatorStatusEnum.UNKNOWN.getValue()).build();
                resMap.put(indicatorConfig.getIndicatorId(), completeProgress);
            }
        }

        return resMap;
    }
}
