package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.supplier;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.LaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.LaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.LaunchPipeHandler;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
public interface LaunchSupplier<CTX extends LaunchInfoFetchContext<R>,
        R extends LaunchInfoResult<?>> extends LaunchPipeHandler<CTX, R> {

    void supply(CTX context);

    @Override
    default void doHandle(CTX context) {
        supply(context);
    }
}
