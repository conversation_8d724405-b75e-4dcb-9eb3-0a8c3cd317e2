package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.impl;

import static com.kuaishou.framework.concurrent.DynamicThreadExecutor.dynamic;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJson;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.ADd_DRAW_SIGN_UP_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum.VALID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.ACTIVITY_SHARD_DATA_SOURCE_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.REGISTRATION_UPDATE_CACHE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.cursorGetRegistrationRecordBufferSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.queryRegistrationRecordCursor;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.queryRegistrationRecordLimit;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.userRegistrationScanThreadSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.cacheExpireTimeConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.cursorGetRegistrationRecordStartId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.sendUserRegistrationChangeEvent;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.sendUserRegistrationEvent;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfExceptionWithWatch;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.roaringbitmap.longlong.Roaring64NavigableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.github.phantomthief.util.CursorIterator;
import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.id.sequence.IdSequenceService;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.RegistrationEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationCacheBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.cache.RedisDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityIdBizType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.CacheKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.DataChangeEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.CommonDataChangeEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.UserRegistrationEventMsg;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.ResponseUtils;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.ValidationUtils;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.registration.UserRegistrationRecordStatusChangeEvent;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * 报名服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Lazy
public class UserRegistrationServiceImpl implements UserRegistrationService {

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private RegistrationConverter registrationConverter;

    @Autowired
    private IdSequenceService idSequenceService;

    @Resource
    private MsgProducer userRegistrationRecordStatusChangeProducer;

    @Resource
    private MsgProducer userRegistrationEventProducer;

    private static final ExecutorService USER_REGISTRATION_SCAN_THREAD =
            dynamic(userRegistrationScanThreadSize::get, "user-registration-scan-thread");

    @Override
    public long saveUserRegistrationRecord(UserRegistrationRecordBO userRegistrationRecord) {
        if (userRegistrationRecord == null) {
            return 0;
        }
        // 检查合法性，非法会抛出异常
        ValidationUtils.validate(userRegistrationRecord);
        UserRegistrationRecordDO userRegistrationRecordDO = registrationConverter
                .convertToUserRecordDO(userRegistrationRecord);
        // 第一次插入生成主键ID
        if (userRegistrationRecordDO.getId() == null || userRegistrationRecordDO.getId() <= 0) {
            long id = idSequenceService.getId(ActivityIdBizType.USER_REGISTRATION_RECORD_ID_TYPE);
            userRegistrationRecordDO.setId(id);
            return userRegistrationRecordDAO.insert(userRegistrationRecordDO);
        }
        // 如果上层没有传递版本号则在该层查询一次进行填充
        if (userRegistrationRecord.getVersion() == null) {
            UserRegistrationRecordBO userRegistrationRecordBO =
                    queryUserRegistrationRecord(userRegistrationRecord.getUserId(),
                            userRegistrationRecord.getActivityId(),
                            userRegistrationRecord.getEntityType(),
                            userRegistrationRecord.getEntityId());
            if (userRegistrationRecordBO == null) {
                throw new BizException(ActivityExceptionCode.CAN_NOT_FIND_RECORD, "未找到对应的用户报名记录");
            }
            userRegistrationRecordDO.setVersion(userRegistrationRecordBO.getVersion());
        }
        long cnt = userRegistrationRecordDAO.updateUserRegistrationRecordById(userRegistrationRecordDO);
        if (cnt <= 0) {
            throw new BizException(BasicErrorCode.UPDATE_FAIL, "用户报名记录更新失败");
        }
        return userRegistrationRecordDO.getId();
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean batchInsertOnDupUpdateStatus(Collection<UserRegistrationRecordBO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return false;
        }
        List<UserRegistrationRecordDO> userRegistrationRecordDOS =
                registrationConverter.batchConvertToUserRecordDO(userRegistrationRecords);
        return userRegistrationRecordDAO.batchInsertOnDupUpdateStatus(userRegistrationRecordDOS);
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean batchInsertUserRegistrationRecord(List<UserRegistrationRecordBO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return false;
        }
        long activityId = userRegistrationRecords.get(0).getActivityId();
        long userId = userRegistrationRecords.get(0).getUserId();
        EntityTypeEnum entityType = userRegistrationRecords.get(0).getEntityType();
        Set<Long> entityIds = Sets.newHashSet();
        for (UserRegistrationRecordBO recordBO : userRegistrationRecords) {
            if (recordBO.getActivityId() != activityId || userId != recordBO.getUserId()
                    || entityType != recordBO.getEntityType()) {
                throw new BizException(BasicErrorCode.PARAM_INVALID, "同一批数据的活动ID、用户ID、实体类型需要一致!");
            }
            entityIds.add(recordBO.getEntityId());
        }
        Map<Long, UserRegistrationRecordBO> registrationRecordMap =
                batchQueryUserRegistrationRecords(userId, activityId, entityType, entityIds);
        // 过滤掉在数据库中已经存在的记录
        List<UserRegistrationRecordBO> insertUserRegistrationRecords = userRegistrationRecords.stream()
                .filter(record -> MapUtils.getObject(registrationRecordMap, record.getEntityId()) == null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insertUserRegistrationRecords)) {
            return true;
        }
        List<UserRegistrationRecordDO> userRegistrationRecordDOS =
                registrationConverter.batchConvertToUserRecordDO(insertUserRegistrationRecords);
        // 批量插入
        return userRegistrationRecordDAO.batchInsertUserRegistrationRecord(userRegistrationRecordDOS);
    }

    @Override
    public boolean batchInsertUserRegistrationRecordNoLimit(List<UserRegistrationRecordBO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return false;
        }
        List<UserRegistrationRecordDO> userRegistrationRecordDOS =
                registrationConverter.batchConvertToUserRecordDO(userRegistrationRecords);
        return userRegistrationRecordDAO.batchInsertUserRegistrationRecord(userRegistrationRecordDOS);
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean batchCancelUserRegistrationRecords(long activityId, EntityTypeEnum entityType,
            Collection<Long> userIds) {
        if (activityId < 1 || CollectionUtils.isEmpty(userIds) || entityType == null) {
            return false;
        }
        // 查出这批用户中有资格的用户
        List<UserRegistrationRecordDO> userRegistrationRecords = userRegistrationRecordDAO
                .batchQueryUserRegistrationRecords(activityId, entityType, null, userIds,
                        VALID);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return true;
        }
        userRegistrationRecords.forEach(record -> record.setStatus(UserRegistrationStatusEnum.INVALID.getCode()));
        return userRegistrationRecordDAO.batchUpdateUserRegistrationRecord(userRegistrationRecords);
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean batchUpdateUserRegistrationRecordsStatus(List<UserRegistrationRecordDO> userRegistrationRecordList,
            UserRegistrationStatusEnum statusEnum) {
        if (CollectionUtils.isEmpty(userRegistrationRecordList)) {
            return false;
        }
        //将资格状态更新为有资格
        userRegistrationRecordList.forEach(userRegistration -> userRegistration.setStatus(statusEnum.getCode()));
        //更新数据库记录
        userRegistrationRecordList.forEach(record -> {
            long count = userRegistrationRecordDAO.updateUserRegistrationRecordById(record);
            if (count <= 0) {
                log.error("[用户资格状态更新] 更新失败，userId:{}, entityId:{}", record.getUserId(),
                        record.getEntityId());
                throw new BizException(BasicErrorCode.UPDATE_FAIL, "用户资格状态更新失败");
            }
        });
        return true;
    }

    @Override
    public int cancelUserRegistrationRecord(long activityId, Long userId) {
        if (activityId < 1 || userId < 1) {
            return 0;
        }
        return userRegistrationRecordDAO.cancelRegistrationByActivityId(userId, activityId);
    }


    @Override
    public UserRegistrationRecordBO queryUserRegistrationRecord(long userId, Long activityId,
            EntityTypeEnum entityType, Long entityId) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, entityType, entityId,
                        userId, null);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return null;
        }
        // 目前一个用户在一个实体下只有一条报名记录
        if (userRegistrationRecords.size() > 1) {
            log.error("User exists multi user registration record, size:{}, params:{}", userRegistrationRecords.size(),
                    Lists.newArrayList(activityId, userId, entityType, entityId));
            throw new BizException(ActivityExceptionCode.CAN_NOT_FIND_RECORD, "用户报名数据异常");
        }
        return registrationConverter.convertToUserRecordBO(userRegistrationRecords.get(0));
    }

    @Override
    public Map<Long, UserRegistrationRecordBO> batchQueryUserRegistrationRecords(Collection<Long> userIds,
            Long activityId, EntityTypeEnum entityType, Long entityId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.batchQueryUserRegistrationRecords(activityId, entityType,
                        entityId, userIds, null);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Maps.newHashMap();
        }
        return userRegistrationRecords.stream().map(registrationConverter::convertToUserRecordBO)
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getUserId(), v), Map::putAll);
    }

    @Override
    public Map<Long, UserRegistrationRecordBO> batchQueryUserRegistrationRecords(Long userId, Long activityId,
            EntityTypeEnum entityType, Collection<Long> entityIds) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.batchQueryUserRegistrationRecords(activityId, entityType,
                        entityIds, userId, VALID, false);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Maps.newHashMap();
        }
        return userRegistrationRecords.stream().map(registrationConverter::convertToUserRecordBO)
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getEntityId(), v), Map::putAll);
    }

    @Override
    public Map<Long, AddRegistrationOptionBO> batchQueryUserRegistrationRecordOption(Long userId, Long activityId,
            EntityTypeEnum entityType, Collection<Long> entityIds, boolean readMaster) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.batchQueryUserRegistrationRecords(activityId, entityType,
                        entityIds, userId, VALID, readMaster);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Maps.newHashMap();
        }
        return userRegistrationRecords.stream()
                .collect(Collectors.toMap(UserRegistrationRecordDO::getEntityId,
                        e -> registrationConverter.convertToUserRegistrationOption(e)));
    }


    @Override
    public List<UserRegistrationRecordBO> queryUserRegistrationRecords(long userId, Long activityId,
            EntityTypeEnum entityType, UserRegistrationStatusEnum registrationStatus) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, entityType, null,
                        userId, registrationStatus);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserAllActivityRegistrationRecords(long userId) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(null, ACTIVITY, null, userId, VALID);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserAllRegistrationRecords(long userId, UserRegistrationStatusEnum registrationStatus) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(null, null, null, userId, registrationStatus);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserRecordsOfMultiActivity(List<Long> activityIds, long userId) {
        if (CollectionUtils.isEmpty(activityIds) || userId <= 0L) {
            return Lists.newArrayList();
        }
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.listUserRecordsOfMultiActivity(activityIds, userId);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserAllRegistrationRecords(long userId, EntityTypeEnum entityType,
            UserRegistrationStatusEnum registrationStatus) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(null, entityType, null, userId,
                        registrationStatus);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserActivityRegistrationRecords(long userId, long activityId,
           UserRegistrationStatusEnum registrationStatus) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, null, null, userId, registrationStatus);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    public List<UserRegistrationRecordBO> queryUserAllStatusRegistrationRecords(long userId) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.queryUserRegistrationRecords(null, ACTIVITY, null, userId, null);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        return userRegistrationRecords.stream()
                .map(registrationConverter::convertToUserRecordBO).collect(Collectors.toList());
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public long logicDeleteUserRegistrationRecord(Long activityId, EntityTypeEnum entityType, Long entityId,
            Long userId, String operator) {
        log.info("Receive logic del user registration req, param is:{}", Lists.newArrayList(activityId,
                entityType, entityId, userId, operator));
        return userRegistrationRecordDAO.logicDeleteRegistrationRecord(activityId, entityType,
                entityId, userId, operator);
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public long deleteUserRegistrationRecord(Long activityId, EntityTypeEnum entityType, Long entityId, Long userId,
            String operator) {
        log.info("Receive del user registration req, param is:{}", Lists.newArrayList(activityId,
                entityType, entityId, userId, operator));
        return userRegistrationRecordDAO.deleteRegistrationRecord(activityId, entityType,
                entityId, userId);
    }

    @Override
    public void handleUserRegistrationRecordChangeEvent(CommonDataChangeEvent<UserRegistrationRecordDO> changeEvent) {
        ValidationUtils.validate(changeEvent);
        if (sendUserRegistrationChangeEvent.isOnFor(changeEvent.getAfterData().getUserId())) {
            sendUserRegistrationRecordChangeStatusMessage(changeEvent.getBeforeData(),
                    changeEvent.getAfterData(), changeEvent.getChangeEventType());
        }
        if (sendUserRegistrationEvent.isOnFor(changeEvent.getAfterData().getUserId())) {
            sendUserRegistrationEventMessage(changeEvent.getBeforeData(), changeEvent.getAfterData(),
                    changeEvent.getChangeEventType());
        }
        StopWatch cost = new StopWatch();
        try {
            // 对用户报名记录的缓存进行更新
            updateUserRegistrationCache(changeEvent);
            perfSuccessWithWatch(REGISTRATION_UPDATE_CACHE, cost);
        } catch (Exception e) {
            perfExceptionWithWatch(REGISTRATION_UPDATE_CACHE,
                    String.valueOf(ResponseUtils.getResultCode(e)), ResponseUtils.getResultMsg(e), cost);
            log.error("[更新资格缓存] 异常，事件内容:{}, error msg:", toJSON(changeEvent), e);
        }
    }

    @Override
    public void saveUserRegistrationCache(UserRegistrationCacheBO userRegistrationCache) {
        if (userRegistrationCache == null) {
            return;
        }
        String cacheKey = CacheKeyEnum.USER_REGISTRATION_CACHE.buildCacheKey(userRegistrationCache.getActivityId(),
                userRegistrationCache.getUserId());
        Map<String, String> registrationRecordMap = Maps.newHashMap();
        registrationRecordMap.put(String.valueOf(userRegistrationCache.getEntityId()),
                toJSON(userRegistrationCache));
        RedisDataSource.getGrowthRedisCommands().hmset(cacheKey, registrationRecordMap);
        // 根据实体类型来获取缓存有效时间
        long expireTime = getUserRegistrationCacheExpireTime(userRegistrationCache);
        RedisDataSource.getGrowthRedisCommands().expire(cacheKey, expireTime);
    }

    @Override
    public List<UserRegistrationCacheBO> getUserRegistrationCache(long userId, long activityId) {
        String cacheKey = CacheKeyEnum.USER_REGISTRATION_CACHE.buildCacheKey(activityId, userId);
        Map<String, String> registrationCache = RedisDataSource.getGrowthRedisCommands().hgetall(cacheKey);
        if (MapUtils.isEmpty(registrationCache)) {
            return null;
        }
        return registrationCache.values().stream()
                .map(a -> ObjectMapperUtils.fromJSON(a, UserRegistrationCacheBO.class))
                .collect(Collectors.toList());
    }

    @Override
    public UserRegistrationCacheBO getUserRegistrationCache(long userId, long activityId, Long entityId) {
        String cacheKey = CacheKeyEnum.USER_REGISTRATION_CACHE.buildCacheKey(activityId, userId);
        String result = RedisDataSource.getGrowthRedisCommands().hget(cacheKey, String.valueOf(entityId));
        return ObjectMapperUtils.fromJSON(result, UserRegistrationCacheBO.class);
    }

    @Override
    public void deleteUserRegistrationCache(long activityId, long userId, long entityId) {
        String cacheKey = CacheKeyEnum.USER_REGISTRATION_CACHE.buildCacheKey(activityId, userId);
        RedisDataSource.getGrowthRedisCommands().hdel(cacheKey, String.valueOf(entityId));
        log.info("Delete user registration cache success, activity id:{}, user id:{}, entity id:{}",
                activityId, userId, entityId);
    }

    @Override
    public Stream<UserRegistrationRecordDO> cursorGetRegistrationRecord(int shardIndex, long activityId) {
        return CursorIterator.<Long, UserRegistrationRecordDO> newGenericBuilder()
                .bufferSize(cursorGetRegistrationRecordBufferSize.get())
                .start(cursorGetRegistrationRecordStartId.get())
                .cursorExtractor(UserRegistrationRecordDO::getId)
                .buildEx((cursor, limit) -> userRegistrationRecordDAO
                        .cursorGetUserRegistrationRecords(shardIndex, cursor, activityId, limit))
                .stream();
    }

    @Override
    public Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByRegistrationTime(int shardIndex,
            long registrationTime, int bufferSize) {
        return CursorIterator.<Long, UserRegistrationRecordDO> newGenericBuilder()
                .bufferSize(bufferSize)
                .start(cursorGetRegistrationRecordStartId.get())
                .cursorExtractor(UserRegistrationRecordDO::getId)
                .buildEx((cursor, limit) -> {
                    log.info("cursorGetRegistrationRecordByRegistrationTime shardIndex {} cursor {} limit {} "
                            + "registrationTime {}", shardIndex, cursor, limit, registrationTime);
                   return userRegistrationRecordDAO
                            .cursorGetUserRegistrationRecordsByRegistrationTime(shardIndex, cursor, registrationTime,
                                    limit);
                })
                .stream();
    }

    @Override
    public Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByRegistrationTime(int shardIndex,
            long registrationTime, int bufferSize, long cursorStartId) {
        return CursorIterator.<Long, UserRegistrationRecordDO> newGenericBuilder()
                .bufferSize(bufferSize)
                .start(cursorStartId)
                .cursorExtractor(UserRegistrationRecordDO::getId)
                .buildEx((cursor, limit) -> userRegistrationRecordDAO
                        .cursorGetUserRegistrationRecordsByRegistrationTime(shardIndex, cursor, registrationTime,
                                limit))
                .stream();
    }

    @Override
    public Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByParentTaskId(int shardIndex, long activityId,
            long entityId, int bufferSize, long cursorStartId) {
        return CursorIterator.<Long, UserRegistrationRecordDO> newGenericBuilder()
                .bufferSize(bufferSize)
                .start(cursorStartId)
                .cursorExtractor(UserRegistrationRecordDO::getId)
                .buildEx((cursor, limit) -> userRegistrationRecordDAO
                        .queryPageListByCursorAndStrategyIdAndStatus(activityId, entityId, null,
                                limit, cursor, shardIndex))
                .stream();
    }

    @Override
    public Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByEntityTypeAndStatus(int shardIndex,
            long activityId, EntityTypeEnum entityType, UserRegistrationStatusEnum registrationStatus, int bufferSize,
            long cursorStartId) {
        return CursorIterator.<Long, UserRegistrationRecordDO> newGenericBuilder()
                .bufferSize(bufferSize)
                .start(cursorStartId)
                .cursorExtractor(UserRegistrationRecordDO::getId)
                .buildEx((cursor, limit) -> userRegistrationRecordDAO
                        .cursorGetUserRegistrationRecords(activityId, entityType, registrationStatus,
                                limit, cursor, shardIndex))
                .stream();
    }

    @Override
    public void coverUserBasicData(long userId, long activityId, Map<String, Object> inputBasicData) {
        // 先查后更新
        List<UserRegistrationRecordDO> originRecordList =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, ACTIVITY, activityId, userId, VALID);
        if (CollectionUtils.isEmpty(originRecordList) || originRecordList.size() != 1) {
            log.error("[覆盖用户基值] 查询不到用户报名记录！userId:{}, activityId:{}", userId, activityId);
            return;
        }
        // 获取原基值
        UserRegistrationRecordDO originRecord = originRecordList.get(0);
        Map<String, Object> jsonData = Maps.newHashMap();
        if (StringUtils.isNotBlank(originRecord.getJsonData())) {
            jsonData = fromJson(originRecord.getJsonData());
        }
        // 原基值数据放入ext
        UserRegistrationRecordExtBO ext = new UserRegistrationRecordExtBO();
        if (StringUtils.isNotBlank(originRecord.getExt())) {
            ext = fromJSON(originRecord.getExt(), UserRegistrationRecordExtBO.class);
        }
        if (MapUtils.isEmpty(ext.getOriginBasicData()) && MapUtils.isNotEmpty(jsonData)) {
            ext.setOriginBasicData(jsonData);
        }
        // 将原基值中，新基值不想覆盖的数据取出
        jsonData.forEach((k, v) -> {
            if (!inputBasicData.containsKey(k)) {
                inputBasicData.put(k, v);
            }
        });
        // 更新对象
        UserRegistrationRecordDO updateParam = new UserRegistrationRecordDO();
        updateParam.setId(originRecord.getId());
        updateParam.setUserId(originRecord.getUserId());
        updateParam.setJsonData(toJSON(inputBasicData));
        updateParam.setVersion(originRecord.getVersion());
        updateParam.setExt(toJSON(ext));
        long updateNum = userRegistrationRecordDAO.updateUserRegistrationRecordById(updateParam);
        if (updateNum <= 0) {
            log.error("[覆盖用户基值] 用户报名记录更新失败！userId:{}, activityId:{}", userId, activityId);
        } else {
            log.info("[覆盖用户基值] 用户自定义基值成功，userId:{}, activityId:{}. inputBasicData:{}", userId,
                    activityId, toJSON(inputBasicData));
        }
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public void batchInsertRegistrationRecords(List<UserRegistrationRecordDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        saveActivityRegistrationRecords(records);
        saveTaskRegistrationRecords(records);
    }

    @Override
    public Roaring64NavigableMap queryActivityTotalUnRiskUserIds(long activityId) {
        Roaring64NavigableMap bitmap = new Roaring64NavigableMap();
        int mysqlShardCount = userRegistrationRecordDAO.getMysqlShardCount();
        // 分片查询资格
        List<Integer> shardList = IntStream.range(0, mysqlShardCount).boxed().collect(Collectors.toList());
        shardList.forEach(shareIndex -> {
            Integer pageSize = queryRegistrationRecordLimit.get();
            Integer startCursor = queryRegistrationRecordCursor.get();
            this.cursorGetRegistrationRecordByEntityTypeAndStatus(shareIndex, activityId, ACTIVITY, VALID,
                    pageSize, startCursor).map(UserRegistrationRecordDO::getUserId).forEach(bitmap::add);
        });
        return bitmap;
    }

    /**
     * 对用户报名信息进行缓存
     */
    private void updateUserRegistrationCache(CommonDataChangeEvent<UserRegistrationRecordDO> changeEvent) {
        UserRegistrationRecordDO afterData = changeEvent.getAfterData();
        // 删除用户的报名记录缓存
        if (checkNeedDeleteUserRegistration(changeEvent)) {
            deleteUserRegistrationCache(afterData.getActivityId(), afterData.getUserId(), afterData.getEntityId());
            return;
        }
        UserRegistrationCacheBO cache = UserRegistrationCacheBO.builder()
                .userId(afterData.getUserId())
                .activityId(afterData.getActivityId())
                .entityId(afterData.getEntityId())
                .entityType(afterData.getEntityType())
                .registrationTime(afterData.getRegistrationTime())
                .jsonData(afterData.getJsonData())
                .build();
        // 更新或者保存缓存
        saveUserRegistrationCache(cache);
    }

    /**
     * 发送用户报名记录状态变更消息
     */
    private void sendUserRegistrationRecordChangeStatusMessage(UserRegistrationRecordDO oldRegistrationRecord,
            UserRegistrationRecordDO newRegistrationRecord, DataChangeEventTypeEnum changeType) {
        if (newRegistrationRecord == null) {
            return;
        }
        if (newRegistrationRecord.getStatus().equals(UserRegistrationStatusEnum.RISK.getCode())) {
            perfScene(REGISTRATION_UPDATE_CACHE, String.valueOf(newRegistrationRecord.getActivityId()), "risk.filter");
            return;
        }
        UserRegistrationRecordStatusChangeEvent changeEvent = UserRegistrationRecordStatusChangeEvent.newBuilder()
                .setUpdateTime(newRegistrationRecord.getUpdateTime())
                .setActivityId(newRegistrationRecord.getActivityId())
                .setEntityId(newRegistrationRecord.getEntityId())
                .setUserId(newRegistrationRecord.getUserId())
                .setOldRecordStatus(oldRegistrationRecord == null ? 0 : oldRegistrationRecord.getStatus())
                .setNewRecordStatus(newRegistrationRecord.getStatus())
                .setDeleted(newRegistrationRecord.getDeleted())
                .setChangeType(changeType.getCode())
                .setEntityType(newRegistrationRecord.getEntityType())
                .build();
        // 营销侧在收到报名成功消息后会弹出挂件
        MqMessage eventMsg = userRegistrationRecordStatusChangeProducer.createMsgBuilder(changeEvent).build();
        MqSyncSendResult sendResult = userRegistrationRecordStatusChangeProducer.sendSync(eventMsg);
        if (!sendResult.isSuccess()) {
            log.error("Send user registration record change failed, result code:{}, error msg",
                    sendResult.getResultCode(), sendResult.getException());
            throw new BizException(ActivityExceptionCode.NETWORK_ERROR, "发送用户报名记录状态变更消息失败!");
        }
    }

    /**
     * 发送用户报名事件消息
     */
    private void sendUserRegistrationEventMessage(UserRegistrationRecordDO oldRegistrationRecord,
            UserRegistrationRecordDO newRegistrationRecord, DataChangeEventTypeEnum changeType) {
        if (newRegistrationRecord == null) {
            return;
        }
        // 判断事件
        RegistrationEventTypeEnum eventTypeEnum = buildEventType(newRegistrationRecord);
        if (eventTypeEnum == RegistrationEventTypeEnum.UNKNOWN) {
            return;
        }
        UserRegistrationEventMsg userRegistrationEventMsg = UserRegistrationEventMsg.newBuilder()
                .setUserId(newRegistrationRecord.getUserId())
                .setActivityId(newRegistrationRecord.getActivityId())
                .setEntityType(newRegistrationRecord.getEntityType())
                .setEntityId(newRegistrationRecord.getEntityId())
                .setEventTime(newRegistrationRecord.getUpdateTime())
                .setEventType(eventTypeEnum.getValue())
                .setOldStatus(oldRegistrationRecord == null ? 0 : oldRegistrationRecord.getStatus())
                .setNewStatus(newRegistrationRecord.getStatus())
                .setChangeType(changeType.getCode())
                .build();
        MqMessage eventMsg = userRegistrationEventProducer.createMsgBuilder(userRegistrationEventMsg).build();
        MqSyncSendResult sendResult = userRegistrationEventProducer.sendSync(eventMsg);
        if (!sendResult.isSuccess()) {
            log.error("[发送用户资格事件] 失败resultCode:{}", sendResult.getResultCode(), sendResult.getException());
            perfException(REGISTRATION_UPDATE_CACHE, String.valueOf(newRegistrationRecord.getActivityId()), "eventMsg");
            throw new BizException(ActivityExceptionCode.NETWORK_ERROR, "发送用户资格事件消息失败!");
        }
        perfSuccess(REGISTRATION_UPDATE_CACHE, String.valueOf(newRegistrationRecord.getActivityId()), "eventMsg");
    }

    private RegistrationEventTypeEnum buildEventType(UserRegistrationRecordDO newRegistrationRecord) {
        // 失败态转变的都不发送
        int status = newRegistrationRecord.getStatus();
        switch (UserRegistrationStatusEnum.getByCode(status)) {
            case VALID:
                return RegistrationEventTypeEnum.VALID_EVENT;
            case INVALID:
                return RegistrationEventTypeEnum.INVALID_EVENT;
            case RISK:
                return RegistrationEventTypeEnum.RISK_EVENT;
            default:
                return RegistrationEventTypeEnum.UNKNOWN;
        }
    }

    /**
     * 根据实体类型返回用户报名记录缓存的有效时间
     */
    private long getUserRegistrationCacheExpireTime(UserRegistrationCacheBO userRegistrationCache) {
        if (EntityTypeEnum.STRATEGY.getCode().equals(userRegistrationCache.getEntityType())) {
            // 策略报名用户量不大，且存在周期比较长，目前设置6个月
            return MapUtils.getLong(cacheExpireTimeConfig.getMap(), CacheKeyEnum.USER_REGISTRATION_CACHE.getCode(),
                    CacheKeyEnum.USER_REGISTRATION_CACHE.getDefaultExpireTime());
        }
        // 任务如果不需要这么长时间后面可在这里扩展
        return CacheKeyEnum.USER_REGISTRATION_CACHE.getDefaultExpireTime();
    }

    /**
     * 判断是否需要删除资格缓存,删除记录或者取消资格等场景需要删除
     */
    private boolean checkNeedDeleteUserRegistration(CommonDataChangeEvent<UserRegistrationRecordDO> changeEvent) {
        // 物理删除
        if (DataChangeEventTypeEnum.DATA_DELETE.equals(changeEvent.getChangeEventType())) {
            return true;
        }
        // 逻辑删除
        if (changeEvent.getAfterData().getDeleted() == 1) {
            return true;
        }
        // 对应插入事件
        if (changeEvent.getBeforeData() == null) {
            return UserRegistrationStatusEnum.INVALID.getCode().equals(changeEvent.getAfterData().getStatus())
                    || UserRegistrationStatusEnum.RISK.getCode().equals(changeEvent.getAfterData().getStatus());
        }
        // 从非无效状态，变成了无效状态需要删除
        if (!UserRegistrationStatusEnum.INVALID.getCode().equals(changeEvent.getBeforeData().getStatus())
                && UserRegistrationStatusEnum.INVALID.getCode().equals(changeEvent.getAfterData().getStatus())) {
            return true;
        }
        // 任务状态变为风控态，需要删除
        if (UserRegistrationStatusEnum.RISK.getCode().equals(changeEvent.getAfterData().getStatus())) {
            return true;
        }
        return false;
    }

    /**
     * 保存活动维度的资格
     * 如果出现幂等，则不保存
     * 解决每日人群追加并发保存的问题
     * @param records
     */
    private void saveActivityRegistrationRecords(List<UserRegistrationRecordDO> records) {
        try {
            List<UserRegistrationRecordDO> activityRegistrationRecords =
                    records.stream().filter(record -> record.getEntityType().equals(ACTIVITY.getCode()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activityRegistrationRecords)) {
                return;
            }
            userRegistrationRecordDAO.batchInsertUserRegistrationRecord(activityRegistrationRecords);
        } catch (DataIntegrityViolationException e) {
            log.warn("[追加用户] 资格记录插入幂等！msg:{}", toJSON(records));
            perfSuccess(ADd_DRAW_SIGN_UP_ACTIVITY, "duplicate", e.getClass().getSimpleName());
        } catch (BizException e) {
            log.error("[追加用户] 资格记录插入失败！msg:{}", toJSON(records), e);
            perfFail(ADd_DRAW_SIGN_UP_ACTIVITY, "insertAfterRetry", e.getClass().getSimpleName());
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        } catch (Exception e) {
            log.error("[追加用户] 资格记录插入失败！msg:{}", toJSON(records), e);
            perfException(ADd_DRAW_SIGN_UP_ACTIVITY, "insertException", e.getClass().getSimpleName());
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }

    /**
     * 保存任务维度的资格
     * @param records
     */
    private void saveTaskRegistrationRecords(List<UserRegistrationRecordDO> records) {
        try {
            List<UserRegistrationRecordDO> taskRegistrationRecords =
                    records.stream().filter(record -> record.getEntityType().equals(TASK.getCode()))
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(taskRegistrationRecords)) {
                return;
            }
            userRegistrationRecordDAO.batchInsertUserRegistrationRecord(taskRegistrationRecords);
        } catch (BizException e) {
            log.error("[追加用户] task资格记录插入失败！msg:{}", toJSON(records), e);
            perfFail(ADd_DRAW_SIGN_UP_ACTIVITY, "insertAfterRetry", e.getClass().getSimpleName());
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        } catch (Exception e) {
            log.error("[追加用户] task资格记录插入失败！msg:{}", toJSON(records), e);
            perfException(ADd_DRAW_SIGN_UP_ACTIVITY, "insertException", e.getClass().getSimpleName());
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }
}
