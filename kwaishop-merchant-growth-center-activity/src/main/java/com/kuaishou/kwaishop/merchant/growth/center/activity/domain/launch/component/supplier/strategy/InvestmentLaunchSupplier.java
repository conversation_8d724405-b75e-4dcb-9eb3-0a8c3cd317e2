package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.supplier.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchInvestmentActivityInfoBO.convertLaunchInvestmentActivityInfoBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType.SUPPLIER_INVESTMENT_STRATEGY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCacheConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityJsonMapConfigKey.investmentActivityInfoMockConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.supplier.LaunchSupplier;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.InvestmentLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.InvestmentLaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchInvestmentActivityInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCacheConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.ActivityInvestmentReportFetchService;
import com.kuaishou.protobuf.plateco.kwaishop.activity.investment.reverse.ActivityModuleForNewTabVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-08
 */
@Component
@Slf4j
public class InvestmentLaunchSupplier implements LaunchSupplier<InvestmentLaunchInfoFetchContext, InvestmentLaunchInfoResult> {

    @Autowired
    private ActivityInvestmentReportFetchService activityInvestmentReportFetchService;

    @Resource
    private LaunchDomainService launchDomainService;

    @Override
    public void supply(InvestmentLaunchInfoFetchContext context) {
        supplyLaunchConfigInfo(context);
        supplyUserActivityInfo(context);
    }

    @Override
    public LaunchPipeHandlerType getType() {
        return SUPPLIER_INVESTMENT_STRATEGY;
    }

    protected void supplyUserActivityInfo(InvestmentLaunchInfoFetchContext context) {
        List<Long> launchInvestmentActivityIds = context.getInvestmentActivityIds();
        Long userId = context.getUserId();
        if (CollectionUtils.isEmpty(launchInvestmentActivityIds)) {
            interruptAndReturn(context);
            return;
        }

        List<LaunchInvestmentActivityInfoBO> mockResult =
                buildMockInvestmentActivityInfo(userId, launchInvestmentActivityIds);
        if (CollectionUtils.isNotEmpty(mockResult)) {
            log.info("[用户招商活动信息] mock数据, userId:{}", userId);
            context.setUserInvestmentActivityInfos(mockResult);
            return;
        }

        List<LaunchInvestmentActivityInfoBO> userInvestmentActivityInfos = Lists.newArrayList();
        List<ActivityModuleForNewTabVO> activityModuleForNewTabVOS =
                activityInvestmentReportFetchService.getUserInvestmentActivityInfo(userId, launchInvestmentActivityIds);

        if (CollectionUtils.isNotEmpty(activityModuleForNewTabVOS)) {
            userInvestmentActivityInfos = activityModuleForNewTabVOS.stream()
                    .map(LaunchInvestmentActivityInfoBO::convertLaunchInvestmentActivityInfoBO)
                    .collect(Collectors.toList());
        }
        context.setUserInvestmentActivityInfos(userInvestmentActivityInfos);
    }

    protected void supplyLaunchConfigInfo(InvestmentLaunchInfoFetchContext context) {
        String channel = context.getChannel();
        String scene = context.getScene();
        LaunchCacheConfigBO cacheConfig = launchCacheConfig.getObject();

        // 查询投放配置
        List<LaunchConfigBO> launchConfigList = ListUtils.emptyIfNull(launchDomainService
                .queryLaunchConfigListByScene(LaunchConfigResourceTypeEnum.INVESTMENT_ACTIVITY.getType(),
                        channel, scene, cacheConfig.getQueryLaunchConfigWithCache()));
        if (CollectionUtils.isEmpty(launchConfigList)) {
            interruptAndReturn(context);
            return;
        }

        // 活动维度投放配置
        Map<Long/*activityId*/, List<LaunchConfigBO>> launchConfigActivityMap =
                launchConfigList.stream().collect(Collectors.groupingBy(LaunchConfigBO::getActivityId));
        // 招商活动ID
        context.setInvestmentActivityIds(new ArrayList<>(launchConfigActivityMap.keySet()));
        context.setLaunchConfigActivityMap(launchConfigActivityMap);
    }

    private List<LaunchInvestmentActivityInfoBO> buildMockInvestmentActivityInfo(Long userId, List<Long> investmentActivityIds) {
        Map<String, LaunchInvestmentActivityInfoBO> userMockMap = investmentActivityInfoMockConfig.getMap();
        if (MapUtils.isEmpty(userMockMap) || !userMockMap.containsKey(String.valueOf(userId))) {
            return Lists.newArrayList();
        }
        LaunchInvestmentActivityInfoBO template = userMockMap.get(String.valueOf(userId));
        if (null == template) {
            return Lists.newArrayList();
        }
        return investmentActivityIds.stream().map(investmentActivityId -> LaunchInvestmentActivityInfoBO.builder()
                .activityId(investmentActivityId)
                .activityName(template.getActivityName())
                .iconUrl(template.getIconUrl())
                .pcUrl(template.getPcUrl())
                .webUrl(template.getWebUrl())
                .activityType(template.getActivityType())
                .activityStartTime(template.getActivityStartTime())
                .activityEndTime(template.getActivityEndTime()).build())
                .collect(Collectors.toList());
    }
}
