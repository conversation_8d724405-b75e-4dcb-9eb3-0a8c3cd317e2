package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeFenUnitAwardValueToYuan;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.SpecifyIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardValueBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.award.AwardConfigFixValueType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.StepShowProgressResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.TaskShowProgressCalculator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.AwardConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.AwardSelectionConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.UserTaskRecordExtFieldBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.AwardConditionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-25
 */
@Slf4j
@Service
public class AwardRuleExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private AwardCalcService awardCalcService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private TaskShowProgressCalculator taskShowProgressCalculator;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.AWARD_RULE_DESC, TemplateParamTypeEnum.ACTIVITY_DRAW_DECR);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result =
                NotificationExtendFunctionParamBO.builder().templateParamMap(Maps.newHashMap()).executeStatus(EXECUTE)
                        .build();
        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[获取奖励规则]触达控制终止，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }
        if (templateParams.contains(TemplateParamTypeEnum.AWARD_RULE_DESC)) {
            String awardTypeStr = queryRelatedAwardConfigRule(configBO, userId);
            if (StringUtils.isEmpty(awardTypeStr)) {
                log.warn("[获取奖励规则]奖励规则为空，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
                perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "奖励规则未知");
                result.setExecuteStatus(STOP);
                result.setTemplateParamMap(params);
                return result;
            }
            String awardTypeName = TemplateParamTypeEnum.AWARD_RULE_DESC.getName();
            params.put(awardTypeName, awardTypeStr);
        }
        if (templateParams.contains(TemplateParamTypeEnum.ACTIVITY_DRAW_DECR)) {
            String drawDescStr = queryDrawDesc(configBO, userId);
            if (StringUtils.isEmpty(drawDescStr)) {
                log.warn("[获取奖励规则]报名描述为空，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
                perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "报名文案未知");
                result.setExecuteStatus(STOP);
                result.setTemplateParamMap(params);
                return result;
            }
            String drawDesc = TemplateParamTypeEnum.ACTIVITY_DRAW_DECR.getName();
            params.put(drawDesc, drawDescStr);
        }
        log.info("[奖励规则配置获取]参数：{},用户ID:{},活动id:{}", toJSON(params), userId, configBO.getActivityId());
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    private String queryDrawDesc(NotificationPushConfigBO configBO, Long userId) {
        UserActivityRecordDO userActivity =
                userActivityRecordDAO.queryUserActivityRecord(userId, configBO.getActivityId(), false);
        Map<String, String> textMap = Maps.newHashMap();
        NotificationParamControlBO control = getParamControl();
        if (control != null && MapUtils.isNotEmpty(textMap)) {
            textMap = control.getText();
        }
        if (userActivity == null) {
            return MapUtils.getString(textMap, "undrawText", "去报名");
        }
        return MapUtils.getString(textMap, "drawText", "去查看");
    }


    private String queryRelatedAwardConfigRule(NotificationPushConfigBO configBO, Long userId) {
        long activityId = configBO.getActivityId();
        ActivityDO activity = activityLocalCacheService.queryActivityInfo(activityId);
        if (activity == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动不存在");
        }
        Long parentTaskId = null;
        switch (NotificationEntityTypeEnum.of(configBO.getEntityType())) {
            case AWARD:
                AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
                if (awardConfigDO == null) {
                    break;
                }
                Map<Long, List<AwardConfigDO>> awardConfigMap = Maps.newHashMap();
                awardConfigMap.put(awardConfigDO.getEntityId(), Lists.newArrayList(awardConfigDO));
                TaskDO childTask = taskLocalCacheService.getTaskByTaskId(awardConfigDO.getEntityId());
                if (childTask == null) {
                    break;
                }
                UserRegistrationRecordDO userRegistration =
                        userRegistrationRecordDAO.queryRecordByUnique(activityId, userId, EntityTypeEnum.TASK,
                                childTask.getParentTask(), false);
                return resolveAwardRule(userId, activity, awardConfigMap, userRegistration,
                        Lists.newArrayList(childTask), "");
            case ACTIVITY:
                List<UserRegistrationRecordDO> userRegistrations =
                        userRegistrationRecordDAO.queryUserRegistrationRecords(configBO.getActivityId(),
                                EntityTypeEnum.TASK, null, userId, UserRegistrationStatusEnum.VALID);
                if (CollectionUtils.isEmpty(userRegistrations)) {
                    return StringUtils.EMPTY;
                }
                // 多子活动后续需求需要改动
                parentTaskId = userRegistrations.get(0).getEntityId();
                break;
            case TASK:
                TaskDO entityTask = taskLocalCacheService.getTaskByTaskId(configBO.getEntityId());
                if (entityTask == null) {
                    throw new BizException(BasicErrorCode.PARAM_INVALID, "实体不存在");
                }
                parentTaskId = entityTask.getParentTask() == 0 ? entityTask.getId() : entityTask.getParentTask();
                break;
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "不支持的实体类型");
        }
        if (parentTaskId == null) {
            return StringUtils.EMPTY;
        }

        List<TaskDO> childTasks = taskLocalCacheService.getChildTask(parentTaskId);
        long currentTime = System.currentTimeMillis();
        Map<Integer, List<TaskDO>> childTaskMap =
                childTasks.stream().collect(Collectors.groupingBy(TaskDO::getPriority));
        if (!childTaskMap.containsKey(1)) {
            log.warn("[获取奖励配置]任务配置无第一优先级任务，activityId:{},userId:{}", activityId, userId);
            return StringUtils.EMPTY;
        }
        List<TaskDO> cycleTasks;
        // 周期任务
        if (childTaskMap.keySet().size() > 1) {
            TaskDO currentTask = childTasks.stream()
                    .filter(e -> e.getStartTime() <= currentTime && e.getEndTime() > currentTime)
                    .findFirst()
                    .orElse(null);
            if (currentTask == null) {
                return StringUtils.EMPTY;
            }
            cycleTasks = childTaskMap.get(currentTask.getPriority());
        } else {
            cycleTasks = childTaskMap.get(1);
        }
        if (CollectionUtils.isEmpty(cycleTasks)) {
            log.warn("[获取奖励配置]无法锁定对应周期任务，activityId:{},userId:{}", activityId, userId);
            return StringUtils.EMPTY;
        }
        List<Long> cycleTaskIds = cycleTasks.stream().map(TaskDO::getId).collect(Collectors.toList());
        List<AwardConfigDO> awardConfigList =
                awardConfigLocalCacheService.queryMultiTaskAwardConfig(activityId, cycleTaskIds);
        UserRegistrationRecordDO userRegistration =
                userRegistrationRecordDAO.queryRecordByUnique(activityId, userId, EntityTypeEnum.TASK, parentTaskId,
                        false);
        if (CollectionUtils.isEmpty(awardConfigList)) {
            log.warn("[获取奖励配置]缺失奖励，activityId:{},userId:{}", activityId, userId);
            return StringUtils.EMPTY;
        }
        String awardCondition = cycleTasks.get(0).getAwardCondition();
        log.info("[获取奖励配置]锁定父任务ID:{},奖励条件:{},userId:{},活动id:{}", parentTaskId, awardCondition, userId,
                activityId);
        Map<Long, List<AwardConfigDO>> awardConfigMap =
                awardConfigList.stream().collect(Collectors.groupingBy(AwardConfigDO::getEntityId));
        return resolveAwardRule(userId, activity, awardConfigMap, userRegistration, cycleTasks, awardCondition);
    }

    public String resolveAwardRule(Long userId, ActivityDO activity, Map<Long, List<AwardConfigDO>> awardConfigMap,
            UserRegistrationRecordDO userTaskRegistration, List<TaskDO> childTasks, String awardCondition) {
        if (MapUtils.isEmpty(awardConfigMap)) {
            log.info("[解析奖励配置]活动无奖励配置，activityId:{},userId:{}", activity.getId(), userId);
            return StringUtils.EMPTY;
        }
        if (userTaskRegistration == null) {
            log.warn("[解析奖励配置]用户无资格，activityId:{},userId:{}", activity.getId(), userId);
            return StringUtils.EMPTY;
        }
        Map<String, Object> baseDataMap =
                ObjectMapperUtils.fromJSON(userTaskRegistration.getJsonData(), Map.class, String.class, Object.class);
        List<AwardValueBO> awardValues = Lists.newArrayList();
        // 获取当前全周期子任务下所有奖励阶梯
        for (TaskDO childTask : childTasks) {
            List<AwardConfigDO> awardConfigList = awardConfigMap.get(childTask.getId());
            if (CollectionUtils.isEmpty(awardConfigList)) {
                continue;
            }
            for (AwardConfigDO awardConfig : awardConfigList) {
                List<AwardValueBO> awardValueList =
                        awardCalcService.calcUserSingleAwardValue(userId, activity, childTask, awardConfig,
                                baseDataMap);
                if (CollectionUtils.isEmpty(awardValueList)) {
                    continue;
                }
                awardValues.addAll(awardValueList);
            }
        }
        if (CollectionUtils.isEmpty(awardValues)) {
            log.warn("[解析奖励配置]缺失奖励参数，奖励条件:{},userId:{},活动id:{}", awardCondition, userId,
                    activity.getId());
            return StringUtils.EMPTY;
        }
        int maxStep = 1;
        // 解析当前锁定奖励周期
        for (AwardValueBO awardValue : awardValues) {
            maxStep = Math.max(maxStep, awardValue.getStep());
        }
        log.info("[解析奖励配置]奖励属性:{},最高阶梯:{},奖励条件:{},userId:{},活动id:{}", awardValues, maxStep,
                awardCondition, userId, activity.getId());
        Map<Integer, List<AwardValueBO>> awardStepMap =
                awardValues.stream().collect(Collectors.groupingBy(AwardValueBO::getStep));
        UserActivityRecordDO userActivity =
                userActivityRecordDAO.queryUserActivityRecord(userId, activity.getId(), false);
        List<AwardValueBO> showValueList = awardStepMap.get(maxStep);
        if (userActivity != null) {
            int currentStep = getCurrentIndicatorStep(userId, activity.getId(), childTasks.get(0).getId(), maxStep);
            showValueList = awardStepMap.get(currentStep);
        }
        if (CollectionUtils.isEmpty(showValueList)) {
            log.warn("[解析奖励配置]缺失奖励结果，奖励条件:{},userId:{},活动id:{}", awardCondition, userId,
                    activity.getId());
            return StringUtils.EMPTY;
        }
        // 获取连接符号
        Map<String, String> textMap = Maps.newHashMap();
        NotificationParamControlBO control = getParamControl();
        if (control != null && MapUtils.isNotEmpty(textMap)) {
            textMap = control.getText();
        }
        String joiner = MapUtils.getString(textMap, "joinerAnd", "+");
        List<Integer> selectAwardTypes = Lists.newArrayList();
        if (StringUtils.isNotEmpty(awardCondition)) {
            AwardConditionBO awardConditionBO = fromJSON(awardCondition, AwardConditionBO.class);
            if (AwardConditionEnum.CHOOSE_ONE.getValue() == awardConditionBO.getConditionType()
                    || AwardConditionEnum.MIX.getValue() == awardConditionBO.getConditionType()) {
                joiner = MapUtils.getString(textMap, "joinerOr", "或");
                UserTaskRecordDO userTask =
                        userTaskRecordDAO.queryUserTaskRecord(userId, activity.getId(), childTasks.get(0).getId(),
                                false);
                selectAwardTypes = resolveSelectAwardType(userTask);
            }
        }

        List<String> unitAwardRule = Lists.newArrayList();
        for (AwardValueBO awardValue : showValueList) {
            // 不在已选择奖励内的类型忽略
            if (CollectionUtils.isNotEmpty(selectAwardTypes) && !selectAwardTypes.contains(awardValue.getAwardType())) {
                continue;
            }
            // 固定值奖励
            if (awardValue.getConfigFixType().equals(AwardConfigFixValueType.FIX_AWARD_VALUE.getType())) {
                AwardTypeEnum awardType = AwardTypeEnum.getByCode(awardValue.getAwardType());
                String desc =
                        awardType.getShowName() + changeFenUnitAwardValueToYuan(awardType, awardValue.getAwardValue())
                                + awardType.getUnit();
                unitAwardRule.add(desc);
                continue;
            }
            // 不存在返点奖励，兜底返回
            if (CollectionUtils.isEmpty(awardValue.getSpecifyIndicatorInfos())) {
                continue;
            }
            // 返点奖励大于1 则不拼规则
            if (awardValue.getSpecifyIndicatorInfos().size() > 1) {
                unitAwardRule.add(awardValue.getAwardName());
                continue;
            }
            for (SpecifyIndicatorInfoBO specifyIndicator : awardValue.getSpecifyIndicatorInfos()) {
                unitAwardRule.add(awardValue.getAwardName() + specifyIndicator.getReturnPercent() + "%");
            }
        }

        return StringUtils.join(unitAwardRule, joiner);
    }

    public int getCurrentIndicatorStep(Long userId, Long activityId, Long childTaskId, int maxStep) {
        log.info("[获取用户当前阶段]用户id:{},任务id:{},活动id:{}", userId, childTaskId, activityId);
        StepShowProgressResultBO stepResult =
                taskShowProgressCalculator.calcStepShowProgressResult(userId, activityId, childTaskId);
        if (stepResult == null) {
            return 0;
        }
        return Math.min(stepResult.getCompleteStepNum() + 1, maxStep);

    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals("awardRule")).findFirst().orElse(null);
    }

    private List<Integer> resolveSelectAwardType(UserTaskRecordDO userTask) {
        List<Integer> result = Lists.newArrayList();
        if (userTask == null || StringUtils.isEmpty(userTask.getExt())) {
            return result;
        }
        String ext = userTask.getExt();
        UserTaskRecordExtFieldBO extFieldBO = fromJSON(ext, UserTaskRecordExtFieldBO.class);
        AwardSelectionConfigBO awardSelectionConfig = extFieldBO.getAwardSelectionConfig();
        if (awardSelectionConfig == null) {
            return result;
        }
        List<Long> selectedAward = awardSelectionConfig.getSelectedAwardConfigIds();
        if (CollectionUtils.isEmpty(selectedAward)) {
            return result;
        }
        List<AwardConfigDO> awardTemplates = awardConfigLocalCacheService.queryAwardConfigByIds(selectedAward);
        return awardTemplates.stream().map(AwardConfigDO::getAwardType).collect(Collectors.toList());
    }
}