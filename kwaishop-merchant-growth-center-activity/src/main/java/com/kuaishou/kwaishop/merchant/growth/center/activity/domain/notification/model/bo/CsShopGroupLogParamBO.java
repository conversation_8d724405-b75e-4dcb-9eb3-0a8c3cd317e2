package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 埋点结构
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CsShopGroupLogParamBO {

    /**
     * 按钮名称
     */
    @JsonProperty(value = "button_name")
    private String buttonName;

    /**
     * 内容
     */
    @JsonProperty(value = "message_content")
    private String messageContent;

    /**
     * 消息类型
     */
    @JsonProperty(value = "robot_message_type")
    private Integer robotMessageType;
}
