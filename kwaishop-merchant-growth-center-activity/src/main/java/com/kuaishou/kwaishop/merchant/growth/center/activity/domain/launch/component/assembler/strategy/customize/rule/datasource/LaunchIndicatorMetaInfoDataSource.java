package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchAssemblerConstantBO.LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchIndicatorMetaInfoDataSource implements LaunchDataSource {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private IndicatorConfigDAO indicatorConfigDAO;

    @Resource
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Resource
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Override
    @SuppressWarnings("unchecked")
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();
        Integer entityType = param.getEntityType();
        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(entityType);
        List<Long> entityIds = param.getEntityIds();

        Map<String, Object> resMap = Maps.newHashMap();

        // 获取用户资格信息
        Map<String, Object> customizeParamMap = param.getCustomizeParamMap();
        List<UserRegistrationRecordBO> userRegistrationRecords = (List<UserRegistrationRecordBO>)
                MapUtils.getObject(customizeParamMap, LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS);


        // 资格为空直接返回
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchIndicatorMetaData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        List<IndicatorConfigDO> indicatorConfigs = Lists.newArrayList();
        switch (entityTypeEnum) {
            case SUB_ACTIVITY:
                // 根据子活动ID拉取任务配置
                List<TaskDO> taskList = launchResolveService
                        .resolveRegistrationTaskConfigBySubActivityId(activityId, entityIds, userRegistrationRecords);
                if (CollectionUtils.isEmpty(taskList)) {
                    break;
                }
                List<Long> childTaskIdList = taskList.stream()
                        .filter(task -> task.getParentTask() != 0)
                        .map(TaskDO::getId).collect(Collectors.toList());
                // 任务维度的指标配置
                List<IndicatorConfigDO> taskIndicatorConfigList =
                        indicatorConfigDAO.queryIndicatorConfigList(activityId, EntityTypeEnum.TASK, childTaskIdList);
                indicatorConfigs.addAll(taskIndicatorConfigList);

                List<AwardConfigDO> awardConfigDOS =
                        awardConfigLocalCacheService.queryMultiTaskAwardConfig(activityId, childTaskIdList);
                if (CollectionUtils.isEmpty(awardConfigDOS)) {
                    break;
                }
                List<Long> awardConfigIdList = awardConfigDOS.stream().map(AwardConfigDO::getId).collect(Collectors.toList());
                // 奖励维度的指标配置
                List<IndicatorConfigDO> awardIndicatorConfigList =
                        indicatorConfigDAO.queryIndicatorConfigList(activityId, EntityTypeEnum.AWARD, awardConfigIdList);
                indicatorConfigs.addAll(awardIndicatorConfigList);
                break;
            default:
                indicatorConfigs = indicatorConfigDAO.queryActivityIndicatorConfig(activityId);
                break;
        }

        if (CollectionUtils.isEmpty(indicatorConfigs)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchIndicatorMetaData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        // 查指标元数据
        List<Long> indicatorIds =
                indicatorConfigs.stream().map(IndicatorConfigDO::getIndicatorId).distinct().collect(Collectors.toList());
        Map<Long, IndicatorDO> indicatorDOMap = indicatorLocalCacheService.queryTaskIndicators(indicatorIds);

        if (MapUtils.isEmpty(indicatorDOMap)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchIndicatorMetaData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        LaunchIndicatorMetaData data = LaunchIndicatorMetaData.builder()
                .indicatorList(new ArrayList<>(indicatorDOMap.values()))
                .build();
        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.INDICATOR_META_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchIndicatorMetaData {
        /**
         * 指标信息
         */
        private List<IndicatorDO> indicatorList;

        public static LaunchIndicatorMetaData empty() {
            return LaunchIndicatorMetaData.builder()
                    .indicatorList(Lists.newArrayList())
                    .build();
        }
    }
}
