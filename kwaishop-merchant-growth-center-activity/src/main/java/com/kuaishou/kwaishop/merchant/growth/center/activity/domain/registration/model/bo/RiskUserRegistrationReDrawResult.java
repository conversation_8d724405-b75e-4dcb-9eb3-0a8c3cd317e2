package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.Set;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-14
 */
@Data
@Builder
public class RiskUserRegistrationReDrawResult {

    /**
     * 活动ID
     */
    private String activityId;
    /**
     * 活动ID对应活动资格重新下发的用户
     */
    private Set<String> userIdSet;
}
