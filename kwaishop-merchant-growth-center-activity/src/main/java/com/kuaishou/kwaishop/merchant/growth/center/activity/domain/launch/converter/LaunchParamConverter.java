package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter;

import org.apache.commons.collections4.ListUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchAssembleParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchFilterParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchAssembleParamDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchFilterParamDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-04-08
 */
public class LaunchParamConverter {

    public static DefaultLaunchFilterParamBO convertToFilterParam(LaunchFilterParamDTO filterParamDTO) {
        if (filterParamDTO == null) {
            return DefaultLaunchFilterParamBO.empty();
        }

        return DefaultLaunchFilterParamBO.builder()
                .userEntityStatusList(ListUtils.emptyIfNull(filterParamDTO.getUserEntityStatusListList()))
                .entityAwardTypeList(ListUtils.emptyIfNull(filterParamDTO.getEntityAwardTypeListList()))
                .entityTagList(ListUtils.emptyIfNull(filterParamDTO.getEntityTagListList()))
                .build();
    }

    public static DefaultLaunchAssembleParamBO convertToAssembleParam(LaunchAssembleParamDTO filterParamDTO) {
        if (filterParamDTO == null) {
            return DefaultLaunchAssembleParamBO.empty();
        }

        return DefaultLaunchAssembleParamBO.builder()
                .notAssembleFieldData(filterParamDTO.getNotAssembleFieldData())
                .build();
    }
}
