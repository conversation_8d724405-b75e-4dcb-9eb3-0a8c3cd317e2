package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.QueryIdDataSourceConfig;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-12
 */
@AllArgsConstructor
@Getter
public enum OffsetEventDataSourceTypeEnum {
    QUERY_ID("queryId", QueryIdDataSourceConfig.class);

    private final String type;

    private final Class<?> clazz;

    public static OffsetEventDataSourceTypeEnum getByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return Arrays.stream(OffsetEventDataSourceTypeEnum.values())
                .filter(offsetEventDataSourceTypeEnum -> offsetEventDataSourceTypeEnum.getType().equals(type))
                .findFirst().orElse(null);
    }
}
