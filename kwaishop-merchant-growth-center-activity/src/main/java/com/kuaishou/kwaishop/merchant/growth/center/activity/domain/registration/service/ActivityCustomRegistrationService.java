package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.ParentTaskRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-10
 */
public interface ActivityCustomRegistrationService {

    /**
     * 特殊用户ID过滤
     */
    boolean filter(Long userId, Long activityId);

    /**
     * 写入活动资格表，并返回jsonData
     */
    Map<String, Long> registrationActivity(long userId, long activityId);

    /**
     * 写入任务资格表
     */
    void registrationTask(long userId, long activityId, List<TaskDO> parentTaskDOList,
            Map<String, Long> jsonMap);

    /**
     * 写入资格表活动+任务记录(事务)(基值数据外部输入，内部不处理)
     * 父任务上绑定不同的基值数据
     */
    boolean registrationActivityAndTask(long userId, long activityId,
            List<ParentTaskRegistrationRecordBO> registrationTasks);

    /**
     * 写入资格表活动+任务记录(事务)(基值数据外部输入，内部不处理)
     * 父任务上绑定相同的基值数据
     */
    boolean registrationActivityAndTask(long userId, long activityId, List<TaskDO> parentTaskList,
            Map<String, Object> jsonMap);

    /**
     * 读取 activityDO 中的人群包ID
     */
    long getCrowdId(ActivityDO activityDO);

    /**
     * 校验用户是否在父任务人群中
     */
    boolean inTaskCrowd(long userId, TaskDO taskDO, Map<String, Long> jsonMap);
}
