package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.condition;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationPushJudgeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OperationFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-26
 */
@Service
@Slf4j
@Lazy
public class PromoterOperationPermissionStrategyServiceImpl implements NotificationPushJudgeStrategyService {

    @Autowired
    private OperationFetchService operationFetchService;

    @Override
    public String judgeConditionCode() {
        return "promoterOperationPermission";
    }

    @Override
    public boolean judgePush(long userId, NotificationPushConfigBO configBO) {
        return operationFetchService.checkPromoterOperationPermission(userId);
    }
}
