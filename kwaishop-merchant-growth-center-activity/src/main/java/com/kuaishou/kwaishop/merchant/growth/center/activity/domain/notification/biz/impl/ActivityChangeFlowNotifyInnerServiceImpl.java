package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditPerfEnum.ACTIVITY_CHANGE_FLOW_SEND_NOTIFY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditRedisKeyEnum.RISK_AUDIT_SEND_ACTIVITY_ID_LIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.activityFlowChangeNotifyConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.normalFormatTimeStamp;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithTime;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ActivityFlowChangeNotifyConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.MixCardNotifyTemplateConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityFlowChangeNotifyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.service.AuditCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz.ActivityChangeFlowNotifyInnerService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotifyActivityDimMsg;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.openapi.model.SendMixCardTemplateMessageReq;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.ActivityFlowChangeSendNotifyMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-11
 */
@Lazy
@Slf4j
@Service
public class ActivityChangeFlowNotifyInnerServiceImpl implements ActivityChangeFlowNotifyInnerService {

    @Resource
    private MsgProducer activityAuditSendNotifyProducer;

    @Autowired
    private AuditCacheService auditCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private AdminKimService adminKimService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    public static final int D_TIME = 3;


    @Override
    public void awardAuditRiskNotifyMsgSend(String auditKey) {
        List<Long> auditSendAcitvityIdList = auditCacheService.getAuditSendAcitvityIdList(auditKey);
        log.info("awardAuditRiskNotifyMsgSend auditKey:{}", auditKey);
        if (CollectionUtils.isEmpty(auditSendAcitvityIdList)) {
            return;
        }
        long nowTime = System.currentTimeMillis();

        auditSendAcitvityIdList.forEach(activityId -> {
            ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg = ActivityFlowChangeSendNotifyMsg.newBuilder()
                    .setNotifyType(ActivityFlowChangeNotifyTypeEnum.APPROVE_TO_RISK.getCode())
                    .setActivityId(activityId)
                    .setEventTime(nowTime)
                    .setBizKey(RISK_AUDIT_SEND_ACTIVITY_ID_LIST.getFullKeyJoinWithColon(auditKey))
                    .build();
            // 发送活动风控送审通知消息
            sendInnerNotifyMsg(recordSendNotifyMsg);
        });
    }

    /**
     * 发送notify消息
     *
     * @param recordSendNotifyMsg
     */
    private void sendInnerNotifyMsg(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg) {
        int notifyType = recordSendNotifyMsg.getNotifyType();
        long activityId = recordSendNotifyMsg.getActivityId();
        ActivityFlowChangeNotifyConfig activityNotifyConfig = activityFlowChangeNotifyConfig.getObject();
        Long sendIntervalTime = activityNotifyConfig.getSendIntervalTime();
        try {
            MqMessage eventMsg = activityAuditSendNotifyProducer.createMsgBuilder(recordSendNotifyMsg).build();
            MqSyncSendResult sendResult = activityAuditSendNotifyProducer.sendSync(eventMsg);
            if (sendResult.isSuccess()) {
                perfSuccessWithTime(ACTIVITY_CHANGE_FLOW_SEND_NOTIFY, String.valueOf(notifyType), activityId);
            } else {
                log.error("[审批单送审通知消息] 发送失败 recordSendNotifyMsg {}", toJSON(recordSendNotifyMsg));
                perfFail(ACTIVITY_CHANGE_FLOW_SEND_NOTIFY, String.valueOf(notifyType), String.valueOf(activityId));
            }
            // 休眠xs，防止下游限流
            if (sendIntervalTime != null && sendIntervalTime > 0) {
                try {
                    Thread.sleep(sendIntervalTime);
                } catch (InterruptedException e) {
                    // do nothing
                    log.warn("[审批单送审通知消息] 休眠异常 recordSendNotifyMsg {}", toJSON(recordSendNotifyMsg));
                }
            }
        } catch (Exception e) {
            log.error("[审批单送审通知消息] 发送失败 recordSendNotifyMsg {}", toJSON(recordSendNotifyMsg));
            perfException(ACTIVITY_CHANGE_FLOW_SEND_NOTIFY, String.valueOf(notifyType), String.valueOf(activityId));
        }
    }

    @Override
    public void awardAuditRiskNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                                               ActivityDO activityDO) throws Exception {
        ActivityFlowChangeNotifyConfig activityNotifyConfig = activityFlowChangeNotifyConfig.getObject();
        String auditKey = recordSendNotifyMsg.getBizKey();
        long activityId = recordSendNotifyMsg.getActivityId();
        if (StringUtils.isBlank(auditKey)) {
            log.error("[活动变更通知消息] 参数错误 activityId:{}, auditKey:{}", activityId, auditKey);
            return;
        }
        String taskKey = String.join(":", auditKey, String.valueOf(activityId));
        List<Long> auditSendTaskIdList = auditCacheService.getAuditSendTaskIdList(taskKey);
        if (CollectionUtils.isEmpty(auditSendTaskIdList)) {
            log.warn("[审批单送审通知消息] 审批单不存在 taskKey:{}", taskKey);
            return;
        }
        List<TaskDO> taskDOList = taskLocalCacheService.batchGetTaskByTaskId(auditSendTaskIdList);
        if (CollectionUtils.isEmpty(taskDOList)) {
            log.warn("[审批单送审通知消息] 任务不存在 recordSendNotifyMsg:{}", toJSON(recordSendNotifyMsg));
            return;
        }
        if (activityNotifyConfig == null || activityNotifyConfig.getActivityAuditSendNotifyTemplate() == null
                || activityNotifyConfig.getActivityAuditSendNotifyTemplate().invalid()) {
            throw new RuntimeException("审批单送审通知模板配置错误");
        }
        // 发送kim卡片消息
        adminKimService.sendMixCardTemplateMessage(buildKimCardMessage(ActivityFlowChangeNotifyTypeEnum.APPROVE_TO_RISK,
                activityDO, taskDOList, activityNotifyConfig, activityNotifyConfig.getActivityAuditSendNotifyTemplate(),
                taskKey));
    }

    @Override
    public void awardAuditRiskTimeoutNotifyMsgSend(Map<Long, Set<Long>> activityNotifyEntityIdMap) {
        if (MapUtils.isEmpty(activityNotifyEntityIdMap)) {
            return;
        }
        Set<Long> activitySet = activityNotifyEntityIdMap.keySet();
        Map<Long, ActivityDO> activityMap =
                activityLocalCacheService.batchQueryActivityInfo(Lists.newArrayList(activitySet));
        if (MapUtils.isEmpty(activityMap)) {
            return;
        }
        long nowTime = System.currentTimeMillis();
        Map<String, List<ActivityDO>> creatorActivityMap = activityMap.values().stream()
                .collect(Collectors.groupingBy(ActivityDO::getCreator, Collectors.toList()));
        // 按照活动创建人聚合发送
        creatorActivityMap.forEach((creator, activityList) -> sendNotifyMsgByCreator(activityNotifyEntityIdMap,
                creator, activityList, nowTime));
    }

    /**
     * 按照活动创建人聚合发送
     *
     * @param activityNotifyEntityIdMap
     * @param creator
     * @param activityList
     * @param nowTime
     */
    private void sendNotifyMsgByCreator(Map<Long, Set<Long>> activityNotifyEntityIdMap, String creator,
                                        List<ActivityDO> activityList, long nowTime) {
        try {
            List<NotifyActivityDimMsg> notifyActivityDimMsgList = activityList.stream()
                    .map(activityDO -> {
                        Long activityId = activityDO.getId();
                        NotifyActivityDimMsg notifyActivityDimMsg = new NotifyActivityDimMsg();
                        notifyActivityDimMsg.setNotifyUserName(creator);
                        notifyActivityDimMsg.setActivityId(activityId);
                        notifyActivityDimMsg.setActivityName(activityDO.getName());
                        Set<Long> entityIds = activityNotifyEntityIdMap.get(activityId);
                        if (CollectionUtils.isEmpty(entityIds)) {
                            log.warn("[活动变更通知消息] 实体缺失 activityId:{}", activityId);
                            return null;
                        }
                        notifyActivityDimMsg.setTaskIds(Lists.newArrayList(entityIds));
                        return notifyActivityDimMsg;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg = ActivityFlowChangeSendNotifyMsg.newBuilder()
                    .setNotifyUser(creator)
                    .setNotifyType(ActivityFlowChangeNotifyTypeEnum.APPROVE_TO_RISK_TIMEOUT.getCode())
                    .setEventTime(nowTime)
                    .setBizKey(toJSON(notifyActivityDimMsgList))
                    .build();
            // 发送活动风控送审通知消息
            sendInnerNotifyMsg(recordSendNotifyMsg);
        } catch (Exception e) {
            log.error("[风控超时通知消息] 发送失败 creator:{} activityNotifyEntityIdMap {}", creator,
                    toJSON(activityNotifyEntityIdMap), e);
        }
    }

    @Override
    public void awardAuditRiskTimeoutNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg) throws Exception {
        // 审批超时通知消息消费
        String bizKey = recordSendNotifyMsg.getBizKey();
        if (StringUtils.isBlank(bizKey)) {
            log.error("[活动变更通知消息] 参数错误 recordSendNotifyMsg:{}", toJSON(recordSendNotifyMsg));
            return;
        }
        List<NotifyActivityDimMsg> notifyActivityDimMsgList = fromJSON(bizKey, List.class, NotifyActivityDimMsg.class);
        if (CollectionUtils.isEmpty(notifyActivityDimMsgList)) {
            log.warn("[审批单送审通知消息] 审批单上下文不存在 recordSendNotifyMsg:{}", toJSON(recordSendNotifyMsg));
            return;
        }
        ActivityFlowChangeNotifyConfig activityNotifyConfig = activityFlowChangeNotifyConfig.getObject();
        notifyActivityDimMsgList = notifyActivityDimMsgList.stream()
                .filter(notifyActivityDimMsg -> checkActivity(notifyActivityDimMsg.getActivityId(), recordSendNotifyMsg,
                        activityNotifyConfig) != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notifyActivityDimMsgList)) {
            log.warn("[审批单送审通知消息] 合理活动不存在 recordSendNotifyMsg:{}", toJSON(recordSendNotifyMsg));
            return;
        }
        // 发送kim卡片消息
        MixCardNotifyTemplateConfig awardSendAuditTimeoutNotifyTemplate =
                activityNotifyConfig.getAwardSendAuditTimeoutNotifyTemplate();
        adminKimService.sendMixCardTemplateMessage(buildNotifyTimeoutMsg(recordSendNotifyMsg, activityNotifyConfig,
                awardSendAuditTimeoutNotifyTemplate, notifyActivityDimMsgList));
    }

    /**
     * 构建审批单送审超时通知消息
     *
     * @param recordSendNotifyMsg
     * @param activityNotifyConfig
     * @param awardSendAuditTimeoutNotifyTemplate
     * @param notifyActivityDimMsgList
     * @return
     */
    private SendMixCardTemplateMessageReq buildNotifyTimeoutMsg(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                                                                ActivityFlowChangeNotifyConfig activityNotifyConfig,
                                                                MixCardNotifyTemplateConfig awardSendAuditTimeoutNotifyTemplate,
                                                                List<NotifyActivityDimMsg> notifyActivityDimMsgList) {
        String cardMixVariables = null;
        try {
            // 获取审批单送审通知用户
            String notifyUser = activityNotifyConfig.getNotifyUser(recordSendNotifyMsg.getNotifyUser(),
                    awardSendAuditTimeoutNotifyTemplate);
            Long activityAwardAuditNotifyTime = activityNotifyConfig.getActivityAwardAuditNotifyTime() == null ? D_TIME
                    : activityNotifyConfig.getActivityAwardAuditNotifyTime();
            String cardVariableConfig = awardSendAuditTimeoutNotifyTemplate.getCardVariableConfig();
            String contentTemplate = awardSendAuditTimeoutNotifyTemplate.getContentTemplate();
            // 内容截断长度max
            long contentTruncateLength = awardSendAuditTimeoutNotifyTemplate.getContentTruncateLength() == null
                    ? D_TIME : awardSendAuditTimeoutNotifyTemplate.getContentTruncateLength();
            StringBuilder examplesSb = new StringBuilder();
            notifyActivityDimMsgList.forEach(msg -> {
                String activityName = msg.getActivityName();
                List<Long> entityIdList = msg.getTaskIds();
                if (CollectionUtils.isEmpty(entityIdList)) {
                    return;
                }
                List<TaskDO> taskDOList = taskLocalCacheService.batchGetTaskByTaskId(entityIdList);
                if (CollectionUtils.isEmpty(taskDOList)) {
                    return;
                }
                int beforeSize = taskDOList.size();
                // 截断
                taskDOList = taskDOList.stream()
                        .limit(contentTruncateLength)
                        .collect(Collectors.toList())
                ;
                int afterSize = taskDOList.size();
                for (int i = 0; i < taskDOList.size(); i++) {
                    TaskDO taskDO = taskDOList.get(i);
                    String moreDesc = "";
                    if (i == afterSize - 1) {
                        moreDesc = buildMoreDesc(beforeSize, afterSize);
                    }
                    examplesSb.append(String.format(contentTemplate, activityName, taskDO.getName(), taskDO.getId(),
                            String.join("~", normalFormatTimeStamp(taskDO.getStartTime()),
                                    normalFormatTimeStamp(taskDO.getEndTime())), moreDesc));
                }
            });
            cardMixVariables = String.format(cardVariableConfig, examplesSb, activityAwardAuditNotifyTime);
            return SendMixCardTemplateMessageReq.builder()
                    .templateId(awardSendAuditTimeoutNotifyTemplate.getTemplateId())
                    .username(notifyUser)
                    .uniqId("")
                    .data(fromJSON(cardMixVariables, Map.class, String.class, String.class))
                    .build();
        } catch (Exception e) {
            log.error("[通知消息] 构建kim卡片异常 cardMixVariables {} notifyTemplateConfig {}",
                    cardMixVariables, toJSON(recordSendNotifyMsg), e);
            throw new RuntimeException("通知消息构建kim卡片异常");
        }
    }

    /**
     * 构建更多描述
     *
     * @param beforeSize
     * @param afterSize
     * @return
     */
    private String buildMoreDesc(int beforeSize, int afterSize) {
        if (beforeSize == afterSize) {
            return "";
        }
        return String.format("等%s条", beforeSize);
    }

    public static void main(String[] args) {
        String cardMixVariables = "{\"example\":\"活动名称：复制：看看排行榜发现金 子任务名称：123赛道1子任务(5.1-6.30) 子任务ID：8251412  "
                + "结算周期：2024-05-01 00:00:00~2024-06-30 23:59:59\\n"
                + "活动名称：商业化账单222 子任务名称：商业化奖励222周期1子任务 子任务ID：8260917  结算周期：2024-07-01 00:00:00~2024-08-31 23:59:59\\n\","
                + "\"expired_days\":\"3\",\"click_link\":\"https://baomai-evt.corp.kuaishou"
                + ".com/page/cultivation/opstrategy/list\"}";
        Map<String, String> stringStringMap = fromJSON(cardMixVariables, Map.class, String.class, String.class);
        log.info("stringStringMap:{}", toJSON(stringStringMap));
    }

    @Override
    public void onlineActivityOperateNotifyMsgSend(Long activityId) {
        ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg = ActivityFlowChangeSendNotifyMsg.newBuilder()
                .setNotifyType(ActivityFlowChangeNotifyTypeEnum.CLICK_ACTIVITY_ONLINE.getCode())
                .setActivityId(activityId)
                .setEventTime(System.currentTimeMillis())
                .build();
        // 发送活动风控送审通知消息
        sendInnerNotifyMsg(recordSendNotifyMsg);
    }

    @Override
    public void onlineActivityOperateNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                                                      ActivityDO activityDO) throws Exception {
        if (activityDO == null || ActivityStatusEnum.ONLINE_PROCESS.getCode() != activityDO.getStatus()) {
            log.warn("[活动上线中内部通知消息] 活动状态不是上线中 recordSendNotifyMsg {}", toJSON(recordSendNotifyMsg));
            return;
        }
        ActivityFlowChangeNotifyConfig activityNotifyConfig = activityFlowChangeNotifyConfig.getObject();
        if (activityNotifyConfig == null || activityNotifyConfig.getActivityOnlineClickNotifyTemplate() == null
                || activityNotifyConfig.getActivityOnlineClickNotifyTemplate().invalid()) {
            throw new RuntimeException("活动上线时通知模板配置错误");
        }
        String cardMixVariables;
        try {
            MixCardNotifyTemplateConfig activityOnlineClickNotifyTemplate =
                    activityNotifyConfig.getActivityOnlineClickNotifyTemplate();
            // 获取审批单送审通知用户
            String notifyUser = activityNotifyConfig.getNotifyUser(activityDO.getCreator(),
                    activityOnlineClickNotifyTemplate);
            String cardVariableConfig = activityOnlineClickNotifyTemplate.getCardVariableConfig();
            cardMixVariables = String.format(cardVariableConfig, activityDO.getName(), activityDO.getId());
            SendMixCardTemplateMessageReq mixCardTemplateMessageReq = SendMixCardTemplateMessageReq.builder()
                    .templateId(activityOnlineClickNotifyTemplate.getTemplateId())
                    .username(notifyUser)
                    .uniqId(activityDO.getId() + "-online")
                    .data(fromJSON(cardMixVariables, Map.class, String.class, String.class))
                    .build();
            adminKimService.sendMixCardTemplateMessage(mixCardTemplateMessageReq);
        } catch (Exception e) {
            log.error("[活动上线中内部通知消息] 发送失败 recordSendNotifyMsg {}", toJSON(recordSendNotifyMsg));
            throw e;
        }
    }

    /**
     * 构建kim卡片消息
     *
     * @param approveToRisk
     * @param activityDO
     * @param taskDOList
     * @param activityNotifyConfig
     * @param uKey
     * @return
     */
    private SendMixCardTemplateMessageReq buildKimCardMessage(ActivityFlowChangeNotifyTypeEnum notifyTypeEnum,
                                                              ActivityDO activityDO,
                                                              List<TaskDO> taskDOList,
                                                              ActivityFlowChangeNotifyConfig activityNotifyConfig,
                                                              MixCardNotifyTemplateConfig notifyTemplateConfig,
                                                              String uKey) {
        String cardMixVariables = null;
        try {
            // 获取审批单送审通知用户
            String notifyUser = activityNotifyConfig.getNotifyUser(activityDO.getCreator(), notifyTemplateConfig);
            Long activityAwardAuditNotifyTime = activityNotifyConfig.getActivityAwardAuditNotifyTime() == null ? D_TIME
                    : activityNotifyConfig.getActivityAwardAuditNotifyTime();
            String cardVariableConfig = notifyTemplateConfig.getCardVariableConfig();
            String activityName = activityDO.getName();
            String contentTemplate = notifyTemplateConfig.getContentTemplate();
            // 内容截断长度max
            long contentTruncateLength = notifyTemplateConfig.getContentTruncateLength() == null
                    ? D_TIME : notifyTemplateConfig.getContentTruncateLength();
            int beforeSize = taskDOList.size();
            // 截断
            taskDOList = taskDOList.stream()
                    .limit(contentTruncateLength)
                    .collect(Collectors.toList())
            ;
            int afterSize = taskDOList.size();
            StringBuilder examplesSb = new StringBuilder();
            for (int i = 0; i < taskDOList.size(); i++) {
                TaskDO taskDO = taskDOList.get(i);
                String moreDesc = "";
                if (i == afterSize - 1) {
                    moreDesc = buildMoreDesc(beforeSize, afterSize);
                }
                examplesSb.append(String.format(contentTemplate, taskDO.getName(), taskDO.getId(), String.join("~",
                        normalFormatTimeStamp(taskDO.getStartTime()),
                        normalFormatTimeStamp(taskDO.getEndTime())), moreDesc));
            }
            if (ActivityFlowChangeNotifyTypeEnum.APPROVE_TO_RISK.equals(notifyTypeEnum)) {
                cardMixVariables = String.format(cardVariableConfig, activityName, examplesSb);
            } else if (ActivityFlowChangeNotifyTypeEnum.APPROVE_TO_RISK_TIMEOUT.equals(notifyTypeEnum)) {
                cardMixVariables = String.format(cardVariableConfig, examplesSb, activityAwardAuditNotifyTime);
            } else {
                throw new IllegalArgumentException("not support this notifyType!!!");
            }
            return SendMixCardTemplateMessageReq.builder()
                    .templateId(notifyTemplateConfig.getTemplateId())
                    .username(notifyUser)
                    .uniqId(uKey)
                    .data(fromJSON(cardMixVariables, Map.class, String.class, String.class))
                    .build();
        } catch (Exception e) {
            log.error("[通知消息] 构建kim卡片异常 activityId {} cardMixVariables {} notifyTemplateConfig {}",
                    activityDO.getId(), cardMixVariables, notifyTemplateConfig, e);
            throw new RuntimeException("通知消息构建kim卡片异常");
        }
    }

    @Override
    public ActivityDO checkActivity(long activityId, ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                                    ActivityFlowChangeNotifyConfig activityNotifyConfig) {
        if (activityId <= 0) {
            log.error("[活动变更通知消息] 参数错误 recordSendNotifyMsg:{}", toJSON(recordSendNotifyMsg));
            return null;
        }
        List<Long> notifyBlackActivityIds = activityNotifyConfig.getNotifyBlackActivityIds();
        // 通知推送黑名单
        if (CollectionUtils.isNotEmpty(notifyBlackActivityIds) && notifyBlackActivityIds.contains(activityId)) {
            log.warn("[活动变更通知消息] 活动在黑名单中，不发送通知 activityId:{}", activityId);
            return null;
        }
        long notifyMinActivityId = activityNotifyConfig.getLeftRangeActivityId();
        if (activityId < notifyMinActivityId) {
            log.warn("[活动变更通知消息] 活动id小于通知阈值，不发送通知 activityId:{}", activityId);
            return null;
        }
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null) {
            log.error("[审批单送审通知消息] 活动不存在 activityId:{}", activityId);
            return null;
        }
        return activityDO;
    }
}
