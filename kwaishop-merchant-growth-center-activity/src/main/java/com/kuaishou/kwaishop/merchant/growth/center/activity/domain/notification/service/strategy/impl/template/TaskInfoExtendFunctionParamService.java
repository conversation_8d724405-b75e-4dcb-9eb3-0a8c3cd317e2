package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.AWARD_COUNT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.INDICATOR_REMAIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorUnitTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.ActivityExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-26
 */
@Slf4j
@Service
public class TaskInfoExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private AwardConfigService awardConfigService;

    @Autowired
    private IndicatorTaskService indicatorTaskService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TASK_NAME, INDICATOR_REMAIN, AWARD_COUNT);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();
        // 获取任务ID
        long taskId;
        ActivityDO activity = activityLocalCacheService.queryActivityInfo(configBO.getActivityId());
        if (activity == null) {
            log.error("[任务模板参数] 无对应活动配置！configId:{}", configBO.getId());
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "活动配置缺失");
            result.setExecuteStatus(STOP);
            return result;
        }
        // 达人业务任务名称参数直接返回活动名称，多子活动后会有修改
        ActivityExtBO ext = getActivityExt(activity);
        if (StringUtils.isNotEmpty(ext.getBizType()) && ext.getBizType().equals(ActivityBizTypeEnum.DAREN.getCode())) {
            log.info("[任务模板参数] 达人任务直接取活动名称作为任务名称！configId:{}", configBO.getId());
            params.put(TASK_NAME.getName(), activity.getName());
            return result;
        }
        if (configBO.getEntityType() == NotificationEntityTypeEnum.TASK.getVal()) {
            taskId = configBO.getEntityId();
        } else if (configBO.getEntityType() == NotificationEntityTypeEnum.AWARD.getVal()) {
            AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
            if (awardConfigDO == null) {
                log.error("[任务模板参数] 无对应奖励配置！configId:{}", configBO.getEntityId());
                perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "奖励配置缺失");
                result.setExecuteStatus(STOP);
                return result;
            }
            taskId = awardConfigDO.getEntityId();
        } else if (configBO.getEntityType() == NotificationEntityTypeEnum.ACTIVITY.getVal()) {
            ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(configBO.getEntityId());
            if (activityDO == null) {
                log.error("[任务模板参数] 无对应活动配置！configId:{}", configBO.getEntityId());
                perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "奖励配置缺失");
                result.setExecuteStatus(STOP);
                return result;
            }
            params.put(TASK_NAME.getName(), activityDO.getName());
            return result;
        } else {
            log.error("[任务模板参数] 非预期实体类型！userId:{}, config:{}", userId, toJSON(configBO));
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "非预期实体类型");
            result.setExecuteStatus(STOP);
            return result;
        }
        TaskDO taskDO = taskLocalCacheService.getTaskByTaskId(taskId);
        if (taskDO == null) {
            log.error("[任务模板参数] 无对应任务！userId:{}, config:{}", userId, toJSON(configBO));
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "任务未知");
            result.setExecuteStatus(STOP);
            return result;
        }
        boolean checkRes = checkTaskInfoComplete(taskDO, userId);
        if (!checkRes) {
            // 校验失败，数据异常，停止推送
            result.setExecuteStatus(STOP);
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "校验失败");
            return result;
        }
        String taskName = taskDO.getName();
        if (taskDO.getParentTask() != 0) {
            TaskDO parentTask = taskLocalCacheService.getTaskByTaskId(taskDO.getParentTask());
            taskName = parentTask.getName();
        }
        if (StringUtils.isNotBlank(taskName)) {
            params.put(TASK_NAME.getName(), taskName);
        }
        if (templateParams.contains(AWARD_COUNT) || taskName.contains("${award")) {
            wrapUserAwardParam(userId, configBO.getActivityId(), taskId, params, taskName);
        }
        if (templateParams.contains(INDICATOR_REMAIN)) {
            wrapUserIndicatorParam(userId, configBO.getActivityId(), taskId, params);
        }

        return result;
    }

    /**
     * 绑定用户奖励参数
     */
    private void wrapUserAwardParam(long userId, long activityId, long taskId, Map<String, String> params,
            String taskName) {
        List<AwardConfigBO> awardConfigBOList =
                awardConfigService.getMultiUserTaskAwardConfig(userId, activityId, Lists.newArrayList(taskId));
        if (CollectionUtils.isEmpty(awardConfigBOList)) {
            return;
        }
        for (AwardConfigBO configBO : awardConfigBOList) {
            AwardTypeEnum awardType = configBO.getAwardType();
            String paramKey = Joiner.on("_").join(AWARD_COUNT.getName(), awardType.getCode());
            Long awardValue = configBO.getAwardValue();
            if ("元".equals(awardType.getUnit())) {
                awardValue = awardValue / 100;
            }
            if (awardValue < 1) {
                log.warn("[通知参数获取异常],奖励数值小于1, userId:{},activityId:{},taskId:{},awardType:{}", userId,
                        activityId,
                        taskId, awardType);
                continue;
            }
            params.put(paramKey, String.valueOf(awardValue));
            // 替换任务中的奖励占位符
            String namePlaceholder = "${award-" + awardType.getCode() + "}";
            if (taskName.contains(namePlaceholder)) {
                taskName = taskName.replace(namePlaceholder, String.valueOf(awardValue));
            }
        }
        params.put(TASK_NAME.getName(), taskName);
    }

    /**
     * 绑定用户奖励参数
     */
    private void wrapUserIndicatorParam(long userId, long activityId, long taskId, Map<String, String> params) {
        List<IndicatorRecordBO> indicatorRecordBOS =
                indicatorTaskService.listUserRecordOfTask(userId, activityId, taskId);
        if (CollectionUtils.isEmpty(indicatorRecordBOS)) {
            return;
        }
        for (IndicatorRecordBO recordBO : indicatorRecordBOS) {
            long indicatorId = recordBO.getIndicatorId();
            long targetValue = recordBO.getTargetValue();
            long currentValue = recordBO.getCurrentValue();
            long remainValue = targetValue - currentValue;
            IndicatorEnum indicatorEnum = IndicatorEnum.of(indicatorId);
            if (indicatorEnum.getUnitType() == IndicatorUnitTypeEnum.AMOUNT) {
                remainValue /= 100;
            }
            if (remainValue < 1) {
                continue;
            }
            String paramKey = Joiner.on("_").join(INDICATOR_REMAIN.getName(), indicatorId);
            params.put(paramKey, String.valueOf(remainValue));
        }
    }

    /**
     * 校验任务信息是否完整
     */
    private boolean checkTaskInfoComplete(TaskDO taskDO, long userId) {
        if (StringUtils.isBlank(taskDO.getName())) {
            log.error("[任务模板参数] 任务名称为空！userId:{}, taskDO:{}", userId, toJSON(taskDO));
            return false;
        }
        return true;
    }
}
