package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-05
 */
@Getter
@AllArgsConstructor
public enum LaunchTemplateCodeEnum {
    UNKNOWN("unknown", "未知"),
    ACTIVITY_ID("activityId", "活动id"),
    ;

    private final String code;

    private final String desc;

    public static LaunchTemplateCodeEnum getByCode(String code) {
        return Arrays.stream(values()).filter(e -> StringUtils.equals(code, e.getCode())).findFirst().orElse(UNKNOWN);
    }
}
