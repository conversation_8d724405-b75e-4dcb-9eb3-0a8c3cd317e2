package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
public enum NotificationStatusEnum {
    UNKNOWN(0, "未知"),
    INVALID(10, "不生效"),
    VALID(20, "生效"),
    NEED_AUDIT(30, "待审核"),
    AUDIT_ING(40, "审核中"),
    AUDIT_FAILED(50, "审核不通过"),
    ;
    private int val;
    private String desc;

    NotificationStatusEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static NotificationStatusEnum of(int val) {
        for (NotificationStatusEnum statusEnum : NotificationStatusEnum.values()) {
            if (statusEnum.getVal() == val) {
                return statusEnum;
            }
        }
        return UNKNOWN;
    }

    public static List<Integer> getAllValidNotificationStatus() {
        return Arrays.stream(NotificationStatusEnum.values())
                .map(NotificationStatusEnum::getVal)
                .collect(Collectors.toList());
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
