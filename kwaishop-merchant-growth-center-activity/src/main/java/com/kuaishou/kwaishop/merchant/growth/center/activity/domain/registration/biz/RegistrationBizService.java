package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.biz;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.registration.RegistrationConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.registration.UpdateOneUserActivityRegistrationRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawRuleDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.QueryDynamicDrawOptionRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-20
 */
public interface RegistrationBizService {
    /**
     * 针对某个活动，自定义excel里用户的基值
     */
    void customizeSetActivityUserBasicData(long activityId);

    /**
     * 计算准入报名
     */
    boolean calcMatchRegistration(RegistrationConfigDO registrationConfigs, List<DynamicDrawRuleDTO> rules);

    /**
     * 获取问卷信息
     */
    List<DynamicDrawRuleDTO> queryDynamicDrawOption(QueryDynamicDrawOptionRequest request);


    void updateUserRegistrationJsonData(UpdateOneUserActivityRegistrationRequest request);
}
