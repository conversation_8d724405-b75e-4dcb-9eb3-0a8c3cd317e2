package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.EsAggregationTypeEnum;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Data
public class TermsAggregationBuild {


    private String name;

    private String field;

    private EsAggregationTypeEnum type;

    private List<TermsAggregationBuild> aggregations;

    public TermsAggregationBuild(String name, String field, EsAggregationTypeEnum type) {
        this.name = name;
        this.field = field;
        this.type = type;
    }

    public TermsAggregationBuild subAggregation(TermsAggregationBuild aggregation) {
        if (aggregations == null) {
            aggregations = new ArrayList<>();
        }
        aggregations.add(aggregation);
        return this;
    }

    public boolean invalid() {
        return StringUtils.isBlank(name) || StringUtils.isBlank(field) || type == null;
    }


}
