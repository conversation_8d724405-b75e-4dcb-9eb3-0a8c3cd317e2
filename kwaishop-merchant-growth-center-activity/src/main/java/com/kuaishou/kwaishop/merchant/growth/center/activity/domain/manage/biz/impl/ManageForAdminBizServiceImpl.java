package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.convert.ManageAdminConverter.buildUserTaskInfo;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.REGISTRATION_SIGN_SPRING_ACTIVITY_WHITELIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.springTaskSellerInfoCheckSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityABTestMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityAllSceneKimRobotConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityCrowdBlackMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.whiteListRegistrationActivityIdList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.againRegistrationTagList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.activity116PrepareRegistrationWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SUCCESS;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.abtest.AbtestInstance;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.converter.AwardRecordConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz.ManageForAdminBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.convert.ManageAdminConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.service.TaskTemplateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.ActivityCustomRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.ActivityRegistrationFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.frame.ActivityRegistrationAbstract;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.MerchantActivityLayerDataLocalDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.audit.UserAuditRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.EcologicFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.LevelFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.SellerInfoFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.distribute.CrowdGroupDistributeService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.UserAuditRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.MerchantActivityLayerDataLocalDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.MerchantGrowthStatus;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.award.AwardRecordInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.AgainRegistrationInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateTaskByTemplateCodeRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateTaskOfActivityWithVariableRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryUserTaskOperatorLogRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserAuditStatisticDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserDataStatisticDetailResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserIndicatorStatisticDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserRegistrationStatisticDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserTaskStatisticDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.WhiteListRegistrationDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.WhiteListRegistrationRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.kim.KimUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.kim.model.ManageConfig;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.RuleCenterClient;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-07
 */
@Service
@Lazy
@Slf4j
public class ManageForAdminBizServiceImpl implements ManageForAdminBizService {

    @Autowired
    private IndicatorDAO indicatorDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private UserAwardRecordDAO userAwardRecordDAO;

    @Autowired
    private IndicatorRecordDAO indicatorRecordDAO;

    @Autowired
    private UserAuditRecordDAO userAuditRecordDAO;

    @Autowired
    private TaskTemplateService taskTemplateService;

    @Autowired
    private AwardRecordConverter awardRecordConverter;

    @Autowired
    private EcologicFetchService ecologicFetchService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private UserRegistrationRecordDAO registrationRecordDAO;

    @Autowired
    private ActivityRegistrationFactory activityRegistrationFactory;

    @Autowired
    private MerchantActivityLayerDataLocalDAO merchantActivityLayerDataLocalDAO;

    @Autowired
    private RiskControlFetchService riskControlFetchService;

    @Autowired
    private CrowdGroupDistributeService crowdGroupDistributeService;

    @Autowired
    private LevelFetchService levelFetchService;

    @Autowired
    private ActivityCustomRegistrationService activityCustomRegistrationService;

    @Autowired
    private SellerInfoFetchService sellerInfoFetchService;

    private static final String SELLER_LEVEL_RULE_CODE = "seller_level_process";
    private static final String LEVEL = "level";
    private static final String LEVEL_VALUE = "levelValue";
    private static final String NAME = "SpringTaskWhiteListRegistration";
    private static final int NOT_SELLER = 0;

    @Override
    public void createTaskByTemplateTag(CreateTaskByTemplateCodeRequest request) {
        String templateId = request.getCode();
        HashMap<String, Object> variables = Maps.newHashMap(request.getVariablesMap());
        log.info("createTaskByTemplateTag param:{}", toJSON(request));
        taskTemplateService.createActivityByTemplateId(templateId, variables);
    }

    @Override
    public void createTaskOfActivityWithVariable(CreateTaskOfActivityWithVariableRequest request) {
        String templateId = request.getCode();
        long activityId = request.getActivityId();
        HashMap<String, Object> variables = Maps.newHashMap(request.getVariablesMap());
        log.info("createTaskOfActivityWithVariable param:{}", toJSON(request));
        taskTemplateService.createOnlyTaskByTemplateId(templateId, activityId, variables);
    }

    @Override
    public List<AgainRegistrationInfoDTO> againRegistrationByLocalBasicsData(String userIdStr) {
        log.info("[基于本地数据重新报名活动请求]，userIdStr:{}", userIdStr);
        if (StringUtils.isEmpty(userIdStr)) {
            throw new BizException(PARAM_INVALID);
        }
        Set<Long> userIdSet =
                Arrays.stream(userIdStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIdSet)) {
            throw new BizException(PARAM_INVALID);
        }
        List<String> tagList = againRegistrationTagList.get();
        if (CollectionUtils.isEmpty(tagList)) {
            throw new BizException(SUCCESS.getCode(), "没有可报名的tag");
        }
        List<AgainRegistrationInfoDTO> result = new ArrayList<>();
        for (long userId : userIdSet) {
            List<MerchantActivityLayerDataLocalDO> merchantActivityLayerDataLocalDOS =
                    merchantActivityLayerDataLocalDAO.queryByUserIdAndTagList(userId, tagList);
            if (CollectionUtils.isEmpty(merchantActivityLayerDataLocalDOS)) {
                log.warn("[该用户不符合报名规则]，userId:{},basicData:{}", userId,
                        toJSON(merchantActivityLayerDataLocalDOS));
                AgainRegistrationInfoDTO infoDTO =
                        AgainRegistrationInfoDTO.newBuilder().setUserId(userId).setMessage("未查询到用户基础数据").build();
                result.add(infoDTO);
                continue;
            }

            Map<String, MerchantActivityLayerDataLocalDO> localDOMap =
                    merchantActivityLayerDataLocalDOS.stream().collect(
                            Collectors.toMap(MerchantActivityLayerDataLocalDO::getTag, Function.identity()));
            MerchantActivityLayerDataLocalDO localDO = null;
            for (String tag : tagList) {
                localDO = localDOMap.get(tag);
                if (localDO != null) {
                    break;
                }
            }
            if (localDO.getStatus() != MerchantGrowthStatus.IGNORE_VALUE) {
                log.warn("[该用户基础数据状态不符合报名规则]，userId:{},basicData:{}", userId, toJSON(localDO));
                AgainRegistrationInfoDTO infoDTO =
                        AgainRegistrationInfoDTO.newBuilder().setUserId(userId).setMessage("该用户基础数据状态不符合加白规则").build();
                result.add(infoDTO);
                continue;
            }
            ActivityRegistrationAbstract registrationService =
                    activityRegistrationFactory.getRegistrationService(localDO.getTag());
            if (registrationService == null) {
                throw new BizException(SERVER_ERROR.getCode(), localDO.getTag() + "对应的报名算法不存在");
            }
            try {
                if (registrationService.filter(userId, registrationService.convert(localDO.getJsonData()))) {
                    AgainRegistrationInfoDTO infoDTO =
                            AgainRegistrationInfoDTO.newBuilder().setUserId(userId).setMessage("报名被过滤，资格不符").build();
                    result.add(infoDTO);
                    continue;
                }
            } catch (Exception e) {
                throw new BizException(SERVER_ERROR);
            }


            // 修改基础数据为代报名状态
            merchantActivityLayerDataLocalDAO.updateStatus(localDO.getTag(), localDO.getUserId(),
                    MerchantGrowthStatus.WAIT_PROCESS_VALUE);

            // 处理报名逻辑
            registrationService.processor(localDO);
            AgainRegistrationInfoDTO infoDTO =
                    AgainRegistrationInfoDTO.newBuilder().setUserId(userId).setMessage("报名完成").setTag(localDO.getTag())
                            .build();
            result.add(infoDTO);
        }
        log.info("[基于本地数据重新报名活动返回值]，userIdStr:{},result:{}", userIdStr, toJSON(result));
        return result;
    }

    @Override
    public UserDataStatisticDetailResponse userDataStatisticDetail(long userId, long activityId, long indicatorId,
            int awardType) {
        // 报名
        List<UserRegistrationRecordDO> registrationList =
                registrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
        // 审批
        List<UserAuditRecordDO> auditList = userAuditRecordDAO.listUserAuditRecord(userId, activityId);
        // 指标
        List<IndicatorRecordDO> indList = indicatorRecordDAO.listUserRecordOfActivity(userId, activityId).stream()
                .sorted(Comparator.comparing(IndicatorRecordDO::getEntityId)).collect(Collectors.toList());
        // 如果有指标筛选
        if (indicatorId > 0) {
            indList = indList.stream().filter(e -> e.getIndicatorId() == indicatorId).collect(Collectors.toList());
        }
        Map<Long, IndicatorDO> indicatorMetaMap = indicatorDAO.queryAll().stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        // 任务
        List<UserTaskRecordDO> taskList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false).stream()
                        .sorted(Comparator.comparing(UserTaskRecordDO::getTaskId)).collect(Collectors.toList());
        List<Long> taskIdList = taskList.stream().map(UserTaskRecordDO::getTaskId).collect(Collectors.toList());
        Map<Long, TaskDO> taskMetaMap = taskLocalCacheService.listTaskById(taskIdList);
        // 奖励
        List<UserAwardRecordDO> awardList =
                userAwardRecordDAO.listUserActivityRecord(userId, activityId, false).stream()
                        .sorted(Comparator.comparing(UserAwardRecordDO::getEntityId)).collect(Collectors.toList());
        // 如果有奖励筛选
        if (awardType > 0) {
            awardList = awardList.stream().filter(e -> e.getAwardType() == awardType).collect(Collectors.toList());
        }
        // 转换
        List<UserIndicatorStatisticDTO> indicatorInfo =
                indList.stream().map(e -> buildUserTaskInfo(e, taskMetaMap.get(e.getEntityId()),
                        indicatorMetaMap.get(e.getIndicatorId()))).collect(Collectors.toList());
        List<UserTaskStatisticDTO> taskInfo = taskList.stream()
                .map(e -> buildUserTaskInfo(e, taskMetaMap.get(e.getTaskId()))).collect(Collectors.toList());
        List<AwardRecordInfoDTO> awardInfo = awardList.stream().map(e -> awardRecordConverter.convertToUserRecordBO(e))
                .map(e -> awardRecordConverter.covertToAwardRecordInfoDTO(e)).collect(Collectors.toList());
        List<UserAuditStatisticDTO> auditInfo = auditList.stream()
                .map(ManageAdminConverter::buildUserAuditInfo).collect(Collectors.toList());
        List<UserRegistrationStatisticDTO> registrationInfo = registrationList.stream()
                .map(ManageAdminConverter::buildUserRegistrationInfo).collect(Collectors.toList());
        // 组装
        return UserDataStatisticDetailResponse.newBuilder()
                .addAllIndicatorInfo(indicatorInfo)
                .addAllTaskInfo(taskInfo)
                .addAllAwardInfo(awardInfo)
                .addAllAuditInfo(auditInfo)
                .addAllRegistrationInfo(registrationInfo)
                .build();
    }

    @Override
    public List<String> getUserTaskOperatorLog(QueryUserTaskOperatorLogRequest request) {
        return ecologicFetchService.getEventLog(request.getUserId(), request.getActivityId());
    }

    @Override
    public List<WhiteListRegistrationDTO> whiteListRegistration(WhiteListRegistrationRequest request) {
        // 1-校验，2-报名
        int type = request.getType();
        if (type <= 0 || type > 2) {
            throw new BizException(PARAM_INVALID, "type输入非法");
        }
        String userIdList = request.getUserIdList();
        long activityId = request.getActivityId();
        if (StringUtils.isEmpty(userIdList)) {
            throw new BizException(PARAM_INVALID, "用户id输入非法");
        }
        if (!whiteListRegistrationActivityIdList.get().contains(activityId)) {
            throw new BizException(PARAM_INVALID, "活动未在白名单中");
        }
        Set<Long> userIdSet =
                Arrays.stream(userIdList.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(userIdSet)) {
            throw new BizException(PARAM_INVALID, "用户id输入非法");
        }
        // 获取所有父任务
        List<TaskDO> parentTaskDOList = taskLocalCacheService.getTaskListByActivityId(activityId).stream()
                .filter(a -> a.getParentTask() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentTaskDOList)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "父任务配置不存在");
        }

        List<WhiteListRegistrationDTO> result = new ArrayList<>();
        List<Long> failUserIds = Lists.newArrayList();
        for (long userId : userIdSet) {
            if (type == 1) {
                String checkStr = checkStr(userId, activityId);
                if (StringUtils.isNotBlank(checkStr)) {
                    WhiteListRegistrationDTO registrationDTO =
                            WhiteListRegistrationDTO.newBuilder().setUserId(userId).setMessage(checkStr).build();
                    result.add(registrationDTO);
                }
            } else {
                // 活动基本校验
                if (activityCustomRegistrationService.filter(userId, activityId)) {
                    WhiteListRegistrationDTO registrationDTO = WhiteListRegistrationDTO.newBuilder().setUserId(userId)
                            .setMessage("[活动报名被过滤]").build();
                    result.add(registrationDTO);
                    failUserIds.add(userId);
                    continue;
                }
                // 运营输入等级校验
                if (!checkSellerLevel(userId, request)) {
                    WhiteListRegistrationDTO registrationDTO = WhiteListRegistrationDTO.newBuilder().setUserId(userId)
                            .setMessage("[输入等级有误]").build();
                    result.add(registrationDTO);
                    failUserIds.add(userId);
                    continue;
                }
                // 商家身份校验
                if (springTaskSellerInfoCheckSwitch.get()) {
                    if (!checkSellerInfo(userId)) {
                        WhiteListRegistrationDTO registrationDTO = WhiteListRegistrationDTO.newBuilder().setUserId(userId)
                                .setMessage("[该用户非商家]").build();
                        result.add(registrationDTO);
                        failUserIds.add(userId);
                        continue;
                    }
                }
                // 构造活动报名相关信息
                Map<String, Object> context = Maps.newHashMap();
                context.put("level", request.getLevel());
                context.put("industry", request.getIndustry());
                try {
                    activityCustomRegistrationService.registrationActivityAndTask(userId, activityId, parentTaskDOList, context);
                    perfSuccess(REGISTRATION_SIGN_SPRING_ACTIVITY_WHITELIST, "春节活动资格下发成功", String.valueOf(activityId));
                } catch (Exception e) {
                    WhiteListRegistrationDTO registrationDTO = WhiteListRegistrationDTO.newBuilder().setUserId(userId)
                            .setMessage("[报名失败]").build();
                    result.add(registrationDTO);
                    failUserIds.add(userId);
                    log.error("[春节任务白名单报名] 同时下发活动和任务资格异常！userId:{}, activityId:{}", userId, activityId, e);
                    perfFail(REGISTRATION_SIGN_SPRING_ACTIVITY_WHITELIST, "同时下发活动和任务资格失败", String.valueOf(activityId));
                }
            }
        }
        sendKimUserFilterMessage(userIdSet.size(), userIdSet.size() - (long) failUserIds.size(),
                failUserIds.size(), failUserIds);
        if (!failUserIds.isEmpty()) {
            log.error("[用户白名单报名] 失败用户描述详情: {}", toJSON(result));
        }
        return result;
    }

    /**
     * 商家身份校验
     */
    private boolean checkSellerInfo(long userId) {
        // 校验userId是否为商家
        try {
            int identityBit = sellerInfoFetchService.querySellerIdentityBit(userId);
            return NOT_SELLER != identityBit;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 运营输入等级校验
     */
    private boolean checkSellerLevel(long userId, WhiteListRegistrationRequest request) {
        // 校验运营等级输入
        if (StringUtils.isNotBlank(request.getLevel())) {
            int currentLevel = 0;
            try {
                currentLevel = levelFetchService.getSellerCurrentLevel(userId);
            } catch (Exception e) {
                log.error("[白名单报名参数校验] 获取商家等级失败");
                return false;
            }
            Map<String, Object> context = Maps.newHashMap();
            Map<String, Object> param = Maps.newHashMap();
            param.put(LEVEL, currentLevel);
            context.put("context", param);
            Object resultJson = RuleCenterClient.getInstance().ruleExecute(SELLER_LEVEL_RULE_CODE, context);
            Map<String, Object> result = ObjectMapperUtils.fromJSON((String) resultJson, Map.class, String.class, Object.class);
            return request.getLevel().equals(result.get(LEVEL_VALUE));
        }
        return true;
    }

    /**
     * 发送kim用户过滤消息
     */
    private void sendKimUserFilterMessage(long registrationCount, long registrationSuccessCount,
            long registrationFailCount, List<Long> failUserIds) {
        ManageConfig manageConfig = activityAllSceneKimRobotConfig.getMap(String.class, ManageConfig.class).get(NAME);
        if (manageConfig == null) {
            log.info("[春节任务白名单报名] kim通知配置为空！");
            return;
        }
        try {
            String kimWarningContent = String.format(manageConfig.getKimContent(), registrationCount,
                    registrationSuccessCount, registrationFailCount, toJSON(failUserIds));
            KimUtils.sendMarkdownGroupBotMessage(manageConfig.getAlarmKimBotUrl(), kimWarningContent,
                    manageConfig.getMentionList());
        } catch (Exception e) {
            log.error("[春节任务白名单报名] kim通知异常！", e);
        }
    }

    private String checkStr(Long userId, Long activityId) {
        if (!activity116PrepareRegistrationWhiteList.get().isOnFor(userId)) {
            return "[未通过] 报名不在放量名单中";
        }
        Long crowdBlack = activityCrowdBlackMap.getMap(Long.class, Long.class).get(activityId);
        if (crowdBlack != null && crowdGroupDistributeService.checkUserInCrowds(userId,
                com.google.common.collect.Lists.newArrayList(crowdBlack))) {
            return "[未通过] 在业务黑名单中";
        }
        String abTestKey = activityABTestMap.getMap(Long.class, String.class).get(activityId);
        if (StringUtils.isNotBlank(abTestKey) && !abPass(userId, abTestKey)) {
            return "[未通过] AB平台过滤";
        }
        if (riskControlFetchService.inRiskBlackList(userId, "116_all_seller_task")) {
            return "[未通过] 在风控黑名单中";
        }
        return Strings.EMPTY;
    }

    private boolean abPass(long userId, String abTestKey) {
        return AbtestInstance.MERCHANT_TASK.getBoolean(abTestKey, true, "", userId);
    }
}