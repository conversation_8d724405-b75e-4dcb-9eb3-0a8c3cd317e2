package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;


import org.apache.commons.lang3.StringUtils;

/**
 * 模版变量枚举
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-01
 */
public enum TemplateParamTypeEnum {

    UNKNOWN_TEMPLATE_PARAM("unknown", "未知模版变量"),
    STRATEGY_MONTH("strategyMonth", "策略月份"),
    STRATEGY_FIRST_THRESHOLD("strategyFirstThreshold", "策略第一个门槛"),
    YESTERDAY_INCREMENTAL_INDICATOR("yesterdayIncrementalIndicator", "昨日增量指标数"),
    NEED_TO_PULL_NEW_NUMBER("needToPullNewNumber", "获取策略第一个门槛奖励还需拉新的人数"),
    STRATEGY_FIRST_THRESHOLD_AWARD("strategyFirstThresholdAward", "策略第一个门槛的奖励"),
    CUMULATIVE_BACK_INDICATED_NUMBER("cumulativeBackIndicatedNumber", "累计拉新回指标数"),
    CUMULATIVE_BACK_AWARD("cumulativeBackAward", "累计拉新回奖励"),
    AWARD_TYPE("awardType", "奖励类型"),
    ACTIVITY_NAME("activityName", "活动名称"),
    DRAW_END_TIME("drawEndTime", "领取截止时间"),
    REMAIN_END_TIME("remainEndTime", "活动剩余时间"),
    ACTIVITY_ID("activityId", "活动ID"),
    TASK_NAME("taskName", "任务名称"),
    INDICATOR_REMAIN("indicatorRemain", "指标剩余完成数量"),
    AWARD_COUNT("awardCount", "奖励数量（金额）"),
    RULE_PAGE_URL("rulePageUrl", "方舟搭建的规则页URL"),

    PC_RULE_PAGE_URL("pcRulePageUrl", "PC端跳转链接"),
    RACE_NAME("raceName", "赛道名称"),
    AWARD_DECR_TEXT("awardDecrText", "奖励取消通知文本"),
    TASK_BUTTON_ITEMS("taskButtonItems", "未完成任务按钮"),
    TASK_BUTTON_ITEMS_STAGE1("taskButtonItems1", "一阶段未完成任务按钮"),
    TASK_BUTTON_ITEMS_STAGE2("taskButtonItems2", "二阶段未完成任务按钮"),
    TASK_BUTTON_ITEMS_STAGE3("taskButtonItems3", "三阶段未完成任务按钮"),
    TASK_REMAIN_COUNT("taskRemainCount", "未完成任务数量"),
    TASK_REMAIN_TIME("taskRemainTime", "任务剩余时间"),
    TASK_BUTTON_ALL("taskButtonAll", "全部任务按钮"),
    CAMP_ID("campId", "课程ID"),
    TASK_COMPLETE_RATE("fp", "任务完成进度"),
    STEP_COMPLETE_NUM("f", "阶梯完成进度"),
    AWARD_TYPE_V2("rewardType", "奖励类型"),
    TIP_PROMOTE_DRAW("tipDrawDesc", "提示栏报名文案"),
    TIP_PROMOTE_URL("tipDrawUrl", "提示栏跳转链接"),
    AWARD_RULE_DESC("awardRuleDesc", "奖励规则文案"),
    REMAIN_DAYS("taskEnd", "剩余天数"),
    ACTIVITY_DRAW_DECR("d", "报名文案"),
    AWARD_DETAIL_PAGE_URL("awardDetailPageUrl", "奖励详情页面链接"),
    ;


    private String name;
    private String desc;

    TemplateParamTypeEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public static TemplateParamTypeEnum of(String name) {
        for (TemplateParamTypeEnum templateParam : TemplateParamTypeEnum.values()) {
            if (StringUtils.equals(templateParam.getName(), name)) {
                return templateParam;
            }
        }
        return UNKNOWN_TEMPLATE_PARAM;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
