package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.AwardConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.AwardSelectionConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.UserTaskRecordExtFieldBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.AwardConditionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-25
 */
@Service
@Slf4j
public class AwardTypeV2NotificationExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;


    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.AWARD_TYPE_V2);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[奖励模板参数]触达控制终止，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }
        String awardTypeStr = queryRelatedAwardConfig(configBO, userId);
        if (StringUtils.isEmpty(awardTypeStr)) {
            log.warn("[奖励模板参数] 奖励模板参数为空 userId:{}, configBO:{}", userId, toJSON(configBO));
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "奖励配置类型文案不存在");
            result.setExecuteStatus(STOP);
            return result;
        }
        String awardTypeName = TemplateParamTypeEnum.AWARD_TYPE_V2.getName();
        params.put(awardTypeName, awardTypeStr);
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    /**
     * 根据不同触达类型，通过不同方式获取奖励配置id
     */
    private String queryRelatedAwardConfig(NotificationPushConfigBO configBO, Long userId) {
        List<Integer> awardTypes = Lists.newArrayList();
        Long templateTaskId = null;
        String awardCondition = StringUtils.EMPTY;
        switch (NotificationEntityTypeEnum.of(configBO.getEntityType())) {
            case AWARD:
                AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
                if (awardConfigDO == null) {
                    break;
                }
                awardTypes.add(awardConfigDO.getAwardType());
                break;
            case ACTIVITY:
                List<UserRegistrationRecordDO> userRegistrations =
                        userRegistrationRecordDAO.queryUserRegistrationRecords(configBO.getActivityId(),
                                EntityTypeEnum.TASK, null, userId, UserRegistrationStatusEnum.VALID);
                if (CollectionUtils.isEmpty(userRegistrations)) {
                    return StringUtils.EMPTY;
                }
                // 多任务后续要改
                Long taskId = userRegistrations.get(0).getEntityId();
                List<TaskDO> childTaskList = taskLocalCacheService.getChildTask(taskId);
                if (CollectionUtils.isNotEmpty(childTaskList)) {
                    templateTaskId = childTaskList.get(0).getId();
                    awardCondition = childTaskList.get(0).getAwardCondition();
                    List<AwardConfigDO> taskAwards =
                            awardConfigLocalCacheService.queryTaskAwardConfig(configBO.getActivityId(),
                                    childTaskList.get(0).getId());
                    awardTypes = taskAwards.stream()
                            .map(AwardConfigDO::getAwardType)
                            .distinct().collect(Collectors.toList());
                }
                break;
            case TASK:
                TaskDO taskDO = taskLocalCacheService.getTaskByTaskId(configBO.getEntityId());
                if (taskDO == null) {
                    throw new BizException(BasicErrorCode.PARAM_INVALID, "实体不存在");
                }
                List<Long> taskIds = Lists.newArrayList(taskDO.getId());
                awardCondition = taskDO.getAwardCondition();
                if (taskDO.getParentTask() == 0) {
                    List<TaskDO> childTasks = taskLocalCacheService.getChildTask(taskDO.getId());
                    taskIds = childTasks.stream().map(TaskDO::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(childTasks)) {
                        awardCondition = childTasks.get(0).getAwardCondition();
                        templateTaskId = childTasks.get(0).getId();
                    }
                }
                List<AwardConfigDO> taskAwards =
                        awardConfigLocalCacheService.queryTaskAwardConfig(taskDO.getActivityId(), taskIds.get(0));
                if (CollectionUtils.isEmpty(taskAwards)) {
                    break;
                }
                awardTypes = taskAwards.stream()
                        .map(AwardConfigDO::getAwardType)
                        .distinct().collect(Collectors.toList());
                break;
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "不支持的实体类型");
        }
        log.info("[通知获取奖励类型]奖励类型:{},奖励条件:{},userId:{},活动id:{}", awardCondition, awardCondition,
                userId, configBO.getActivityId());
        Map<String, String> textMap = Maps.newHashMap();
        NotificationParamControlBO control = getParamControl();
        if (control != null && MapUtils.isNotEmpty(textMap)) {
            textMap = control.getText();
        }
        String joiner = MapUtils.getString(textMap, "joinerAnd", "+");
        if (StringUtils.isEmpty(awardCondition)) {
            return StringUtils.join(awardTypes.stream()
                    .map(e -> AwardTypeEnum.getByCode(e).getShowName())
                    .collect(Collectors.toList()), joiner);
        }
        // 多选奖励连接符
        AwardConditionBO awardConditionBO = fromJSON(awardCondition, AwardConditionBO.class);
        if (AwardConditionEnum.CHOOSE_ONE.getValue() != awardConditionBO.getConditionType()
                && AwardConditionEnum.MIX.getValue() != awardConditionBO.getConditionType()) {
            return StringUtils.join(awardTypes.stream()
                    .map(e -> AwardTypeEnum.getByCode(e).getShowName())
                    .collect(Collectors.toList()), joiner);
        }
        joiner = MapUtils.getString(textMap, "joinerOr", "或");
        // 过滤选择的奖励类型
        if (templateTaskId != null) {
            UserTaskRecordDO userTask =
                    userTaskRecordDAO.queryUserTaskRecord(userId, configBO.getActivityId(), templateTaskId, false);
            List<Integer> selectAwardType = resolveSelectAwardType(userTask);
            if (CollectionUtils.isNotEmpty(selectAwardType)) {
                awardTypes = awardTypes.stream().filter(selectAwardType::contains).collect(Collectors.toList());
            }
        }
        return StringUtils.join(awardTypes.stream()
                .map(e -> AwardTypeEnum.getByCode(e).getShowName())
                .collect(Collectors.toList()), joiner);

    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals("awardType")).findFirst().orElse(null);
    }

    private List<Integer> resolveSelectAwardType(UserTaskRecordDO userTask) {
        List<Integer> result = Lists.newArrayList();
        if (userTask == null || StringUtils.isEmpty(userTask.getExt())) {
            return result;
        }
        String ext = userTask.getExt();
        UserTaskRecordExtFieldBO extFieldBO = fromJSON(ext, UserTaskRecordExtFieldBO.class);
        AwardSelectionConfigBO awardSelectionConfig = extFieldBO.getAwardSelectionConfig();
        if (awardSelectionConfig == null) {
            return result;
        }
        List<Long> selectedAward = awardSelectionConfig.getSelectedAwardConfigIds();
        if (CollectionUtils.isEmpty(selectedAward)) {
            return result;
        }
        List<AwardConfigDO> awardTemplates = awardConfigLocalCacheService.queryAwardConfigByIds(selectedAward);
        return awardTemplates.stream().map(AwardConfigDO::getAwardType).collect(Collectors.toList());
    }
}
