package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.UserDrawTaskResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-17
 */
public interface OffsetEventRegistrationService {
    /**
     * 重刷资格记录
     */
    void refreshRegistrationRecord(UserRegistrationRecordDO registrationRecordDO);

    /**
     * 校验是否可以领取任务
     */
    UserDrawTaskResult checkOffsetEventTaskCanDrawTask(Long userId, TaskDO parentTask);

    /**
     * 计算资格标签
     */
    List<String> calRegistrationRecordTag(TaskDO layerParentTask, Long userId, AddRegistrationOptionBO userOption);

    /**
     * 用户某个活动下是否存在偏移任务
     * 传入父任务id，优先判断指定父任务中是否存在偏移任务。否则，整个活动级别判断
     */
    boolean existOffsetEventTask(Long activityId, Long userId, List<Long> taskIds);
}
