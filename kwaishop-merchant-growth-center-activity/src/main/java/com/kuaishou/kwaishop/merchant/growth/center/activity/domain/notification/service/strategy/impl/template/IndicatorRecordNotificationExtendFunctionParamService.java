package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.enums.StrategyNotificationStatusTypeEnum.NOT_UP_TO_STANDARD_IN_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.SingleValueBeyondSendRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.SingleValueBeyondSendRule.SingleValueBeyondRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordExtFieldBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.ActivityStrategyInnerProcessor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.ActivityStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.StrategyAwardService;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-04
 */
@Service
@Slf4j
public class IndicatorRecordNotificationExtendFunctionParamService
        extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private ActivityStrategyService activityStrategyService;

    @Autowired
    private ActivityStrategyInnerProcessor activityStrategyInnerProcessor;

    @Autowired
    private StrategyAwardService strategyAwardService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.YESTERDAY_INCREMENTAL_INDICATOR,
                TemplateParamTypeEnum.NEED_TO_PULL_NEW_NUMBER,
                TemplateParamTypeEnum.CUMULATIVE_BACK_INDICATED_NUMBER);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        // 降级、黑名单校验
        NotificationExtendFunctionParamBO checkResult = checkDegradationAndBlackList(userId, configBO);
        if (checkResult != null) {
            return checkResult;
        }
        // 获取策略奖励发送规则
        SingleValueBeyondSendRule sendRule = getSingleValueBeyondSendRule(userId, configBO);
        if (sendRule == null) {
            result.setExecuteStatus(STOP);
            return result;
        }
        // 获取策略第一个门槛的发奖规则
        SingleValueBeyondRule minThresholdSendRule = getMinThresholdSendRule(userId, configBO, sendRule);
        if (minThresholdSendRule == null) {
            result.setExecuteStatus(STOP);
            return result;
        }
        // 获取用户策略指标记录
        IndicatorRecordBO indicatorRecord = getIndicatorRecordBO(userId, configBO, sendRule);
        if (indicatorRecord == null) {
            result.setExecuteStatus(SKIP);
            return result;
        }
        // 获取昨日拉新人数
        Long lastCurrentValue = getLastCurrentValue(indicatorRecord);
        if (lastCurrentValue == null) {
            result.setExecuteStatus(SKIP);
            return result;
        }
        // 计算昨日增量指标数
        long increment = indicatorRecord.getCurrentValue() - lastCurrentValue;
        if (increment <= 0) {
            log.warn("[获取模板参数警告] 昨天拉新人数为：{}，跳过本次推送, userId: {}, "
                    + "entityId: {}", increment, userId, configBO.getEntityId());
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(),
                    String.valueOf(indicatorRecord.getEntityId()));
            result.setExecuteStatus(SKIP);
            return result;
        }
        params.put(TemplateParamTypeEnum.YESTERDAY_INCREMENTAL_INDICATOR.getName(), String.valueOf(increment));
        // 门槛1 - 当前拉新人数
        long needToPullNewNumber = minThresholdSendRule.getBeyond() - indicatorRecord.getCurrentValue();
        // 该参数只有在用户未达标的时候才会使用，用户达标后该参数会小于等于0，跳过当前推送
        if (needToPullNewNumber <= 0 && configBO.getEntityStatus() == NOT_UP_TO_STANDARD_IN_ACTIVITY.getCode()) {
            // 未达标的时候才会用到这个参数
            log.warn("[获取模板参数警告] 策略第一个门槛奖励还需拉新的人数为：{}, 用户已达标，跳过本次推送", needToPullNewNumber);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(),
                    String.valueOf(indicatorRecord.getEntityId()));
            result.setExecuteStatus(SKIP);
            return result;
        }
        params.put(TemplateParamTypeEnum.NEED_TO_PULL_NEW_NUMBER.getName(), String.valueOf(needToPullNewNumber));
        // 累计拉新回人数
        params.put(TemplateParamTypeEnum.CUMULATIVE_BACK_INDICATED_NUMBER.getName(),
                String.valueOf(indicatorRecord.getCurrentValue()));
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    /**
     * 获取奖励发送规则
     */
    private SingleValueBeyondSendRule getSingleValueBeyondSendRule(long userId, NotificationPushConfigBO configBO) {
        AwardConfigBO awardConfigBO =
                strategyAwardService.getStrategyMainAwardRuleConfigWithCache(configBO.getEntityId());
        // 获取奖励配置的目的是为了获取奖励发送规则
        if (awardConfigBO == null || awardConfigBO.getSendRule() == null) {
            log.error("[获取模板参数失败][获取奖励配置失败] Fail to query awardConfig, userId is :{},"
                    + " notificationPushConfigBO is :{}", userId, configBO);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            return null;
        }
        List<SingleValueBeyondSendRule> userStrategyAwardSendRules =
                strategyAwardService.getUserStrategyAwardSendRule(userId, awardConfigBO);
        if (CollectionUtils.isEmpty(userStrategyAwardSendRules)) {
            return null;
        }
        // 当拉新和拉回都设置了目标时按照拉新来计算是否达标即可
        return userStrategyAwardSendRules.get(0);
    }

    /**
     * 获取奖励发送规则中门槛值最小的一条规则
     */
    private SingleValueBeyondRule getMinThresholdSendRule(long userId, NotificationPushConfigBO configBO,
            SingleValueBeyondSendRule sendRule) {
        List<SingleValueBeyondRule> rules = sendRule.getRules();
        // 获取所有奖励发送规则中门槛最低的一条
        SingleValueBeyondRule minThreshSendRule = CollectionUtils.isEmpty(rules) ? null : rules
                .stream()
                .min(Comparator.comparing(SingleValueBeyondRule::getBeyond))
                .orElse(null);
        if (minThreshSendRule == null) {
            log.error("[获取模板参数失败][获取奖励发送规则失败] Fail to get sendRule, userId is :{}, "
                    + "notificationPushConfigBO is :{}, sendRule is :{}", userId, configBO, sendRule);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            return null;
        }
        return minThreshSendRule;
    }

    /**
     * 获取用户策略指标记录
     */
    private IndicatorRecordBO getIndicatorRecordBO(long userId, NotificationPushConfigBO configBO,
            SingleValueBeyondSendRule sendRule) {
        IndicatorRecordBO indicatorRecord = indicatorService.getIndicatorRecord(userId,
                configBO.getActivityId(), configBO.getEntityId(), sendRule.getIndicatorConfigId());
        // 用户指标记录靠DE同步过来的，有可能会有数据延迟同步的问题，获取不到时跳过当前的触达
        if (indicatorRecord == null) {
            log.warn("[获取模板参数警告] 用户指标数据为空，可能存在数据同步延迟，跳过本次推送, userId is :{}, "
                    + "notificationPushConfigBO is :{}, sendRule is :{}", userId, configBO, sendRule);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(), String.valueOf(configBO.getEntityId()));
            return null;
        }
        return indicatorRecord;
    }

    /**
     * 获取昨日拉新人数
     */
    private Long getLastCurrentValue(IndicatorRecordBO indicatorRecord) {
        if (StringUtils.isEmpty(indicatorRecord.getExt())) {
            log.warn("[获取模板参数警告] indicatorRecord ext为空, indicatorRecord is :{}", toJSON(indicatorRecord));
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(),
                    String.valueOf(indicatorRecord.getEntityId()), "extIsNull");
            return null;
        }
        // 扩展字段中如果没有昨日拉新的人数，说明昨天的数据还没有同步过来
        IndicatorRecordExtFieldBO indicatorRecordExtFieldBO =
                ObjectMapperUtils.fromJSON(indicatorRecord.getExt(), IndicatorRecordExtFieldBO.class);
        // 这个字段为此次新增，旧数据可能没有没有该值
        if (indicatorRecordExtFieldBO.getLastCurrentValue() == null) {
            log.warn("[获取模板参数警告] 用户指标ext中不存在昨日拉新人数lastCurrentValue，跳过本次推送, "
                    + "indicatorRecord is :{}", toJSON(indicatorRecord));
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(),
                    String.valueOf(indicatorRecord.getEntityId()), "lastCurrentValueIsNull");
            return null;
        }
        // 当天数据的同步时间为昨天，如果数据同步时间不是昨天，跳过今天的推送
        long target = activityStrategyInnerProcessor.getCurrSyncDataTimeStamp();
        if (!DateUtils.isSameDay(indicatorRecordExtFieldBO.getSyncTime(), target)) {
            log.warn("[获取模板参数警告] 今天数据未同步，跳过此次推送，上次数据同步时间：{}, 目标数据同步时间：{}, indicatorRecord is :{}",
                    DateUtils.normalFormatTimeStamp(indicatorRecordExtFieldBO.getSyncTime()),
                    DateUtils.normalFormatTimeStamp(target), toJSON(indicatorRecord));
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(),
                    String.valueOf(indicatorRecord.getEntityId()), "deDateNotSameDay");
            return null;
        }
        return indicatorRecordExtFieldBO.getLastCurrentValue();
    }
}
