package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchLLMCommonConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.kwaibusiness.kflow.ai.engine.FlowAiInput;
import com.kuaishou.kwaibusiness.kflow.ai.engine.FlowAiOutput;
import com.kuaishou.kwaibusiness.kflow.ai.engine.KFlowAiEngine;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMCommonExecuteParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMCommonExecuteResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncProcessParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncResultUpdateParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMGenConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenAsyncStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenResultCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.enums.GenDataStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenDataDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenDomainService;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-21
 */
@Service
@Slf4j
public class LLMGenDomainServiceImpl implements LLMGenDomainService {

    @Autowired
    private KFlowAiEngine kFlowAiEngine;

    @Autowired
    private LLMGenCacheService llmGenCacheService;

    @Autowired
    private LLMGenDataDomainService llmGenDataDomainService;

    private static final String DEFAULT_CONTEXT_KEY = "context";

    @Override
    public LLMCommonExecuteResultBO execute(LLMCommonExecuteParamBO param) {
        checkExecuteParam(param);

        try {
            LLMCommonConfigBO config = launchLLMCommonConfig.getObject();
            if (config == null || CollectionUtils.isEmpty(config.getGenConfigs())) {
                log.error("[llmAgent运行] Agent配置异常 param:{}", toJSON(param));
                throw new BizException(BasicErrorCode.SERVER_ERROR, "Agent配置异常");
            }

            String genScene = param.getGenScene();
            String userId = param.getUserId();

            LLMGenConfigBO genConfig = config.getGenConfigs().stream()
                    .filter(e -> Objects.equals(e.getGenScene(), genScene)).findFirst()
                    .orElse(null);
            if (genConfig == null) {
                log.error("[llmAgent运行] Agent场景不存在 param:{}", toJSON(param));
                throw new BizException(BasicErrorCode.SERVER_ERROR, "Agent场景不存在");
            }

            LLMGenTypeEnum agentType = LLMGenTypeEnum.getByType(genConfig.getAgentType());
            if (Objects.equals(agentType, LLMGenTypeEnum.UNKNOWN)) {
                log.error("[llmAgent运行] Agent配置异常 param:{}", toJSON(param));
                throw new BizException(BasicErrorCode.SERVER_ERROR, "Agent配置异常");
            }

            // 获取执行流程唯一键
            String agentKey = genConfig.getAgentKey();

            switch (agentType) {
                case KFLOW_AI:
                    Map<String, Object> context = Maps.newHashMap();
                    context.put(DEFAULT_CONTEXT_KEY, param.getContext());
                    return executeKflowAI(userId, context, agentKey);
                default:
                    log.error("[llmAgent运行] 不支持的Agent类型 param:{}", toJSON(param));
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的Agent类型");
            }
        } catch (Exception e) {
            log.error("[llmAgent运行] 执行失败 param:{}", toJSON(param), e);
            return LLMCommonExecuteResultBO.error(e.getMessage());
        }
    }

    private LLMCommonExecuteResultBO executeKflowAI(String userId,
            Map<String, Object> context, String agentKey) {
        FlowAiInput input = FlowAiInput.newBuilder()
                .setCode(agentKey)
                .setUserId(userId)
                .setContext(context)
                .build();
        try {
            FlowAiOutput output = kFlowAiEngine.execute(input);
            String answer = output.getAnswer();
            return LLMCommonExecuteResultBO.success(answer);
        } catch (Exception e) {
            log.error("[llmAgent运行] 执行异常 input:{}", toJSON(input), e);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "执行异常");
        }
    }

    private void checkExecuteParam(LLMCommonExecuteParamBO param) {
        checkArgument(param != null, "llmAgent运行参数不能为空");
        checkArgument(StringUtils.isNotBlank(param.getGenScene()), "llmAgent运行场景不能为空");
        checkArgument(StringUtils.isNotBlank(param.getUserId()), "llmAgent运行用户ID不能为空");
        checkArgument(param.getContext() != null, "llmAgent运行上下文不能为空");
    }

    @Override
    public void processGenAsync(LLMContentGenAsyncProcessParamBO param) {
        String uniqueKey = param.getUniqueKey();
        GenDataRecordBO originRecord = llmGenDataDomainService.queryByUniqueKey(uniqueKey);
        if (originRecord == null || !Objects.equals(originRecord.getStatus(),
                GenDataStatusEnum.GENERATING.getStatus())) {
            log.error("[llm物料异步生成] 生成记录异常 param:{}", toJSON(param));
            throw new BizException(SERVER_ERROR, "生成记录异常");
        }

        try {
            // agent执行内部上下文
            LLMContentGenContextBO context = LLMContentGenContextBO.builder()
                    .genScene(param.getGenScene())
                    .customizeInfo(param.getCustomizeInfo())
                    .operator(param.getOperator())
                    .uniqueKey(param.getUniqueKey())
                    .build();

            // agent执行参数
            LLMCommonExecuteParamBO executeParam = LLMCommonExecuteParamBO.builder()
                    .genScene(param.getGenScene())
                    .userId(param.getOperator())
                    .context(context)
                    .build();

            LLMCommonExecuteResultBO executeResult = execute(executeParam);
            if (executeResult == null || executeResult.getResultCode() != 1) {
                log.error("[llm物料异步生成处理] 执行失败 param:{}, executeResult:{}"
                        , toJSON(param), toJSON(executeParam));
                throw new BizException(BasicErrorCode.SERVER_ERROR, "llm物料异步生成处理执行失败");
            }

            log.info("[llm物料异步生成处理] 执行成功 param:{}, executeResult:{}", toJSON(param),
                    toJSON(executeResult));
        } catch (Exception e) {
            log.error("[llm物料异步生成] 执行失败 param:{}", toJSON(param), e);
            throw new BizException(SERVER_ERROR, "llm物料异步生成执行失败");
        }
    }

    @Override
    public Boolean setGenAsyncResult(LLMContentGenAsyncResultUpdateParamBO param) {
        log.info("[llm物料异步生成更新] 开始执行 param:{}", toJSON(param));

        checkArgument(param != null, "生成参数不能为空");
        checkArgument(StringUtils.isNotBlank(param.getUniqueKey()), "唯一键不能为空");
        checkArgument(param.getResultCode() != null, "生成结果不能为空");

        String uniqueKey = param.getUniqueKey();
        Integer resultCode = param.getResultCode();

        Boolean lock = llmGenCacheService.tryLockLLMContentGenAsyncResult(uniqueKey);
        if (!lock) {
            log.info("[llm物料异步生成更新] param:{}", toJSON(param));
            return false;
        }

        try {
            GenDataRecordBO genDataRecord = llmGenDataDomainService.queryByUniqueKey(uniqueKey);
            if (genDataRecord == null || !Objects.equals(genDataRecord.getStatus(),
                    LLMGenAsyncStatusEnum.GENERATING.getStatus())) {
                log.warn("[llm物料异步生成更新] 状态异常 跳过 param:{}, genDataRecord:{}",
                        toJSON(param), toJSON(genDataRecord));
                return true;
            }

            LLMGenResultCodeEnum resultCodeEnum = LLMGenResultCodeEnum.getByCode(resultCode);
            if (Objects.equals(resultCodeEnum, LLMGenResultCodeEnum.SUCCESS)) {
                llmGenDataDomainService.successGenDataRecord(uniqueKey, param.getContent());
            } else {
                llmGenDataDomainService.failGenDataRecord(uniqueKey, param.getErrorMsg());
            }

            llmGenCacheService.deleteLLMContentGenAsyncUniqueKey(uniqueKey);
            log.info("[llm物料异步生成更新] 执行成功 param:{}, genDataRecord:{}", toJSON(param), toJSON(genDataRecord));
            return true;
        } catch (Exception e) {
            log.error("[llm物料异步生成更新] 更新失败 param:{}", toJSON(param), e);
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        } finally {
            llmGenCacheService.unlockLLMContentGenAsyncResult(uniqueKey);
        }
    }
}
