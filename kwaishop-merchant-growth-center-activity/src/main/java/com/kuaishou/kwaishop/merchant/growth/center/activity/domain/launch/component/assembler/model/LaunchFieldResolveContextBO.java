package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldConfigBO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchFieldResolveContextBO {

    private LaunchContentFieldConfigBO fieldConfig;

    private List<LaunchContentFieldConfigBO> fieldConfigs;

    private LaunchFieldAssembleContext fieldAssembleContext;
}
