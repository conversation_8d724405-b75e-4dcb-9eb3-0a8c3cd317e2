package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;

/**
 * 推送模版变量获取
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-28
 */
public class NotificationTemplateParamStrategyFactory {

    private static final Map<List<TemplateParamTypeEnum>, AbstractNotificationExtendFunctionParamService>
            NOTIFICATION_TEMPLATE_PARAM_STRATEGY_MAP = new HashMap<>();

    /**
     * service注册
     */
    public static void register(List<TemplateParamTypeEnum> templateParams,
            AbstractNotificationExtendFunctionParamService strategyService) {
        NOTIFICATION_TEMPLATE_PARAM_STRATEGY_MAP.put(templateParams, strategyService);
    }

    /**
     * 找到能够返回模版变量的service，包含即可，取第一个立即返回
     */
    public static Set<AbstractNotificationExtendFunctionParamService> getTemplateParamStrategyServiceList(
            List<TemplateParamTypeEnum> templatePrams) {
        Set<AbstractNotificationExtendFunctionParamService> result = new HashSet<>();
        for (TemplateParamTypeEnum param : templatePrams) {
            for (Entry<List<TemplateParamTypeEnum>, AbstractNotificationExtendFunctionParamService> entry
                    : NOTIFICATION_TEMPLATE_PARAM_STRATEGY_MAP.entrySet()) {
                List<TemplateParamTypeEnum> list = entry.getKey();
                AbstractNotificationExtendFunctionParamService service = entry.getValue();
                if (list.contains(param)) {
                    result.add(service);
                    break;
                }
            }
        }
        return result;
    }
}
