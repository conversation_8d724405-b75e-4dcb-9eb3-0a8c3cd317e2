package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
@Slf4j
@Service
public class RemainDaysExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    private static final long ONE_DAY = 3600 * 24 * 1000;


    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.REMAIN_DAYS);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[触达获取剩余天数参数]触达控制终止，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }
        Long currentTime = System.currentTimeMillis();
        long days = 0;
        NotificationEntityTypeEnum entityType = NotificationEntityTypeEnum.of(configBO.getEntityType());
        switch (entityType) {
            case TASK:
                UserTaskRecordDO userTask =
                        userTaskRecordDAO.queryUserTaskRecord(userId, configBO.getActivityId(), configBO.getEntityId(),
                                false);
                if (userTask == null) {
                    log.info("[触达获取剩余天数参数]用户任务缺失，activityId:{},userId:{},entityId:{}",
                            configBO.getActivityId(), userId, configBO.getEntityId());
                    perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "报名任务不存在");
                    result.setExecuteStatus(STOP);
                    return result;
                }
                if (userTask.getParentId() == 0) {
                    List<UserTaskRecordDO> userChildTasks =
                            userTaskRecordDAO.queryUserTaskRecordByParentId(userId, configBO.getActivityId(),
                                    userTask.getTaskId(), false);
                    userTask = userChildTasks.stream()
                            .filter(e -> e.getStartTime() <= currentTime && e.getEndTime() >= currentTime)
                            .findFirst().orElse(null);
                    if (userTask == null) {
                        perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "用户任务未报名");
                        log.info("[触达获取剩余天数参数]用户子任务缺失，activityId:{},userId:{},entityId:{}",
                                configBO.getActivityId(), userId, configBO.getEntityId());
                        result.setExecuteStatus(STOP);
                        return result;
                    }
                }
                days = (userTask.getEndTime() - currentTime) / (ONE_DAY);
                break;
            case ACTIVITY:
                UserActivityRecordDO userActivity =
                        userActivityRecordDAO.queryUserActivityRecord(userId, configBO.getActivityId(), false);
                if (userActivity == null) {
                    perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "报名活动不存在");
                    result.setExecuteStatus(STOP);
                    return result;
                }
                ActivityDO activity = activityLocalCacheService.queryActivityInfo(configBO.getActivityId());
                if (activity == null) {
                    perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "活动不存在");
                    result.setExecuteStatus(STOP);
                    return result;
                }
                days = (activity.getEndTime() - currentTime) / (ONE_DAY);
            default:
                break;
        }
        if (days <= 0) {
            log.info("[触达获取剩余天数参数]天数小于0，activityId:{},userId:{},entityId:{}",
                    configBO.getActivityId(), userId, configBO.getEntityId());
            result.setExecuteStatus(STOP);
            return result;
        }
        String remainDays = TemplateParamTypeEnum.REMAIN_DAYS.getName();
        params.put(remainDays, String.valueOf(days));
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals("remainDays")).findFirst().orElse(null);
    }
}