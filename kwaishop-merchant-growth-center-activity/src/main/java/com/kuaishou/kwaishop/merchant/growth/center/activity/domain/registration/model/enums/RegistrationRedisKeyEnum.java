package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.common.base.Joiner;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-02
 */
public enum RegistrationRedisKeyEnum {
    /**
     * 等级流量活动创建锁
     */
    FLOW_ACTIVITY_CREATE("flow:activity:create:"),
    /**
     * 风控用户资格动态下发Shard执行记录RedisKey
     */
    RISK_USER_REGISTRATION_RE_DRAW_SHARD("risk:user:registration:re:draw:shard:"),
    /**
     * 风控用户资格动态下发活动执行记录RedisKey
     */
    RISK_USER_REGISTRATION_RE_DRAW_ACTIVITY("risk:user:registration:re:draw:activity:"),
    /**
     * 风控用户资格动态下发用户执行记录RedisKey
     */
    RISK_USER_REGISTRATION_RE_DRAW_USER("risk:user:registration:re:draw:user:"),
    /**
     * 用户资格
     */
    RISK_USER_REGISTRATION_RE_DRAW_FINISH_KIM_REPORT("risk:user:registration:re:draw:finish:kim:report:"),
    ;

    private final String prefix;
    private static final String SEPARATOR = "_";
    private static final String COLON = ":";

    static {
        Map<String, RegistrationRedisKeyEnum> map = new HashMap<>();
        for (RegistrationRedisKeyEnum value : values()) {
            if (map.containsKey(value.getPrefix())) {
                throw new IllegalArgumentException(
                        "！！！！严重错误！！！！RedisKey prefix 冲突:" + value.getPrefix());
            }
            map.put(value.getPrefix(), value);
        }
    }

    RegistrationRedisKeyEnum(String prefix) {
        this.prefix = prefix;
    }


    public String getFullKey(long value) {
        return this.prefix + value;
    }

    public String getFullKey(String value) {
        return this.prefix + value;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public String getFullKeyJoinWithUnderline(CharSequence... keySuffix) {
        return this.prefix + String.join(SEPARATOR, keySuffix);
    }

    public String getFullKeyJoinWithUnderline(Long... keySuffix) {
        return this.prefix + Arrays.stream(keySuffix).map(String::valueOf).collect(Collectors.joining(SEPARATOR));
    }

    public String getFullKeyJoinWithColon(CharSequence... keySuffix) {
        return this.prefix + String.join(COLON, keySuffix);
    }
    public String getFullKeyJoinWithColon(Long... keySuffix) {
        return this.prefix + Arrays.stream(keySuffix).map(String::valueOf).collect(Collectors.joining(COLON));
    }
    public String joinKey(Object...args) {
        return this.prefix + Joiner.on("_").join(args);
    }
}
