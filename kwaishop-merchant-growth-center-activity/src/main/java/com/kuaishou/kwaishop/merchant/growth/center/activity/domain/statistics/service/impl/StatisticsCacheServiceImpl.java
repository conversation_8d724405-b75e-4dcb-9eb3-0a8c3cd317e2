package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_SYNC_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.BATCH_EXECUTE_IDEMPOTENCE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.BATCH_EXECUTE_TOTAL_USER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.BATCH_FAIL_USER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.BATCH_SUCCESS_USER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.STATISTICS_CHANGE_SYNC_LOCK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRedisKeyEnum.STATISTICS_PERFORMANCE_DATA_LOCK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.batchExecuteFrameworkRedisDataExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.performanceDataAggHourExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.roiIdempotenceLockExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.statisticsChangeSyncLockExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToStringBasic;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;

import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Sets;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.BatchExecuteResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;

import io.lettuce.core.SetArgs;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-19
 */
@Slf4j
@Lazy
@Service
public class StatisticsCacheServiceImpl implements StatisticsCacheService {
    @Override
    public boolean setRoiCalcLock(String eventId, int roiType) {
        String redisKey = BATCH_EXECUTE_IDEMPOTENCE.getFullKeyJoinWithColon(String.valueOf(roiType), eventId);
        // 尝试setNx,过期时间为24小时
        String res = getGrowthRedisCommands().set(redisKey, String.valueOf(System.currentTimeMillis()),
                SetArgs.Builder.nx().ex(roiIdempotenceLockExpireTime.get()));
        return "OK".equals(res);
    }

    @Override
    public boolean existRoiCalcLock(String eventId, int roiType) {
        String redisKey = BATCH_EXECUTE_IDEMPOTENCE.getFullKeyJoinWithColon(String.valueOf(roiType), eventId);
        return getGrowthRedisCommands().exists(redisKey) == 1;
    }

    @Override
    public void setBatchExecuteTotalNum(String eventId, long totalNum) {
        // 上线总人数
        String totalRedisKey = BATCH_EXECUTE_TOTAL_USER.getFullKeyJoinWithColon(eventId);
        getGrowthRedisCommands().set(totalRedisKey, String.valueOf(totalNum),
                SetArgs.Builder.ex(batchExecuteFrameworkRedisDataExpireTime.get()));
    }

    @Override
    public long getBatchExecuteTotalNum(String eventId) {
        // 上线总人数
        String totalRedisKey = BATCH_EXECUTE_TOTAL_USER.getFullKeyJoinWithColon(eventId);
        return Long.parseLong(getGrowthRedisCommands().get(totalRedisKey));
    }

    @Override
    public boolean batchExecuteCheckUserAlreadyDone(String eventId, Integer executeType, long userId) {
        // 完成用户数
        String successRedisKey = BATCH_SUCCESS_USER.getFullKeyJoinWithColon(eventId);
        if (getGrowthRedisCommands().sismember(successRedisKey, String.valueOf(userId))) {
            return true;
        }
        // 失败用户数
        String failRedisKey = BATCH_FAIL_USER.getFullKeyJoinWithColon(eventId);
        if (getGrowthRedisCommands().sismember(failRedisKey, String.valueOf(userId))) {
            return true;
        }
        return false;
    }

    @Override
    public boolean batchExecuteCheckUserAlreadySuccess(String eventId, Integer executeType, long userId) {
        // 完成用户数
        String successRedisKey = BATCH_SUCCESS_USER.getFullKeyJoinWithColon(eventId);
        return getGrowthRedisCommands().sismember(successRedisKey, String.valueOf(userId));
    }

    @Override
    public void batchExecuteAddUserToSuccessList(String eventId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        // 上线完成用户数
        String successRedisKey = BATCH_SUCCESS_USER.getFullKeyJoinWithColon(eventId);
        getGrowthRedisCommands().sadd(successRedisKey, userIds.stream().map(String::valueOf).toArray(String[]::new));
        // 设置过期时间
        getGrowthRedisCommands().expire(successRedisKey, batchExecuteFrameworkRedisDataExpireTime.get());
    }

    @Override
    public boolean setStatisticsChangeSyncLock(long userId, long activityId, long changeTime) {
        String lockKey = STATISTICS_CHANGE_SYNC_LOCK.getFullKeyJoinWithColon(userId, activityId);
        String res = getGrowthRedisCommands().set(lockKey, String.valueOf(changeTime),
                SetArgs.Builder.nx().ex(statisticsChangeSyncLockExpireTime.get()));
        return "OK".equals(res);
    }

    @Override
    public Long getStatisticsChangeSyncLock(long userId, long activityId) {
        String lockKey = STATISTICS_CHANGE_SYNC_LOCK.getFullKeyJoinWithColon(userId, activityId);
        if (getGrowthRedisCommands().exists(lockKey) == 1) {
            // 兼容，如果get为null，当不存在处理
            String lockTime = getGrowthRedisCommands().get(lockKey);
            if (StringUtils.isBlank(lockTime)) {
                perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, "redis.exists.null");
                return null;
            }
            return Long.parseLong(lockTime);
        }
        return null;
    }

    @Override
    public void delStatisticsChangeSyncLock(long userId, long activityId) {
        String lockKey = STATISTICS_CHANGE_SYNC_LOCK.getFullKeyJoinWithColon(userId, activityId);
        getGrowthRedisCommands().del(lockKey);
    }
    @Override
    public void batchExecuteAddUserToFailList(String eventId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        // 失败用户数
        String failRedisKey = BATCH_FAIL_USER.getFullKeyJoinWithColon(eventId);
        getGrowthRedisCommands().sadd(failRedisKey, userIds.stream().map(String::valueOf).toArray(String[]::new));
        // 设置过期时间
        getGrowthRedisCommands().expire(failRedisKey, batchExecuteFrameworkRedisDataExpireTime.get());
    }

    @Override
    public BatchExecuteResult getBatchExecuteResult(String eventId) {
        // 上线总人数
        String totalRedisKey = BATCH_EXECUTE_TOTAL_USER.getFullKeyJoinWithColon(eventId);
        long totalValue = 0;
        String totalRedisValue = getGrowthRedisCommands().get(totalRedisKey);
        if (StringUtils.isNotBlank(totalRedisValue)) {
            totalValue = Long.parseLong(totalRedisValue);
        }
        // 失败用户数
        String failRedisKey = BATCH_FAIL_USER.getFullKeyJoinWithColon(eventId);
        long failUserNum = getGrowthRedisCommands().scard(failRedisKey);
        // 上线完成用户数
        String successRedisKey = BATCH_SUCCESS_USER.getFullKeyJoinWithColon(eventId);
        long successUserNum = getGrowthRedisCommands().scard(successRedisKey);
        // 是否完成
        boolean finish = totalValue == failUserNum + successUserNum;
        Set<String> failUserList = Sets.newHashSet();
        if (finish) {
            failUserList = getGrowthRedisCommands().smembers(failRedisKey);
        }
        // 组装
        BatchExecuteResult result = new BatchExecuteResult();
        result.setEventId(eventId);
        result.setFinish(finish);
        result.setTotalNum(totalValue);
        result.setSuccessUserNum(successUserNum);
        result.setFailUserNum(failUserNum);
        result.setFailUserList(failUserList);
        return result;
    }

    @Override
    public boolean setPerformanceDataAggLock(long activityId, int checkHour) {
        long currentTime = System.currentTimeMillis();
        String currentDate = milliToStringBasic(currentTime);
        String lockKey = STATISTICS_PERFORMANCE_DATA_LOCK
                .getFullKeyJoinWithColon(String.valueOf(activityId), currentDate, String.valueOf(checkHour));
        String res = getGrowthRedisCommands().set(lockKey, String.valueOf(currentTime),
                SetArgs.Builder.nx().ex(performanceDataAggHourExpireTime.get()));
        return "OK".equals(res);
    }


}
