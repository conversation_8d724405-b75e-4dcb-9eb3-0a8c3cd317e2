package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Getter
@AllArgsConstructor
public enum LaunchDataSourceTypeEnum {
    UNKNOWN("unknown", "未知"),
    ACTIVITY_CONFIG_DATA("activityConfigData", "活动配置数据"),
    KCONF_COMMON_DATA("kconfCommonData", "kconf通用配置数据"),
    TASK_CONFIG_DATA("taskConfigData", "任务配置数据"),
    USER_AWARD_DATA("userAwardData", "用户奖励数据"),
    USER_INDICATOR_DATA("userIndicatorData", "用户指标数据"),
    USER_TASK_DATA("userTaskData", "用户任务数据"),
    USER_INVESTMENT_ACTIVITY_DATA("userInvestmentActivityData", "用户招商活动数据"),
    INDICATOR_META_DATA("indicatorMetaData", "指标元数据"),
    ;


    private final String type;

    private final String desc;

    public static LaunchDataSourceTypeEnum getByType(String type) {
        return Arrays.stream(values()).filter(e -> StringUtils.equals(e.getType(), type)).findFirst().orElse(UNKNOWN);
    }
}
