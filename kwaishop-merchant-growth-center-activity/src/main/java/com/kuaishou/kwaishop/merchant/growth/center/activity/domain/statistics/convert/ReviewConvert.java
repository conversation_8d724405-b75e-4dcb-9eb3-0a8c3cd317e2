package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJson;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.UserRegistrationRecordResolver.fillJsonDataMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.ACTIVITY_DAYS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_AVG_DATA;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.resolver.CommonResolver.getAwardRiskReason;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRecordStatusEnum.OPERATION_SENDING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRecordStatusEnum.isNeedStatistics;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeFenUnitAwardValueToYuan;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.converter.IndicatorResolver.resolveIndicatorRecordStepInfo;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum.RISK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.BASE_ACTIVITY_TOTAL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.BASE_AVG;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.BASE_TOTAL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.DEFAULT_TABLE_CONTENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.PARENT_TASK_DESC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STAGE_SUFFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STATISTICS_USER_DETAIL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils.getDynamicTableHeaderConfig;
import static com.kuaishou.kwaishop.merchant.growth.utils.converter.IndicatorUnitUtils.convertToShowValue;
import static java.math.RoundingMode.HALF_UP;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.AwardResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRecordStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.UserIndicatorStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.DynamicTableHeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.HeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.DynamicTableBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserAwardReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserIndicatorReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserTaskReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.annotation.CodeNote;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.DistributorDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.UserAuditRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsBaseInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsEsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsForecastInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsTaskInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-21
 */
@Slf4j
public class ReviewConvert {

    private static final long TEN_THOUSAND = 10000;

    public static Map<String, Object> buildStatisticsEsUpdateMap(StatisticsEsDO statisticsEsDO) {
        Map<String, Object> statisticsMap = Maps.newHashMap();
        if (statisticsEsDO == null) {
            return statisticsMap;
        }
        if (statisticsEsDO.getDraw() != null) {
            statisticsMap.put("draw", statisticsEsDO.getDraw());
        }
        if (statisticsEsDO.getAward() != null) {
            statisticsMap.put("award", statisticsEsDO.getAward());
        }
        if (statisticsEsDO.getUpdateVersion() != null) {
            statisticsMap.put("updateVersion", statisticsEsDO.getUpdateVersion());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getActivityIndicatorInfo())) {
            statisticsMap.put("activityIndicatorInfo", statisticsEsDO.getActivityIndicatorInfo());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getTaskInfo())) {
            statisticsMap.put("taskInfo", statisticsEsDO.getTaskInfo());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getAwardInfo())) {
            statisticsMap.put("awardInfo", statisticsEsDO.getAwardInfo());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getIndicatorInfo())) {
            statisticsMap.put("indicatorInfo", statisticsEsDO.getIndicatorInfo());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getForecastInfo())) {
            statisticsMap.put("forecastInfo", statisticsEsDO.getForecastInfo());
        }
        if (CollectionUtils.isNotEmpty(statisticsEsDO.getBaseInfo())) {
            statisticsMap.put("baseInfo", statisticsEsDO.getBaseInfo());
        }
        return statisticsMap;
    }

    /**
     * 构建活动维度信息
     */
    public static StatisticsDimensionBO buildActivityDimension(long activityId,
            List<IndicatorConfigDO> baseIndicatorList) {
        StatisticsDimensionBO activityDimension = new StatisticsDimensionBO();
        activityDimension.setEntityId(activityId);
        activityDimension.setEntityType(EntityTypeEnum.ACTIVITY);
        activityDimension.setEntityParam(buildEntityParam("activityId", activityId));
        if (CollectionUtils.isNotEmpty(baseIndicatorList)) {
            List<Long> baseIndicatorIdList = baseIndicatorList.stream()
                    .map(IndicatorConfigDO::getIndicatorId).distinct().collect(Collectors.toList());
            activityDimension.setBaseIndicatorList(baseIndicatorIdList);
        }
        return activityDimension;
    }

    /**
     * 构建任务维度信息
     */
    public static StatisticsDimensionBO buildTaskDimension(long taskId) {
        StatisticsDimensionBO taskDimension = new StatisticsDimensionBO();
        taskDimension.setEntityId(taskId);
        taskDimension.setEntityType(EntityTypeEnum.TASK);
        taskDimension.setEntityParam(buildEntityParam("taskId", taskId));
        return taskDimension;
    }

    /**
     * 构建子活动维度信息
     */
    public static StatisticsDimensionBO buildSubActivityDimension(long entityId, List<Long> parentTaskIdList,
            List<IndicatorConfigDO> baseIndicatorList) {
        StatisticsDimensionBO subActivityDimension = new StatisticsDimensionBO();
        subActivityDimension.setEntityId(entityId);
        subActivityDimension.setEntityType(EntityTypeEnum.SUB_ACTIVITY);
        subActivityDimension.setEntityParam(buildEntityParam("parentTaskIdList", parentTaskIdList));
        if (CollectionUtils.isNotEmpty(baseIndicatorList)) {
            List<Long> baseIndicatorIdList = baseIndicatorList.stream()
                    .filter(e -> parentTaskIdList.contains(e.getEntityId()))
                    .map(IndicatorConfigDO::getIndicatorId).distinct().collect(Collectors.toList());
            subActivityDimension.setBaseIndicatorList(baseIndicatorIdList);
        }
        return subActivityDimension;
    }

    /**
     * 构建分层维度信息
     */
    public static StatisticsDimensionBO buildLayerDimension(long entityId, long parentTaskId,
            List<Long> baseIndicatorList) {
        StatisticsDimensionBO layerDimension = new StatisticsDimensionBO();
        layerDimension.setEntityId(entityId);
        layerDimension.setEntityType(EntityTypeEnum.LAYER);
        layerDimension.setEntityParam(buildEntityParam("parentTaskId", parentTaskId));
        layerDimension.setBaseIndicatorList(baseIndicatorList);
        return layerDimension;
    }

    /**
     * 构建分层周期维度信息
     */
    public static StatisticsDimensionBO buildLayerPeriodDimension(long entityId, List<Long> taskIdList,
            List<Long> baseIndicatorList) {
        StatisticsDimensionBO periodDimension = new StatisticsDimensionBO();
        periodDimension.setEntityId(entityId);
        periodDimension.setEntityType(EntityTypeEnum.PERIOD);
        periodDimension.setEntityParam(buildEntityParam("taskIdList", taskIdList));
        periodDimension.setBaseIndicatorList(baseIndicatorList);
        return periodDimension;
    }

    /**
     * 构建实体参数
     */
    public static Map<String, Object> buildEntityParam(String key, Object value) {
        Map<String, Object> res = Maps.newHashMap();
        res.put(key, value);
        return res;
    }

    /**
     * 构建统计预估信息
     */
    public static StatisticsForecastInfoBO convertStatisticsForecastInfo(ForecastTaskInfo forecastTaskInfo) {
        StatisticsForecastInfoBO forecastInfoBO = new StatisticsForecastInfoBO();
        forecastInfoBO.setTaskId(forecastTaskInfo.getTaskId());
        forecastInfoBO.setParentTaskId(forecastTaskInfo.getParentTaskId());
        forecastInfoBO.setForecastAward(forecastTaskInfo.getBusinessForecastValue());
        return forecastInfoBO;
    }

    /**
     * 构建统计初始化任务信息
     */
    public static List<StatisticsTaskInfoBO> buildInitStatisticsTask(List<UserRegistrationRecordDO> records) {
        return records.stream().map(ReviewConvert::buildInitStatisticsTask).collect(Collectors.toList());
    }

    public static StatisticsTaskInfoBO buildInitStatisticsTask(UserRegistrationRecordDO record) {
        StatisticsTaskInfoBO parentTaskInfo = new StatisticsTaskInfoBO();
        parentTaskInfo.setTaskId(record.getEntityId());
        parentTaskInfo.setParentTaskId(0L);
        parentTaskInfo.setIsSuccess(0L);
        parentTaskInfo.setIsPredictSuccess(0L);
        parentTaskInfo.setStatus(UserTaskStatusEnum.DRAWING.getValue());
        parentTaskInfo.setIsRisk(0L);
        if (record.getStatus().equals(RISK.getCode())) {
            parentTaskInfo.setIsRisk(1L);
        }
        return parentTaskInfo;
    }

    /**
     * 构建统计指标信息
     */
    public static List<StatisticsIndicatorInfoBO> buildStatisticsIndicator(List<IndicatorRecordDO> userIndicatorList,
            Map<Long, Map<String, Object>> basicDataMap, Map<Long, Long> parentChildMap,
            Map<Long, TaskDO> taskConfigMap) {
        if (CollectionUtils.isEmpty(userIndicatorList)) {
            return Lists.newArrayList();
        }
        return userIndicatorList.stream()
                .map(e -> buildIndicatorRecordDO(e, basicDataMap, parentChildMap, taskConfigMap))
                .collect(Collectors.toList());
    }

    public static StatisticsIndicatorInfoBO buildIndicatorRecordDO(IndicatorRecordDO userIndicatorRecord,
            Map<Long, Map<String, Object>> basicDataMap, Map<Long, Long> parentChildMap,
            Map<Long, TaskDO> taskConfigMap) {
        long taskId = userIndicatorRecord.getEntityId();
        long indicatorId = userIndicatorRecord.getIndicatorId();
        TaskDO taskConfig = taskConfigMap.get(taskId);
        StatisticsIndicatorInfoBO indicatorInfo = new StatisticsIndicatorInfoBO();
        indicatorInfo.setTaskId(taskId);
        indicatorInfo.setIndicatorId(indicatorId);
        indicatorInfo.setCurrentValue(userIndicatorRecord.getCurrentValue());
        indicatorInfo.setTargetValue(userIndicatorRecord.getTargetValue());
        indicatorInfo.setStatus(userIndicatorRecord.getStatus());
        indicatorInfo.setPriority(taskConfig.getPriority());
        // 指标阶梯信息
        Map<Integer, Long> stepInfo = resolveIndicatorRecordStepInfo(userIndicatorRecord, taskConfig);
        if (MapUtils.isNotEmpty(stepInfo)) {
            indicatorInfo.setStepList(stepInfo);
        }
        if (!parentChildMap.containsKey(taskId)) {
            return indicatorInfo;
        }
        // 父任务基值
        long parentTaskId = parentChildMap.get(taskId);
        Map<String, Object> parentBasicData = basicDataMap.getOrDefault(parentTaskId, Maps.newHashMap());
        String basicValueKey = BASE_INDICATOR_AVG_DATA.format(indicatorId);
        String baseDayAvgStr = MapUtils.getString(parentBasicData, basicValueKey, "0");
        long basicValue = new BigDecimal(baseDayAvgStr).setScale(0, RoundingMode.UP).longValue();
        indicatorInfo.setBaseAvgValue(basicValue);
        // 计算指标增量
        if (parentBasicData.containsKey(ACTIVITY_DAYS.key())) {
            // 活动有效天数
            long activityDays = MapUtils.getLong(parentBasicData, ACTIVITY_DAYS.key(), 0L);
            long incrementValue = userIndicatorRecord.getCurrentValue() - basicValue * activityDays;
            indicatorInfo.setIncrementValue(Math.max(incrementValue, 0));
        }
        return indicatorInfo;
    }

    public static List<StatisticsAwardInfoBO> buildStatisticsAward(List<UserAwardRecordDO> userAwardRecordList,
            Map<Long, Long> parentChildMap) {
        if (CollectionUtils.isEmpty(userAwardRecordList)) {
            return Lists.newArrayList();
        }
        // 状态过滤
        userAwardRecordList = userAwardRecordList.stream()
                .filter(e -> isNeedStatistics(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userAwardRecordList)) {
            return Lists.newArrayList();
        }
        List<StatisticsAwardInfoBO> res = Lists.newArrayList();
        // 按任务聚合
        Map<Long, List<UserAwardRecordDO>> userTaskAwardMap = userAwardRecordList.stream()
                .collect(Collectors.groupingBy(UserAwardRecordDO::getEntityId));
        // 任务下奖励类型数据合并
        userTaskAwardMap.forEach((taskId, awardList) -> {
            long parentTaskId = MapUtils.getLong(parentChildMap, taskId, 0L);
            Map<Integer, List<UserAwardRecordDO>> awardTypeMap = awardList.stream()
                    .collect(Collectors.groupingBy(UserAwardRecordDO::getAwardType));
            awardTypeMap.forEach(
                    (awardType, list) -> res.add(buildStatisticsAwardInfoBO(taskId, parentTaskId, awardType, list)));
        });
        return res;
    }

    public static StatisticsAwardInfoBO buildStatisticsAwardInfoBO(long taskId, long parentTaskId, int awardType,
            List<UserAwardRecordDO> list) {
        long sumAwardValue = list.stream().filter(e -> e.getStatus().intValue() != OPERATION_SENDING.getCode())
                .mapToLong(UserAwardRecordDO::getAwardValue).sum();
        long originAwardValue = list.stream().mapToLong(AwardResolver::resolveOriginAwardValue).sum();
        StatisticsAwardInfoBO awardInfo = new StatisticsAwardInfoBO();
        awardInfo.setParentTaskId(parentTaskId);
        awardInfo.setTaskId(taskId);
        awardInfo.setAwardType(awardType);
        awardInfo.setInitAwardValue(originAwardValue);
        awardInfo.setFinalAwardValue(sumAwardValue);
        return awardInfo;
    }

    @CodeNote(value = "解析用户报名记录的json数据", modified = true)
    public static Map<String, Object> buildStatisticsBaseInfoBO(long parentTaskId, DistributorDataBO baseData,
                                                                String jsonData, List<TaskDO> childTasks) {
        StatisticsBaseInfoBO statisticsBaseInfoBO = new StatisticsBaseInfoBO();
        statisticsBaseInfoBO.setTaskId(parentTaskId);
        statisticsBaseInfoBO.setBaseTrUp(baseData.getSlrOrderSettlerAmt());
        statisticsBaseInfoBO.setBaseTrDown(baseData.getSlrSettlePayOrderAmt());
        statisticsBaseInfoBO.setBaseTr(getTenThousand(baseData.getTrRate()));
        // 退款率分子
        statisticsBaseInfoBO.setBaseRefundRateUp(
                getBaseRefundRateUp(baseData.getSlrRefundOrderAmt(), baseData.getSlrRefundPayOrderAmt()));
        statisticsBaseInfoBO.setBaseRefundRateDown(baseData.getSlrRefundPayOrderAmt());
        // 未退款率
        statisticsBaseInfoBO.setBaseRefundRate(getRefundRateTenThousand(baseData.getUnRefundRate()));
        Map<String, Object> baseInfo = fromJson(toJSON(statisticsBaseInfoBO));
        // 基值指标均值
        if (StringUtils.isBlank(jsonData)) {
            return baseInfo;
        }
        Map<String, Object> jsonBaseData = fromJson(jsonData);
        // 增加基值总值
        baseInfo.putAll(calcActivityBaseIndicatorTotal(jsonBaseData));
        // 子任务基值
        fillJsonDataMap(childTasks, jsonBaseData, baseInfo);
        return baseInfo;
    }


    private static long getBaseRefundRateUp(long slrRefundOrderAmt, long slrRefundPayOrderAmt) {
        long refundRateUp = slrRefundPayOrderAmt - slrRefundOrderAmt;
        if (refundRateUp < 0) {
            return 0;
        }
        return refundRateUp;
    }

    private static long getRefundRateTenThousand(String unRefundRate) {
        BigDecimal refundRate = new BigDecimal(1).subtract(new BigDecimal(unRefundRate));
        return refundRate.multiply(new BigDecimal(TEN_THOUSAND)).setScale(0, HALF_UP)
                .longValue();
    }

    private static long getTenThousand(String value) {
        return new BigDecimal(value).multiply(new BigDecimal(TEN_THOUSAND)).setScale(0, HALF_UP)
                .longValue();
    }

    public static Map<String, Object> calcActivityBaseIndicatorTotal(Map<String, Object> baseData) {
        // 基期均值 * 活动天数
        long activityDays = MapUtils.getLong(baseData, ACTIVITY_DAYS.getTemplate(), 0L);
        Map<String, Object> result = new HashMap<>();
        baseData.forEach((key, value) -> {
            if (key.startsWith(BASE_AVG)) {
                long activityBaseIndicatorTotal = new BigDecimal(String.valueOf(value))
                        .multiply(new BigDecimal(String.valueOf(activityDays))).longValue();
                String totalKey = key.replace(BASE_AVG, BASE_ACTIVITY_TOTAL);
                result.put(totalKey, activityBaseIndicatorTotal);
            } else if (key.startsWith(BASE_TOTAL)) {
                // 新增基期计算字段
                long activityBaseIndicatorTotal = new BigDecimal(String.valueOf(value)).longValue();
                String totalKey = key.replace(BASE_TOTAL, BASE_ACTIVITY_TOTAL);
                result.put(totalKey, activityBaseIndicatorTotal);
            }
        });
        return result;
    }

    public static UserAwardReviewDetailBO convertUserAwardReviewDetailBO(UserAwardRecordDO userAwardRecord,
            AwardConfigDO awardConfig, UserAuditRecordDO userAuditRecord) {
        AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardConfig.getAwardType());
        // 奖励状态
        AwardRecordStatusEnum recordStatusEnum = AwardRecordStatusEnum.of(userAwardRecord.getStatus());
        // 风控原因
        String riskReason = null;
        if (recordStatusEnum.equals(AwardRecordStatusEnum.RISKED)) {
            riskReason = getAwardRiskReason(userAuditRecord);
        }
        // 奖励数值转换
        String awardShowCount = changeFenUnitAwardValueToYuan(awardTypeEnum, userAwardRecord.getAwardValue());
        UserAwardReviewDetailBO userAwardReviewDetailBO = new UserAwardReviewDetailBO();
        userAwardReviewDetailBO.setTaskId(userAwardRecord.getEntityId());
        userAwardReviewDetailBO.setAwardName(awardConfig.getAwardName());
        userAwardReviewDetailBO.setAwardType(awardTypeEnum.getShowName());
        userAwardReviewDetailBO.setUpdateTime(userAwardRecord.getUpdateTime());
        userAwardReviewDetailBO.setAwardCount(userAwardRecord.getAwardValue());
        userAwardReviewDetailBO.setStatus(recordStatusEnum.getDesc());
        userAwardReviewDetailBO.setRiskReason(riskReason);
        userAwardReviewDetailBO.setAwardShowCount(awardShowCount + awardTypeEnum.getUnit());
        return userAwardReviewDetailBO;
    }

    public static UserIndicatorReviewDetailBO convertUserIndicatorReviewDetailBO(IndicatorRecordDO userIndicatorRecord,
            TaskDO taskDO, IndicatorDO indicatorDO) {
        String unit = indicatorDO.getUnit();
        long currentValue = userIndicatorRecord.getCurrentValue();
        long targetValue = userIndicatorRecord.getTargetValue();
        String progressRate = "0%";
        if (targetValue > 0) {
            progressRate = BigDecimal.valueOf(currentValue).multiply(new BigDecimal(100))
                    .divide(BigDecimal.valueOf(targetValue), 2, HALF_UP).toPlainString() + "%";
        }
        if (userIndicatorRecord.getIndicatorId() != null
                && IndicatorEnum.RANK.getValue() == userIndicatorRecord.getIndicatorId()) {
            if (currentValue > 0) {
                currentValue = Long.MAX_VALUE - currentValue;
            }
            targetValue = Long.MAX_VALUE - targetValue;
        }
        UserIndicatorReviewDetailBO res = new UserIndicatorReviewDetailBO();
        res.setTaskId(userIndicatorRecord.getEntityId());
        res.setTaskName(taskDO.getName());
        res.setIndicatorId(userIndicatorRecord.getIndicatorId());
        res.setIndicatorName(indicatorDO.getName());
        res.setCurrentValue(currentValue);
        res.setTargetValue(targetValue);
        res.setStartTime(userIndicatorRecord.getStartTime());
        res.setEndTime(userIndicatorRecord.getEndTime());
        res.setStatus(UserIndicatorStatusEnum.of(userIndicatorRecord.getStatus()).getType());
        res.setProgressRate(progressRate);
        res.setCurrentValueStr(convertToShowValue(currentValue, unit, false) + unit);
        res.setTargetValueStr(convertToShowValue(targetValue, unit, false) + unit);
        return res;
    }

    public static UserTaskReviewDetailBO convertUserTaskReviewDetailBO(UserTaskRecordDO userTaskRecord, TaskDO taskDO) {
        UserTaskReviewDetailBO userTaskReviewDetailBO = new UserTaskReviewDetailBO();
        userTaskReviewDetailBO.setId(userTaskRecord.getId());
        userTaskReviewDetailBO.setStartTime(userTaskRecord.getStartTime());
        userTaskReviewDetailBO.setEndTime(userTaskRecord.getEndTime());
        userTaskReviewDetailBO.setStatus(UserTaskStatusEnum.of(userTaskRecord.getStatus()).getType());
        userTaskReviewDetailBO.setParentTask(userTaskRecord.getParentId());
        userTaskReviewDetailBO.setName(taskDO.getName());
        // 父任务
        if (taskDO.getParentTask() == 0) {
            userTaskReviewDetailBO.setStage(PARENT_TASK_DESC);
        } else {
            userTaskReviewDetailBO.setStage(String.format(STAGE_SUFFIX, taskDO.getStage()));
        }
        return userTaskReviewDetailBO;
    }

    public static Map<String, Object> convertUserReviewDetailBO(Map<String, Object> esData,
            UserReviewDetailBO userRealtimeData, List<HeaderConfig> headerConfigs) {
        StatisticsEsDO statisticsEsDO = fromJSON(toJSON(esData), StatisticsEsDO.class);
        UserReviewDetailBO userReviewDetailBO = new UserReviewDetailBO();
        userReviewDetailBO.setUserId(statisticsEsDO.getUserId());
        userReviewDetailBO.setUserName(statisticsEsDO.getUserName());
        userReviewDetailBO.setUserHeadUrl(statisticsEsDO.getUserHeadUrl());
        if (userRealtimeData != null) {
            userReviewDetailBO.setTaskInfo(userRealtimeData.getTaskInfo());
            userReviewDetailBO.setIndicatorInfo(userRealtimeData.getIndicatorInfo());
            userReviewDetailBO.setAwardInfo(userRealtimeData.getAwardInfo());
            userReviewDetailBO.setRegistrationRiskReason(userRealtimeData.getRegistrationRiskReason());
            userReviewDetailBO.setDrawRiskDesc(userRealtimeData.getDrawRiskDesc());
            userReviewDetailBO.setDrawRiskReason(userRealtimeData.getDrawRiskReason());
        }
        Map<String, Object> tableData = fromJson(toJSON(userReviewDetailBO));
        for (HeaderConfig headerConfig : headerConfigs) {
            if (!tableData.containsKey(headerConfig.getField()) && !"info".equals(headerConfig.getShowType())) {
                tableData.put(headerConfig.getField(),
                        esData.getOrDefault(headerConfig.getField(), DEFAULT_TABLE_CONTENT));
            }
        }
        return tableData;
    }

    public static DynamicTableBO convertEmptyDynamicTableBO() {
        DynamicTableHeaderConfig tableHeaderConfig = getDynamicTableHeaderConfig(STATISTICS_USER_DETAIL);
        List<HeaderConfig> headerConfigs = new ArrayList<>(tableHeaderConfig.getHeaderConfigs());
        return convertDynamicTableBO(headerConfigs, Lists.newArrayList());
    }

    public static DynamicTableBO convertDynamicTableBO(List<HeaderConfig> headerConfigs,
            List<Map<String, Object>> userData) {
        DynamicTableBO res = new DynamicTableBO();
        res.setData(userData);
        res.setHeader(headerConfigs);
        res.setTotal((long) userData.size());
        return res;
    }
}
