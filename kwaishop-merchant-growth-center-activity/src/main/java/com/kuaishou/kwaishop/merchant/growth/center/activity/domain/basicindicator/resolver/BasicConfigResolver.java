package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.resolver;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.BasicConfigConstants.BASIC_FACTOR_CODE_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.BasicConfigConstants.BASIC_FACTOR_TEMPLATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.BasicConfigConstants.BASIC_FORMULA_MAX_TEMPLATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.BasicConfigConstants.BASIC_FORMULA_MIN_TEMPLATE;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.google.api.client.util.Lists;
import com.google.api.client.util.Preconditions;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicSystemCalcConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.BasicCalcTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndicatorTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-26
 */
@Slf4j
public class BasicConfigResolver {
    /**
     * 获取基期中所有指标配置的所有因子
     */
    public static List<BasicFactorConfigBO> getAllBasicFactorConfig(BasicConfigBO basicConfigBO) {
        if (null == basicConfigBO) {
            return Lists.newArrayList();
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        if (CollectionUtils.isEmpty(basicIndicatorConfigList)) {
            return Lists.newArrayList();
        }
        return basicIndicatorConfigList.stream().flatMap(basicIndicatorConfigBO ->
                getAllBasicFactorConfig(basicIndicatorConfigBO).stream()).collect(Collectors.toList());
    }

    /**
     * 获取基期中所有指标配置的所有因子(会按照基期结束时间倒序排序)
     */
    public static List<BasicFactorConfigBO> getAllBasicFactorConfigSort(BasicConfigBO basicConfigBO) {
        if (null == basicConfigBO) {
            return Lists.newArrayList();
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        if (CollectionUtils.isEmpty(basicIndicatorConfigList)) {
            return Lists.newArrayList();
        }
        try {
            return basicIndicatorConfigList.stream().flatMap(basicIndicatorConfigBO ->
                            getAllBasicFactorConfig(basicIndicatorConfigBO).stream())
                    // 按基期结束时间倒序排序
                    .sorted(Comparator.comparing(BasicFactorConfigBO::getFixedEndTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getAllBasicFactorConfigSort error, basicConfigBO:{}", toJSON(basicConfigBO), e);
            return getAllBasicFactorConfig(basicConfigBO);
        }
    }

    /**
     * 获取基期中某个指标配置的所有因子
     */
    public static List<BasicFactorConfigBO> getAllBasicFactorConfig(BasicIndicatorConfigBO basicIndicatorConfigBO) {
        List<BasicFactorConfigBO> list = Lists.newArrayList();
        if (null == basicIndicatorConfigBO) {
            return list;
        }
        switch (basicIndicatorConfigBO.getBasicValueType()) {
            case CUSTOMIZE:
                list.add(basicIndicatorConfigBO.getBasicCustomConfig().getCustomizeBasicConfig());
                break;
            case SYSTEM_CALC:
                BasicSystemCalcConfigBO basicSystemCalcConfig = basicIndicatorConfigBO.getBasicSystemCalcConfig();
                List<BasicFactorConfigBO> basicFactorConfigList = basicSystemCalcConfig.getBasicFactorConfigList();
                list.addAll(basicFactorConfigList);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "非法因子类型");
        }
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(list), "基期因子不能为空");
        return list;
    }

    /**
     * 生成基期公式
     */
    public static String generateFormula(BasicSystemCalcConfigBO basicSystemCalcConfigBO) {
        List<BasicFactorConfigBO> basicFactorConfigList = basicSystemCalcConfigBO.getBasicFactorConfigList();

        AtomicInteger factorCount = new AtomicInteger(1);
        List<String> factors = basicFactorConfigList.stream().map(basicFactorConfigBO -> {
            BigDecimal coefficient = basicFactorConfigBO.getCoefficient();
            // 自定义默认系数为1
            if (coefficient == null) {
                coefficient = new BigDecimal(1);
            }

            String factorCode = String.format(BASIC_FACTOR_CODE_PREFIX, factorCount.getAndIncrement());
            basicFactorConfigBO.setFactorCode(factorCode);
            return String.format(BASIC_FACTOR_TEMPLATE, factorCode, coefficient.toPlainString());
        }).collect(Collectors.toList());
        if (basicFactorConfigList.size() == 1) {
            return factors.get(0);
        }
        BasicCalcTypeEnum basicCalcType = basicSystemCalcConfigBO.getBasicCalcType();
        switch (basicCalcType) {
            case max:
                return String.format(BASIC_FORMULA_MAX_TEMPLATE, String.join(",", factors));
            case min:
                return String.format(BASIC_FORMULA_MIN_TEMPLATE, String.join(",", factors));
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "非法计算类型");
        }
    }

    /**
     * 返回多个基期指标配置中存在相对偏移的指标配置
     */
    public static List<BasicIndicatorConfigBO> getRelativeBasicIndicatorConfigList(BasicConfigBO basicConfigBO) {
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        if (CollectionUtils.isEmpty(basicIndicatorConfigList)) {
            return Lists.newArrayList();
        }
        return basicIndicatorConfigList.stream()
                .filter(BasicConfigResolver::checkRelativeBasicIndicatorConfigBO)
                .collect(Collectors.toList());
    }

    /**
     * 校验是否存在相对偏移的指标配置
     */
    public static boolean checkRelativeBasicIndicatorConfigBO(BasicIndicatorConfigBO basicIndicatorConfigBO) {
        List<BasicFactorConfigBO> allBasicFactorConfig = getAllBasicFactorConfig(basicIndicatorConfigBO);
        return allBasicFactorConfig.stream().anyMatch(basicFactorConfigBO ->
                basicFactorConfigBO.getIndicatorTimeType().equals(IndicatorTimeTypeEnum.RELATIVE_TIME));
    }
}
