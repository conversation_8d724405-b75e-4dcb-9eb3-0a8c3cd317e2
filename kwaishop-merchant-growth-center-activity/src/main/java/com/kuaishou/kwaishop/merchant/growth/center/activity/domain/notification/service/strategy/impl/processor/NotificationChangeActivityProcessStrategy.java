package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.processor;

import static com.kuaishou.framework.concurrent.DynamicThreadExecutor.dynamic;
import static com.kuaishou.framework.supplier.DynamicSuppliers.dynamicRateLimiter;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum.PROCESSING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum.ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.NOTIFICATION_CHANGE_ACTIVITY_PROCESS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.notificationConsumerRegistrationSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationConsumerRegistrationLimit;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationCreateConsumerRateLimiterConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationCreateConsumerThreadPoolSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.notificationCreateAdminActivityList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.notificationCreateAdminUserList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushCreateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-27
 */
@Service
@Slf4j
public class NotificationChangeActivityProcessStrategy implements NotificationChangeProcessStrategy {

    @Autowired
    private NotificationPushCreateService notificationPushCreateService;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    // 触达配置创建消费扫表线程池（默认10个线程）
    private static final ExecutorService NOTIFICATION_CREATE_EXECUTOR =
            dynamic(notificationCreateConsumerThreadPoolSize::get, "notification-create-consumer");

    private static final DynamicRateLimiter RATE_LIMITER =
            dynamicRateLimiter(notificationCreateConsumerRateLimiterConfig);

    /**
     * 代码迁移 NotificationCreateConsumer
     */
    @Override
    public void process(long activityId, List<NotificationPushConfigBO> configs, long eventTime) {
        try {
            StopWatch sw = new StopWatch();
            log.info("[触达配置变更活动实体处理] 开始执行 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime);

            ActivityDO activityDO = activityDAO.queryById(activityId);
            if (activityDO == null) {
                log.warn("[触达配置变更活动实体处理] 活动元数据为空 activityId:{}", activityId);
                perfException(NOTIFICATION_CHANGE_ACTIVITY_PROCESS, "活动配置异常", String.valueOf(activityId));
                return;
            }

            // 活动维度放量
            if (!notificationCreateAdminActivityList.get().isOnFor(activityId)) {
                return;
            }

            // 根据 entityStatus 事件类型进行聚合
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap =
                    configs.stream().collect(Collectors.groupingBy(NotificationPushConfigBO::getEntityStatus));

            // 遍历资格记录
            int shardNum = userRegistrationRecordDAO.getMysqlShardCount();
            // 扫表资格记录表
            CountDownLatch latch = new CountDownLatch(shardNum);
            // 分shard处理
            for (int shard = 0; shard < shardNum; shard++) {
                int finalShard = shard;
                NOTIFICATION_CREATE_EXECUTOR.submit(() -> {
                    try {
                        handleOneShardRegistrationRecord(finalShard, activityId, entityStatusMap, eventTime);
                    } catch (Exception e) {
                        log.error("[触达配置变更活动实体处理] 异常！shard:{}", finalShard, e);
                        perfException(NOTIFICATION_CHANGE_ACTIVITY_PROCESS, "shard异常", e.getClass().getSimpleName());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            log.info("[触达配置变更活动实体处理] 执行成功 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime);
            perfSuccessWithWatch(NOTIFICATION_CHANGE_ACTIVITY_PROCESS, sw);
        } catch (Exception e) {
            log.error("[触达配置变更活动实体处理] 执行异常 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime, e);
            perfException(NOTIFICATION_CHANGE_ACTIVITY_PROCESS, "执行异常", String.valueOf(activityId),
                    e.getClass().getSimpleName());
        }
    }

    private void handleOneShardRegistrationRecord(int shard, long activityId,
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap, long eventTime) {
        int limit = notificationConsumerRegistrationLimit.get();
        long cursor = 0;
        // 限流
        RATE_LIMITER.acquire();
        // 针对每一个分片进行分页遍历
        while (true) {
            // 快速终止
            if (notificationConsumerRegistrationSwitch.get()) {
                break;
            }
            // 分页取记录
            List<UserRegistrationRecordDO> pageRecordList =
                    userRegistrationRecordDAO.cursorGetUserRegistrationRecords(activityId, EntityTypeEnum.ACTIVITY,
                            UserRegistrationStatusEnum.VALID, limit, cursor, shard);

            if (CollectionUtils.isEmpty(pageRecordList)) {
                break;
            }

            // 处理记录
            pageRecordList.forEach(record -> handleOneRecordRegistrationRecord(record, entityStatusMap, eventTime));
            // 赋值
            cursor = pageRecordList.get(pageRecordList.size() - 1).getId() + 1;

            perfSuccess(NOTIFICATION_CHANGE_ACTIVITY_PROCESS, "单分片执行完成", String.valueOf(shard));
        }
    }

    private void handleOneRecordRegistrationRecord(UserRegistrationRecordDO recordDO,
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap, Long eventTime) {
        if (!notificationCreateAdminUserList.get().isOnFor(recordDO.getUserId())) {
            log.info("[触达配置变更活动实体处理] 用户不在白名单 userId:{}, activityId:{}", recordDO.getUserId(),
                    recordDO.getActivityId());
            return;
        }


        entityStatusMap.forEach((entityStatus, configsOfCertainEntityStatus) -> {
            doHandleSingleUserRegistrationRecord(entityStatus, configsOfCertainEntityStatus, recordDO, eventTime);
        });
    }

    private void doHandleSingleUserRegistrationRecord(int entityStatus, List<NotificationPushConfigBO> configs,
            UserRegistrationRecordDO record, long eventTime) {
        Long userId = record.getUserId();
        Long createTime = record.getCreateTime();
        Long activityId = record.getActivityId();
        UserActivityRecordDO userActivityRecordDO =
                userActivityRecordDAO.queryUserActivityRecord(userId, activityId, false);

        UserActivityEventTypeEnum userEntityEventType = UserActivityEventTypeEnum.of(entityStatus);
        switch (userEntityEventType) {
            case REGISTRATION:
                // 资格记录失效跳过
                if (!UserRegistrationStatusEnum.VALID.getCode().equals(record.getStatus())
                        || record.getDeleted() != 0) {
                    log.info("[触达配置变更活动实体处理] 资格记录失效 userId:{}, activityId:{}",
                            userId, activityId);
                    return;
                }
                // 已领取跳过
                if (Objects.nonNull(userActivityRecordDO)) {
                    log.info("[触达配置变更活动实体处理] 用户已领取活动 userId:{}, activityId:{}",
                            userId, activityId);
                    return;
                }

                notificationPushCreateService.notificationPushCreate(userId, configs, createTime);
                return;
            case DRAW:
                // FIXME 扫表优化
                if (Objects.isNull(userActivityRecordDO)) {
                    log.info("[触达配置变更活动实体处理] 用户没有领取活动 userId:{}, activityId:{}",
                            userId, activityId);
                    return;
                }

                if (userActivityRecordDO.getStatus() != PROCESSING.getValue()) {
                    log.info("[触达配置变更活动实体处理] 用户活动不是进行中 userId:{}, activityId:{}",
                            userId, activityId);
                    return;
                }

                notificationPushCreateService.notificationPushCreate(userId, configs, createTime);
                return;
            default:
                //
        }
    }

    @Override
    public NotificationEntityTypeEnum getType() {
        return ACTIVITY;
    }
}
