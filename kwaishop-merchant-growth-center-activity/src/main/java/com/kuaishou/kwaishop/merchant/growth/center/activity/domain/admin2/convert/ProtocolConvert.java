package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.convert;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kconf.common.json.JsonMapperUtils.fromJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivityPatternTypeEnum.LEADERBOARD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType.ACTIVITY_STRATEGY_ADMIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityCreateTypeEnum.TASK_ADMIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityType.ACTIVITY_TYPE_TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.impl.IndustryActivityInfoConverter.buildAwardCondition;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.impl.IndustryActivityInfoConverter.buildDefaultTaskCompleteCondition;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.impl.IndustryActivityInfoConverter.buildIndicatorCompleteCondition;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.constant.TaskConstant.INDICATOR_TAG;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryAwardValueTypeEnum.AWARD_POOL_DISTRIBUTE_RULE_CALC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryAwardValueTypeEnum.CUSTOMIZE_RULE_CALC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.StepCompleteTypeEnum.getTaskTypeEnumByCode;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityCycleTypeEnum.STABLE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditSendExtDataTypeEnum.SPECIFY_INDICATOR_AUDIT_WITH_DETAIL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRuleInputEnum.RETURN_FORMULA_CALC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRuleInputEnum.SPECIFY_INDICATOR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRuleInputEnum.TR_INDICATOR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeFenUnitAwardValueToYuan;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeYuanUnitAwardValueToFenWithCheck;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.enums.ValueTypeEnum.RULE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConditionEnum.COURSE_LIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConditionEnum.INSURANCE_ACTIVITY_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConditionEnum.WECHAT_LIST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConfigStatusEnum.EFFECTIVE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskPeriodTypeEnum.RELATIVE_TYPE_ENUMS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.CrowdTypeEnum.REGISTRATION;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.AWARD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.PERIOD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.awardCustomizeRuleCalcConfigList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.showChannelMapEntrance;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.preAwardRelativeSendTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.strategyAdminAllSceneConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.calculateDayBetween;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.getAfterDateMilli;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToStringMMdd;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.AmountUtils.changeLi2Fen;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.getEndOfDay;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.getStartOfDay;
import static com.kuaishou.kwaishop.merchant.growth.utils.converter.IndicatorUnitUtils.convertToShowValue;
import static com.kuaishou.kwaishop.merchant.growth.utils.converter.IndicatorUnitUtils.convertToStorageValue;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static java.util.Collections.singletonList;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.TaskTimeBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.AwardCustomizeRuleCalcConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.IndicatorConfigExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.AwardDistributeRuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.CustomizeRuleCalcConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.FixedAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.IndustryAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.ReturnPercentConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.AssignIndicatorBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.IncreaseIndicatorBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.IndicatorStepConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.IndicatorTargetConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.indicator.IndustryIndicatorBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndustryAwardValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.TaskTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorDataFetchConfigResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.ExpireTimeConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.SendConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.AdminAllSceneConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.RuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ShowChannelMapEntranceConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.ActivityConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.AwardExcelFileBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.RankSubActivityConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.RankTraceConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.SubActivityStepConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.TargetExcelFileBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.AdminAwardDelayTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.AdminAwardRiskTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.AdminAwardSendTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.AwardTargetTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.CustomizeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ExpireTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.IndicatorTargetTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.bo.AuditConfigExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.bo.AuditSendConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditBeginTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditExpireActionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditPolicyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardInterestExtParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.ActivityAwardSendRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.AwardRankingConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.AwardSendRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.AwardStepConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.MagnetInterestBenefitInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.ruletype.MagnetInterestPackageInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardCalTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardDeductEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardSendRuleTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardSendTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.TaskAwardSendTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.DynamicValueConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.enums.ValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorCalcRangeConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorCalcRangeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorExecuteCompleteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.AwardManageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.IndicatorManageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.CycleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.CategoryBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.AuditConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.AmountUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.protobuf.ad.ecom.benefit.AdEcomBenefitInfo;
import com.kuaishou.protobuf.ad.ecom.benefit.AdEcomBenefitPackageInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-26
 */
public class ProtocolConvert {
    /**
     * 活动协议对象转换活动数据库对象
     */
    public static ActivityDO convertActivityDO(ActivityConfig activityProtoCol, String operator) {
        // 组装
        ActivityDO activityDO = new ActivityDO();
        activityDO.setId(activityProtoCol.getActivityId());
        activityDO.setName(activityProtoCol.getName());
        activityDO.setShowName(activityProtoCol.getName());
        activityDO.setDescription(activityProtoCol.getDesc());
        activityDO.setBizName(activityProtoCol.getIndustryCode());
        activityDO.setStartTime(activityProtoCol.getStartTime());
        activityDO.setEndTime(activityProtoCol.getEndTime());
        activityDO.setResourceActivityId(activityProtoCol.getResourceActivityId());
        activityDO.setCreateType(TASK_ADMIN.getCode());
        activityDO.setCrowdType(REGISTRATION.getCode());
        activityDO.setType(ACTIVITY_TYPE_TASK.getValue());
        activityDO.setAlias(String.valueOf(System.currentTimeMillis()));
        activityDO.setSeriesType(ACTIVITY_STRATEGY_ADMIN.getLongValue());
        activityDO.setCompleteCondition(buildDefaultTaskCompleteCondition());
        activityDO.setShowTag(activityProtoCol.getActivityTag());
        activityDO.setDrawEndTime(Optional.ofNullable(activityProtoCol.getDrawEndTime())
                .orElse(activityProtoCol.getEndTime()));
        activityDO.setCreator(operator);
        activityDO.setModifier(operator);
        return activityDO;
    }

    /**
     * 子活动协议对象转换任务数据库对象
     */
    public static TaskDO convertTaskDO(SubActivityConfig subActivityConfig, CycleConfigBO cycleConfigBO,
            String operator) {
        TaskDO taskDO = new TaskDO();
        taskDO.setName(subActivityConfig.getName());
        taskDO.setStartTime(subActivityConfig.getStartTime());
        taskDO.setEndTime(subActivityConfig.getEndTime());
        taskDO.setType(Objects.requireNonNull(getTaskTypeEnumByCode(subActivityConfig.getStepCompleteType())));
        taskDO.setPeriodType(subActivityConfig.getPeriodType());
        taskDO.setPeriodDay(subActivityConfig.getPeriodDay());
        taskDO.setStatus(TaskConfigStatusEnum.ONLINE.getValue());
        taskDO.setPreTask(0L);
        taskDO.setParentTask(0L);
        taskDO.setCreator(operator);
        taskDO.setModifier(operator);
        taskDO.setCreateTime(System.currentTimeMillis());
        taskDO.setUpdateTime(System.currentTimeMillis());
        if (cycleConfigBO != null && !cycleConfigBO.getCycleType().equals(STABLE.getCode())) {
            long endTime = getAfterDateMilli(subActivityConfig.getStartTime(), cycleConfigBO.getCycleDay());
            taskDO.setEndTime(endTime);
            // 如果周期任务存在偏移事件。则子任务持续时间为周期循环时间
            if (StringUtils.isNotBlank(subActivityConfig.getOffsetEventType())) {
                taskDO.setPeriodDay(cycleConfigBO.getCycleDay());
            }
        }
        // 子活动标签
        if (CollectionUtils.isNotEmpty(subActivityConfig.getTags())) {
            taskDO.setTags(toJSON(subActivityConfig.getTags()));
        }
        return taskDO;
    }

    /**
     * 排行榜子活动协议对象转换任务数据库对象
     */
    public static TaskDO convertRankTaskDO(RankSubActivityConfig rankSubActivityConfig, String name, String operator) {
        TaskDO taskDO = new TaskDO();
        taskDO.setName(name);
        taskDO.setStartTime(getStartOfDay(rankSubActivityConfig.getStartTime()));
        taskDO.setEndTime(getEndOfDay(rankSubActivityConfig.getEndTime()));
        taskDO.setPreTask(0L);
        taskDO.setParentTask(0L);
        taskDO.setType(TaskTypeEnum.SINGLE_PHASE.getCode());
        taskDO.setStatus(TaskConfigStatusEnum.ONLINE.getValue());
        taskDO.setPeriodType(TaskPeriodTypeEnum.ABSOLUTE.getCode());
        taskDO.setCreator(operator);
        taskDO.setModifier(operator);
        taskDO.setCreateTime(System.currentTimeMillis());
        taskDO.setUpdateTime(System.currentTimeMillis());
        return taskDO;
    }

    /**
     * 转换排行榜赛道任务数据库对象
     */
    public static TaskDO convertRankTraceTaskDO(String showName, RankTraceConfig rankTraceConfig,
            TaskTimeBO taskTime, String operator) {
        // 赛道顺序
        Integer traceOrder = rankTraceConfig.getTraceOrder();
        // 赛道子任务的名称
        String startTime = milliToStringMMdd(taskTime.getStartTime());
        String endTime = milliToStringMMdd(taskTime.getEndTime());
        String timeInfo = String.format("(%s-%s)", startTime, endTime);
        if (startTime.equals(endTime)) {
            timeInfo = String.format("(%s)", startTime);
        }
        String name = String.format("%s赛道%s子任务%s", showName, traceOrder, timeInfo);
        TaskDO taskDO = new TaskDO();
        taskDO.setName(name);
        taskDO.setStartTime(taskTime.getStartTime());
        taskDO.setEndTime(taskTime.getEndTime());
        taskDO.setPreTask(0L);
        taskDO.setParentTask(0L);
        // 不存在阶梯
        taskDO.setStage(1);
        taskDO.setPriority(taskTime.getPriority() + 1);
        taskDO.setType(TaskTypeEnum.SINGLE_PHASE.getCode());
        taskDO.setStatus(TaskConfigStatusEnum.ONLINE.getValue());
        taskDO.setPeriodType(TaskPeriodTypeEnum.ABSOLUTE.getCode());
        taskDO.setCreator(operator);
        taskDO.setModifier(operator);
        taskDO.setCompleteCondition(buildTraceTaskCompleteConfig(rankTraceConfig));
        taskDO.setAwardCondition(buildAwardCondition(0, null));
        taskDO.setCreateTime(System.currentTimeMillis());
        taskDO.setUpdateTime(System.currentTimeMillis());
        return taskDO;
    }

    /**
     * 构建排行指标配置
     */
    public static IndicatorManageBO buildRankIndicatorManage(String operator, int topN) {
        IndicatorConfigDO indicatorConfigDO = new IndicatorConfigDO();
        indicatorConfigDO.setIndicatorId(IndicatorEnum.RANK.getValue());
        indicatorConfigDO.setTargetValue(Long.MAX_VALUE - topN);
        indicatorConfigDO.setType(EntityTypeEnum.TASK.getCode());
        indicatorConfigDO.setValueType(ValueTypeEnum.FIXED.getCode());
        indicatorConfigDO.setExecuteCompleteType(IndicatorExecuteCompleteType.AFTER_EXPIRE.getValue());
        indicatorConfigDO.setStatus(IndicatorConfigStatusEnum.EFFECTIVE.getValue());
        indicatorConfigDO.setCreator(operator);
        indicatorConfigDO.setModifier(operator);
        indicatorConfigDO.setCreateTime(System.currentTimeMillis());
        indicatorConfigDO.setUpdateTime(System.currentTimeMillis());
        return IndicatorManageBO.builder()
                .indicatorConfig(indicatorConfigDO)
                .build();
    }

    /**
     * 转换任务基值指标对象
     */
    public static IndicatorConfigDO convertTaskBasicIndicatorConfig(IndicatorDO indicatorDO,
            List<CategoryBO> categoryList, Long itemGroupId, String topic, String operator) {
        if (null == indicatorDO) {
            throw new BizException(PARAM_INVALID, "基值指标不存在");
        }
        IndicatorCalcRangeConfig calcRangeConfig = IndicatorCalcRangeConfig.builder()
                .categoryList(categoryList)
                .itemGroupId(itemGroupId)
                .topic(topic)
                .tag(String.format(INDICATOR_TAG, indicatorDO.getId()))
                .build();

        IndicatorConfigExtBO indicatorConfigExtBO = new IndicatorConfigExtBO();
        // 获取指标当前的最大有效取数版本号
        Integer dataFetchVersion = IndicatorDataFetchConfigResolver.getIndicatorMaxEffectiveVersionByEnv(indicatorDO);
        if (dataFetchVersion != null) {
            indicatorConfigExtBO.setDataFetchVersion(dataFetchVersion);
        }
        return IndicatorConfigDO.builder()
                .type(ACTIVITY.getCode())
                .indicatorId(indicatorDO.getId())
                .calcRangeType(IndicatorCalcRangeTypeEnum.FIXED.getValue())
                .calcRangeConfig(toJSON(calcRangeConfig))
                .status(EFFECTIVE.getValue())
                .creator(operator)
                .modifier(operator)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .ext(toJSON(indicatorConfigExtBO))
                .build();
    }

    /**
     * 阶梯协议转换指标阶梯配置对象
     */
    public static IndicatorStepConfigBO convertIndicatorStepConfig(IndustryIndicatorBO stepIndicatorProtoCol,
            Integer step) {
        String targetType = stepIndicatorProtoCol.getTargetType();
        String targetValue = stepIndicatorProtoCol.getTargetValue();
        String unit = stepIndicatorProtoCol.getUnit();
        String storage = indicatorTargetValueShowToStorage(targetType, targetValue, unit);
        IndicatorStepConfigBO indicatorStepConfigBO = new IndicatorStepConfigBO();
        indicatorStepConfigBO.setStep(step);
        indicatorStepConfigBO.setType(targetType);
        indicatorStepConfigBO.setValue(storage);
        return indicatorStepConfigBO;
    }

    public static String indicatorTargetValueShowToStorage(String targetType, String value, String unit) {
        IndicatorTargetTypeEnum valueType = IndicatorTargetTypeEnum.getByCode(targetType);
        switch (valueType) {
            case INCR_FIXED:
                IncreaseIndicatorBO increaseFixedBO = fromJSON(value, IncreaseIndicatorBO.class);
                increaseFixedBO.setIncrFixed(convertToStorageValue(increaseFixedBO.getIncrFixed(), unit));
                increaseFixedBO.setMinTargetValue(convertToStorageValue(increaseFixedBO.getMinTargetValue(), unit));
                if (increaseFixedBO.getMaxTargetValue() != null) {
                    Long maxTargetValue = increaseFixedBO.getMaxTargetValue();
                    increaseFixedBO.setMaxTargetValue(convertToStorageValue(maxTargetValue, unit));
                }
                return toJSON(increaseFixedBO);
            case INCR_PERCENT:
            case NO_MORE_THAN:
            case ESTIMATION_CALC:
                IncreaseIndicatorBO increasePercentBO = fromJSON(value, IncreaseIndicatorBO.class);
                increasePercentBO.setMinTargetValue(convertToStorageValue(increasePercentBO.getMinTargetValue(), unit));
                if (increasePercentBO.getMaxTargetValue() != null) {
                    Long maxTargetValue = increasePercentBO.getMaxTargetValue();
                    increasePercentBO.setMaxTargetValue(convertToStorageValue(maxTargetValue, unit));
                }
                return toJSON(increasePercentBO);
            case SAME_FIXED:
                AssignIndicatorBO assignIndicatorBO = fromJSON(value, AssignIndicatorBO.class);
                assignIndicatorBO.setStableValue(convertToStorageValue(assignIndicatorBO.getStableValue(), unit));
                return toJSON(assignIndicatorBO);
            case DIFFERENT_FIXED:
                return value;
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "非预期目标计算类型");
        }
    }

    public static String indicatorTargetValueStorageToShow(String targetType, String value, String unit) {
        IndicatorTargetTypeEnum valueType = IndicatorTargetTypeEnum.getByCode(targetType);
        switch (valueType) {
            case INCR_FIXED:
                IncreaseIndicatorBO increaseFixedBO = fromJSON(value, IncreaseIndicatorBO.class);
                increaseFixedBO.setIncrFixed(convertToShowValue(increaseFixedBO.getIncrFixed(), unit, true));
                increaseFixedBO.setMinTargetValue(convertToShowValue(increaseFixedBO.getMinTargetValue(), unit, true));
                if (increaseFixedBO.getMaxTargetValue() != null) {
                    Long maxTargetValue = increaseFixedBO.getMaxTargetValue();
                    increaseFixedBO.setMaxTargetValue(convertToShowValue(maxTargetValue, unit, true));
                }
                return toJSON(increaseFixedBO);
            case INCR_PERCENT:
            case NO_MORE_THAN:
            case ESTIMATION_CALC:
                IncreaseIndicatorBO increasePercentBO = fromJSON(value, IncreaseIndicatorBO.class);
                increasePercentBO
                        .setMinTargetValue(convertToShowValue(increasePercentBO.getMinTargetValue(), unit, true));
                if (increasePercentBO.getMaxTargetValue() != null) {
                    Long maxTargetValue = increasePercentBO.getMaxTargetValue();
                    increasePercentBO.setMaxTargetValue(convertToShowValue(maxTargetValue, unit, true));
                }
                return toJSON(increasePercentBO);
            case SAME_FIXED:
                AssignIndicatorBO assignIndicatorBO = fromJSON(value, AssignIndicatorBO.class);
                assignIndicatorBO.setStableValue(convertToShowValue(assignIndicatorBO.getStableValue(), unit, true));
                return toJSON(assignIndicatorBO);
            case DIFFERENT_FIXED:
                TargetExcelFileBO targetExcelFileBO = fromJSON(value, TargetExcelFileBO.class);
                if (Objects.isNull(targetExcelFileBO)) {
                    return value;
                }
                //历史指标上传方式选中Excel的做兼容
                if (Objects.isNull(targetExcelFileBO.getCustomizeType())) {
                    targetExcelFileBO.setCustomizeType(CustomizeTypeEnum.EXCEL.getCode());
                }
                value = toJSON(targetExcelFileBO);
                return value;
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "非预期目标计算类型");
        }
    }

    /**
     * 阶梯协议转换奖励阶梯配置对象
     */
    public static AwardStepConfigBO convertAwardStepConfig(IndustryAwardBO stepAwardProtoCol, Integer step,
           TaskAwardSendTimeTypeEnum awardSendTimeType, AdEcomBenefitPackageInfo benefitPackageInfo) {
        String targetType = stepAwardProtoCol.getTargetType();
        String targetValue = stepAwardProtoCol.getTargetValue();
        Integer awardType = stepAwardProtoCol.getAwardType();
        // 转换为存储奖励值
        String storageTargetValue =
                awardTargetValueShowToStorage(targetType, targetValue, awardType, awardSendTimeType);
        AwardStepConfigBO awardStepConfigBO = new AwardStepConfigBO();
        awardStepConfigBO.setStep(step);
        awardStepConfigBO.setType(targetType);
        awardStepConfigBO.setValue(storageTargetValue);
        if (null != benefitPackageInfo) {
            MagnetInterestPackageInfo magnetInterestPackageInfo =
                    convertMagnetInterestPackageInfo(stepAwardProtoCol, benefitPackageInfo);
            awardStepConfigBO.setMagnetInterestPackageInfo(magnetInterestPackageInfo);
        }
        return awardStepConfigBO;
    }

    public static MagnetInterestPackageInfo convertMagnetInterestPackageInfo(IndustryAwardBO industryAward,
            AdEcomBenefitPackageInfo benefitPackageInfo) {
        MagnetInterestPackageInfo magnetInterestPackageInfo = new MagnetInterestPackageInfo();
        magnetInterestPackageInfo.setPackageId(Long.valueOf(industryAward.getMagnetInterestPackageId()));
        magnetInterestPackageInfo.setValue(changeLi2Fen(benefitPackageInfo.getTotalValue()));
        magnetInterestPackageInfo.setBenefitInfoList(convertMagnetInterestBenefitInfo(benefitPackageInfo.getBenefitPackageContentList()));
        return magnetInterestPackageInfo;
    }

    private static List<MagnetInterestBenefitInfo> convertMagnetInterestBenefitInfo(List<AdEcomBenefitInfo> benefitInfoList) {
        if (CollectionUtils.isEmpty(benefitInfoList)) {
            return Lists.newArrayList();
        }
        return benefitInfoList.stream().map(benefitInfo -> {
            MagnetInterestBenefitInfo magnetInterestBenefitInfo = new MagnetInterestBenefitInfo();
            magnetInterestBenefitInfo.setName(benefitInfo.getBenefitName());
            magnetInterestBenefitInfo.setNumber(benefitInfo.getNumber());
            magnetInterestBenefitInfo.setValue(changeLi2Fen(benefitInfo.getBenefitValue()));
            return magnetInterestBenefitInfo;
        }).collect(Collectors.toList());
    }

    private static String awardTargetValueShowToStorage(String targetType, String value, int awardType,
            TaskAwardSendTimeTypeEnum awardSendTimeType) {
        AwardTargetTypeEnum valueType = AwardTargetTypeEnum.getByCode(targetType);
        AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
        switch (valueType) {
            case SAME_FIXED:
                FixedAwardBO fixedAwardBO = fromJSON(value, FixedAwardBO.class);
                long awardCount = changeYuanUnitAwardValueToFenWithCheck(awardTypeEnum,
                        String.valueOf(fixedAwardBO.getAwardCount()), false);
                fixedAwardBO.setAwardCount(awardCount);
                return toJSON(fixedAwardBO);
            case DIFFERENT_FIXED:
            case CUSTOMIZE_RULE_CALC:
                return value;
                // 根据瓜分规则计算
            case AWARD_POOL_DISTRIBUTE_RULE_CALC:
                CustomizeRuleCalcConfig customizeRuleCalcConfig = fromJSON(value, CustomizeRuleCalcConfig.class);
                if (customizeRuleCalcConfig.getMaxAwardValue() != null) {
                    long maxAwardValue = changeYuanUnitAwardValueToFenWithCheck(awardTypeEnum,
                            String.valueOf(customizeRuleCalcConfig.getMaxAwardValue()), false);
                    customizeRuleCalcConfig.setMaxAwardValue(maxAwardValue);
                }
                return toJSON(customizeRuleCalcConfig);
            case INCR_SPECIFY_INDICATOR_RETURN:
            case SPECIFY_INDICATOR_CALC:
            case ESTIMATION_STRATEGY_CALC:
                ReturnPercentConfig returnPercentConfig = fromJSON(value, ReturnPercentConfig.class);
                long maxAwardValue = changeYuanUnitAwardValueToFenWithCheck(awardTypeEnum,
                        String.valueOf(returnPercentConfig.getMaxAwardValue()), false);
                returnPercentConfig.setMaxAwardValue(maxAwardValue);
                if (returnPercentConfig.getMinAwardValue() != null) {
                    long minAwardValue = changeYuanUnitAwardValueToFenWithCheck(awardTypeEnum,
                            String.valueOf(returnPercentConfig.getMinAwardValue()), false);
                    returnPercentConfig.setMinAwardValue(minAwardValue);
                }
                // 若前返和后返指标不一样
                if (Boolean.FALSE.equals(returnPercentConfig.getSameReturnIndicator())) {
                    returnPercentConfig.setSpecifyIndicatorInfos(returnPercentConfig.getSpecifyIndicatorInfos().stream()
                            .filter(specifyIndicatorInfoBO -> specifyIndicatorInfoBO.getTaskAwardSendTimeType()
                                    .equals(awardSendTimeType)).collect(Collectors.toList()));
                }
                return toJSON(returnPercentConfig);
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "非预期奖励后台配置类型");
        }
    }

    public static String awardTargetValueStorageToShow(String targetType, String afterSendValue, String preSendValue,
            int awardType) {
        AwardTargetTypeEnum valueType = AwardTargetTypeEnum.getByCode(targetType);
        AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
        switch (valueType) {
            case SAME_FIXED:
                FixedAwardBO fixedAwardBO = fromJSON(afterSendValue, FixedAwardBO.class);
                String awardCount = changeFenUnitAwardValueToYuan(awardTypeEnum, fixedAwardBO.getAwardCount(), true);
                fixedAwardBO.setAwardCount(Long.valueOf(awardCount));
                return toJSON(fixedAwardBO);
            case DIFFERENT_FIXED:
                AwardExcelFileBO awardExcelFileBO = fromJSON(afterSendValue, AwardExcelFileBO.class);
                if (Objects.isNull(awardExcelFileBO)) {
                    return afterSendValue;
                }
                if (Objects.isNull(awardExcelFileBO.getCustomizeType())) {
                    awardExcelFileBO.setCustomizeType(CustomizeTypeEnum.EXCEL.getCode());
                }
                afterSendValue = toJSON(awardExcelFileBO);
                return afterSendValue;
            case INCR_SPECIFY_INDICATOR_RETURN:
            case SPECIFY_INDICATOR_CALC:
            case ESTIMATION_STRATEGY_CALC:
                ReturnPercentConfig afterSendReturnPercentConfig =
                        convertToReturnPercentConfig(awardTypeEnum, afterSendValue);
                if (Boolean.FALSE.equals(afterSendReturnPercentConfig.getSameReturnIndicator())) {
                    ReturnPercentConfig preSendReturnPercentConfig =
                            convertToReturnPercentConfig(awardTypeEnum, preSendValue);
                    /**
                     * @since 2024.10.8 目前前返的返点指标仅根据一个指标返点
                     * 保障前返指标在前，后返在后
                     */
                    afterSendReturnPercentConfig.getSpecifyIndicatorInfos()
                            .add(0, preSendReturnPercentConfig.getSpecifyIndicatorInfos().get(0));
                }
                return toJSON(afterSendReturnPercentConfig);
            case CUSTOMIZE_RULE_CALC:
                return afterSendValue;
            case AWARD_POOL_DISTRIBUTE_RULE_CALC:
                CustomizeRuleCalcConfig ruleCalcConfig = fromJSON(afterSendValue, CustomizeRuleCalcConfig.class);
                if (ruleCalcConfig.getMaxAwardValue() != null) {
                    String returnMaxAwardValue = changeFenUnitAwardValueToYuan(awardTypeEnum,
                            ruleCalcConfig.getMaxAwardValue(), true);
                    ruleCalcConfig.setMaxAwardValue(Long.parseLong(returnMaxAwardValue));
                }
                return toJSON(ruleCalcConfig);
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "非预期奖励后台配置类型");
        }
    }

    /**
     * 阶梯指标协议转换指标配置记录
     */
    public static IndicatorConfigDO convertIndicatorConfigDO(IndicatorDO indicatorDO,
            List<IndicatorStepConfigBO> allStepConfigs, IndicatorStepConfigBO rankStepConfig,
            IndustryIndicatorBO stepIndicatorBO, Integer completeType, String operator) {
        // 目标配置
        IndicatorTargetConfigBO targetConfigBO = new IndicatorTargetConfigBO();
        targetConfigBO.setStepConfigs(allStepConfigs);
        targetConfigBO.setIndicatorRankingConfig(rankStepConfig);
        targetConfigBO.setMinimumTargetRate(stepIndicatorBO.getMinimumTargetRate());
        // 规则配置
        AdminAllSceneConfig adminAllSceneConfig = strategyAdminAllSceneConfig.getObject();
        RuleConfig ruleConfig = adminAllSceneConfig.getRuleConfig();
        // 动态配置
        DynamicValueConfig dynamicValueConfig = new DynamicValueConfig();
        dynamicValueConfig.setType(RULE.getCode());
        dynamicValueConfig.setTargetConfig(toJSON(targetConfigBO));
        dynamicValueConfig.setContent(ruleConfig.getTargetRuleCode());
        // 类目&商品筛选包
        IndicatorCalcRangeConfig calcRangeConfig = new IndicatorCalcRangeConfig();
        calcRangeConfig.setCategoryList(stepIndicatorBO.getCategoryList());
        calcRangeConfig.setItemGroupId(stepIndicatorBO.getItemGroupId());
        calcRangeConfig.setTopic(stepIndicatorBO.getTopic());
        // 课程ID
        List<IndicatorConditionBO> indicatorConditionBOList = Lists.newArrayList();
        String indicatorCondition = null;
        if (CollectionUtils.isNotEmpty(stepIndicatorBO.getCourseList())) {
            IndicatorConditionBO indicatorConditionBO = IndicatorConditionBO.builder()
                    .name(COURSE_LIST.getCode()).value(stepIndicatorBO.getCourseList()).build();
            indicatorConditionBOList.add(indicatorConditionBO);
        }
        if (CollectionUtils.isNotEmpty(stepIndicatorBO.getWechatList())) {
            IndicatorConditionBO indicatorConditionBO = IndicatorConditionBO.builder()
                    .name(WECHAT_LIST.getCode()).value(stepIndicatorBO.getWechatList()).build();
            indicatorConditionBOList.add(indicatorConditionBO);
        }
        if (StringUtils.isNotBlank(stepIndicatorBO.getInsuranceActivityId())) {
            IndicatorConditionBO indicatorConditionBO = IndicatorConditionBO.builder()
                    .name(INSURANCE_ACTIVITY_ID.getCode())
                    .value(stepIndicatorBO.getInsuranceActivityId()).build();
            indicatorConditionBOList.add(indicatorConditionBO);
        }
        if (CollectionUtils.isNotEmpty(indicatorConditionBOList)) {
            indicatorCondition = toJSON(indicatorConditionBOList);
        }

        // 指标结算方式
        if (completeType == null) {
            completeType = indicatorDO.getCloseType();
        }
        // 指标标签
        IndicatorConfigExtBO indicatorConfigExtBO = new IndicatorConfigExtBO();
        indicatorConfigExtBO.setTags(stepIndicatorBO.getTags());
        // 获取指标当前的最大有效取数版本号
        Integer dataFetchVersion = IndicatorDataFetchConfigResolver.getIndicatorMaxEffectiveVersionByEnv(indicatorDO);
        if (dataFetchVersion != null) {
            indicatorConfigExtBO.setDataFetchVersion(dataFetchVersion);
        }
        IndicatorConfigDO indicatorConfigDO = new IndicatorConfigDO();
        indicatorConfigDO.setIndicatorId(indicatorDO.getId());
        indicatorConfigDO.setType(TASK.getCode());
        indicatorConfigDO.setCalcRangeType(indicatorDO.getCalcType());
        indicatorConfigDO.setExecuteCompleteType(completeType);
        indicatorConfigDO.setValueType(RULE.getCode());
        indicatorConfigDO.setStatus(EFFECTIVE.getValue());
        indicatorConfigDO.setIndicatorCondition(indicatorCondition);
        indicatorConfigDO.setCreator(operator);
        indicatorConfigDO.setModifier(operator);
        indicatorConfigDO.setCreateTime(System.currentTimeMillis());
        indicatorConfigDO.setUpdateTime(System.currentTimeMillis());
        indicatorConfigDO.setDynamicTargetValue(toJSON(dynamicValueConfig));
        indicatorConfigDO.setCalcRangeConfig(toJSON(calcRangeConfig));
        indicatorConfigDO.setExt(toJSON(indicatorConfigExtBO));
        return indicatorConfigDO;
    }

    /**
     * 阶梯奖励协议转换奖励组合对象
     */
    @SuppressWarnings("checkstyle:ParameterNumber")
    public static AwardManageBO convertAwardManageBO(AwardSendRuleConfigBO awardSendRuleConfig,
            List<IndicatorManageBO> returnIndicators, List<String> awardInputList, String operator,
            String delayCalcRule, IndustryAwardBO stageAward, Integer awardRiskType,
            TaskAwardSendTimeTypeEnum awardSendTimeType, boolean additionalSend) {
        // 奖励三方数据
        if (CollectionUtils.isNotEmpty(returnIndicators)) {
            awardInputList.add(SPECIFY_INDICATOR.getCode());
            awardInputList.add(TR_INDICATOR.getCode());
            awardInputList.add(RETURN_FORMULA_CALC.getCode());
        }

        // 奖励发送类型
        int awardSendType = AwardSendTypeEnum.NEED_AUDIT_OPERATION_SEND.getValue();

        List<AuditConfigDO> auditConfig = Lists.newArrayList();
        // 不为服务市场和勋章，根据风控类型判断是否更改审批配置和发放方式
        List<Integer> serviceAndMedal = Lists.newArrayList(AwardTypeEnum.SERVICE_MARKET_COUPON.getCode(),
                AwardTypeEnum.NEW_MEDAL.getCode());
        if (!serviceAndMedal.contains(stageAward.getAwardType())) {
            if (awardRiskType == null || !awardRiskType.equals(AdminAwardRiskTypeEnum.NOT_NEED_RISK.getCode())) {
                auditConfig = singletonList(buildScheduleAwardAuditConfig(operator, returnIndicators));
            } else {
                awardSendType = AwardSendTypeEnum.DIRECT_OPERATION_SEND.getValue();
            }
        }
        // 前返奖励发奖模式
        if (awardSendTimeType == TaskAwardSendTimeTypeEnum.PRE_SEND) {
            awardSendType = AwardSendTypeEnum.DIRECT_OPERATION_SEND.getValue();
        }
        // 自定义奖励计算规则
        supplyCustomizeRuleCalc(awardInputList, stageAward);

        // 奖励配置组装
        AwardConfigDO config = convertAwardConfigDO(awardSendRuleConfig, stageAward, awardInputList, operator,
                delayCalcRule, awardSendType, additionalSend, awardSendTimeType);
        // 相关数据组装
        AwardManageBO awardManageBO = new AwardManageBO();
        awardManageBO.setAwardConfig(config);
        awardManageBO.setIndicatorConfigList(returnIndicators);
        awardManageBO.setAuditConfig(auditConfig);
        return awardManageBO;
    }

    private static void supplyCustomizeRuleCalc(List<String> awardInputList, IndustryAwardBO stageAward) {
        AwardTargetTypeEnum awardTargetType = AwardTargetTypeEnum.getByCode(stageAward.getTargetType());
        if (!(AwardTargetTypeEnum.CUSTOMIZE_RULE_CALC.equals(awardTargetType)
                || AwardTargetTypeEnum.AWARD_POOL_DISTRIBUTE_RULE_CALC.equals(awardTargetType))) {
            return;
        }
        CustomizeRuleCalcConfig ruleCalcConfig =
                fromJSON(stageAward.getTargetValue(), CustomizeRuleCalcConfig.class);
        checkArgument(ruleCalcConfig != null && StringUtils.isNotBlank(ruleCalcConfig.getCustomizeRuleCalcCode()),
                "自定义规则奖励计算配置异常");

        List<AwardCustomizeRuleCalcConfigBO> customizeRuleCalcConfigs = awardCustomizeRuleCalcConfigList.getList();
        AwardCustomizeRuleCalcConfigBO awardCustomizeRuleCalcConfig = customizeRuleCalcConfigs.stream()
                .filter(config -> config.getAccessible()
                        && StringUtils.equals(config.getCode(), ruleCalcConfig.getCustomizeRuleCalcCode())
                        && CollectionUtils.isNotEmpty(config.getAwardRuleInputList()))
                .findFirst()
                .orElse(null);

        if (awardCustomizeRuleCalcConfig != null
                && CollectionUtils.isNotEmpty(awardCustomizeRuleCalcConfig.getAwardRuleInputList())) {
            awardInputList.clear();
            awardInputList.addAll(awardCustomizeRuleCalcConfig.getAwardRuleInputList());
        }
    }

    /**
     * 阶梯奖励协议转换奖励配置记录
     */
    @SuppressWarnings("checkstyle:ParameterNumber")
    public static AwardConfigDO convertAwardConfigDO(AwardSendRuleConfigBO awardSendRuleConfig,
            IndustryAwardBO stageAward, List<String> awardInputList, String operator, String delayCalcRule,
            int awardSendType, boolean additionalSend, TaskAwardSendTimeTypeEnum sendTimeType) {
        // 规则配置
        AdminAllSceneConfig adminAllSceneConfig = strategyAdminAllSceneConfig.getObject();
        RuleConfig ruleConfig = adminAllSceneConfig.getRuleConfig();
        String awardRuleCode = ruleConfig.getAwardRuleCode();
        // 支持自定义奖励计算规则
        IndustryAwardValueTypeEnum valueType = IndustryAwardValueTypeEnum.getByType(stageAward.getTargetType());
        if (CUSTOMIZE_RULE_CALC.equals(valueType) || AWARD_POOL_DISTRIBUTE_RULE_CALC.equals(valueType)) {
            CustomizeRuleCalcConfig ruleCalcConfig =
                    fromJSON(stageAward.getTargetValue(), CustomizeRuleCalcConfig.class);
            awardRuleCode = ruleCalcConfig.getCustomizeRuleCalcCode();
        }
        // 奖励发送配置
        ActivityAwardSendRule awardSendRule = new ActivityAwardSendRule();
        awardSendRule.setAwardSendType(awardSendType);
        awardSendRule.setAwardCalType(AwardCalTypeEnum.DYNAMIC.getCode());
        awardSendRule.setAwardSendRuleCode(awardRuleCode);
        awardSendRule.setAwardSendRuleConfig(toJSON(awardSendRuleConfig));
        awardSendRule.setAwardRuleInput(awardInputList);
        // 是否需要奖励扣减
        awardSendRule.setIsAwardDeduct(
                stageAward.isAwardDeduct() ? AwardDeductEnum.DEDUCT.getCode() : AwardDeductEnum.NO_DEDUCT.getCode());
        // 前返后补类型的后返，需要默认扣减
        if (sendTimeType.equals(TaskAwardSendTimeTypeEnum.AFTER_SEND) && additionalSend) {
            awardSendRule.setIsAwardDeduct(AwardDeductEnum.DEDUCT.getCode());
        }
        awardSendRule.setAdditionalSend(additionalSend);
        // 奖励配置记录
        AwardConfigDO awardConfigDO = new AwardConfigDO();
        awardConfigDO.setAwardName(stageAward.getAwardName());
        awardConfigDO.setExpireTime(stageAward.getExpireTime());
        awardConfigDO.setAwardType(stageAward.getAwardType());
        awardConfigDO.setStatus(AwardConfigStatusEnum.EFFECTIVE.getCode());
        awardConfigDO.setSendRuleType(AwardSendRuleTypeEnum.TASK.getCode());
        awardConfigDO.setSendRule(toJSON(awardSendRule));
        awardConfigDO.setInterestConfigId(0L);
        awardConfigDO.setCreator(operator);
        awardConfigDO.setModifier(operator);
        awardConfigDO.setDelayCalcRule(delayCalcRule);
        awardConfigDO.setCreateTime(System.currentTimeMillis());
        awardConfigDO.setUpdateTime(System.currentTimeMillis());
        // 奖励发放时机
        awardConfigDO.setSendTimeType(sendTimeType.getCode());
        // 定制属性填充
        awardConfigDOCustomAssemble(awardSendRuleConfig, stageAward.getFundBudgetId(), stageAward.getMedalId(),
                awardSendRule, awardConfigDO);

        return awardConfigDO;
    }

    /**
     * awardConfigDO 定制属性填充<br/>
     * v1. 服务市场奖励直接发放奖励;服务市场券数量固定1
     * v2. 无过期时间的和权益中心约定过期时间置0
     * v3. 大促勋章奖励直接发放奖励;数量固定1
     */
    private static void awardConfigDOCustomAssemble(AwardSendRuleConfigBO awardSendRuleConfig, String fundBudgetId,
            Long medalId, ActivityAwardSendRule awardSendRule, AwardConfigDO awardConfigDO) {
        Integer awardType = awardConfigDO.getAwardType();
        AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
        // 服务市场不走横向发奖
        if (Objects.equals(awardTypeEnum, AwardTypeEnum.SERVICE_MARKET_COUPON)) {
            AwardInterestExtParamBO interestExtParamBO = AwardInterestExtParamBO.builder()
                    .interestScene(AwardSendRuleTypeEnum.TASK.getCode())
                    .fundBudgetId(fundBudgetId).build();
            awardConfigDO.setInterestExtParam(toJSON(interestExtParamBO));
            awardConfigDO.setSendRule(buildFixAwardSendRule(awardType, awardSendRuleConfig, awardSendRule));
        }

        if (Objects.equals(awardTypeEnum, AwardTypeEnum.NEW_MEDAL)) {
            AwardInterestExtParamBO interestExtParamBO = AwardInterestExtParamBO.builder()
                    .interestScene(AwardSendRuleTypeEnum.MEDAL.getCode())
                    .fundBudgetId(fundBudgetId).build();
            awardConfigDO.setInterestExtParam(toJSON(interestExtParamBO));
            awardConfigDO.setSendRule(buildFixAwardSendRule(awardType, awardSendRuleConfig, awardSendRule));
            awardConfigDO.setInterestConfigId(medalId);
        }

        if (Objects.equals(awardTypeEnum, AwardTypeEnum.MAGNET_INTEREST_PACKAGE)) {
            awardConfigDO.setSendRule(buildFixAwardSendRule(awardType, awardSendRuleConfig, awardSendRule));
        }

        // 无过期时间的和权益中心约定过期时间置0
        if (!awardTypeEnum.getExpireTimeRequired()) {
            awardConfigDO.setExpireTime(0L);
        }
    }

    private static String buildFixAwardSendRule(Integer awardType, AwardSendRuleConfigBO awardSendRuleConfig,
            ActivityAwardSendRule awardSendRule) {
        if (AwardTypeEnum.MAGNET_INTEREST_PACKAGE.getCode().equals(awardType)) {
            awardSendRule.setAwardSendType(AwardSendTypeEnum.NEED_AUDIT.getValue());
        } else {
            awardSendRule.setAwardSendType(AwardSendTypeEnum.DIRECT.getValue());
        }
        awardSendRule.setAwardCount(1);
        List<AwardStepConfigBO> stepConfigs = awardSendRuleConfig.getStepConfigs();
        // 服务市场券数量默认1
        for (AwardStepConfigBO stepConfig : stepConfigs) {
            FixedAwardBO fixedAwardBO = fromJson(stepConfig.getValue(), FixedAwardBO.class);
            fixedAwardBO.setAwardCount(1L);
            stepConfig.setValue(toJSON(fixedAwardBO));
        }
        awardSendRuleConfig.setStepConfigs(stepConfigs);
        awardSendRule.setAwardSendRuleConfig(toJSON(awardSendRuleConfig));
        return toJSON(awardSendRule);
    }

    /**
     * 将阶梯奖励对象转为纵向排行榜赛道奖励配置
     */
    public static AwardRankingConfigBO convertAwardRankingConfig(IndustryAwardBO rankTraceAward) {
        Integer awardType = rankTraceAward.getAwardType();
        String targetType = rankTraceAward.getTargetType();
        String targetValue = rankTraceAward.getTargetValue();
        // 转换为存储奖励值
        String storageTargetValue =
                awardTargetValueShowToStorage(targetType, targetValue, awardType, TaskAwardSendTimeTypeEnum.AFTER_SEND);
        AwardRankingConfigBO awardRankingConfigBO = new AwardRankingConfigBO();
        awardRankingConfigBO.setType(targetType);
        awardRankingConfigBO.setValue(storageTargetValue);
        awardRankingConfigBO.setRankBegin(rankTraceAward.getRankBegin());
        awardRankingConfigBO.setRankEnd(rankTraceAward.getRankEnd());
        AwardDistributeRuleConfig awardDistributeRuleConfig = rankTraceAward.getAwardDistributeRuleConfig();
        if (awardDistributeRuleConfig != null && StringUtils.isNotBlank(awardDistributeRuleConfig.getAwardPoolFixValue())) {
            awardDistributeRuleConfig = AwardDistributeRuleConfig.builder()
                    .distributeType(awardDistributeRuleConfig.getDistributeType())
                    // 奖励池元转分
                    .awardPoolFixValue(AmountUtils.changeY2F(awardDistributeRuleConfig.getAwardPoolFixValue()))
                    .awardPoolType(awardDistributeRuleConfig.getAwardPoolType())
                    .distributeIndicatorId(awardDistributeRuleConfig.getDistributeIndicatorId())
                    .build();
        }
        awardRankingConfigBO.setAwardDistributeRuleConfig(awardDistributeRuleConfig);
        return awardRankingConfigBO;
    }

    /**
     * 将阶梯奖励对象转为奖励过期时间配置
     */
    public static ExpireTimeConfigBO convertExpireTimeConfig(IndustryAwardBO layerAward, TaskDO childTask,
            TaskAwardSendTimeTypeEnum awardSendTimeType) {
        ExpireTimeConfigBO expireTimeConfig = new ExpireTimeConfigBO();
        switch (awardSendTimeType) {
            case PRE_SEND:
                // 前返失效时间默认子任务结束时间
                expireTimeConfig.setExpireTimeType(ExpireTimeTypeEnum.ABSOLUTE.getCode());
                expireTimeConfig.setExpireTime(childTask.getEndTime());
                break;
            case AFTER_SEND:
                expireTimeConfig.setExpireTimeType(layerAward.getExpireTimeType());
                expireTimeConfig.setExpireTime(layerAward.getExpireTime());
                expireTimeConfig.setExpireDays(layerAward.getExpireDays());
                break;
            default:
                break;
        }
        return expireTimeConfig;
    }

    /**
     * 将阶梯奖励对象转为奖励过期时间配置
     */
    public static SendConfigBO convertSendConfig(IndustryAwardBO layerAward,
            TaskAwardSendTimeTypeEnum awardSendTimeType) {
        SendConfigBO sendConfigBO = new SendConfigBO();
        switch (awardSendTimeType) {
            case AFTER_SEND:
                sendConfigBO.setAwardSendType(layerAward.getAwardSendType());
                sendConfigBO.setDelaySendTimeType(layerAward.getAwardDelayTimeType());
                sendConfigBO.setDelaySendTime(layerAward.getAwardDelayTime());
                break;
            case PRE_SEND:
                sendConfigBO.setAwardSendType(AdminAwardSendTypeEnum.DIRECT.getCode());
                sendConfigBO.setDelaySendTimeType(
                        AdminAwardDelayTimeTypeEnum.DELAY_RELATIVE_USER_TASK_START_TIME.getCode());
                sendConfigBO.setRelativeSendTime(preAwardRelativeSendTime.get());
                break;
            default:
                throw new BizException(SERVER_ERROR, "非法的奖励发放时间类型");
        }
        return sendConfigBO;
    }

    /**
     * 构建默认奖励审批配置
     */
    public static AuditConfigDO buildDefaultAwardAuditConfig(String operator) {
        return AuditConfigDO.builder()
                .auditType(AuditTypeEnum.RISK_AWARD_OFFLINE.getCode())
                .auditBeginType(AuditBeginTypeEnum.AFTER_AUDIT.getCode())
                .entityType(AWARD.getCode())
                .expireAction(AuditExpireActionEnum.KIM_WARNING.getCode())
                .auditPolicyType(AuditPolicyTypeEnum.RISK_REALTIME.getCode())
                .auditWaitTime(Duration.ofDays(7).toMillis())
                .status(AuditConfigStatusEnum.VALID.getCode())
                .creator(operator)
                .modifier(operator)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 构建定时送审奖励审批配置
     */
    public static AuditConfigDO buildScheduleAwardAuditConfig(String operator,
            List<IndicatorManageBO> returnIndicators) {
        AuditConfigExtBO auditConfigExtBO = buildAuditConfigExt(returnIndicators);
        return AuditConfigDO.builder()
                .auditType(AuditTypeEnum.RISK_AWARD_OFFLINE_SCHEDULE.getCode())
                .auditBeginType(AuditBeginTypeEnum.AFTER_AUDIT.getCode())
                .entityType(AWARD.getCode())
                .expireAction(AuditExpireActionEnum.KIM_WARNING.getCode())
                .auditPolicyType(AuditPolicyTypeEnum.RISK_REALTIME.getCode())
                .auditWaitTime(Duration.ofDays(7).toMillis())
                .status(AuditConfigStatusEnum.VALID.getCode())
                .ext(toJSON(auditConfigExtBO))
                .creator(operator)
                .modifier(operator)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .build();
    }

    private static AuditConfigExtBO buildAuditConfigExt(List<IndicatorManageBO> returnIndicators) {
        // 返点指标是否需要包含明细
        boolean specifyIndicatorAuditWithDetail = returnIndicators.stream()
                .map(indicatorManage -> indicatorManage.getIndicatorConfig().getIndicatorId())
                .anyMatch(IndicatorResolver::resolveAuditWithDetail);

        AuditConfigExtBO auditConfigExtBO = null;
        if (specifyIndicatorAuditWithDetail) {
            auditConfigExtBO = AuditConfigExtBO.builder().auditSendConfig(AuditSendConfig.builder()
                            .auditSendInputList(Lists.newArrayList(SPECIFY_INDICATOR_AUDIT_WITH_DETAIL.getCode())).build())
                    .build();
        }

        return auditConfigExtBO;
    }

    /**
     * 构建子任务完成配置
     */
    public static String buildChildTaskCompleteConfig(SubActivityStepConfig stepConfig) {
        // 完成配置
        Integer completeCondition = stepConfig.getCompleteCondition();
        Integer completeCount = stepConfig.getCompleteCount();
        //Long requireCompleteIndicatorId = convertRequireCompleteIndicatorId(stepConfig);
        return buildIndicatorCompleteCondition(StringUtils.EMPTY, completeCondition,
                completeCount, null);
    }

    /**
     * 构建赛道任务完成配置
     */
    public static String buildTraceTaskCompleteConfig(RankTraceConfig traceConfig) {
        // 完成配置
        Integer completeCondition = traceConfig.getCompleteCondition();
        Integer completeCount = traceConfig.getCompleteCount();
        return buildIndicatorCompleteCondition(LEADERBOARD.getType(), completeCondition,
                completeCount, null);
    }

    /**
     * 分层任务协议解析必须完成指标
     */
    public static Long convertRequireCompleteIndicatorId(SubActivityStepConfig stepConfig) {
        // 定位带tag的指标
        List<IndustryIndicatorBO> tagIndicator = stepConfig.getIndicatorConfigs().stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getTags()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tagIndicator)) {
            return null;
        }
        return tagIndicator.get(0).getIndicatorId();
    }

    /**
     * 构建周期奖励名称
     */
    public static String buildAwardName(Long startTimeMilli, Long endTimeMilli, String showName) {
        String startTime = milliToStringMMdd(startTimeMilli);
        String endTime = milliToStringMMdd(endTimeMilli);
        if (startTime.equals(endTime)) {
            return String.format("%s%s奖励", showName, StringUtils.EMPTY);
        }
        String awardTime = String.format("（%s-%s）", startTime, endTime);
        return String.format("%s%s奖励", showName, awardTime);
    }

    /**
     * 构建展示渠道对应的入口
     */
    public static List<String> transformShowConfigChannels(List<String> showChannels) {
        Map<String, ShowChannelMapEntranceConfig> configMap = showChannelMapEntrance.getMap();
        Set<String> showConfigChannels = Sets.newHashSet();
        showChannels.forEach(showChannel -> {
            showConfigChannels.add(showChannel);
            if (configMap.containsKey(showChannel)) {
                ShowChannelMapEntranceConfig entranceConfig = configMap.get(showChannel);
                if (CollectionUtils.isNotEmpty(entranceConfig.getEntranceChannels())) {
                    showConfigChannels.addAll(entranceConfig.getEntranceChannels());
                }
            }
        });
        return new ArrayList<>(showConfigChannels);
    }

    public static List<String> transformShowChannels(List<String> showConfigChannels) {
        Map<String, ShowChannelMapEntranceConfig> configMap = showChannelMapEntrance.getMap();
        Set<String> showChannels = Sets.newHashSet();
        showChannels.addAll(showConfigChannels);
        showConfigChannels.forEach(showChannel -> {
            if (configMap.containsKey(showChannel)) {
                ShowChannelMapEntranceConfig entranceConfig = configMap.get(showChannel);
                if (CollectionUtils.isNotEmpty(entranceConfig.getEntranceChannels())) {
                    showChannels.removeAll(entranceConfig.getEntranceChannels());
                }
            }
        });
        return new ArrayList<>(showChannels);
    }

    public static IndicatorConfigDO buildSortIndicatorConfig(IndicatorDO indicator) {
        // 类目&商品筛选包 todo 后续前端协议扩充
        IndicatorCalcRangeConfig calcRangeConfig = new IndicatorCalcRangeConfig();
        // 获取指标当前的最大有效取数版本号
        IndicatorConfigExtBO indicatorConfigExtBO = new IndicatorConfigExtBO();
        Integer dataFetchVersion = IndicatorDataFetchConfigResolver.getIndicatorMaxEffectiveVersionByEnv(indicator);
        if (dataFetchVersion != null) {
            indicatorConfigExtBO.setDataFetchVersion(dataFetchVersion);
        }

        IndicatorConfigDO indicatorConfigDO = new IndicatorConfigDO();
        indicatorConfigDO.setIndicatorId(indicator.getId());
        indicatorConfigDO.setType(PERIOD.getCode());
        indicatorConfigDO.setCalcRangeType(indicator.getCalcType());
        indicatorConfigDO.setStatus(EFFECTIVE.getValue());
        indicatorConfigDO.setCalcRangeConfig(toJSON(calcRangeConfig));
        indicatorConfigDO.setExt(toJSON(indicatorConfigExtBO));
        return indicatorConfigDO;
    }

    /**
     * 转化指标返点模型
     */
    public static ReturnPercentConfig convertToReturnPercentConfig(AwardTypeEnum awardTypeEnum, String value) {
        ReturnPercentConfig returnPercentConfig = fromJSON(value, ReturnPercentConfig.class);
        String returnMaxAwardValue = changeFenUnitAwardValueToYuan(awardTypeEnum,
                returnPercentConfig.getMaxAwardValue(), true);
        returnPercentConfig.setMaxAwardValue(Long.parseLong(returnMaxAwardValue));
        if (returnPercentConfig.getMinAwardValue() != null) {
            String returnMinAwardValue = changeFenUnitAwardValueToYuan(awardTypeEnum,
                    returnPercentConfig.getMinAwardValue(), true);
            returnPercentConfig.setMinAwardValue(Long.parseLong(returnMinAwardValue));
        }
        return returnPercentConfig;
    }

    /**
     * 获取子活动天数
     */
    public static Integer getSubActivityDays(SubActivityConfig subActivityConfig) {
        TaskPeriodTypeEnum periodTypeEnum = TaskPeriodTypeEnum.of(subActivityConfig.getPeriodType());
        Integer subActivityDay;
        if (RELATIVE_TYPE_ENUMS.contains(periodTypeEnum)) {
            subActivityDay = subActivityConfig.getPeriodDay();
        } else {
            subActivityDay = Integer.parseInt(String.valueOf(
                    calculateDayBetween(subActivityConfig.getStartTime(), subActivityConfig.getEndTime())));
        }
        return subActivityDay;
    }
}
