package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoActivityStatusEnum.END;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoActivityStatusEnum.NOT_BEGIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoActivityStatusEnum.PROCESS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoDrawStatusEnum.DRAW;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoDrawStatusEnum.NOT_DRAW;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.ACTIVITY_PROCESSING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.IndicatorConfigResolver.resolveItemGroupId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum.MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum.ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.USER_NOTIFICATION_INFO_QUERY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo.UserNotificationSourceTypeEnum.SELECTION_DECISION;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.list.enums.UserActivityListSourceTypeEnum.PORTRAIT_DISTRIBUTOR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_NOTIFICATION_INFO_QUERY_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.userNotificationInfoCacheSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.userNotificationInfoCacheExpireSeconds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.userNotificationInfoMockActivityIds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.selectionDecisionNotificationInfoConfig;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ecyrd.speed4j.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.GetUserActivityListInfoDrawStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.SelectionDecisionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.SelectionDecisionResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.list.enums.DarenSquareSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.list.strategy.UserActivityListResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.SicServiceFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UserNotificationShowConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.GetUserActivityListInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.UserActivityListAggInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.UserActivityListInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.sic.service.protobuf.BatchExistResult;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
@Component
@Slf4j
public class SelectionDecisionInfoStrategy implements UserNotificationInfoStrategy {

    @Autowired
    private UserActivityListResolver userActivityListResolver;

    @Autowired
    private NotificationPushBasicService notificationPushBasicService;

    @Autowired
    private IndicatorConfigDAO indicatorConfigDAO;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private SicServiceFetchService sicServiceFetchService;

    private static final String CONTENT_KEY_LESS = "less";

    private static final String CONTENT_KEY_MORE = "more";

    private static final String CONTENT_KEY_LIMIT = "limit";

    @Override
    public UserNotificationShowConfig query(long userId, String param) {
        StopWatch sw = new StopWatch();
        log.info("[选品中心用户触达信息] 参数 userId:{}, param:{}", userId, param);
        SelectionDecisionParamBO paramBO;
        try {
            paramBO = fromJSON(param, SelectionDecisionParamBO.class);
        } catch (Exception e) {
            log.error("[选品中心用户触达信息] 参数异常 userId:{}, param:{}", userId, param, e);
            perfException(USER_NOTIFICATION_INFO_QUERY, "param.error");
            throw new BizException(USER_NOTIFICATION_INFO_QUERY_ERROR, "参数异常");
        }
        Long itemId = paramBO.getItemId();
        if (Objects.isNull(itemId)) {
            log.error("[选品中心用户触达信息] 参数异常 userId:{}, param:{}", userId, param);
            perfException(USER_NOTIFICATION_INFO_QUERY, "param.error");
            throw new BizException(USER_NOTIFICATION_INFO_QUERY_ERROR, "参数异常");
        }

        if (userNotificationInfoCacheSwitch.get()) {
            String redisKey = NotificationRedisKeyEnum.USER_NOTIFICATION_INFO_CACHE
                    .getFullKeyJoinWithColon(getSource().getSource(), String.valueOf(userId), String.valueOf(itemId));
            String redisValue = getGrowthRedisCommands().get(redisKey);

            if (StringUtils.isNotBlank(redisValue)) {
                perfSuccessWithWatch(USER_NOTIFICATION_INFO_QUERY, "hit.cache", sw);
                return fromJSON(redisValue, UserNotificationShowConfig.class);
            }

            UserNotificationShowConfig result = doQuery(userId, itemId);
            getGrowthRedisCommands().setex(redisKey, userNotificationInfoCacheExpireSeconds.get(), toJSON(result));

            perfSuccessWithWatch(USER_NOTIFICATION_INFO_QUERY, "miss.cache", sw);
            return result;
        }

        UserNotificationShowConfig result = doQuery(userId, itemId);
        perfSuccessWithWatch(USER_NOTIFICATION_INFO_QUERY, "without.cache", sw);

        return result;
    }

    public UserNotificationShowConfig doQuery(long userId, long itemId) {
        List<UserActivityListInfoDTO> activities = getUserActivityList(userId);
        List<Long> activityIds = activities.stream().map(UserActivityListInfoDTO::getActivityId)
                .collect(Collectors.toList());

        activityIds = filterProcessingNotificationActivities(activityIds);

        activityIds = filterExistedItemGroupActivities(userId, activityIds, itemId);

        if (CollectionUtils.isEmpty(activityIds)) {
            return UserNotificationShowConfig.newBuilder()
                    .setShow(false)
                    .setData(StringUtils.EMPTY)
                    .build();
        }

        List<Long> finalActivityIds = activityIds;
        UserActivityListInfoDTO selectedUserActivity = activities.stream()
                .filter(activity -> activity.getActivityId() == finalActivityIds.get(0))
                .findFirst().orElse(null);
        if (Objects.isNull(selectedUserActivity)) {
            log.info("[选品中心用户触达信息] 符合要求活动为空 userId:{}, itemId:{}", userId, itemId);
            return UserNotificationShowConfig.newBuilder()
                    .setShow(false)
                    .setData(StringUtils.EMPTY)
                    .build();
        }

        UserNotificationShowConfig result = getUserNotificationShowConfig(selectedUserActivity);
        log.info("[选品中心用户触达信息] 用户触达信息查询成功 userId:{}, itemId:{}, result:{}", userId, itemId, result);

        return result;
    }

    private List<Long> filterExistedItemGroupActivities(long userId, List<Long> activityIds, long itemId) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return Lists.newArrayList();
        }

        Map<Long, List<Long>> itemGroupIdMap = getItemGroupIdMap(userId, activityIds);

        List<Long> itemGroupIds = new ArrayList<>(itemGroupIdMap.keySet());

        Map<Long/*selectionGroupId*/, Long/*subjectId*/> selectionMap = Maps.newHashMap();
        itemGroupIds.forEach(itemGroupId -> selectionMap.put(itemGroupId, itemId));

        // 商品包批量判存 如果未勾选实时判存则不会返回
        List<BatchExistResult> batchExistResults = sicServiceFetchService.batchQueryExists(selectionMap);
        if (CollectionUtils.isEmpty(batchExistResults)) {
            log.info("[选品中心用户触达信息] 商品包判存结果为空 userId:{}, activityIds:{}, itemId:{}",
                    userId, toJSON(activityIds), itemId);
            return Lists.newArrayList();
        }

        Set<Long> existedItemGroupIds = batchExistResults.stream().filter(BatchExistResult::getResult)
                .map(result -> result.getBatchExistKey().getSelectionGroupId())
                .collect(Collectors.toSet());
        List<Long> existedActivityIds = existedItemGroupIds.stream()
                .flatMap(itemGroupId -> itemGroupIdMap.get(itemGroupId).stream())
                .collect(Collectors.toList());

        List<Long> result = activityIds.stream().filter(existedActivityIds::contains).collect(Collectors.toList());

        log.info("[选品中心用户触达信息] 符合商品包判存的活动ID为 activityIds:{}", toJSON(result));
        return result;
    }

    private Map<Long, List<Long>> getItemGroupIdMap(long userId, List<Long> activityIds) {
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.listUserRecordsOfMultiActivity(activityIds, userId);

        List<Long> parentTaskIds = userRegistrationRecords.stream()
                .filter(record -> record.getEntityType().equals(EntityTypeEnum.TASK.getCode()))
                .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());

        List<TaskDO> childTasks = taskLocalCacheService.batchGetChildTask(parentTaskIds);
        List<Long> childTaskIds = childTasks.stream().map(TaskDO::getId).collect(Collectors.toList());
        List<IndicatorConfigDO> indicatorConfigs = indicatorConfigDAO
                .queryList(activityIds, childTaskIds);

        Map<Long/*itemGroupId*/, List<Long>/*activityId*/> itemGroupIdMap = Maps.newHashMap();
        indicatorConfigs.forEach(config -> {
            Long itemGroupId = resolveItemGroupId(config);
            if (Objects.isNull(itemGroupId)) {
                return;
            }

            Long activityId = config.getActivityId();
            List<Long> activityIdsOfCertainItemGroup = itemGroupIdMap.getOrDefault(itemGroupId, Lists.newArrayList());
            activityIdsOfCertainItemGroup.add(activityId);
            itemGroupIdMap.put(itemGroupId, activityIdsOfCertainItemGroup);
        });
        return itemGroupIdMap;
    }

    private List<Long> filterProcessingNotificationActivities(List<Long> activityIds) {
        // 临时mock逻辑
        List<Long> mockActivityIds = userNotificationInfoMockActivityIds.get();
        if (CollectionUtils.isNotEmpty(mockActivityIds)) {
            return mockActivityIds;
        }

        if (CollectionUtils.isEmpty(activityIds)) {
            return Lists.newArrayList();
        }

        List<NotificationPushConfigBO> notificationPushConfigs = notificationPushBasicService
                .getNotificationPushConfigByEntityIds(activityIds, ACTIVITY,
                        UserActivityEventTypeEnum.DRAW.getValue(), MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE,
                        ACTIVITY_PROCESSING.getCode());

        if (CollectionUtils.isEmpty(notificationPushConfigs)) {
            log.info("[选品中心用户触达信息] 触达配置为空 activityIds:{}", toJSON(activityIds));
            return Lists.newArrayList();
        }

        activityIds = notificationPushConfigs.stream().map(NotificationPushConfigBO::getEntityId)
                .distinct().collect(Collectors.toList());

        log.info("[选品中心用户触达信息] 符合触达配置活动ID activityIds:{}", toJSON(activityIds));
        return activityIds;
    }

    private List<UserActivityListInfoDTO> getUserActivityList(long userId) {
        GetUserActivityListInfoRequest request = GetUserActivityListInfoRequest.newBuilder()
                .setUserId(userId)
                .setSource(PORTRAIT_DISTRIBUTOR.getValue())
                .setDrawStatus((int) GetUserActivityListInfoDrawStatusEnum.NONE.getValue())
                .setActivityStatus((int) GetUserActivityListInfoDrawStatusEnum.NONE.getValue())
                .setScene(DarenSquareSceneEnum.USER_NOTIFICATION_INFO.getValue())
                .build();

        UserActivityListAggInfoDTO aggInfo = userActivityListResolver.resolve(request);
        if (null == aggInfo || CollectionUtils.isEmpty(aggInfo.getUserActivityListList())) {
            log.info("[选品中心用户触达信息] 用户活动列表为空 userId:{}", userId);
            return Lists.newArrayList();
        }
        List<UserActivityListInfoDTO> activities = aggInfo.getUserActivityListList();
        long currentTimeMillis = System.currentTimeMillis();

        // 过滤已经结束的活动
        List<UserActivityListInfoDTO> result = activities.stream().filter(activity -> {
            long showStatus = activity.getShowStatus();
            if (showStatus == END.getValue()) {
                return false;
            }

            return showStatus != NOT_DRAW.getValue() || currentTimeMillis <= activity.getEndTime();
        }).collect(Collectors.toList());

        log.info("[选品中心用户触达信息] 符合条件用户活动为 activityList:{}", toJSON(result));
        return result;
    }

    private static UserNotificationShowConfig getUserNotificationShowConfig(UserActivityListInfoDTO userActivity) {
        SelectionDecisionInfoConfig config = selectionDecisionNotificationInfoConfig.getObject();
        Map<String, Map<String, SelectionDecisionDataBO>> statusConfig = config.getConfig();

        String activityName = userActivity.getActivityName();
        long activityId = userActivity.getActivityId();
        GetUserActivityListInfoActivityStatusEnum status =
                GetUserActivityListInfoActivityStatusEnum.of(userActivity.getShowStatus());

        long currentTimeMillis = System.currentTimeMillis();

        long activityWrapStatus = 0;
        long drawWrapStatus = 0;

        switch (status) {
            case NOT_BEGIN:
            case PROCESS:
                drawWrapStatus = DRAW.getValue();
                activityWrapStatus = status.getValue();
                break;
            case UNDRAW:
                drawWrapStatus = NOT_DRAW.getValue();
                long startTime = userActivity.getStartTime();
                long endTime = userActivity.getEndTime();
                if (currentTimeMillis < startTime) {
                    activityWrapStatus = NOT_BEGIN.getValue();
                } else if (currentTimeMillis <= endTime) {
                    activityWrapStatus = PROCESS.getValue();
                }
                break;
            default:
        }

        UserNotificationShowConfig.Builder resultBuilder = UserNotificationShowConfig.newBuilder()
                .setShow(false)
                .setData(StringUtils.EMPTY);

        if (activityWrapStatus == 0 || drawWrapStatus == 0) {
            log.info("[选品中心用户触达信息] 符合状态活动ID为空 activityId:{}", activityId);
            return resultBuilder.build();
        }

        Map<String, SelectionDecisionDataBO> dataMap = statusConfig
                .getOrDefault(String.valueOf(activityWrapStatus), Maps.newHashMap());

        SelectionDecisionDataBO selectionDecisionData = dataMap.get(String.valueOf(drawWrapStatus));
        if (Objects.isNull(selectionDecisionData)) {
            log.info("[选品中心用户触达信息] 触达信息配置为空 activityId:{}", activityId);
            return resultBuilder.build();
        }

        String jumpUrlTemplate = selectionDecisionData.getJumpUrl();
        String jumpUrl = String.format(jumpUrlTemplate, activityId);

        String content = getContent(userActivity, activityWrapStatus, drawWrapStatus, selectionDecisionData);

        SelectionDecisionResultBO data = SelectionDecisionResultBO.builder()
                .activityId(activityId)
                .activityName(activityName)
                .content(content)
                .jumpUrl(jumpUrl)
                .build();

        log.info("[选品中心用户触达信息] 选品触达结果 result:{}", toJSON(data));
        return resultBuilder.setShow(true).setData(toJSON(data)).build();
    }

    private static String getContent(UserActivityListInfoDTO userActivity, long activityWrapStatus,
            long drawWrapStatus, SelectionDecisionDataBO selectionDecisionData) {
        String content = selectionDecisionData.getContent();

        if (activityWrapStatus == PROCESS.getValue() && drawWrapStatus == DRAW.getValue()) {
            long drawCount = userActivity.getDrawCount();

            Map<String, Object> contentMap = fromJSON(content, Map.class, String.class, Object.class);

            Integer limit = MapUtils.getInteger(contentMap, CONTENT_KEY_LIMIT, 100);
            String contentKey = drawCount < limit ? CONTENT_KEY_LESS : CONTENT_KEY_MORE;

            String contentTemplate = MapUtils.getString(contentMap, contentKey, "");
            content = StringUtils.equals(contentKey, CONTENT_KEY_LESS)
                      ? contentTemplate : String.format(contentTemplate, drawCount);
        }
        return content;
    }

    @Override
    public UserNotificationSourceTypeEnum getSource() {
        return SELECTION_DECISION;
    }
}
