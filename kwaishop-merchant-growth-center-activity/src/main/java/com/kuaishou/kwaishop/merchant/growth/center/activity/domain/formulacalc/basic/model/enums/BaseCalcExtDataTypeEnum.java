package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Getter
@AllArgsConstructor
public enum BaseCalcExtDataTypeEnum {

    UNKNOWN("unknown", "未知"),
    CYCLE_DURATION("cycleDuration", "周期时间"),
    ;

    private final String type;

    private final String desc;

    public static BaseCalcExtDataTypeEnum getByType(String type) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(UNKNOWN);
    }
}
