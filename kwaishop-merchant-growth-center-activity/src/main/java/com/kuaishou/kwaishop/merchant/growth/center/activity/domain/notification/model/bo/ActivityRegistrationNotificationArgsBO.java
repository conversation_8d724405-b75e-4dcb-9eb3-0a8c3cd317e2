package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityRegistrationNotificationArgsBO {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * RegistrationNotificationTaskTypeEnum 推送类型
     */
    private int registrationNotificationType;

    /**
     * 过滤人群包id
     */
    private int crowdGroupId;

}
