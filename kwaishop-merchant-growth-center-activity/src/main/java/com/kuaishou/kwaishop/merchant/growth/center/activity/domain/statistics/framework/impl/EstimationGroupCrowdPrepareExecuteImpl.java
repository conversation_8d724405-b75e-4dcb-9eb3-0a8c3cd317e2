package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.getComparatorNum;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.convert.AutoEstimateFlagGenerator.estimateTypeEnable;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationPerfEnum.DAP_ESTIMATION_PREPARE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.estimationCrowdPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import com.github.rholder.retry.RetryException;
import com.google.api.client.util.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.ProtocolCheckService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.command.UserEstimationPrepareSaveCmd;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyMsgSendService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.UserEstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.EstimationCrowdPullBasicData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.StrategyIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.StrategyPeriodConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.StrategySnapshotExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserBasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserCrowdAbConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyGroupAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategySnapshotEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationPrepareEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.AutoEstimationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.StrategySnapshotSubStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationPrepareEntityRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.service.UserEstimationStrategyDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.EstimationGroupCrowdInfoBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-05
 */
@Service
@Slf4j
@Lazy
public class EstimationGroupCrowdPrepareExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private EstimationCacheService estimationCacheService;

    @Autowired
    private UserEstimationStrategyDomainService userEstimationStrategyDomainService;

    @Autowired
    private UserEstimationPrepareEntityRepository userEstimationPrepareEntityRepository;

    @Autowired
    private UserEstimationStrategyReadService userEstimationStrategyReadService;

    @Autowired
    private EstimationStrategyMsgSendService estimationStrategyMsgSendService;

    @Autowired
    private ProtocolCheckService protocolCheckService;

    @Autowired
    private EstimationStrategyReadService estimationStrategyReadService;


    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        EstimationGroupCrowdInfoBatchExecuteEvent executeEvent = (EstimationGroupCrowdInfoBatchExecuteEvent) event;
        // 1 查询本次测算人群导入配置
        String eventId = event.getEventId();
        try {
            EstimationStrategyGroupAggregateRoot groupAggregateRoot =
                    executeEvent.getEstimationStrategyGroupAggregateRoot();
            EstimationStrategySnapshotEntity specifyVersionEntity = groupAggregateRoot.getSpecifyVersionEntity();
            // 使用快照的人群配置
            Long strategyId = executeEvent.getStrategyId();
            String prepareSyncVersion = executeEvent.getPrepareSyncVersion();
            // 1.1 先尝试从缓存中拉取人群数据（重试场景）
            Set<Long> estimationCrowdSet = loadCrowdInfoFromCache(groupAggregateRoot, prepareSyncVersion);
            if (CollectionUtils.isNotEmpty(estimationCrowdSet)) {
                return estimationCrowdSet;
            }
            // 2 测算获取人群
            Set<Long> crowdUserIdSet =
                    userEstimationStrategyReadService.queryEstimationUserIdWhenEstimationProcess(groupAggregateRoot);
            if (CollectionUtils.isEmpty(crowdUserIdSet)) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "查询测算人群数据为空");
            }
            // 非实时测算才校验
            if (BooleanUtils.isTrue(executeEvent.getCustomizeBasic()) && !groupAggregateRoot.realtimeEstimation()) {
                BasicConfigBO basicConfigBO = specifyVersionEntity.getBasicConfigBO();
                // 有商家不在自定义基期里面,那么需要异常抛出
                protocolCheckService.checkStrategyCrowdExcelToSeller("测算校验-", basicConfigBO, crowdUserIdSet);
            }

            // 3 缓存测算总人数
            int size = crowdUserIdSet.size();
            estimationCacheService.setEstimationCrowdTotalCnt(strategyId, prepareSyncVersion,
                    EstimationStrategyTypeEnum.GROUP, size);

            // 4 缓存测算用户集合到bitMap
            estimationCacheService.setEstimationCrowdSet(strategyId, prepareSyncVersion,
                    EstimationStrategyTypeEnum.GROUP, crowdUserIdSet);

            // 6 更新测算人群总数到策略组
            specifyVersionEntity.fillEstimationStartTime();
            specifyVersionEntity.setSubStatus(StrategySnapshotSubStatusEnum.CROWD_DATA_SYNC);
            specifyVersionEntity.updateCrowCntOnExt((long) size);
            // 5 非第一个周期-发送周期测算人群固化消息
            if (groupAggregateRoot.periodRealTimeCalcAndNoFirstVersion()) {
                sendStrategyVersionCrowdFixMsg(groupAggregateRoot);
            }
            PerfUtil.perfSuccess(DAP_ESTIMATION_PREPARE, "initExecuteCrowdAndCache", eventId);
            return crowdUserIdSet;
        } catch (Exception e) {
            log.error("[{}] initExecuteCrowdAndCache happen error", eventId, e);
            PerfUtil.perfException(DAP_ESTIMATION_PREPARE, "initExecuteCrowdAndCache", eventId);
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送策略人群修复消息
     *
     * @param estimationStrategyGroupAggregateRoot
     */
    private void sendStrategyVersionCrowdFixMsg(EstimationStrategyGroupAggregateRoot estimationStrategyGroupAggregateRoot) {
        // 发送策略组版本人群固化确定消息
        try {
            estimationStrategyMsgSendService.sendGroupPeriodEstimationFixCrowdMsg(estimationStrategyGroupAggregateRoot);
        } catch (Exception e) {
            log.error("[周期测算] sendStrategyVersionCrowdFixMsg error {}", estimationStrategyGroupAggregateRoot.getId(), e);
        }
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        if (!EnvUtils.isProd()) {
            log.info("[EstimationGroupCrowdPrepareExecuteImpl] batchCustomizeExecute start eventId {} executeConfig "
                    + "{} userId {}", eventId, executeConfig, toJSON(userIdList));
        }
        ExecuteHandleResult result = new ExecuteHandleResult();
        EstimationCrowdPullBasicData crowdPullBasicData = fromJSON(executeConfig, EstimationCrowdPullBasicData.class);
        BasicConfigBO basicConfigBO = crowdPullBasicData.getBasicConfigBO();
        String bizDate = crowdPullBasicData.getBizDate();
        Boolean customizeBasic = crowdPullBasicData.getCustomizeBasic();
        List<Long> successUserIdList = Lists.newArrayList();
        List<Long> failedUserIdList = Lists.newArrayList();
        Long groupId = crowdPullBasicData.getStrategyId();
        String strategyVersion = crowdPullBasicData.getStrategyVersion();
        // 判断后续是否需要重新同步版本基期
        String prepareSyncVersion = estimationStrategyReadService.queryUserPrepareSyncVersion(groupId,
                strategyVersion, EstimationStrategyTypeEnum.GROUP);
        userIdList.forEach(userId -> {
            String ukId = String.join("-", eventId, String.valueOf(userId));
            // 加锁
            try {
                // 1 查询DB判断用户策略组基期数据是否已经落库
                List<UserEstimationPrepareEntity> userEstimationPrepareEntities =
                        userEstimationPrepareEntityRepository.queryVersionMapByBizKey(userId, groupId,
                                null, EstimationStrategyTypeEnum.GROUP);
                UserEstimationPrepareEntity estimationPrepareEntity = null;
                if (CollectionUtils.isNotEmpty(userEstimationPrepareEntities)) {
                    Map<String, UserEstimationPrepareEntity> groupVersionMap = userEstimationPrepareEntities.stream()
                            .collect(Collectors.toMap(UserEstimationPrepareEntity::getStrategyVersion,
                                    Function.identity(), (a, b) -> a));
                    estimationPrepareEntity = groupVersionMap.get(prepareSyncVersion);
                }
                // 1.1 幂等判断基期/AB数据是否齐全
                if (estimationPrepareEntity != null && estimationPrepareEntity.userBasicDataReady()) {
                    // 防止前置消息发送失败，再次尝试发送同步消息
                    sendHiveMsg(estimationPrepareEntity, bizDate, true);
                    successUserIdList.add(userId);
                    return;
                }
                Map<String, Object> jsonKeyBasicAvgMap = Maps.newHashMap();
                if (BooleanUtils.isTrue(customizeBasic)) {
                    // 需要获取自定义基期数据
                    jsonKeyBasicAvgMap = userEstimationStrategyReadService.getUserEstimationCustomizeValue(groupId,
                            EstimationStrategyTypeEnum.GROUP, userId, basicConfigBO);
                }
                // 2 构造保存指令
                UserEstimationPrepareSaveCmd userEstimationPrepareSaveCmd = buildSaveCmd(userId,
                        prepareSyncVersion, estimationPrepareEntity, jsonKeyBasicAvgMap, crowdPullBasicData,
                        userEstimationPrepareEntities);
                estimationPrepareEntity =
                        userEstimationStrategyDomainService.saveUserPrepareEntityOnly(userEstimationPrepareSaveCmd);
                // 3 发送kafka消息同步hive（维护p_date;kafka2Hive保障幂等）
                // 后续版本没有写入成功标识
                sendHiveMsg(estimationPrepareEntity, bizDate, false);
                // 4 处理结果构建（会被缓存做幂等）
                successUserIdList.add(userId);
                PerfUtil.perfSuccess(DAP_ESTIMATION_PREPARE, "saveCrowdInfo", ukId);
            } catch (DataIntegrityViolationException ex) {
                log.warn("[策略组人群基期数据拉取&保存] 重复消费！userId {} eventId {}", userId, eventId);
                perfException(DAP_ESTIMATION_PREPARE, "duplicateKeyException", ukId);
            } catch (Exception e) {
                log.error("[策略组人群基期数据拉取&保存] 出现异常 userId {} eventId {}", userId, eventId, e);
                PerfUtil.perfException(DAP_ESTIMATION_PREPARE, "saveCrowdInfo", ukId, e.getMessage());
                failedUserIdList.add(userId);
            }
        });

        result.setSuccessUserList(successUserIdList);
        estimationCacheService.batchAddUserToSuccessList(eventId, successUserIdList);
        result.setFailUserList(failedUserIdList);
        return result;
    }

    private void sendHiveMsg(UserEstimationPrepareEntity estimationPrepareEntity, String bizDate, boolean next)
            throws ExecutionException, RetryException {
        // 如果所有的测算指标都不需要传基期，则不需要发kafka消息
        if (estimationPrepareEntity.withoutBasicData()) {
            return;
        }
        boolean sendRes = estimationStrategyMsgSendService.sendUserPrepareMsg(estimationPrepareEntity,
                bizDate, next);
        if (sendRes) {
            // 成功发送成功标识
            estimationPrepareEntity.updateSendFlag();
        }
    }

    @Override
    protected boolean needRetryWhenExp() {
        return true;
    }


    /**
     * 先尝试从缓存中拉取人群数据
     *
     * @param strategyGroupEntity
     * @param prepareSyncVersion  *
     * @return
     * @throws IOException
     */
    private Set<Long> loadCrowdInfoFromCache(EstimationStrategyGroupAggregateRoot strategyGroupEntity,
                                             String prepareSyncVersion) throws IOException {
        Long strategyId = strategyGroupEntity.getId();
        EstimationStrategySnapshotEntity specifyVersionEntity = strategyGroupEntity.getSpecifyVersionEntity();
        StrategySnapshotExtBO extBO = specifyVersionEntity.getExtBO();
        if (extBO == null || extBO.getSyncCrowdCnt() == null) {
            return Sets.newHashSet();
        }
        long syncCrowdCnt = extBO.getSyncCrowdCnt();
        long cacheCrowdTotalCnt = estimationCacheService.getEstimationCrowdTotalCnt(strategyId, prepareSyncVersion,
                EstimationStrategyTypeEnum.GROUP);
        // 如果缓存的总人数和本次导入的人群总人数一致，则直接从缓存中拉取人群数据
        if (syncCrowdCnt == cacheCrowdTotalCnt) {
            Set<Long> estimationCrowdSet =
                    estimationCacheService.getEstimationCrowdSet(strategyId, prepareSyncVersion,
                            EstimationStrategyTypeEnum.GROUP);
            if (syncCrowdCnt == estimationCrowdSet.size()) {
                return estimationCrowdSet;
            }
        }
        return Sets.newHashSet();
    }

    private UserEstimationPrepareSaveCmd buildSaveCmd(
            Long userId, String prepareSyncVersion, UserEstimationPrepareEntity estimationPrepareEntity,
            Map<String, Object> jsonKeyBasicAvgMap, EstimationCrowdPullBasicData crowdPullBasicData,
            List<UserEstimationPrepareEntity> userEstimationPrepareEntities) {
        Long groupId = crowdPullBasicData.getStrategyId();
        CrowdConfigBO crowdConfigBO = crowdPullBasicData.getCrowdConfigBO();
        BasicConfigBO basicConfigBO = crowdPullBasicData.getBasicConfigBO();
        Map<Long, IndicatorDO> baseIndicatorMap = crowdPullBasicData.getBaseIndicatorMap();
        Map<Long, String> jsonKeyMap = crowdPullBasicData.getJsonKeyMap();
        Integer activityDays = crowdPullBasicData.getActivityDays();
        Integer autoEstimateFlag = crowdPullBasicData.getAutoEstimateFlag();
        Map<Long, IndicatorConfigDO> estimationIndicatorConfigMap = crowdPullBasicData.getEstimationIndicatorConfigMap();
        // 之前已插入对应的prepare数据，无需再查询基期
        UserBasicConfigBO userBasicConfigBO = null;
        UserCrowdAbConfigBO crowdConfig = null;
        boolean basicDataReady = estimationPrepareEntity != null && estimationPrepareEntity.userBasicDataReady();
        if (CollectionUtils.isNotEmpty(userEstimationPrepareEntities)) {
            // 过滤出早于当前版本的prepare数据
            userEstimationPrepareEntities = userEstimationPrepareEntities.stream()
                    .filter(userEstimationPrepareEntity -> {
                        String strategyVersion = userEstimationPrepareEntity.getStrategyVersion();
                        return getComparatorNum(strategyVersion) < getComparatorNum(prepareSyncVersion);
                    })
                    .sorted(Comparator.comparingInt(strategy -> getComparatorNum(strategy.getStrategyVersion())))
                    .collect(Collectors.toList());
        }
        // 2.1 查询基期
        if (!basicDataReady) {
            List<UserBasicConfigBO.UserIndicatorBasicConfig> indicatorBasicDataList =
                    userEstimationStrategyReadService.parseIndicatorBasicData(userId,
                            basicConfigBO, jsonKeyBasicAvgMap, jsonKeyMap, baseIndicatorMap, activityDays, estimationIndicatorConfigMap);
            userBasicConfigBO = UserBasicConfigBO.builder()
                    .indicatorBasicDataList(indicatorBasicDataList)
                    .build();
            // 2.2 获取用户人群参数
            crowdConfig = userEstimationStrategyReadService.getUserCrowdConfig(crowdConfigBO, userId);
        }
        // 2.3 构造实体落库
        return UserEstimationPrepareSaveCmd.builder()
                .userId(userId)
                .strategyId(groupId)
                .strategyVersion(prepareSyncVersion)
                .strategyTypeEnum(EstimationStrategyTypeEnum.GROUP)
                .userBasicConfigBO(userBasicConfigBO)
                .crowdConfig(crowdConfig)
                .oldEntity(estimationPrepareEntity)
                .preEntityList(userEstimationPrepareEntities)
                .autoEstimateFlag(autoEstimateFlag)
                .build();
    }

    /**
     * 计算测算类型
     * 实时测算-2
     * 周期测算-1
     * 其他-null
     *
     * @param autoEstimateFlag
     * @return
     */
    private Integer calcEstimationType(Integer autoEstimateFlag) {
        // [cb] 计算测算类型
        if (autoEstimateFlag == null || autoEstimateFlag == 0) {
            return null;
        }
        boolean realtimeEstimation = estimateTypeEnable(autoEstimateFlag, AutoEstimationTypeEnum.REALTIME_ESTIMATE);
        if (realtimeEstimation) {
            return AutoEstimationTypeEnum.REALTIME_ESTIMATE.getCode();
        }
        boolean periodEstimation = estimateTypeEnable(autoEstimateFlag, AutoEstimationTypeEnum.PERIOD_ESTIMATE);
        if (periodEstimation) {
            return AutoEstimationTypeEnum.PERIOD_ESTIMATE.getCode();
        }
        return null;
        // [ce]
    }

    /**
     * 获取实时标签
     *
     * @param dynamicEventTags
     * @return
     */
    private static String getRealtimeTag(List<String> dynamicEventTags) {
        // [cb] 获取实时标签
        if (CollectionUtils.isEmpty(dynamicEventTags)) {
            return null;
        }
        return String.join(",", dynamicEventTags);
        // [ce]
    }

    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 获取执行配置必要信息携带
        EstimationGroupCrowdInfoBatchExecuteEvent executeEvent = (EstimationGroupCrowdInfoBatchExecuteEvent) event;
        EstimationStrategyGroupAggregateRoot strategyGroupEntity =
                executeEvent.getEstimationStrategyGroupAggregateRoot();
        EstimationStrategySnapshotEntity specifyVersionEntity = strategyGroupEntity.getSpecifyVersionEntity();
        // 获取对应策略组版本所对应的周期天数
        Integer periodNum = estimationStrategyReadService.parseMultiVersionEstimationPeriodNum(strategyGroupEntity);
        if (periodNum == null || periodNum <= 0) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "策略组版本周期天数为空");
        }
        StrategyPeriodConfig periodConfigBO = strategyGroupEntity.getPeriodConfigBO();
        StrategyIndicatorConfigBO indicatorConfigBO = strategyGroupEntity.getIndicatorConfigBO();
        Map<Long, IndicatorDO> baseIndicatorMap = executeEvent.getBaseIndicatorMap();
        // 获取人群&基期配置
        EstimationCrowdPullBasicData crowdPullBasicData =
                new EstimationCrowdPullBasicData(specifyVersionEntity.getCrowdConfigBO(),
                        specifyVersionEntity.getBasicConfigBO(), executeEvent.getCustomizeBasic(),
                        baseIndicatorMap, executeEvent.getJsonKeyMap(),
                        specifyVersionEntity.getExtBO().getBizDate(), periodNum);
        crowdPullBasicData.setAutoEstimateFlag(specifyVersionEntity.getAutoEstimateFlag());
        crowdPullBasicData.setStrategyId(executeEvent.getStrategyId());
        crowdPullBasicData.setStrategyVersion(specifyVersionEntity.getStrategyVersion());
        crowdPullBasicData.setStrategyType(EstimationStrategyTypeEnum.GROUP);
        crowdPullBasicData.setDynamicEventTags(convertDynamicEventTags(periodConfigBO));
        if (indicatorConfigBO != null) {
            // 构造测算指标配置
            crowdPullBasicData.setEstimationIndicatorConfigMap(indicatorConfigBO.buildEstimationIndicatorConfig(baseIndicatorMap));
        }
        return toJSON(crowdPullBasicData);
    }

    /**
     * 获取实时测算的tags
     *
     * @param periodConfigBO
     * @return
     */
    private List<String> convertDynamicEventTags(StrategyPeriodConfig periodConfigBO) {
        if (periodConfigBO == null || periodConfigBO.getRealtimeEstimationConfig() == null) {
            return Lists.newArrayList();
        }
        return periodConfigBO.getRealtimeEstimationConfig().getDynamicEventTagsList();
    }

    @Override
    protected boolean filterSuccessUser() {
        return true;
    }

    @Override
    int getPartitionSize() {
        return estimationCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.ESTIMATION_GROUP_CROWD_PREPARE;
    }
}
