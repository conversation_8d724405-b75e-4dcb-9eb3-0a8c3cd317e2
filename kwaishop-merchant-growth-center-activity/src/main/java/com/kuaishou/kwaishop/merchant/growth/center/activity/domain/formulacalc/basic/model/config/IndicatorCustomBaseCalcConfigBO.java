package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorCustomBaseCalcConfigBO {

    /**
     * 指标ID - 自定义基期计算配置映射
     */
    private Map<String/*indicatorId*/, IndicatorCustomBaseCalcItemConfigBO/*config*/> calcItemConfigMap;
}
