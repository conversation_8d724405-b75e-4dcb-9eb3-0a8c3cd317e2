package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.app.consumer.hivemsg.HiveSyncDoneMsgInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.UserTaskPerfEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-27
 */
@Slf4j
public abstract class AbstractHiveSyncDoneMsgProcessor {

    public void process(HiveSyncDoneMsgInfo msgInfo) {
        log.info("hive sync done msg process start, msgInfo:{}", ObjectMapperUtils.toJSON(msgInfo));
        StopWatch stopWatch = PerfUtil.getStopWatch();
        Boolean flag = Boolean.TRUE;
        try {
            this.doProcess(msgInfo);
        } catch (Exception e) {
            flag = Boolean.FALSE;
            PerfUtil.perfExceptionWithWatch(UserTaskPerfEnum.MQ_TASK_CROWD_DIFF_PROCESS, msgInfo.getBizCode(),
                    e.getClass().getSimpleName(), stopWatch);
            throw new RuntimeException(e);
        } finally {
            log.info("hive sync done msg process, success:{}, cost:{}", flag, stopWatch.getTimeMicros());
        }
    }

    protected abstract void doProcess(HiveSyncDoneMsgInfo msgInfo);
}
