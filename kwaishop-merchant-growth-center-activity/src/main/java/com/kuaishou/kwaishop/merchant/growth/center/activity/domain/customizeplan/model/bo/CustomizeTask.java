package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardItemBO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeTask {

    /**
     * 任务ID
     */
    private long taskId;

    /**
     * 标题 eg：学习开店必修课
     */
    private String title;

    /**
     * 指标
     */
    private List<CustomizeIndicator> indicatorList;

    /**
     * 奖励
     */
    private List<AwardItemBO> awardList;

    /**
     *状态 对应UserTaskStatusEnum
     */
    private int userTaskStatus;
}
