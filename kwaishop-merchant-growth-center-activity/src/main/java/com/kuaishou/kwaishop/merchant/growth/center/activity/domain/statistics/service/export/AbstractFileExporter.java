package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.BLOB_STORE_TABLE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.FILE_PATH_DIR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_EXPORT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.exportSellerDetailPageSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.taskReviewExportPageSizeConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.fileExportSingleFileMaxSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.fileUploadWaitSeconds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.specialCharRegEx;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ecyrd.speed4j.StopWatch;
import com.google.common.base.Joiner;
import com.google.common.util.concurrent.ListenableFuture;
import com.kuaishou.api.blobstore.ApiBlobStore;
import com.kuaishou.api.blobstore.BlobStoreKey;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.BaseExportParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportElement;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportRequest;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.CdnUploadFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.es.EsQueryResponse;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.operation.longtask.client.openutil.UserTaskUtil;

import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.CompressionMethod;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-06
 */
@Slf4j
@Lazy
@Component
public abstract class AbstractFileExporter<R extends ExportRequest>
        implements FileExportService {

    @Autowired
    private CdnUploadFetchService cdnUploadFetchService;

    private static final Map<Long, Long> PROGRESS_MAP = new ConcurrentHashMap<>();

    protected abstract ExportResponse listElement(BaseExportParam param, R request);

    protected abstract String genFileName(BaseExportParam param);

    protected abstract String genZipFileName();

    protected abstract Map<String, List<List<String>>> getHeader(BaseExportParam param, R request);

    /**
     * 获取默认的分页大小
     * @return
     */
    protected int getDefaultPageSize(String bizId) {
        if (StringUtils.isBlank(bizId)) {
            return exportSellerDetailPageSize.get();
        }
        Map<String, Integer> reviewExportPageSizeConfigMap = taskReviewExportPageSizeConfig.getMap();
        if (MapUtils.isNotEmpty(reviewExportPageSizeConfigMap) && reviewExportPageSizeConfigMap.get(bizId) != null) {
            return reviewExportPageSizeConfigMap.get(bizId);
        }
        return exportSellerDetailPageSize.get();
    }

    protected void reportProgress(long scheduleId, EsQueryResponse<Map<String, Object>> queryRes) {
        if (CollectionUtils.isEmpty(queryRes.getData())) {
            PROGRESS_MAP.remove(scheduleId);
            return;
        }
        long handleNum = PROGRESS_MAP.getOrDefault(scheduleId, 0L);
        handleNum = handleNum + queryRes.getData().size();
        PROGRESS_MAP.put(scheduleId, handleNum);
        log.info("[导出进度上报] {}-{}", scheduleId, String.format("%s/%s", handleNum, queryRes.getTotal()));
        UserTaskUtil.modifyTaskProgress(scheduleId, String.format("%s/%s", handleNum, queryRes.getTotal()));
    }

    /**
     * 导出文件
     */
    public String export(BaseExportParam param, List<String> files) {
        String zipPath = doZip(param, files);
        files.forEach(f -> FileUtils.deleteQuietly(new File(f)));
        String blobKey = doUpload(zipPath);
        FileUtils.deleteQuietly(new File(zipPath));
        String url = cdnUploadFetchService.generateCdnUrl(blobKey);
        if (StringUtils.isBlank(url)) {
            log.error("[表格导出] 未获取到下载地址， {}-{}", toJSON(param), blobKey);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "未获取到下载地址");
        }
        return url;
    }

    protected List<String> doXls(BaseExportParam param, R request) {
        int suffixNum = 1;
        List<String> files = new ArrayList<>();
        String filePath = genFilePath(param, suffixNum);
        List<ExportElement> elements = new ArrayList<>();
        // 表头
        Map<String, List<List<String>>> header = getHeader(param, request);
        ExcelWriter writer = buildWriter(filePath);

        ExportResponse res = listElement(param, request);
        if (CollectionUtils.isEmpty(res.getData())) {
            // 空结果不导出
            if (!param.emptyNoExport()) {
                writeData(writer, elements, header);
                files.add(filePath);
            }
            return files;
        }
        StopWatch stopWatch = new StopWatch();
        while (true) {
            if (CollectionUtils.isEmpty(res.getData())) {
                if (CollectionUtils.isNotEmpty(elements)) {
                    writeData(writer, elements, header);
                    files.add(filePath);
                } else {
                    writer.finish();
                }
                break;
            }
            elements.addAll(res.getData());
            // 设置请求一次就返回
            if (request.queryOnceReturn()) {
                writeData(writer, elements, header);
                files.add(filePath);
                break;
            }
            if (elements.size() >= fileExportSingleFileMaxSize.get()) {
                // 关闭旧文件
                writeData(writer, elements, header);
                files.add(filePath);
                // 生成新文件
                suffixNum++;
                filePath = genFilePath(param, suffixNum);
                writer = buildWriter(filePath);
                elements = new ArrayList<>();
            }
            request.setCursor(res.getCursor());
            res = listElement(param, request);
        }
        perfSuccessWithWatch(MQ_USER_STATISTICS_EXPORT, getBizType().getType(), stopWatch);
        return files;
    }

    private void writeData(ExcelWriter writer, List<ExportElement> elements, Map<String, List<List<String>>> headers) {
        List<String> sheetNames =
                elements.stream().map(ExportElement::getSheet).distinct().collect(Collectors.toList());
        for (String sheetName : sheetNames) {
            // 分层name进行替换
            String noSpecialSheetName = removeSpecialSheetName(sheetName);
            WriteSheet sheet = EasyExcel.writerSheet(noSpecialSheetName).head(headers.get(sheetName)).build();
            List<List<String>> sheetData = elements.stream().filter(e -> e.getSheet().equals(sheetName))
                    .map(ExportElement::getData).collect(Collectors.toList());
            writer.write(sheetData, sheet);
        }
        writer.finish();
    }


    private static String removeSpecialSheetName(String sheetName) {
        String regEx = specialCharRegEx.get();
        Pattern p = Pattern.compile(regEx);
        Matcher matcher = p.matcher(sheetName);
        return matcher.replaceAll("-");
    }

    /**
     * 压缩文件
     */
    private String doZip(BaseExportParam param, List<String> files) {
        String zipPath = genZipFilePath(param);
        try {
            ZipFile zipFile = new ZipFile(zipPath);
            ZipParameters parameters = new ZipParameters();
            parameters.setCompressionMethod(CompressionMethod.STORE);
            parameters.setCompressionLevel(CompressionLevel.FASTEST);

            for (String file : files) {
                File fileToZip = new File(file);
                zipFile.addFile(fileToZip, parameters);
            }
        } catch (Exception e) {
            log.error("[文件压缩zip] 异常，files:{} zipPath:{}", files, zipPath, e);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "文件压缩zip失败");
        }
        return zipPath;
    }

    /**
     * 上传文件到blob存储
     */
    private String doUpload(String zipFilePath) {
        String filename = UUID.randomUUID().toString() + ".zip";
        try {
            BlobStoreKey key = BLOB_STORE_TABLE.key(filename);
            ListenableFuture<Boolean> future = ApiBlobStore
                    .saveToBlobStore(BLOB_STORE_TABLE.key(filename), new FileInputStream(zipFilePath), true);
            boolean result = future.get(fileUploadWaitSeconds.get(), TimeUnit.SECONDS);
            if (!result) {
                throw new BizException(BasicErrorCode.SERVER_ERROR, "文件上传blob失败");
            }
            return key.getRawKey();
        } catch (Exception e) {
            log.error("[文件上传blob] 失败, path:{}, filename:{}", zipFilePath, filename, e);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "文件上传blob失败");
        }
    }

    /**
     * 构建文件写入器
     */
    private ExcelWriter buildWriter(String filePath) {
        ExcelWriterBuilder writeBuilder =
                EasyExcel.write(filePath).excelType(ExcelTypeEnum.XLSX).autoCloseStream(true);
        return writeBuilder.build();
    }

    /**
     * 生成excel文件路径
     */
    private String genFilePath(BaseExportParam param, int suffixNum) {
        String fileName = genFileName(param);
        String processFileName = removeSpecialSheetName(fileName);
        return Joiner.on("_").join(FILE_PATH_DIR + "/" + processFileName,
                param.getFileId(), suffixNum + ExcelTypeEnum.XLSX.getValue());
    }

    public static void main(String[] args) {
        String s = removeSpecialSheetName("活动12943子活动618大促团长任务赛_夏日美食清凉变美赛道商达详情导出_1_1.xlsx");
        String s2 = removeSpecialSheetName("活动12943子活动618大促团长任务赛_夏日美食/清凉/变美赛道商达详情导出_1_1.xlsx");
        System.out.println(s);
        System.out.println(s2);
    }

    /**
     * 生成zip文件路径
     */
    private String genZipFilePath(BaseExportParam param) {
        return Joiner.on("_").join(FILE_PATH_DIR + "/" + genZipFileName(),
                param.getFileId(), ".zip");
    }

    protected String getValue(Object v) {
        if (v == null) {
            return "-";
        }
        return v.toString();
    }

}
