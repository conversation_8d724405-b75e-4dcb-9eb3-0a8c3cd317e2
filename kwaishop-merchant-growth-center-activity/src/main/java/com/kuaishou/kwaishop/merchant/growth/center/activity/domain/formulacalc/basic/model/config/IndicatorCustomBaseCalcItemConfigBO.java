package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorCustomBaseCalcItemConfigBO {

    /**
     * 指标ID
     */
    private Long indicatorId;

    /**
     * 支持自定义计算规则Code列表
     */
    private List<String> supportedRuleCodeList;

    /**
     * 基期计算类型
     */
    private Integer baseCalcType;
}
