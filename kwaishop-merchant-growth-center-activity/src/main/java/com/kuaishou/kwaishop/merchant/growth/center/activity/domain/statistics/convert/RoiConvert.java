package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert;

import java.util.List;
import java.util.stream.Collectors;

import com.kuaishou.infra.boot.context.ApplicationContextHolder;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiLevelAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.UserRoiStepAggregationInfoBO;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.BaseInfo;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerAwardData;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerIndicatorData;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerSingleDimensionData;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-25
 */
public class RoiConvert {

    /**
     * 阶梯数据转换
     */
    public static SellerSingleDimensionData buildStepDimensionData(BaseInfo layerBasicConfig, String ruleBizId,
            UserRoiStepAggregationInfoBO stepInfo, UserRoiLevelAggregationInfoBO levelInfo) {
        String subActivityName = levelInfo.getSubActivityName();
        int subActivityOrder = levelInfo.getSubActivityOrder();
        int levelOrder = levelInfo.getLevelOrder();
        long startTime = levelInfo.getPeriodStartTime();
        long endTime = levelInfo.getPeriodEndTime();
        // 指标
        List<SellerIndicatorData> sellerIndicatorDataList = stepInfo.getRoiIndicatorInfoList().stream()
                .map(RoiConvert::convertSellerIndicatorData).collect(Collectors.toList());
        // 奖励
        List<SellerAwardData> sellerAwardDataList = stepInfo.getRoiAwardInfoList().stream()
                .map(RoiConvert::convertSellerAwardData).collect(Collectors.toList());
        // 组装
        return SellerSingleDimensionData.newBuilder()
                .setBaseInfo(layerBasicConfig)
                .setRuleBizId(ruleBizId)
                .setSubActivityOrder(subActivityOrder)
                .setStepOrder(stepInfo.getStepOrder())
                .setSubActivityName(String.format("子活动「%s」", subActivityName))
                .setLevelName(String.format("分层%s", levelOrder))
                .setStepName(String.format("阶段%s", stepInfo.getStepOrder()))
                .addAllIndicatorInfo(sellerIndicatorDataList)
                .addAllAwardInfo(sellerAwardDataList)
                .setPeriodStartTime(startTime)
                .setPeriodEndTime(endTime)
                .build();
    }

    public static SellerIndicatorData convertSellerIndicatorData(RoiIndicatorInfoBO indicatorInfo) {
        return SellerIndicatorData.newBuilder()
                .setId(indicatorInfo.getIndicatorId())
                .setName(indicatorInfo.getIndicatorName())
                .setBaseValue(indicatorInfo.getBaseValue())
                .setTargetValue(indicatorInfo.getTargetValue())
                .setTag(indicatorInfo.getTag())
                .putAllExtraIndicatorMap(indicatorInfo.getExtraIndicatorMap())
                .build();
    }

    public static SellerAwardData convertSellerAwardData(RoiAwardInfoBO awardInfoBO) {
        return SellerAwardData.newBuilder()
                .setAwardType(awardInfoBO.getAwardType())
                .setAwardValue(awardInfoBO.getAwardValue())
                .build();
    }

    public static BaseInfo convertBaseInfo(BasicConfigBO basicConfigBO) {
        IndicatorBasicNewService indicatorBasicNewService =
                ApplicationContextHolder.getBean(IndicatorBasicNewService.class);
        BasicTimeRange basicTimeRange = indicatorBasicNewService.getBasicTimeRange(null, basicConfigBO);
        return BaseInfo.newBuilder()
                .setBaseStartTime(basicTimeRange.getEarliestStartTime())
                .setBaseEndTime(basicTimeRange.getLatestEndTime())
                .build();
    }

}
