package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.UserUnableDrawTaskReasonEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDrawTaskResult {
    private boolean enable;

    private UserUnableDrawTaskReasonEnum reasonEnum;

    private String taskOffsetEventType;

    private Long offsetEventHappenTime;

    private Long taskStartTime;

    private Long taskEndTime;

    private Long baseStartTime;

    private Long baseEndTime;
}
