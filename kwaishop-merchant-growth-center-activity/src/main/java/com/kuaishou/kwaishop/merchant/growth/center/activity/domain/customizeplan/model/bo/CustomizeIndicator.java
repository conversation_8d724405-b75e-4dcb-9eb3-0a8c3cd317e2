package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomizeIndicator {
    /**
     * 指标ID
     */
    private long indicatorId;
    /**
     * 是否展示进度
     */
    private boolean showProgress;
    /**
     * 完成进度
     */
    private CompleteProgressBO indicatorProgress;
    /**
     * 攻略
     */
    private String guideUrl;
    /**
     * 按钮
     */
    private ButtonInfo button;
    /**
     * 用户任务状态
     */
    private int userIndicatorStatus;

    /**
     * 指标单位
     */
    private String unit;
}
