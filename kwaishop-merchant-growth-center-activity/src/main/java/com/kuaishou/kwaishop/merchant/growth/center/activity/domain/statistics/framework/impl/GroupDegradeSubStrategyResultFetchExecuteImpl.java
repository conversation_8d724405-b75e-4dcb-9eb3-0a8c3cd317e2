package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.FIRST_VERSION;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationPerfEnum.PERIOD_ESTIMATION_AUTO_RUN_DEGRADE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.estimationCrowdPartitionSize;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.UserEstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.GroupEstimateDegradeConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyGroupAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategySnapshotEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationResultRecordAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.StrategyGroupAggregateRootRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationResultAggregateRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.service.UserEstimationStrategyDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.GroupEstimationDegradeBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-28
 */
@Service
@Slf4j
@Lazy
public class GroupDegradeSubStrategyResultFetchExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {


    @Autowired
    private EstimationCacheService estimationCacheService;

    @Autowired
    private UserEstimationStrategyReadService userEstimationStrategyReadService;

    @Autowired
    private StrategyGroupAggregateRootRepository strategyGroupAggregateRootRepository;

    @Autowired
    private UserEstimationResultAggregateRepository userEstimationResultAggregateRepository;

    @Autowired
    private UserEstimationStrategyDomainService userEstimationStrategyDomainService;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        log.info("[周期测算策略降级] 初始化人群和缓存 eventId {}", event.getEventId());
        GroupEstimationDegradeBatchExecuteEvent degradeBatchExecuteEvent =
                (GroupEstimationDegradeBatchExecuteEvent) event;
        String eventId = event.getEventId();
        try {
            EstimationStrategyGroupAggregateRoot groupAggregateRoot = degradeBatchExecuteEvent.getGroupAggregateRoot();
            if (groupAggregateRoot == null || groupAggregateRoot.getSpecifyVersionEntity() == null
                    || CollectionUtils.isEmpty(groupAggregateRoot.getStrategyEntityList())) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "测算组为空");
            }
            Set<Long> crowdUserIdSet =
                    userEstimationStrategyReadService.queryEstimationUserIdWhenEstimationProcess(groupAggregateRoot);
            if (CollectionUtils.isEmpty(crowdUserIdSet)) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "降级查询测算人群数据为空");
            }
            // 3 缓存测算总人数
            PerfUtil.perfSuccess(PERIOD_ESTIMATION_AUTO_RUN_DEGRADE, eventId, "initExecuteCrowdAndCache");
            return crowdUserIdSet;
        } catch (Exception e) {
            log.error("[周期测算策略降级] 初始化人群和缓存 eventId {} happen error", eventId, e);
            PerfUtil.perfException(PERIOD_ESTIMATION_AUTO_RUN_DEGRADE, eventId, "initExecuteCrowdAndCache");
            throw new RuntimeException(e);
        }
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        if (!EnvUtils.isProd()) {
            log.info("[周期测算策略降级] batchCustomizeExecute start eventId {}", eventId);
        }
        ExecuteHandleResult result = new ExecuteHandleResult();
        List<Long> successUserIdList = Lists.newArrayList();
        List<Long> failUserIdList = Lists.newArrayList();
        GroupEstimateDegradeConfig groupEstimateDegradeConfig = fromJSON(executeConfig,
                GroupEstimateDegradeConfig.class);
        if (groupEstimateDegradeConfig == null || !groupEstimateDegradeConfig.isValid()) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "测算组降级配置异常");
        }
        Long strategyId = groupEstimateDegradeConfig.getStrategyId();
        String strategyVersion = groupEstimateDegradeConfig.getStrategyVersion();
        EstimationStrategyGroupAggregateRoot groupAggregateRoot =
                strategyGroupAggregateRootRepository.querySpecifyVersionEntity(strategyId, strategyVersion, true);
        if (groupAggregateRoot == null || groupAggregateRoot.getSpecifyVersionEntity() == null
                || CollectionUtils.isEmpty(groupAggregateRoot.getStrategyEntityList())) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "测算组为空");
        }
        Long preSuccessStrategyId = groupEstimateDegradeConfig.getPreSuccessStrategyId();
        String preSuccessStrategyVersion = groupEstimateDegradeConfig.getPreSuccessStrategyVersion();
        Map<Long, Long> preSubStrategyIdMap = groupEstimateDegradeConfig.getPreSubStrategyIdMap();
        EstimationStrategyGroupAggregateRoot preSuccessGroupAggregateRoot =
                strategyGroupAggregateRootRepository.querySpecifyVersionEntity(preSuccessStrategyId,
                        preSuccessStrategyVersion, true);
        if (preSuccessGroupAggregateRoot == null || preSuccessGroupAggregateRoot.getSpecifyVersionEntity() == null
                || CollectionUtils.isEmpty(preSuccessGroupAggregateRoot.getStrategyEntityList())) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "前置成功测算组为空");
        }
        List<EstimationStrategyAggregateRoot> strategyEntityList = preSuccessGroupAggregateRoot.getStrategyEntityList();
        List<Long> preStrategyIds = strategyEntityList.stream()
                .map(EstimationStrategyAggregateRoot::getSpecifyVersionEntity)
                .filter(Objects::nonNull)
                .map(EstimationStrategySnapshotEntity::getStrategyId)
                .distinct()
                .collect(Collectors.toList());
        EstimationStrategyTypeEnum strategyType = groupEstimateDegradeConfig.getStrategyType();
        if (EstimationStrategyTypeEnum.GROUP.equals(strategyType)) {
            userIdList.forEach(userId -> {
                try {
                    List<UserEstimationResultRecordAggregateRoot> userResultList =
                            userEstimationResultAggregateRepository.queryUserEstimationResultRecords(userId,
                                    preStrategyIds, FIRST_VERSION, true);
                    // 没查到说明是开启了实时测算，用千人一面的数据下发
                    if (CollectionUtils.isEmpty(userResultList)) {
                        // todo:dzg 实时测算有可能存在历史测算结果，后续可以考虑如何下发千人一面数据
                        successUserIdList.add(userId);
                        return;
                    }
                    // 保存测试结果
                    userResultList
                            .forEach(preUserResult -> userEstimationStrategyDomainService.saveEstimationResultByHistoryResult(userId,
                                    preSubStrategyIdMap, preUserResult));
                    successUserIdList.add(userId);
                    // 更新缓存
                } catch (DataIntegrityViolationException ex) {
                    log.warn("[GroupDegradeSubStrategyResultFetchExecuteImpl] 重复消费！userId {} eventId {}", userId,
                            eventId);
                } catch (Exception e) {
                    log.error("测算人群拉取同步出现异常 userId {} eventId {}", userId, eventId, e);
                    PerfUtil.perfException(PERIOD_ESTIMATION_AUTO_RUN_DEGRADE, String.valueOf(userId),
                            "saveUserEstimationResult", e.getMessage());
                    failUserIdList.add(userId);
                }

            });
        } else {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "非策略组类型不支持");
        }
        // 缓存，用于后续判断是否完成全部保存
        estimationCacheService.batchAddUserToSuccessList(eventId, successUserIdList);
        result.setSuccessUserList(successUserIdList);
        result.setFailUserList(failUserIdList);
        return result;
    }

    @Override
    protected boolean filterSuccessUser() {
        return true;
    }

    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 获取执行配置必要信息携带
        GroupEstimationDegradeBatchExecuteEvent executeEvent = (GroupEstimationDegradeBatchExecuteEvent) event;
        // 获取人群&基期配置
        GroupEstimateDegradeConfig degradeConfig = new GroupEstimateDegradeConfig(executeEvent.getStrategyId(),
                executeEvent.getStrategyVersion(), executeEvent.getPreSuccessStrategyId(),
                executeEvent.getPreSuccessStrategyVersion(), EstimationStrategyTypeEnum.GROUP,
                executeEvent.getPreSubStrategyIdMap());
        return toJSON(degradeConfig);
    }

    @Override
    int getPartitionSize() {
        return estimationCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.GROUP_DEGRADE_SUB_STRATEGY_RESULT_FETCH;
    }
}
