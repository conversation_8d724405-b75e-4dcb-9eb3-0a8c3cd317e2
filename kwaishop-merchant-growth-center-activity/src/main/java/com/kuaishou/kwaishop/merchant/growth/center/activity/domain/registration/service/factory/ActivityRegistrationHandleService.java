package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory;

import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.custom.ManualDrawSignUpActivityRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-23
 */
@Lazy
@Service
public interface ActivityRegistrationHandleService {

    /**
     * 处理人工报名
     */
    void handleManualDrawSignUp(ManualDrawSignUpActivityRequest request);

    /**
     * 处理动态报名
     */
    boolean handleAddUserRegistration(long userId, long activityId, List<Long> parentTaskIds, String source,
            Map<Long, AddRegistrationOptionBO> optionMap);

    /**
     * 活动类型
     */
    ActivitySeriesType getActivitySeriesType();

    /**
     * 处理二次风控数据
     */
    void handleRiskInterruptRegistration(Long userId, Long activityId, String riskReason);
}
