package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model;

import java.math.BigDecimal;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums.BaseFormulaResultTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseFormulaResult {

    /**
     * 基期计算结果
     */
    private BigDecimal resultValue;

    /**
     * 基期计算结果类型
     */
    private BaseFormulaResultTypeEnum baseFormulaResultType;
}
