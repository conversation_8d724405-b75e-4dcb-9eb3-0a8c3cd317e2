package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyGroupAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EstimationGroupCrowdInfoBatchExecuteEvent extends BatchExecuteEvent {

    /**
     * 策略ID
     */
    private Long strategyId;

    private String strategyVersion;


    /**
     * 策略准备同步版本
     */
    private String prepareSyncVersion;

    private EstimationStrategyGroupAggregateRoot estimationStrategyGroupAggregateRoot;


    /**
     * 自定义基期数据上传
     */
    private Boolean customizeBasic;

    /**
     * 基期指标config
     */
    private Map<Long, IndicatorDO> baseIndicatorMap;
    /**
     * 指标id -> jsonKey信息
     */
    private Map<Long, String> jsonKeyMap;
}
