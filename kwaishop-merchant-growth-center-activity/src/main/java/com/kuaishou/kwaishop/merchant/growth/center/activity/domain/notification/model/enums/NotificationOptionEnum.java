package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-23
 */
public enum NotificationOptionEnum {
    UNKNOWN(0, "unknown"),
    TEMPLATE_CODE(1, "templateCode"),
    INTERVAL_DAY(2, "intervalDay"),
            ;
    private int val;
    private String name;

    NotificationOptionEnum(int val, String name) {
        this.val = val;
        this.name = name;
    }

    public static NotificationOptionEnum of(String name) {
        for (NotificationOptionEnum optionEnum : NotificationOptionEnum.values()) {
            if (optionEnum.getName().equals(name)) {
                return optionEnum;
            }
        }
        return UNKNOWN;
    }

    public String getName() {
        return name;
    }
}
