package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchAssemblerConstantBO.LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.launchUserIndicatorDataSourceAggSwitch;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.platform.UserStepIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchUserIndicatorDataSource implements LaunchDataSource {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private IndicatorRecordDAO indicatorRecordDAO;

    @Resource
    private IndicatorConfigDAO indicatorConfigDAO;

    @Autowired
    private IndicatorTaskService indicatorTaskService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Override
    @SuppressWarnings("unchecked")
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();
        Long userId = param.getUserId();
        Integer entityType = param.getEntityType();
        List<Long> entityIds = param.getEntityIds();

        List<IndicatorRecordDO> userIndicatorRecords = Lists.newArrayList();
        List<IndicatorConfigDO> indicatorConfigs = Lists.newArrayList();
        List<UserStepIndicatorInfoBO> userStepIndicatorInfoList = Lists.newArrayList();

        Map<String, Object> resMap = Maps.newHashMap();

        // 获取用户资格信息
        Map<String, Object> customizeParamMap = param.getCustomizeParamMap();
        List<UserRegistrationRecordBO> userRegistrationRecords = (List<UserRegistrationRecordBO>)
                MapUtils.getObject(customizeParamMap, LAUNCH_ASSEMBLER_CONTEXT_USER_REGISTRATION_RECORDS);

        // 资格为空直接返回
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            resMap.put(getDataSourceType().getType(), toJSON(LaunchUserIndicatorData.empty()));
            return LaunchDataSourceResultBO.builder().resMap(resMap).build();
        }

        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(entityType);
        switch (entityTypeEnum) {
            case SUB_ACTIVITY:
                // 根据子活动ID拉取任务配置
                List<TaskDO> taskList = launchResolveService
                        .resolveRegistrationTaskConfigBySubActivityId(activityId, entityIds, userRegistrationRecords);
                if (CollectionUtils.isEmpty(taskList)) {
                    break;
                }

                List<Long> taskIdList = taskList.stream().map(TaskDO::getId).collect(Collectors.toList());

                // 用户指标记录
                userIndicatorRecords = indicatorRecordDAO.listUserRecordOfTaskList(userId, activityId,
                        taskIdList, false);

                // 指标元数据
                indicatorConfigs = indicatorConfigDAO.queryTaskIndicatorConfigByIds(activityId, taskIdList);
                List<Long> indicatorIdList = indicatorConfigs.stream().map(IndicatorConfigDO::getIndicatorId)
                        .distinct().collect(Collectors.toList());
                Map<Long, IndicatorDO> indicatorMap =
                        indicatorLocalCacheService.batchQueryTaskIndicator(indicatorIdList);

                // 用户活动数据
                UserActivityRecordDO userActivityRecord =
                        userActivityRecordDAO.queryUserActivityRecord(userId, activityId, false);

                // 用户指标数据
                userStepIndicatorInfoList = indicatorTaskService.getUserIndicatorStepConfig(userId,
                        activityId, taskList, indicatorMap, userActivityRecord,
                        launchUserIndicatorDataSourceAggSwitch.get());

                break;
            default:
                userIndicatorRecords = indicatorRecordDAO.listUserRecordOfActivity(userId, activityId, false);
                indicatorConfigs = indicatorConfigDAO.queryActivityIndicatorConfig(activityId);
        }

        LaunchUserIndicatorData data = LaunchUserIndicatorData.builder()
                .indicatorConfigs(indicatorConfigs)
                .userIndicatorRecords(userIndicatorRecords)
                .userStepIndicatorInfoList(userStepIndicatorInfoList)
                .build();
        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.USER_INDICATOR_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchUserIndicatorData {

        /**
         * 指标配置列表
         */
        private List<IndicatorConfigDO> indicatorConfigs;

        /**
         * 用户指标记录列表
         */
        private List<IndicatorRecordDO> userIndicatorRecords;

        /**
         * 用户阶梯指标数据列表
         * 仅子活动实体类型
         */
        private List<UserStepIndicatorInfoBO> userStepIndicatorInfoList;

        public static LaunchUserIndicatorData empty() {
            return LaunchUserIndicatorData.builder()
                    .indicatorConfigs(Lists.newArrayList())
                    .userIndicatorRecords(Lists.newArrayList())
                    .userStepIndicatorInfoList(Lists.newArrayList())
                    .build();
        }
    }
}
