package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.LaunchFilter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model.LaunchFilterTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.helper.LaunchInfoDegreeHelper;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigGrayConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigSnapshotBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigAuditActionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigReleaseStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-28
 */
@Component
@Slf4j
public class DefaultLaunchDegreeFilter implements LaunchFilter<DefaultLaunchInfoFetchContext> {

    @Resource
    private LaunchResolveService launchResolveService;

    @Override
    public boolean match(DefaultLaunchInfoFetchContext context) {
        return true;
    }

    @Override
    public void filter(DefaultLaunchInfoFetchContext context) {
        List<Long> launchActivityIds = context.getLaunchActivityIds();
        if (CollectionUtils.isEmpty(launchActivityIds)) {
            return;
        }
        Long userId = context.getUserId();
        String scene = context.getScene();

        // 存量放量逻辑
        launchActivityIds = launchActivityIds.stream().filter(activityId ->
                        !LaunchInfoDegreeHelper.degree(scene, String.valueOf(activityId), userId))
                .collect(Collectors.toList());

        context.setLaunchActivityIds(launchActivityIds);

        // 增量灰度逻辑
        Map<Long, List<LaunchConfigBO>> launchConfigActivityMap = context.getLaunchConfigActivityMap();
        Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap = Maps.newHashMap();
        launchActivityIds.forEach(activityId -> {
            List<LaunchConfigBO> launchConfigs = launchConfigActivityMap.get(activityId);
            if (CollectionUtils.isEmpty(launchConfigs)) {
                return;
            }

            List<LaunchConfigBO> filteredConfigs = Lists.newArrayList();
            launchConfigs.forEach(launchConfig -> {
                Boolean releaseStatusEnableFlag = launchResolveService.resolveReleaseStatusEnableFlag(launchConfig);
                if (releaseStatusEnableFlag) {
                    processWithFlag(context, userId, filteredConfigs, launchConfig);
                } else {
                    processWithoutFlag(context, userId, filteredConfigs, launchConfig);
                }
            });

            filteredLaunchConfigActivityMap.put(activityId, filteredConfigs);
        });
        context.setLaunchConfigActivityMap(filteredLaunchConfigActivityMap);
        context.setLaunchActivityIds(new ArrayList<>(filteredLaunchConfigActivityMap.keySet()));

        if (!EnvUtils.isProd()) {
            Map<Long, List<Long>> activityLaunchConfigIdMap = Maps.newHashMap();
            MapUtils.emptyIfNull(filteredLaunchConfigActivityMap).forEach((activityId, launchConfigs) -> {
                if (CollectionUtils.isEmpty(launchConfigs)) {
                    return;
                }
                activityLaunchConfigIdMap.put(activityId, launchConfigs.stream().map(LaunchConfigBO::getId)
                        .collect(Collectors.toList()));
            });
            log.info("[投放过滤器] 降级过滤完成 userId:{}, scene:{}, activityLaunchConfigIdMap:{}",
                    userId, context.getScene(), toJSON(MapUtils.emptyIfNull(activityLaunchConfigIdMap)));
        }
    }

    private void processWithFlag(DefaultLaunchInfoFetchContext context, Long userId,
            List<LaunchConfigBO> filteredConfigs, LaunchConfigBO launchConfig) {
        LaunchConfigStatusEnum status = launchConfig.getStatus();
        LaunchConfigReleaseStatusEnum releaseStatus = launchConfig.getReleaseStatus();
        switch (status) {
            case ENABLE:
                switch (releaseStatus) {
                    case GRAYING:
                        // 灰度状态特殊处理
                        processGraying(context, userId, filteredConfigs, launchConfig, true);
                        break;
                    default:
                        // 其他状态添加
                        filteredConfigs.add(launchConfig);
                }
                break;
            case DISABLE:
                switch (releaseStatus) {
                    case GRAYING:
                        // 灰度状态特殊处理
                        processGraying(context, userId, filteredConfigs, launchConfig, true);
                        break;
                    default:
                        // 其他状态过滤
                }
                break;
            default:
                log.error("[投放降级过滤器] 不支持的投放配置状态 context:{}, launchConfig:{}",
                        toJSON(context), toJSON(launchConfig));
        }
    }

    /**
     * 增量灰度逻辑
     * <p>
     * 1. 生效 -> 直接加入
     * 2. 失效 -> 直接过滤
     * <p>
     * 3. 灰度中
     * 3.1 命中灰度逻辑 -> 取最新的快照
     * 3.2 未命中灰度逻辑
     * - 不是新增配置 -> 取目前的快照，根据ext中的之前状态判断是否展示
     * - 新增配置 -> 直接过滤
     * <p>
     * 4. 审批中
     * 4.1 不是新增配置 -> 取目前的快照，根据ext中的之前状态判断是否展示
     * 4.2 新增配置 -> 直接过滤
     * <p>
     * 5. 审批失败
     * 5.1 不是新增配置 -> 取目前的快照，根据ext中之前的状态判断是否展示
     * 5.2 新增配置 -> 直接过滤
     */
    private void processWithoutFlag(DefaultLaunchInfoFetchContext context, Long userId,
            List<LaunchConfigBO> filteredConfigs, LaunchConfigBO launchConfig) {
        LaunchConfigStatusEnum status = launchConfig.getStatus();
        switch (status) {
            case UNKNOWN:
            case DISABLE:
                // 未知｜失效 过滤
                break;
            case ENABLE:
                // 生效加入
                filteredConfigs.add(launchConfig);
                break;
            case GRAYING:
                processGraying(context, userId, filteredConfigs, launchConfig, false);
                break;
            case AUDITING:
            case AUDIT_REJECT:
                // 审批中｜审批失败 解析快照配置
                LaunchConfigSnapshotBO auditingSnapshot = launchResolveService.resolveSnapshot(launchConfig);
                if (auditingSnapshot == null) {
                    log.error("[投放降级过滤器] 快照投放配置异常 context:{}, launchConfig:{}",
                            toJSON(context), toJSON(launchConfig));
                    break;
                }
                // 如果没有命中灰度，根据动作状态和前置状态判断
                filterByActionAndStatus(filteredConfigs, launchConfig, auditingSnapshot);
                break;
            default:
                //
        }
    }

    private void processGraying(DefaultLaunchInfoFetchContext context, Long userId,
            List<LaunchConfigBO> filteredConfigs, LaunchConfigBO launchConfig, Boolean withFlag) {
        // 解析灰度配置
        LaunchConfigGrayConfigBO grayConfig = launchResolveService.resolveGrayConfig(launchConfig);
        if (grayConfig == null || CollectionUtils.isEmpty(grayConfig.getGrayUserIds())) {
            log.error("[投放降级过滤器] 灰度投放配置异常 context:{}, launchConfig:{}",
                    toJSON(context), toJSON(launchConfig));
            return;
        }

        // 解析快照配置
        LaunchConfigSnapshotBO graySnapshot = launchResolveService.resolveSnapshot(launchConfig);
        if (graySnapshot == null) {
            log.error("[投放降级过滤器] 快照投放配置异常 context:{}, launchConfig:{}",
                    toJSON(context), toJSON(launchConfig));
            return;
        }

        // 判断是否命中灰度
        if (!grayConfig.getGrayUserIds().contains(userId)) {
            processNonHitGraying(filteredConfigs, launchConfig, withFlag, graySnapshot);
        } else {
            processHitGraying(filteredConfigs, graySnapshot);
        }
    }

    private static void processNonHitGraying(List<LaunchConfigBO> filteredConfigs,
            LaunchConfigBO launchConfig, Boolean withFlag, LaunchConfigSnapshotBO graySnapshot) {
        if (!withFlag) {
            // 历史兼容逻辑：如果没有命中灰度，根据动作状态和前置状态判断
            filterByActionAndStatus(filteredConfigs, launchConfig, graySnapshot);
        } else {
            // 状态机区分逻辑
            if (!Objects.equals(launchConfig.getStatus(), LaunchConfigStatusEnum.ENABLE)) {
                return;
            }
            filteredConfigs.add(launchConfig);
        }
    }

    private static void processHitGraying(List<LaunchConfigBO> filteredConfigs, LaunchConfigSnapshotBO graySnapshot) {
        // 如果命中灰度，取最新的快照
        LaunchConfigBO latestLaunchConfig = graySnapshot.buildFromSnapshot();
        if (latestLaunchConfig == null) {
            return;
        }
        // 如果最新快照不是有效，过滤
        if (!Objects.equals(latestLaunchConfig.getStatus(), LaunchConfigStatusEnum.ENABLE)) {
            return;
        }
        filteredConfigs.add(latestLaunchConfig);
    }

    private static void filterByActionAndStatus(List<LaunchConfigBO> filteredConfigs, LaunchConfigBO launchConfig,
            LaunchConfigSnapshotBO graySnapshot) {
        LaunchConfigAuditActionEnum action =
                LaunchConfigAuditActionEnum.getByCode(graySnapshot.getAction());
        // 如果动作为新增，则过滤
        if (Objects.equals(action, LaunchConfigAuditActionEnum.ADD)) {
            return;
        }

        // 如果动作不为新增，则根据之前的状态判断是否过滤
        LaunchConfigStatusEnum previousStatus =
                LaunchConfigStatusEnum.getByStatus(graySnapshot.getPreviousStatus());
        if (!Objects.equals(previousStatus, LaunchConfigStatusEnum.ENABLE)) {
            return;
        }
        filteredConfigs.add(launchConfig);
    }

    @Override
    public LaunchFilterTypeEnum getType() {
        return LaunchFilterTypeEnum.DEGREE_FILTER;
    }

    @Override
    public int order() {
        return 0;
    }
}
