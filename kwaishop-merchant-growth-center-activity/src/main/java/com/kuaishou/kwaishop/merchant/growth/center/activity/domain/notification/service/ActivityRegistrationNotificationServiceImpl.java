package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service;

import static com.kuaishou.framework.util.HostInfo.debugHostOrStaging;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.ACTIVITY_REGISTRATION_NOTIFICATION_SERVICE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.userActivityRegisterStatusNotificationPushWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.ActivityRegistrationNotificationArgsBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.ActivityRegistrationNotificationBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushCreateBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.sync.AbstractMultipleSync;

import lombok.extern.slf4j.Slf4j;

/**
 * 报名后触发领取活动消息推送
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-17
 */
@Lazy
@Slf4j
@Service
public class ActivityRegistrationNotificationServiceImpl
        extends AbstractMultipleSync<UserRegistrationRecordDO, ActivityRegistrationNotificationBO> {

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private NotificationPushCreateService notificationPushCreateService;

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;


    @Override
    public int shardCount() {
        if (debugHostOrStaging()) {
            return 1;
        }
        return 1000;
    }

    @Override
    public int shardId(ActivityRegistrationNotificationBO activityRegistrationNotificationBO) {
        return 0;
    }

    /**
     * 拉取所有报名记录
     */
    @Override
    public Stream<UserRegistrationRecordDO> fetch(int shardId) {
        ActivityRegistrationNotificationArgsBO argsBO = (ActivityRegistrationNotificationArgsBO) getCustomParam();
        long activityId = argsBO.getActivityId();
        return userRegistrationService.cursorGetRegistrationRecord(shardId, activityId);
    }


    /**
     * 查看是否有对应的推送配置
     */
    @Override
    public ActivityRegistrationNotificationBO transform(UserRegistrationRecordDO userRegistrationRecordDO) {
        boolean flag = true;
        NotificationPushCreateBO notificationCreateBO = NotificationPushCreateBO.builder()
                .userId(userRegistrationRecordDO.getUserId())
                .entityId(userRegistrationRecordDO.getActivityId())
                .entityType(NotificationEntityTypeEnum.ACTIVITY.getVal())
                .entityStatus(UserActivityEventTypeEnum.REGISTRATION.getValue())
                .eventTime(System.currentTimeMillis())
                .build();
        // 灰度放量
        if (!userActivityRegisterStatusNotificationPushWhiteList.get().isOnFor(userRegistrationRecordDO.getUserId())) {
            flag = false;
        }
        // 校验记录状态为有资格
        if (!UserRegistrationStatusEnum.VALID.getCode().equals(userRegistrationRecordDO.getStatus())) {
            flag = false;
        }

        // 人群包过滤
        ActivityRegistrationNotificationArgsBO argsBO = (ActivityRegistrationNotificationArgsBO) getCustomParam();
        if (argsBO.getCrowdGroupId() > 0 && !notificationPushFetchService
                .judgeInCrowdPackage(userRegistrationRecordDO.getActivityId(), userRegistrationRecordDO.getUserId(),
                        argsBO.getCrowdGroupId())) {
            flag = false;
        }

        return ActivityRegistrationNotificationBO.builder().notificationPushCreateBO(notificationCreateBO)
                .flag(flag)
                .build();
    }

    @Override
    public boolean insert(long shardId, ActivityRegistrationNotificationBO activityRegistrationNotificationBO) {
        try {
            if (!activityRegistrationNotificationBO.isFlag()) {
                perfFail(ACTIVITY_REGISTRATION_NOTIFICATION_SERVICE, "registration push");
                return false;
            }
            NotificationPushCreateBO notificationPushCreateBO =
                    activityRegistrationNotificationBO.getNotificationPushCreateBO();
            notificationPushCreateService.notificationPushCreate(notificationPushCreateBO);
            perfSuccess(ACTIVITY_REGISTRATION_NOTIFICATION_SERVICE, "registration push");
            return true;
        } catch (Exception e) {
            log.error("[ActivityRegistrationNotificationServiceImpl] error, notificationPushCreateBO:{}",
                    ObjectMapperUtils.toJSON(activityRegistrationNotificationBO), e);
            perfException(ACTIVITY_REGISTRATION_NOTIFICATION_SERVICE, e.getClass().getSimpleName(),
                    e.getMessage());
            return false;
        }
    }

    @Override
    public String getBizName() {
        return "ActivityRegistrationTriggerNotificationService";
    }
}
