package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import javax.validation.constraints.NotNull;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户报名记录BO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationRecordBO {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空!")
    private Long userId;
    /**
     * 活动ID
     */
    @NotNull(message = "活动ID不能为空!")
    private Long activityId;
    /**
     * 实体类型
     */
    @NotNull(message = "实体类型不能为空!")
    private EntityTypeEnum entityType;
    /**
     * 实体ID
     */
    @NotNull(message = "实体ID不能为空!")
    private Long entityId;
    /**
     * 报名状态
     */
    private Integer status;
    /**
     * 报名时间
     */
    private Long registrationTime;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 报名来源
     */
    private String source;
    /**
     * 扩展数据
     */
    private String ext;
    /**
     * 用户基础数据
     */
    private String jsonData;

}
