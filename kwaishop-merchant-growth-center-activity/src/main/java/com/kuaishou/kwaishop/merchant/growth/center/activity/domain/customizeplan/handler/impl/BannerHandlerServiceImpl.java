package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler.impl;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanBasicInfoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.CustomizePlanSceneEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Service
@Slf4j
public class BannerHandlerServiceImpl extends AbstractCustomizePlanHandlerService {
    @Override
    public CustomizePlanSceneEnum getScene() {
        return CustomizePlanSceneEnum.BANNER;
    }

    @Override
    protected CustomizePlan constructCustomizePlan(long userId, CustomizePlanConfig customizePlanConfig) {
        CustomizePlan customizePlan = new CustomizePlan();
        customizePlan.setPriority(customizePlanConfig.getPriority());
        CustomizePlanBasicInfoConfig basicInfo = customizePlanConfig.getBasicInfo();
        customizePlan.setBannerPC(basicInfo.getTaskCenterCardBannerPC());
        customizePlan.setBannerAPP(basicInfo.getTaskCenterCardBannerAPP());
        customizePlan.setPlanCode(customizePlanConfig.getPlanCode());
        customizePlan.setPriority(customizePlanConfig.getPriority());
        return customizePlan;
    }
}
