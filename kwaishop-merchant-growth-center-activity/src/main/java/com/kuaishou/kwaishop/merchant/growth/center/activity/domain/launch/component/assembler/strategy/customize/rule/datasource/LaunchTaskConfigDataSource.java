package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchTaskConfigDataSource implements LaunchDataSource {

    @Resource
    private TaskLocalCacheService taskLocalCacheService;

    @Resource
    private LaunchResolveService launchResolveService;

    @Override
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();

        List<TaskDO> allTasks = Lists.newArrayList();

        // 根据实体类型过滤
        Integer entityType = param.getEntityType();
        List<Long> entityIds = param.getEntityIds();
        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(entityType);
        switch (entityTypeEnum) {
            case SUB_ACTIVITY:
                allTasks.addAll(ListUtils.emptyIfNull(
                        launchResolveService.resolveTaskConfigBySubActivityId(activityId, entityIds)));
                break;
            default:
                allTasks = ListUtils.emptyIfNull(taskLocalCacheService.getTaskListByActivityId(activityId));
                break;
        }

        LaunchTaskConfigData data = LaunchTaskConfigData.builder().taskConfigs(allTasks).build();

        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.TASK_CONFIG_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchTaskConfigData {

        private List<TaskDO> taskConfigs;
    }
}
