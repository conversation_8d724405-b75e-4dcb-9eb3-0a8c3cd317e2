package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler.impl;

import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.reco.platform.sdk.index.photoMap.ObjectMapperUtils.toJSON;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler.CustomizePlanHandlerService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.AdmissionRuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanConstructorConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.rule.CustomizePlanAdmissionRuleService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.supplier.CustomizePlanConstructor;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.gray.GrayControlUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Slf4j
public abstract class AbstractCustomizePlanHandlerService implements CustomizePlanHandlerService {

    @Qualifier("defaultAdmissionRuleService")
    @Autowired
    private CustomizePlanAdmissionRuleService defaultAdmissionRuleService;

    @Autowired
    private List<CustomizePlanAdmissionRuleService> admissionRuleServiceList;

    @Autowired
    private List<CustomizePlanConstructor> customizePlanConstructorList;

    @Override
    public boolean filter(long userId, CustomizePlanConfig customizePlanConfig) {
        // 判断定制计划配置是否降级或用户不在定制计划配置放量名单中
        return hitUpgradeSwitch(customizePlanConfig) || notHitUserGrayLogic(userId, customizePlanConfig);
    }

    @Override
    public boolean hitAdmissionRule(long userId, AdmissionRuleConfig admissionRuleConfig) {
        CustomizePlanAdmissionRuleService admissionRuleService = getAdmissionRuleService(admissionRuleConfig.getType());
        if (admissionRuleService == null) {
            log.error("[查询定制计划] 未找到对应准入处理器, userId:{}, admissionRuleConfig:{}", userId, toJSON(admissionRuleConfig));
            throw new BizException(SERVER_ERROR, "定制计划未找到对应准入处理器");
        }
        return admissionRuleService.hitAdmissionRule(userId, admissionRuleConfig);
    }

    @Override
    public CustomizePlan buildCustomizePlan(long userId, CustomizePlanConfig customizePlanConfig) {
        // 定制计划配置校验
        checkCustomizePlanConfig(customizePlanConfig);
        if (filter(userId, customizePlanConfig)) {
            log.info("[查询定制计划] 定制计划配置降级或用户未命中定制计划放量白名单, userId:{}, customizePlanConfig:{}", userId, customizePlanConfig);
            return null;
        }
        if (!hitAdmissionRule(userId, customizePlanConfig.getAdmissionRuleConfig())) {
            return null;
        }
        return constructCustomizePlan(userId, customizePlanConfig);
    }

    protected abstract CustomizePlan constructCustomizePlan(long userId, CustomizePlanConfig customizePlanConfig);

    /**
     * 校验定制计划配置
     */
    protected void checkCustomizePlanConfig(CustomizePlanConfig customizePlanConfig) {
        if (Objects.isNull(customizePlanConfig)) {
            log.error("[查询定制计划] 定制计划配置为空");
            throw new BizException(SERVER_ERROR, "定制计划配置为空");
        }
        AdmissionRuleConfig admissionRuleConfig = customizePlanConfig.getAdmissionRuleConfig();
        if (Objects.isNull(admissionRuleConfig)) {
            log.error("[查询定制计划] 定制计划准入规则配置为空, customizePlanConfig:{}", toJSON(customizePlanConfig));
            throw new BizException(SERVER_ERROR, "定制计划准入规则配置为空");
        }
        String type = admissionRuleConfig.getType();
        if (StringUtils.isBlank(type)) {
            log.error("[查询定制计划] 定制计划准入规则类型未配置, customizePlanConfig:{}", toJSON(customizePlanConfig));
            throw new BizException(SERVER_ERROR, "定制计划准入规则为空");
        }
        CustomizePlanConstructorConfig customizePlanConstructor = customizePlanConfig.getCustomizePlanConstructor();
        if (Objects.isNull(customizePlanConstructor)) {
            log.error("[查询定制计划] 定制计划构造器配置为空, customizePlanConfig:{}", toJSON(customizePlanConfig));
            throw new BizException(SERVER_ERROR, "定制计划构造器配置为空");
        }
        Map<String, Object> configMap = customizePlanConstructor.getConfigMap();
        if (MapUtils.isEmpty(configMap)) {
            log.error("[查询定制计划] 定制计划构造器参数配置为空, customizePlanConstructor:{}", toJSON(customizePlanConstructor));
            throw new BizException(SERVER_ERROR, "定制计划构造器参数为空");
        }
        if (Objects.isNull(customizePlanConfig.getBasicInfo())) {
            log.error("[查询定制计划] 定制计划基础信息为空, customizeConfig:{}", toJSON(customizePlanConfig));
            throw new BizException(SERVER_ERROR, "定制计划基础信息为空");
        }
    }

    /**
     * 降级开关是否开启
     */
    protected boolean hitUpgradeSwitch(CustomizePlanConfig customizePlanConfig) {
        Boolean upgradeSwitch = customizePlanConfig.getUpgradeSwitch();
        return BooleanUtils.isTrue(upgradeSwitch);
    }

    /**
     * 用户是否不在放量名单中
     */
    protected boolean notHitUserGrayLogic(long userId, CustomizePlanConfig customizePlanConfig) {
        if (userId <= 0) {
            log.error("[查询定制计划] 用户ID无效，userId: {}", userId);
            return true;
        }
        String grayTailNumber = customizePlanConfig.getGrayTailNumber();
        //未在灰度名单中直接返回true
        return StringUtils.isNotBlank(grayTailNumber) && !GrayControlUtils.isOnFor(userId, grayTailNumber);
    }

    /**
     * 获取对应准入规则执行器
     */
    protected CustomizePlanAdmissionRuleService getAdmissionRuleService(String ruleType) {
        return admissionRuleServiceList.stream()
                .filter(admissionRule -> StringUtils.equals(admissionRule.getAdmissionRule().getRuleType(), ruleType))
                .findFirst().orElse(null);
    }

    /**
     * 获取定制计划对应构造器
     */
    protected CustomizePlanConstructor getCustomizePlanConstructor(String constructorCode) {
        return customizePlanConstructorList.stream()
                .filter(constructor -> StringUtils.equals(constructor.getConstructorCode().getConstructorCode(), constructorCode))
                .findFirst().orElse(null);
    }
}
