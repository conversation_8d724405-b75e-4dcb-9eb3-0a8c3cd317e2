package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <z<PERSON><PERSON><PERSON>@kuaishou.com>
 * Created on 2022-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Less50WRegistrationConfigBO {
    /**
     * 人群包映射 Map<父任务别名,人群圈选id>
     */
    private Map<String, Long> crowdMap;
}
