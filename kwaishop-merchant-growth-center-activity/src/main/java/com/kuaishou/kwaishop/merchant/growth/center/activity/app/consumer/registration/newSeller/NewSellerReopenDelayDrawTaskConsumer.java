package com.kuaishou.kwaishop.merchant.growth.center.activity.app.consumer.registration.newSeller;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.MQ_NEW_SELLER_REOPEN_DELAY_DRAW_TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.mq.ActivityConsumerGroup.c_kwaishop_merchant_new_seller_reopen_task_draw_delay_topic;
import static com.kuaishou.kwaishop.merchant.growth.center.client.config.mq.KwaishopMerchantGrowthMqTopic.kwaishop_merchant_new_seller_reopen_task_draw_delay_topic;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.infra.framework.mq.ConsumeContext;
import com.kuaishou.infra.framework.mq.ConsumeResult;
import com.kuaishou.infra.framework.mq.MqConsumer;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.service.UserActivityOperateService;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.ShopRedrawMsg;

import kuaishou.common.BizDef;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-08-08
 */
@Lazy
@Slf4j
@Service
public class NewSellerReopenDelayDrawTaskConsumer implements MqConsumer {
    @Resource
    private UserActivityOperateService userActivityOperateService;

    @NotNull
    @Override
    public String consumerGroup() {
        return c_kwaishop_merchant_new_seller_reopen_task_draw_delay_topic.name();
    }

    @NotNull
    @Override
    public String getLogicTopic() {
        return kwaishop_merchant_new_seller_reopen_task_draw_delay_topic.name();
    }

    @NotNull
    @Override
    public BizDef bizDef() {
        return BizDef.KWAISHOP_MERCHANT_GROWTH;
    }

    @NotNull
    @Override
    public String appKey() {
        return "1419b14a28544e7db60de28d22135eef";
    }

    @Override
    public ConsumeResult onMessage(MqMessage message, ConsumeContext context) {
        ShopRedrawMsg shopRedrawMsg = null;
        try {
            shopRedrawMsg = ShopRedrawMsg.parseFrom(message.getData());
            log.info("[关店重开延迟领取任务消息] 收到消息, msg:{}", toJSON(shopRedrawMsg));
            userActivityOperateService.drawUserActivityByTag(shopRedrawMsg.getUserId(), shopRedrawMsg.getTag(), true,
                    "system");
            log.info("[关店重开延迟领取任务消息] 处理成功, msg:{}", toJSON(shopRedrawMsg));
            perfSuccess(MQ_NEW_SELLER_REOPEN_DELAY_DRAW_TASK, "delayDraw");
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("[关店重开延迟领取任务消息] 处理失败, msg:{}", toJSON(shopRedrawMsg), e);
            perfException(MQ_NEW_SELLER_REOPEN_DELAY_DRAW_TASK, e.getMessage());
            return ConsumeResult.LATER;
        }
    }
}
