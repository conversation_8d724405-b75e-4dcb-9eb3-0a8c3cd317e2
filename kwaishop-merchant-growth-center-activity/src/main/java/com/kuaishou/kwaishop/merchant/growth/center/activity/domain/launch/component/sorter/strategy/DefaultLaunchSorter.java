package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.sorter.strategy;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.helper.LaunchInfoFetchHelper.doSortLaunchActivityIds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType.SORTER_DEFAULT_STRATEGY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCommonConfig;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.UserActivityRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.sorter.LaunchSorter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.DefaultLaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.converter.ActivityTaskRecordConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.UserActivityShowBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityQueryService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Component
@Slf4j
public class DefaultLaunchSorter implements LaunchSorter<DefaultLaunchInfoFetchContext, DefaultLaunchInfoResult> {

    @Resource
    private UserActivityQueryService userActivityQueryService;

    @Override
    public void sort(DefaultLaunchInfoFetchContext context) {
        doSort(context);
    }

    private void doSort(DefaultLaunchInfoFetchContext context) {
        Long userId = context.getUserId();
        List<Long> launchActivityIds = context.getLaunchActivityIds();
        Map<Long, ActivityDO> allActivityMetaMap = context.getAllActivityMetaMap();

        List<UserActivityRecordDO> allUserActivityRecords = context.getAllUserActivityRecords();
        if (userId == null || CollectionUtils.isEmpty(launchActivityIds) || MapUtils.isEmpty(allActivityMetaMap)) {
            interruptAndReturn(context);
            return;
        }

        List<UserActivityRecordBO> allUserActivityRecordBOList = allUserActivityRecords
                .stream().map(ActivityTaskRecordConverter::convertUserActivityRecordBO).collect(Collectors.toList());

        Map<Long, UserActivityShowBO> userActivityShowInfoMap = userActivityQueryService
                .getUserActivityShowInfo(userId, launchActivityIds, allActivityMetaMap, allUserActivityRecordBOList);

        List<Long> sortedLaunchActivityIds = doSortLaunchActivityIds(launchActivityIds, userActivityShowInfoMap);
        sortedLaunchActivityIds = processManualSort(context.getScene(), sortedLaunchActivityIds);
        context.setLaunchActivityIds(sortedLaunchActivityIds);
    }

    private List<Long> processManualSort(String scene, List<Long> sortedLaunchActivityIds) {
        LaunchCommonConfigBO config = launchCommonConfig.getObject();
        if (config == null || MapUtils.isEmpty(config.getSceneActivityManualSortConfig())) {
            return sortedLaunchActivityIds;
        }
        List<Long> sceneManualSortActivityIds = config.getSceneActivityManualSortConfig().get(scene);
        if (CollectionUtils.isEmpty(sceneManualSortActivityIds)) {
            return sortedLaunchActivityIds;
        }

        List<Long> result = Lists.newArrayList();

        // 遍历手动排序活动列表，对于上下文中存在的活动，加入 result
        sceneManualSortActivityIds.stream().filter(sortedLaunchActivityIds::contains).forEach(result::add);
        // 遍历上下文中存在的活动，对于手动排序活动列表中不存在的（去重），加入result
        sortedLaunchActivityIds.stream().filter(activityId -> !sceneManualSortActivityIds.contains(activityId))
                .forEach(result::add);

        if (!EnvUtils.isProd()) {
            log.info("[投放活动排序] scene:{}, sortedLaunchActivityIds:{}, sceneManualSortActivityIds:{}, result:{}",
                    toJSON(scene), toJSON(sortedLaunchActivityIds), toJSON(sceneManualSortActivityIds), toJSON(result));
        }
        // 兜底去重
        return result.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public LaunchPipeHandlerType getType() {
        return SORTER_DEFAULT_STRATEGY;
    }
}
