package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.impl;

import static com.kuaishou.framework.util.HostInfo.debugHostOrStaging;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.ActivityIndustryConverter.convertPeriodConfigBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.isLiteActivity;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.parseIndicatorStepCount;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.NOTIFICATION_PUSH_CREATE_SERVICE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_SEND_DELAY_MSG_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.TASK_SHOW_PROGRESS_CALC_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.TRIGGER_NOTIFICATION_CONFIG_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.needAuditChannelList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.sendDelayNotificationExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.notificationAdminPermission;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.sendDelayNotificationMsgWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils.normalFormatTimeStamp;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgBuilder;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.TaskTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.service.impl.ActivityNotificationServiceImpl;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.GetNotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushCreateBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.DeleteEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationOccasionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushConfigCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushCreateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationCodeStrategyFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationCodeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ActivityRedisUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.notification.NotificationPushConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.notification.NotificationPushConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.DelayNotificationPushMsg;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
@Lazy
@Slf4j
@Service
public class NotificationPushCreateServiceImpl implements NotificationPushCreateService {

    @Autowired
    private NotificationPushConfigCacheService notificationPushConfigCacheService;

    @Autowired
    private NotificationPushBasicService notificationPushBasicService;

    @Autowired
    @Qualifier("delayNotificationPushProducer")
    private MsgProducer delayNotificationPushProducer;

    @Autowired
    private ActivityNotificationServiceImpl activityNotificationService;

    @Autowired
    private AdminKimService adminKimService;

    @Autowired
    private NotificationCodeStrategyFactory notificationCodeStrategyFactory;

    @Autowired
    private NotificationPushConfigDAO notificationPushConfigDAO;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Override
    public void notificationPushCreate(NotificationPushCreateBO notificationPushCreateBO) {
        // userId校验
        long userId = notificationPushCreateBO.getUserId();
        long eventTime = notificationPushCreateBO.getEventTime();

        if (userId <= 0) {
            log.error("[NotificationPushCreateService] userId invalid, notification:{}",
                    ObjectMapperUtils.toJSON(notificationPushCreateBO));
            perfFail(NOTIFICATION_PUSH_CREATE_SERVICE, "userId.invalid");
            return;
        }
        // 实体类型校验
        if (!NotificationEntityTypeEnum.getAllNotificationValidEntityTypes()
                .contains(notificationPushCreateBO.getEntityType())) {
            log.error("[NotificationPushCreateService] entityType not exists, notification:{}",
                    ObjectMapperUtils.toJSON(notificationPushCreateBO));
            perfFail(NOTIFICATION_PUSH_CREATE_SERVICE, "entityType.invalid");
            return;
        }

        // 获取推送配置
        GetNotificationPushConfigBO getConfigBO = assembleGetNotificationPushConfigBO(notificationPushCreateBO);
        List<NotificationPushConfigBO> notificationPushConfigs =
                notificationPushConfigCacheService.getNotificationPushConfigByIdxLoadingCache(getConfigBO);

        doPushCreate(userId, notificationPushConfigs, eventTime);
    }

    @Override
    public void notificationPushCreate(long userId, List<NotificationPushConfigBO> configs, long eventTime) {
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        doPushCreate(userId, configs, eventTime);
    }

    private void doPushCreate(long userId, List<NotificationPushConfigBO> notificationPushConfigs, long eventTime) {
        if (CollectionUtils.isEmpty(notificationPushConfigs)) {
            // 没有对应推送配置
            return;
        }
        // 过滤无效配置
        notificationPushConfigs = notificationPushConfigs.stream()
                .filter(config -> config.getStatus() == NotificationStatusEnum.VALID.getVal()
                        && config.getDeleted() == DeleteEnum.NORMAL.getVal())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(notificationPushConfigs)) {
            // 没有对应推送配置
            log.info("[NotificationPushCreateService] 没有对应推送配置, userId:{}, notificationPushConfigs:{}, "
                    + "eventTime:{}", userId, toJSON(notificationPushConfigs), eventTime);
            return;
        }

        if (debugHostOrStaging()) {
            log.info("[NotificationPushCreateService] {} 推送配置:{}", userId, notificationPushConfigs);
        }

        for (NotificationPushConfigBO config : notificationPushConfigs) {
            // 判断渠道是否需要推送延迟消息
            NotificationChannelEnum channel = NotificationChannelEnum.of(config.getChannel());
            if (!channel.getWithNotification()) {
                log.info("[NotificationPushCreateService] 渠道不需要推送延迟消息, userId:{}, config:{}, "
                        + "eventTime:{}", userId, toJSON(config), eventTime);
                return;
            }

            // 发送延迟推送消息
            sendDelayNotification(userId, config, eventTime);
        }
    }

    private GetNotificationPushConfigBO assembleGetNotificationPushConfigBO(
            NotificationPushCreateBO notificationPushCreateBO) {
        return GetNotificationPushConfigBO.builder()
                .entityId(notificationPushCreateBO.getEntityId())
                .entityStatus(notificationPushCreateBO.getEntityStatus())
                .entityType(notificationPushCreateBO.getEntityType())
                .build();
    }

    /**
     * 触发推送配置
     */
    @Override
    public void triggerNotificationConfig(long userId, long configId, long eventTime) {
        // 获取配置
        NotificationPushConfigBO configBO = notificationPushConfigCacheService
                .getNotificationPushConfigByIdLoadingCache(configId);
        if (configBO == null) {
            log.error("[NotificationPushCreateService] configBO is null, configId:{}", configId);
            throw new BizException(TRIGGER_NOTIFICATION_CONFIG_ERROR, "推送配置获取异常");
        }

        // 解锁
        String redisKey = NotificationRedisKeyEnum.SEND_DELAY_NOTIFICATION_PUSH
                .getFullKeyJoinWithColon(userId, configId);
        String redisValue = ActivityRedisUtils.getKey(redisKey);
        // 当前锁的推送时间若未到过期时间，不执行推送 (这里重复只可能是失败重试或者重平衡导致offset位点多次消费，不存在并发)
        if (StringUtils.isNotBlank(redisValue) && System.currentTimeMillis() <= Long.parseLong(redisValue)) {
            log.error("[NotificationPushCreateService] notification push time not used");
            return;
        }
        ActivityRedisUtils.delRedisKey(redisKey);

        // 配置失效
        if (configBO.getStatus() == NotificationStatusEnum.INVALID.getVal()
                || configBO.getDeleted() == DeleteEnum.DELETED.getVal()) {
            log.warn("[NotificationPushCreateService] 取消推送，配置失效 userId:{}, configId:{}, status:{}",
                    userId, configId, configBO.getStatus());
            return;
        }

        // 计算下一次推送时间，发送延迟消息
        sendDelayNotification(userId, configBO, eventTime);
    }

    /**
     * 发送延迟消息
     */
    @Override
    public void sendDelayNotification(long userId, NotificationPushConfigBO config, long eventTime) {
        long delayTime = 0L;
        String redisKey = NotificationRedisKeyEnum.SEND_DELAY_NOTIFICATION_PUSH
                .getFullKeyJoinWithColon(userId, config.getId());
        long nextOccasionTime = 0;
        switch (NotificationOccasionEnum.of(config.getOccasion())) {
            case AFTER:
                nextOccasionTime = notificationPushBasicService
                        .getNotificationPushNextOccasionTime(config.getPeriodConfig(), eventTime);
                if (!EnvUtils.isProd()) {
                    log.info(
                            "[NotificationPushCreateService] userId:{}, configId:{}, 推送时间 下一次推送时间:{}, 事件时间:{}",
                            userId, config.getId(), normalFormatTimeStamp(nextOccasionTime),
                            normalFormatTimeStamp(eventTime));
                }
                long currentTime = System.currentTimeMillis();
                if (nextOccasionTime < currentTime) {
                    log.warn("[NotificationPushCreateService] 推送时间无效, 停止推送 下一次推送时间:{}, 当前时间:{}, 事件时间:{}",
                            normalFormatTimeStamp(nextOccasionTime), normalFormatTimeStamp(currentTime),
                            normalFormatTimeStamp(eventTime));
                    return;
                }
                delayTime = nextOccasionTime - currentTime;
                break;
            case IMMEDIATE:
                break;
            default:
                perfFail(NOTIFICATION_PUSH_CREATE_SERVICE, "occasion.invalid", String.valueOf(config.getId()));
                return;
        }

        // msg白名单开关
        if (!sendDelayNotificationMsgWhiteList.get().isOnFor(userId)) {
            log.info("[NotificationPushCreateService] userId: {}, 发送延迟推送消息白名单没命中", userId);
            return;
        }

        nextOccasionTime = nextOccasionTime == 0 ? System.currentTimeMillis() : nextOccasionTime;
        long expireTime = (delayTime / 1000) + sendDelayNotificationExpireTime.get();
        // 幂等校验，防止重复发送
        if (!ActivityRedisUtils.lockNxEx(redisKey, String.valueOf(nextOccasionTime), expireTime)) {
            log.warn("[NotificationPushCreateService] 分布式锁失败，消息重复触发！userId:{}, config:{}", userId,
                    ObjectMapperUtils.toJSON(config));
            perfFail(NOTIFICATION_PUSH_CREATE_SERVICE, "send.delayMsg.locked");
            return;
        }
        // 构建消息
        DelayNotificationPushMsg msg = DelayNotificationPushMsg.newBuilder()
                .setConfigId(config.getId())
                .setUserId(userId)
                .setEventTime(eventTime)
                .setMessageTime(System.currentTimeMillis())
                .build();
        try {
            // 发送消息
            MqSyncSendResult result = sendDelayMqMsg(delayTime, msg);
            if (!result.isSuccess()) {
                throw new BizException(NOTIFICATION_SEND_DELAY_MSG_ERROR);
            }
            if (!EnvUtils.isProd()) {
                log.info("[NotificationPushCreateService] 发送延迟消息, nextOccasionTime:{}, delayTime:{}, msg:{}, config:{}",
                        nextOccasionTime, delayTime, ObjectMapperUtils.toJSON(msg), ObjectMapperUtils.toJSON(config));
            }
            perfSuccess(NOTIFICATION_PUSH_CREATE_SERVICE, "send.delayMsg", String.valueOf(config.getActivityId()),
                    NotificationEntityTypeEnum.of(config.getEntityType()).getDesc());
        } catch (Exception e) {
            // 释放锁，mq重试
            ActivityRedisUtils.delRedisKey(redisKey);
            log.error(
                    "[NotificationPushCreateService] 发送延迟消息失败, nextOccasionTime:{}, delayTime:{}, msg:{}, config:{}",
                    nextOccasionTime, delayTime, ObjectMapperUtils.toJSON(msg), ObjectMapperUtils.toJSON(config), e);
            perfException(NOTIFICATION_PUSH_CREATE_SERVICE, "send.delayMsg");
            throw new BizException(NOTIFICATION_SEND_DELAY_MSG_ERROR);
        }
    }

    @Override
    @Deprecated
    public void createNotificationConfig(ActivityDO activity, List<AwardConfigDO> awardConfigList, String code) {
        NotificationCodeEnum notificationCode = NotificationCodeEnum.getByCode(code);
        List<NotificationPushConfigBO> notificationPushConfigs = Lists.newArrayList();
        switch (notificationCode) {
            case PROMOTE_SIGN_UP:
                PeriodConfigBO periodConfigBO =
                        convertPeriodConfigBO(activity.getDrawStartTime(), activity.getDrawEndTime());
                log.info("[纵向配置后台] activityId :{} ,促报名时间周期：{}", activity.getId(), toJSON(periodConfigBO));
                notificationPushConfigs = activityNotificationService.buildNotificationConfig(activity.getId(),
                        Lists.newArrayList(activity.getId()), periodConfigBO, code);
                break;
            case REWARD_TO_ACCOUNT:
            case AWARD_CANCEL:
            case FAILED_PASS_AUDIT:
                if (CollectionUtils.isNotEmpty(awardConfigList)) {
                    List<Long> awardConfigIdList =
                            awardConfigList.stream().map(AwardConfigDO::getId).collect(Collectors.toList());
                    notificationPushConfigs = activityNotificationService.buildNotificationConfig(activity.getId(),
                            awardConfigIdList, null, code);
                }
                break;
            case MERCHANT_BOOT_CAMP:
                if (CollectionUtils.isNotEmpty(awardConfigList)) {
                    List<Long> awardConfigIdList =
                            awardConfigList.stream()
                                    .filter(awardConfigDO -> Objects.equals(awardConfigDO.getAwardType(),
                                            AwardTypeEnum.SERVICE_MARKET_COUPON.getCode()))
                                    .map(AwardConfigDO::getId).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(awardConfigIdList)) {
                        break;
                    }
                    notificationPushConfigs = activityNotificationService.buildNotificationConfig(activity.getId(),
                            awardConfigIdList, null, code);
                }
                break;
            case RANKING_TERMINATION:
                notificationPushConfigs =
                        activityNotificationService.buildNotificationConfig(activity.getId(),
                                Lists.newArrayList(activity.getId()), null, code);
                break;
            default:
        }
        notificationPushConfigs.forEach(
                config -> notificationPushBasicService.createOrUpdateNotificationPushConfig(config));
    }

    @Override
    public List<Long> createNotificationConfig(ActivityDO activity, List<AwardConfigDO> awardConfigList,
            List<TaskDO> taskList, NotificationConfigBO notificationConfig, String operator) {
        if (Objects.isNull(activity) || Objects.isNull(notificationConfig)) {
            return Lists.newArrayList();
        }
        List<NotificationPushConfigBO> notificationPushConfigs =
                buildNotificationPushConfig(activity, awardConfigList, taskList, notificationConfig);
        if (CollectionUtils.isEmpty(notificationPushConfigs)) {
            return Lists.newArrayList();
        }
        notificationPushConfigs.forEach(
                config -> notificationPushBasicService.createOrUpdateNotificationPushConfig(config));
        //对于需要送审的进行送审
        if (needAuditChannelList.get().contains(notificationConfig.getChannel())) {
            if (notificationAdminPermission.get().contains(operator)) {
                //白名单 免审批 将所有送审的都置为审核通过
                notificationPushConfigs.forEach(notificationPushConfigBO -> {
                    NotificationPushConfigDO updateParam = new NotificationPushConfigDO();
                    updateParam.setId(notificationPushConfigBO.getId());
                    updateParam.setStatus(NotificationStatusEnum.VALID.getVal());
                    notificationPushConfigDAO.updateSelectiveById(updateParam);
                });
            } else {
                adminKimService.submitNotificationAudit("submitNotificationAudit", notificationPushConfigs, operator);
            }
        }
        return notificationPushConfigs.stream().map(NotificationPushConfigBO::getId).collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> buildNotificationPushConfig(ActivityDO activity,
            List<AwardConfigDO> awardConfigList, List<TaskDO> taskList,
            NotificationConfigBO notificationConfig) {
        NotificationCodeEnum notificationCode =
                NotificationCodeEnum.getByCode(notificationConfig.getNotificationCode());
        if (!isLiteActivity(activity)) {
            taskList = filterTaskByType(activity, taskList, notificationCode);
            if (CollectionUtils.isEmpty(taskList)) {
                return Lists.newArrayList();
            }
        }

        NotificationCodeStrategyService notificationCodeStrategyService =
                notificationCodeStrategyFactory.getNotificationPeriodConfigStrategyService(notificationCode);
        if (notificationCodeStrategyService == null) {
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return notificationCodeStrategyService.buildNotificationConfig(activity, taskList, awardConfigList,
                notificationConfig);
    }

    /**
     * 根据触达类型，对任务进行过滤
     */
    private List<TaskDO> filterTaskByType(ActivityDO activity, List<TaskDO> taskList,
            NotificationCodeEnum notificationCode) {
        if (CollectionUtils.isEmpty(taskList)) {
            return Lists.newArrayList();
        }
        // 抽取父任务
        List<TaskDO> parentTaskList = taskList.stream()
                .filter(task -> task.getParentTask() == 0)
                .collect(Collectors.toList());

        Set<Long> singlePhaseParentTaskIdList = new HashSet<>();
        Set<Long> multiPhaseParentTaskIdList = new HashSet<>();

        parentTaskList.forEach(parentTask -> {
            List<TaskDO> childTaskList = taskList.stream()
                    .filter(task -> task.getParentTask().equals(parentTask.getId()))
                    .collect(Collectors.toList());
            if (isSingleStepTask(activity.getId(), parentTask, childTaskList)) {
                singlePhaseParentTaskIdList.add(parentTask.getId());
            } else {
                multiPhaseParentTaskIdList.add(parentTask.getId());
            }
        });

        List<TaskDO> filteredTaskList;
        switch (notificationCode) {
            case ACTIVITY_PROGRESS:
                filteredTaskList = taskList.stream()
                        .filter(taskDO -> singlePhaseParentTaskIdList.contains(taskDO.getId())
                                || singlePhaseParentTaskIdList.contains(taskDO.getParentTask()))
                        .collect(Collectors.toList());
                break;
            case ACTIVITY_REACH_STEP:
                filteredTaskList = taskList.stream()
                        .filter(taskDO -> multiPhaseParentTaskIdList.contains(taskDO.getId())
                                || multiPhaseParentTaskIdList.contains(taskDO.getParentTask()))
                        .collect(Collectors.toList());
                break;
            default:
                filteredTaskList = taskList;
        }

        log.info("[触达创建-任务过滤] 触达类型:{}, 过滤前taskList:{}, 过滤后taskList:{}",
                notificationCode.getName(), toJSON(taskList), toJSON(filteredTaskList));
        return filteredTaskList;
    }


    /**
     * 判断当前任务是否是单阶梯任务
     */
    private boolean isSingleStepTask(long activityId, TaskDO parentTask, List<TaskDO> childTasks) {
        TaskTypeEnum taskTypeEnum = TaskTypeEnum.getByCode(parentTask.getType());
        switch (taskTypeEnum) {
            // 按照最高档发奖 多阶梯只有一个子任务
            case SINGLE_PHASE:
                List<IndicatorConfigDO> indicatorConfigs =
                        indicatorLocalCacheService.queryTaskIndicatorConfig(activityId, childTasks.get(0).getId());
                if (CollectionUtils.isEmpty(indicatorConfigs)) {
                    throw new BizException(TASK_SHOW_PROGRESS_CALC_ERROR, "指标配置不存在");
                }
                int stepCount = parseIndicatorStepCount(indicatorConfigs.get(0));
                return stepCount == 1;
            // 完成一档发一档 每个阶梯对应一个子任务
            case MULTI_STAGE:
                // 由于存在周期任务，先根据周期（priority）进行聚合
                Map<Integer, List<TaskDO>> taskMap = childTasks.stream()
                        .collect(Collectors.groupingBy(TaskDO::getPriority));
                // 判断任一周期内子任务数量是否为1
                for (Map.Entry<Integer, List<TaskDO>> integerListEntry : taskMap.entrySet()) {
                    return integerListEntry.getValue().size() == 1;
                }
            default:
                return false;
        }
    }


    private MqSyncSendResult sendDelayMqMsg(long delayTime, DelayNotificationPushMsg msg) {
        MsgBuilder builder = delayNotificationPushProducer.createMsgBuilder(msg);
        if (delayTime > 0) {
            builder.withDelay(Duration.ofMillis(delayTime));
        }
        return delayNotificationPushProducer.sendSync(builder.build());
    }
}
