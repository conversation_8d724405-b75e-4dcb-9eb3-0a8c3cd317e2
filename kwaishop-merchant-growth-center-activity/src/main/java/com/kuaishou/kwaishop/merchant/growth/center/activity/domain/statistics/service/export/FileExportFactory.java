package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.BaseExportParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-06
 */
@Slf4j
@Service
@Lazy
public class FileExportFactory {

    @Resource
    private List<FileExportService> exporters;

    public List<String> doXls(BaseExportParam param) {
        FileExportService exporter = getExporterByBizType(param.getBizType());
        if (exporter == null) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "无导出实现类");
        }
        return exporter.doXls(param);
    }

    public String export(BaseExportParam param, List<String> excels) {
        FileExportService exporter = getExporterByBizType(param.getBizType());
        if (exporter == null) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "无导出实现类");
        }

        return exporter.export(param, excels);
    }

    private FileExportService getExporterByBizType(ExportFileBizTypeEnum bizType) {
        for (FileExportService exporter : exporters) {
            if (exporter.getBizType() == bizType) {
                return exporter;
            }
        }
        return null;
    }
}
