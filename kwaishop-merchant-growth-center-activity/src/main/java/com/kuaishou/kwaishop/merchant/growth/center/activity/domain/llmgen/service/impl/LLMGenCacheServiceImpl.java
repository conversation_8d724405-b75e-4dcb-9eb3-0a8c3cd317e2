package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenRedisKeyEnum.LLM_CONTENT_GEN_ASYNC_LOCK_PREFIX;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.llmContentGenAsyncLockTimeoutMillSeconds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenCacheService;

import io.lettuce.core.SetArgs;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-21
 */
@Service
@Slf4j
public class LLMGenCacheServiceImpl implements LLMGenCacheService {

    @Override
    public Boolean setLLMContentGenAsyncUniqueKey(String uniqueKey, Long expireSeconds) {
        SetArgs args = SetArgs.Builder.ex(expireSeconds);
        String res = getGrowthRedisCommands().set(uniqueKey, String.valueOf(System.currentTimeMillis()), args);
        return StringUtils.equalsIgnoreCase(res, "OK");
    }

    @Override
    public String getLLMContentGenAsyncUniqueKey(String uniqueKey) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "llm物料异步生成key不能为空");
        return getGrowthRedisCommands().get(uniqueKey);
    }

    @Override
    public void deleteLLMContentGenAsyncUniqueKey(String uniqueKey) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "llm物料异步生成key不能为空");
        getGrowthRedisCommands().del(uniqueKey);
    }

    @Override
    public Boolean tryLockLLMContentGenAsyncResult(String uniqueKey) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "llm物料异步生成key不能为空");
        String key = LLM_CONTENT_GEN_ASYNC_LOCK_PREFIX.getFullKeyJoinWithColon(uniqueKey);
        Boolean res = getGrowthRedisCommands().setnx(key, String.valueOf(System.currentTimeMillis()));
        if (res) {
            getGrowthRedisCommands().pexpire(key, llmContentGenAsyncLockTimeoutMillSeconds.get());
            return true;
        }
        return false;
    }

    @Override
    public void unlockLLMContentGenAsyncResult(String uniqueKey) {
        checkArgument(StringUtils.isNotBlank(uniqueKey), "llm物料异步生成key不能为空");
        String key = LLM_CONTENT_GEN_ASYNC_LOCK_PREFIX.getFullKeyJoinWithColon(uniqueKey);
        getGrowthRedisCommands().del(key);
    }
}
