package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-15
 */
@Getter
@AllArgsConstructor
public enum LLMGenSceneTypeEnum {
    UNKNOWN("unknown", "未知", "unknown"),
    LAUNCH_CONTENT_TEXT_GENERATE("launch_content_text_generate", "投放文案物料生成", "launch_content_text_generate"),
    LAUNCH_CONTENT_PIC_GENERATE("launch_content_pic_generate", "投放图片物料生成", "launch_content_pic_generate"),
    COMPONENT_DATA_TEXT_GENERATE("component_data_text_generate", "组件数据文案物料生成", "component_data_text_generate"),
    ;

    private final String scene;

    private final String desc;

    private final String prefix;

    public static LLMGenSceneTypeEnum getByScene(String scene) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getScene(), scene)).findFirst().orElse(UNKNOWN);
    }
}
