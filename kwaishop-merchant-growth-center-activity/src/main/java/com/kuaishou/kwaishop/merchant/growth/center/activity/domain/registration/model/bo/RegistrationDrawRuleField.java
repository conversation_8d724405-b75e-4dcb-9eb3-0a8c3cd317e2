package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegistrationDrawRuleField {
    /**
     * 条件属性名称
     */
    private String field;
    /**
     * 条件值
     */
    private List<String> conditions;
    /**
     * 条件操作
     */
    private String operator;
}
