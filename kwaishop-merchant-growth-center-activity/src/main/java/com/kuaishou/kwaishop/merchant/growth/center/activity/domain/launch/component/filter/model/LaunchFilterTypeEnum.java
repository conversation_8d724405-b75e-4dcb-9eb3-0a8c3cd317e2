package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-22
 */
@Getter
@AllArgsConstructor
public enum LaunchFilterTypeEnum {
    DEFAULT("default", "默认"),
    ENTITY_FILTER("entityFilter", "实体过滤"),
    RULE_FILTER("ruleFilter", "规则过滤"),
    SHOW_FILTER("showFilter", "展示过滤"),
    DEGREE_FILTER("degreeFilter", "降级过滤"),
    AWARD_TYPE_FILTER("awardTypeFilter", "奖励类型过滤"),
    INVESTMENT_ACTIVITY_INFO_FILTER("investmentActivityInfoFilter", "招商活动信息过滤"),
    ;

    private final String type;

    private final String desc;

    public static LaunchFilterTypeEnum getByType(String type) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(null);
    }
}
