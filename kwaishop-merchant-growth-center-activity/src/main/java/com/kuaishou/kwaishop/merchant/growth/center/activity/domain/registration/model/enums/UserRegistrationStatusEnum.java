package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;

import java.util.Objects;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 报名记录状态
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum UserRegistrationStatusEnum {
    INVALID(0, "取消资格"),
    VALID(1, "有资格"),
    RISK(90, "风控"),
    ;

    private Integer code;
    private String desc;

    public static UserRegistrationStatusEnum getByCode(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }

}
