package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-12
 */
public enum RegistrationRuleOperatorEnum {
    UNKNOWN("UNKNOWN", "未知"),

    AND_OPERATOR("AND", "与"),

    OR_OPERATOR("OR", "或"),
    ;

    private final String value;
    private final String desc;

    //
    public static RegistrationRuleOperatorEnum of(String value) {
        for (RegistrationRuleOperatorEnum val : RegistrationRuleOperatorEnum.values()) {
            if (val.getValue().equals(value)) {
                return val;
            }
        }
        return UNKNOWN;
    }

    RegistrationRuleOperatorEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
