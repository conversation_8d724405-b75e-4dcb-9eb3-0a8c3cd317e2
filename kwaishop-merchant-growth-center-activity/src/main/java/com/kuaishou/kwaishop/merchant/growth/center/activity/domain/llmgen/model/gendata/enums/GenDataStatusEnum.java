package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum GenDataStatusEnum {
    UNKNOWN(0, "未知"),
    GENERATING(1, "生成中"),
    GENERATE_SUCCESS(2, "已生成"),
    GENERATE_FAILED(3, "生成失败");

    private final int status;

    private final String desc;

    public static GenDataStatusEnum getByStatus(Integer status) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getStatus(), status)).findFirst().orElse(UNKNOWN);
    }
}
