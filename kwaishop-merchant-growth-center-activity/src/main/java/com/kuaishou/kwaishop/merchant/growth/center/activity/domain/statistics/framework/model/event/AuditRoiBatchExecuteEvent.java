package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuditRoiBatchExecuteEvent extends BatchExecuteEvent {
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 分层配置信息
     */
    private List<RoiActivityLevelConfigBO> activityLayerRoiInfo;
    /**
     * 分层人群信息
     */
    private Map<Long, Set<Long>> layerTaskCrowd;
}
