package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.UserIndicatorStatusEnum.AUDIT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.UserIndicatorStatusEnum.PROCESSING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter.buildDefaultInsertRecord;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter.convertIndicatorInfo2Forecast;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.SCHEDULE_EXPECT_AWARD_CALC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.aggregation.ActivityAggregationHelper.degreeForExpectCalc;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.noNeedAggFinalAwardByMysqlSum;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.expectAwardSupportedBizTypes;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.calculateDayBetween;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.ActivityExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.UserActivityTaskRecordCombineBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardRecordStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.biz.IndicatorCustomizeCalcProcessService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ExpectDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ExpectTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastIndicatorInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRecordService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.UserTaskRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityQueryService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.statistics.StatisticsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OperationFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsSumDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-17
 */
@Slf4j
@Lazy
@Service
public class StatisticsRecordServiceImpl implements StatisticsRecordService {

    private static final List<Integer> FILTER_AWARD_STATUS =
            Arrays.asList(AwardRecordStatusEnum.RISKED.getCode(), AwardRecordStatusEnum.CANCEL.getCode());
    // 实际下发奖励状态列表
    private static final List<Integer> ALREADY_SEND_STATUS =
            Collections.singletonList(AwardRecordStatusEnum.ALREADY_SEND.getCode());

    @Autowired
    private StatisticsDAO statisticsDAO;

    @Autowired
    private UserAwardRecordDAO userAwardRecordDAO;

    @Autowired
    private IndicatorRecordDAO indicatorRecordDAO;

    @Autowired
    private OperationFetchService operationFetchService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private StatisticsCalcService statisticsCalcService;

    @Autowired
    private StatisticsReviewService statisticsReviewService;

    @Autowired
    private UserActivityQueryService userActivityQueryService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private IndicatorCustomizeCalcProcessService indicatorCustomizeCalcProcessService;

    @Override
    public void processStatisticsRecord(long userId, long activityId, boolean roiAccess, String dateVersion,
            StatisticsDO statisticsDO) {
        // 获取当前指标值
        List<IndicatorRecordDO> indicators = indicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        // 获取预估指标值
        List<ForecastIndicatorInfo> forecastIndicatorInfos = indicators.stream()
                .map(this::buildForecastIndicatorInfo).collect(Collectors.toList());
        // 判断能完成任务列表
        List<Long> canFinishTaskList = statisticsCalcService.calcUserCanFinishTask(forecastIndicatorInfos);
        // 获取当前已完成的任务列表
        UserActivityTaskRecordCombineBO userActivityInfo =
                userActivityQueryService.getUserActivityInfoByActivityId(userId, activityId);
        List<Long> finishTaskList = Lists.newArrayList();
        if (userActivityInfo != null) {
            finishTaskList = userActivityInfo.getUserTaskRecordBOList().stream()
                    .filter(userTaskRecordBO -> userTaskRecordBO.getStatus().equals(
                            UserTaskStatusEnum.SUCCESS.getValue())).map(UserTaskRecordBO::getTaskId)
                    .collect(Collectors.toList());
        }
        // 构建统计记录
        ForecastDataBO forecastDataBO = ForecastDataBO.builder()
                .indicatorInfo(forecastIndicatorInfos)
                .canFinishTaskList(canFinishTaskList)
                .finishTaskList(finishTaskList)
                .build();
        StatisticsDO targetStatistics = StatisticsDO.builder()
                .entityType(EntityTypeEnum.USER.getCode())
                .activityId(activityId)
                .entityId(userId)
                .forecastTotalAward(0L)
                .expectTotalAward(0L)
                .dataVersion(dateVersion)
                .forecastRoi("暂无")
                .build();
        if (CollectionUtils.isNotEmpty(canFinishTaskList)) {
            // 任务配置
            List<TaskDO> taskConfigList = taskLocalCacheService.batchGetTaskByTaskId(canFinishTaskList);
            Map<Long, TaskDO> taskConfigMap =
                    taskConfigList.stream().collect(Collectors.toMap(BaseDO::getId, Function.identity()));
            // 奖励配置
            List<AwardConfigDO> awardConfigList =
                    awardConfigLocalCacheService.queryMultiTaskAwardConfig(activityId, canFinishTaskList);
            long forecastAwardValue = 0;
            List<ForecastTaskInfo> taskInfo = Lists.newArrayList();
            for (Long canFinishTask : canFinishTaskList) {
                TaskDO taskDO = taskConfigMap.get(canFinishTask);
                if (taskDO == null) {
                    log.error("[预估奖励计算] 任务ID没有匹配对应任务 canFinishTask:{}，taskConfigMap：{}",
                            canFinishTask, toJSON(taskConfigMap));
                    continue;
                }
                ForecastTaskInfo forecastTaskInfo =
                        buildForecastTaskInfo(userId, taskDO, awardConfigList, forecastIndicatorInfos);
                taskInfo.add(forecastTaskInfo);
                // 活动预估奖励
                forecastAwardValue = forecastAwardValue + forecastTaskInfo.getBusinessForecastValue();
            }
            forecastDataBO.setTaskInfo(taskInfo);

            // 已下发奖励
            Map<Integer, Long> alreadySendAwardValue;
            List<UserAwardRecordDO> awardRecords = userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
            if (CollectionUtils.isNotEmpty(awardRecords)) {
                alreadySendAwardValue = generateAlreadySendAwardValue(awardRecords);
                forecastDataBO.setAlreadySendAwardValue(alreadySendAwardValue);
            }
            targetStatistics.setForecastData(toJSON(forecastDataBO));
            if (forecastAwardValue > 0 && roiAccess) {
                statisticsCalcService.calcForecastSellerRoi(userId, activityId, taskConfigList,
                        forecastIndicatorInfos, taskInfo);
            }
            targetStatistics.setForecastTotalAward(forecastAwardValue);
        }

        // 如果主动降级，则离线计算奖励期望
        if (degreeForExpectCalc(userId, activityId)) {
            calcExpectAward(userId, activityId, indicators, targetStatistics);
        }

        // 同步数据到ES
        statisticsReviewService.syncTaskPredictAwardInfoToEs(userId, activityId, forecastDataBO);
        insertOrUpdateStatisticsRecord(statisticsDO, targetStatistics);
    }

    private void calcExpectAward(long userId, long activityId, List<IndicatorRecordDO> indicators,
            StatisticsDO targetStatistics) {
        ActivityDO activity = activityLocalCacheService.queryActivityInfo(activityId);
        if (Objects.isNull(activity)) {
            return;
        }
        ActivityExtBO activityExtBO = fromJSON(activity.getExt(), ActivityExtBO.class);
        if (Objects.isNull(activityExtBO)) {
            return;
        }
        ActivityBizTypeEnum bizTypeEnum = ActivityBizTypeEnum.getByCode(activityExtBO.getBizType());
        List<String> supportedBizTypes = expectAwardSupportedBizTypes.get();
        if (CollectionUtils.isEmpty(supportedBizTypes) || !supportedBizTypes.contains(bizTypeEnum.getCode())) {
            return;
        }

        // 计算奖励期望
        List<ExpectTaskInfo> expectTaskInfos = supplyExpectData(userId, activityId, indicators);
        long expectTotalAward = expectTaskInfos.stream().mapToLong(ExpectTaskInfo::getExpectValue).sum();
        ExpectDataBO expectData = ExpectDataBO.builder().taskInfos(expectTaskInfos).build();
        targetStatistics.setExpectData(toJSON(expectData));
        targetStatistics.setExpectTotalAward(expectTotalAward);
    }

    private List<ExpectTaskInfo> supplyExpectData(long userId, long activityId, List<IndicatorRecordDO> indicators) {
        log.info("[奖励预期计算] 奖励预期计算开始 userId:{}, activityId:{}", userId, activityId);
        List<ExpectTaskInfo> result = Lists.newArrayList();

        // 获取任务元数据信息
        List<Long> taskIds = indicators.stream().map(IndicatorRecordDO::getEntityId).distinct()
                .collect(Collectors.toList());
        List<TaskDO> tasks = taskLocalCacheService.batchGetTaskByTaskId(taskIds);

        // 获取奖励配置元数据信息
        List<AwardConfigDO> awardConfigList = awardConfigLocalCacheService
                .queryMultiTaskAwardConfig(activityId, taskIds);
        Map<Long, List<AwardConfigDO>> awardConfigTaskMap = awardConfigList.stream()
                .collect(Collectors.groupingBy(AwardConfigDO::getEntityId));

        // 获取指标记录信息
        Map<Long, List<IndicatorRecordDO>> indicatorRecordTaskMap = indicators.stream()
                .collect(Collectors.groupingBy(IndicatorRecordDO::getEntityId));

        for (TaskDO task : tasks) {
            Long taskId = task.getId();
            List<AwardConfigDO> awardConfigs = awardConfigTaskMap.get(taskId);
            List<IndicatorRecordDO> indicatorRecords = indicatorRecordTaskMap.get(taskId);
            if (CollectionUtils.isEmpty(awardConfigs) || CollectionUtils.isEmpty(indicatorRecords)) {
                continue;
            }
            ExpectTaskInfo expectTaskInfo;
            try {
                expectTaskInfo = statisticsCalcService.calcSingleTaskExpectAward(userId,
                        activityId, indicatorRecords, awardConfigs, task);
                log.info("[奖励预期计算] 奖励预期计算成功 userId:{}, activityId:{}, expectTaskInfo:{}",
                        userId, activityId, toJSON(expectTaskInfo));
            } catch (Exception e) {
                log.warn("[奖励预期计算] 奖励预期计算失败 userId:{}, activityId:{}, task:{}",
                        userId, activityId, toJSON(task), e);
                perfException(SCHEDULE_EXPECT_AWARD_CALC, "task", String.valueOf(taskId), e.getMessage());
                continue;
            }

            result.add(expectTaskInfo);
        }

        return result;
    }

    /**
     * 计算各指标预估对象
     */
    private ForecastIndicatorInfo buildForecastIndicatorInfo(IndicatorRecordDO indicator) {
        ForecastIndicatorInfo forecastIndicatorInfo = new ForecastIndicatorInfo();
        forecastIndicatorInfo.setTaskId(indicator.getEntityId());
        forecastIndicatorInfo.setIndicatorId(indicator.getIndicatorId());
        forecastIndicatorInfo.setTargetValue(indicator.getTargetValue());
        forecastIndicatorInfo.setCurrentValue(indicator.getCurrentValue());
        // 指标还未更新
        if (indicator.getCurrentValue() == 0) {
            return forecastIndicatorInfo;
        }
        // 指标不为进行中
        if (indicator.getStatus() != PROCESSING.getValue() && indicator.getStatus() != AUDIT.getValue()) {
            forecastIndicatorInfo.setForecastValue(indicator.getCurrentValue());
            int maxReachStep = statisticsCalcService.calcMaxReachStep(indicator, indicator.getCurrentValue());
            forecastIndicatorInfo.setMaxReachLevel(maxReachStep);
            return forecastIndicatorInfo;
        }
        // 活动天数
        long activityDays = calculateDayBetween(indicator.getStartTime(), indicator.getEndTime());
        // 当前天数(以更新时间作为当前天数计算)(更新时间不能大于结束时间)
        long currentTime = Math.min(indicator.getUpdateTime(), indicator.getEndTime());
        long currentDays = calculateDayBetween(indicator.getStartTime(), currentTime);
        // 不能是0
        currentDays = Math.max(currentDays, 1);
        // 计算预估值
        long forecastValue = statisticsCalcService.calcForecastIndicatorValue(indicator.getIndicatorId(),
                indicator.getCurrentValue(), activityDays, currentDays);
        forecastIndicatorInfo.setForecastValue(forecastValue);
        // 计算达成层级
        if (forecastValue >= indicator.getTargetValue()) {
            int maxReachStep = statisticsCalcService.calcMaxReachStep(indicator, forecastValue);
            forecastIndicatorInfo.setMaxReachLevel(maxReachStep);
        }
        return forecastIndicatorInfo;
    }

    @Override
    public void finalStatisticsRecord(long userId, long activityId, String dateVersion,
            StatisticsDO statisticsDO, int finalStatus) {
        // 获取当前指标值
        List<IndicatorRecordDO> indicators = indicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        // 实际数据
        List<ForecastIndicatorInfo> actualIndicatorInfo = indicators.stream()
                .map(e -> {
                    ForecastIndicatorInfo forecastIndicatorInfo = convertIndicatorInfo2Forecast(e);
                    int maxReachStep = statisticsCalcService.calcMaxReachStep(e, e.getCurrentValue());
                    forecastIndicatorInfo.setMaxReachLevel(maxReachStep);
                    return forecastIndicatorInfo;
                })
                .collect(Collectors.toList());
        // 实际奖励
        long totalAward = 0;
        List<UserAwardRecordDO> awardRecords = userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
        if (CollectionUtils.isNotEmpty(awardRecords)) {
            totalAward = awardRecords.stream()
                    .filter(e -> !FILTER_AWARD_STATUS.contains(e.getStatus()))
                    .mapToLong(UserAwardRecordDO::getAwardValue).sum();
        }
        // 已下发奖励
        ForecastDataBO forecastDataBO = ForecastDataBO.builder().build();
        Map<Integer, Long> alreadySendAwardValue;
        if (statisticsDO != null) {
            String forecastData = statisticsDO.getForecastData();
            if (StringUtils.isNotEmpty(forecastData)) {
                forecastDataBO = fromJSON(forecastData, ForecastDataBO.class);
            }
        }
        if (CollectionUtils.isNotEmpty(awardRecords)) {
            alreadySendAwardValue = generateAlreadySendAwardValue(awardRecords);
            forecastDataBO.setAlreadySendAwardValue(alreadySendAwardValue);
        }
        // 获取当前已完成的任务列表
        UserActivityTaskRecordCombineBO userActivityInfo =
                userActivityQueryService.getUserActivityInfoByActivityId(userId, activityId);
        List<Long> finishTaskList = Lists.newArrayList();
        if (userActivityInfo != null) {
            finishTaskList = userActivityInfo.getUserTaskRecordBOList().stream()
                    .filter(userTaskRecordBO -> userTaskRecordBO.getStatus().equals(
                            UserTaskStatusEnum.SUCCESS.getValue())).map(UserTaskRecordBO::getTaskId)
                    .collect(Collectors.toList());
        }
        forecastDataBO.setFinishTaskList(finishTaskList);
        StatisticsDO targetStatistics = StatisticsDO.builder()
                .entityType(EntityTypeEnum.USER.getCode())
                .actualData(toJSON(actualIndicatorInfo))
                .forecastData(toJSON(forecastDataBO))
                .activityId(activityId)
                .entityId(userId)
                .dataVersion(dateVersion)
                .totalAward(totalAward)
                .forecastTotalAward(totalAward)
                .status(finalStatus)
                .build();
        insertOrUpdateStatisticsRecord(statisticsDO, targetStatistics);
    }

    @Override
    public void processActivityStatisticsRecord(long activityId, String dateVersion, StatisticsDO statisticsDO,
            int activityStatus) {
        // 统计总数
        StatisticsSumDO statisticsSumDO = statisticsDAO.queryActivityStatisticsSum(activityId);
        long sumTotalAward = 0;
        long sumForecastTotalAward = 0;
        if (statisticsSumDO != null) {
            sumTotalAward = statisticsSumDO.getSumTotalAward();
            sumForecastTotalAward = statisticsSumDO.getSumForecastTotalAward();
        }

        long drawCount = statisticsDAO.queryActivityUserDrawCount(activityId);
        // 达成人数去重,完成任意任务
        long completeCount = statisticsDAO.queryActivityUserCompleteCount(activityId);
        // 奖励人数去重
        long awardCount = statisticsDAO.queryActivityUserAwardCount(activityId);
        // 活动已下发奖励
        Map<Integer, Long> alreadySendAwardValue = generateActivityAlreadySendAwardValue(activityId);
        // 构建统计记录
        ForecastDataBO forecastDataBO = ForecastDataBO.builder().build();
        if (statisticsDO != null && StringUtils.isNotBlank(statisticsDO.getForecastData())) {
            forecastDataBO = fromJSON(statisticsDO.getForecastData(), ForecastDataBO.class);
        }
        forecastDataBO.setDrawCount(drawCount);
        forecastDataBO.setCompleteCount(completeCount);
        forecastDataBO.setAwardCount(awardCount);
        forecastDataBO.setAlreadySendAwardValue(alreadySendAwardValue);
        StatisticsDO targetStatistics = StatisticsDO.builder()
                .entityType(EntityTypeEnum.ACTIVITY.getCode())
                .activityId(activityId)
                .entityId(activityId)
                .totalAward(sumTotalAward)
                .forecastTotalAward(sumForecastTotalAward)
                .dataVersion(dateVersion)
                .status(activityStatus)
                .forecastData(toJSON(forecastDataBO))
                .build();
        insertOrUpdateStatisticsRecord(statisticsDO, targetStatistics);
    }

    @Override
    public void insertOrUpdateStatisticsRecord(StatisticsDO origin, StatisticsDO target) {
        if (origin == null) {
            StatisticsDO initRecord = buildDefaultInsertRecord(target);
            long insertNum = statisticsDAO.insert(initRecord);
            log.info("[预估奖励计算] 统计记录插入成功！record:{}-{}", toJSON(target), insertNum);
            return;
        }
        target.setId(origin.getId());
        target.setVersion(origin.getVersion());
        long updateNum = statisticsDAO.updateSelectiveById(target);
        log.info("[预估奖励计算] 统计记录更新成功！record:{}-{}", toJSON(target), updateNum);
    }

    @Override
    public ForecastTaskInfo buildForecastTaskInfo(long userId, TaskDO taskDO, List<AwardConfigDO> awardConfigList,
            List<ForecastIndicatorInfo> forecastIndicatorInfos) {
        long activityId = taskDO.getActivityId();
        long taskId = taskDO.getId();
        // 指标配置筛选
        forecastIndicatorInfos = forecastIndicatorInfos.stream()
                .filter(e -> e.getTaskId() == taskId).collect(Collectors.toList());
        // 奖励配置筛选
        List<AwardConfigDO> awardConfigs = awardConfigList.stream()
                .filter(e -> e.getEntityId().equals(taskId))
                // 部分场景下不支持激励提前计算
                .filter(awardConfigDO -> !indicatorCustomizeCalcProcessService.isCustomCalculationForAwardConfig(
                        awardConfigDO))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(awardConfigs)) {
            return ForecastTaskInfo.builder().build();
        }
        // 计算预估奖励
        long taskPredictAward = 0;
        long taskPredictBusinessAward = 0;
        Map<Integer, Long> predictAward = Maps.newHashMap();
        try {
            predictAward = statisticsCalcService.calcSingleTaskPredictAward(userId, activityId,
                    taskId, forecastIndicatorInfos, awardConfigs, taskDO);
        } catch (Exception e) {
            log.error("[预估奖励计算] 子任务预估奖励计算错误 userId:{},canFinishTask:{}，awardConfigList：{}",
                    userId, taskId, toJSON(awardConfigs), e);
        }
        if (MapUtils.isEmpty(predictAward)) {
            return ForecastTaskInfo.builder().build();
        }
        for (int awardType : predictAward.keySet()) {
            taskPredictAward = taskPredictAward + predictAward.get(awardType);
            AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);
            if (awardTypeEnum.getIsBusiness()) {
                taskPredictBusinessAward = taskPredictBusinessAward + predictAward.get(awardType);
            }
        }
        return ForecastTaskInfo.builder()
                .taskId(taskId)
                .parentTaskId(taskDO.getParentTask())
                .forecastValue(taskPredictAward)
                .businessForecastValue(taskPredictBusinessAward)
                .forecastValueByType(predictAward)
                .build();
    }

    @Override
    public List<StatisticsDO> queryStatisticsInfo(long activityId, List<Long> entityIds, Integer entityType) {
        return statisticsDAO.queryStatisticsRecordByEntityIds(activityId, entityIds, entityType);
    }

    private Map<Integer, Long> generateAlreadySendAwardValue(List<UserAwardRecordDO> userAwardRecordDOList) {
        Map<Integer, Long> awardValueMap = Maps.newHashMap();
        userAwardRecordDOList.stream()
                .filter(userAwardRecordDO -> ALREADY_SEND_STATUS.contains(userAwardRecordDO.getStatus()))
                .forEach(userAwardRecordDO -> {
                    awardValueMap.put(userAwardRecordDO.getAwardType(),
                            awardValueMap.getOrDefault(userAwardRecordDO.getAwardType(), 0L) + userAwardRecordDO
                                    .getAwardValue());
                });
        return awardValueMap;
    }

    private Map<Integer, Long> generateActivityAlreadySendAwardValue(long activityId) {
        Map<Integer, Long> alreadySendAwardValue = Maps.newHashMap();
        // 开关为true,则不执行下面全量查询用户活动聚合发奖情况的逻辑，对于报名人数多的活动容易OOM
        if (noNeedAggFinalAwardByMysqlSum.get()) {
            return alreadySendAwardValue;
        }
        List<StatisticsDO> statisticsDOList = statisticsDAO.queryUserStatisticsRecord(activityId);
        if (CollectionUtils.isEmpty(statisticsDOList)) {
            return alreadySendAwardValue;
        }
        statisticsDOList.forEach(statisticsDO -> {
            // 获取用户维度已下发奖励
            String forecastData = statisticsDO.getForecastData();
            if (StringUtils.isEmpty(forecastData)) {
                return;
            }
            ForecastDataBO forecastDataBO = fromJSON(forecastData, ForecastDataBO.class);
            if (MapUtils.isEmpty(forecastDataBO.getAlreadySendAwardValue())) {
                return;
            }
            forecastDataBO.getAlreadySendAwardValue().entrySet().forEach(entry -> {
                alreadySendAwardValue.put(entry.getKey(),
                        alreadySendAwardValue.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            });
        });
        return alreadySendAwardValue;
    }
}
