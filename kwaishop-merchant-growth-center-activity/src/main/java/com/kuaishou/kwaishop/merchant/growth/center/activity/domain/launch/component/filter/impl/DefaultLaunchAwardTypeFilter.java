package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model.LaunchFilterTypeEnum.AWARD_TYPE_FILTER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.impl.LaunchResolveServiceImpl.resolveStageFromSubActivityId;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.LaunchFilter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.filter.model.LaunchFilterTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchFilterParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-04-08
 */
@Component
@Slf4j
public class DefaultLaunchAwardTypeFilter implements LaunchFilter<DefaultLaunchInfoFetchContext> {

    @Resource
    private LaunchResolveService launchResolveService;

    @Resource
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Override
    public void filter(DefaultLaunchInfoFetchContext context) {
        // 获取投放活动配置
        Map<Long, List<LaunchConfigBO>> launchConfigActivityMap = context.getLaunchConfigActivityMap();
        List<Long> launchActivityIds = context.getLaunchActivityIds();

        if (MapUtils.isEmpty(launchConfigActivityMap) || CollectionUtils.isEmpty(launchActivityIds)) {
            return;
        }

        Long userId = context.getUserId();
        DefaultLaunchFilterParamBO filterParam = context.getFilterParam();

        // 过滤实体奖励类型列表
        launchConfigActivityMap =
                filterByAwardTypeList(userId, launchActivityIds, launchConfigActivityMap, filterParam);
        launchActivityIds = new ArrayList<>(launchConfigActivityMap.keySet());

        // 覆盖投放活动数据
        context.setLaunchActivityIds(launchActivityIds);
        context.setLaunchConfigActivityMap(launchConfigActivityMap);

        if (!EnvUtils.isProd()) {
            Map<Long, List<Long>> activityLaunchConfigIdMap = Maps.newHashMap();
            MapUtils.emptyIfNull(launchConfigActivityMap).forEach((activityId, launchConfigs) -> {
                if (CollectionUtils.isEmpty(launchConfigs)) {
                    return;
                }
                activityLaunchConfigIdMap.put(activityId, launchConfigs.stream().map(LaunchConfigBO::getId)
                        .collect(Collectors.toList()));
            });
            log.info("[投放过滤器] 奖励类型过滤完成 userId:{}, scene:{}, activityLaunchConfigIdMap:{}",
                    userId, context.getScene(), toJSON(MapUtils.emptyIfNull(activityLaunchConfigIdMap)));
        }
    }

    private Map<Long, List<LaunchConfigBO>> filterByAwardTypeList(Long userId, List<Long> launchActivityIds,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap, DefaultLaunchFilterParamBO filterParam) {

        // 获取奖励类型参数
        List<Integer> entityAwardTypeList = filterParam.getEntityAwardTypeList();
        if (CollectionUtils.isEmpty(entityAwardTypeList)) {
            return launchConfigActivityMap;
        }

        Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap = Maps.newHashMap();

        // 活动维度遍历
        launchActivityIds.forEach(launchActivityId -> {
            filterSingleActivityAwardTypeList(launchActivityId, launchConfigActivityMap, entityAwardTypeList,
                    filteredLaunchConfigActivityMap);
        });

        return filteredLaunchConfigActivityMap;
    }

    private void filterSingleActivityAwardTypeList(Long launchActivityId,
            Map<Long, List<LaunchConfigBO>> launchConfigActivityMap,
            List<Integer> entityAwardTypeList,
            Map<Long, List<LaunchConfigBO>> filteredLaunchConfigActivityMap) {

        List<LaunchConfigBO> filteredLaunchConfigs;

        // 如果活动投放配置为空 直接返回
        List<LaunchConfigBO> launchConfigs = launchConfigActivityMap.get(launchActivityId);
        if (CollectionUtils.isEmpty(launchConfigs)) {
            return;
        }

        // 获取投放活动实体类型
        LaunchConfigBO firstLaunchConfig = launchConfigs.get(0);
        EntityTypeEnum entityType = EntityTypeEnum.getByCode(firstLaunchConfig.getEntityType());

        switch (entityType) {
            case SUB_ACTIVITY:
                filteredLaunchConfigs = filterSubActivityAwardTypeList(
                        launchActivityId, entityAwardTypeList, launchConfigs);
                break;
            case ACTIVITY:
                filteredLaunchConfigs = filterActivityAwardTypeList(
                        launchActivityId, entityAwardTypeList, launchConfigs);
                break;
            default:
                throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的实体类型");
        }

        if (CollectionUtils.isNotEmpty(filteredLaunchConfigs)) {
            filteredLaunchConfigActivityMap.put(launchActivityId, filteredLaunchConfigs);
        }
    }

    private List<LaunchConfigBO> filterActivityAwardTypeList(Long launchActivityId, List<Integer> entityAwardTypeList,
            List<LaunchConfigBO> launchConfigs) {

        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        // 查询活动维度奖励配置
        List<AwardConfigDO> activityAwardConfigs =
                awardConfigLocalCacheService.queryAwardConfigByActivityId(launchActivityId);
        if (CollectionUtils.isEmpty(activityAwardConfigs)) {
            return filteredLaunchConfigs;
        }

        List<Integer> activityAwardTypeList = activityAwardConfigs.stream()
                .map(AwardConfigDO::getAwardType).distinct().collect(Collectors.toList());

        // 是否存在交集
        if (CollectionUtils.containsAny(activityAwardTypeList, entityAwardTypeList)) {
            filteredLaunchConfigs.addAll(launchConfigs);
        }

        return filteredLaunchConfigs;
    }

    private List<LaunchConfigBO> filterSubActivityAwardTypeList(Long launchActivityId,
            List<Integer> entityAwardTypeList,
            List<LaunchConfigBO> launchConfigs) {

        List<LaunchConfigBO> filteredLaunchConfigs = Lists.newArrayList();

        // 获取任务记录
        List<Long> subActivityIds =
                launchConfigs.stream().map(LaunchConfigBO::getEntityId).collect(Collectors.toList());
        List<TaskDO> tasks =
                launchResolveService.resolveTaskConfigBySubActivityId(launchActivityId, subActivityIds);

        // 区分父子任务
        List<TaskDO> parentTasks = ListUtils.emptyIfNull(tasks).stream()
                .filter(task -> Objects.equals(task.getParentTask(), 0L)).collect(Collectors.toList());
        List<TaskDO> subTasks = ListUtils.emptyIfNull(tasks).stream()
                .filter(task -> !Objects.equals(task.getParentTask(), 0L)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentTasks) || CollectionUtils.isEmpty(subTasks)) {
            return filteredLaunchConfigs;
        }

        // 构建任务映射
        Map<Integer/*stage*/, TaskDO> parentTaskStageMap = parentTasks.stream()
                .collect(Collectors.toMap(TaskDO::getStage, Function.identity(), (k1, k2) -> k1));

        Map<Long, List<TaskDO>> subTaskParentIdMap =
                subTasks.stream().collect(Collectors.groupingBy(TaskDO::getParentTask));
        List<Long> subTaskIds = subTasks.stream().map(TaskDO::getId).collect(Collectors.toList());

        // 查询奖励配置
        List<AwardConfigDO> awardConfigs =
                awardConfigLocalCacheService.queryMultiTaskAwardConfig(launchActivityId, subTaskIds);
        Map<Long, List<AwardConfigDO>> awardConfigTaskIdMap =
                awardConfigs.stream().collect(Collectors.groupingBy(AwardConfigDO::getEntityId));

        // 遍历投放配置
        launchConfigs.forEach(launchConfig -> {
            Integer stage = resolveStageFromSubActivityId(launchConfig.getEntityId());
            TaskDO parentTask = parentTaskStageMap.get(stage);
            if (parentTask == null) {
                return;
            }

            List<TaskDO> subTasksOfCertainParent = subTaskParentIdMap.get(parentTask.getId());
            if (CollectionUtils.isEmpty(subTasksOfCertainParent)) {
                return;
            }

            // 遍历子任务奖励配置
            boolean containsResult = subTasksOfCertainParent.stream().anyMatch(subTask -> {
                Long subTaskId = subTask.getId();
                List<AwardConfigDO> awardConfigOfCertainTask = awardConfigTaskIdMap.get(subTaskId);
                if (CollectionUtils.isEmpty(awardConfigOfCertainTask)) {
                    return false;
                }

                List<Integer> awardTypeList = awardConfigOfCertainTask.stream()
                        .map(AwardConfigDO::getAwardType).distinct().collect(Collectors.toList());

                // 是否存在交集
                return CollectionUtils.containsAny(awardTypeList, entityAwardTypeList);
            });

            if (containsResult) {
                filteredLaunchConfigs.add(launchConfig);
            }
        });

        return filteredLaunchConfigs;
    }

    @Override
    public boolean match(DefaultLaunchInfoFetchContext context) {
        DefaultLaunchFilterParamBO filterParam = context.getFilterParam();
        return filterParam != null && CollectionUtils.isNotEmpty(filterParam.getEntityAwardTypeList());
    }

    @Override
    public int order() {
        return 0;
    }

    @Override
    public LaunchFilterTypeEnum getType() {
        return AWARD_TYPE_FILTER;
    }
}
