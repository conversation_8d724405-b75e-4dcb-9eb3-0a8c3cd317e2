package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.processor;

import static com.kuaishou.framework.concurrent.DynamicThreadExecutor.dynamic;
import static com.kuaishou.framework.supplier.DynamicSuppliers.dynamicRateLimiter;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum.PROCESSING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.NOTIFICATION_CHANGE_TASK_PROCESS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.notificationConsumerRegistrationSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationChangeTaskProcessRateLimiter;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationChangeTaskProcessThreadPoolSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.notificationConsumerRegistrationLimit;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.notificationChangeTaskProcessActivityList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.notificationChangeTaskProcessUserList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ecyrd.speed4j.StopWatch;
import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushCreateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
@Component
@Slf4j
public class NotificationChangeTaskProcessStrategy implements NotificationChangeProcessStrategy {

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private ActivityDAO activityDAO;

    @Autowired
    private NotificationPushCreateService notificationPushCreateService;

    private static final DynamicRateLimiter RATE_LIMITER =
            dynamicRateLimiter(notificationChangeTaskProcessRateLimiter);

    // 触达配置变更任务实体处理（默认10个线程）
    private static final ExecutorService NOTIFICATION_CHANGE_TASK_EXECUTOR = dynamic(
            notificationChangeTaskProcessThreadPoolSize::get, "notification-change-task-process");

    @Override
    public void process(long activityId, List<NotificationPushConfigBO> configs, long eventTime) {
        try {
            StopWatch sw = new StopWatch();
            log.info("[触达配置变更任务实体处理] 开始执行 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime);

            ActivityDO activityDO = activityDAO.queryById(activityId);
            if (activityDO == null) {
                log.warn("[触达配置变更任务实体处理] 活动元数据为空 activityId:{}", activityId);
                perfException(NOTIFICATION_CHANGE_TASK_PROCESS, "活动配置异常", String.valueOf(activityId));
                return;
            }

            // 活动维度放量
            if (!notificationChangeTaskProcessActivityList.get().isOnFor(activityId)) {
                return;
            }

            // 根据 entityStatus 事件类型进行聚合
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap =
                    configs.stream().collect(Collectors.groupingBy(NotificationPushConfigBO::getEntityStatus));

            // 遍历资格记录
            int shardNum = userTaskRecordDAO.getMysqlShardCount();
            // 扫表资格记录表
            CountDownLatch latch = new CountDownLatch(shardNum);
            // 分shard处理
            for (int shard = 0; shard < shardNum; shard++) {
                int finalShard = shard;
                NOTIFICATION_CHANGE_TASK_EXECUTOR.submit(() -> {
                    try {
                        handleSingleUserTaskShard(finalShard, activityId, entityStatusMap, eventTime);
                    } catch (Exception e) {
                        log.error("[触达配置变更任务实体处理] 异常！shard:{}", finalShard, e);
                        perfException(NOTIFICATION_CHANGE_TASK_PROCESS, "shard异常", e.getClass().getSimpleName());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            log.info("[触达配置变更任务实体处理] 执行成功 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime);
            perfSuccessWithWatch(NOTIFICATION_CHANGE_TASK_PROCESS, String.valueOf(activityId), sw);
        } catch (Exception e) {
            log.error("[触达配置变更任务实体处理] 执行异常 activityId:{}, configs:{}, eventTime:{}",
                    activityId, toJSON(configs), eventTime, e);
            perfException(NOTIFICATION_CHANGE_TASK_PROCESS, "执行异常", String.valueOf(activityId),
                    e.getClass().getSimpleName());
        }
    }

    private void handleSingleUserTaskShard(int shard, long activityId,
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap, long eventTime) {
        int limit = notificationConsumerRegistrationLimit.get();
        long cursor = 0;
        // 限流
        RATE_LIMITER.acquire();
        // 针对每一个分片进行分页遍历
        while (true) {
            // 快速终止
            if (notificationConsumerRegistrationSwitch.get()) {
                break;
            }
            // 分页取记录
            List<UserTaskRecordDO> pageRecordList =
                    userTaskRecordDAO.scanByActivityId(activityId, shard, cursor, limit);

            if (CollectionUtils.isEmpty(pageRecordList)) {
                break;
            }

            // 处理记录
            pageRecordList.forEach(record -> handleSingleUserTaskRecord(record, entityStatusMap, eventTime));
            // 赋值
            cursor = pageRecordList.get(pageRecordList.size() - 1).getId() + 1;
            perfSuccess(NOTIFICATION_CHANGE_TASK_PROCESS, "单分片执行完成", String.valueOf(shard));
        }
    }

    private void handleSingleUserTaskRecord(UserTaskRecordDO record,
            Map<Integer, List<NotificationPushConfigBO>> entityStatusMap, long eventTime) {

        if (!notificationChangeTaskProcessUserList.get().isOnFor(record.getUserId())) {
            log.info("[触达配置变更任务实体处理] 用户不在白名单 userId:{}, activityId:{}", record.getUserId(),
                    record.getActivityId());
            return;
        }

        entityStatusMap.forEach((entityStatus, configsOfCertainEntityStatus) ->
                doHandleSingleUserTaskRecord(entityStatus, configsOfCertainEntityStatus, record, eventTime));
    }

    private void doHandleSingleUserTaskRecord(int entityStatus, List<NotificationPushConfigBO> configs,
            UserTaskRecordDO record, long eventTime) {

        Long userId = record.getUserId();
        UserTaskEventTypeEnum userTaskEventType = UserTaskEventTypeEnum.of(entityStatus);

        // 过滤出 entityId 相同的触达配置
        configs = configs.stream().filter(config -> Objects.equals(config.getEntityId(), record.getTaskId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configs)) {
            log.info("[触达配置变更任务实体处理] 推送触达配置实体过滤为空 userId:{}, eventTime:{}", userId, eventTime);
            return;
        }

        switch (userTaskEventType) {
            case DRAW:
                eventTime = record.getStartTime();

                UserTaskStatusEnum userTaskStatus = UserTaskStatusEnum.of(record.getStatus());
                if (!userTaskStatus.equals(PROCESSING)) {
                    log.info("[触达配置变更任务实体处理] 推送触达配置过滤 userId:{}, configs:{}, eventTime:{}",
                            userId, toJSON(configs), eventTime);
                    return;
                }

                notificationPushCreateService.notificationPushCreate(userId, configs, eventTime);
                log.info("[触达配置变更任务实体处理] 推送触达配置成功 userId:{}, configs:{}, eventTime:{}",
                        userId, toJSON(configs), eventTime);
                return;
            default:
                //
        }
    }

    @Override
    public NotificationEntityTypeEnum getType() {
        return TASK;
    }
}
