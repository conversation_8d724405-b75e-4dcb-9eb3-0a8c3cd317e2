package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.ACTIVITY_SHARD_DATA_SOURCE_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.REGISTRATION_SIGN_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityABTestMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityCrowdBlackMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.commonMockJsonMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.activityCrowdRegistrationWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.abtest.AbtestInstance;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorCalcRangeConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.MockJsonMapBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.ParentTaskRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.ActivityCustomRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.TaskCrowdTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.TaskConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.distribute.CrowdGroupDistributeService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.RuleCenterClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义活动上线相关操作
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-10
 */
@Slf4j
@Lazy
@Service
public class ActivityCustomRegistrationServiceImpl implements ActivityCustomRegistrationService {

    @Autowired
    private RiskControlFetchService riskControlFetchService;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private IndicatorTaskService indicatorTaskService;

    @Autowired
    private CrowdGroupDistributeService crowdGroupDistributeService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private TaskConfigDAO taskConfigDAO;

    @Override
    public boolean filter(Long userId, Long activityId) {
        if (!activityCrowdRegistrationWhiteList.get().isOnFor(userId)) {
            log.warn("[活动报名被过滤] 用户不在放量名单中, userId:{},activityId:{}", userId, activityId);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "用户不在放量名单中");
            return true;
        }
        Long crowdBlack = activityCrowdBlackMap.getMap(Long.class, Long.class).get(activityId);
        if (crowdBlack != null && crowdBlack > 0 && crowdGroupDistributeService.checkUserInCrowds(userId,
                Lists.newArrayList(crowdBlack))) {
            log.warn("[活动报名被过滤] 在业务黑名单中, userId:{},activityId:{}", userId, activityId);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "在业务黑名单中");
            return true;
        }
        String abTestKey = activityABTestMap.getMap(Long.class, String.class).get(activityId);
        if (StringUtils.isNotBlank(abTestKey) && !abPass(userId, abTestKey)) {
            log.warn("[活动报名被过滤] AB平台过滤, userId:{},activityId:{}", userId, activityId);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "AB平台过滤");
            return true;
        }
        if (riskControlFetchService.inRiskBlackList(userId)) {
            log.warn("[活动报名被过滤] 在风控黑名单中, userId:{},activityId:{}", userId, activityId);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "在风控黑名单中");
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Long> registrationActivity(long userId, long activityId) {
        try {
            List<UserRegistrationRecordBO> activityBOList = Lists.newArrayList();
            UserRegistrationRecordBO activityRecordBO = new UserRegistrationRecordBO();
            activityRecordBO.setUserId(userId);
            activityRecordBO.setActivityId(activityId);
            activityRecordBO.setEntityType(EntityTypeEnum.ACTIVITY);
            activityRecordBO.setEntityId(activityId);
            activityRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
            activityRecordBO.setRegistrationTime(System.currentTimeMillis());
            activityRecordBO.setOperator("System");
            activityRecordBO.setSource("ActivityCustomRegistrationServiceImpl");
            List<IndicatorConfigDO> indicatorConfigDOS =
                    indicatorLocalCacheService.queryTaskIndicatorConfig(activityId, 0L);
            Map<String, Long> jsonMap = null;
            if (CollectionUtils.isNotEmpty(indicatorConfigDOS)) {
                jsonMap = new HashMap<>();
                Map<Long/*IndicatorConfigDO#ID*/, String/*7_day_100*/> configValueMap =
                        getIndicatorConfigMap(indicatorConfigDOS);
                Map<Long/*IndicatorConfigDO#ID*/, Long/*indicatorValue*/> baseValueMap =
                        indicatorTaskService.batchQueryActivityDMBasicIndicatorValue(userId, indicatorConfigDOS);
                for (Map.Entry<Long, Long> entry : baseValueMap.entrySet()) {
                    // key : IndicatorCalcRangeConfig#tag,value : indicatorValue
                    jsonMap.put(configValueMap.get(entry.getKey()), entry.getValue());
                }
                if (getMockJsonMap(userId, activityId) != null) {
                    jsonMap = getMockJsonMap(userId, activityId);
                }
                String jsonData = toJSON(jsonMap);
                activityRecordBO.setJsonData(jsonData);
            }
            activityBOList.add(activityRecordBO);
            userRegistrationService.batchInsertUserRegistrationRecord(activityBOList);
            log.info("[报名活动成功] userId: {}, activityId: {}", userId, activityId);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "报名活动成功");
            return jsonMap;
        } catch (Exception e) {
            log.error("[报名活动失败] userId: {}, activityId: {}", userId, activityId, e);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "报名活动失败");
            return null;
        }
    }

    @Override
    public void registrationTask(long userId, long activityId, List<TaskDO> parentTaskDOList,
            Map<String, Long> jsonMap) {
        try {
            List<UserRegistrationRecordBO> taskBOList = Lists.newArrayList();
            for (TaskDO taskDO : parentTaskDOList) {
                if (!inTaskCrowd(userId, taskDO, jsonMap)) {
                    log.info("[报名任务] 备过滤 userId:{}, activityId:{}, taskId:{}", userId, activityId, taskDO.getId());
                    continue;
                }
                UserRegistrationRecordBO taskRecordBO = new UserRegistrationRecordBO();
                taskRecordBO.setUserId(userId);
                taskRecordBO.setActivityId(activityId);
                taskRecordBO.setEntityType(EntityTypeEnum.TASK);
                taskRecordBO.setEntityId(taskDO.getId());
                taskRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
                taskRecordBO.setRegistrationTime(System.currentTimeMillis());
                taskRecordBO.setOperator("System");
                taskRecordBO.setSource("ActivityCustomRegistrationServiceImpl");
                taskBOList.add(taskRecordBO);
            }
            userRegistrationService.batchInsertUserRegistrationRecord(taskBOList);
            for (UserRegistrationRecordBO recordBO : taskBOList) {
                log.info("[报名任务成功] userId:{}, activityId:{}, taskId:{}", userId, activityId, recordBO.getEntityId());
                perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(recordBO.getEntityId()), "报名任务成功");
            }
        } catch (Exception e) {
            log.error("[报名任务失败] userId:{}, activityId:{}", userId, activityId, e);
            perfScene(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "报名任务失败");
        }
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean registrationActivityAndTask(long userId, long activityId,
            List<ParentTaskRegistrationRecordBO> registrationTasks) {
        List<UserRegistrationRecordBO> insertRecords = Lists.newArrayList();
        // 判断是否已有活动资格记录
        UserRegistrationRecordBO activityRegistrationRecord = userRegistrationService
                .queryUserRegistrationRecord(userId, activityId, EntityTypeEnum.ACTIVITY, activityId);
        if (activityRegistrationRecord == null) {
            // 构造活动资格记录
            insertRecords.add(buildActivityRegistrationRecord(userId, activityId, null));
        }
        List<TaskDO> parentTaskList = registrationTasks.stream()
                .map(ParentTaskRegistrationRecordBO::getParentTask)
                .collect(Collectors.toList());
        List<Long> parentTaskIdList = parentTaskList.stream()
                        .map(TaskDO::getId).collect(Collectors.toList());
        // 构造任务资格记录
        registrationTasks.forEach(portraitTask -> {
            UserRegistrationRecordBO taskRegistrationRecord = buildTaskRegistrationRecord(userId,
                    activityId, portraitTask.getParentTask(), portraitTask.getJsonData());
            insertRecords.add(taskRegistrationRecord);
        });
        return batchInsertUserRegistrationRecord(userId, activityId, insertRecords, parentTaskIdList);
    }

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SHARD_DATA_SOURCE_NAME)
    public boolean registrationActivityAndTask(long userId, long activityId, List<TaskDO> parentTaskList,
            Map<String, Object> jsonMap) {
        List<UserRegistrationRecordBO> insertRecords = Lists.newArrayList();
        // 判断是否已有活动资格记录
        UserRegistrationRecordBO activityRegistrationRecord = userRegistrationService
                .queryUserRegistrationRecord(userId, activityId, EntityTypeEnum.ACTIVITY, activityId);
        if (activityRegistrationRecord == null) {
            // 构造活动资格记录
            insertRecords.add(buildActivityRegistrationRecord(userId, activityId, jsonMap));
        }
        List<Long> parentTaskIdList = parentTaskList.stream().map(BaseDO::getId).collect(Collectors.toList());
        // 构造任务资格记录
        parentTaskList.forEach(parentTask -> {
            UserRegistrationRecordBO taskRegistrationRecord =
                    buildTaskRegistrationRecord(userId, activityId, parentTask, jsonMap);
            insertRecords.add(taskRegistrationRecord);
        });
        // 插入
        return batchInsertUserRegistrationRecord(userId, activityId, insertRecords, parentTaskIdList);
    }

    /**
     * 批量插入
     */
    private boolean batchInsertUserRegistrationRecord(long userId, long activityId,
            List<UserRegistrationRecordBO> insertRecords, List<Long> parentTaskIdList) {
        // 插入
        boolean insertSuccess = userRegistrationService.batchInsertUserRegistrationRecordNoLimit(insertRecords);
        if (insertSuccess) {
            log.info("[同时下发活动和任务资格] 资格插入成功，userId:{}, activityId:{}, taskIdList:{}", userId, activityId,
                    parentTaskIdList);
            perfSuccess(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "同时报名活动任务成功");
        } else {
            log.error("[同时下发活动和任务资格] 资格插入失败，userId:{}, activityId:{}, taskIdList:{}", userId, activityId,
                    parentTaskIdList);
            perfFail(REGISTRATION_SIGN_ACTIVITY, String.valueOf(activityId), "同时报名活动任务失败");
        }
        return insertSuccess;
    }
    private UserRegistrationRecordBO buildTaskRegistrationRecord(long userId, long activityId, TaskDO parentTask,
            Map<String, Object> jsonMap) {
        UserRegistrationRecordBO taskRecordBO = new UserRegistrationRecordBO();
        taskRecordBO.setUserId(userId);
        taskRecordBO.setActivityId(activityId);
        taskRecordBO.setEntityType(EntityTypeEnum.TASK);
        taskRecordBO.setEntityId(parentTask.getId());
        taskRecordBO.setJsonData(toJSON(jsonMap));
        taskRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        taskRecordBO.setRegistrationTime(System.currentTimeMillis());
        taskRecordBO.setOperator("System");
        taskRecordBO.setSource("registrationActivityAndTask");
        return taskRecordBO;

    }

    private UserRegistrationRecordBO buildActivityRegistrationRecord(long userId, long activityId, Map<String, Object> jsonMap) {
        UserRegistrationRecordBO activityRecordBO = new UserRegistrationRecordBO();
        activityRecordBO.setUserId(userId);
        activityRecordBO.setActivityId(activityId);
        activityRecordBO.setOperator("System");
        activityRecordBO.setEntityType(EntityTypeEnum.ACTIVITY);
        activityRecordBO.setEntityId(activityId);
        activityRecordBO.setJsonData(toJSON(jsonMap));
        activityRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        activityRecordBO.setRegistrationTime(System.currentTimeMillis());
        activityRecordBO.setSource("registrationActivityAndTask");
        return activityRecordBO;
    }

    @Override
    public long getCrowdId(ActivityDO activityDO) {
        ValidateUtil.checkArgument(StringUtils.isNotEmpty(activityDO.getCrowdConfig()), "活动人群配置不存在");
        CrowdConfigBO crowdConfigBO = ObjectMapperUtils.fromJSON(activityDO.getCrowdConfig(), CrowdConfigBO.class);
        Long crowdId = crowdConfigBO.getCrowdId();
        if (crowdId == null || crowdId < 1) {
            // 如果 crowdId为0，说明活动上没有配置人群包。需要去使用子任务上的人群包
            return 0L;
        }
        return crowdId;
    }

    private Map<Long/*IndicatorConfigDO#ID*/, String/*7_day_100*/> getIndicatorConfigMap(
            List<IndicatorConfigDO> indicatorConfigDOS) {
        Map<Long, String> configTagMap = new HashMap<>();
        for (IndicatorConfigDO configDO : indicatorConfigDOS) {
            IndicatorCalcRangeConfig rangeConfig =
                    ObjectMapperUtils.fromJSON(configDO.getCalcRangeConfig(), IndicatorCalcRangeConfig.class);
            // value 值为 例如  7_day_tag 其中tag 实际是IndicatorId
            long gap = DateUtils.getDayBetween(rangeConfig.getFixedStartTime(), rangeConfig.getFixedEndTime());
            String mapValue = gap + "_day_" + rangeConfig.getTag();
            configTagMap.put(configDO.getId(), mapValue);
        }
        return configTagMap;
    }

    private boolean abPass(long userId, String abTestKey) {
        return AbtestInstance.MERCHANT_TASK.getBoolean(abTestKey, true, "", userId);
    }

    @Override
    public boolean inTaskCrowd(long userId, TaskDO taskDO, Map<String, Long> jsonMap) {
        int crowdType = taskDO.getCrowdType();
        if (TaskCrowdTypeEnum.UNKNOWN.getCode() == crowdType) {
            return false;
        }
        if (TaskCrowdTypeEnum.ALL.getCode() == crowdType) {
            return true;
        }
        if (TaskCrowdTypeEnum.PACKAGE.getCode() == crowdType) {
            CrowdConfigBO crowdConfigBO = ObjectMapperUtils.fromJSON(taskDO.getCrowdConfig(), CrowdConfigBO.class);
            Long crowdId = crowdConfigBO.getCrowdId();
            if (crowdId == null || crowdId < 1) {
                log.error("[任务人群配置不存在] taskId:{}", taskDO.getId());
                perfFail(REGISTRATION_SIGN_ACTIVITY, "任务人群配置不存在");
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "任务人群配置不存在");
            }
            return crowdGroupDistributeService.checkUserInCrowds(userId, Lists.newArrayList(crowdId));
        }
        if (TaskCrowdTypeEnum.RULE.getCode() == crowdType) {
            CrowdConfigBO crowdConfigBO = ObjectMapperUtils.fromJSON(taskDO.getCrowdConfig(), CrowdConfigBO.class);
            String ruleId = crowdConfigBO.getRuleId();
            if (StringUtils.isEmpty(ruleId)) {
                log.error("[任务规则配置不存在] taskId:{}", taskDO.getId());
                perfFail(REGISTRATION_SIGN_ACTIVITY, "任务规则配置不存在");
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "任务规则配置不存在");
            }
            HashMap<String, Object> context = Maps.newHashMap();
            jsonMap.put("taskId", taskDO.getId());
            context.put("context", jsonMap);
            return (boolean) RuleCenterClient.getInstance().ruleExecute(ruleId, context);
        }
        return false;
    }

    private Map<String, Long> getMockJsonMap(Long userId, Long activityId) {
        Map<Long, MockJsonMapBO> map = commonMockJsonMap.getMap(Long.class, MockJsonMapBO.class);
        MockJsonMapBO mockJsonMapBO = map.get(activityId);
        if (mockJsonMapBO == null) {
            return null;
        }
        String mockStr = mockJsonMapBO.getMockJsonMap().get(userId);
        if (StringUtils.isBlank(mockStr)) {
            return null;
        }
        log.info("[该用户基础数据被mock] userId:{},activityId:{}", userId, activityId);
        return ObjectMapperUtils.fromJSON(mockStr, Map.class, String.class, Long.class);
    }
}
