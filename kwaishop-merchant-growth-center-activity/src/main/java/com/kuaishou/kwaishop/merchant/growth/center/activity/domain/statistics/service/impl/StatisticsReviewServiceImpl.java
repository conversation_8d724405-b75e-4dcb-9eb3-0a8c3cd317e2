package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.framework.concurrent.DynamicThreadExecutor.dynamic;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJson;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType.ACTIVITY_STRATEGY_ADMIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityLiteConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.isLiteActivity;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveBaseIndicatorIdListFromTaskExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveBasicConfigFromTaskExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.UserRegistrationRecordResolver.fillJsonDataMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.STRATEGY_FIX_AWARD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.STRATEGY_FIX_TARGET;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorTypeEnum.STATISTICS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_END_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_PROGRESS_QUERY_1;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_START_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.BASE_INFO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.DEFAULT_USERS_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.FORECAST_INFO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.INDICATOR_PERFORMANCE_TAG;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.LEAF_PARTY_CODE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.MY_NO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.SMB_BIZ_CODE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.SMB_OWNER_STAFF;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STATISTICS_USER_DETAIL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STRATEGY_FIX_AWARD_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STRATEGY_FIX_TARGET_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.TASK_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.TASK_INFO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.USER_HEAD_URL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.USER_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.USER_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.buildInitStatisticsTask;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.buildStatisticsAward;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.buildStatisticsBaseInfoBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.buildStatisticsEsUpdateMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.buildStatisticsIndicator;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertDynamicTableBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertEmptyDynamicTableBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertUserAwardReviewDetailBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertUserIndicatorReviewDetailBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertUserReviewDetailBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert.convertUserTaskReviewDetailBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_INIT_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_SYNC_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_TASK_SYNC_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.SCHEDULE_STATISTICS_FORECAST_DATA_SYNC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.TASK_STATISTICS_USER_AGGREGATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.buildAggJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.parseTermsResult;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.es.EsIndexConfig.KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.LAYER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.PERIOD;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.SUB_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.statisticLevelIndustryQuerySwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.statisticsReviewDataExportThreadPoolSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.statisticsReviewDataFillThreadPoolSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.statisticsActivityInitRateLimit;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.autoTestAccountUserId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.statisticsReviewConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.defaultUserHeadUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityBooleanConfigKey.reviewPeriodFieldQueryRbSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.staffPolicyRepoConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils.getDynamicTableHeaderConfig;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSceneWithWatch;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.github.phantomthief.collection.BufferTrigger;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.framework.supplier.DynamicSuppliers;
import com.kuaishou.infra.framework.common.util.TermHelper;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.LiteConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.IndustryAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.FixAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.FixTargetBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.AwardTargetTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.IndicatorTargetTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.resolver.CommonResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.generator.service.IdGeneratorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorStatisticsConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.IndicatorConfigTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.model.enums.PolicyDrawType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.model.enums.SiteTagEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.ReviewConvert;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.DynamicTableHeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastIndicatorInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.HeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerDimStatisticSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerExportConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerPolicySignUpSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SlrBelongInfoCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.DynamicTableBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.RuleExecuteResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsReviewConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserAwardReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserIndicatorReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.UserTaskReviewDetailBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ActivityReviewParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.SellerDetailParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRecordService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewV2Service;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRoiService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.TermsAggregationBuild;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.FileExportFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.SellerTagConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.StaffPolicyRepoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.ChildrenTaskDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.LayerDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.PeriodDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.SubActivityDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.TaskDimensionInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.ActivityConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.annotation.CodeNote;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.ddd.repository.property.DataResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.TailNumberUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.audit.UserAuditRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.StatisticsEsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.TermsAggData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.statistics.StatisticsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.DataManagerFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OperationCoreFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OrgManageFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RuleExecuteFacadeMixService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.SellerProfileFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.UserInfoFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.DistributorDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.UserRiskResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.es.EsQueryResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.UserAuditRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsEsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsForecastInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsTaskInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.UserStatisticsChangeSyncMsg;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.operation.core.center.protobuf.attr.crmattr.SellerAttrInfoDTO;
import com.kuaishou.user.model.UserCache;

import io.vavr.Tuple2;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Lazy
@Service
public class StatisticsReviewServiceImpl implements StatisticsReviewService {

    // 商达列表补充数据线程池（默认10个线程）
    private static final ExecutorService USER_REVIEW_DATA_FILL_EXECUTOR =
            dynamic(statisticsReviewDataFillThreadPoolSize::get, "statistics-user-review-data-fill-task");

    // 商达列表补充数据线程池（默认10个线程）
    private static final ExecutorService USER_EXPORT_EXECUTOR =
            dynamic(statisticsReviewDataExportThreadPoolSize::get, "statistics-user-review-data-export-task");

    protected final DynamicRateLimiter activityDataInitRateLimiter =
            DynamicSuppliers.dynamicRateLimiter(statisticsActivityInitRateLimit);

    @Autowired
    private IndicatorDAO indicatorDAO;

    @Autowired
    private StatisticsDAO statisticsDAO;

    @Autowired
    private StatisticsEsDAO statisticsEsDAO;

    @Autowired
    private AdminKimService adminKimService;

    @Autowired
    private FileExportFactory fileExportFactory;

    @Autowired
    private IndicatorConfigDAO indicatorConfigDAO;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private QueryBuildFactory queryBuildFactory;

    @Autowired
    private IdGeneratorService idGeneratorService;

    @Autowired
    private UserAwardRecordDAO userAwardRecordDAO;

    @Autowired
    private UserAuditRecordDAO userAuditRecordDAO;

    @Autowired
    private UserInfoFetchService userInfoFetchService;

    @Autowired
    private IndicatorRecordDAO userIndicatorRecordDAO;

    @Autowired
    private ActivityConfigService activityConfigService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private StatisticsCalcService statisticsCalcService;

    @Autowired
    private RegistrationConverter registrationConverter;

    @Autowired
    private StatisticsCacheService statisticsCacheService;

    @Autowired
    private RiskControlFetchService riskControlFetchService;

    @Autowired
    private StatisticsRecordService statisticsRecordService;

    @Autowired
    private DataManagerFetchService dataManagerFetchService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Autowired
    private StatisticsMsgProduceService statisticsMsgProduceService;

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private StatisticsRoiService statisticsRoiService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private OrgManageFetchService orgManageFetchService;

    @Autowired
    private IndicatorBasicNewService indicatorBasicNewService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private SellerProfileFetchService sellerProfileFetchService;

    @Autowired
    private OperationCoreFetchService operationCoreFetchService;

    @Autowired
    private StatisticsReviewV2Service statisticsReviewV2Service;

    @Autowired
    private RuleExecuteFacadeMixService ruleExecuteFacadeMixService;

    private static final int BATCH_SIZE = 1000;
    private static final int LINGER = 120;
    private static final String BIZ_CODE = "merchant_operation";

    // 这个声明是lazy的，只有第一次执行enqueue时才会创建schedule线程池和容器，所以是可以被安全的在field里声明的
    private final BufferTrigger<UserStatisticsChangeSyncMsg> bufferTrigger =
            BufferTrigger.<UserStatisticsChangeSyncMsg>batchBlocking() //
                    .batchSize(BATCH_SIZE) // 每1000个归并一次
                    .linger(LINGER, TimeUnit.SECONDS) // 每隔120秒消费一次
                    .setConsumerEx(this::doBatchConsumer) // 消费调用函数（会在一个独立线程执行）
                    .build();
    /**
     * 消费方法
     */
    private void doBatchConsumer(List<UserStatisticsChangeSyncMsg> params) {
        // 批量消费
        // 按活动聚合
        Map<Long, List<UserStatisticsChangeSyncMsg>> activityAgg = params.stream()
                .collect(Collectors.groupingBy(UserStatisticsChangeSyncMsg::getActivityId));
        // 按用户聚合
        activityAgg.forEach((activityId, userEventList) -> {
            Map<Long, List<UserStatisticsChangeSyncMsg>> userAgg = userEventList.stream()
                    .collect(Collectors.groupingBy(UserStatisticsChangeSyncMsg::getUserId));
            boolean roiAccess = statisticsRoiService.getRoiAccessWithCache(activityId);
            userAgg.forEach((userId, eventList) -> {
                List<UserStatisticsChangeSyncMsg> sortEvent = eventList.stream()
                        .sorted(Comparator.comparing(UserStatisticsChangeSyncMsg::getChangeTime).reversed())
                        .collect(Collectors.toList());
                long changeTime = sortEvent.get(0).getChangeTime();
                handleUserStatisticsData(userId, activityId, changeTime, roiAccess);
                perfSuccess(MQ_USER_STATISTICS_TASK_SYNC_CONSUME, String.valueOf(activityId), "bufferTrigger.consume");
            });
        });
    }

    @PostConstruct
    protected void init() {
        // 程序结束时把所有积攒的数据一次性消费干净
        TermHelper.addTerm(() -> {
            log.info("[统计用户任务变动-PostConstruct] bufferTrigger shutdown!");
            bufferTrigger.manuallyDoTrigger(); // 程序结束时把所有积攒的数据一次性消费干净
        });
    }

    @PreDestroy
    protected void destroy() {
        log.info("[统计用户任务变动-PreDestroy] bufferTrigger shutdown!");
        bufferTrigger.manuallyDoTrigger(); // 程序结束时把所有积攒的数据一次性消费干净
    }


    @Override
    public void initStatisticsReviewData(long userId, long activityId, String initData, ActivityDO activity) {
        activityDataInitRateLimiter.acquire();
        Map<String, Object> data = Maps.newHashMap();
        // 本身数据
        if (StringUtils.isNotBlank(initData)) {
            data.putAll(fromJson(initData));
        }
        // 查活动元数据
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (null == activityDO) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "活动不存在");
        }
        // 反查幂等
        StatisticsEsDO esDO = statisticsEsDAO.queryStatisticsUserData(userId, activityId);
        if (esDO != null) {
            boolean success = statisticsEsDAO.insertOnDuplicateUpdate(userId, activityId, data);
            log.info("[用户复盘数据初始化] 用户初始数据覆盖成功，userId:{}, activityId:{}, data:{}, success:{}",
                    userId, activityId, toJSON(data), success);
            perfScene(MQ_USER_STATISTICS_INIT_CONSUME, String.valueOf(activityId), "already.exist");
            return;
        }
        // 用户自身信息（鲁班无法泛化调用）
        UserCache userCache = userInfoFetchService.querySellerInfo(userId);
        data.put(USER_NAME, DEFAULT_USERS_NAME);
        data.put(USER_HEAD_URL, defaultUserHeadUrl.get());
        if (userCache != null) {
            data.put(USER_NAME, userCache.getUserName());
            data.put(USER_HEAD_URL, userCache.getUserHead());
        }
        // 活动自身信息
        if (activity != null) {
            data.put(ACTIVITY_NAME, activity.getName());
            data.put(ACTIVITY_START_TIME, activityDO.getStartTime());
            data.put(ACTIVITY_END_TIME, activityDO.getEndTime());
        }
        // 二级行业信息
        String industryCode = MapUtils.getString(data, LEAF_PARTY_CODE, StringUtils.EMPTY);
        if (statisticLevelIndustryQuerySwitch.get() && StringUtils.isNotBlank(industryCode)) {
            orgManageFetchService.fillSellerBelongInfo(BIZ_CODE, industryCode, data);
        }
        // 初始化-查询商家中小归属信息
        SellerAttrInfoDTO sellerSmbAttrInfo = operationCoreFetchService.querySellerAttrInfo(userId, SMB_BIZ_CODE);
        if (sellerSmbAttrInfo != null) {
            String smbPartyId = sellerSmbAttrInfo.getPartyId();
            String managerId = sellerSmbAttrInfo.getManagerId();
            data.put(SMB_OWNER_STAFF, managerId);
            if (StringUtils.isNotBlank(smbPartyId)) {
                orgManageFetchService.fillSellerBelongInfo(SMB_BIZ_CODE, smbPartyId, data);
            }
        }
        Map<String, Object> initAggregationData = Maps.newHashMap();
        if (!isLiteActivity(activityDO)) {
            // 指标可以不初始化，奖励可以不初始化，任务/基值信息得初始化
            initAggregationData = getUserStatisticsInitAggregationData(userId, activityId);
        }

        if (MapUtils.isEmpty(initAggregationData) && !isLiteActivity(activityDO)) {
            perfSuccess(MQ_USER_STATISTICS_INIT_CONSUME, String.valueOf(activityId), "init.filter");
            return;
        }
        if (MapUtils.isNotEmpty(initAggregationData)) {
            data.putAll(initAggregationData);
        }
        boolean success = statisticsEsDAO.insertOnDuplicateUpdate(userId, activityId, data);
        log.info("[用户复盘数据初始化] 用户数据初始化成功，userId:{}, activityId:{}, data:{}, success:{}",
                userId, activityId, toJSON(data), success);
        perfSuccess(MQ_USER_STATISTICS_INIT_CONSUME, String.valueOf(activityId));
    }

    /**
     * 填充商家标签信息
     *
     * @param userId
     * @param data
     */
    private void fillSellerProfile(long userId, Map<String, Object> data) {
        try {
            StaffPolicyRepoConfig policyRepoConfig = staffPolicyRepoConfig.getObject();
            // 降级开关
            if (policyRepoConfig == null || BooleanUtils.isTrue(policyRepoConfig.getSellerProfileQueryCloseSwitch())) {
                return;
            }
            List<SellerTagConfig> exportSellerTagList = policyRepoConfig.getExportSellerTagList();
            if (CollectionUtils.isEmpty(exportSellerTagList)) {
                throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "商家标签配置为空");
            }
            Map<String, String> tagKeyMap = exportSellerTagList.stream()
                    .collect(Collectors.toMap(SellerTagConfig::getTagKey, SellerTagConfig::getShowKey, (v1, v2) -> v1));

            ArrayList<String> targetNameList = Lists.newArrayList(tagKeyMap.keySet());
            Map<Long, Map<String, String>> sellerProfileMap =
                    sellerProfileFetchService.synGetSelectionIdsInfo(Collections.singletonList(userId),
                            exportSellerTagList);
            if (MapUtils.isEmpty(sellerProfileMap) || MapUtils.isEmpty(sellerProfileMap.get(userId))) {
                return;
            }
            Map<String, String> sellerProfile = sellerProfileMap.get(userId);
            targetNameList.forEach(tagKey -> {
                String saveKey = tagKeyMap.get(tagKey);
                String value = sellerProfile.get(tagKey);
                if (StringUtils.isNotBlank(value)) {
                    data.put(saveKey, value);
                }
            });
            if (!EnvUtils.isProd()) {
                log.info("[用户复盘数据初始化] 商家标签信息，userId:{}, data:{}", userId, toJSON(data));
            }
        } catch (Exception e) {
            log.error("[用户复盘数据初始化] 商家标签信息查询失败，userId:{}", userId, e);
        }
    }

    @Override
    public void aggHandleUserChangeEvent(long userId, long activityId, long eventTime) {
        UserStatisticsChangeSyncMsg changeSyncMsg = UserStatisticsChangeSyncMsg.newBuilder()
                .setUserId(userId)
                .setActivityId(activityId)
                .setChangeTime(eventTime)
                .build();
        // 这个会调用入队方法，把userStatisticsChangeSyncMsg压入定制的容器中
        StatisticsReviewConfig reviewConfig = statisticsReviewConfig.getObject();
        if (reviewConfig.getAggHandleUserChange() == null || Boolean.TRUE.equals(
                reviewConfig.getAggHandleUserChange())) {
            bufferTrigger.enqueue(changeSyncMsg);
            return;
        }
        boolean roiAccess = statisticsRoiService.getRoiAccessWithCache(activityId);
        handleUserStatisticsData(userId, activityId, eventTime, roiAccess);
    }

    @Override
    public void handleUserStatisticsData(long userId, long activityId, long changeTime, boolean roiAccess) {
        // 是否已加锁
        Long lockTime = statisticsCacheService.getStatisticsChangeSyncLock(userId, activityId);
        if (lockTime != null) {
            if (changeTime < lockTime) {
                perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), "cache.expire");
            } else {
                // 重发消息
                perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), "wait.again");
                statisticsMsgProduceService.sendStatisticsChangeMsg(userId, activityId, changeTime, true);
            }
            return;
        }
        // 查询ES记录的反查时间
        StatisticsEsDO originRecord = statisticsEsDAO.queryStatisticsUserData(userId, activityId);
        // 未初始化就有变动消息不处理
        if (originRecord == null) {
            log.info("[统计数据变动处理] 未初始化就有变动消息不处理，userId:{}, activityId:{}", userId, activityId);
            perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), "no.record");
            return;
        }
        if (originRecord.getUpdateVersion() != null && originRecord.getUpdateVersion() > changeTime) {
            perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), "record.expire");
            return;
        }
        // 尝试加锁
        boolean lock = statisticsCacheService.setStatisticsChangeSyncLock(userId, activityId, changeTime);
        if (!lock) {
            // 重发消息
            perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), "lock.fail");
            statisticsMsgProduceService.sendStatisticsChangeMsg(userId, activityId, changeTime, true);
            return;
        }
        // 反查更新
        try {
            StatisticsEsDO statisticsEsDO =
                    getUserStatisticsAggregationData(userId, activityId, originRecord, roiAccess);
            Map<String, Object> updateParam = buildStatisticsEsUpdateMap(statisticsEsDO);
            boolean success = statisticsEsDAO.insertOnDuplicateUpdate(userId, activityId, updateParam);
            if (success) {
                perfSuccess(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId));
            } else {
                // 解锁
                perfFail(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId));
                statisticsCacheService.delStatisticsChangeSyncLock(userId, activityId);
            }
        } catch (Exception e) {
            log.error("[统计数据变动处理] 异常！userId:{}, activityId:{}", userId, activityId, e);
            perfException(MQ_USER_STATISTICS_SYNC_CONSUME, String.valueOf(activityId), e.getClass().getSimpleName());
            // 解锁
            statisticsCacheService.delStatisticsChangeSyncLock(userId, activityId);
        }
    }

    @Override
    public Map<String, Object> getUserStatisticsInitAggregationData(long userId, long activityId) {
        // 报名记录
        List<UserRegistrationRecordDO> userRegistrationRecordList =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, TASK, null, userId, null);
        if (CollectionUtils.isEmpty(userRegistrationRecordList)) {
            if (autoTestAccountUserId.get().contains(userId)) {
                return Maps.newHashMap();
            }
            throw new BizException(BasicErrorCode.SERVER_ERROR, "无有效任务报名记录");
        }
        // 基值信息
        List<UserRegistrationRecordDO> needBaseInfoRecords = resolveNeedBaseInfoRecords(userRegistrationRecordList);
        List<Map<String, Object>> userBaseInfoList = buildBaseInfo(userId, activityId, needBaseInfoRecords);
        // 构建任务信息
        List<StatisticsTaskInfoBO> initTaskInfo = buildInitStatisticsTask(userRegistrationRecordList);
        Map<String, Object> initAggregationData = Maps.newHashMap();
        initAggregationData.put(TASK_INFO, initTaskInfo);
        if (CollectionUtils.isNotEmpty(userBaseInfoList)) {
            initAggregationData.put(BASE_INFO, userBaseInfoList);
        }
        return initAggregationData;
    }

    @Override
    public StatisticsEsDO getUserStatisticsAggregationData(long userId, long activityId, StatisticsEsDO originRecord,
                                                           boolean roiAccess) {
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (null == activityDO) {
            throw new BizException(PARAM_INVALID, "活动不存在");
        }
        if (isLiteActivity(activityDO)) {
            return getUserStatisticsAggregationDataOfLiteActivity(userId, activityId);
        }
        return getUserStatisticsAggregationDataOfNormalActivity(userId, activityId, originRecord, roiAccess);
    }

    private StatisticsEsDO getUserStatisticsAggregationDataOfNormalActivity(long userId, long activityId,
                                                                            StatisticsEsDO originRecord,
                                                                            boolean roiAccess) {
        StatisticsEsDO statisticsEsDO = new StatisticsEsDO();
        // 报名记录
        UserActivityRecordDO userActivityRecordDO = userActivityRecordDAO.queryUserActivityRecord(userId, activityId,
                false);
        long draw = userActivityRecordDO != null ? 1 : 0;
        // 任务记录
        List<UserTaskRecordDO> userTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false);
        // 父子关系
        Map<Long, Long> parentChildMap = userTaskRecordList.stream()
                .collect(Collectors.toMap(UserTaskRecordDO::getTaskId, UserTaskRecordDO::getParentId));
        // 指标记录
        List<IndicatorRecordDO> userIndicatorRecordList =
                userIndicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        List<Long> taskIdList =
                userIndicatorRecordList.stream().map(IndicatorRecordDO::getEntityId).collect(Collectors.toList());
        Map<Long, TaskDO> taskConfigMap = taskLocalCacheService.listTaskById(taskIdList);
        // 奖励记录
        List<UserAwardRecordDO> userAwardRecordList =
                userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
        long award = CollectionUtils.isNotEmpty(userAwardRecordList) ? 1 : 0;
        // 报名记录
        List<UserRegistrationRecordDO> userRegistrationRecordList =
                userRegistrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
        // 各父任务基值
        Map<Long, Map<String, Object>> userParentBasicDataMap = userRegistrationRecordList.stream()
                .filter(e -> e.getEntityType().equals(TASK.getCode()))
                .collect(Collectors.toMap(UserRegistrationRecordDO::getEntityId,
                        e -> registrationConverter.resolveBasicDataFromJsonData(e)));
        // 组装
        List<StatisticsTaskInfoBO> statisticsTaskInfos =
                buildStatisticsTask(userTaskRecordList, userIndicatorRecordList, originRecord.getTaskInfo());
        List<StatisticsIndicatorInfoBO> statisticsIndicatorInfos = buildStatisticsIndicator(userIndicatorRecordList,
                userParentBasicDataMap, parentChildMap, taskConfigMap);
        List<StatisticsAwardInfoBO> statisticsAwardInfos = buildStatisticsAward(userAwardRecordList, parentChildMap);
        if (CollectionUtils.isNotEmpty(statisticsAwardInfos) && roiAccess) {
            // 触发ROI计算
            calcAwardRoi(userId, activityId, statisticsAwardInfos, statisticsIndicatorInfos, taskConfigMap,
                    userParentBasicDataMap);
        }
        // 分析基值信息是否有遗漏，有的话补偿
        List<Map<String, Object>> statisticsBaseInfos = buildAggStatisticsBaseInfos(userId, activityId,
                userRegistrationRecordList, statisticsEsDO, draw, originRecord);
        // 基值信息遗漏且任务未领取，会补偿任务记录
        if (CollectionUtils.isEmpty(statisticsEsDO.getTaskInfo())) {
            statisticsEsDO.setTaskInfo(statisticsTaskInfos);
        }
        if (CollectionUtils.isNotEmpty(userAwardRecordList)) {
            statisticsEsDO.setAward(award);
        }
        if (CollectionUtils.isNotEmpty(userRegistrationRecordList)) {
            UserRegistrationRecordDO userRegistration = userRegistrationRecordList.stream()
                    .filter(a -> a.getEntityType().equals(ACTIVITY.getCode())).findFirst().orElse(null);
            if (userRegistration != null && UserRegistrationStatusEnum.RISK.getCode()
                    .equals(userRegistration.getStatus())) {
                statisticsEsDO.setRisk(1L);
            }
        }
        statisticsEsDO.setIndicatorInfo(statisticsIndicatorInfos);
        statisticsEsDO.setAwardInfo(statisticsAwardInfos);
        statisticsEsDO.setUpdateVersion(System.currentTimeMillis());
        statisticsEsDO.setBaseInfo(statisticsBaseInfos);
        statisticsEsDO.setDraw(draw);

        return statisticsEsDO;
    }

    private StatisticsEsDO getUserStatisticsAggregationDataOfLiteActivity(long userId, long activityId) {
        StatisticsEsDO statisticsEsDO = new StatisticsEsDO();
        // 报名记录
        List<UserRegistrationRecordDO> userRegistrationRecordList =
                userRegistrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
        if (CollectionUtils.isNotEmpty(userRegistrationRecordList)) {
            UserRegistrationRecordDO userRegistration = userRegistrationRecordList.stream()
                    .filter(a -> a.getEntityType().equals(ACTIVITY.getCode())).findFirst().orElse(null);
            if (userRegistration != null && UserRegistrationStatusEnum.RISK.getCode()
                    .equals(userRegistration.getStatus())) {
                statisticsEsDO.setRisk(1L);
            }
        }
        UserActivityRecordDO userActivityRecordDO = userActivityRecordDAO.queryUserActivityRecord(userId, activityId,
                false);
        long draw = userActivityRecordDO != null ? 1 : 0;

        statisticsEsDO.setUpdateVersion(System.currentTimeMillis());
        statisticsEsDO.setDraw(draw);
        return statisticsEsDO;
    }

    private void calcAwardRoi(long userId, long activityId, List<StatisticsAwardInfoBO> statisticsAwardInfos,
                              List<StatisticsIndicatorInfoBO> statisticsIndicatorInfos, Map<Long, TaskDO> taskConfigMap,
                              Map<Long, Map<String, Object>> userParentBasicDataMap) {
        try {
            log.info("[发奖ROI计算] userId:{}, activityId:{}, awardInfos:{}", userId, activityId,
                    toJSON(statisticsAwardInfos));
            // 过滤finalAward == 0
            List<StatisticsAwardInfoBO> finalAwardInfo = statisticsAwardInfos.stream()
                    .filter(e -> e.getFinalAwardValue() > 0).collect(
                            Collectors.toList());
            if (CollectionUtils.isEmpty(finalAwardInfo)) {
                return;
            }
            List<Long> awardTaskIds =
                    finalAwardInfo.stream().map(StatisticsAwardInfoBO::getTaskId).distinct()
                            .collect(Collectors.toList());
            List<TaskDO> awardTaskDOS = new ArrayList<>();
            awardTaskIds.forEach(e -> {
                TaskDO taskDO = MapUtils.getObject(taskConfigMap, e, null);
                if (taskDO == null) {
                    return;
                }
                awardTaskDOS.add(taskDO);
            });
            List<StatisticsIndicatorInfoBO> awardTaskIndicatorInfo = statisticsIndicatorInfos.stream()
                    .filter(e -> awardTaskIds.contains(e.getTaskId()))
                    .collect(Collectors.toList());
            statisticsCalcService.asyncCalcReviewSellerRoi(userId, activityId, awardTaskDOS, awardTaskIndicatorInfo,
                    statisticsAwardInfos, userParentBasicDataMap);
        } catch (Exception e) {
            log.warn("[发奖ROI计算] 异常，userId:{}, activityId:{}, awardInfos:{}", userId, activityId,
                    toJSON(statisticsAwardInfos), e);
            perfException(MQ_USER_STATISTICS_SYNC_CONSUME, "calc.roi", e.getClass().getSimpleName());
        }
    }

    /**
     * 补上未初始化的,覆盖基值
     */
    @CodeNote(value = "解析用户报名记录的json数据", modified = true)
    private List<Map<String, Object>> buildAggStatisticsBaseInfos(long userId, long activityId,
                                                                  List<UserRegistrationRecordDO> userRegistrationRecordList,
                                                                  StatisticsEsDO statisticsEsDO, long draw,
                                                                  StatisticsEsDO originRecord) {
        userRegistrationRecordList = resolveNeedBaseInfoRecords(userRegistrationRecordList);
        if (CollectionUtils.isEmpty(userRegistrationRecordList)) {
            return null;
        }
        // 最新聚合信息
        List<Map<String, Object>> aggBaseInfos = Lists.newArrayList();
        // 原始基值信息
        List<Map<String, Object>> originBaseInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(originRecord.getBaseInfo())) {
            originBaseInfos = originRecord.getBaseInfo();
        }
        // 缺失基值信息
        List<UserRegistrationRecordDO> lackBaseInfos = Lists.newArrayList();
        for (UserRegistrationRecordDO registrationRecord : userRegistrationRecordList) {
            Long entityId = registrationRecord.getEntityId();
            Map<String, Object> originBaseInfo = originBaseInfos.stream()
                    .filter(e -> {
                        return MapUtils.getLong(e, TASK_ID, 0L).equals(entityId);
                    })
                    .findFirst().orElse(null);
            // 没有定位到应该有基值的记录，记录补偿
            if (MapUtils.isEmpty(originBaseInfo)) {
                lackBaseInfos.add(registrationRecord);
                continue;
            }
            Map<String, Object> jsonBaseData = registrationConverter.resolveBasicDataFromJsonData(registrationRecord);
            // 子任务基值填充
            List<TaskDO> childTasks = taskLocalCacheService.getChildTask(entityId);
            fillJsonDataMap(childTasks, jsonBaseData, originBaseInfo);
            aggBaseInfos.add(originBaseInfo);
        }
        // 存在缺失基值，执行补偿
        if (CollectionUtils.isNotEmpty(lackBaseInfos)) {
            List<Map<String, Object>> baseInfos = buildBaseInfo(userId, activityId, lackBaseInfos);
            aggBaseInfos.addAll(baseInfos);
            // 如果未领取，说明初始化任务信息也得更新
            if (draw == 0) {
                statisticsEsDO.setTaskInfo(buildInitStatisticsTask(userRegistrationRecordList));
            }
        }
        // 针对测算来源的指标/奖励值需要兼容成基期数据传入整型
        compatibleFixIndicatorAndAward(aggBaseInfos);
        return aggBaseInfos;
    }

    /**
     * 针对测算来源的指标/奖励值需要兼容成基期数据传入整型
     *
     * @param aggBaseInfos
     */
    @CodeNote("解析用户报名记录的json数据")
    public void compatibleFixIndicatorAndAward(List<Map<String, Object>> aggBaseInfos) {
        if (CollectionUtils.isEmpty(aggBaseInfos)) {
            return;
        }
        // 指标考核值兼容
        aggBaseInfos.stream()
                .filter(map -> {
                    Set<String> keySet = map.keySet();
                    if (CollectionUtils.isEmpty(keySet)) {
                        return false;
                    }
                    return keySet.stream()
                            .anyMatch(key -> key.startsWith(STRATEGY_FIX_TARGET_KEY));
                })
                .forEach(this::doIndicatorCompatible);
        // 奖励值兼容
        aggBaseInfos.stream()
                .filter(map -> {
                    Set<String> keySet = map.keySet();
                    if (CollectionUtils.isEmpty(keySet)) {
                        return false;
                    }
                    return keySet.stream()
                            .anyMatch(key -> key.startsWith(STRATEGY_FIX_AWARD_KEY));
                })
                .forEach(this::doAwardCompatible);
    }

    /**
     * 奖励值兼容
     *
     * @param map
     */
    private void doAwardCompatible(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        keySet = keySet.stream()
                .filter(key -> key.startsWith(STRATEGY_FIX_AWARD_KEY))
                .collect(Collectors.toSet());
        keySet.forEach(key -> {
            try {
                Object awardFixObj = map.get(key);
                if (!(awardFixObj instanceof String)) {
                    return;
                }
                // 转成fixAwardBO
                FixAwardBO fixAwardBO = fromJSON(awardFixObj, FixAwardBO.class);
                if (fixAwardBO == null || StringUtils.isBlank(fixAwardBO.getAwardTargetType())) {
                    return;
                }
                String indicatorKey = STRATEGY_FIX_AWARD.format(fixAwardBO.getAwardType(), fixAwardBO.getStep());
                // 非千人千面的固定值移除key后直接返回
                if (!AwardTargetTypeEnum.DIFFERENT_FIXED.getCode().equalsIgnoreCase(fixAwardBO.getAwardTargetType())) {
                    map.remove(indicatorKey);
                    return;
                }
                List<FixAwardBO.RewardScopeVO> values = fixAwardBO.getValues();
                if (CollectionUtils.isEmpty(values)) {
                    return;
                }
                // 转成整型
                map.put(indicatorKey, Long.valueOf(values.get(0).getValue()));
            } catch (Exception e) {
                log.error("compatibleFixAward happen error key {} map {}", key, toJSON(map), e);
            }
        });

    }
    /**
     * 指标考核值兼容
     *
     * @param map
     */
    private void doIndicatorCompatible(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        keySet = keySet.stream()
                .filter(key -> key.startsWith(STRATEGY_FIX_TARGET_KEY))
                .collect(Collectors.toSet());
        keySet.forEach(key -> {
            try {
                Object indicatorFixObj = map.get(key);
                if (!(indicatorFixObj instanceof String)) {
                    return;
                }
                // 转成FixTargetBO
                FixTargetBO fixTargetBO = fromJSON(indicatorFixObj, FixTargetBO.class);
                if (fixTargetBO == null || StringUtils.isBlank(fixTargetBO.getIndicatorTargetType())) {
                    return;
                }
                String indicatorKey = STRATEGY_FIX_TARGET.format(fixTargetBO.getIndicatorId(),
                        fixTargetBO.getStep());
                // 非千人千面的固定值移除key后直接返回
                if (!IndicatorTargetTypeEnum.DIFFERENT_FIXED.getCode().equalsIgnoreCase(fixTargetBO.getIndicatorTargetType())) {
                    map.remove(indicatorKey);
                    return;
                }
                // 转成整型
                map.put(indicatorKey, Long.valueOf(fixTargetBO.getIncValue()));
            } catch (Exception e) {
                log.error("compatibleFixIndicator happen error map {}", toJSON(map), e);
            }
        });
    }

    private List<UserRegistrationRecordDO> resolveNeedBaseInfoRecords(
            List<UserRegistrationRecordDO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        List<Long> userParentTaskIdList = userRegistrationRecords.stream()
                .filter(e -> e.getEntityType().equals(TASK.getCode()))
                .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());
        Map<Long, TaskDO> taskMap = taskLocalCacheService.batchGetTaskByTaskId(userParentTaskIdList).stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        return userRegistrationRecords.stream()
                .filter(e -> taskMap.containsKey(e.getEntityId()))
                .filter(e -> resolveBaseIndicatorIdListFromTaskExt(taskMap.get(e.getEntityId())) != null)
                .collect(Collectors.toList());
    }

    /**
     * 如果基值信息为空填充，基值信息
     */
    private List<Map<String, Object>> buildBaseInfo(long userId, long activityId,
                                                    List<UserRegistrationRecordDO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        List<Long> userParentTaskIdList = userRegistrationRecords.stream()
                .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());
        Map<Long, TaskDO> taskMap = taskLocalCacheService.batchGetTaskByTaskId(userParentTaskIdList).stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        List<Map<String, Object>> aggBaseInfos = userRegistrationRecords.stream().map(parentTaskRecord -> {
            long parentTaskId = parentTaskRecord.getEntityId();
            List<TaskDO> childTasks = taskLocalCacheService.getChildTask(parentTaskId);
            TaskDO taskDO = taskMap.get(parentTaskId);
            BasicConfigBO basicConfigBO = resolveBasicConfigFromTaskExt(taskDO);
            BasicTimeRange basicTimeRange =
                    indicatorBasicNewService.getBasicTimeRange(userId, basicConfigBO);
            Long fixStartTime = basicTimeRange.getEarliestStartTime();
            Long fixEndTime = basicTimeRange.getLatestEndTime();
            DistributorDataBO baseData =
                    dataManagerFetchService.queryDistributorDataElseMarketData(userId, fixStartTime, fixEndTime);
            return buildStatisticsBaseInfoBO(parentTaskId, baseData, parentTaskRecord.getJsonData(), childTasks);
        }).collect(Collectors.toList());
        // 针对测算来源的指标/奖励值需要兼容成基期数据传入整型
        compatibleFixIndicatorAndAward(aggBaseInfos);
        return aggBaseInfos;
    }
    @Override
    public void handleOneActivityAggregationDataSync(ActivityDO activityDO, StatisticsReviewConfig config,
                                                     boolean statisticsPerformance) {
        StopWatch sw = new StopWatch();
        long activityId = activityDO.getId();
        // 黑名单
        List<Long> blackActivityList = config.getBlackActivityList();
        if (CollectionUtils.isNotEmpty(blackActivityList) && blackActivityList.contains(activityId)) {
            return;
        }
        // 放量名单
        if (!TailNumberUtils.isOnFor(activityId, config.getActivityGreyTailNumber())) {
            return;
        }
        List<TaskDO> activityTaskList = taskLocalCacheService.getTaskListByActivityId(activityDO.getId());
        // 基值指标
        List<IndicatorConfigDO> baseIndicatorConfigs = indicatorConfigDAO
                .queryActivityIndicatorConfigByType(activityId, IndicatorConfigTypeEnum.BASE.getValue());
        // 活动期指标(每天只跑数次)
        List<Long> activityIndicatorIds = Lists.newArrayList();
        if (statisticsPerformance) {
            List<IndicatorDO> activityIndicatorDOs = indicatorDAO.queryByTag(INDICATOR_PERFORMANCE_TAG, null);
            activityIndicatorIds =
                    activityIndicatorDOs.stream().map(BaseDO::getId).collect(Collectors.toList());
        }
        List<Long> finalActivityIndicatorIds = activityIndicatorIds;
        // 解析活动需要几个维度的数据
        List<StatisticsDimensionBO> statisticsDimensions =
                resolveStatisticsDimension(activityDO, activityTaskList, baseIndicatorConfigs, config);
        // 奖励类型
        List<Long> awardTypes = Lists.newArrayList();
        if (isLiteActivity(activityDO)) {
            // 兼容极速版活动
            LiteConfig liteConfig = getActivityLiteConfig(activityDO);
            List<IndustryAwardBO> awardConfigs = liteConfig.getAwardConfigs();
            if (CollectionUtils.isNotEmpty(awardConfigs)) {
                awardTypes =
                        awardConfigs.stream().map(awardConfig -> (long) awardConfig.getAwardType()).distinct().collect(Collectors.toList());
            }
        } else {
            List<AwardConfigDO> awardConfigList = awardConfigLocalCacheService.queryAwardConfigByActivityId(activityId);
            awardTypes =
                    awardConfigList.stream().map(e -> (long) e.getAwardType()).distinct().collect(Collectors.toList());
        }

        // 进行同步
        List<Long> finalAwardTypes = awardTypes;
        statisticsDimensions.forEach(dimension -> syncUserAggregationDataToActivityStatistics(activityId,
                dimension.getEntityType().getCode(), dimension.getEntityId(), dimension.getEntityParam(),
                dimension.getBaseIndicatorList(), finalActivityIndicatorIds, finalAwardTypes));
        perfSceneWithWatch(TASK_STATISTICS_USER_AGGREGATE, String.valueOf(activityId), sw);
    }
    @Override
    public List<StatisticsDimensionBO> resolveStatisticsDimension(ActivityDO activityDO, List<TaskDO> activityTaskList,
                                                                  List<IndicatorConfigDO> baseIndicatorList,
                                                                  StatisticsReviewConfig config) {
        List<StatisticsDimensionBO> dimensions = Lists.newArrayList();
        // 活动维度
        dimensions.add(ReviewConvert.buildActivityDimension(activityDO.getId(), baseIndicatorList));
        if (isLiteActivity(activityDO)) {
            return dimensions;
        }
        // 父任务维度
        List<TaskDO> parentTasks = activityTaskList.stream()
                .filter(e -> e.getParentTask() == 0).collect(Collectors.toList());
        if (TailNumberUtils.isOnFor(activityDO.getId(), config.getDataAggTaskDimensionTailNumber())) {
            List<StatisticsDimensionBO> taskDimensions = parentTasks.stream()
                    .map(TaskDO::getId).map(ReviewConvert::buildTaskDimension).collect(Collectors.toList());
            dimensions.addAll(taskDimensions);
        }
        // 策略运营活动专属维度
        if (activityDO.getSeriesType() != ACTIVITY_STRATEGY_ADMIN.getValue()) {
            return dimensions;
        }
        // 子活动维度
        Map<Integer, List<TaskDO>> subActivityMap = parentTasks.stream()
                .collect(Collectors.groupingBy(TaskDO::getStage));
        subActivityMap.forEach((subActivityOrder, taskList) -> {
            List<Long> parentTaskIdList = taskList.stream().map(BaseDO::getId).collect(Collectors.toList());
            long subActivityEntityId =
                    idGeneratorService.concatDimensionId(activityDO.getId(), SUB_ACTIVITY, subActivityOrder, 0, 0);
            dimensions.add(
                    ReviewConvert.buildSubActivityDimension(subActivityEntityId, parentTaskIdList, baseIndicatorList));
        });
        // 分层维度
        Map<Long, List<TaskDO>> parentChildTaskMap = activityTaskList.stream()
                .filter(e -> e.getParentTask() != 0).collect(Collectors.groupingBy(TaskDO::getParentTask));
        parentTasks.forEach(taskDO -> {
            int subOrder = taskDO.getStage();
            int layerOrder = taskDO.getPriority();
            long layerEntityId =
                    idGeneratorService.concatDimensionId(activityDO.getId(), LAYER, subOrder, layerOrder, 0);
            List<Long> baseIndicatorIdList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(baseIndicatorList)) {
                baseIndicatorIdList = baseIndicatorList.stream()
                        .filter(e -> e.getEntityId().equals(taskDO.getId())).map(IndicatorConfigDO::getIndicatorId)
                        .collect(Collectors.toList());
            }
            dimensions.add(ReviewConvert.buildLayerDimension(layerEntityId, taskDO.getId(), baseIndicatorIdList));
            // 周期维度
            Map<Integer, List<TaskDO>> periodMap = parentChildTaskMap.get(taskDO.getId()).stream()
                    .collect(Collectors.groupingBy(TaskDO::getPriority));
            List<Long> finalBaseIndicatorIdList = baseIndicatorIdList;
            periodMap.forEach((periodOrder, taskList) -> {
                List<Long> periodTaskIdList = taskList.stream().map(BaseDO::getId).collect(Collectors.toList());
                long periodEntityId = idGeneratorService
                        .concatDimensionId(activityDO.getId(), PERIOD, subOrder, layerOrder, periodOrder);
                dimensions.add(ReviewConvert.buildLayerPeriodDimension(periodEntityId, periodTaskIdList,
                        finalBaseIndicatorIdList));
            });
        });
        return dimensions;
    }
    @Override
    public void syncUserAggregationDataToActivityStatistics(long activityId, int entityType, long entityId,
                                                            Map<String, Object> entityParam,
                                                            List<Long> baseIndicatorList,
                                                            List<Long> activityIndicatorIds, List<Long> awardTypes) {
        StopWatch stopWatch = new StopWatch();
        // 统计指标聚合数据
        // look:dzg
        Map<String, Long> statisticsField = buildStatisticsIndicatorData(entityType, entityParam);
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        String baseIndicatorCondition = config.getStatisticsBaseIndicatorConditions();
        String performanceIndicatorCondition = config.getStatisticsActivityIndicatorConditions();
        String awardCondition = config.getStatisticsAwardConditions();
        String finalAwardCondition = config.getStatisticsAwardFinalConditions();
        // 统计基值指标数据
        Map<String, Long> baseIndicatorField =
                buildStatisticsIndicatorData(entityType, entityParam, baseIndicatorList, baseIndicatorCondition);
        // 活动期指标
        Map<String, Long> activityIndicatorField =
                buildStatisticsIndicatorData(entityType, entityParam, activityIndicatorIds,
                        performanceIndicatorCondition);
        // 初始奖励发放聚合数据
        Map<String, Long> awardField =
                buildStatisticsIndicatorData(entityType, entityParam, awardTypes, awardCondition);
        // 最终奖励发放聚合数据
        Map<String, Long> awardFinalField =
                buildStatisticsIndicatorData(entityType, entityParam, awardTypes, finalAwardCondition);
        // 原始记录
        StatisticsDO originStatisticsDO =
                statisticsDAO.queryStatisticsDimensionRecord(activityId, entityType, entityId);
        StatisticsDO updateParam = new StatisticsDO();
        updateParam.setActivityId(activityId);
        updateParam.setEntityType(entityType);
        updateParam.setEntityId(entityId);
        Map<String, Object> aggregationInfoMap = Maps.newHashMap();
        if (originStatisticsDO != null && StringUtils.isNotBlank(originStatisticsDO.getAggregationInfo())) {
            aggregationInfoMap = fromJson(originStatisticsDO.getAggregationInfo());
        }
        aggregationInfoMap.putAll(statisticsField);
        aggregationInfoMap.putAll(baseIndicatorField);
        aggregationInfoMap.putAll(activityIndicatorField);
        aggregationInfoMap.putAll(awardField);
        aggregationInfoMap.putAll(awardFinalField);
        updateParam.setAggregationInfo(toJSON(aggregationInfoMap));
        statisticsRecordService.insertOrUpdateStatisticsRecord(originStatisticsDO, updateParam);
        perfSceneWithWatch(TASK_STATISTICS_USER_AGGREGATE, String.valueOf(entityId), stopWatch);
    }

    private Map<String, Long> buildStatisticsIndicatorData(int entityType,
                                                           Map<String, Object> entityParam,
                                                           List<Long> indicatorIdList, String conditionsTemplate) {
        StopWatch stopWatch = new StopWatch();
        if (CollectionUtils.isEmpty(indicatorIdList)) {
            return Maps.newHashMap();
        }
        Map<String, Long> res = Maps.newHashMap();
        for (Long indicatorId : indicatorIdList) {
            Map<String, Object> variables = Maps.newHashMap();
            variables.put("indicatorId", indicatorId);
            StringSubstitutor sub = new StringSubstitutor(variables);
            String conditions = sub.replace(conditionsTemplate);
            IndicatorStatisticsConfigBO statisticsConfigBO = fromJSON(conditions, IndicatorStatisticsConfigBO.class);
            Long value = buildSingleIndicatorStatisticsData(indicatorId, statisticsConfigBO, entityType,
                    entityParam);
            // 存储
            if (value != null) {
                res.put(statisticsConfigBO.getSaveField(), value);
            }
        }
        perfSceneWithWatch(TASK_STATISTICS_USER_AGGREGATE, "动态指标聚合", stopWatch);
        return res;
    }

    private Map<String, Long> buildStatisticsIndicatorData(int entityType, Map<String, Object> entityParam) {
        StopWatch stopWatch = new StopWatch();
        // 统计指标集合
        List<IndicatorDO> statisticsIndicator =
                indicatorDAO.queryIndicator(null, Lists.newArrayList(STATISTICS.getValue()));
        if (CollectionUtils.isEmpty(statisticsIndicator)) {
            return Maps.newHashMap();
        }
        // 每个指标对应一个queryBuild
        Map<String, Long> statisticsField = Maps.newHashMap();
        for (IndicatorDO indicatorDO : statisticsIndicator) {
            if (StringUtils.isBlank(indicatorDO.getStatisticsConfig())) {
                log.warn("[活动维度统计] 统计指标无查询配置，indicator:{}", toJSON(indicatorDO));
                continue;
            }
            IndicatorStatisticsConfigBO statisticsConfigBO =
                    fromJSON(indicatorDO.getStatisticsConfig(), IndicatorStatisticsConfigBO.class);
            Long value = buildSingleIndicatorStatisticsData(indicatorDO.getId(), statisticsConfigBO, entityType,
                    entityParam);
            // 存储
            if (value != null) {
                statisticsField.put(statisticsConfigBO.getSaveField(), value);
            }
        }
        perfSceneWithWatch(TASK_STATISTICS_USER_AGGREGATE, "统计指标", stopWatch);
        return statisticsField;
    }

    public Long buildSingleIndicatorStatisticsData(long indicatorId, IndicatorStatisticsConfigBO statisticsConfigBO,
                                                   int entityType, Map<String, Object> entityParam) {
        StopWatch stopWatch = new StopWatch();
        // 构建queryBuild
        List<StatisticsConditionBO> conditions = statisticsConfigBO.getConditions();
        if (CollectionUtils.isEmpty(conditions)) {
            log.warn("[活动维度统计] 统计指标无查询条件配置，indicator:{}", toJSON(indicatorId));
            return null;
        }
        conditions = conditions.stream()
                .filter(e -> isNotEmpty(e.getBelongDimension()) && e.getBelongDimension().contains(entityType))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(conditions)) {
            log.warn("[活动维度统计] 统计指标无实体查询条件配置，indicator:{}", toJSON(indicatorId));
            return null;
        }
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilder(conditions, entityParam);
        String aggJson = buildAggJson(statisticsConfigBO.getAggType(),
                statisticsConfigBO.getAggField(), statisticsConfigBO.getAggJson(),
                statisticsConfigBO.getSaveField(), statisticsConfigBO.isNestedQuery(), conditions, entityParam);
        // 查询
        long result = statisticsEsDAO.queryStatisticsData(KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ,
                queryBuilder, aggJson, statisticsConfigBO.getSaveField());
        perfSceneWithWatch(TASK_STATISTICS_USER_AGGREGATE, "单指标聚合", stopWatch);
        return result;
    }

    @Override
    public void syncTaskPredictAwardInfoToEs(long userId, long activityId, ForecastDataBO forecastDataBO) {
        if (forecastDataBO == null || CollectionUtils.isEmpty(forecastDataBO.getTaskInfo())) {
            return;
        }
        // 查询ES记录的反查时间
        StatisticsEsDO originRecord = statisticsEsDAO.queryStatisticsUserData(userId, activityId);
        // 未初始化就有变动消息不处理
        if (originRecord == null) {
            log.info("[同步预估数据到ES] ES记录未初始化不处理，userId:{}, activityId:{}", userId, activityId);
            perfScene(SCHEDULE_STATISTICS_FORECAST_DATA_SYNC, String.valueOf(activityId), "no.record");
            return;
        }
        List<ForecastTaskInfo> taskInfo = forecastDataBO.getTaskInfo();
        List<StatisticsForecastInfoBO> forecastInfos = taskInfo.stream()
                .map(ReviewConvert::convertStatisticsForecastInfo).collect(Collectors.toList());
        Map<String, Object> forecastInfoMap = Maps.newHashMap();
        forecastInfoMap.put(FORECAST_INFO, forecastInfos);
        try {
            boolean success = statisticsEsDAO.insertOnDuplicateUpdate(userId, activityId, forecastInfoMap);
            if (success) {
                perfSuccess(SCHEDULE_STATISTICS_FORECAST_DATA_SYNC, String.valueOf(activityId));
            } else {
                // 解锁
                perfFail(SCHEDULE_STATISTICS_FORECAST_DATA_SYNC, String.valueOf(activityId));
            }
        } catch (Exception e) {
            perfException(SCHEDULE_STATISTICS_FORECAST_DATA_SYNC, String.valueOf(activityId),
                    e.getClass().getSimpleName());
        }
    }

    @Override
    public DynamicTableBO getSingleDimensionReviewDetail(long activityId, int subActivityOrder,
                                                         int layerOrder, int periodOrder, int pageNo, int pageSize,
                                                         List<StatisticsConditionBO> queryConditions) {
        // 翻译成任务ID查询条件
        TaskDimensionInfoBO taskDimensionInfo =
                activityConfigService.getTaskByDimensionOrder(activityId, subActivityOrder, layerOrder, periodOrder);
        TaskDO layerParentTask = taskDimensionInfo.getLayerTask();
        List<Long> periodChildTaskIdList = taskDimensionInfo.getPeriodTask().stream()
                .map(BaseDO::getId).collect(Collectors.toList());
        // 表头
        long layerParentId = taskDimensionInfo.getLayerTask().getId();
        List<Long> periodTaskIdList = taskDimensionInfo.getPeriodTask().stream()
                .map(BaseDO::getId).collect(Collectors.toList());
        List<IndicatorConfigDO> indicatorConfigList =
                indicatorLocalCacheService.queryActivityIndicatorConfig(activityId);
        List<HeaderConfig> headerConfigs =
                buildDynamicTable(STATISTICS_USER_DETAIL, layerParentId, periodTaskIdList, indicatorConfigList);
        // 构建查询条件
        if (BooleanUtils.isNotTrue(reviewPeriodFieldQueryRbSwitch.get()) && CollectionUtils.isNotEmpty(periodTaskIdList)) {
            queryConditions.add(queryBuildFactory.buildNestedTermsCondition(TASK_ID, TASK_INFO, periodTaskIdList));
        } else {
            queryConditions.add(queryBuildFactory.buildNestedTermCondition(TASK_ID, TASK_INFO,
                    layerParentTask.getId()));
        }
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilder(queryConditions, null);
        // 翻页查询商家数据
        EsQueryResponse<Map<String, Object>> pageData =
                statisticsEsDAO.pageQueryUserData(queryBuilder, pageNo, pageSize);
        // 获取指标数据
        List<IndicatorDO> indicatorDOS = indicatorDAO.queryAll();
        List<Map<String, Object>> resolveData = pageData.getData().stream()
                .map(e -> resolveUserDetailData(e, taskDimensionInfo, indicatorDOS)).collect(Collectors.toList());
        // 填充实时数据
        List<Long> userIdList = pageData.getData().stream().map(e -> MapUtils.getLong(e, USER_ID))
                .distinct().collect(Collectors.toList());
        Map<Long, UserReviewDetailBO> userDataMap =
                batchGetUserAggregationData(userIdList, layerParentTask, periodChildTaskIdList);
        // 转换
        List<Map<String, Object>> data = resolveData.stream()
                .map(e -> convertUserReviewDetailBO(e, userDataMap.get(MapUtils.getLong(e, USER_ID)), headerConfigs))
                .collect(Collectors.toList());
        // 组装
        DynamicTableBO res = new DynamicTableBO();
        res.setData(data);
        res.setTotal(pageData.getTotal());
        res.setHeader(headerConfigs);
        return res;
    }

    @Override
    public DynamicTableBO getSingleUserReviewDetail(long userId, long activityId,
                                                    List<StatisticsConditionBO> queryConditions) {
        // 资格
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return convertEmptyDynamicTableBO();
        }
        List<Long> userRegisterTaskList = userRegistrationRecords.stream()
                .filter(e -> e.getEntityType().equals(TASK.getCode()))
                .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());
        // 构建查询条件
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilder(queryConditions, null);
        // 查询商家ES数据
        EsQueryResponse<Map<String, Object>> pageData = statisticsEsDAO.pageQueryUserData(queryBuilder, 1, 1);
        if (CollectionUtils.isEmpty(pageData.getData())) {
            return convertEmptyDynamicTableBO();
        }
        Map<String, Object> userEsData = pageData.getData().get(0);
        // 指标
        List<IndicatorDO> indicatorDOS = indicatorDAO.queryAll();
        Map<Long, IndicatorDO> indicatorMap = indicatorDAO.queryAll().stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        // 任务记录
        List<UserTaskRecordDO> userTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false);
        // 指标记录
        List<IndicatorRecordDO> userIndicatorRecordList =
                userIndicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        // 奖励记录
        List<UserAwardRecordDO> userAwardRecordList =
                userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
        // 奖励配置
        List<AwardConfigDO> awardConfigList = awardConfigLocalCacheService.queryAwardConfigByActivityId(activityId);
        Map<Long, AwardConfigDO> awardConfigMap = awardConfigList.stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        // 审批配置
        List<UserAuditRecordDO> userAuditRecordList = userAuditRecordDAO.listUserAuditRecord(userId, activityId);
        // 指标配置
        List<Long> indicatorConfigIdList =
                userIndicatorRecordList.stream().map(IndicatorRecordDO::getConfigId).collect(Collectors.toList());
        List<IndicatorConfigDO> indicatorConfigList = new ArrayList<>(indicatorLocalCacheService
                .batchQueryIndicatorConfigById(indicatorConfigIdList).values());
        // 风控数据
        Tuple2<Map<Long, String>, UserRiskResult> riskData =
                getUserRiskAggregationData(userId, userRegistrationRecords);
        // 表格表头
        List<HeaderConfig> dynamicHeaderConfigs =
                buildDynamicTable(STATISTICS_USER_DETAIL, 0, null, indicatorConfigList);
        // 子活动拆分
        List<SubActivityDimensionBO> userDimensionList =
                activityConfigService.buildUserActivityDimensionInfo(activityId, userRegisterTaskList);
        // 数据解析
        List<Map<String, Object>> data = Lists.newArrayList();
        userDimensionList.forEach(userSubActivityDimensionBO -> {
            LayerDimensionBO userLayerDimension = userSubActivityDimensionBO.getLayerList().get(0);
            userLayerDimension.getPeriodList().forEach(userPeriodDimension -> {
                TaskDimensionInfoBO taskDimensionInfo =
                        buildTaskDimensionInfo(userSubActivityDimensionBO, userLayerDimension, userPeriodDimension);
                Map<String, Object> userResolveData =
                        resolveUserDetailData(userEsData, taskDimensionInfo, indicatorDOS);
                List<UserTaskRecordDO> periodTaskRecord = userTaskRecordList.stream()
                        .filter(e -> e.getTaskId().equals(userLayerDimension.getParentTaskId())
                                || userPeriodDimension.getPeriodTaskIdList().contains(e.getTaskId()))
                        .collect(Collectors.toList());
                UserReviewDetailBO userReviewDetailBO = buildUserReviewDetailBO(periodTaskRecord,
                        userIndicatorRecordList, userAwardRecordList, indicatorMap, awardConfigMap,
                        userAuditRecordList);
                userReviewDetailBO =
                        mergeUserAggregationData(userReviewDetailBO, riskData, userLayerDimension.getParentTaskId());
                data.add(convertUserReviewDetailBO(userResolveData, userReviewDetailBO, dynamicHeaderConfigs));
            });
        });
        // 组装
        return convertDynamicTableBO(dynamicHeaderConfigs, data);
    }

    private TaskDimensionInfoBO buildTaskDimensionInfo(SubActivityDimensionBO subActivityDimension,
                                                       LayerDimensionBO layerDimension,
                                                       PeriodDimensionBO periodDimension) {
        TaskDO userLayerTask = taskLocalCacheService.getTaskByTaskId(layerDimension.getParentTaskId());
        List<TaskDO> periodTask =
                taskLocalCacheService.batchGetTaskByTaskId(periodDimension.getPeriodTaskIdList());
        TaskDimensionInfoBO taskDimensionInfo = new TaskDimensionInfoBO();
        taskDimensionInfo.setSubActivityName(subActivityDimension.getSubActivityName());
        taskDimensionInfo.setLayerName(layerDimension.getLayerName());
        taskDimensionInfo.setPeriodName(periodDimension.getPeriodName());
        taskDimensionInfo.setLayerTask(userLayerTask);
        taskDimensionInfo.setPeriodTask(periodTask);
        return taskDimensionInfo;
    }
    /**
     * 特殊解析
     */
    @Override
    public Map<String, Object> resolveUserDetailData(Map<String, Object> data, TaskDimensionInfoBO taskDimensionInfo,
                                                     List<IndicatorDO> indicatorDOS) {
        Map<Long, IndicatorDO> indicatorDOMap = indicatorDOS.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));
        // 规则解析
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        String ruleCode = config.getUserDetailResolveRuleCode();
        Map<String, Object> context = new HashMap<>();
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("userData", toJSON(data));
        ruleContext.put("dimension", toJSON(taskDimensionInfo));
        ruleContext.put("indicatorDOMap", toJSON(indicatorDOMap));
        context.put("context", ruleContext);
        DataResult<Object> dataResult = ruleExecuteFacadeMixService.executeRule(ruleCode, null, context);
        if (dataResult == null || dataResult.getData() == null) {
            log.error("[自定义规则校验] 规则计算结果为空 context:{}", toJSON(context));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "规则计算结果为空");
        }
        Object resultJson = dataResult.getData();
        RuleExecuteResult ruleExecuteResult = fromJSON(resultJson, RuleExecuteResult.class);
        if (ruleExecuteResult.getResult() != 1) {
            log.error("[商达用户详情数据解析] 规则执行失败，context:{}, res:{}", toJSON(context),
                    toJSON(ruleExecuteResult));
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return ruleExecuteResult.getData();
    }

    @Override
    public List<HeaderConfig> buildDynamicTable(String scene, long layerParentTaskId, List<Long> periodTaskIdList,
                                                List<IndicatorConfigDO> indicatorConfigs) {
        DynamicTableHeaderConfig tableHeaderConfig = getDynamicTableHeaderConfig(scene);
        // 固定表头设置
        List<HeaderConfig> headerConfigs = new ArrayList<>(tableHeaderConfig.getHeaderConfigs());
        // 动态表头构建
        List<Long> indicatorIdList = indicatorConfigs.stream()
                .map(IndicatorConfigDO::getIndicatorId).distinct().collect(Collectors.toList());
        Map<Long, IndicatorDO> indicatorMap = indicatorLocalCacheService.queryTaskIndicators(indicatorIdList);
        // 任务信息
        List<TaskDO> periodTaskList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(periodTaskIdList)) {
            periodTaskList = taskLocalCacheService.batchGetTaskByTaskId(periodTaskIdList);
        }
        // 规则解析
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        String ruleCode = config.getDynamicHeaderBuildRuleCode();
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("scene", scene);
        ruleContext.put("indicatorConfigs", toJSON(indicatorConfigs));
        ruleContext.put("indicatorMap", toJSON(indicatorMap));
        ruleContext.put("layerParentTaskId", String.valueOf(layerParentTaskId));
        ruleContext.put("periodTaskList", toJSON(periodTaskList));
        Map<String, Object> context = new HashMap<>();
        context.put("context", ruleContext);
        DataResult<Object> dataResult = ruleExecuteFacadeMixService.executeRule(ruleCode, null, context);
        if (dataResult == null || dataResult.getData() == null) {
            log.error("[商达用户详情动态表头构建] 规则计算结果为空 context:{}", toJSON(context));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "规则计算结果为空");
        }
        Object resultJson = dataResult.getData();
        RuleExecuteResult ruleExecuteResult = fromJSON(resultJson, RuleExecuteResult.class);
        if (ruleExecuteResult.getResult() != 1) {
            log.error("[商达用户详情动态表头构建] 规则执行失败，context:{}, res:{}", toJSON(context),
                    toJSON(ruleExecuteResult));
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        String ruleData = ruleExecuteResult.getDataStr();
        List<HeaderConfig> ruleHeadConfig = fromJSON(ruleData, List.class, HeaderConfig.class);
        headerConfigs.addAll(ruleHeadConfig);
        return headerConfigs;
    }

    @Override
    @SneakyThrows
    public Map<Long, UserReviewDetailBO> batchGetUserAggregationData(List<Long> userIdList, TaskDO layerParentTask,
                                                                     List<Long> periodChildTaskIdList) {
        Map<Long, UserReviewDetailBO> res = new ConcurrentHashMap<>();
        CountDownLatch latch = new CountDownLatch(userIdList.size());
        for (long userId : userIdList) {
            USER_REVIEW_DATA_FILL_EXECUTOR.submit(() -> {
                try {
                    UserReviewDetailBO userReviewDetailBO =
                            getUserAggregationData(userId, layerParentTask, periodChildTaskIdList);
                    if (userReviewDetailBO != null) {
                        res.put(userId, userReviewDetailBO);
                    }
                } catch (Exception e) {
                    log.error("[复盘构建用户实时聚合信息] 异常！userId:{}", userId, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        return res;
    }
    @Override
    public UserReviewDetailBO getUserAggregationData(long userId, TaskDO layerParentTask,
                                                     List<Long> periodChildTaskIdList) {
        long activityId = layerParentTask.getActivityId();
        long layerParentTaskId = layerParentTask.getId();
        // 资格
        List<UserRegistrationRecordDO> userRegistrationRecords =
                userRegistrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
        // 风控数据
        Tuple2<Map<Long, String>, UserRiskResult> riskData =
                getUserRiskAggregationData(userId, userRegistrationRecords);
        // 活动数据
        UserReviewDetailBO userReviewDetailBO =
                getUserActivityAggregationData(userId, activityId, layerParentTaskId, periodChildTaskIdList);
        return mergeUserAggregationData(userReviewDetailBO, riskData, layerParentTaskId);
    }

    private UserReviewDetailBO mergeUserAggregationData(UserReviewDetailBO userActivityAggregationData,
                                                        Tuple2<Map<Long, String>, UserRiskResult> riskData,
                                                        long layerParentTaskId) {
        // 报名风控原因
        String registrationRiskReason = riskData._1.get(layerParentTaskId);
        // 领取风控
        String drawRiskDesc = MY_NO;
        // 领取风控原因
        String drawRiskReason = null;
        UserReviewDetailBO userReviewDetailBO = userActivityAggregationData;
        // 没有领取
        if (userActivityAggregationData == null) {
            userReviewDetailBO = new UserReviewDetailBO();
            drawRiskDesc = riskData._2.getRiskDesc();
            drawRiskReason = riskData._2.getRiskReason();
        }
        userReviewDetailBO.setRegistrationRiskReason(registrationRiskReason);
        userReviewDetailBO.setDrawRiskDesc(drawRiskDesc);
        userReviewDetailBO.setDrawRiskReason(drawRiskReason);
        return userReviewDetailBO;
    }

    private Tuple2<Map<Long, String>, UserRiskResult> getUserRiskAggregationData(long userId,
                                                                                 List<UserRegistrationRecordDO> userRegistrationRecords) {
        Map<Long, String> registrationRiskReasonMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userRegistrationRecords)) {
            registrationRiskReasonMap = userRegistrationRecords.stream()
                    .filter(e -> e.getEntityType().equals(TASK.getCode()))
                    .filter(e -> e.getStatus().equals(UserRegistrationStatusEnum.RISK.getCode()))
                    .collect(Collectors
                            .toMap(UserRegistrationRecordDO::getEntityId, CommonResolver::getRegistrationReason));
        }
        // 实时风控原因
        UserRiskResult userRiskResult = riskControlFetchService.getUserRiskResult(userId, ACTIVITY_PROGRESS_QUERY_1);
        return new Tuple2<>(registrationRiskReasonMap, userRiskResult);
    }

    private UserReviewDetailBO getUserActivityAggregationData(long userId, long activityId, long layerParentTaskId,
                                                              List<Long> periodChildTaskIdList) {
        // 任务记录
        List<UserTaskRecordDO> userTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, false);
        if (CollectionUtils.isEmpty(userTaskRecordList)) {
            return null;
        }
        userTaskRecordList = userTaskRecordList.stream()
                .filter(e -> e.getTaskId() == layerParentTaskId || periodChildTaskIdList.contains(e.getTaskId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userTaskRecordList)) {
            return null;
        }
        // 指标记录
        List<IndicatorRecordDO> userIndicatorRecordList =
                userIndicatorRecordDAO.listUserRecordOfActivity(userId, activityId);
        Map<Long, IndicatorDO> indicatorMap = indicatorDAO.queryAll().stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        // 奖励记录
        List<UserAwardRecordDO> userAwardRecordList =
                userAwardRecordDAO.listUserActivityRecord(userId, activityId, false);
        // 审批配置
        List<UserAuditRecordDO> userAuditRecordList = userAuditRecordDAO.listUserAuditRecord(userId, activityId);
        // 奖励配置
        List<AwardConfigDO> awardConfigList = awardConfigLocalCacheService.queryAwardConfigByActivityId(activityId);
        Map<Long, AwardConfigDO> awardConfigMap = awardConfigList.stream()
                .collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        return buildUserReviewDetailBO(userTaskRecordList, userIndicatorRecordList, userAwardRecordList, indicatorMap,
                awardConfigMap, userAuditRecordList);
    }

    private UserReviewDetailBO buildUserReviewDetailBO(List<UserTaskRecordDO> userTaskRecordList,
                                                       List<IndicatorRecordDO> userIndicatorRecordList,
                                                       List<UserAwardRecordDO> userAwardRecordList,
                                                       Map<Long, IndicatorDO> indicatorMap,
                                                       Map<Long, AwardConfigDO> awardConfigMap,
                                                       List<UserAuditRecordDO> userAuditRecordList) {
        if (CollectionUtils.isEmpty(userTaskRecordList)) {
            return null;
        }
        List<Long> userTaskIdList =
                userTaskRecordList.stream().map(UserTaskRecordDO::getTaskId).distinct().collect(Collectors.toList());
        Map<Long, TaskDO> taskMap = taskLocalCacheService.listTaskById(userTaskIdList);
        // 指标过滤
        userIndicatorRecordList = userIndicatorRecordList.stream()
                .filter(e -> userTaskIdList.contains(e.getEntityId())).collect(Collectors.toList());
        // 奖励过滤
        userAwardRecordList = userAwardRecordList.stream()
                .filter(e -> userTaskIdList.contains(e.getEntityId())).collect(Collectors.toList());
        Map<String, UserAuditRecordDO> userAuditRecordMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userAuditRecordList)) {
            userAuditRecordMap = userAuditRecordList.stream()
                    .collect(Collectors.toMap(UserAuditRecordDO::getSubjectId, Function.identity()));
        }
        Map<String, UserAuditRecordDO> finalUserAuditRecordMap = userAuditRecordMap;
        // 组装
        List<UserTaskReviewDetailBO> userTaskReviewDetails = userTaskRecordList.stream()
                .map(e -> convertUserTaskReviewDetailBO(e, taskMap.get(e.getTaskId()))).collect(Collectors.toList());
        List<UserIndicatorReviewDetailBO> userIndicatorReviewDetails = userIndicatorRecordList.stream()
                .map(e -> convertUserIndicatorReviewDetailBO(e, taskMap.get(e.getEntityId()),
                        indicatorMap.get(e.getIndicatorId()))).collect(Collectors.toList());
        List<UserAwardReviewDetailBO> userAwardReviewDetails = userAwardRecordList.stream()
                .map(e -> convertUserAwardReviewDetailBO(e, awardConfigMap.get(e.getAwardConfigId()),
                        finalUserAuditRecordMap.get(e.getUniqueId())))
                .collect(Collectors.toList());
        UserReviewDetailBO res = new UserReviewDetailBO();
        res.setTaskInfo(userTaskReviewDetails);
        res.setIndicatorInfo(userIndicatorReviewDetails);
        res.setAwardInfo(userAwardReviewDetails);
        return res;
    }

    @SneakyThrows
    @Override
    public void exportActivitySellerReviewDetail(long activityId, long scheduleId, List<String> selectHeads,
                                                 String operator, SellerExportConditionBO sellerExportCondition) {
        // 活动信息
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        // 子活动拆分
        List<SubActivityDimensionBO> dimensionList = activityConfigService.buildActivityDimensionInfo(activityId);
        // 基础参数
        SellerDetailParam param = new SellerDetailParam();
        param.setActivityId(activityId);
        param.setBizType(ExportFileBizTypeEnum.STATISTICS_DETAIL);
        param.setScheduleId(scheduleId);
        param.setOperator(operator);
        param.setSelectHeads(selectHeads);
        param.setSellerExportCondition(sellerExportCondition);
        param.setExportOutSource(sellerExportCondition.getExportOutSource());
        // 每个子活动一堆excel
        CountDownLatch latch = new CountDownLatch(dimensionList.size());
        Map<Integer, List<String>> subActivityFileMap = new ConcurrentHashMap<>();
        AtomicBoolean executeException = new AtomicBoolean(false);
        for (SubActivityDimensionBO dimension : dimensionList) {
            USER_EXPORT_EXECUTOR.submit(() -> {
                try {
                    param.setFileId(String.valueOf(dimension.getSubActivityOrder()));
                    SellerDetailParam doXlsParam = buildSellerDetailParam(param, dimension);
                    subActivityFileMap.put(dimension.getSubActivityOrder(), fileExportFactory.doXls(doXlsParam));
                } catch (Exception e) {
                    log.error("[创建导出excel] 异常！activityId:{}, dimension:{}", activityId, toJSON(dimension), e);
                    executeException.set(true);
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        if (executeException.get()) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "创建导出excel异常");
        }
        List<String> fileUrls = subActivityFileMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        String fileUrl = fileExportFactory.export(param, fileUrls);
        // 消息号通知
        adminKimService.sendActivityExportMsg(fileUrl, activityDO, operator);
    }

    private SellerDetailParam buildSellerDetailParam(SellerDetailParam baseParam, SubActivityDimensionBO dimension) {
        SellerDetailParam param = new SellerDetailParam();
        param.setActivityId(baseParam.getActivityId());
        param.setBizType(baseParam.getBizType());
        param.setFileId(baseParam.getFileId());
        param.setScheduleId(baseParam.getScheduleId());
        param.setOperator(baseParam.getOperator());
        param.setSelectHeads(baseParam.getSelectHeads());
        param.setDimensionInfo(dimension);
        param.setSellerExportCondition(baseParam.getSellerExportCondition());
        param.setExportOutSource(baseParam.getExportOutSource());
        return param;
    }

    @Override
    public void exportActivityReviewData(long activityId, int subActivityOrder, int layerOrder, String operator) {
        int entityType = layerOrder == 0 ? SUB_ACTIVITY.getCode() : LAYER.getCode();
        entityType = subActivityOrder == 0 ? ACTIVITY.getCode() : entityType;
        List<ChildrenTaskDimensionBO> childrenTaskDimensionBOS =
                getChildrenTaskByOrder(activityId, subActivityOrder, layerOrder, 0, entityType);
        // 子活动拆分
        List<SubActivityDimensionBO> dimensionList = activityConfigService.buildActivityDimensionInfo(activityId);
        if (CollectionUtils.isEmpty(childrenTaskDimensionBOS)) {
            throw new BizException(PARAM_INVALID, "活动数据不存在");
        }
        ActivityReviewParam param = new ActivityReviewParam();
        param.setOperator(operator);
        param.setFileId(String.valueOf(System.currentTimeMillis()));
        param.setBizType(ExportFileBizTypeEnum.ACTIVITY_REVIEW);
        param.setActivityId(activityId);
        param.setSubActivityOrder(subActivityOrder);
        param.setLayerOrder(layerOrder);
        param.setChildrenTaskDimensionBOS(childrenTaskDimensionBOS);
        param.setDimensionList(dimensionList);
        // 活动信息
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        // 获取excel
        List<String> fileUrls = fileExportFactory.doXls(param);

        String fileUrl = fileExportFactory.export(param, fileUrls);
        // 消息号通知
        adminKimService.sendActivityExportMsg(fileUrl, activityDO, operator);
    }

    @Override
    public List<ChildrenTaskDimensionBO> getChildrenTaskByOrder(long activityId, int subActivityOrder, int layerOrder,
                                                                int period, int entityType) {
        List<ChildrenTaskDimensionBO> childrenTaskDimensionBOS = getActivityChildrenDimensionBO(activityId);
        return childrenTaskDimensionBOS.stream()
                .filter(e -> e.getEntityType() == entityType)
                .filter(e -> subActivityOrder <= 0 || e.getSubActivityOrder() == subActivityOrder)
                .filter(e -> layerOrder <= 0 || e.getLayerOrder() == layerOrder)
                .filter(e -> period <= 0 || e.getPeriodOrder() == period)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, SellerPolicySignUpSummary> querySellerActivitySignUpSummary(StaffPolicyRepoConfig policyRepoConfig,
                                                                                 List<Long> activityIdList,
                                                                                 SlrBelongInfoCondition belongInfoCondition) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return Maps.newHashMap();
        }
        // 筛选条件构造
        List<StatisticsConditionBO> queryConditions = buildSlrFilterConditions(activityIdList, belongInfoCondition,
                policyRepoConfig);
        // 剔除风控资格
        queryConditions.add(queryBuildFactory.buildTermCondition("risk", 0));
        // 构建查询条件
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderContainsShould(queryConditions, null);
        // 获取配置
        String signUpInfoAggregationsStr = policyRepoConfig.getPolicySignUpInfoAggregations();
        String policySignUpTermsAggPath = policyRepoConfig.getPolicySignUpTermsAggPath();
        String aggJson = buildAggJsonByConfig(signUpInfoAggregationsStr);
        // 聚合查询
        Map<String, TermsAggData> aggregationsMap =
                statisticsEsDAO.queryTermsAggData(KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ, queryBuilder, aggJson);
        // 解析结果
        return parseTermsResult(policySignUpTermsAggPath, aggregationsMap);
    }

    @Override
    public Map<Long, SellerDimStatisticSummary> querySellerSignUpSummary(StaffPolicyRepoConfig policyRepoConfig,
                                                                         List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Maps.newHashMap();
        }
        // 基础查询条件构造
        List<StatisticsConditionBO> queryConditions = statisticsReviewV2Service.baseAggQueryBuild(policyRepoConfig,
                userIdList);
        // 商家报名聚合信息
        Map<String, TermsAggData> sellerSignUpAggregationsMap =
                statisticsReviewV2Service.getSellerSignUpAggMap(policyRepoConfig, userIdList, queryConditions);
        // 商家进行中数量
        Long progressActivityCnt = statisticsReviewV2Service.getProgressActivityCnt(policyRepoConfig, queryConditions);
        // 待完成活动数：进行中活动数-已完成活动数
        // draw = 1 & taskInfo.isSuccess = 1 terms userId
        Map<String, TermsAggData> sellerSignUpFinishAggregationsMap =
                statisticsReviewV2Service.getSignUpFinishAggDataMap(policyRepoConfig, queryConditions);
        String sellerSignUpTermsAggPath = policyRepoConfig.getSellerSignUpTermsAggPath();
        String sellerSignUpFinishTermsAggPath = policyRepoConfig.getSellerSignUpFinishTermsAggPath();
        return parseTermsResult(sellerSignUpTermsAggPath, sellerSignUpAggregationsMap, sellerSignUpFinishTermsAggPath
                , sellerSignUpFinishAggregationsMap, progressActivityCnt);
    }

    private static String buildAggJsonByConfig(String sellerSignUpInfoAggregations) {
        List<TermsAggregationBuild> signUpInfoAggregations = fromJSON(sellerSignUpInfoAggregations, List.class,
                TermsAggregationBuild.class);
        // 构造聚合json
        return buildAggJson(signUpInfoAggregations);
    }

    @Override
    public EsQueryResponse<Map<String, Object>> pageQueryReviewInfo(StaffPolicyRepoConfig policyRepoConfig,
                                                                    List<Long> activityIdList,
                                                                    SlrBelongInfoCondition belongInfoCondition,
                                                                    int pageNo, int pageSize) {
        // 筛选条件构造
        List<StatisticsConditionBO> queryConditions = buildSlrFilterConditions(activityIdList, belongInfoCondition,
                policyRepoConfig);
        // 构建查询条件
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderContainsShould(queryConditions, null);
        return statisticsEsDAO.pageQueryUserData(queryBuilder, pageNo, pageSize);
    }

    @Override
    public List<StatisticsConditionBO> buildSlrFilterConditions(List<Long> activityIdList,
                                                                SlrBelongInfoCondition belongInfoCondition,
                                                                StaffPolicyRepoConfig policyRepoConfig) {
        List<StatisticsConditionBO> queryConditions = Lists.newArrayList();
        // 活动ID批量
        if (CollectionUtils.isNotEmpty(activityIdList)) {
            if (activityIdList.size() == 1) {
                queryConditions.add(queryBuildFactory.buildTermCondition(ACTIVITY_ID, activityIdList.get(0)));
            } else {
                queryConditions.add(queryBuildFactory.buildTermsCondition(ACTIVITY_ID, activityIdList));
            }
        }
        if (belongInfoCondition != null && belongInfoCondition.conditionIsNotEmpty()) {
            boolean jumpPermissionCheck = false;
            if (policyRepoConfig != null && policyRepoConfig.getPolicyPermissionConfig() != null) {
                jumpPermissionCheck = BooleanUtils.isTrue(policyRepoConfig
                        .getPolicyPermissionConfig().hitWhiteLogic(belongInfoCondition.getStaffCode()));
            }
            String staffCode = belongInfoCondition.getStaffCode();
            if (SiteTagEnum.SMB.equals(belongInfoCondition.getSiteTag())) {
                statisticsReviewV2Service.buildSellerSmbBelongQueryCondition(belongInfoCondition, staffCode,
                        jumpPermissionCheck, queryConditions);
            } else {
                statisticsReviewV2Service.buildSellerIndustryBelongQueryCondition(belongInfoCondition, staffCode,
                        jumpPermissionCheck, queryConditions);
            }
            Long userId = belongInfoCondition.getUserId();
            if (userId != null && userId > 0) {
                queryConditions.add(queryBuildFactory.buildTermCondition("userId", userId));
            }
            String userNick = belongInfoCondition.getUserNick();
            if (StringUtils.isNotBlank(userNick)) {
                queryConditions.add(queryBuildFactory.buildMatchCondition("userName", userNick));
            }
            PolicyDrawType drawType = belongInfoCondition.getDrawType();
            if (drawType != null) {
                queryConditions.add(queryBuildFactory.buildTermCondition("draw",
                        PolicyDrawType.DRAW.equals(drawType) ? 1 : 0));
            }
        }
        return queryConditions;
    }
    private List<ChildrenTaskDimensionBO> getActivityChildrenDimensionBO(long activityId) {
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (null == activityDO) {
            throw new BizException(PARAM_INVALID, "活动不存在");
        }
        List<TaskDO> taskDOList = taskLocalCacheService.getTaskListByActivityId(activityId);
        List<ChildrenTaskDimensionBO> dimensions = Lists.newArrayList();
        // 活动维度
        dimensions.add(buildActivityDimension(activityId, taskDOList));
        if (isLiteActivity(activityDO)) {
            // 极速版活动直接返回
            return dimensions;
        }
        // 父任务维度
        List<TaskDO> parentTasks = taskDOList.stream()
                .filter(e -> e.getParentTask() == 0).collect(Collectors.toList());
        // 子活动维度
        Map<Integer, List<TaskDO>> subActivityMap = parentTasks.stream()
                .collect(Collectors.groupingBy(TaskDO::getStage));
        subActivityMap.forEach((subActivityOrder, taskList) -> {
            List<Long> parentTaskIdList = taskList.stream().map(BaseDO::getId).collect(Collectors.toList());
            List<Long> childrenTask = taskDOList.stream()
                    .filter(e -> parentTaskIdList.contains(e.getParentTask())).map(BaseDO::getId)
                    .collect(Collectors.toList());
            dimensions.add(buildSubActivityDimension(activityId, subActivityOrder, childrenTask));
        });
        // 分层维度
        Map<Long, List<TaskDO>> parentChildTaskMap = taskDOList.stream()
                .filter(e -> e.getParentTask() != 0).collect(Collectors.groupingBy(TaskDO::getParentTask));
        parentTasks.forEach(taskDO -> {
            int subOrder = taskDO.getStage();
            int layerOrder = taskDO.getPriority();
            List<Long> childrenTask = taskDOList.stream()
                    .filter(e -> e.getParentTask().longValue() == taskDO.getId()).map(BaseDO::getId)
                    .collect(Collectors.toList());
            dimensions.add(buildLayerDimension(activityId, subOrder, layerOrder, childrenTask));
            // 周期维度
            Map<Integer, List<TaskDO>> periodMap = parentChildTaskMap.get(taskDO.getId()).stream()
                    .collect(Collectors.groupingBy(TaskDO::getPriority));
            periodMap.forEach((periodOrder, taskList) -> {
                List<Long> periodTaskIdList = taskList.stream().map(BaseDO::getId).collect(Collectors.toList());
                dimensions.add(
                        buildLayerPeriodDimension(activityId, subOrder, layerOrder, periodOrder, periodTaskIdList));
            });
        });
        return dimensions;
    }

    private ChildrenTaskDimensionBO buildActivityDimension(long activityId, List<TaskDO> taskDOS) {
        ChildrenTaskDimensionBO childrenTaskDimensionBO = new ChildrenTaskDimensionBO();
        childrenTaskDimensionBO.setSubActivityOrder(0);
        childrenTaskDimensionBO.setLayerOrder(0);
        childrenTaskDimensionBO.setPeriodOrder(0);
        childrenTaskDimensionBO.setEntityType(EntityTypeEnum.ACTIVITY.getCode());
        childrenTaskDimensionBO.setEntityId(activityId);
        if (CollectionUtils.isNotEmpty(taskDOS)) {
            childrenTaskDimensionBO.setChildrenTaskIds(taskDOS.stream().map(BaseDO::getId).collect(Collectors.toList()));
        }
        return childrenTaskDimensionBO;
    }

    /**
     * 构建子活动维度信息
     */
    public ChildrenTaskDimensionBO buildSubActivityDimension(long activityId, int subActivityOrder,
                                                             List<Long> childrenTaskIds) {
        ChildrenTaskDimensionBO childrenTaskDimensionBO = new ChildrenTaskDimensionBO();
        long subActivityEntityId =
                idGeneratorService.concatDimensionId(activityId, SUB_ACTIVITY, subActivityOrder, 0, 0);
        childrenTaskDimensionBO.setSubActivityOrder(subActivityOrder);
        childrenTaskDimensionBO.setLayerOrder(0);
        childrenTaskDimensionBO.setPeriodOrder(0);
        childrenTaskDimensionBO.setEntityType(EntityTypeEnum.SUB_ACTIVITY.getCode());
        childrenTaskDimensionBO.setEntityId(subActivityEntityId);
        childrenTaskDimensionBO.setChildrenTaskIds(childrenTaskIds);
        return childrenTaskDimensionBO;
    }

    public ChildrenTaskDimensionBO buildLayerDimension(long activityId, int subActivityOrder, int layerOrder,
                                                       List<Long> childrenTaskIds) {
        long layerEntityId =
                idGeneratorService.concatDimensionId(activityId, LAYER, subActivityOrder, layerOrder, 0);
        ChildrenTaskDimensionBO childrenTaskDimensionBO = new ChildrenTaskDimensionBO();
        childrenTaskDimensionBO.setSubActivityOrder(subActivityOrder);
        childrenTaskDimensionBO.setLayerOrder(layerOrder);
        childrenTaskDimensionBO.setPeriodOrder(0);
        childrenTaskDimensionBO.setEntityType(EntityTypeEnum.LAYER.getCode());
        childrenTaskDimensionBO.setEntityId(layerEntityId);
        childrenTaskDimensionBO.setChildrenTaskIds(childrenTaskIds);
        return childrenTaskDimensionBO;
    }

    public ChildrenTaskDimensionBO buildLayerPeriodDimension(long activityId, int subActivityOrder, int layerOrder,
                                                             int periodOrder,
                                                             List<Long> taskIdList) {
        long periodEntityId = idGeneratorService
                .concatDimensionId(activityId, PERIOD, subActivityOrder, layerOrder, periodOrder);
        ChildrenTaskDimensionBO childrenTaskDimensionBO = new ChildrenTaskDimensionBO();
        childrenTaskDimensionBO.setSubActivityOrder(subActivityOrder);
        childrenTaskDimensionBO.setLayerOrder(layerOrder);
        childrenTaskDimensionBO.setPeriodOrder(periodOrder);
        childrenTaskDimensionBO.setEntityType(EntityTypeEnum.PERIOD.getCode());
        childrenTaskDimensionBO.setEntityId(periodEntityId);
        childrenTaskDimensionBO.setChildrenTaskIds(taskIdList);
        return childrenTaskDimensionBO;
    }

    private List<StatisticsTaskInfoBO> buildStatisticsTask(List<UserTaskRecordDO> userTaskRecordList,
                                                           List<IndicatorRecordDO> userIndicatorRecordList,
                                                           List<StatisticsTaskInfoBO> originTaskInfos) {
        if (CollectionUtils.isEmpty(userTaskRecordList)) {
            perfScene(MQ_USER_STATISTICS_SYNC_CONSUME, "indicator.not.generate");
            return Lists.newArrayList();
        }
        // 原taskInfo记录
        Map<Long, StatisticsTaskInfoBO> originTaskInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(originTaskInfos)) {
            originTaskInfoMap =
                    originTaskInfos.stream().collect(Collectors.toMap(StatisticsTaskInfoBO::getTaskId, e -> e));
        }
        // 父子隔离
        List<UserTaskRecordDO> userParentTaskList = userTaskRecordList.stream()
                .filter(e -> e.getParentId() == 0).collect(Collectors.toList());
        Map<Long, List<UserTaskRecordDO>> userChildTaskMap = userTaskRecordList.stream()
                .filter(e -> e.getParentId() != 0).collect(Collectors.groupingBy(UserTaskRecordDO::getParentId));
        // 指标映射
        Map<Long, List<IndicatorRecordDO>> userIndicatorMap = userIndicatorRecordList.stream()
                .collect(Collectors.groupingBy(IndicatorRecordDO::getEntityId));
        // 父任务组装
        List<StatisticsTaskInfoBO> taskInfoList = Lists.newArrayList();
        for (UserTaskRecordDO parentTask : userParentTaskList) {
            List<UserTaskRecordDO> childTaskList = userChildTaskMap.get(parentTask.getTaskId());
            StatisticsTaskInfoBO parentTaskInfo =
                    MapUtils.getObject(originTaskInfoMap, parentTask.getTaskId(), new StatisticsTaskInfoBO());
            parentTaskInfo.setTaskId(parentTask.getTaskId());
            parentTaskInfo.setParentTaskId(parentTask.getParentId());
            parentTaskInfo.setStatus(parentTask.getStatus());
            // 任意一个子活动完成算完成
            parentTaskInfo.setIsSuccess(calcStatisticsParentTaskStatus(childTaskList));
            // 子任务组装
            long isParentPredictSuccess = 0;
            for (UserTaskRecordDO childTask : childTaskList) {
                StatisticsTaskInfoBO childTaskInfo =
                        MapUtils.getObject(originTaskInfoMap, childTask.getTaskId(), new StatisticsTaskInfoBO());
                childTaskInfo.setTaskId(childTask.getTaskId());
                childTaskInfo.setParentTaskId(childTask.getParentId());
                childTaskInfo.setStatus(childTask.getStatus());
                childTaskInfo.setIsSuccess(calcStatisticsChildTaskStatus(childTask));
                long isPredictSuccess = calcStatisticsChildTaskStatus(userIndicatorMap.get(childTask.getTaskId()));
                if (isPredictSuccess > 0) {
                    isParentPredictSuccess = 1L;
                }
                childTaskInfo.setIsPredictSuccess(isPredictSuccess);
                taskInfoList.add(childTaskInfo);
            }
            parentTaskInfo.setIsPredictSuccess(isParentPredictSuccess);
            taskInfoList.add(parentTaskInfo);
        }
        return taskInfoList;
    }
    private long calcStatisticsChildTaskStatus(List<IndicatorRecordDO> userIndicatorRecords) {
        if (CollectionUtils.isEmpty(userIndicatorRecords)) {
            return 0;
        }
        List<ForecastIndicatorInfo> forecastIndicatorInfos = userIndicatorRecords.stream()
                .map(StatisticsConverter::convertIndicatorInfo2Forecast).collect(Collectors.toList());
        List<Long> canFinishTaskList = statisticsCalcService.calcUserCanFinishTask(forecastIndicatorInfos);
        if (CollectionUtils.isEmpty(canFinishTaskList)) {
            return 0;
        }
        return 1;
    }
    /**
     * 父任务下是否有子任务完成
     */
    private long calcStatisticsParentTaskStatus(List<UserTaskRecordDO> childTaskList) {
        long successChildTaskNum = childTaskList.stream()
                .filter(e -> e.getStatus().equals(UserTaskStatusEnum.SUCCESS.getValue())).count();
        return successChildTaskNum > 0 ? 1 : 0;
    }
    /**
     * 父任务下是否有子任务完成
     */
    private long calcStatisticsChildTaskStatus(UserTaskRecordDO childTask) {
        return childTask.getStatus().equals(UserTaskStatusEnum.SUCCESS.getValue()) ? 1 : 0;
    }
}
