package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-18
 */
@Data
public class RegistrationCrowdConstantBO {

    /**
     * 人群动态事件 - 完成前置子活动Code
     */
    public static final String DYNAMIC_EVENT_PRE_STAGE_CODE = "complete_pre_sub_activity";

    /**
     * 完成前置子活动 活动ID key
     */
    public static final String PRE_STAGE_ACTIVITY_ID_KEY = "activity_id";

    /**
     * 完成前置子活动 活动Order key
     */
    public static final String PRE_STAGE_SUB_ACTIVITY_ORDER_KEY = "sub_activity_id";

    /**
     * 完成前置子活动默认活动ID
     */
    public static final String PRE_STAGE_DEFAULT_ACTIVITY_ID = "0";
}
