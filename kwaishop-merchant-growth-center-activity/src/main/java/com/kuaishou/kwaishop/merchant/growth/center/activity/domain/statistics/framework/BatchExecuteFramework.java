package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework;

import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.BatchExecuteUserEventMsg;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-15
 */
public interface BatchExecuteFramework {
    /**
     * 提取处理用户人群
     */
    Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event);

    /**
     * 用户批量切分转发
     */
    void partitionCrowdSendMsg(Set<Long> totalCrowd, BatchExecuteEvent event);

    /**
     * 批量用户处理
     */
    void batchExecute(BatchExecuteUserEventMsg eventMsg);

    /**
     * 获取处理类型
     */
    BatchExecuteType getBatchExecuteType();
}
