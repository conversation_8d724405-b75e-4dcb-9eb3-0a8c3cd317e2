package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.supplier;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.BaseCalcExtDataSupplyParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums.BaseCalcExtDataTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Component
@Slf4j
public class BaseCalcExtDataSupplier {

    @Autowired
    private List<BaseCalcExtDataSupplyStrategy> strategies;

    private Map<BaseCalcExtDataTypeEnum, BaseCalcExtDataSupplyStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = strategies.stream().collect(
                Collectors.toMap(BaseCalcExtDataSupplyStrategy::getType, Function.identity(), (k1, k2) -> k1));
    }

    public Map<String, Object> supply(BaseCalcExtDataSupplyParam param) {
        BaseCalcExtDataTypeEnum type = param.getType();

        Map<String, Object> result = Maps.newHashMap();

        if (MapUtils.isEmpty(strategyMap)) {
            return result;
        }

        BaseCalcExtDataSupplyStrategy strategy = strategyMap.get(type);
        if (Objects.isNull(strategy)) {
            return result;
        }

        return strategy.supply(param);
    }
}
