package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildHandler;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-09
 */
@Slf4j
@Lazy
@Component
public class MatchQueryBuild implements QueryBuildHandler {
    @Override
    public QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam) {
        // match查询
        if (StringUtils.isBlank(condition.getFieldName())) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "match查询条件字段不能为空");
        }
        ConditionValueTypeEnum valueTypeEnum = ConditionValueTypeEnum.of(condition.getConditionValueType());
        if (valueTypeEnum.equals(ConditionValueTypeEnum.UNKNOWN)) {
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "无法识别match查询条件值类型");
        }
        String conditionValue = condition.getConditionValue();
        return new MatchQueryBuilder(condition.getFieldName(), conditionValue);
    }

    @Override
    public ConditionTypeEnum conditionType() {
        return ConditionTypeEnum.MATCH;
    }
}
