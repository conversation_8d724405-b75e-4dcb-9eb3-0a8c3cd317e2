package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.NOTIFICATION_CHANGE_PROCESS;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccessWithWatch;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.ecyrd.speed4j.StopWatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.processor.NotificationChangeProcessStrategy;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-26
 */
@Service
@Lazy
@Slf4j
public class NotificationChangeProcessor {

    @Autowired
    private NotificationPushBasicService notificationPushBasicService;

    @Autowired
    private List<NotificationChangeProcessStrategy> strategies;

    private Map<NotificationEntityTypeEnum, NotificationChangeProcessStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = strategies.stream().collect(Collectors.toMap(
                NotificationChangeProcessStrategy::getType, Function.identity(), (k1, k2) -> k1));
    }

    public void process(long activityId, List<Long> changeConfigList, long eventTime) {
        StopWatch sw = new StopWatch();
        List<NotificationPushConfigBO> notificationPushConfigs =
                notificationPushBasicService.getNotificationPushConfigsByIds(changeConfigList);

        if (CollectionUtils.isEmpty(notificationPushConfigs)) {
            log.info("[触达配置变更回刷消费] 没有有效的触达配置 activityId:{}, changeConfigList:{}, eventTime:{}",
                    activityId, toJSON(changeConfigList), eventTime);
            throw new BizException(PARAM_INVALID, String.format("没有有效的触达配置 changeConfigList:%s",
                    toJSON(changeConfigList)));
        }

        log.info("[触达配置变更回刷消费] 回刷触达配置 activityId:{}, notificationPushConfigs:{}, eventTime:{}",
                activityId, toJSON(notificationPushConfigs), eventTime);

        // 根据 entityType 聚合触达配置
        Map<NotificationEntityTypeEnum, List<NotificationPushConfigBO>> entityTypeMap = Maps.newHashMap();
        notificationPushConfigs.forEach(config -> {
            NotificationEntityTypeEnum type = NotificationEntityTypeEnum.of(config.getEntityType());
            List<NotificationPushConfigBO> configsOfCertainType =
                    entityTypeMap.getOrDefault(type, Lists.newArrayList());
            configsOfCertainType.add(config);
            entityTypeMap.put(type, configsOfCertainType);
        });

        // 遍历 entityType 聚合触达配置
        entityTypeMap.forEach((entityType, configs) -> {
            NotificationChangeProcessStrategy strategy = strategyMap.get(entityType);
            if (Objects.isNull(strategy)) {
                log.info("[触达配置变更回刷消费] 回刷策略不存在 entityType:{}", entityType);
                perfScene(NOTIFICATION_CHANGE_PROCESS, "回刷策略不存在", String.valueOf(entityType.getDesc()));
                return;
            }

            strategy.process(activityId, configs, eventTime);
        });

        log.info("[触达配置变更回刷消费] 回刷触达配置完成 activityId:{}, notificationPushConfigs:{}, eventTime:{}",
                activityId, toJSON(notificationPushConfigs), eventTime);
        perfSuccessWithWatch(NOTIFICATION_CHANGE_PROCESS, String.valueOf(activityId), sw);
    }
}
