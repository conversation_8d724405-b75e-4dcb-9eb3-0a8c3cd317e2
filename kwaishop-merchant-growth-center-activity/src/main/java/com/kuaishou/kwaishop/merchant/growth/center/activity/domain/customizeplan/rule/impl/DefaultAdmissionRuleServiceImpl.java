package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.rule.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.AdmissionRuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.AdmissionRuleEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.rule.CustomizePlanAdmissionRuleService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Component("defaultAdmissionRuleService")
@Slf4j
public class DefaultAdmissionRuleServiceImpl implements CustomizePlanAdmissionRuleService {

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Override
    public boolean hitAdmissionRule(long userId, AdmissionRuleConfig admissionRuleConfig) {
        String paramMapKey = getParamMapKey();
        List<Long> activityIdList = getActivityIdList(admissionRuleConfig.getConfigMap(), paramMapKey)
                .stream().map(Long::valueOf).collect(Collectors.toList());
        for (Long activityId : activityIdList) {
            List<UserRegistrationRecordBO> userRegistrationRecords =
                    userRegistrationService.queryUserRegistrationRecords(userId,
                            activityId, EntityTypeEnum.ACTIVITY, UserRegistrationStatusEnum.VALID);
            if (CollectionUtils.isEmpty(userRegistrationRecords)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public AdmissionRuleEnum getAdmissionRule() {
        return AdmissionRuleEnum.DEFAULT;
    }

    /**
     * 从参数Map中获取准入规则所需参数
     */
    private List<String> getActivityIdList(Map<String, Object> configMap, String paramMapKey) {
        if (MapUtils.isEmpty(configMap) || !configMap.containsKey(paramMapKey)) {
            log.error("[查询定制计划] 定制计划未配置准入参数, configMap:{}, paramMapKey:{}", toJSON(configMap),
                    paramMapKey);
            throw new BizException(SERVER_ERROR, "定制计划未配置准入参数");
        }
        Object paramValue = configMap.get(paramMapKey);
        if (!(paramValue instanceof List)) {
            return Lists.newArrayList();
        }
        return (List<String>) paramValue;
    }

    /**
     * 准入规则参数Key
     */
    public String getParamMapKey() {
        return "activityIdList";
    }
}
