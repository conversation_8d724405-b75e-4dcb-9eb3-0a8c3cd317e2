package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.datasource;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchDataSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.customize.rule.LaunchDataSource;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Component
@Slf4j
public class LaunchActivityConfigDataSource implements LaunchDataSource {

    @Resource
    private ActivityLocalCacheService activityLocalCacheService;

    @Override
    public LaunchDataSourceResultBO fetch(LaunchDataSourceContextBO param) {
        Long activityId = param.getActivityId();
        ActivityDO activity = activityLocalCacheService.queryActivityInfo(activityId);
        if (activity == null) {
            log.warn("[投放拉取活动配置数据] 活动不存在 param:{}", toJSON(param));
            return LaunchDataSourceResultBO.getDefaultResult();
        }

        LaunchActivityConfigData data = LaunchActivityConfigData.builder().activityConfig(activity).build();

        Map<String, Object> resMap = Maps.newHashMap();
        resMap.put(getDataSourceType().getType(), toJSON(data));
        return LaunchDataSourceResultBO.builder().resMap(resMap).build();
    }

    @Override
    public LaunchDataSourceResultBO degree(LaunchDataSourceContextBO param) {
        return LaunchDataSourceResultBO.getDefaultResult();
    }

    @Override
    public LaunchDataSourceTypeEnum getDataSourceType() {
        return LaunchDataSourceTypeEnum.ACTIVITY_CONFIG_DATA;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LaunchActivityConfigData {

        private ActivityDO activityConfig;
    }
}
