package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.entityType;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationPerfEnum.DELAY_NOTIFICATION_PUSH_CONSUMER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.DELAY_NOTIFICATION_PUSH_CONSUMER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_REGISTRATION_CONSUMER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.notificationTempSkipTaskStatusCheckActivityList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationEntityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 任务维度推送策略
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Slf4j
@Service
public class NotificationTaskEntityStrategyService extends AbstractNotificationEntityService {

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Override
    public NotificationEntityTypeEnum getNotificationStrategyEntityType() {
        return NotificationEntityTypeEnum.TASK;
    }

    @Override
    public boolean checkEntityStatus(long userId, NotificationPushConfigBO configBO) {
        int currentStatus = getCurrentStatus(userId, configBO);
        boolean flag = currentStatus == configBO.getEntityStatus();
        if (!flag) {
            log.info("[任务维度推送] 当前用户任务状态发生改变，取消推送 userId:{},"
                            + " configId:{}, currentStatus:{}, configStatus:{}",
                    userId, configBO.getId(), currentStatus, configBO.getEntityStatus());
        }
        return flag;
    }

    @Override
    public boolean checkEndTime(long userId, NotificationPushConfigBO configBO) {
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(configBO.getActivityId());
        if (activityDO == null) {
            log.error("[任务维度推送] activityDO is null, msg:{}", toJSON(configBO));
            throw new BizException(NOTIFICATION_REGISTRATION_CONSUMER_ERROR, "strategyBO is null");
        }
        // 当前时间大于活动结束时间
        if (System.currentTimeMillis() > activityDO.getEndTime()) {
            log.info("[任务维度推送] 当前活动已结束，推送取消, userId:{}, configBO:{}", userId, toJSON(configBO));
            return true;
        }
        return false;
    }

    private int getCurrentStatus(long userId, NotificationPushConfigBO configBO) {
        UserTaskRecordDO userTaskRecordDO =
                userTaskRecordDAO.queryUserTaskRecord(userId, configBO.getActivityId(), configBO.getEntityId(),
                        false);
        if (userTaskRecordDO == null) {

            // FIXME 对于加白活动，临时跳过异常的任务触达消息消费
            List<Long> activityIds = notificationTempSkipTaskStatusCheckActivityList.get();
            if (CollectionUtils.isNotEmpty(activityIds) && activityIds.contains(configBO.getActivityId())) {
                perfFail(DELAY_NOTIFICATION_PUSH_CONSUMER, "userTaskRecordDO.skip");
                log.warn("[任务维度推送] 临时跳过存量异常消息 userId:{}, config:{}", userId, toJSON(configBO));
                return UserTaskEventTypeEnum.UNKNOWN.getValue();
            }

            perfFail(DELAY_NOTIFICATION_PUSH_CONSUMER, "userTaskRecordDO.invalid");
            throw new BizException(DELAY_NOTIFICATION_PUSH_CONSUMER_ERROR, "用户任务记录获取失败-重试");
        }
        return convertTaskStatus(UserTaskStatusEnum.of(userTaskRecordDO.getStatus()));
    }

    private int convertTaskStatus(UserTaskStatusEnum taskStatus) {
        switch (taskStatus) {
            case PROCESSING:
                return UserTaskEventTypeEnum.DRAW.getValue();
            case RISK:
                return UserTaskEventTypeEnum.RISK.getValue();
            default:
                return UserTaskEventTypeEnum.UNKNOWN.getValue();
        }
    }
}
