package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
public enum NotificationConfigPeriodTypeEnum {
    UNKNOWN(0, "未知"),
    ABSOLUTE(1, "绝对"),
    RELATIVE(2, "相对"),
    ;

    private int val;

    private String desc;

    NotificationConfigPeriodTypeEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static NotificationConfigPeriodTypeEnum of(int type) {
        for (NotificationConfigPeriodTypeEnum periodType : NotificationConfigPeriodTypeEnum.values()) {
            if (periodType.getVal() == type) {
                return periodType;
            }
        }
        return UNKNOWN;
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
