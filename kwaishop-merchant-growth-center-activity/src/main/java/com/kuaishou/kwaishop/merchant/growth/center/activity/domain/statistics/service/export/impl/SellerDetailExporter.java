package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.resolver.CommonResolver.getRegistrationReason;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.service.impl.StaffPolicyRepoBizServiceImpl.getStaffPolicyRepoConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_PROGRESS_QUERY_1;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.DRAW_RISK_DESC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.DRAW_RISK_GROUP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.DRAW_RISK_REASON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.MY_YES;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.REGISTRATION_RISK_DESC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.REGISTRATION_RISK_GROUP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.REGISTRATION_RISK_REASON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STAFF_POLICY_USER_DETAIL_EXPORT_EXTEND;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STATISTICS_USER_DETAIL_EXPORT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.TASK_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.TASK_INFO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.USER_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.exportHeadSelectConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.staffPolicyRepoConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils.getDynamicTableHeaderConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.delaycalc.model.DelayRuleConstants.USER_ID_KEY;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.DynamicTableHeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.HeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerExportConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SlrBelongInfoCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.BaseExportParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportElement;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportHeadField;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportHeadModuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.SellerDetailParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.SellerDetailQuery;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.SellerDetailResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportOutSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.AbstractFileExporter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.FileExportService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.SellerTagConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.StaffPolicyRepoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.LayerDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.PeriodDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.TaskDimensionInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.ActivityConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.StatisticsEsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.SellerProfileFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.UserRiskResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.es.EsQueryResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsEsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsTaskInfoBO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-06
 */
@Lazy
@Slf4j
@Service
public class SellerDetailExporter extends AbstractFileExporter<SellerDetailQuery>
        implements FileExportService {

    @Autowired
    private IndicatorDAO indicatorDAO;

    @Autowired
    private StatisticsEsDAO statisticsEsDAO;

    @Autowired
    private QueryBuildFactory queryBuildFactory;

    @Autowired
    private ActivityConfigService activityConfigService;

    @Autowired
    private StatisticsReviewService statisticsReviewService;

    @Autowired
    private RiskControlFetchService riskControlFetchService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Autowired
    private SellerProfileFetchService sellerProfileFetchService;

    @Override
    protected ExportResponse listElement(BaseExportParam param, SellerDetailQuery request) {
        SellerDetailParam sellerDetailParam = (SellerDetailParam) param;
        SellerDetailResponse res = new SellerDetailResponse();
        EsQueryResponse<Map<String, Object>> queryRes = statisticsEsDAO.scrollQueryUserData(request.getQueryBuilder(),
                request.getPageNo(), request.getPageSize(), request.getScrollId());
        List<Map<String, Object>> list = queryRes.getData();
        if (CollectionUtils.isNotEmpty(list)) {
            // 查询商家标签
            StaffPolicyRepoConfig policyRepoConfig = staffPolicyRepoConfig.getObject();
            List<SellerTagConfig> exportSellerTagList = policyRepoConfig.getExportSellerTagList();
            // 查询商家标签信息
            Map<Long, Map<String, String>> userProfileTagMap = getUserProfileTagInfo(policyRepoConfig, list,
                    exportSellerTagList, sellerDetailParam);
            res.setData(list.stream()
                    .map(e -> {
                        // 填充商家标签信息
                        fillUserProfileTagInfo(e, userProfileTagMap, exportSellerTagList);
                        return toElement(e, sellerDetailParam);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        reportProgress(sellerDetailParam.getScheduleId(), queryRes);
        res.setCursor(queryRes.getScrollId());
        return res;
    }

    /**
     * 查询商家标签信息
     *
     * @param policyRepoConfig
     * @param list
     * @param exportSellerTagList
     * @param sellerDetailParam
     * @return
     */
    private Map<Long, Map<String, String>> getUserProfileTagInfo(StaffPolicyRepoConfig policyRepoConfig,
                                                                 List<Map<String, Object>> list,
                                                                 List<SellerTagConfig> exportSellerTagList,
                                                                 SellerDetailParam sellerDetailParam) {
        Map<Long, Map<String, String>> userProfileTagMap = Maps.newHashMap();
        try {
            ExportOutSourceEnum exportOutSource = sellerDetailParam.getExportOutSource();
            // 非小二库场景直接返回
            if (!ExportOutSourceEnum.STAFF_POLICY.equals(exportOutSource)) {
                return userProfileTagMap;
            }
            if (BooleanUtils.isNotTrue(policyRepoConfig.getSellerProfileQueryCloseSwitch())) {
                List<Long> userIds = list.stream()
                        .map(data -> MapUtils.getLong(data, USER_ID_KEY))
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(userIds)) {
                    // 分批查询商家标签
                    userProfileTagMap = sellerProfileFetchService.parallelGetSelectionIdsInfo(userIds,
                            exportSellerTagList);
                }
            }
            return userProfileTagMap;
        } catch (Exception e) {
            log.error("getUserProfileTagInfo error, list:{}", list.size(), e);
            return userProfileTagMap;
        }
    }

    /**
     * 填充商家标签信息
     *
     * @param e
     * @param finalUserProfileTagMap
     * @param exportSellerTagList
     */
    private static void fillUserProfileTagInfo(Map<String, Object> e,
                                               Map<Long, Map<String, String>> finalUserProfileTagMap,
                                               List<SellerTagConfig> exportSellerTagList) {
        if (MapUtils.isEmpty(finalUserProfileTagMap) || CollectionUtils.isEmpty(exportSellerTagList)) {
            return;
        }
        Long userId = MapUtils.getLong(e, USER_ID_KEY);
        try {
            if (userId != null) {
                Map<String, String> userTagMap = finalUserProfileTagMap.get(userId);
                exportSellerTagList.forEach(tag -> {
                    if (MapUtils.isEmpty(userTagMap) || StringUtils.isBlank(userTagMap.get(tag.getShowKey()))) {
                        return;
                    }
                    String showKey = tag.getShowKey();
                    e.put(showKey, userTagMap.get(showKey));
                });
            }
        } catch (Exception ex) {
            log.error("fillUserProfileTagInfo error, userId:{}", userId, ex);
        }
    }

    private ExportElement toElement(Map<String, Object> queryResult, SellerDetailParam sellerDetailParam) {
        // 分层信息
        long activityId = sellerDetailParam.getActivityId();
        int subActivityOrder = sellerDetailParam.getDimensionInfo().getSubActivityOrder();
        List<LayerDimensionBO> layerDimensions = sellerDetailParam.getDimensionInfo().getLayerList();
        StatisticsEsDO statisticsEsDO = fromJSON(toJSON(queryResult), StatisticsEsDO.class);
        List<Long> taskIdList = statisticsEsDO.getTaskInfo().stream()
                .map(StatisticsTaskInfoBO::getTaskId).collect(Collectors.toList());
        List<LayerDimensionBO> userLayerDimensions = layerDimensions.stream()
                .filter(e -> taskIdList.contains(e.getParentTaskId())).collect(Collectors.toList());
        if (userLayerDimensions.size() != 1) {
            log.error("[复盘数据导出] 无法定位分层信息 statisticsEsDO:{}", toJSON(statisticsEsDO));
            return null;
        }
        LayerDimensionBO userLayerDimension = userLayerDimensions.get(0);
        long layerParentTaskId = userLayerDimension.getParentTaskId();
        String sheetName =
                userLayerDimension.getLayerName() + String.format("(分层%s)", userLayerDimension.getLayerOrder());
        // 行数据解析
        Map<String, Object> resolveData =
                resolveQueryResult(queryResult, activityId, subActivityOrder, userLayerDimension);
        List<HeaderConfig> headerConfigs = sellerDetailParam.getHeader().get(layerParentTaskId);
        // 要导出报名时风控，实时查
        fillUserRiskInfo(activityId, layerParentTaskId, headerConfigs, resolveData);
        ExportElement element = new ExportElement();
        List<String> res = Lists.newArrayList();
        for (HeaderConfig headerConfig : headerConfigs) {
            res.add(String.valueOf(resolveData.getOrDefault(headerConfig.getField(), "")));
        }
        element.setData(res);
        element.setSheet(sheetName);
        return element;
    }

    private void fillUserRiskInfo(long activityId, long layerParentTaskId, List<HeaderConfig> headerConfigs,
                                  Map<String, Object> resolveData) {
        // 要导出报名时风控，实时查
        long userId = MapUtils.getLong(resolveData, USER_ID, 0L);
        if (headerConfigs.stream().anyMatch(e -> DRAW_RISK_GROUP.equals(e.getGroup()))) {
            UserRiskResult userRiskResult =
                    riskControlFetchService.getUserRiskResult(userId, ACTIVITY_PROGRESS_QUERY_1);
            resolveData.put(DRAW_RISK_DESC, userRiskResult.getRiskDesc());
            resolveData.put(DRAW_RISK_REASON, userRiskResult.getRiskReason());
        }
        // 要导出下发风控，判断要不要反查风控原因
        if (headerConfigs.stream().anyMatch(e -> REGISTRATION_RISK_GROUP.equals(e.getGroup()))) {
            if (userId > 0 && MY_YES.equals(MapUtils.getString(resolveData, REGISTRATION_RISK_DESC))) {
                UserRegistrationRecordDO recordDO = userRegistrationRecordDAO
                        .queryRecordByUnique(activityId, userId, EntityTypeEnum.TASK, layerParentTaskId, false);
                String riskReason = getRegistrationReason(recordDO);
                if (StringUtils.isNotBlank(riskReason)) {
                    resolveData.put(REGISTRATION_RISK_REASON, riskReason);
                }
            }
        }
    }

    /**
     * 解析对象
     */
    private Map<String, Object> resolveQueryResult(Map<String, Object> queryResult, long activityId,
                                                   int subActivityOrder, LayerDimensionBO layerDimension) {
        TaskDimensionInfoBO taskDimensionInfoBO = activityConfigService
                .getTaskByDimensionOrder(activityId, subActivityOrder, layerDimension.getLayerOrder(), 0);
        List<IndicatorDO> indicatorDOS = indicatorDAO.queryAll();
        return statisticsReviewService.resolveUserDetailData(queryResult, taskDimensionInfoBO, indicatorDOS);
    }

    @Override
    protected String genFileName(BaseExportParam param) {
        SellerDetailParam sellerDetailParam = (SellerDetailParam) param;
        return String.format("活动%s子活动%s商达详情导出", sellerDetailParam.getActivityId(),
                sellerDetailParam.getDimensionInfo().getSubActivityName());
    }

    @Override
    protected String genZipFileName() {
        return "活动商达详情导出";
    }

    @Override
    protected Map<String, List<List<String>>> getHeader(BaseExportParam param, SellerDetailQuery request) {
        SellerDetailParam sellerDetailParam = (SellerDetailParam) param;
        Map<String, List<List<String>>> res = Maps.newHashMap();
        Map<Long, String> taskSheetMap = sellerDetailParam.getTaskSheetMap();
        sellerDetailParam.getHeader().forEach((layerTaskId, headers) -> {
            List<List<String>> header =
                    headers.stream().map(e -> Lists.newArrayList(e.getName())).collect(Collectors.toList());
            res.put(taskSheetMap.get(layerTaskId), header);
        });
        return res;
    }

    /**
     * 根据导出勾选项筛选
     * 普通属性就看选的里面有没有
     * 活动期表现判断，过程指标值判断，基期指标值判断
     */
    private boolean filterSelectHead(HeaderConfig headerConfig, List<String> selectHeads) {
        // 没选默认全部
        if (CollectionUtils.isEmpty(selectHeads)) {
            return true;
        }
        Map<String, List<ExportHeadModuleConfig>> selectConfigMap = exportHeadSelectConfig.getMap();
        List<ExportHeadModuleConfig> moduleConfigs = selectConfigMap.get(getBizType().getCode());
        // 选了没配都别导了
        if (CollectionUtils.isEmpty(moduleConfigs)) {
            return false;
        }
        moduleConfigs = fromJSON(toJSON(moduleConfigs), List.class, ExportHeadModuleConfig.class);
        List<String> propertyList = Lists.newArrayList();
        moduleConfigs.forEach(moduleConfig -> {
            List<String> propertyFieldList = moduleConfig.getModuleFields().stream()
                    .filter(ExportHeadField::isProperty).map(ExportHeadField::getFieldTag).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(propertyFieldList)) {
                propertyList.addAll(propertyFieldList);
            }
        });
        String needExistHead = headerConfig.getField();
        if (propertyList.contains(headerConfig.getGroup())) {
            needExistHead = headerConfig.getGroup();
        }
        return selectHeads.contains(needExistHead);
    }

    @Override
    public ExportFileBizTypeEnum getBizType() {
        return ExportFileBizTypeEnum.STATISTICS_DETAIL;
    }

    @Override
    public List<String> doXls(BaseExportParam exportParam) {
        SellerDetailParam sellerDetailParam = (SellerDetailParam) exportParam;
        // 查询条件构造
        List<StatisticsConditionBO> queryConditions = buildExportQueryConditions(sellerDetailParam);
        // 动态表头
        Map<Long, List<HeaderConfig>> dynamicHeaders = buildSubActivityHeader(sellerDetailParam);
        // 过滤用户选项
        List<String> userSelectHead = sellerDetailParam.getSelectHeads();
        List<Long> keyList = new ArrayList<>(dynamicHeaders.keySet());
        keyList.forEach(key -> dynamicHeaders.put(key, dynamicHeaders.get(key).stream()
                .filter(e -> filterSelectHead(e, userSelectHead))
                .collect(Collectors.toList())));
        // 分层任务和sheet映射
        Map<Long, String> taskSheetMap = sellerDetailParam.getDimensionInfo().getLayerList().stream().collect(
                Collectors.toMap(LayerDimensionBO::getParentTaskId,
                        e -> e.getLayerName() + String.format("(分层%s)", e.getLayerOrder())));
        sellerDetailParam.setHeader(dynamicHeaders);
        sellerDetailParam.setTaskSheetMap(taskSheetMap);
        SellerDetailQuery sellerDetailQuery = new SellerDetailQuery();
        sellerDetailQuery.setPageNo(1);
        sellerDetailQuery.setPageSize(getDefaultPageSize(String.valueOf(sellerDetailParam.getActivityId())));
        sellerDetailQuery.setQueryBuilder(queryBuildFactory.buildQueryBuilderContainsShould(queryConditions, null));
        return doXls(exportParam, sellerDetailQuery);
    }

    /**
     * 导出条件构造
     *
     * @param sellerDetailParam
     * @return
     */
    private List<StatisticsConditionBO> buildExportQueryConditions(SellerDetailParam sellerDetailParam) {
        List<StatisticsConditionBO> queryConditions = Lists.newArrayList();
        SellerExportConditionBO sellerExportCondition = sellerDetailParam.getSellerExportCondition();
        Long activityId = sellerDetailParam.getActivityId();
        queryConditions.add(queryBuildFactory.buildNestedTermsCondition(TASK_ID, TASK_INFO,
                sellerDetailParam.getDimensionInfo().getParentTaskIdList()));
        SlrBelongInfoCondition slrBelongInfoCondition = null;
        if (sellerExportCondition != null) {
            slrBelongInfoCondition = sellerExportCondition.getSlrBelongInfoCondition();
        }
        StaffPolicyRepoConfig policyRepoConfig = getStaffPolicyRepoConfig();
        // 导出条件构造
        List<StatisticsConditionBO> filterConditions =
                statisticsReviewService.buildSlrFilterConditions(Collections.singletonList(activityId),
                        slrBelongInfoCondition, policyRepoConfig);
        queryConditions.addAll(filterConditions);
        return queryConditions;
    }

    private Map<Long, List<HeaderConfig>> buildSubActivityHeader(SellerDetailParam sellerDetailParam) {
        long activityId = sellerDetailParam.getActivityId();
        Map<Long, List<HeaderConfig>> layerHeaders = Maps.newHashMap();
        for (LayerDimensionBO layerDimensionBO : sellerDetailParam.getDimensionInfo().getLayerList()) {
            List<Long> periodTaskIdList = layerDimensionBO.getPeriodList().stream()
                    .map(PeriodDimensionBO::getPeriodTaskIdList).flatMap(List::stream).collect(Collectors.toList());
            List<IndicatorConfigDO> indicatorConfigList =
                    indicatorLocalCacheService.queryActivityIndicatorConfig(activityId);
            List<HeaderConfig> layerHeader = statisticsReviewService.buildDynamicTable(STATISTICS_USER_DETAIL_EXPORT,
                    layerDimensionBO.getParentTaskId(), periodTaskIdList, indicatorConfigList);
            // 如果是小二政策库，则额外加对应的商家标签查询导出字段
            if (ExportOutSourceEnum.STAFF_POLICY.equals(sellerDetailParam.getExportOutSource())) {
                DynamicTableHeaderConfig tableHeaderConfig =
                        getDynamicTableHeaderConfig(STAFF_POLICY_USER_DETAIL_EXPORT_EXTEND);
                if (tableHeaderConfig != null && CollectionUtils.isNotEmpty(tableHeaderConfig.getHeaderConfigs())) {
                    layerHeader.addAll(tableHeaderConfig.getHeaderConfigs());
                }
            }
            layerHeaders.put(layerDimensionBO.getParentTaskId(), layerHeader);
        }
        return layerHeaders;
    }
}
