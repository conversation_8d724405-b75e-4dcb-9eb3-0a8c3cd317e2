package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BasicFormulaCalcScaleConfig {

    /**
     * 指标ID-精度配置
     */
    private Map<String/*indicatorId*/, Integer/*scale*/> indicatorScaleConfigMap;
}
