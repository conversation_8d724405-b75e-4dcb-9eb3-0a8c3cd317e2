package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.supplier;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveCycleDuration;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.CYCLE_DURATION_KEY;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.BaseCalcExtDataSupplyParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums.BaseCalcExtDataTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Component
@Slf4j
public class BaseCalcCycleDurationSupplyStrategy implements BaseCalcExtDataSupplyStrategy {

    @Resource
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    public Map<String, Object> supply(BaseCalcExtDataSupplyParam param) {
        Long userId = param.getUserId();
        IndicatorConfigDO indicatorConfig = param.getIndicatorConfig();
        Map<String, Object> inputDataMap = param.getInputDataMap();

        long cycleDuration = 0L;
        if (indicatorConfig != null) {
            Long activityId = indicatorConfig.getActivityId();
            Long parentTaskId = indicatorConfig.getEntityId();

            TaskDO parentTask = taskLocalCacheService.getTaskByTaskId(parentTaskId);
            if (parentTask == null) {
                log.error("[基期计算补充任务周期] 子任务查询为空 userId:{}, activityId:{}, parentTaskId:{}",
                        userId, activityId, parentTaskId);
                throw new BizException(BasicErrorCode.SERVER_ERROR, "子任务查询为空");
            }

            cycleDuration = resolveCycleDuration(parentTask);
        } else {
            if (MapUtils.isEmpty(inputDataMap) || !inputDataMap.containsKey(CYCLE_DURATION_KEY)) {
                log.error("[基期计算补充任务周期] 周期时间为空 param:{}", toJSON(param));
                throw new BizException(BasicErrorCode.PARAM_INVALID, "周期时间为空");
            }
            cycleDuration = MapUtils.getLong(inputDataMap, CYCLE_DURATION_KEY, 0L);
            if (cycleDuration <= 0L) {
                log.error("[基期计算补充任务周期] 周期时间为空 param:{}", toJSON(param));
                throw new BizException(BasicErrorCode.PARAM_INVALID, "周期时间为空");
            }
        }


        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(CYCLE_DURATION_KEY, cycleDuration);

        return resultMap;
    }

    @Override
    public BaseCalcExtDataTypeEnum getType() {
        return BaseCalcExtDataTypeEnum.CYCLE_DURATION;
    }
}
