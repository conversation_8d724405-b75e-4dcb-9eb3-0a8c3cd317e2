package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.DapEstimationStrategyResult;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EstimationResultSaveBatchEvent extends BatchExecuteEvent {

    /**
     * 用户测算结果map
     */
    private Map<Long, DapEstimationStrategyResult> userStrategyResultMap;
}
