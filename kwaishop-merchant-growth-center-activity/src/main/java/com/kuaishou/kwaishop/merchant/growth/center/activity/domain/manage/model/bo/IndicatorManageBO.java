package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.AuditConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicatorManageBO {
    /**
     * 指标配置对象
     */
    private IndicatorConfigDO indicatorConfig;
    /**
     * 审批配置
     */
    private List<AuditConfigDO> auditConfig;
}
