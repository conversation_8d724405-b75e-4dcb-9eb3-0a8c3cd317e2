package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RiskUserRegistrationReDrawResult;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-14
 */
public interface RegistrationCacheService {

    /**
     * 添加shard到风控用户资格重新下发成功shard
     */
    void addShardIndexToReDrawSet(List<Integer> shards, String key);

    /**
     * 添加活动Id到风控用户资格重新下发活动IDSet中
     */
    void addActivityIdToReDrawSet(List<Long> activityIds, String key);

    /**
     * 添加用户Id到风控用户资格重新下发成功Set中
     */
    void addUserIdToReDrawSuccessSet(List<Long> userIds, String key);

    /**
     * 构建风控用户资格重新下发结果
     */
    List<RiskUserRegistrationReDrawResult> buildRegistrationReDrawResult(String key);
}
