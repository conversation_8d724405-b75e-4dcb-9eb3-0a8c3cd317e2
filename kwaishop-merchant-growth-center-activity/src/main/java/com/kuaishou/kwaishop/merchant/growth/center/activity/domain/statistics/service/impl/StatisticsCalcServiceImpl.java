package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.Indicator.RoiExtraIndicatorTypeEnum.ACTIVITY_PERIOD_FORECAST_SINGLE_FAN_LTV90;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.Indicator.RoiExtraIndicatorTypeEnum.BASE_PERIOD_SINGLE_FAN_LTV90;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.parseIndicatorStepCount;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveBasicConfigFromTaskExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter.convertIndicatorInfo2DTO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.SCHEDULE_STATISTICS_SYNC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRoiTypeEnum.FORECAST;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRoiTypeEnum.REVIEW;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum.TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.ACTIVITY_CONFIG_UN_EXPECT;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.summingLong;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.IndicatorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityCheckService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardValueBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordExtFieldBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ExpectAwardCalcContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ExpectAwardValueBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ExpectTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastIndicatorInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ForecastTaskInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsRoiTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRoiService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.CompleteConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityStatusChangeService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.es.StatisticsIndicatorInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.BaseInfo;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerAwardData;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerIndicatorData;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerSingleDimensionData;
import com.kuaishou.kwaishop.merchant.resource.center.client.enums.RoiCalculateOccasionEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-17
 */
@Slf4j
@Lazy
@Service
public class StatisticsCalcServiceImpl implements StatisticsCalcService {

    @Autowired
    private AwardCalcService awardCalcService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private StatisticsMsgProduceService statisticsMsgProduceService;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;

    @Autowired
    private UserActivityStatusChangeService userActivityStatusChangeService;

    @Autowired
    private RegistrationConverter registrationConverter;

    @Autowired
    private AdminActivityCheckService adminActivityCheckService;

    @Autowired
    private IndicatorConfigDAO indicatorConfigDAO;

    @Autowired
    private StatisticsRoiService statisticsRoiService;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private IndicatorBasicNewService indicatorBasicNewService;

    @Autowired
    private IndicatorCalcService indicatorCalcService;

    @Override
    public long calcForecastIndicatorValue(long indicatorId, long currentValue, long activityDays, long currentDays) {
        BigDecimal cv = BigDecimal.valueOf(currentValue);
        BigDecimal ads = BigDecimal.valueOf(activityDays);
        BigDecimal cds = BigDecimal.valueOf(currentDays);
        if (indicatorId == IndicatorEnum.RANK.getValue()) {
            return cv.longValue();
        }
        return cv.multiply(ads).divide(cds, RoundingMode.UP).longValue();
    }

    @Override
    public int calcMaxReachStep(IndicatorRecordDO indicatorRecordDO, long forecastValue) {
       return indicatorCalcService.calcMaxReachStep(indicatorRecordDO, forecastValue);
    }

    @Override
    public int calcStatisticsRecordStatus(int userActivityStatus) {
        UserActivityStatusEnum userActivityStatusEnum = UserActivityStatusEnum.of(userActivityStatus);
        switch (userActivityStatusEnum) {
            case AUDITING:
            case SUCCESS:
                return StatisticsStatusEnum.SUCCESS.getValue();
            case STOP:
            case FAIL:
            case RISK:
                return StatisticsStatusEnum.FAIL.getValue();
            case PROCESSING:
                return StatisticsStatusEnum.PROCESSING.getValue();
            default:
                log.error("[计算统计记录状态] 非预期用户活动状态:{}", userActivityStatus);
                throw new BizException(BasicErrorCode.SERVER_ERROR, "非预期用户活动状态");
        }
    }

    @Override
    public Map<Integer, Long> calcSingleTaskPredictAward(long userId, long activityId, long taskId,
            List<ForecastIndicatorInfo> forecastIndicatorInfos, List<AwardConfigDO> awardConfigList,
            TaskDO taskConfig) {
        Map<Integer, List<AwardConfigDO>> awardTypeConfigMap = awardConfigList.stream()
                .collect(Collectors.groupingBy(AwardConfigDO::getAwardType));
        Map<Integer, Long> awardTypeTotalMap = Maps.newHashMap();
        awardTypeConfigMap.forEach((awardType, list) -> awardTypeTotalMap.put(awardType,
                list.stream().mapToLong(config -> awardCalcService
                        .calcUserPredictAwardValue(userId, activityId, taskConfig, config, forecastIndicatorInfos)
                        .getAwardValue()).sum()));
        return awardTypeTotalMap;
    }

    @Override
    public List<Long> calcStepTargetFromIndicatorExt(IndicatorRecordDO indicator) {
        String ext = indicator.getExt();
        if (StringUtils.isEmpty(ext)) {
            return Lists.newArrayList();
        }
        IndicatorRecordExtFieldBO recordExt = fromJSON(ext, IndicatorRecordExtFieldBO.class);
        // 阶梯的数量
        IndicatorConfigDO indicatorConfig =
                indicatorLocalCacheService.queryIndicatorConfigById(indicator.getConfigId());
        int stepCount = parseIndicatorStepCount(indicatorConfig);
        // 先支持10个层级
        List<Long> stepTargetList = Lists.newArrayList();
        for (int step = 1; step <= stepCount; step++) {
            String stepTargetKey = "step_target_" + step;
            long stepTarget = MapUtils.getLong(recordExt.getComponentValues(), stepTargetKey, -1L);
            if (stepTarget < 0) {
                break;
            }
            stepTargetList.add(stepTarget);
        }
        return stepTargetList;
    }

    @Override
    public List<Long> calcUserCanFinishTask(List<ForecastIndicatorInfo> forecastIndicatorInfos) {
        // 任务ID聚合
        Map<Long, List<ForecastIndicatorInfo>> taskGroupIndicatorInfos =
                forecastIndicatorInfos.stream().collect(Collectors.groupingBy(ForecastIndicatorInfo::getTaskId));
        List<Long> taskIdList =
                forecastIndicatorInfos.stream().map(ForecastIndicatorInfo::getTaskId).collect(Collectors.toList());
        List<TaskDO> taskDOS = taskLocalCacheService.batchGetTaskByTaskId(taskIdList);
        if (CollectionUtils.isEmpty(taskDOS)) {
            return Lists.newArrayList();
        }
        Map<Long, TaskDO> taskMetaMap = taskDOS.stream().collect(Collectors.toMap(BaseDO::getId, Function.identity()));
        List<Long> finishTaskList = Lists.newArrayList();
        taskGroupIndicatorInfos.forEach((taskId, list) -> {
            TaskDO taskDO = taskMetaMap.get(taskId);
            if (taskDO == null) {
                return;
            }
            String completeCondition = taskDO.getCompleteCondition();
            CompleteConditionBO completeConditionBO =
                    ObjectMapperUtils.fromJSON(completeCondition, CompleteConditionBO.class);
            List<IndicatorRecordDO> recordDOList = list.stream()
                    .map(StatisticsConverter::convertForecastIndicatorInfo2DO).collect(Collectors.toList());
            int taskStatus =
                    userActivityStatusChangeService.calculateUserTaskStatusByConfig(completeConditionBO, recordDOList);
            if (taskStatus == UserTaskStatusEnum.SUCCESS.getValue()) {
                finishTaskList.add(taskId);
            }
        });
        return finishTaskList;
    }

    @Override
    public void calcForecastSellerRoi(long userId, long activityId, List<TaskDO> canFinishTaskConfig,
            List<ForecastIndicatorInfo> indicatorInfos, List<ForecastTaskInfo> taskInfo) {
        try {
            // 报名记录
            List<UserRegistrationRecordDO> userRegistrationRecordList =
                    userRegistrationRecordDAO.listRecordsByUserAndActivityId(activityId, userId);
            // 各父任务基值
            Map<Long, Map<String, Object>> userParentBasicDataMap = userRegistrationRecordList.stream()
                    .filter(e -> e.getEntityType().equals(TASK.getCode()))
                    .collect(Collectors.toMap(UserRegistrationRecordDO::getEntityId,
                            e -> registrationConverter.resolveBasicDataFromJsonData(e)));
            List<StatisticsIndicatorInfoBO> indicatorInfoBOS = new ArrayList<>();
            indicatorInfos.forEach(e -> {
                StatisticsIndicatorInfoBO statisticsIndicatorInfoBO = new StatisticsIndicatorInfoBO();
                statisticsIndicatorInfoBO.setTaskId(e.getTaskId());
                statisticsIndicatorInfoBO.setIndicatorId(e.getIndicatorId());
                statisticsIndicatorInfoBO.setCurrentValue(e.getForecastValue());
                indicatorInfoBOS.add(statisticsIndicatorInfoBO);
            });
            List<StatisticsAwardInfoBO> awardInfoBOS = new ArrayList<>();
            taskInfo.forEach(e -> {
                Map<Integer, Long> awardValueByType = e.getForecastValueByType();
                awardValueByType.forEach((awardType, awardValue) -> {
                    StatisticsAwardInfoBO awardInfoBO = new StatisticsAwardInfoBO();
                    awardInfoBO.setParentTaskId(e.getParentTaskId());
                    awardInfoBO.setTaskId(e.getTaskId());
                    awardInfoBO.setAwardType(awardType);
                    awardInfoBO.setFinalAwardValue(awardValue);
                    awardInfoBOS.add(awardInfoBO);
                });
            });
            List<SellerSingleDimensionData> sellerSingleDimensionDataList =
                    buildSellerDimensionList(userId, canFinishTaskConfig, indicatorInfoBOS, awardInfoBOS,
                            userParentBasicDataMap, FORECAST);
            // 发消息
            String eventId = RoiCalculateOccasionEnum.FORECAST.getPrefix() + activityId;
            statisticsMsgProduceService.sendUserRoiCalcMsg(userId, eventId, 1, sellerSingleDimensionDataList);
        } catch (Exception e) {
            perfException(SCHEDULE_STATISTICS_SYNC, "单商家ROI计算", String.valueOf(activityId));
            log.error("[预估奖励计算] 单用户触发ROI计算异常！ userId:{}, activityId:{}", userId, activityId);
        }
    }

    @Override
    public void asyncCalcReviewSellerRoi(long userId, long activityId, List<TaskDO> awardTaskDOS,
            List<StatisticsIndicatorInfoBO> indicatorInfos, List<StatisticsAwardInfoBO> awardInfoBOS,
            Map<Long, Map<String, Object>> userParentBasicDataMap) {
        List<SellerSingleDimensionData> sellerSingleDimensionDataList =
                buildSellerDimensionList(userId, awardTaskDOS, indicatorInfos, awardInfoBOS, userParentBasicDataMap,
                        StatisticsRoiTypeEnum.REVIEW);
        // 发消息
        String eventId = RoiCalculateOccasionEnum.REVIEW_ACTIVITY.getPrefix() + activityId;
        statisticsMsgProduceService.sendUserRoiCalcMsg(userId, eventId, 1, sellerSingleDimensionDataList);
        String globalEventId = RoiCalculateOccasionEnum.REVIEW_GLOBAL.getPrefix() + activityId;
        statisticsMsgProduceService.sendUserRoiCalcMsg(userId, globalEventId, 1, sellerSingleDimensionDataList);
    }

    private List<SellerSingleDimensionData> buildSellerDimensionList(Long userId, List<TaskDO> awardTaskDOS,
            List<StatisticsIndicatorInfoBO> indicatorInfos, List<StatisticsAwardInfoBO> awardInfoBOS,
            Map<Long, Map<String, Object>> userParentBasicDataMap, StatisticsRoiTypeEnum statisticsRoiType) {
        // 按照父任务ID做聚合-分层
        Map<Long, List<TaskDO>> taskMapGroupByParentTask = awardTaskDOS.stream().collect(Collectors.groupingBy(
                TaskDO::getParentTask));
        // 查询父任务基本信息
        List<Long> parentTaskIds = new ArrayList<>(taskMapGroupByParentTask.keySet());
        List<TaskDO> parentTaskDOS = taskLocalCacheService.batchGetTaskByTaskId(parentTaskIds);
        Map<Long, TaskDO> parentTaskMap = parentTaskDOS.stream().collect(Collectors.toMap(BaseDO::getId, e -> e));
        // 指标元数据
        List<Long> indicatorIds =
                indicatorInfos.stream().map(StatisticsIndicatorInfoBO::getIndicatorId).collect(Collectors.toList());
        Map<Long, IndicatorDO> indicatorMap = indicatorLocalCacheService.queryTaskIndicators(indicatorIds);
        // 商家roi维度数据
        List<SellerSingleDimensionData> sellerSingleDimensionDataS = new ArrayList<>();
        taskMapGroupByParentTask.forEach((parentTaskId, taskDOS) -> {
            List<Long> taskIds = taskDOS.stream().map(BaseDO::getId).collect(Collectors.toList());
            // 父任务基本信息
            TaskDO parentTask = MapUtils.getObject(parentTaskMap, parentTaskId, null);
            // 基期数据
            Map<String, Object> baseJsonData =
                    MapUtils.getObject(userParentBasicDataMap, parentTaskId, new HashMap<>());
            // 指标信息
            List<StatisticsIndicatorInfoBO> phaseIndicatorInfo = indicatorInfos.stream()
                    .filter(e -> taskIds.contains(e.getTaskId())).collect(Collectors.toList());
            // 奖励信息
            List<StatisticsAwardInfoBO> phaseAwardInfo = awardInfoBOS.stream()
                    .filter(e -> taskIds.contains(e.getTaskId())).collect(Collectors.toList());
            SellerSingleDimensionData singleDimensionData =
                    buildSellerSingleDimensionData(userId, parentTask, baseJsonData, phaseIndicatorInfo, phaseAwardInfo,
                            indicatorMap, statisticsRoiType);
            sellerSingleDimensionDataS.add(singleDimensionData);
        });
        return sellerSingleDimensionDataS;
    }

    private SellerSingleDimensionData buildSellerSingleDimensionData(Long userId, TaskDO parentTaskDO,
            Map<String, Object> baseJsonData, List<StatisticsIndicatorInfoBO> indicatorInfoBOS,
            List<StatisticsAwardInfoBO> awardInfoBOS, Map<Long, IndicatorDO> indicatorMap,
            StatisticsRoiTypeEnum statisticsRoiType) {
        BasicConfigBO basicConfigBO = resolveBasicConfigFromTaskExt(parentTaskDO);
        // 活动ID_分层父任务ID_周期
        String ruleBizId =
                String.format("%s_%s_%s", parentTaskDO.getActivityId(), parentTaskDO.getId(), 1);
        return SellerSingleDimensionData.newBuilder()
                .setBaseInfo(buildBaseInfo(userId, basicConfigBO))
                .setRuleBizId(ruleBizId)
                .setSubActivityOrder(parentTaskDO.getStage())
                .setStepOrder(1)
                .addAllIndicatorInfo(
                        buildRoiIndicatorInfo(userId, indicatorInfoBOS, indicatorMap, baseJsonData, statisticsRoiType,
                                parentTaskDO))
                .addAllAwardInfo(buildRoiAwardInfo(awardInfoBOS))
                .setPeriodStartTime(parentTaskDO.getStartTime())
                .setPeriodEndTime(parentTaskDO.getEndTime())
                .build();
    }

    private BaseInfo buildBaseInfo(Long userId, BasicConfigBO baseConfigBO) {
        if (null == baseConfigBO) {
            return BaseInfo.newBuilder().build();
        }
        BasicTimeRange basicTimeRange = indicatorBasicNewService.getBasicTimeRange(userId, baseConfigBO);
        return BaseInfo.newBuilder()
                .setBaseStartTime(basicTimeRange.getEarliestStartTime())
                .setBaseEndTime(basicTimeRange.getLatestEndTime())
                .build();
    }

    private List<SellerAwardData> buildRoiAwardInfo(List<StatisticsAwardInfoBO> awardInfoBOS) {
        List<SellerAwardData> sellerAwardDataS = new ArrayList<>();
        for (StatisticsAwardInfoBO awardInfoBO : awardInfoBOS) {
            SellerAwardData sellerAwardData = SellerAwardData.newBuilder()
                    .setAwardType(awardInfoBO.getAwardType())
                    .setAwardValue(awardInfoBO.getFinalAwardValue())
                    .build();
            sellerAwardDataS.add(sellerAwardData);
        }
        return sellerAwardDataS;
    }

    private List<SellerIndicatorData> buildRoiIndicatorInfo(Long userId,
            List<StatisticsIndicatorInfoBO> indicatorInfoBOS,
            Map<Long, IndicatorDO> indicatorDOMap, Map<String, Object> jsonData,
            StatisticsRoiTypeEnum statisticsRoiType,
            TaskDO parentTaskDO) {
        Map<Long, Long> forecastIndicatorMap =
                indicatorInfoBOS.stream().collect(groupingBy(StatisticsIndicatorInfoBO::getIndicatorId,
                        summingLong(StatisticsIndicatorInfoBO::getCurrentValue)));
        List<SellerIndicatorData> roiIndicatorInfoBOS = new ArrayList<>();
        forecastIndicatorMap.forEach((indicatorId, totalValue) -> {
            IndicatorDO indicatorDO = MapUtils.getObject(indicatorDOMap, indicatorId, null);
            Map<String, Long> extraIndicatorMap =
                    this.getExtraIndicatorMap(userId, statisticsRoiType, parentTaskDO, indicatorDO, totalValue);
            SellerIndicatorData roiIndicatorInfoBO =
                    convertIndicatorInfo2DTO(indicatorDO, totalValue, jsonData, extraIndicatorMap);
            roiIndicatorInfoBOS.add(roiIndicatorInfoBO);
        });
        return roiIndicatorInfoBOS;
    }

    @Override
    public ExpectTaskInfo calcSingleTaskExpectAward(long userId, long activityId,
            List<IndicatorRecordDO> indicatorRecords,
            List<AwardConfigDO> awardConfigs, TaskDO task) {

        Long taskId = task.getId();
        if (CollectionUtils.isEmpty(indicatorRecords) || CollectionUtils.isEmpty(awardConfigs)) {
            return ExpectTaskInfo.builder()
                    .taskId(taskId)
                    .expectValue(0L)
                    .expectTaskDetailMap(Maps.newHashMap())
                    .build();
        }

        List<IndicatorConfigDO> indicatorConfigList = indicatorConfigDAO
                .queryTaskBaseIndicatorConfig(activityId, task.getParentTask());
        List<Long> baseIndicatorIds = indicatorConfigList.stream()
                .map(IndicatorConfigDO::getIndicatorId)
                .distinct().collect(Collectors.toList());

        // 获取阶梯数量
        IndicatorRecordDO tempIndicator = indicatorRecords.stream()
                .filter(indicator -> indicator.getIndicatorId() != IndicatorEnum.RANK.getValue())
                .findFirst()
                .orElseThrow(() -> new BizException(ACTIVITY_CONFIG_UN_EXPECT, "指标配置异常"));

        IndicatorConfigDO indicatorConfig = indicatorLocalCacheService
                .queryIndicatorConfigById(tempIndicator.getConfigId());

        // 兼容排行榜单阶梯
        int stepCount = Math.max(parseIndicatorStepCount(indicatorConfig), 1);

        // 根据奖励类型聚合奖励配置
        Map<Integer, List<AwardConfigDO>> awardConfigTypeMap = awardConfigs.stream()
                .collect(groupingBy(AwardConfigDO::getAwardType));

        // 计算期望最大层级
        int maxReachLevel = getMaxReachLevelForUserExpectAward(indicatorRecords);

        Map<Long, List<Long>> indicatorTargetValueMap = Maps.newHashMap();
        indicatorRecords.forEach(indicatorRecord -> {
            List<Long> targetValues = calcStepTargetForExpectAward(indicatorRecord);
            indicatorTargetValueMap.put(indicatorRecord.getId(), targetValues);
        });

        Map<String, Map<Integer, Long>> expectTaskDetailMap = Maps.newHashMap();
        AtomicLong sumExpectValue = new AtomicLong();
        Map<String, Long> expectBusinessValue = Maps.newHashMap();
        Map<Integer, Boolean> rebateIndicatorCoveredMap = Maps.newHashMap();

        // 遍历所有奖励配置
        awardConfigTypeMap.forEach((awardType, awardConfigsOfCertainType) -> {

            AwardTypeEnum awardTypeEnum = AwardTypeEnum.getByCode(awardType);

            // 计算相同奖励类型多阶梯预估奖励
            ExpectAwardCalcContext context = ExpectAwardCalcContext.builder()
                    .userId(userId)
                    .activityId(activityId)
                    .task(task)
                    .indicatorRecords(indicatorRecords)
                    .stepCount(stepCount)
                    .maxReachLevel(maxReachLevel)
                    .indicatorTargetValueMap(indicatorTargetValueMap)
                    .baseIndicatorIds(baseIndicatorIds)
                    .build();

            List<ExpectAwardValueBO> awardExpectValuesOfCertainType = awardConfigsOfCertainType.stream()
                    .map(awardConfig -> {
                        context.setAwardConfig(awardConfig);
                        return awardCalcService.calcUserExpectAwardValue(context);
                    }).collect(Collectors.toList());

            // 遍历单奖励类型奖励列表
            awardExpectValuesOfCertainType.forEach(awardExpectValue -> {

                AwardValueBO award = awardExpectValue.getAwardValue();
                // 遍历单奖励类型多阶梯预估奖励
                Map<String, Object> componentValues = award.getComponentValues();
                componentValues.forEach((stepTag, expectValue) -> {
                    // 获取结果单阶梯多奖励类型奖励值
                    Map<Integer, Long> expectValueOfCertainStep = expectTaskDetailMap
                            .getOrDefault(stepTag, Maps.newHashMap());

                    // 获取相同阶梯单类型奖励值
                    Long expectValueOfCertainValue = expectValueOfCertainStep.getOrDefault(awardType, 0L);
                    expectValueOfCertainValue += (Long) expectValue;
                    expectValueOfCertainStep.put(awardType, expectValueOfCertainValue);

                    expectTaskDetailMap.put(stepTag, expectValueOfCertainStep);

                    // 对于 B 补奖励类型统计
                    if (awardTypeEnum.getIsBusiness()) {
                        Long expectBusinessValueOfCertainStep = expectBusinessValue.getOrDefault(stepTag, 0L);
                        expectBusinessValueOfCertainStep += (Long) expectValue;
                        expectBusinessValue.put(stepTag, expectBusinessValueOfCertainStep);
                    }
                });

                sumExpectValue.addAndGet(award.getAwardValue());
            });

            boolean rebateIndicatorCovered = awardExpectValuesOfCertainType.stream()
                    .allMatch(ExpectAwardValueBO::getRebateIndicatorCovered);

            rebateIndicatorCoveredMap.put(awardType, rebateIndicatorCovered);
        });

        return ExpectTaskInfo.builder()
                .taskId(taskId)
                .expectValue(sumExpectValue.get())
                .expectBusinessValue(expectBusinessValue)
                .expectTaskDetailMap(expectTaskDetailMap)
                .rebateIndicatorCoveredMap(rebateIndicatorCoveredMap)
                .build();
    }

    private int getMaxReachLevelForUserExpectAward(List<IndicatorRecordDO> indicatorRecords) {
        Integer step = indicatorRecords.stream().map(indicatorRecord ->
                        indicatorRecord.getCurrentValue() < indicatorRecord.getTargetValue()
                        ? 1 : this.calcMaxReachStep(indicatorRecord, indicatorRecord.getCurrentValue()))
                .min(Integer::compareTo).orElse(1);

        return Math.max(step, 1);
    }

    private List<Long> calcStepTargetForExpectAward(IndicatorRecordDO indicator) {
        if (!adminActivityCheckService.isRankActivity(indicator.getActivityId())) {
            return calcStepTargetFromIndicatorExt(indicator);
        }

        if (indicator.getIndicatorId() == IndicatorEnum.RANK.getValue()) {
            return Lists.newArrayList();
        }

        List<Long> result = Lists.newArrayList();
        String ext = indicator.getExt();
        if (StringUtils.isEmpty(ext)) {
            return result;
        }

        // 排行榜单阶梯
        IndicatorRecordExtFieldBO recordExt = fromJSON(ext, IndicatorRecordExtFieldBO.class);
        long stepTarget = MapUtils.getLong(recordExt.getComponentValues(), "step_target_" + 1, -1L);
        if (stepTarget < 0) {
            return result;
        }
        result.add(stepTarget);

        return result;
    }

    /**
     * 获取额外的指标参数
     * @param userId
     * @param statisticsRoiType
     * @param parentTaskDO
     * @param indicatorDO
     * @return
     */
    private Map<String, Long> getExtraIndicatorMap(Long userId, StatisticsRoiTypeEnum statisticsRoiType,
            TaskDO parentTaskDO, IndicatorDO indicatorDO, Long totalValue) {
        Map<String, Long> extraIndicatorMap = new HashMap<>();
        // 针对新粉指标,额外新增活动期预估ltv90指标
        if (!indicatorDO.getId().equals(IndicatorEnum.NEW_FANS_CNT.getValue())) {
            return extraIndicatorMap;
        }
        Long roiCalSingleFanLtv90;
        switch (statisticsRoiType) {
            case FORECAST:
                roiCalSingleFanLtv90 = statisticsRoiService
                        .getRoiCalSingleFanLtv90(FORECAST, userId, null, null);
                extraIndicatorMap.put(BASE_PERIOD_SINGLE_FAN_LTV90.getType(), roiCalSingleFanLtv90);
                break;
            case REVIEW:
                UserTaskRecordDO recordDO = userTaskRecordDAO.queryUserTaskRecord(userId, parentTaskDO.getActivityId(),
                        parentTaskDO.getId(), false);
                ValidateUtil.checkArgument(null != recordDO,
                        String.format("用户任务记录为空,userId: %s,taskId:%s", userId, parentTaskDO.getId()));
                Map<String, Object> extMap = Maps.newHashMap();
                extMap.put("startTime", recordDO.getStartTime());
                extMap.put("endTime", recordDO.getEndTime());
                extMap.put("totalFans", totalValue);
                roiCalSingleFanLtv90 = statisticsRoiService
                        .getRoiCalSingleFanLtv90(REVIEW, userId, null, extMap);
                extraIndicatorMap.put(ACTIVITY_PERIOD_FORECAST_SINGLE_FAN_LTV90.getType(), roiCalSingleFanLtv90);
                break;
            default:
                break;
        }
        return extraIndicatorMap;
    }
}
