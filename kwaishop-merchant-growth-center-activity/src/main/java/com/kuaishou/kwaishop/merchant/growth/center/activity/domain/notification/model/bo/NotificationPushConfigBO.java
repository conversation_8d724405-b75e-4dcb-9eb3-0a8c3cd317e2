package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationPushConfigBO {
    private long id;
    /**
     * 活动id
     */
    private long activityId;
    /**
     * 实体id
     */
    private long entityId;
    /**
     * 实体类型 1-活动，2-任务，3-指标
     */
    private int entityType;
    /**
     * 实体状态
     */
    private int entityStatus;
    /**
     * 推送渠道 1-通知中心 2-青鸟
     */
    private int channel;
    /**
     * 推送时机，1-前，2-立即，3-后
     */
    private int occasion;
    /**
     * 配置状态，1-生效，2-不生效
     */
    private int status;
    /**
     * 推送code配置 json
     */
    private String templateConfig;
    /**
     * 周期性配置 json
     */
    private PeriodConfigBO periodConfig;
    /**
     * 扩展参数 json
     */
    private String ext;
    /**
     * 创建时间
     */
    private long createTime;
    /**
     * 更新时间
     */
    private long updateTime;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 修改者
     */
    private String modifier;
    /**
     * 是否被删除，0-未删除，1-已删除
     */
    private int deleted;
    /**
     * 版本号
     */
    private long version;
    /**
     * 通知名称
     */
    private String name;

    /**
     * bpm送审bizKey
     */
    private String bizKey;

    /**
     * 场景值 目前与notificationCode相同
     */
    private String scene;
}
