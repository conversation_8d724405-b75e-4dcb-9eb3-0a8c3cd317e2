package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.promotion;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-22
 */
@Data
public class PromotionRegistrationTaskConfig {
    /**
     * 描述
     */
    private String desc;
    /**
     * 人群ID
     */
    private Long crowdId;
    /**
     * 任务alias
     */
    private String alias;
    /**
     * 绑定基值
     */
    private String staticBasicData;
    /**
     * 绑定excel
     */
    private String customizeUrl;
    /**
     * excel模型枚举
     */
    private Integer excelModelType;

    /**
     * 互斥任务组，只放比这个任务高优先级的任务
     */
    private List<String> exclusiveTaskAlias;


}
