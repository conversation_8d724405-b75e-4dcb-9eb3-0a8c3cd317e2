package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.github.rholder.retry.WaitStrategies.fixedWait;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.MQ_BATCH_USER_EXECUTE_EVENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.MQ_USER_ROI_CALC_DATA_EVENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.ACTIVITY_STATISTICS_SYNC_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.StatisticsPerfEnum.MQ_USER_STATISTICS_SYNC_CONSUME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.statisticsChangeSyncMsgDelaySeconds;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.BatchExecuteUserEventMsg;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.UserStatisticsChangeSyncMsg;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics.ActivityStatisticsDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics.ActivityStatisticsFinishMsg;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerRoiCalculateDataDTO;
import com.kuaishou.kwaishop.merchant.interest.center.resource.protobuf.SellerSingleDimensionData;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-17
 */
@Service
@Slf4j
@Lazy
public class StatisticsMsgProduceServiceImpl implements StatisticsMsgProduceService {

    // 发送消息
    private static final Retryer<Boolean> RETRY = RetryerBuilder.<Boolean>newBuilder()
            .retryIfExceptionOfType(Exception.class)
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .withWaitStrategy(fixedWait(100, TimeUnit.MILLISECONDS))
            .build();

    @Autowired
    @Qualifier("batchUserExecuteProducer")
    private MsgProducer batchUserExecuteProducer;

    @Autowired
    @Qualifier("userSellerRoiDataEventProducer")
    private MsgProducer userSellerRoiDataEventProducer;

    @Autowired
    @Qualifier("userStatisticsChangeProducer")
    private MsgProducer userStatisticsChangeProducer;

    @Autowired
    @Qualifier("activityStatisticsFinishProducer")
    private MsgProducer activityStatisticsFinishProducer;

    @Autowired
    @Qualifier("activityStatisticsReviewProducer")
    private MsgProducer activityStatisticsReviewProducer;

    @Override
    public void sendBatchExecuteMsg(Integer executeType, String eventId, String executeConfig, List<Long> userIdList,
                                    boolean needReport, boolean retry) {
        BatchExecuteUserEventMsg message = BatchExecuteUserEventMsg.newBuilder()
                .addAllUserId(userIdList)
                .setEventId(eventId)
                .setExecuteType(executeType)
                .setExecuteConfig(executeConfig)
                .setFinishAction(needReport)
                .setRetry(retry)
                .build();
        // 发送消息
        try {
            RETRY.call(() -> sendMsg(message));
            perfSuccess(MQ_BATCH_USER_EXECUTE_EVENT, eventId);
        } catch (BizException | RetryException e) {
            log.error("[批量用户处理消息] 批量用户处理消息重试发送失败！msg:{}", toJSON(message), e);
            perfFail(MQ_BATCH_USER_EXECUTE_EVENT, eventId, "afterRetry", e.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("[批量用户处理消息] 批量用户处理消息重试处理异常！msg:{}", toJSON(message), e);
            perfException(MQ_BATCH_USER_EXECUTE_EVENT, eventId, e.getMessage());
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }

    @Override
    public void sendUserRoiCalcMsg(long userId, String eventId, long totalNum,
                                   List<SellerSingleDimensionData> singleDimensionDataList) {
        SellerRoiCalculateDataDTO message = SellerRoiCalculateDataDTO.newBuilder()
                .setEventId(eventId)
                .setSellerId(userId)
                .setTotalNum(totalNum)
                .addAllData(singleDimensionDataList)
                .build();
        // 发送消息
        try {
            RETRY.call(() -> sendMsg(message));
        } catch (BizException | RetryException e) {
            log.error("[ROI单用户计算消息] ROI单用户计算消息重试发送失败！msg:{}", toJSON(message), e);
            perfFail(MQ_USER_ROI_CALC_DATA_EVENT, "afterRetry", e.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("[ROI单用户计算消息] ROI单用户计算消息重试处理异常！msg:{}", toJSON(message), e);
            perfException(MQ_USER_ROI_CALC_DATA_EVENT, e);
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }

    @Override
    public void sendStatisticsChangeMsg(long userId, long activityId, long changeTime, boolean delay) {
        UserStatisticsChangeSyncMsg message = UserStatisticsChangeSyncMsg.newBuilder()
                .setUserId(userId)
                .setActivityId(activityId)
                .setChangeTime(changeTime)
                .setSendTime(System.currentTimeMillis())
                .build();
        // 发送消息
        try {
            RETRY.call(() -> sendMsg(message, delay));
        } catch (BizException | RetryException e) {
            log.error("[发送统计变动消息] 统计变动消息重试发送失败！msg:{}", toJSON(message), e);
            perfFail(MQ_USER_STATISTICS_SYNC_CONSUME, "afterRetry", e.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("[发送统计变动消息] 统计变动消息重试处理异常！msg:{}", toJSON(message), e);
            perfException(MQ_USER_STATISTICS_SYNC_CONSUME, e);
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }

    @Override
    public void sendActivityStatisticsFinishMsg(long activityId, long eventTime) {
        ActivityStatisticsFinishMsg message = ActivityStatisticsFinishMsg.newBuilder()
                .setActivityId(activityId)
                .setEventTime(eventTime)
                .build();
        // 发送消息
        try {
            RETRY.call(() -> sendMsg(message));
        } catch (BizException | RetryException e) {
            log.error("[发送活动统计完成消息] 消息重试发送失败！msg:{}", toJSON(message), e);
            perfFail(ACTIVITY_STATISTICS_SYNC_CONSUME, "afterRetry", e.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("[发送活动统计完成消息] 消息重试处理异常！msg:{}", toJSON(message), e);
            perfException(ACTIVITY_STATISTICS_SYNC_CONSUME, e);
        }
    }

    @Override
    public void sendActivityStatisticsReviewDailyMsg(ActivityStatisticsDTO statisticsDTO) {
        // 发送消息
        try {
            RETRY.call(() -> {
                MqMessage eventMsg = activityStatisticsReviewProducer
                        .createMsgBuilder(statisticsDTO)
                        .build();
                MqSyncSendResult syncSendResult = activityStatisticsReviewProducer.sendSync(eventMsg);
                if (!EnvUtils.isProd()) {
                    log.info("[发送活动统计复盘消息] 消息回传:{}", toJSON(statisticsDTO));
                }
                if (!syncSendResult.isSuccess()) {
                    perfFail(ACTIVITY_STATISTICS_SYNC_CONSUME, "sendFail");
                    throw new BizException(BasicErrorCode.SERVER_ERROR);
                }
                return true;
            });
        } catch (BizException | RetryException e) {
            log.error("[发送活动统计复盘消息] 消息重试发送失败！msg:{}", toJSON(statisticsDTO), e);
            perfFail(ACTIVITY_STATISTICS_SYNC_CONSUME, "afterRetry", e.getClass().getSimpleName());
            throw new BizException(BasicErrorCode.SERVER_ERROR, "发送活动统计复盘消息失败");
        } catch (Exception e) {
            log.error("[发送活动统计复盘消息] 消息重试处理异常！msg:{}", toJSON(statisticsDTO), e);
            perfException(ACTIVITY_STATISTICS_SYNC_CONSUME, e);
        }
    }

    /**
     * 消息发送逻辑
     */
    private Boolean sendMsg(ActivityStatisticsFinishMsg message) {
        MqMessage eventMsg = activityStatisticsFinishProducer.createMsgBuilder(message).build();
        MqSyncSendResult sendResult = activityStatisticsFinishProducer.sendSync(eventMsg);
        log.info("[发送活动统计完成消息] 消息回传:{}", toJSON(message));
        if (!sendResult.isSuccess()) {
            log.error("[发送活动统计完成消息] 消息发送失败，{}", toJSON(message));
            perfFail(ACTIVITY_STATISTICS_SYNC_CONSUME, "sendFail");
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return true;
    }

    /**
     * 消息发送逻辑
     */
    private Boolean sendMsg(SellerRoiCalculateDataDTO message) {
        MqMessage eventMsg = userSellerRoiDataEventProducer.createMsgBuilder(message).build();
        MqSyncSendResult sendResult = userSellerRoiDataEventProducer.sendSync(eventMsg);
        log.info("[ROI单用户计算消息] 消息回传:{}", toJSON(message));
        if (!sendResult.isSuccess()) {
            log.error("[ROI单用户计算消息] 消息发送失败，{}", toJSON(message));
            perfFail(MQ_USER_ROI_CALC_DATA_EVENT, "sendFail");
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return true;
    }

    /**
     * 消息发送逻辑
     */
    private Boolean sendMsg(BatchExecuteUserEventMsg message) {
        MqMessage eventMsg = batchUserExecuteProducer.createMsgBuilder(message).build();
        MqSyncSendResult sendResult = batchUserExecuteProducer.sendSync(eventMsg);
        if (!sendResult.isSuccess()) {
            log.error("[批量用户处理消息] 消息发送失败，{}", toJSON(message));
            perfFail(MQ_BATCH_USER_EXECUTE_EVENT, "sendFail");
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return true;
    }

    /**
     * 消息发送逻辑
     */
    private Boolean sendMsg(UserStatisticsChangeSyncMsg message, boolean delay) {
        MqMessage eventMsg = userStatisticsChangeProducer.createMsgBuilder(message).build();
        if (delay) {
            eventMsg = userStatisticsChangeProducer.createMsgBuilder(message)
                    .withDelay(Duration.ofSeconds(statisticsChangeSyncMsgDelaySeconds.get())).build();
        }
        MqSyncSendResult sendResult = userStatisticsChangeProducer.sendSync(eventMsg);
        if (!sendResult.isSuccess()) {
            log.error("[发送统计变动消息] 消息发送失败，{}", toJSON(message));
            perfFail(MQ_USER_STATISTICS_SYNC_CONSUME, "sendFail");
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return true;
    }

}
