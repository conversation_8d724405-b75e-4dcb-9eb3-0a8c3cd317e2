package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.supplier.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType.SUPPLIER_DEFAULT_STRATEGY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.activityListBatchQuerySizeLimit;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCacheConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCommonConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.supplier.LaunchSupplier;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.DefaultLaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.DefaultLaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCacheConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchConfigResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.pipeline.adapter.model.LaunchPipeHandlerType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Component
@Slf4j
public class DefaultLaunchSupplier implements LaunchSupplier<DefaultLaunchInfoFetchContext, DefaultLaunchInfoResult> {

    @Resource
    private UserRegistrationService userRegistrationService;

    @Resource
    private ActivityDAO activityDAO;

    @Resource
    private UserActivityRecordDAO userActivityRecordDAO;

    @Resource
    private LaunchDomainService launchDomainService;

    @Resource
    private LaunchResolveService launchResolveService;

    @Override
    public void supply(DefaultLaunchInfoFetchContext context) {
        supplyLaunchConfigInfo(context);
        supplyUserActivityInfo(context);
    }

    protected void supplyUserActivityInfo(DefaultLaunchInfoFetchContext context) {
        List<Long> launchActivityIds = context.getLaunchActivityIds();
        Long userId = context.getUserId();
        if (CollectionUtils.isEmpty(launchActivityIds)) {
            interruptAndReturn(context);
            return;
        }

        List<UserRegistrationRecordBO> userRegistrationRecords = ListUtils.emptyIfNull(
                        userRegistrationService.queryUserRecordsOfMultiActivity(launchActivityIds, userId)).stream()
                .filter(e -> Objects.equals(e.getStatus(), UserRegistrationStatusEnum.VALID.getCode()))
                .collect(Collectors.toList());

        List<Long> activityIds = userRegistrationRecords.stream()
                .filter(record -> Objects.equals(record.getEntityType(), EntityTypeEnum.ACTIVITY))
                .map(UserRegistrationRecordBO::getActivityId)
                .collect(Collectors.toList());

        // 查询活动元数据
        Map<Long, ActivityDO> allActivityMap = Maps.newHashMap();
        List<List<Long>> activityIdPartitions = Lists.partition(activityIds, activityListBatchQuerySizeLimit.get());
        activityIdPartitions.forEach(partition -> allActivityMap.putAll(activityDAO.batchQueryByIds(partition)));
        // 前置过滤展示截止时间
        Map<Long, ActivityDO> filteredMap = Maps.newHashMap();
        allActivityMap.forEach((activityId, activity) -> {
            Long showEndTime = activity.getShowEndTime();

            // 最长投放时间
            Long maxLaunchDuration = 0L;
            LaunchCommonConfigBO config = launchCommonConfig.getObject();
            if (config != null && config.getMaxLaunchDuration() != null) {
                maxLaunchDuration = config.getMaxLaunchDuration();
            }

            if (System.currentTimeMillis() <= showEndTime + maxLaunchDuration) {
                filteredMap.put(activityId, activity);
            }
        });
        activityIds = new ArrayList<>(filteredMap.keySet());

        List<UserActivityRecordDO> allUserActivityRecords =
                userActivityRecordDAO.queryUserActivityRecordByActivityList(userId, activityIds);

        // 前置过滤超过领取时间 & 没有领取的活动
        Set<Long> userActivityIds =
                allUserActivityRecords.stream().map(UserActivityRecordDO::getActivityId).collect(Collectors.toSet());
        activityIds = activityIds.stream().filter(activityId -> {
            ActivityDO activity = allActivityMap.get(activityId);
            if (activity == null) {
                return false;
            }
            if (userActivityIds.contains(activityId)) {
                return true;
            }

            return System.currentTimeMillis() <= activity.getDrawEndTime();
        }).collect(Collectors.toList());

        context.setLaunchActivityIds(activityIds);
        context.setAllUserRegistrationRecords(userRegistrationRecords);
        context.setAllActivityMetaMap(filteredMap);
        context.setAllUserActivityRecords(allUserActivityRecords);
    }

    protected void supplyLaunchConfigInfo(DefaultLaunchInfoFetchContext context) {
        String channel = context.getChannel();
        String scene = context.getScene();
        LaunchCacheConfigBO cacheConfig = launchCacheConfig.getObject();

        // 查询投放配置
        List<LaunchConfigBO> launchConfigList = ListUtils.emptyIfNull(launchDomainService
                .queryLaunchConfigListByScene(LaunchConfigResourceTypeEnum.STRATEGY_ACTIVITY.getType(),
                        channel, scene, cacheConfig.getQueryLaunchConfigWithCache()));
        if (CollectionUtils.isEmpty(launchConfigList)) {
            interruptAndReturn(context);
            return;
        }

        // 活动维度投放配置
        Map<Long/*activityId*/, List<LaunchConfigBO>> launchConfigActivityMap =
                launchConfigList.stream().collect(Collectors.groupingBy(LaunchConfigBO::getActivityId));

        context.setLaunchActivityIds(new ArrayList<>(launchConfigActivityMap.keySet()));
        context.setLaunchConfigActivityMap(launchConfigActivityMap);

        LaunchConfigBO firstLaunchConfig = launchConfigList.get(0);
        EntityTypeEnum entityTypeEnum = EntityTypeEnum.getByCode(firstLaunchConfig.getEntityType());
        context.setSelectedEntityType(entityTypeEnum);
    }

    @Override
    public LaunchPipeHandlerType getType() {
        return SUPPLIER_DEFAULT_STRATEGY;
    }
}
