package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.notificationCode;

import static com.kuaishou.kconf.common.json.JsonMapperUtils.fromJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.AWARD_CANCEL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.FAILED_PASS_AUDIT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.MERCHANT_BOOT_CAMP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.RANKING_TERMINATION;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum.REWARD_TO_ACCOUNT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.needAuditChannelList;
import static com.kuaishou.kwaishop.platform.common.utils.JsonUtil.toJson;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.MerchantCenterMessageTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationCodeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-26
 */
@Service
@Slf4j
@Lazy
public class DefaultStrategyService extends AbstractNotificationCodeStrategyService {
    @Override
    public Set<NotificationCodeEnum> getNotificationCodeSet() {
        return Sets.newHashSet(MERCHANT_BOOT_CAMP, REWARD_TO_ACCOUNT, AWARD_CANCEL, FAILED_PASS_AUDIT, RANKING_TERMINATION);
    }

    @Override
    public List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList,
           List<AwardConfigDO> awardConfigList, NotificationConfigBO notificationConfig) {
        NotificationCodeEnum notificationCode =
                NotificationCodeEnum.getByCode(notificationConfig.getNotificationCode());
        switch (notificationCode) {
            case MERCHANT_BOOT_CAMP:
                if (CollectionUtils.isNotEmpty(awardConfigList)) {
                    awardConfigList = awardConfigList.stream()
                            .filter(awardConfigDO -> Objects.equals(awardConfigDO.getAwardType(),
                                    AwardTypeEnum.SERVICE_MARKET_COUPON.getCode()))
                            .collect(Collectors.toList());
                }
            case REWARD_TO_ACCOUNT:
            case AWARD_CANCEL:
            case FAILED_PASS_AUDIT:
                if (CollectionUtils.isNotEmpty(awardConfigList)) {
                    List<Long> awardConfigIdList = awardConfigList.stream()
                            .map(AwardConfigDO::getId)
                            .collect(Collectors.toList());
                    return buildDefaultNotificationConfig(activity,
                            awardConfigIdList, notificationConfig);
                }
            case RANKING_TERMINATION:
                return buildDefaultNotificationConfig(activity,
                        Lists.newArrayList(activity.getId()), notificationConfig);
            default:
                return Lists.newArrayList();
        }
    }

    private List<NotificationPushConfigBO> buildDefaultNotificationConfig(ActivityDO activityDO,
            List<Long> entityIdList, NotificationConfigBO notificationConfig) {
        if (CollectionUtils.isEmpty(entityIdList)) {
            return Lists.newArrayList();
        }
        NotificationAdminDomainBO notificationAdminDomainBO = getNotificationAdminDomainBO(activityDO, notificationConfig);

        //构建templateConfig
        String templateConfig = notificationAdminDomainBO.getTemplateConfig();
        if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
            MerchantCenterMessageTemplateConfigBO templateConfigBO =
                    ObjectMapperUtils.fromJSON(templateConfig, MerchantCenterMessageTemplateConfigBO.class);
            if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
                templateConfigBO.setCode(notificationConfig.getTemplateCode());
            }
            templateConfig = toJson(templateConfigBO);
        }
        int status;
        if (needAuditChannelList.get().contains(notificationConfig.getChannel())) {
            status = NotificationStatusEnum.NEED_AUDIT.getVal();
        } else {
            status = NotificationStatusEnum.VALID.getVal();
        }
        String finalTemplateConfig = templateConfig;
        return entityIdList.stream().map(entityId -> {
            NotificationPushConfigBO notificationPushConfigBO = NotificationPushConfigBO.builder()
                    .activityId(activityDO.getId())
                    .entityId(entityId)
                    .entityType(notificationAdminDomainBO.getEntityType())
                    .entityStatus(notificationAdminDomainBO.getEntityStatus())
                    .channel(notificationConfig.getChannel())
                    .templateConfig(finalTemplateConfig)
                    .occasion(notificationAdminDomainBO.getOccasion())
                    .creator(SymbolConstants.DEFAULT_USER_SYMBOL)
                    .modifier(SymbolConstants.DEFAULT_USER_SYMBOL)
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .status(status)
                    .scene(notificationConfig.getNotificationCode())
                    .build();
            PeriodConfigBO periodConfig = notificationAdminDomainBO.getPeriodConfig() == null ? null : fromJson(
                    notificationAdminDomainBO.getPeriodConfig(), PeriodConfigBO.class);
            notificationPushConfigBO.setPeriodConfig(periodConfig);
            return notificationPushConfigBO;
        }).collect(Collectors.toList());
    }
}
