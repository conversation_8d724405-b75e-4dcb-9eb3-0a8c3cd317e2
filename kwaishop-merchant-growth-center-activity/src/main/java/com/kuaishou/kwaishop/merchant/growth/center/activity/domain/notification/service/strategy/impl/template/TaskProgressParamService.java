package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_COMPLETE_RATE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.cache.ActivityRedisDataSource.getGrowthRedisCommands;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.progressNotificationPushLimitSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.taskProgressNotificationConfig;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.TaskProgressNotificationConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.TaskProgressPushBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.TaskShowProgressCalculator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.show.processor.TaskShowProgressResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-22
 */
@Service
@Slf4j
public class TaskProgressParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private TaskShowProgressCalculator taskShowProgressCalculator;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TASK_COMPLETE_RATE);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {

        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();

        // 降级
        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[任务进度模板参数]触达控制终止，触达停止，userId:{}, configId:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }

        TaskProgressNotificationConfig config = taskProgressNotificationConfig.getObject();
        if (Objects.isNull(config) || CollectionUtils.isEmpty(config.getRateList())) {
            result.setExecuteStatus(STOP);
            return result;
        }

        List<BigDecimal> targetRateList = config.getRateList().stream()
                .map(rate -> new BigDecimal(rate.getRate())).collect(Collectors.toList());

        long activityId = configBO.getActivityId();
        long taskId = configBO.getEntityId();
        long configId = configBO.getId();

        TaskShowProgressResultBO taskShowProgressResult = taskShowProgressCalculator
                .calcTaskShowProgressResult(userId, activityId, taskId);
        BigDecimal completeRate = taskShowProgressResult.getCompleteRate();
        BigDecimal notificationRate = null;

        // 计算最大完成进度
        for (BigDecimal targetRate : targetRateList) {
            if (completeRate.compareTo(targetRate) >= 0) {
                notificationRate = targetRate;
            }
        }
        if (Objects.isNull(notificationRate)) {
            result.setExecuteStatus(SKIP);
            log.info("[任务进度模板参数] 没有达到目标进度，本次推送跳过 userId:{}, config:{}", userId, toJSON(configBO));
            return result;
        }

        if (notificationRate.compareTo(BigDecimal.valueOf(1)) == 0
                && taskShowProgressResult.getTotalCount() == 1) {
            result.setExecuteStatus(STOP);
            log.info("[任务进度模板参数] 单指标完成不推送 userId:{}, config:{}", userId, toJSON(configBO));
            return result;
        }

        // 是否已经推送进度 optimise bitmap
        String redisKey = NotificationRedisKeyEnum.TASK_PROGRESS_NOTIFICATION_PUSH
                .getFullKeyJoinWithColon(userId, configId);
        String redisValue = getGrowthRedisCommands().get(redisKey);

        if (progressNotificationPushLimitSwitch.get() && StringUtils.isNotBlank(redisValue)) {
            TaskProgressPushBO taskProgressPushBO = fromJSON(redisValue, TaskProgressPushBO.class);
            String pushedCompleteTaskRate = taskProgressPushBO.getPushedCompleteTaskRate();

            if (StringUtils.isNotBlank(pushedCompleteTaskRate)
                    && new BigDecimal(pushedCompleteTaskRate).compareTo(notificationRate) >= 0) {
                result.setExecuteStatus(SKIP);
                log.info("[任务进度模板参数] 进度已经重复推送，本次推送跳过 userId:{}, config:{}, notificationRate:{}",
                        userId, toJSON(configBO), notificationRate);
                return result;
            }
        }

        UserTaskRecordDO userTaskRecord = userTaskRecordDAO.queryUserTaskRecord(userId, activityId, taskId, true);
        long expireTime = userTaskRecord.getEndTime() - System.currentTimeMillis();
        TaskProgressPushBO taskProgressPushBO = TaskProgressPushBO.builder()
                .pushedCompleteTaskRate(notificationRate.toString()).build();
        getGrowthRedisCommands().psetex(redisKey, expireTime, toJSON(taskProgressPushBO));

        params.put(TASK_COMPLETE_RATE.getName(), notificationRate.multiply(BigDecimal.valueOf(100)).setScale(0,
                RoundingMode.DOWN).toString() + '%');
        log.info("[任务进度模板参数] 获取模板参数 userId:{}, config:{}, param:{}", userId, toJSON(config),
                toJSON(params));
        return result;
    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals(TASK_COMPLETE_RATE.getName()))
                .findFirst().orElse(null);
    }
}
