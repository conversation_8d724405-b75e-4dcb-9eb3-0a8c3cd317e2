package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.USER_NOTIFICATION_INFO_QUERY_ERROR;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UserNotificationShowConfig;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
@Component
@Slf4j
public class UserNotificationInfoProvider {

    @Autowired
    private List<UserNotificationInfoStrategy> strategies;

    private Map<UserNotificationSourceTypeEnum, UserNotificationInfoStrategy> strategyMap;

    @PostConstruct
    public void init() {
        strategyMap = strategies.stream().collect(
                Collectors.toMap(UserNotificationInfoStrategy::getSource, Function.identity(), (k1, k2) -> k1));
    }

    public UserNotificationShowConfig query(long userId, String param, String source) {
        if (userId < 0 || StringUtils.isEmpty(param) || StringUtils.isEmpty(source)) {
            log.error("[查询用户触达信息] 参数异常 userId:{}, param:{}, source:{}", userId, param, source);
            throw new BizException(USER_NOTIFICATION_INFO_QUERY_ERROR, "参数异常");
        }

        UserNotificationSourceTypeEnum sourceType = UserNotificationSourceTypeEnum.of(source);
        UserNotificationInfoStrategy strategy = strategyMap.get(sourceType);

        if (Objects.isNull(strategy)) {
            log.error("[查询用户触达信息] source不存在 userId:{}, param:{}, source:{}", userId, param, source);
            throw new BizException(USER_NOTIFICATION_INFO_QUERY_ERROR, "source不存在");
        }

        return strategy.query(userId, param);
    }
}
