package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_STATISTICS_DATA;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.statisticsReviewConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils.getDynamicTableHeaderConfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.DynamicTableHeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.HeaderConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.RuleExecuteResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsReviewConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ActivityReviewParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ActivityReviewQuery;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ActivityReviewResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.BaseExportParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportElement;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.export.ExportResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRecordService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.AbstractFileExporter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.FileExportService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.ChildrenTaskDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.SubActivityDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.ddd.repository.property.DataResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RuleExecuteFacadeMixService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-08
 */
@Lazy
@Slf4j
@Service
public class ActivityReviewExporter extends AbstractFileExporter<ActivityReviewQuery>
        implements FileExportService {

    private static final String SHEET_NAME = "sheet1";

    @Autowired
    private IndicatorService indicatorService;
    @Autowired
    private AwardConfigService awardConfigService;
    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;
    @Autowired
    private IndicatorLocalCacheService indicatorLocalCacheService;
    @Autowired
    private StatisticsRecordService statisticsRecordService;

    @Autowired
    private RuleExecuteFacadeMixService ruleExecuteFacadeMixService;

    @Override
    protected ExportResponse listElement(BaseExportParam param, ActivityReviewQuery request) {
        ActivityReviewParam activityReviewParam = (ActivityReviewParam) param;
        List<ExportElement> data = new ArrayList<>();
        ActivityReviewResponse res = new ActivityReviewResponse();
        if (StringUtils.isBlank(request.getScrollId())) {
            res.setData(data);
            return res;
        }
        // 获取活动数据
        long activityId = activityReviewParam.getActivityId();
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        List<SubActivityDimensionBO> dimensionBOS = activityReviewParam.getDimensionList();
        // 实体统计信息
        List<ChildrenTaskDimensionBO> childrenTaskDimensionBOS = activityReviewParam.getChildrenTaskDimensionBOS();
        // 统计数据
        List<Long> entityIds = new ArrayList<>();
        childrenTaskDimensionBOS.forEach(
                childrenTaskDimensionBO -> entityIds.add(childrenTaskDimensionBO.getEntityId()));
        List<StatisticsDO> statisticsDOS = statisticsRecordService.queryStatisticsInfo(activityId, entityIds, null);
        Map<Long, StatisticsDO> statisticsDOMap =
                statisticsDOS.stream().collect(Collectors.toMap(StatisticsDO::getEntityId, e -> e));
        List<Long> taskIds = new ArrayList<>();
        childrenTaskDimensionBOS.forEach(e -> taskIds.addAll(e.getChildrenTaskIds()));
        // 获取指标配置
        List<IndicatorConfigBO> indicatorConfigs =
                indicatorService.batchListIndicatorConfig(Collections.singletonList(activityId), taskIds)
                        .stream().filter(e -> e.getType().intValue() == EntityTypeEnum.TASK.getCode())
                        .collect(Collectors.toList());
        List<Long> indicatorIdList = indicatorConfigs.stream()
                .map(IndicatorConfigBO::getIndicatorId).distinct().collect(Collectors.toList());
        Map<Long, IndicatorDO> indicatorMap = indicatorLocalCacheService.queryTaskIndicators(indicatorIdList);

        // 获取奖励配置
        List<AwardConfigBO> awardConfigBOS = awardConfigService.getMultiTaskAwardConfig(activityId, taskIds);
        List<Integer> awardTypeCodes =
                awardConfigBOS.stream().map(e -> e.getAwardType().getCode()).collect(Collectors.toList());
        // 多行数据
        List<ExportElement> exportElements = new ArrayList<>();
        for (ChildrenTaskDimensionBO childrenTaskDimensionBO : childrenTaskDimensionBOS) {
            List<String> rowData = new ArrayList<>();
            StatisticsDO statisticsDO = statisticsDOMap.get(childrenTaskDimensionBO.getEntityId());
            Map<String, Object> rowDataMap =
                    getActivityReviewData(childrenTaskDimensionBO, statisticsDO, dimensionBOS, indicatorMap,
                            awardTypeCodes, activityDO);
            List<HeaderConfig> headerConfigs = activityReviewParam.getHeader().get(SHEET_NAME);
            for (HeaderConfig headerConfig : headerConfigs) {
                rowData.add(String.valueOf(rowDataMap.get(headerConfig.getField())));
            }
            ExportElement exportElement = new ExportElement();
            exportElement.setData(rowData);
            exportElement.setSheet(SHEET_NAME);
            exportElements.add(exportElement);
        }
        res.setCursor("");
        res.setData(exportElements);
        log.info("res:{}", ObjectMapperUtils.toJSON(res));
        return res;
    }

    private Map<String, Object> getActivityReviewData(ChildrenTaskDimensionBO childrenTaskDimensionBO,
            StatisticsDO statisticsDO, List<SubActivityDimensionBO> dimensionBOS, Map<Long, IndicatorDO> indicatorDOMap,
            List<Integer> awardTypes, ActivityDO activityDO) {
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("childrenTaskDimensionBO", toJSON(childrenTaskDimensionBO));
        ruleContext.put("statisticsDO", toJSON(statisticsDO));
        ruleContext.put("dimensionBOS", toJSON(dimensionBOS));
        ruleContext.put("indicatorDOMap", toJSON(indicatorDOMap));
        ruleContext.put("awardTypes", toJSON(awardTypes));
        ruleContext.put("activityDO", toJSON(activityDO));
        ruleContext.put("scene", "getData");
        // 规则code
        // 规则解析
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        String ruleCode = config.getActivityExportDynamicBuildRuleCode();
        // 规则解析
        Map<String, Object> context = new HashMap<>();
        context.put("context", ruleContext);
        log.info("rule request:{}", ObjectMapperUtils.toJSON(context));
        DataResult<Object> dataResult = ruleExecuteFacadeMixService.executeRule(ruleCode, null, context);
        if (dataResult == null || dataResult.getData() == null) {
            log.error("[活动导出动态构建数据] 规则计算结果为空 context:{}", toJSON(context));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "规则计算结果为空");
        }
        Object resultJson = dataResult.getData();
        RuleExecuteResult ruleExecuteResult = fromJSON(resultJson, RuleExecuteResult.class);
        if (ruleExecuteResult.getResult() != 1) {
            log.error("[活动导出动态构建数据] 规则执行失败，context:{}, res:{}", toJSON(context),
                    toJSON(ruleExecuteResult));
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        return ruleExecuteResult.getData();
    }

    @Override
    protected String genFileName(BaseExportParam param) {
        return "活动复盘数据导出";
    }

    @Override
    protected String genZipFileName() {
        return "活动复盘数据导出";
    }

    @Override
    protected Map<String, List<List<String>>> getHeader(BaseExportParam param, ActivityReviewQuery request) {
        ActivityReviewParam activityReviewParam = (ActivityReviewParam) param;
        Map<String, List<List<String>>> res = Maps.newHashMap();
        activityReviewParam.getHeader().forEach((sheetName, headers) -> {
            List<List<String>> header =
                    headers.stream().map(e -> Lists.newArrayList(e.getName())).collect(Collectors.toList());
            res.put(sheetName, header);
        });
        return res;
    }

    @Override
    public ExportFileBizTypeEnum getBizType() {
        return ExportFileBizTypeEnum.ACTIVITY_REVIEW;
    }

    @Override
    public List<String> doXls(BaseExportParam exportParam) {
        ActivityReviewParam reviewParam = (ActivityReviewParam) exportParam;
        long activityId = reviewParam.getActivityId();
        List<ChildrenTaskDimensionBO> childrenTaskDimensionBOS = reviewParam.getChildrenTaskDimensionBOS();
        // 获取指标和奖励配置，构建动态表头
        List<Long> taskIds = new ArrayList<>();
        childrenTaskDimensionBOS.forEach(e -> taskIds.addAll(e.getChildrenTaskIds()));
        // 获取指标配置
        List<IndicatorConfigBO> indicatorConfigs =
                indicatorService.batchListIndicatorConfig(Collections.singletonList(activityId), taskIds)
                        .stream().filter(e -> e.getType().intValue() == EntityTypeEnum.TASK.getCode())
                        .collect(Collectors.toList());
        List<Long> indicatorIdList = indicatorConfigs.stream()
                .map(IndicatorConfigBO::getIndicatorId).distinct().collect(Collectors.toList());
        Map<Long, IndicatorDO> indicatorMap = indicatorLocalCacheService.queryTaskIndicators(indicatorIdList);

        // 获取奖励配置
        List<AwardConfigBO> awardConfigBOS = awardConfigService.getMultiTaskAwardConfig(activityId, taskIds);
        List<Integer> awardTypeCodes =
                awardConfigBOS.stream().map(e -> e.getAwardType().getCode()).collect(Collectors.toList());


        // 动态表头 -> 根据指标和奖励动态设置
        Map<String, List<HeaderConfig>> dynamicHeaders = buildActivityReviewExportHeader(indicatorMap, awardTypeCodes);

        log.info("dynamicHeaders:{}", ObjectMapperUtils.toJSON(dynamicHeaders));

        reviewParam.setHeader(dynamicHeaders);
        // 统计实体ID
        List<Long> entityIds = childrenTaskDimensionBOS.stream().map(ChildrenTaskDimensionBO::getEntityId)
                .collect(Collectors.toList());
        ActivityReviewQuery activityReviewQuery = new ActivityReviewQuery();
        activityReviewQuery.setActivityId(activityId);
        activityReviewQuery.setEntityIds(entityIds);
        activityReviewQuery.setTaskIds(taskIds);
        activityReviewQuery.setIndicatorIds(indicatorIdList);
        activityReviewQuery.setAwardTypes(awardTypeCodes);
        activityReviewQuery.setCursor("1");
        return doXls(exportParam, activityReviewQuery);
    }

    private Map<String, List<HeaderConfig>> buildActivityReviewExportHeader(Map<Long, IndicatorDO> indicatorMap,
            List<Integer> awardTypes) {
        Map<String, List<HeaderConfig>> result = new HashMap<>();
        // key sheetName, value 表头
        DynamicTableHeaderConfig tableHeaderConfig = getDynamicTableHeaderConfig(ACTIVITY_STATISTICS_DATA);
        // 固定表头设置
        List<HeaderConfig> headerConfigs = new ArrayList<>(tableHeaderConfig.getHeaderConfigs());
        headerConfigs.addAll(getDynamicHeader(indicatorMap, awardTypes));
        result.put(SHEET_NAME, headerConfigs);
        return result;
    }

    private List<HeaderConfig> getDynamicHeader(Map<Long, IndicatorDO> indicatorMap, List<Integer> awardTypes) {
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("indicatorMap", toJSON(indicatorMap));
        ruleContext.put("awardTypes", toJSON(awardTypes));
        ruleContext.put("scene", "getDynamicHeader");
        // 规则code
        // 规则解析
        StatisticsReviewConfig config = statisticsReviewConfig.getObject();
        String ruleCode = config.getActivityExportDynamicBuildRuleCode();
        // 规则解析
        Map<String, Object> context = new HashMap<>();
        context.put("context", ruleContext);
        log.info("rule request:{}", ObjectMapperUtils.toJSON(context));
        DataResult<Object> dataResult = ruleExecuteFacadeMixService.executeRule(ruleCode, null, context);
        if (dataResult == null || dataResult.getData() == null) {
            log.error("[活动导出动态构建数据] 规则计算结果为空 context:{}", toJSON(context));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "规则计算结果为空");
        }
        Object resultJson = dataResult.getData();
        log.info("resultJson:{}", resultJson.toString());
        RuleExecuteResult ruleExecuteResult = fromJSON(resultJson, RuleExecuteResult.class);
        log.info("ruleExecuteResult:{}", ObjectMapperUtils.toJSON(ruleExecuteResult));
        if (ruleExecuteResult.getResult() != 1) {
            log.error("[活动导出动态表头构建] 规则执行失败，context:{}, res:{}", toJSON(context),
                    toJSON(ruleExecuteResult));
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        String ruleData = ruleExecuteResult.getDataStr();
        return fromJSON(ruleData, List.class, HeaderConfig.class);
    }
}
