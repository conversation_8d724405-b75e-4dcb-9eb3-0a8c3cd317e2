package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.HashMap;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-22
 */
public class NotificationEntityStrategyFactory {

    private static final Map<NotificationEntityTypeEnum, AbstractNotificationEntityService>
            NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP = new HashMap();

    /**
     * service注册
     */
    public static void register(NotificationEntityTypeEnum entityType,
            AbstractNotificationEntityService strategyService) {
        NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP.put(entityType, strategyService);
    }

    /**
     * 根据推送实体类型获取对应策略service
     */
    public static AbstractNotificationEntityService getNotificationStrategyService(int entityType) {
        return NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP.get(NotificationEntityTypeEnum.of(entityType));
    }
}
