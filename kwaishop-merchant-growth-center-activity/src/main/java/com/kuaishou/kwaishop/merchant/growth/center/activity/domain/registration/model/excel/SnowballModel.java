package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.excel;

import java.util.Objects;

import com.alibaba.excel.annotation.ExcelProperty;
import com.kuaishou.kwaishop.merchant.growth.utils.excel.annotation.ExcelNotNull;
import com.kuaishou.kwaishop.merchant.growth.utils.excel.annotation.PositiveInteger;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-03
 */
@Data
public class SnowballModel {
    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("商家ID")
    private Long userId;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("基期新粉（个）")
    private Long basicNewFans;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉一档目标（个）")
    private Long newFansTargetLevel1;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉二档目标（个）")
    private Long newFansTargetLevel2;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉三档目标（个）")
    private Long newFansTargetLevel3;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉四档目标（个）")
    private Long newFansTargetLevel4;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("基期商业化消耗（分）")
    private Long basicCost;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("商业化消耗一档目标（分）")
    private Long costTargetLevel1;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("商业化消耗二档目标（分）")
    private Long costTargetLevel2;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("商业化消耗三档目标（分）")
    private Long costTargetLevel3;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("商业化消耗四档目标（分）")
    private Long costTargetLevel4;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("基期新粉LTV（分）")
    private Long basicNewFansLtv;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉LTV一档目标（分）")
    private Long newFansLtvTargetLevel1;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉LTV二档目标（分）")
    private Long newFansLtvTargetLevel2;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉LTV三档目标（分）")
    private Long newFansLtvTargetLevel3;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("新粉LTV四档目标（分）")
    private Long newFansLtvTargetLevel4;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("基期新粉风控GMV（分）")
    private Long basicNewFansRiskGmv;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("预估一档奖励（分）")
    private Long predictAwardLevel1;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("预估二档奖励（分）")
    private Long predictAwardLevel2;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("预估三档奖励（分）")
    private Long predictAwardLevel3;

    @PositiveInteger
    @ExcelNotNull
    @ExcelProperty("预估四档奖励（分）")
    private Long predictAwardLevel4;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SnowballModel demoMode = (SnowballModel) o;
        return Objects.equals(userId, demoMode.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId);
    }
}
