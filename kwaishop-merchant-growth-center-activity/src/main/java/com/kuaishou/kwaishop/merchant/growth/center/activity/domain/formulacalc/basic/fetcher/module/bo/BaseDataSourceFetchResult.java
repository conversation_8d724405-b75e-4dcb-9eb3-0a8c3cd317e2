package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseDataSourceFetchResult {

    /**
     * 原始指标基期数据
     */
    private List<Map<String, Object>> rawIndicatorBaseValues;

    /**
     * 多段原始指标基期数据
     */
    private Map<String/*segmentId*/, List<Map<String, Object>>> multiSegmentMap;
}
