package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushCreateBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
public interface NotificationPushCreateService {
    void notificationPushCreate(NotificationPushCreateBO notificationPushCreateBO);

    void triggerNotificationConfig(long userId, long configId, long eventTime);

    void sendDelayNotification(long userId, NotificationPushConfigBO config, long eventTime);

    void createNotificationConfig(ActivityDO activity, List<AwardConfigDO> awardConfigList, String code);

    List<Long> createNotificationConfig(ActivityDO activity, List<AwardConfigDO> awardConfigList,
            List<TaskDO> taskList, NotificationConfigBO notificationConfig, String operator);

    List<NotificationPushConfigBO> buildNotificationPushConfig(ActivityDO activity, List<AwardConfigDO> awardConfigList,
            List<TaskDO> taskList, NotificationConfigBO notificationConfig);

    void notificationPushCreate(long userId, List<NotificationPushConfigBO> configs, long eventTime);
}
