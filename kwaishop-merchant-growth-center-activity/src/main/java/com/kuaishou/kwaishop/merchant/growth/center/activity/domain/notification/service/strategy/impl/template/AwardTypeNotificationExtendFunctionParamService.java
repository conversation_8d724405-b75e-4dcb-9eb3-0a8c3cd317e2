package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum.CASH;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum.KWAI_COIN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.StrategyAwardService;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取奖励类型
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-07
 */
@Service
@Slf4j
@Deprecated
public class AwardTypeNotificationExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private StrategyAwardService strategyAwardService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.AWARD_TYPE);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        // 降级、黑名单校验
        NotificationExtendFunctionParamBO checkResult = checkDegradationAndBlackList(userId, configBO);
        if (checkResult != null) {
            return checkResult;
        }
        AwardConfigBO awardConfigBO =
                strategyAwardService.getStrategyMainAwardRuleConfigWithCache(configBO.getEntityId());
        if (awardConfigBO == null || awardConfigBO.getSendRuleType() == null) {
            log.error("[获取模板参数失败][获取奖励配置失败] Fail to query awardConfig, userId is :{}, "
                    + "notificationPushConfigBO is :{}", userId, toJSON(configBO));
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            // 查询不到奖励配置，停止推送
            result.setExecuteStatus(STOP);
            return result;
        }
        String awardType = TemplateParamTypeEnum.AWARD_TYPE.getName();
        switch (awardConfigBO.getAwardType()) {
            case CASH:
                params.put(awardType, CASH.getDesc());
                break;
            case KWAI_COIN:
                params.put(awardType, KWAI_COIN.getDesc());
                break;
            default:
                params.put(awardType, "流量");
        }
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }
}
