package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * 店铺群消息渠道推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-18
 */
@Lazy
@Slf4j
@Service
public class CsShopGroupChannelStrategyService implements NotificationChannelStrategyService {

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;

    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return NotificationChannelEnum.CS_SHOP_GROUP;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        // 店铺群消息推送
        notificationPushFetchService.shopMessagePush(userId, configBO, templateParams);
    }
}
