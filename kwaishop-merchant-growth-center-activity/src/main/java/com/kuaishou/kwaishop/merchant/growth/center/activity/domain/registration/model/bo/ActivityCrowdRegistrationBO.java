package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCrowdRegistrationBO {
    /**
     * 用户id，用户测试报名逻辑使用
     */
    private Long userId;
    /**
     * 本次报名的活动
     */
    private Long activityId;
    /**
     * 人群包id
     */
    private Long crowdId;

}
