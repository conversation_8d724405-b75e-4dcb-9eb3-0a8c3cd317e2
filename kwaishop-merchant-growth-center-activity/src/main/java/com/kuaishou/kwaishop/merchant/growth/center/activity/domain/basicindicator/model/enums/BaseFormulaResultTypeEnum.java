package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.ActivityBaseAlgorithmEnum;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-06
 */
@Getter
@AllArgsConstructor
public enum BaseFormulaResultTypeEnum {

    UNKNOWN(0, "UNKNOWN"),
    AVG(1, "AVG"),
    TOTAL(2, "TOTAL"),
    ;

    private final int type;

    private final String desc;

    private static final List<ActivityBaseAlgorithmEnum> TOTAL_ALGORITHM_LIST =
            Lists.newArrayList(ActivityBaseAlgorithmEnum.CUSTOM_BASE_CALC_RULE);

    public static BaseFormulaResultTypeEnum getByType(Integer type, BaseFormulaResultTypeEnum defaultType) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(defaultType);
    }

    public static BaseFormulaResultTypeEnum getByAlgorithmList(List<ActivityBaseAlgorithmEnum> algorithmEnums) {
        if (CollectionUtils.isEmpty(algorithmEnums)) {
            return UNKNOWN;
        }

        List<ActivityBaseAlgorithmEnum> distinctEnums = algorithmEnums.stream().distinct().collect(Collectors.toList());
        if (distinctEnums.size() > 1) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "基期算法类型异常");
        }

        ActivityBaseAlgorithmEnum activityBaseAlgorithmEnum = distinctEnums.get(0);

        return TOTAL_ALGORITHM_LIST.contains(activityBaseAlgorithmEnum) ? TOTAL : AVG;
    }
}
