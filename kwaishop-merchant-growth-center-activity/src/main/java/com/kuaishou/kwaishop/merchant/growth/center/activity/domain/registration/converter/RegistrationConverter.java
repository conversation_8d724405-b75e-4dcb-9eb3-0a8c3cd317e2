package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationCacheBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;

/**
 * 报名域转换器
 *
 * <AUTHOR>
 */
public interface RegistrationConverter {

    /**
     * BO转DO
     */
    UserRegistrationRecordDO convertToUserRecordDO(UserRegistrationRecordBO userRegistrationRecordBO);

    /**
     * 批量转换资格记录
     */
    List<UserRegistrationRecordDO> batchConvertToUserRecordDO(
            Collection<UserRegistrationRecordBO> userRegistrationRecords);

    /**
     * DO转BO
     */
    UserRegistrationRecordBO convertToUserRecordBO(UserRegistrationRecordDO userRegistrationRecordDO);

    AddRegistrationOptionBO convertToUserRegistrationOption(UserRegistrationRecordDO userRegistrationRecordDO);

    /**
     * 构造默认的报名记录对象
     */
    UserRegistrationRecordBO buildDefaultRecordBO(long userId, long activityId, EntityTypeEnum entityType,
            long entityId, String operator, String jsonData);

    /**
     * 活动报名记录的基值数据
     */
    Map<String, Object> resolveBasicDataFromJsonData(UserRegistrationRecordDO registrationRecordDO);

    /**
     * 解析报名规则选项
     */
    AddRegistrationOptionBO resolveCrowdOption(String drawRule);

    /**
     * 解析报名规则匹配条件
     */
    List<String> resolveDrawRuleFieldCondition(String drawRule, String field);

    /**
     * 解析报名规则选项从Model
     */
    AddRegistrationOptionBO resolveRegistrationOptionByExt(String ext);

    /**
     * 转换DO到cacheBO
     */
    UserRegistrationCacheBO convertToCacheBO(UserRegistrationRecordDO record);

    /**
     * 校验是否偏移任务的资格
     * @param userRegistration
     * @return
     */
    boolean checkOffsetEventRegistrationRecord(UserRegistrationRecordDO userRegistration);

    /**
     * 获取资格表里面的基期数据
     * @param userRegistrationRecordDO
     * @param indicatorId
     * @return
     */
    Long fetchBasicIndicatorValue(UserRegistrationRecordDO userRegistrationRecordDO, Long indicatorId);
}
