package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo;

import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-19
 */
@Data
public class OffsetEventMockData {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 事件mock发生时间
     * key: offsetEventType  value: happenTime
     */
    private Map<String, Long> mockHappenTime;
}
