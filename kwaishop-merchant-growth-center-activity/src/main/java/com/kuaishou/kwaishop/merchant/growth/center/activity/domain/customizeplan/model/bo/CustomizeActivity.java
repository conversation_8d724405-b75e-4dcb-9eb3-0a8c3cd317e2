package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizeActivity {

    /**
     * 标题 eg：开店准备
     */
    private String title;

    /**
     * 关联的任务系统活动ID
     */
    private long activityId;

    /**
     * 活动完成进度 0/3
     */
    private CompleteProgressBO activityProgress;

    /**
     * 活动关联的任务
     */
    private List<CustomizeTask> taskList;

    /**
     * 用户活动状态 对应UserActivityStatusEnum
     */
    private int userActivityStatus;
}
