package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-24
 */
@Getter
public enum LLMGenRedisKeyEnum {

    LLM_CONTENT_GEN_ASYNC_LOCK_PREFIX("llm:content:gen:async:lock:prefix"),
    ;

    private final String prefix;

    private static final String SEPARATOR = "_";

    private static final String COLON = ":";

    static {
        Map<String, LLMGenRedisKeyEnum> map = new HashMap<>();
        for (LLMGenRedisKeyEnum value : values()) {
            if (map.containsKey(value.getPrefix())) {
                throw new IllegalArgumentException(
                        "！！！！严重错误！！！！RedisKey prefix 冲突:" + value.getPrefix());
            }
            map.put(value.getPrefix(), value);
        }
    }

    LLMGenRedisKeyEnum(String prefix) {
        this.prefix = prefix;
    }

    public String getFullKeyJoinWithColon(CharSequence... keySuffix) {
        return this.prefix + String.join(COLON, keySuffix);
    }

    public String getFullKeyJoinWithColon(Long... keySuffix) {
        return this.prefix + Arrays.stream(keySuffix).map(String::valueOf).collect(Collectors.joining(COLON));
    }
}
