package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BasicFactorConfig {
    private Long userId;

    private IndicatorDO indicatorDO;

    private IndicatorConfigDO indicatorConfigDO;

    private BasicFactorConfigBO basicFactorConfigBO;

    private Integer scale;

    private Long cycleDuration;
}
