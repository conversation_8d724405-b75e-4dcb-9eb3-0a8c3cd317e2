package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OffsetPeriodConfigBO {
    /**
     * 偏移事件类型
     */
    private String offsetEventType;

    /**
     * 周期时间
     */
    private Long periodTime;

    /**
     * 相对时间
     */
    private Long relativeTime;
}
