package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegistrationDrawRule {

    /**
     * 领取条件列表
     */
    private List<RegistrationDrawRuleField> fields;

    /**
     * 领取控制选项
     */
    private List<RegistrationDrawOptionField> optionFields;

    /**
     * 领取条件操作
     */
    private String operator;
}
