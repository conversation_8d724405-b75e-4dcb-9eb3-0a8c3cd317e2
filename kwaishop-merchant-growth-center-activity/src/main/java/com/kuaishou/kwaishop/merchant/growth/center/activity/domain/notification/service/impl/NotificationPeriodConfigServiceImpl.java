package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.impl;


import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.localDateAndLocalTimeToMilli;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.localDateTimeToMilli;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.localDateToMilli;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToLocalDate;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToLocalDateTime;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPeriodConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * 周期性配置periodConfig 解析service
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
@Lazy
@Slf4j
@Service
public class NotificationPeriodConfigServiceImpl implements NotificationPeriodConfigService {

    private static final String ALL = "*";
    private static final String TO = "-";
    private static final String MULTI = ",";
    private static final String STEP = "/";
    private static final int ZERO = 0;
    private static final int TWENTY_FOUR = 24;
    private static final int SIXTY = 60;

    /**
     * 计算出一个最近候选绝对时间
     * @param notificationPeriod 配置对象
     * @param beginTime 起始时间点
     * @return 候选时间
     */
    public long parseAbsoluteLatestTime(NotificationPeriodBO notificationPeriod, long beginTime) {
        List<LocalDate> dateList = parseDate(notificationPeriod.getDay(), beginTime);
        List<Integer> hourList = parseTime(notificationPeriod.getHour(), ZERO, TWENTY_FOUR);
        List<Integer> minuteList = parseTime(notificationPeriod.getMinute(), ZERO, SIXTY);
        for (LocalDate candidateDate : dateList) {
            for (int hour : hourList) {
                for (int minute : minuteList) {
                    LocalTime candidateTime = LocalTime.of(hour, minute);
                    long candidateTimeStamp = localDateAndLocalTimeToMilli(candidateDate, candidateTime);
                    if (candidateTimeStamp > System.currentTimeMillis()) {
                        return candidateTimeStamp;
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 计算出一个最近候选相对时间
     *
     * @param notificationPeriod 配置对象
     * @param beginTime 起始时间点
     * @return 候选时间
     */
    public long parseRelativeLatestTime(NotificationPeriodBO notificationPeriod, long beginTime) {
        List<LocalDate> dateList = parseDate(notificationPeriod.getDay(), beginTime);
        List<Integer> hourList = parseTime(notificationPeriod.getHour(), ZERO, TWENTY_FOUR);
        List<Integer> minuteList = parseTime(notificationPeriod.getMinute(), ZERO, SIXTY);
        int hourOffset = milliToLocalDateTime(beginTime).getHour();
        int minuteOffset = milliToLocalDateTime(beginTime).getMinute();
        for (LocalDate candidateDate : dateList) {
            for (int hour : hourList) {
                for (int minute : minuteList) {
                    LocalTime candidateTime = LocalTime.of(hour, minute);
                    LocalDateTime candidateDateTime =
                            milliToLocalDateTime(localDateAndLocalTimeToMilli(candidateDate, candidateTime))
                                    .plusHours(hourOffset).plusMinutes(minuteOffset);
                    if (localDateTimeToMilli(candidateDateTime) > System.currentTimeMillis()) {
                        return localDateTimeToMilli(candidateDateTime);
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 解析时间配置
     *
     * @param timeConfig 时间配置
     * @param begin 范围起始
     * @param end 范围结束
     * @return 列表
     */
    private List<Integer> parseTime(String timeConfig, int begin, int end) {
        if (StringUtils.isEmpty(timeConfig)) {
            return Lists.newArrayList(0);
        }
        // 如果是"*"
        if (ALL.equals(timeConfig)) {
            return buildTimeList(begin, end);
        }
        // 如果是"x-x"
        if (timeConfig.contains(TO) && !timeConfig.contains(STEP)) {
            int left = Integer.parseInt(timeConfig.split(TO)[0]);
            int right = Integer.parseInt(timeConfig.split(TO)[1]);
            return buildTimeList(left, right);
        }
        // 如果是"x,x"
        if (timeConfig.contains(MULTI)) {
            String[] config = timeConfig.split(MULTI);
            ArrayList<Integer> timeList = new ArrayList<>();
            for (String time : config) {
                timeList.add(Integer.parseInt(time));
            }
            return timeList;
        }
        // 如果是"x-x/x"
        if (timeConfig.contains(STEP)) {
            String range = timeConfig.split(STEP)[0];
            int step = Integer.parseInt(timeConfig.split(STEP)[1]);
            int left;
            int right;
            if (ALL.equals(range)) {
                left = begin;
                right = end;
            } else {
                left = Integer.parseInt(range.split(TO)[0]);
                right = Integer.parseInt(range.split(TO)[1]);
            }
            ArrayList<Integer> timeList = new ArrayList<>();
            for (int time = left; time < right; time += step) {
                timeList.add(time);
            }
            return timeList;
        }
        // 只能是单数了
        return Collections.singletonList(Integer.parseInt(timeConfig));
    }

    /**
     * 解析日期配置
     *
     * @param dateConfig 日期配置
     * @param beginTime 范围起始
     * @return 列表
     */
    private List<LocalDate> parseDate(String dateConfig, long beginTime) {
        LocalDate beginDate = milliToLocalDate(beginTime);
        // 如果是 "", 返回今天
        if (StringUtils.isEmpty(dateConfig)) {
            LocalDate today = milliToLocalDate(System.currentTimeMillis());
            return Arrays.asList(today);
        }
        // 如果是"*"，返今明两天
        if (ALL.equals(dateConfig)) {
            LocalDate today = milliToLocalDate(System.currentTimeMillis());
            LocalDate tomorrow = today.plusDays(1);
            return Arrays.asList(today, tomorrow);
        }
        // 如果是"*/x"，如果刚好是今天，就返今天和下次，否则返下次
        if (dateConfig.contains(ALL)) {
            ArrayList<LocalDate> dateList = new ArrayList<>();
            int step = Integer.parseInt(dateConfig.split(STEP)[1]);
            LocalDate today = milliToLocalDate(System.currentTimeMillis());
            LocalDate candidateDate = beginDate;
            while (true) {
                if (candidateDate.isAfter(today)) {
                    dateList.add(candidateDate);
                    break;
                }
                if (candidateDate.isEqual(today)) {
                    dateList.add(candidateDate);
                }
                candidateDate = candidateDate.plusDays(step);
            }
            return dateList;
        }
        // 如果是"x-x"
        if (dateConfig.contains(TO) && !dateConfig.contains(STEP)) {
            int left = Integer.parseInt(dateConfig.split(TO)[0]);
            int right = Integer.parseInt(dateConfig.split(TO)[1]);
            return buildDateList(left, right, beginDate);
        }
        // 如果是"x,x"
        if (dateConfig.contains(MULTI)) {
            String[] config = dateConfig.split(MULTI);
            ArrayList<LocalDate> dateList = new ArrayList<>();
            for (String date : config) {
                if (Integer.parseInt(date) < 1) {
                    // 如果小于 1 直接跳过不构建
                    continue;
                }
                LocalDate candidateDate = beginDate.plusDays(Integer.parseInt(date) - 1);
                if (localDateToMilli(candidateDate)
                        >= localDateToMilli(milliToLocalDate(System.currentTimeMillis()))) {
                    dateList.add(candidateDate);
                }
            }
            return dateList;
        }
        // 如果是"x-x/x"
        if (dateConfig.contains(STEP)) {
            String range = dateConfig.split(STEP)[0];
            int step = Integer.parseInt(dateConfig.split(STEP)[1]);
            int left = Integer.parseInt(range.split(TO)[0]);
            int right = Integer.parseInt(range.split(TO)[1]);
            ArrayList<LocalDate> dateList = new ArrayList<>();
            LocalDate candidateDate = beginDate.plusDays(left - 1);
            LocalDate endDate = beginDate.plusDays(right - 1);
            while (candidateDate.isBefore(endDate)) {
                dateList.add(candidateDate);
                candidateDate = candidateDate.plusDays(step);
            }
            return dateList;
        }
        // 只剩单值了
        return Collections.singletonList(beginDate.plusDays(Integer.parseInt(dateConfig) - 1));
    }


    /**
     * 构造日期列表
     *
     * @param start 开始
     * @param end 结束
     * @param beginDate 开始日期
     * @return 列表
     */
    private List<LocalDate> buildDateList(int start, int end, LocalDate beginDate) {
        List<LocalDate> list = new ArrayList<>();
        for (int i = start - 1; i < end; i++) {
            LocalDate date = beginDate.plusDays(i);
            if (localDateToMilli(date) >= localDateToMilli(milliToLocalDate(System.currentTimeMillis()))) {
                list.add(date);
            }
        }
        return list;
    }

    /**
     * 构造列表
     *
     * @param start 开始
     * @param end 结束
     * @return 列表
     */
    private List<Integer> buildTimeList(int start, int end) {
        ArrayList<Integer> list = new ArrayList<>();
        for (int i = start; i < end; i++) {
            list.add(i);
        }
        return list;
    }
}
