package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.notificationCode;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static java.util.Collections.singletonList;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityCycleTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationConfigPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationCodeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.TaskExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
@Lazy
@Slf4j
@Service
public class ActivityStartStrategyService extends AbstractNotificationCodeStrategyService {

    private static final String HOUR = "hour";

    private static final String MINUTE = "minute";

    @Override
    public Set<NotificationCodeEnum> getNotificationCodeSet() {
        return Sets.newHashSet(NotificationCodeEnum.ACTIVITY_START);
    }

    @Override
    public List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList, List<AwardConfigDO> awardConfigList,
            NotificationConfigBO notificationConfig) {
        NotificationAdminDomainBO notificationAdminDomainBO = getNotificationAdminDomainBO(activity, notificationConfig);
        List<NotificationPushConfigBO> res = Lists.newArrayList();
        taskList = taskList.stream().filter(task -> task.getParentTask() == 0).collect(Collectors.toList());

        taskList.forEach(parentTask -> {
            try {
                NotificationPushConfigBO notificationPushConfigBO =
                        buildNotificationPushConfigTemplate(notificationAdminDomainBO, activity, notificationConfig, parentTask.getId());

                if (Objects.isNull(notificationPushConfigBO.getPeriodConfig())) {
                    PeriodConfigBO periodConfigBO = buildPeriodConfig(parentTask, notificationAdminDomainBO.getParamMap());
                    notificationPushConfigBO.setPeriodConfig(periodConfigBO);
                }
                res.add(notificationPushConfigBO);
            } catch (Exception e) {
                log.error("[触达配置创建] activityStart 创建失败, activityId:{}, notificationConfig:{}",
                        activity.getId(), toJSON(notificationConfig), e);
                throw new BizException(BasicErrorCode.SERVER_ERROR, "任务开始提醒触达配置创建失败");
            }
        });

        return res;
    }

    private PeriodConfigBO buildPeriodConfig(TaskDO parentTask, Map<String, Object> paramMap) {
        if (MapUtils.isEmpty(paramMap)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "任务开始提醒触达配置参数缺失");
        }

        String day = calculateNotifyDay(parentTask);
        Object hourObject = paramMap.get(HOUR);
        Object minuteObject = paramMap.get(MINUTE);
        if (!(hourObject instanceof String) || !(minuteObject instanceof String)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "任务开始提醒触达配置参数有误");
        }
        NotificationPeriodBO notificationPeriod = NotificationPeriodBO.builder()
                .day(day)
                .hour((String) hourObject)
                .minute((String) minuteObject)
                .periodType(NotificationConfigPeriodTypeEnum.ABSOLUTE.getVal())
                .build();

        return PeriodConfigBO.builder()
                .notificationPeriods(singletonList(notificationPeriod))
                .build();
    }

    private String calculateNotifyDay(TaskDO parentTask) {
        //获取开始时间
        long startTime = parentTask.getStartTime();
        //获取任务结束时间
        long endTime = parentTask.getEndTime();
        Integer periodType = parentTask.getPeriodType();
        TaskPeriodTypeEnum typeEnum = TaskPeriodTypeEnum.of(periodType);
        String day = "";
        switch (typeEnum) {
            case DAY:
            case CUSTOM:
            case ABSOLUTE:
                //周期类型
                Integer cycleType = ActivityCycleTypeEnum.STABLE.getCode();
                //周期天数
                long cycleDay = 0;
                TaskExtBO taskExt = ObjectMapperUtils.fromJSON(parentTask.getExt(), TaskExtBO.class);
                if (Objects.nonNull(taskExt) && Objects.nonNull(taskExt.getCycleConfig())) {
                    cycleType = taskExt.getCycleConfig().getCycleType();
                    cycleDay = Objects.isNull(taskExt.getCycleConfig().getCycleDay())
                            ? 0 : taskExt.getCycleConfig().getCycleDay();
                }
                ActivityCycleTypeEnum cycleTypeEnum = ActivityCycleTypeEnum.getByCode(cycleType);
                switch (cycleTypeEnum) {
                    case DAILY:
                    case CUSTOMIZE:
                        // 计算周期间隔
                        long millionSecondsOfDayInterval = TimeUnit.DAYS.toMillis(cycleDay);
                        // 计算全部周期数量, 兼容 23:59:59 此处向上取整, 同时根据惰性最大周期数量取 min
                        int totalCycleNum = (int) Math.max(((endTime - startTime) / millionSecondsOfDayInterval) + 1, 1);
                        //第一个周期任务触达时间
                        List<Long> dayList = new ArrayList<>();
                        dayList.add(1L);
                        for (int i = 1; i < totalCycleNum; i++) {
                            dayList.add(dayList.get(i - 1) + cycleDay);
                        }
                        day = StringUtils.join(dayList, SymbolConstants.COMMA);
                        break;
                    case STABLE:
                        day = String.valueOf(1L);
                        break;
                    default:
                        throw new BizException(BasicErrorCode.PARAM_INVALID, "任务循环类型不存在");
                }
                break;
            case RELATIVE:
            case DELAY_RELATIVE:
                day = String.valueOf(1L);
                break;
            default:
                throw new BizException(BasicErrorCode.PARAM_INVALID, "任务周期类型不存在");
        }
        return day;
    }
}
