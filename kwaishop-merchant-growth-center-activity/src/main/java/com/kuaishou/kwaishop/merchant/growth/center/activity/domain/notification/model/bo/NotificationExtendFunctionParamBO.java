package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推送扩展功能参数
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationExtendFunctionParamBO {

    private Map<String, String> templateParamMap;

    private NotificationExecuteStatusEnum executeStatus;
}

