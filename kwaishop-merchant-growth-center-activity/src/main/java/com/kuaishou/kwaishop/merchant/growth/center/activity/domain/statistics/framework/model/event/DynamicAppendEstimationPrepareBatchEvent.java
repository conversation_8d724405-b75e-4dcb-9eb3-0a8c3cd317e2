package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.EstimateCrowdAppendResultVO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DynamicAppendEstimationPrepareBatchEvent extends BatchExecuteEvent {
    /**
     * 策略ID
     */
    private Long strategyId;

    private String strategyVersion;

    /**
     * 追加离线日期
     */
    private String pDate;

    private EstimationStrategyTypeEnum strategyType;

    private List<EstimateCrowdAppendResultVO> crowdAppendResultList;

    /**
     * 人群配置
     */
    private CrowdConfigBO crowdConfigBO;

    /**
     * 基期配置
     */
    private BasicConfigBO basicConfigBO;


    /**
     * 活动天数
     */
    private Integer activityDays;


    /**
     * 基期指标config
     */
    private Map<Long, IndicatorDO> baseIndicatorMap;

    public boolean invalid() {
        if (strategyId == null || strategyId <= 0 || strategyType == null) {
            return true;
        }
        if (CollectionUtils.isEmpty(crowdAppendResultList) || MapUtils.isEmpty(baseIndicatorMap)) {
            return true;
        }
        EstimateCrowdAppendResultVO invalidResult = crowdAppendResultList.stream()
                .filter(EstimateCrowdAppendResultVO::invalid)
                .findAny()
                .orElse(null);
        return invalidResult != null;
    }
}
