package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Getter
@AllArgsConstructor
public enum AdmissionRuleEnum {

    UNKNOWN("unknown", "未知"),

    DEFAULT("default", "默认"),
    ;

    /**
     * 定制计划准入规则类型
     */
    private final String ruleType;

    /**
     * 描述
     */
    private final String desc;

    public static AdmissionRuleEnum of(String ruleType) {
        for (AdmissionRuleEnum val : AdmissionRuleEnum.values()) {
            if (val.getRuleType().equals(ruleType)) {
                return val;
            }
        }
        return UNKNOWN;
    }
}
