package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.StrategyBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.ActivityStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 获取策略月份
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-04
 */
@Service
@Slf4j
public class StrategyMonthNotificationExtendFunctionParamService
        extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private ActivityStrategyService activityStrategyService;

    @Autowired
    private AwardConfigService awardConfigService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.STRATEGY_MONTH);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        // 降级、黑名单校验
        NotificationExtendFunctionParamBO checkResult = checkDegradationAndBlackList(userId, configBO);
        if (checkResult != null) {
            return checkResult;
        }
        Long strategyId = getStrategyId(configBO);
        StrategyBO strategyBO = activityStrategyService.queryBaseStrategyByIdWithLocalCache(strategyId);
        if (strategyBO == null) {
            log.error("[获取模板参数失败] Fail to get template params , userId is :{}, strategyId is :{}, "
                    + "notificationPushConfigBO is :{}", userId, strategyId, configBO);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            result.setExecuteStatus(STOP);
            return result;
        }
        int month = DateUtils.getMothOfYear(strategyBO.getStartTime());
        params.put(TemplateParamTypeEnum.STRATEGY_MONTH.getName(), String.valueOf(month));
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

}
