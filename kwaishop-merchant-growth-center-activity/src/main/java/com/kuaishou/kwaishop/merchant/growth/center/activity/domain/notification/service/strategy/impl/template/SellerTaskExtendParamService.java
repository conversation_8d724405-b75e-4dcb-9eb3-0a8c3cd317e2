package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_BUTTON_ALL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_BUTTON_ITEMS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_BUTTON_ITEMS_STAGE1;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_BUTTON_ITEMS_STAGE2;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_BUTTON_ITEMS_STAGE3;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_REMAIN_COUNT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.TASK_REMAIN_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.csShopGroupMessageConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.sellerGroupConfig;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.cs.common.card.protobuf.BodyContentTypeEnum;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.BodyContentItem;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.ExtraAction;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.ExtraActionStatusEnum;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.ExtraActionTypeEnum;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.ExtraUrl;
import com.kuaishou.kwaishop.cs.shop.group.scene.protobuf.TaskContentItem;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.FrontResourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ButtonInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ComponentConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ComponentDataBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.FrontConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.StrategyInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.TargetInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.ComponentNameEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.helper.ComponentDataExpHelper;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.CsShopGroupLogParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.CsShopGroupMessageConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.CsShopGroupTaskFieldConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.CsShopGroupTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.SellerGroupConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.TimeConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-30
 */
@Slf4j
@Service
public class SellerTaskExtendParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private ComponentDataExpHelper componentDataExpHelper;

    private static final String TRACKING_PRE = "tracking";

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TASK_BUTTON_ITEMS, TASK_BUTTON_ITEMS_STAGE1, TASK_BUTTON_ITEMS_STAGE2, TASK_BUTTON_ITEMS_STAGE3, TASK_REMAIN_COUNT,
                TASK_REMAIN_TIME, TASK_BUTTON_ALL);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();
        // 获取活动ID
        long activityId = configBO.getActivityId();
        // 获取活动元信息中frontConfig
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null) {
            log.error("[任务模板参数] 活动信息获取失败");
            result.setExecuteStatus(STOP);
            return result;
        }
        List<FrontConfigBO> frontConfigBOList = fromJSON(activityDO.getFrontConfig(), List.class, FrontConfigBO.class);
        if (CollectionUtils.isEmpty(frontConfigBOList)) {
            log.error("[任务模板参数] 活动前端配置获取失败");
            result.setExecuteStatus(STOP);
            return result;
        }
        // 获取推送模板配置中的店铺群配置
        CsShopGroupTemplateConfigBO templateConfigBO = fromJSON(configBO.getTemplateConfig(), CsShopGroupTemplateConfigBO.class);
        if (templateConfigBO.getCsShopGroupConfig() == null) {
            log.error("[任务模板参数] 店铺群配置获取失败");
            result.setExecuteStatus(STOP);
            return result;
        }
        // 获取全部任务按钮参数信息
        List<BodyContentItem> bodyContentItem = buildAllTaskButtonItem(activityId);
        params.put(TASK_BUTTON_ALL.getName(), toJSON(bodyContentItem));
        // 获取活动下所有子任务
        List<TaskDO> taskList = taskLocalCacheService.getTaskListByActivityId(activityId);
        List<TaskDO> childTaskList = taskList.stream().filter(e -> e.getParentTask() != 0).collect(Collectors.toList());
        Map<Long, Long> taskParentChildMap = childTaskList.stream().collect(Collectors.toMap(TaskDO::getParentTask, TaskDO::getId));
        Map<Long, String> taskIdNameMap = childTaskList.stream().collect(Collectors.toMap(TaskDO::getId, TaskDO::getName));
        // 获取各阶段任务列表
        SellerGroupConfigBO sellerGroupConfigBO = sellerGroupConfig.getObject();
        List<Long> stageFirstTasks = Lists.newArrayList();
        List<Long> stageSecondTasks = Lists.newArrayList();
        List<Long> stageThirdTasks = Lists.newArrayList();
        List<Long> allStageTasks = Lists.newArrayList();
        for (TaskDO taskDO : childTaskList) {
            String tags = taskDO.getTags();
            List<String> tagList = fromJSON(tags, List.class, String.class);
            if (tagStageMatch(tagList, sellerGroupConfigBO.getTagStageFirst())) {
                stageFirstTasks.add(taskDO.getId());
            } else if (tagStageMatch(tagList, sellerGroupConfigBO.getTagStageSecond())) {
                stageSecondTasks.add(taskDO.getId());
            } else if (tagStageMatch(tagList, sellerGroupConfigBO.getTagStageThird())) {
                stageThirdTasks.add(taskDO.getId());
            }
            allStageTasks.add(taskDO.getId());
        }

        // 获取用户未完成阶段任务
        List<Integer> incompleteTaskStatusEnums = Lists.newArrayList(UserTaskStatusEnum.PROCESSING.getValue());
        List<UserTaskRecordDO> allStageTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordList(userId, activityId, allStageTasks).stream()
                        .filter(e -> incompleteTaskStatusEnums.contains(e.getStatus())).sorted(Comparator.comparing(UserTaskRecordDO::getTaskId))
                        .collect(Collectors.toList());
        List<UserTaskRecordDO> stageFirstTaskRecordList = allStageTaskRecordList.stream()
                .filter(e -> stageFirstTasks.contains(e.getTaskId())).collect(Collectors.toList());
        List<UserTaskRecordDO> stageSecondTaskRecordList = allStageTaskRecordList.stream()
                .filter(e -> stageSecondTasks.contains(e.getTaskId())).collect(Collectors.toList());
        List<UserTaskRecordDO> stageThirdTaskRecordList = allStageTaskRecordList.stream()
                .filter(e -> stageThirdTasks.contains(e.getTaskId())).collect(Collectors.toList());

        // 埋点, 阶段任务列表
        if (CollectionUtils.isNotEmpty(allStageTaskRecordList)) {
            params.put(String.join("_", TRACKING_PRE, TASK_BUTTON_ITEMS.getName()),
                    getTrackingContent(allStageTaskRecordList));
        }
        if (CollectionUtils.isNotEmpty(stageFirstTaskRecordList)) {
            params.put(String.join("_", TRACKING_PRE, TASK_BUTTON_ITEMS_STAGE1.getName()),
                    getTrackingContent(stageFirstTaskRecordList));
        }
        if (CollectionUtils.isNotEmpty(stageSecondTaskRecordList)) {
            params.put(String.join("_", TRACKING_PRE, TASK_BUTTON_ITEMS_STAGE2.getName()),
                    getTrackingContent(stageSecondTaskRecordList));
        }
        if (CollectionUtils.isNotEmpty(stageThirdTaskRecordList)) {
            params.put(String.join("_", TRACKING_PRE, TASK_BUTTON_ITEMS_STAGE3.getName()),
                    getTrackingContent(stageThirdTaskRecordList));
        }

        // 赋值未完成任务数量
        params.put(TASK_REMAIN_COUNT.getName(), String.valueOf(allStageTaskRecordList.size()));
        // 赋值任务剩余时间
        if (CollectionUtils.isNotEmpty(allStageTaskRecordList)) {
            UserTaskRecordDO userTaskRecordDO = allStageTaskRecordList.get(0);
            params.put(TASK_REMAIN_TIME.getName(), remainDays(userTaskRecordDO.getEndTime()));
        }
        long taskId = 0;
        if (configBO.getEntityType() == NotificationEntityTypeEnum.TASK.getVal()) {
            taskId = configBO.getEntityId();
        }
        TaskDO taskDO = taskLocalCacheService.getTaskByTaskId(taskId);
        String taskName = "";
        if (taskDO != null) {
            taskName = taskDO.getName();
            if (StringUtils.isNotBlank(taskName)) {
                params.put(TASK_NAME.getName(), taskName);
            }
        }
        List<FrontConfigBO> frontConfigBOS = frontConfigBOList.stream().filter(frontConfig -> frontConfig.getScene().equals(
                FrontResourceTypeEnum.COMPONENT_BUILD.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(frontConfigBOS)) {
            return result;
        }
        FrontConfigBO frontConfigBO = frontConfigBOS.get(0);
        List<ComponentConfigBO> componentList = frontConfigBO.getComponent();
        Map<Long, CsShopGroupTaskFieldConfigBO> taskFieldConfigBOMap = Maps.newHashMap();
        for (ComponentConfigBO componentConfigBO : componentList) {
            CsShopGroupTaskFieldConfigBO taskFieldConfigBO;
            String data = componentDataExpHelper.getComponentData(componentConfigBO, activityId, userId);
            ComponentDataBO componentDataBO = fromJSON(data, ComponentDataBO.class);
            String componentName = componentConfigBO.getComponentName();
            Long childTaskId = taskParentChildMap.getOrDefault(componentConfigBO.getEntityId(), 0L);
            taskFieldConfigBO = buildTaskConfigBO(componentDataBO, componentName, taskIdNameMap, childTaskId);
            taskFieldConfigBOMap.put(childTaskId, taskFieldConfigBO);
        }

        List<BodyContentItem> stageFirstBodyContentItems = buildBodyContentItems(stageFirstTaskRecordList, taskFieldConfigBOMap);
        List<BodyContentItem> stageSecondBodyContentItems = buildBodyContentItems(stageSecondTaskRecordList, taskFieldConfigBOMap);
        List<BodyContentItem> stageThirdBodyContentItems = buildBodyContentItems(stageThirdTaskRecordList, taskFieldConfigBOMap);
        List<BodyContentItem> allStageBodyContentItems = buildBodyContentItems(allStageTaskRecordList, taskFieldConfigBOMap);
        params.put(TASK_BUTTON_ITEMS_STAGE1.getName(), toJSON(stageFirstBodyContentItems));
        params.put(TASK_BUTTON_ITEMS_STAGE2.getName(), toJSON(stageSecondBodyContentItems));
        params.put(TASK_BUTTON_ITEMS_STAGE3.getName(), toJSON(stageThirdBodyContentItems));
        params.put(TASK_BUTTON_ITEMS.getName(), toJSON(allStageBodyContentItems));
        result.setTemplateParamMap(params);
        return result;
    }

    private String getTrackingContent(List<UserTaskRecordDO> userTaskRecordDOList) {
        if (userTaskRecordDOList.size() > 3) {
            userTaskRecordDOList = userTaskRecordDOList.subList(0, 3);
        }
        List<String> taskIds =
                userTaskRecordDOList.stream().map(e -> String.valueOf(e.getTaskId())).collect(Collectors.toList());
        return String.join(",", taskIds);
    }

    private List<BodyContentItem> buildAllTaskButtonItem(Long activityId) {
        CsShopGroupMessageConfigBO messageConfigBO = csShopGroupMessageConfig.getObject();
        if (messageConfigBO == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID.getCode(), "获取店铺群消息配置失败");
        }
        // 查看全部任务埋点
        CsShopGroupLogParamBO paramBO = CsShopGroupLogParamBO.builder()
                .buttonName(messageConfigBO.getAllTaskButtonName())
                .messageContent(String.valueOf(activityId))
                .robotMessageType(3)
                .build();
        ExtraAction extraAction = ExtraAction.newBuilder()
                .setTitle(messageConfigBO.getAllTaskButtonName())
                .setUrlForClient(messageConfigBO.getGrowthCenterJumpUrl())
                .setUrlForWeb(messageConfigBO.getGrowthCenterPcJumpUrl())
                .setType(ExtraActionTypeEnum.BUTTON_VALUE)
                .setLogParams(toJSON(paramBO))
                .setStatus(ExtraActionStatusEnum.WHITE_VALUE).build();
        TaskContentItem taskContentItem = TaskContentItem.newBuilder()
                .setExtraAction(extraAction).build();
        BodyContentItem bodyContentItem = BodyContentItem.newBuilder()
                .setContentType(BodyContentTypeEnum.COUPON_VALUE)
                .setContentItem(toJSON(taskContentItem)).build();
        return Collections.singletonList(bodyContentItem);
    }

    private List<BodyContentItem> buildBodyContentItems(List<UserTaskRecordDO> userTaskRecordDOList,
            Map<Long, CsShopGroupTaskFieldConfigBO> taskFieldConfigBOMap) {
        List<BodyContentItem> result = Lists.newArrayList();
        userTaskRecordDOList =
                userTaskRecordDOList.stream().sorted(Comparator.comparing(UserTaskRecordDO::getTaskId))
                        .collect(Collectors.toList());
        if (userTaskRecordDOList.size() > 3) {
            userTaskRecordDOList = userTaskRecordDOList.subList(0, 3);
        }
        for (UserTaskRecordDO userTaskRecordDO : userTaskRecordDOList) {
            Long taskId = userTaskRecordDO.getTaskId();
            CsShopGroupTaskFieldConfigBO taskFieldConfigBO = taskFieldConfigBOMap.getOrDefault(taskId, null);
            if (taskFieldConfigBO != null) {
                String title = taskFieldConfigBO.getTitle();
                String desc = taskFieldConfigBO.getStrategyText();
                ExtraUrl extraUrl = ExtraUrl.newBuilder()
                        .setUrlForWeb(taskFieldConfigBO.getStrategyJumpUrl())
                        .setUrlForClient(taskFieldConfigBO.getStrategyJumpUrl()).build();
                ExtraAction extraAction = ExtraAction.newBuilder()
                        .setTitle(taskFieldConfigBO.getButtonText())
                        .setUrlForClient(taskFieldConfigBO.getButtonJumpUrl())
                        .setUrlForWeb(taskFieldConfigBO.getButtonPcJumpUrl())
                        .setType(taskFieldConfigBO.getType())
                        .setStatus(taskFieldConfigBO.getStatus())
                        .setLogParams(taskFieldConfigBO.getLogParams()).build();
                // 添加埋点, 攻略部分
                CsShopGroupLogParamBO logParamBO =
                        CsShopGroupLogParamBO.builder().buttonName(desc).messageContent(String.valueOf(taskId))
                                .robotMessageType(3).build();
                TaskContentItem taskContentItem = TaskContentItem.newBuilder()
                        .setTitle(title)
                        .setDesc(desc)
                        .setExtraUrl(extraUrl)
                        .setExtraAction(extraAction)
                        .setLogParams(StringUtils.isNotEmpty(desc) ? toJSON(logParamBO) : "").build();
                BodyContentItem bodyContentItem = BodyContentItem.newBuilder()
                        .setContentType(BodyContentTypeEnum.COMMON_CONTENT_VALUE)
                        .setContentItem(toJSON(taskContentItem)).build();
                result.add(bodyContentItem);
            }
        }
        return result;
    }

    private CsShopGroupTaskFieldConfigBO buildTaskConfigBO(ComponentDataBO componentDataBO, String componentName,
            Map<Long, String> taskIdNameMap, Long taskId) {
        CsShopGroupMessageConfigBO csShopGroupMessageConfigBO = csShopGroupMessageConfig.getObject();
        if (csShopGroupMessageConfigBO == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "获取店铺群消息配置失败");
        }
        CsShopGroupTaskFieldConfigBO taskFieldConfigBO = null;
        ComponentNameEnum componentNameEnum = ComponentNameEnum.getByName(componentName);
        StrategyInfoBO strategyInfo = componentDataBO.getStrategyInfo();
        ButtonInfoBO buttonInfo = componentDataBO.getButtonInfo();
        List<TargetInfoBO> targetInfo = componentDataBO.getTargetInfo();
        // 添加埋点, 按钮部分
        CsShopGroupLogParamBO logParamBO =
                CsShopGroupLogParamBO.builder().buttonName(buttonInfo.getText()).messageContent(String.valueOf(taskId))
                        .robotMessageType(3).build();
        if (componentNameEnum.equals(ComponentNameEnum.SIMPLE)) {
            taskFieldConfigBO = CsShopGroupTaskFieldConfigBO.builder()
                    .title(taskIdNameMap.getOrDefault(taskId, ""))
                    .strategyText(strategyInfo.getText())
                    .strategyJumpUrl(strategyInfo.getJumpUrl())
                    .strategyPcJumpUrl(strategyInfo.getPcJumpUrl())
                    .buttonText(buttonInfo.getText())
                    .buttonJumpUrl(buttonInfo.getJumpUrl())
                    .buttonPcJumpUrl(buttonInfo.getPcJumpUrl())
                    .type(ExtraActionTypeEnum.BUTTON_VALUE)
                    .logParams(toJSON(logParamBO))
                    .status(ExtraActionStatusEnum.WHITE_VALUE).build();
        } else if (componentNameEnum.equals(ComponentNameEnum.STEP)) {
            taskFieldConfigBO = CsShopGroupTaskFieldConfigBO.builder()
                    .title(taskIdNameMap.getOrDefault(taskId, ""))
                    .strategyText(targetInfo.size() == 1 ? (targetInfo.get(0).getStrategyInfo() == null
                                                            ? "" : targetInfo.get(0).getStrategyInfo().getText()) : "")
                    .strategyJumpUrl(targetInfo.size() == 1 ? (targetInfo.get(0).getStrategyInfo() == null
                                                               ? "" : targetInfo.get(0).getStrategyInfo().getJumpUrl()) : "")
                    .strategyPcJumpUrl(targetInfo.size() == 1 ? (targetInfo.get(0).getStrategyInfo() == null
                                                                 ? "" : targetInfo.get(0).getStrategyInfo().getPcJumpUrl()) : "")
                    .buttonText(targetInfo.size() == 1 ? (targetInfo.get(0).getButtonInfo() == null
                                                          ? "" : targetInfo.get(0).getButtonInfo().getText())
                                                       : csShopGroupMessageConfigBO.getActionName())
                    .buttonJumpUrl(targetInfo.size() == 1 ? (targetInfo.get(0).getButtonInfo() == null
                                                            ? "" : targetInfo.get(0).getButtonInfo().getJumpUrl())
                                                          : csShopGroupMessageConfigBO.getGrowthCenterJumpUrl())
                    .buttonPcJumpUrl(targetInfo.size() == 1 ? (targetInfo.get(0).getButtonInfo() == null
                                                               ? "" : targetInfo.get(0).getButtonInfo().getPcJumpUrl())
                                                            : csShopGroupMessageConfigBO.getGrowthCenterPcJumpUrl())
                    .type(ExtraActionTypeEnum.BUTTON_VALUE)
                    .logParams(toJSON(logParamBO))
                    .status(ExtraActionStatusEnum.WHITE_VALUE).build();
        }
        return taskFieldConfigBO;
    }

    public String remainDays(long time) {
        long remainTime = time - System.currentTimeMillis();
        remainTime = remainTime < 0L ? 0 : remainTime;
        long days = remainTime / TimeConstants.DAY / 1000 + 1;
        return String.valueOf(days);
    }

    private boolean tagStageMatch(List<String> tagList, String stageTag) {
        for (String tag : tagList) {
            if (tag.contains(stageTag)) {
                return true;
            }
        }
        return false;
    }
}
