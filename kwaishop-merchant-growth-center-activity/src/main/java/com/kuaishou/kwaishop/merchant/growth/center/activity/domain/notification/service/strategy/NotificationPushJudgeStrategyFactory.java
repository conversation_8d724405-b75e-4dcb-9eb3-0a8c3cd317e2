package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityBizType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.notificationRuleConfig;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationPushConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationRuleConfigMapBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-26
 */
@Service
@Slf4j
@Lazy
public class NotificationPushJudgeStrategyFactory {

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    private static Map<String, NotificationPushJudgeStrategyService>
            NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP;

    @Autowired
    private List<NotificationPushJudgeStrategyService> notificationPushJudgeStrategyServiceList;

    @PostConstruct
    public void init() {
        NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(notificationPushJudgeStrategyServiceList)) {
            NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP = notificationPushJudgeStrategyServiceList.stream()
                    .collect(Collectors.toMap(NotificationPushJudgeStrategyService::judgeConditionCode, Function.identity(), (k1, k2) -> k1));
        }
    }

    /**
     * 判断是否需要推送
     */
    public boolean judgePush(long userId, NotificationPushConfigBO configBO) {
        if (userId <= 0 || null == configBO) {
            return false;
        }
        List<NotificationPushConditionBO> conditionList = resolveNotificationPushConditionList(configBO);
        if (CollectionUtils.isEmpty(conditionList)) {
            return true;
        }

        for (NotificationPushConditionBO condition : conditionList) {
            if (!NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP.containsKey(condition.getConditionCode())) {
                continue;
            }
            NotificationPushJudgeStrategyService notificationPushJudgeStrategyService =
                    NOTIFICATION_ENTITY_TYPE_STRATEGY_MAP.get(condition.getConditionCode());
            if (null == notificationPushJudgeStrategyService) {
                continue;
            }
            boolean push = notificationPushJudgeStrategyService.judgePush(userId, configBO);
            if (!EnvUtils.isProd()) {
                log.info("[触达推送决策] condition:{}, userId:{}, activityId:{}, entityId:{}, result:{}",
                        notificationPushJudgeStrategyService.judgeConditionCode(), userId, configBO.getActivityId(), configBO.getEntityId(), push);
            }
            if (!push) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析出触达是否通知条件
     */
    private List<NotificationPushConditionBO> resolveNotificationPushConditionList(NotificationPushConfigBO configBO) {
        NotificationRuleConfigMapBO notificationRuleConfigMapBO = notificationRuleConfig.getObject();
        if (null == notificationRuleConfigMapBO) {
            return Lists.newArrayList();
        }
        long activityId = configBO.getActivityId();
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        ActivityBizTypeEnum bizTypeEnum = getActivityBizType(activityDO);
        if (null == bizTypeEnum) {
            return Lists.newArrayList();
        }
        switch (bizTypeEnum) {
            case SELLER:
                return resolveNotificationRuleConfigList(configBO, notificationRuleConfigMapBO.getSellerConfigs());
            case DAREN:
                return resolveNotificationRuleConfigList(configBO, notificationRuleConfigMapBO.getDarenConfigs());
            default:
                return Lists.newArrayList();
        }
    }

    private List<NotificationPushConditionBO> resolveNotificationRuleConfigList(NotificationPushConfigBO configBO,
            List<NotificationRuleConfigBO> notificationRuleConfigs) {
        List<NotificationPushConditionBO> conditionList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(notificationRuleConfigs)) {
            return conditionList;
        }
        Map<Integer, NotificationRuleConfigBO> channelNotificationRuleConfigMap = notificationRuleConfigs.stream()
                .collect(Collectors.toMap(NotificationRuleConfigBO::getChannel, Function.identity(), (k1, k2) -> k1));

        int channel = configBO.getChannel();
        if (MapUtils.isEmpty(channelNotificationRuleConfigMap) || !channelNotificationRuleConfigMap.containsKey(channel)) {
            return conditionList;
        }
        NotificationRuleConfigBO channelConfig = channelNotificationRuleConfigMap.get(channel);
        if (null == channelConfig || CollectionUtils.isEmpty(channelConfig.getNotificationList())) {
            return conditionList;
        }
        Map<String, NotificationAdminDomainBO> notificationAdminMap = channelConfig.getNotificationList().stream()
                .collect(Collectors.toMap(NotificationAdminDomainBO::getCode, Function.identity(), (k1, k2) -> k1));
        if (MapUtils.isEmpty(notificationAdminMap) || !notificationAdminMap.containsKey(configBO.getScene())) {
            return conditionList;
        }
        NotificationAdminDomainBO notificationAdminDomainBO = notificationAdminMap.get(configBO.getScene());

        if (null == notificationAdminDomainBO) {
            return conditionList;
        }
        if (CollectionUtils.isNotEmpty(channelConfig.getNotificationPushConditionList())) {
            // 渠道维度过滤条件
            conditionList.addAll(channelConfig.getNotificationPushConditionList());
        }
        if (CollectionUtils.isNotEmpty(notificationAdminDomainBO.getNotificationPushConditionList())) {
            // 渠道 + 场景维度过滤条件
            conditionList.addAll(notificationAdminDomainBO.getNotificationPushConditionList());
        }
        return conditionList;
    }
}
