package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.client;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.offsetEventMockData;
import static com.kuaishou.kwaishop.merchant.growth.utils.mock.GrowthParamMockUtils.getUidMappedMockValue;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.OffsetEventHappenTimeFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.factory.OffsetEventDataSourceFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventMockData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.OffsetEventDataSourceTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
@Service
@Slf4j
public class OffsetEventHappenTimeFetchClient {
    @Resource
    private OffsetEventDataSourceFactory offsetEventDataSourceFactory;

    private static final String MOCK_SCENE = "offsetEvent";

    /**
     * 获取偏移事件发生时间
     */
    public Long fetchOffsetEventHappenTime(OffsetEventConfig offsetEventConfig, Long userId) {
        String dataSourceType = offsetEventConfig.getDataSourceType();
        OffsetEventDataSourceTypeEnum offsetEventDataSourceTypeEnum =
                OffsetEventDataSourceTypeEnum.getByType(dataSourceType);
        Preconditions.checkNotNull(offsetEventDataSourceTypeEnum, "offsetEventDataSourceTypeEnum is null");

        // 命中mock数据
        List<OffsetEventMockData> mockDataList = offsetEventMockData.getList();
        OffsetEventMockData offsetEventMockData =
                CollectionUtils.emptyIfNull(mockDataList).stream()
                        .filter(mockData -> mockData.getUserId().equals(userId)).findFirst()
                        .orElse(null);
        if (null != offsetEventMockData && MapUtils.isNotEmpty(offsetEventMockData.getMockHappenTime())
                && offsetEventMockData.getMockHappenTime().containsKey(offsetEventConfig.getEventType())) {
            return offsetEventMockData.getMockHappenTime().get(offsetEventConfig.getEventType());
        }
        Long finalUserId = userId;

        long mockUserId = getUidMappedMockValue(MOCK_SCENE, userId);
        if (mockUserId > 0L) {
            finalUserId = mockUserId;
        }

        Object dataSourceConfig = ObjectMapperUtils.fromJSON(offsetEventConfig.getDataSourceConfig(),
                offsetEventDataSourceTypeEnum.getClazz());

        OffsetEventConfigBO objectOffsetEventConfigBO = OffsetEventConfigBO.builder()
                .dataSourceConfig(dataSourceConfig)
                .eventType(offsetEventConfig.getEventType())
                .dataSourceType(offsetEventDataSourceTypeEnum)
                .build();
        OffsetEventHappenTimeFetchService<?> offsetEventHappenTimeFetchService =
                offsetEventDataSourceFactory.route(offsetEventDataSourceTypeEnum);
        Long happenTime =
                offsetEventHappenTimeFetchService.fetchOffsetEventHappenTime(objectOffsetEventConfigBO, finalUserId);
        log.info("fetchOffsetEventHappenTime, userId:{}, finalUserId:{}, happenTime:{}", userId, finalUserId,
                happenTime);
        return happenTime;
    }
}
