package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild;

import java.util.Map;

import org.elasticsearch.index.query.QueryBuilder;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-26
 */
public interface QueryBuildHandler {
    /**
     * 构建一个condition的QueryBuild
     */
    QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam);

    /**
     * 对应条件类型
     */
    ConditionTypeEnum conditionType();
}
