package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.roi.RoiActivityLevelConfigBO;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-16
 */
@Data
public class AuditRoiExecuteConfigBO {
    /**
     * 活动ID
     */
    private long activityId;
    /**
     * 用户对应的父任务
     */
    private Map<Long, List<Long>> userCanDrawParentTaskMap;
    /**
     * 各分层配置信息
     */
    private List<RoiActivityLevelConfigBO> activityLayerRoiInfo;
}
