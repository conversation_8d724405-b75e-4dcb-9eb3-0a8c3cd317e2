package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchDataSourceContextBO {

    private Long userId;

    private Long activityId;

    private Integer entityType;

    private List<Long> entityIds;

    private Map<String, Object> customizeParamMap;

    private Boolean withDegree;

    private LaunchDataSourceTypeEnum dataSourceType;
}
