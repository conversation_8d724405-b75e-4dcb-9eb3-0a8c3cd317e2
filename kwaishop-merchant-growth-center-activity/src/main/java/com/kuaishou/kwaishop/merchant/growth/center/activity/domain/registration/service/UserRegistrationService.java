package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import org.roaringbitmap.longlong.Roaring64NavigableMap;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationCacheBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.CommonDataChangeEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;

/**
 * 报名服务(for user)
 *
 * <AUTHOR>
 */
public interface UserRegistrationService {

    /**
     * 插入或更新一条用户报名记录，在同一实体和类型下仅允许报一次名
     */
    long saveUserRegistrationRecord(UserRegistrationRecordBO userRegistrationRecord);

    /**
     * 批量保存用户报名记录
     */
    boolean batchInsertOnDupUpdateStatus(Collection<UserRegistrationRecordBO> userRegistrationRecords);

    /**
     * 批量插入用户报名记录，已经存在的不再插入
     * <p>
     * 同一批数据中需要保障活动ID和用户ID和实体ID都相同
     */
    boolean batchInsertUserRegistrationRecord(List<UserRegistrationRecordBO> userRegistrationRecords);

    /**
     * 批量插入多条用户报名记录，无限制
     */
    boolean batchInsertUserRegistrationRecordNoLimit(List<UserRegistrationRecordBO> userRegistrationRecords);

    /**
     * 批量取消用户在某个活动下的资格
     */
    boolean batchCancelUserRegistrationRecords(long activityId, EntityTypeEnum entityType, Collection<Long> userIds);

    /**
     * 批量更新用户某个活动下所有资格记录的状态
     */
    boolean batchUpdateUserRegistrationRecordsStatus(List<UserRegistrationRecordDO> userRegistrationRecordList,
            UserRegistrationStatusEnum statusEnum);

    /**
     * 取消报名
     */
    int cancelUserRegistrationRecord(long activityId, Long userId);

    /**
     * 查询用户在某个实体下的报名记录，如果存在多条则抛出异常
     */
    UserRegistrationRecordBO queryUserRegistrationRecord(long userId, Long activityId,
            EntityTypeEnum entityType, Long entityId);

    /**
     * 批量查询
     */
    Map<Long, UserRegistrationRecordBO> batchQueryUserRegistrationRecords(Collection<Long> userIds,
            Long activityId, EntityTypeEnum entityType, Long entityId);

    /**
     * 批量查询用户资格记录
     */
    Map<Long, UserRegistrationRecordBO> batchQueryUserRegistrationRecords(Long userId, Long activityId,
            EntityTypeEnum entityTypeEnum, Collection<Long> entityIds);

    /**
     * 批量查询用户资格记录选项配置
     */
    Map<Long, AddRegistrationOptionBO> batchQueryUserRegistrationRecordOption(Long userId, Long activityId,
            EntityTypeEnum entityTypeEnum, Collection<Long> entityIds, boolean readMaster);

    /**
     * 查询用户在某个活动下指定状态的报名记录
     */
    List<UserRegistrationRecordBO> queryUserRegistrationRecords(long userId, Long activityId,
            EntityTypeEnum entityType, UserRegistrationStatusEnum registrationStatus);

    /**
     * 查询用户所有活动报名记录
     */
    List<UserRegistrationRecordBO> queryUserAllActivityRegistrationRecords(long userId);

    /**
     * 查询用户所有的资格记录
     */
    List<UserRegistrationRecordBO> queryUserAllRegistrationRecords(long userId, UserRegistrationStatusEnum registrationStatus);

    List<UserRegistrationRecordBO> queryUserRecordsOfMultiActivity(List<Long> activityIds, long userId);

    /**
     * 查询用户所有的资格记录
     */
    List<UserRegistrationRecordBO> queryUserAllRegistrationRecords(long userId, EntityTypeEnum entityType,
            UserRegistrationStatusEnum registrationStatus);

    /**
     * 查询用户指定活动所有的资格记录
     */
    List<UserRegistrationRecordBO> queryUserActivityRegistrationRecords(long userId, long activityId, UserRegistrationStatusEnum registrationStatus);

    /**
     * 查询用户所有活动资格记录（包括风控和失效的）
     */
    List<UserRegistrationRecordBO> queryUserAllStatusRegistrationRecords(long userId);

    /**
     * 逻辑删除用户报名记录
     */
    long logicDeleteUserRegistrationRecord(Long activityId, EntityTypeEnum entityType, Long entityId,
            Long userId, String operator);

    /**
     * 物理删除用户报名记录
     */
    long deleteUserRegistrationRecord(Long activityId, EntityTypeEnum entityType, Long entityId,
            Long userId, String operator);

    /**
     * 处理用户报名记录变更事件
     */
    void handleUserRegistrationRecordChangeEvent(CommonDataChangeEvent<UserRegistrationRecordDO> changeEvent);

    /**
     * 保存用户报名的缓存
     */
    void saveUserRegistrationCache(UserRegistrationCacheBO userRegistrationCache);

    /**
     * 获取用户报名缓存
     */
    List<UserRegistrationCacheBO> getUserRegistrationCache(long userId, long activityId);

    /**
     * 获取用户报名缓存
     */
    UserRegistrationCacheBO getUserRegistrationCache(long userId, long activityId, Long entityId);

    /**
     * 删除用户报名的缓存
     */
    void deleteUserRegistrationCache(long activityId, long userId, long entityId);

    /**
     * 流式拉取报名数据
     */
    Stream<UserRegistrationRecordDO> cursorGetRegistrationRecord(int shardIndex, long activityId);

    /**
     * 流式拉取报名数据
     */
    Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByRegistrationTime(int shardIndex,
            long registrationTime, int bufferSize);

    /**
     * 流式拉取报名数据
     */
    Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByRegistrationTime(int shardIndex,
            long registrationTime, int bufferSize, long cursorStartId);

    /**
     * 流式拉取报名数据
     */
    Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByParentTaskId(int shardIndex,
            long activityId, long entityId, int bufferSize, long cursorStartId);

    /**
     * 流式拉取资格数据
     */
    Stream<UserRegistrationRecordDO> cursorGetRegistrationRecordByEntityTypeAndStatus(int shardIndex, long activityId,
            EntityTypeEnum entityType, UserRegistrationStatusEnum registrationStatus, int bufferSize,
            long cursorStartId);

    /**
     * 覆盖用户基值数据
     */
    void coverUserBasicData(long userId, long activityId, Map<String, Object> inputBasicData);

    /**
     * 批量新增资格记录（带事务）
     */
    void batchInsertRegistrationRecords(List<UserRegistrationRecordDO> userRegistrationRecords);

    /**
     * 获取活动下所有有资格的用户id的bitmap
     */
    Roaring64NavigableMap queryActivityTotalUnRiskUserIds(long activityId);
}
