package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service.processor;

import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.app.consumer.hivemsg.HiveSyncDoneMsgInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityCrowdService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.domain.HiveSyncDone;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service.AbstractHiveSyncDoneMsgProcessor;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-27
 */
@HiveSyncDone(bizCode = "task_crowd_diff")
@Slf4j
public class TaskCrowdDailyDiffProcessor extends AbstractHiveSyncDoneMsgProcessor {

    @Autowired
    private AdminActivityCrowdService adminActivityCrowdService;

    @Override
    protected void doProcess(HiveSyncDoneMsgInfo msgInfo) {
        log.info("接收diff数据处理消息 TaskCrowdDailyDiffProcessor msgInfo:{}", ObjectMapperUtils.toJSON(msgInfo));
        String pDate = msgInfo.getPDate();
        adminActivityCrowdService.processUserAddByDynamicCrowdDiff(pDate);
    }
}
