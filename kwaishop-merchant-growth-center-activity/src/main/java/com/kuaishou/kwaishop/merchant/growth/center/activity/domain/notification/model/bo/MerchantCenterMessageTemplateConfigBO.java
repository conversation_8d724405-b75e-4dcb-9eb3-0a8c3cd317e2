package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MerchantCenterMessageTemplateConfigBO extends BaseMessageTemplateConfigBO {
    /**
     * 模板code
     */
    private String code;
    /**
     * 通知code
     */
    private String notificationCode;
}
