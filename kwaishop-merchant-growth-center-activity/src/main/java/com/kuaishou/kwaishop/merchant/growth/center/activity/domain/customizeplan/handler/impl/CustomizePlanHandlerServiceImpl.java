package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler.impl;

import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizeActivity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanBasicInfoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlanConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.TextStyleBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.CustomizePlanSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.PlanStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.supplier.CustomizePlanConstructor;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Service
@Slf4j
public class CustomizePlanHandlerServiceImpl extends AbstractCustomizePlanHandlerService {
    @Override
    public CustomizePlanSceneEnum getScene() {
        return CustomizePlanSceneEnum.CUSTOMIZE_PLAN;
    }

    @Override
    protected CustomizePlan constructCustomizePlan(long userId, CustomizePlanConfig customizePlanConfig) {
        String constructorCode = customizePlanConfig.getCustomizePlanConstructor().getConstructorCode();
        CustomizePlanConstructor customizePlanConstructor = getCustomizePlanConstructor(constructorCode);
        if (Objects.isNull(customizePlanConstructor)) {
            log.error("[查询定制计划] 计划对应构造器code未找到对应定制计划构造器, constructorCode:{}", constructorCode);
            throw new BizException(SERVER_ERROR, "未找到对应构造器");
        }
        // 构造器填充定制计划
        CustomizePlan customizePlan =
                customizePlanConstructor.initCustomizePlan(userId, customizePlanConfig.getCustomizePlanConstructor().getConfigMap());
        if (customizePlan == null) {
            return null;
        }

        return buildCompletedCustomizePlan(customizePlan, customizePlanConfig);
    }

    /**
     * 构建完整的定制计划
     */
    private CustomizePlan buildCompletedCustomizePlan(CustomizePlan customizePlan, CustomizePlanConfig customizePlanConfig) {
        customizePlan.setPlanCode(customizePlanConfig.getPlanCode());
        customizePlan.setPriority(customizePlanConfig.getPriority());
        CustomizePlanBasicInfoConfig basicInfo = customizePlanConfig.getBasicInfo();
        customizePlan.setBannerPC(basicInfo.getTaskCenterCardBannerPC());
        customizePlan.setBannerAPP(basicInfo.getTaskCenterCardBannerAPP());
        customizePlan.setPicUrl(basicInfo.getCustomizePlanPicture());
        customizePlan.setMainTitle(TextStyleBO.builder().text(basicInfo.getMainTitle()).build());
        customizePlan.setStartTime(basicInfo.getStartTime());
        customizePlan.setEndTime(basicInfo.getEndTime());
        customizePlan.setDesc(basicInfo.getDesc());
        customizePlan.setDrawEndTime(basicInfo.getDrawEndTime());
        long currentTime = System.currentTimeMillis();
        if (currentTime < basicInfo.getStartTime()) {
            customizePlan.setPlanStatus(PlanStatusEnum.WAIT_START.getStatus());
        } else if (currentTime < basicInfo.getEndTime()) {
            customizePlan.setPlanStatus(PlanStatusEnum.PROCESSING.getStatus());
        } else if (currentTime > basicInfo.getEndTime()) {
            customizePlan.setPlanStatus(PlanStatusEnum.END.getStatus());
        }
        List<CustomizeActivity> activityList = customizePlan.getActivityList();
        Map<String, String> activityNameMap = basicInfo.getActivityNameMap();
        activityList.forEach(activity -> {
            Long activityId = activity.getActivityId();
            String name = activityNameMap.get(String.valueOf(activityId));
            activity.setTitle(name);
        });

        return customizePlan;
    }
}
