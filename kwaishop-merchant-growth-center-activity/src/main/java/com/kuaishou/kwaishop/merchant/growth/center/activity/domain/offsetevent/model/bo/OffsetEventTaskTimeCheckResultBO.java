package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-16
 */
@Data
public class OffsetEventTaskTimeCheckResultBO {
    /**
     * 是否有效任务
     * 1.任务时间开始时间 > 活动时间 无效任务
     * 2.基期时间与任务时间重合 无效任务
     */
    private boolean valid;

    /**
     * 任务偏移事件发生时间
     */
    private Long taskOffsetEventHappenTime;

    /**
     * 基期偏移事件发生时间
     */
    private Long basicOffsetEventHappenTime;

    /**
     * 任务开始时间
     */
    private Long taskStartTime;

    /**
     * 任务结束时间
     */
    private Long taskEndTime;

    /**
     * 基期开始时间
     */
    private Long basicStartTime;

    /**
     * 基期结束时间
     */
    private Long basicEndTime;

    /**
     * 任务标签
     */
    private List<String> tags;
}
