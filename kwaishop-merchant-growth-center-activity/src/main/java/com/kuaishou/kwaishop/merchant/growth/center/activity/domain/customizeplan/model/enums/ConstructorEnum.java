package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Getter
@AllArgsConstructor
public enum ConstructorEnum {

    ACTIVITY("activity", "运营策略活动"),
    UNKNOWN("unknown", "未知"),;

    public static ConstructorEnum of(String constructorCode) {
        for (ConstructorEnum val : ConstructorEnum.values()) {
            if (val.getConstructorCode().equals(constructorCode)) {
                return val;
            }
        }
        return UNKNOWN;
    }

    /**
     * 定制计划构造器
     */
    private final String constructorCode;

    /**
     * 描述
     */
    private final String desc;
}
