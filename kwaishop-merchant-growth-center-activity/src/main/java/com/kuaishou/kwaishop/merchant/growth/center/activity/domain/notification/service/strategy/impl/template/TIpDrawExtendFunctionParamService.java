package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.PageResourceTypeEnum.DAREN_WORKBENCH;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.judgeCommonShowChannel;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.resolveRulePageUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.notificationParamControlBO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.darenWorkbenchActivityListUrl;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationParamControlBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-25
 */
@Service
@Slf4j
public class TIpDrawExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.TIP_PROMOTE_DRAW, TemplateParamTypeEnum.TIP_PROMOTE_URL);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        NotificationParamControlBO control = getParamControl();
        if (control != null && control.isSwitchFlag()) {
            log.info("[通知获取提示栏文案]触达控制终止，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            result.setExecuteStatus(STOP);
            result.setTemplateParamMap(params);
            return result;
        }
        List<UserRegistrationRecordBO> userRegistrations =
                userRegistrationService.queryUserAllActivityRegistrationRecords(userId);
        if (CollectionUtils.isEmpty(userRegistrations)) {
            log.warn("[通知获取提示栏文案]用户资格不存在，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "用户资格不存在");
            result.setExecuteStatus(STOP);
            return result;
        }
        List<Long> activityIds = userRegistrations.stream()
                .map(UserRegistrationRecordBO::getActivityId)
                .collect(Collectors.toList());
        if (!activityIds.contains(configBO.getActivityId())) {
            log.warn("[通知获取提示栏文案]用户资格不存在本活动，触达停止，userID:{},通知配置ID:{}", userId,
                    configBO.getId());
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "用户当前活动资格不存在");
            result.setExecuteStatus(STOP);
            return result;
        }
        List<UserActivityRecordDO> userActivityList =
                userActivityRecordDAO.queryUserActivityRecordByActivityList(userId, activityIds);
        List<Long> userDrawActivityList = userActivityList.stream()
                .map(UserActivityRecordDO::getActivityId)
                .collect(Collectors.toList());
        // 有资格的活动列表map
        Map<Long, ActivityDO> activityMap = activityLocalCacheService.batchQueryActivityInfo(activityIds);
        // 最终需要触达的活动map
        Map<Long, ActivityDO> finalActivityMap = Maps.newHashMap();
        ActivityDO activityDO = activityMap.get(configBO.getActivityId());
        if (activityDO == null) {
            log.warn("[通知获取提示栏文案]活动不存在，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "活动不存在");
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动不存在");
        }
        // 当前活动达人工作台的进入达人列表，分销广场的按单个判断，达人工作台的聚类后观察个数
        if (judgeCommonShowChannel(activityDO, DAREN_WORKBENCH.getType())) {
            for (Entry<Long, ActivityDO> activityEntry : activityMap.entrySet()) {
                if (judgeCommonShowChannel(activityEntry.getValue(), DAREN_WORKBENCH.getType())) {
                    finalActivityMap.put(activityEntry.getKey(), activityEntry.getValue());
                }
            }
        } else {
            finalActivityMap.put(activityDO.getId(), activityDO);
        }
        Long currentTimeStamp = System.currentTimeMillis();
        List<Long> unDrawActivityIds = finalActivityMap.keySet().stream()
                .filter(e -> !userDrawActivityList.contains(e)
                        && activityMap.containsKey(e) && activityMap.get(e).getDrawEndTime() > currentTimeStamp)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unDrawActivityIds)) {
            log.warn("[通知获取提示栏文案]活动不存在，触达停止，userID:{},通知配置ID:{}", userId, configBO.getId());
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "未报名活动不存在");
            result.setExecuteStatus(STOP);
            return result;
        }
        Map<String, String> textMap = Maps.newHashMap();
        if (control != null && MapUtils.isNotEmpty(textMap)) {
            textMap = control.getText();
        }
        String tip = MapUtils.getString(textMap, "multi", "你有多个新任务待报名");
        String pageRuleUrl =
                darenWorkbenchActivityListUrl.get() + "&fromSource=" + NotificationChannelEnum.of(configBO.getChannel())
                        .getName();
        if (unDrawActivityIds.size() == 1) {
            tip = MapUtils.getString(textMap, "single", "你有新的任务待报名");
            pageRuleUrl = resolveRulePageUrl(activityMap.get(unDrawActivityIds.get(0)), configBO.getChannel());
        }
        log.info("[通知获取提示栏文案]未报名活动:{},userId:{},活动id:{}", toJSON(unDrawActivityIds), userId,
                configBO.getActivityId());
        String tipPromoteDraw = TemplateParamTypeEnum.TIP_PROMOTE_DRAW.getName();
        String tipDrawUrl = TemplateParamTypeEnum.TIP_PROMOTE_URL.getName();
        params.put(tipPromoteDraw, tip);
        params.put(tipDrawUrl, pageRuleUrl);
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    private NotificationParamControlBO getParamControl() {
        List<NotificationParamControlBO> paramControls = notificationParamControlBO.getList();
        if (CollectionUtils.isEmpty(paramControls)) {
            return null;
        }
        return paramControls.stream().filter(e -> e.getScene().equals("tipDrawDesc")).findFirst().orElse(null);
    }

}