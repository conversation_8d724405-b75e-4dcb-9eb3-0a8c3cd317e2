package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.handler;

import static com.kuaishou.kconf.common.json.JsonMapperUtils.toJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.resolver.CommonResolver.resolveSdmMultiRowsData;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.customBaseCalcConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.BaseAlgorithmUtils.algorithmProcessIndicatorDataAvg;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.BaseAlgorithmUtils.algorithmProcessIndicatorDataAvgByScale;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.CYCLE_DURATION_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.INDICATOR_CONFIG_DO;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants.USER_ID;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.ActivityBaseAlgorithmEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.IndicatorTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.BaseDynamicValue;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.BaseDynamicValueParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.service.DynamicValueServiceI;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.BaseDataSourceFetcher;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.BaseCalcExtDataSupplyParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.BasicFactorConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config.CustomBaseCalcConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config.CustomBaseCalcItemConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums.BaseCalcExtDataTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.supplier.BaseCalcExtDataSupplier;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.model.IndicatorAlgorithmCalcParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.core.ICalcFactorFetchHandler;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.annotation.BizFactor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.bo.FactorValueFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.BizSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.FactorSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-21
 */
@BizFactor(bizScene = BizSceneEnum.BASIC_CALC, factorSourceType = FactorSourceTypeEnum.custom)
@Slf4j
public class BasicCalcCustomFactorFetchHandler implements ICalcFactorFetchHandler {
    @Resource
    private IndicatorBasicNewService indicatorBasicNewService;
    @Resource
    private AdminActivityOnlineService adminActivityOnlineService;
    @Resource
    private IndicatorLocalCacheService indicatorLocalCacheService;
    @Resource
    private DynamicValueServiceI dynamicValueServiceI;
    @Resource
    private BaseCalcExtDataSupplier baseCalcExtDataSupplier;
    @Resource
    private BaseDataSourceFetcher baseDataSourceFetcher;
    @Resource
    private IndicatorTaskService indicatorTaskService;

    @Override
    public void fillFactorFetchConfig(FactorValueFetchContext context) {
        Map<String, Object> bizParam = context.getBizParam();
        Long userId = MapUtils.getLong(bizParam, USER_ID);
        BasicIndicatorConfigBO basicIndicatorConfig =
                (BasicIndicatorConfigBO) MapUtils.getObject(bizParam, FormulaCalcConstants.BASIC_INDICATOR_CONFIG);
        Long indicatorId = basicIndicatorConfig.getIndicatorId();
        IndicatorDO indicatorDO = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
        IndicatorConfigDO indicatorConfigDO = null;
        if (null != MapUtils.getObject(bizParam, INDICATOR_CONFIG_DO)) {
            indicatorConfigDO =
                    (IndicatorConfigDO) MapUtils.getObject(bizParam, INDICATOR_CONFIG_DO);
        }
        // [cb] 因子精度值获取
        Integer scale = null;
        if (null != MapUtils.getObject(bizParam, FormulaCalcConstants.INDICATOR_SCALE_SCENE)) {
            scale = MapUtils.getInteger(bizParam, FormulaCalcConstants.INDICATOR_SCALE_SCENE);
        }
        // 周期时间
        Long cycleDuration = null;
        if (null != MapUtils.getObject(bizParam, CYCLE_DURATION_KEY)) {
            cycleDuration = MapUtils.getLong(bizParam, CYCLE_DURATION_KEY);
        }
        // [ce]
        List<BasicFactorConfigBO> basicFactorConfigList =
                basicIndicatorConfig.getBasicSystemCalcConfig().getBasicFactorConfigList();
        String factorCode = context.getFactorCode();
        BasicFactorConfigBO basicFactorConfigBO = basicFactorConfigList.stream()
                .filter(basicFactorConfig -> basicFactorConfig.getFactorCode().equals(factorCode)).findFirst()
                .orElseThrow(() -> new BizException(BasicErrorCode.SERVER_ERROR, "基期因子配置不存在"));
        BasicFactorConfig basicFactorConfig =
                BasicFactorConfig.builder().userId(userId).indicatorConfigDO(indicatorConfigDO).indicatorDO(indicatorDO)
                        .scale(scale)
                        .basicFactorConfigBO(basicFactorConfigBO).cycleDuration(cycleDuration).build();
        context.setFetchConfig(basicFactorConfig);
    }

    @Override
    public Double fetchFactorValue(FactorValueFetchContext context) {
        BasicFactorConfig fetchConfig = (BasicFactorConfig) context.getFetchConfig();
        BasicFactorConfigBO basicFactorConfigBO = fetchConfig.getBasicFactorConfigBO();
        ActivityBaseAlgorithmEnum baseAlgorithm = basicFactorConfigBO.getBaseAlgorithm();
        String customBaseCalcRuleCode = basicFactorConfigBO.getCustomBaseCalcRuleCode();
        Long userId = fetchConfig.getUserId();

        List<String> effectiveBasicDate = indicatorBasicNewService.resolveSingleFactorUserEffectiveBasicDate(
                userId, basicFactorConfigBO);
        if (CollectionUtils.isEmpty(effectiveBasicDate)) {
            log.warn("[基期因子]有效时间为空, userId:{}, indicatorId:{}", userId,
                    fetchConfig.getIndicatorDO().getId());
            // 相对时间模式下，可能存在偏移事件未发生求基期的情况 || 有效开播日，可能被全部剔除，则基期日均为0
            if (basicFactorConfigBO.getIndicatorTimeType() == IndicatorTimeTypeEnum.CUSTOM_TIME
                    && !basicFactorConfigBO.isExclusiveInvalidLiveDays()) {
                throw new BizException(BasicErrorCode.SERVER_ERROR, "基期因子的有效时间为空");
            }
        }
        log.info("[基期因子] 有效时间:{}， userId:{}, indicatorId:{}", toJson(effectiveBasicDate),
                userId, fetchConfig.getIndicatorDO().getId());

        switch (baseAlgorithm) {
            case CUSTOM_BASE_CALC_RULE:
                return processCustomBaseCalc(fetchConfig, effectiveBasicDate);
            case TOTAL:
                return processTotalBaseCalc(userId, fetchConfig, effectiveBasicDate);
            default:
                return processDefaultBaseCalc(fetchConfig, basicFactorConfigBO, baseAlgorithm, customBaseCalcRuleCode,
                        userId, effectiveBasicDate);
        }
    }


    private double processTotalBaseCalc(long userId, BasicFactorConfig basicFactorConfig,
              List<String> effectiveBasicDate) {
        return indicatorTaskService.queryUserIndicatorBasicTotalValue(userId,
                basicFactorConfig.getIndicatorDO(), basicFactorConfig.getIndicatorConfigDO(), effectiveBasicDate);
    }

    private double processDefaultBaseCalc(BasicFactorConfig fetchConfig, BasicFactorConfigBO basicFactorConfigBO,
            ActivityBaseAlgorithmEnum baseAlgorithm, String customBaseCalcRuleCode, Long userId,
            List<String> effectiveBasicDate) {

        List<Map<String, Object>> effectiveIndicatorRawData =
                adminActivityOnlineService.querySingleBaseIndicatorRawValue(userId,
                        fetchConfig.getIndicatorConfigDO(), fetchConfig.getIndicatorDO(), effectiveBasicDate);
        log.info("[基期因子] 基期指标原始值:{}， userId:{}, indicatorId:{}", toJson(effectiveIndicatorRawData),
                userId, fetchConfig.getIndicatorDO().getId());

        List<Long> effectiveIndicatorData =
                resolveSdmMultiRowsData(userId, effectiveIndicatorRawData, effectiveBasicDate);
        IndicatorAlgorithmCalcParam param = IndicatorAlgorithmCalcParam.builder().baseAlgorithm(baseAlgorithm)
                .exclusiveMaxAndMin(basicFactorConfigBO.isExclusiveMaxMinDays())
                .effectiveIndicatorValue(effectiveIndicatorData)
                .customBaseCalcRuleCode(customBaseCalcRuleCode)
                .build();

        Integer scale = fetchConfig.getScale();
        // [cb] 查询基期数据
        if (scale != null) {
            param.setScale(scale);
            return algorithmProcessIndicatorDataAvgByScale(param);
        }
        long avgBasicIndicatorValue = algorithmProcessIndicatorDataAvg(param);
        return (double) avgBasicIndicatorValue;
        // [ce]
    }

    private Double processCustomBaseCalc(BasicFactorConfig fetchConfig, List<String> effectiveBasicDate) {
        // 获取业务参数
        Long userId = fetchConfig.getUserId();
        Long indicatorId = fetchConfig.getIndicatorDO().getId();
        BasicFactorConfigBO basicFactorConfig = fetchConfig.getBasicFactorConfigBO();
        IndicatorConfigDO indicatorConfig = fetchConfig.getIndicatorConfigDO();
        Long cycleDuration = fetchConfig.getCycleDuration();
        Integer scale = fetchConfig.getScale();

        if (userId == null || userId <= 0L || (indicatorConfig == null && cycleDuration == 0L)) {
            log.error("[基期自定义规则计算] 参数异常 fetchConfig:{}", ObjectMapperUtils.toJSON(fetchConfig));
            throw new BizException(BasicErrorCode.PARAM_INVALID, "基期自定义规则计算参数异常");
        }

        IndicatorDO indicator = indicatorLocalCacheService.queryTaskIndicator(indicatorId);
        if (indicator == null) {
            log.error("[基期自定义规则计算] 参数异常 fetchConfig:{}", ObjectMapperUtils.toJSON(fetchConfig));
            throw new BizException(BasicErrorCode.PARAM_INVALID, "基期自定义规则计算参数异常");
        }

        // 获取自定义基期计算规则Code配置
        String customBaseCalcRuleCode = basicFactorConfig.getCustomBaseCalcRuleCode();

        // 获取自定义基期计算规则配置
        CustomBaseCalcConfigBO config = customBaseCalcConfig.getObject();
        if (config == null || MapUtils.isEmpty(config.getCustomBaseCalcItemConfigMap())) {
            log.error("[基期自定义规则计算] 配置异常 basicFactorConfig:{}",
                    ObjectMapperUtils.toJSON(basicFactorConfig));
            throw new BizException(BasicErrorCode.PARAM_INVALID, "基期自定义规则计算配置异常");
        }

        Map<String, CustomBaseCalcItemConfigBO> customBaseCalcItemConfigMap = config.getCustomBaseCalcItemConfigMap();
        CustomBaseCalcItemConfigBO itemConfig = customBaseCalcItemConfigMap.get(customBaseCalcRuleCode);
        if (itemConfig == null) {
            log.error("[基期自定义规则计算] 配置异常 basicFactorConfig:{}",
                    ObjectMapperUtils.toJSON(basicFactorConfig));
            throw new BizException(BasicErrorCode.PARAM_INVALID, "基期自定义规则计算配置异常");
        }

        // 查询DM原始数据
        Integer dataSourceFetchType = itemConfig.getDataSourceFetchType();
        BaseDataSourceFetchTypeEnum fetchType = BaseDataSourceFetchTypeEnum.getByType(dataSourceFetchType);

        BaseDataSourceFetchParam param = BaseDataSourceFetchParam.builder()
                .userId(userId)
                .indicatorConfig(indicatorConfig)
                .indicator(indicator)
                .baseDataSourceFetchType(fetchType)
                .dateList(effectiveBasicDate)
                .cycleDuration(cycleDuration)
                .build();
        BaseDataSourceFetchResult fetchResult = baseDataSourceFetcher.fetch(param);

        // 填充额外数据
        Map<String, Object> allExtData = new HashMap<>();
        allExtData.put("indicatorId", indicatorId);

        // 遍历获取外部数据类型
        List<String> requiredExtDataTypeList = itemConfig.getRequiredExtDataTypeList();
        if (CollectionUtils.isNotEmpty(requiredExtDataTypeList)) {
            requiredExtDataTypeList.forEach(type -> {

                BaseCalcExtDataTypeEnum typeEnum = BaseCalcExtDataTypeEnum.getByType(type);
                Map<String, Object> inputMap = new HashMap<>();
                inputMap.put(CYCLE_DURATION_KEY, cycleDuration);

                BaseCalcExtDataSupplyParam supplyParam = BaseCalcExtDataSupplyParam.builder()
                        .userId(userId)
                        .indicatorConfig(indicatorConfig)
                        .type(typeEnum)
                        .inputDataMap(inputMap)
                        .build();

                Map<String, Object> extData = baseCalcExtDataSupplier.supply(supplyParam);
                allExtData.putAll(extData);
            });
        }

        return algorithmProcessIndicatorDataCustomRule(userId, customBaseCalcRuleCode, effectiveBasicDate,
                fetchResult, allExtData, scale);
    }

    private Double algorithmProcessIndicatorDataCustomRule(long userId, String customBaseCalcRuleCode,
            List<String> effectiveBasicDate, BaseDataSourceFetchResult fetchResult,
            Map<String, Object> extData, Integer scale) {

        BaseDynamicValueParam baseDynamicValueParam = BaseDynamicValueParam.builder()
                .userId(userId)
                .customBaseCalcRuleCode(customBaseCalcRuleCode)
                .effectiveBasicDate(effectiveBasicDate)
                .rawIndicatorBaseValues(ListUtils.emptyIfNull(fetchResult.getRawIndicatorBaseValues()))
                .multiSegmentMap(MapUtils.emptyIfNull(fetchResult.getMultiSegmentMap()))
                .scale(scale)
                .extData(extData)
                .build();

        BaseDynamicValue baseDynamicValue;
        try {
            baseDynamicValue = dynamicValueServiceI.getBaseDynamicValue(baseDynamicValueParam);
        } catch (Exception e) {
            log.error("[自定义规则基期计算] 基期计算失败 param:{}", ObjectMapperUtils.toJSON(baseDynamicValueParam), e);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "自定义规则基期计算失败");
        }

        if (baseDynamicValue.getResult() != 1) {
            log.error("[自定义规则基期计算] 基期计算失败 param:{}, result:{}",
                    ObjectMapperUtils.toJSON(baseDynamicValueParam), ObjectMapperUtils.toJSON(baseDynamicValue));
            throw new BizException(BasicErrorCode.SERVER_ERROR, "自定义规则基期计算失败");
        }

        return Double.valueOf(baseDynamicValue.getBaseValue());
    }
}
