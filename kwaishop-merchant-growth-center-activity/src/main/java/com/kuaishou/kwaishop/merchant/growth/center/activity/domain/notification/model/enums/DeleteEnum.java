package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-12
 */
public enum DeleteEnum {
    NORMAL(0, "正常"),
    DELETED(1, "已删除")
    ;

    private int val;
    private String desc;

    DeleteEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static DeleteEnum of(int status) {
        for (DeleteEnum deleteEnum : DeleteEnum.values()) {
            if (deleteEnum.getVal() == status) {
                return deleteEnum;
            }
        }
        return NORMAL;
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
