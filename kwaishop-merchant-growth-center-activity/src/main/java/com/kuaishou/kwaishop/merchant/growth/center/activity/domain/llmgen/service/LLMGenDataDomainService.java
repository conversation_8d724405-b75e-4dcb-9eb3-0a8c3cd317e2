package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-22
 */
public interface LLMGenDataDomainService {

    /**
     * 保存生成数据
     */
    void createGenDataRecord(GenDataRecordBO genDataRecord);

    /**
     * 将生成数据设置为成功
     */
    void successGenDataRecord(String uniqueKey, Map<String, Object> contentMap);

    /**
     * 将生成数据设置为失败
     */
    void failGenDataRecord(String uniqueKey, String errorMsg);

    /**
     * 根据唯一键更新生成数据
     */
    void updateGenDataRecordByUniqueKey(GenDataRecordBO genDataRecord);

    /**
     * 根据唯一键查询数据
     */
    GenDataRecordBO queryByUniqueKey(String uniqueKey);
}
