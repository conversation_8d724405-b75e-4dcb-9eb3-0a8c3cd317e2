package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
public interface BaseDataSourceFetchStrategy {

    /**
     * 取数
     */
    BaseDataSourceFetchResult fetch(BaseDataSourceFetchParam param);

    /**
     * 获取取数方式
     */
    BaseDataSourceFetchTypeEnum getType();
}
