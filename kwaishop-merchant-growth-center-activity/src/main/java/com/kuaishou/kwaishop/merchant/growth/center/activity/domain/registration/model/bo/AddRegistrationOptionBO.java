package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddRegistrationOptionBO {
    /**
     * 报名开始事件
     */
    private Long overrideStartTime;
    /**
     * 前置子活动id/父taskId
     */
    private List<Long> preSubActivityId;

    /**
     * 1.偏移事件发生
     * 2.基期填充结束
     */
    private Boolean offsetEventFinishFlag;
}
