package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-08
 */
@AllArgsConstructor
@Getter
public enum UserPlanStatusEnum {
    UNKNOWN(0, "未知"),

    DRAWING(10, "待领取"),

    PROCESSING(20, "进行中"),

    SUCCESS(40, "成功"),

    FAIL(50, "失败"),
    ;

    public static UserPlanStatusEnum of(int status) {
        for (UserPlanStatusEnum val : UserPlanStatusEnum.values()) {
            if (val.getStatus() == status) {
                return val;
            }
        }
        return UNKNOWN;
    }

    private final int status;

    private final String desc;
}
