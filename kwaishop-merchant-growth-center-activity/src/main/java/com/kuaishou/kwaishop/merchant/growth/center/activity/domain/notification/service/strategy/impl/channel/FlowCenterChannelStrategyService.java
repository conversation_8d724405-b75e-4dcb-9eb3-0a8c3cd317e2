package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * 主站渠道推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Lazy
@Slf4j
@Service
public class FlowCenterChannelStrategyService implements NotificationChannelStrategyService {

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;

    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return NotificationChannelEnum.FLOW_CENTER;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        // 主站消息推送
        notificationPushFetchService.flowCenterMessagePush(userId, configBO);
    }
}
