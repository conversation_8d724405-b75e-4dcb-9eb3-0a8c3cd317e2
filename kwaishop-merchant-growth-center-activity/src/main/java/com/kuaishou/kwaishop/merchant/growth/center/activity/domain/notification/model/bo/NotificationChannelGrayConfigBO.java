package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import java.util.Map;

import com.kuaishou.framework.config.util.CityHashTailNumberConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NotificationChannelGrayConfigBO {
    /**
     * 渠道-用户维度放量开关
     */
    private Map<Integer/*channelId*/, CityHashTailNumberConfig/*userDimension*/> whiteList;
}
