package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.dispatcher;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.context.LaunchInfoFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.model.result.LaunchInfoResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.fetcher.service.LaunchInfoFetchWrapper;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchAssembleParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.DefaultLaunchFilterParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchInfoBO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-08
 */
@Component
@Slf4j
public class LaunchInfoFetchDispatcher {

    @Resource
    private List<LaunchInfoFetchWrapper> launchInfoFetchWrappers;

    private Map<Integer, LaunchInfoFetchWrapper> launchInfoFetchWrapperMap;

    @PostConstruct
    public void init() {
        launchInfoFetchWrapperMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(launchInfoFetchWrappers)) {
            launchInfoFetchWrapperMap = launchInfoFetchWrappers.stream()
                    .collect(Collectors.toMap(LaunchInfoFetchWrapper::getResourceType, Function.identity(), (k1, k2) -> k1));
        }
    }

    public LaunchInfoResult<List<LaunchInfoBO>> dispatch(List<Integer> resourceTypeList, Long userId,
           String channel, String scene, DefaultLaunchFilterParamBO filterParam, DefaultLaunchAssembleParamBO assembleParam) {
        if (CollectionUtils.isEmpty(resourceTypeList)) {
            log.error("[投放配置分发] 资源类型为空, userId:{}, channel:{}, scene:{}", userId, channel, scene);
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "资源类型为空");
        }
        List<LaunchInfoResult<List<LaunchInfoBO>>> resultList = Lists.newArrayList();
        for (Integer resourceType : resourceTypeList) {
            LaunchInfoFetchWrapper launchInfoFetchWrapper = launchInfoFetchWrapperMap.get(resourceType);
            if (launchInfoFetchWrapper == null) {
                log.error("[投放配置分发] 未找到对应处理器, resourceType:{}, userId:{}, channel:{}, scene:{}",
                        resourceType, userId, channel, scene);
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "未找到对应的分发器");
            }
            // 初始化上下文
            LaunchInfoFetchContext context =
                    launchInfoFetchWrapper.initContext(userId, resourceType, channel, scene, filterParam, assembleParam);
            // 取数
            LaunchInfoResult<List<LaunchInfoBO>> result = launchInfoFetchWrapper.fetch(context);
            if (null != result) {
                resultList.add(result);
            }
        }
        // 合并处理结果
        return combineResult(resultList);
    }

    /**
     * 合并处理结果
     */
    private LaunchInfoResult<List<LaunchInfoBO>> combineResult(List<LaunchInfoResult<List<LaunchInfoBO>>> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        List<LaunchInfoBO> allLaunchInfoList = Lists.newArrayList();
        LaunchInfoResult<List<LaunchInfoBO>> result = new LaunchInfoResult<>();
        for (LaunchInfoResult<List<LaunchInfoBO>> curResult : resultList) {
            if (!curResult.isSuccess()) {
                log.error("[投放配置分发] 投放配置查询失败, result:{}", toJSON(curResult));
                result.setSuccess(false);
                result.setMessage(curResult.getMessage());
                return result;
            }
            if (CollectionUtils.isNotEmpty(curResult.getData())) {
                allLaunchInfoList.addAll(curResult.getData());
            }
        }
        result.setSuccess(true);
        result.setData(allLaunchInfoList);
        return result;
    }

}
