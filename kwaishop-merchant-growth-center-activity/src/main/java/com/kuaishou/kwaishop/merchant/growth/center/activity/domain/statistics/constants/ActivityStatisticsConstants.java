package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.google.common.collect.Lists;
import com.kuaishou.api.blobstore.BlobStoreTable;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-24
 */
public class ActivityStatisticsConstants {
    /**
     * 生效活动整体
     */
    public static final List<Integer> ACTIVITY_EFFECTIVE_STATUS_LIST =
            Arrays.asList(ActivityStatusEnum.EFFECTIVE.getCode(), ActivityStatusEnum.ONLINE.getCode());
    /**
     * 活动期表现标签
     */
    public static final List<String> INDICATOR_PERFORMANCE_TAG = Collections.singletonList("scene_performance");
    /**
     * 左占位符
     */
    public static final String START_SIGN = "{";
    /**
     * 右占位符
     */
    public static final String END_SIGN = "}";
    /**
     * ES聚合字段
     */
    public static final String AGGREGATION = "aggregations";
    public static final String USER_ID = "userId";
    public static final String USER_NAME = "userName";
    public static final String USER_HEAD_URL = "userHeadUrl";
    public static final String DRAW = "draw";
    public static final String DEFAULT_USERS_NAME = "快手用户";
    public static final String DEFAULT_TABLE_CONTENT = "--";
    public static final String ACTIVITY_ID = "activityId";
    public static final String TASK_ID = "taskId";
    public static final String TASK_INFO = "taskInfo";
    public static final String BASE_INFO = "baseInfo";
    public static final String FORECAST_INFO = "forecastInfo";
    public static final String STATISTICS_USER_DETAIL = "statisticsUserDetail";
    public static final String STATISTICS_USER_DETAIL_EXPORT = "statisticsUserDetailExport";
    public static final String STAFF_POLICY_USER_DETAIL_EXPORT_EXTEND = "staffPolicyUserDetailExportExtend";
    public static final String PLAN_STATISTICS_DETAIL = "planStatisticsDetail";
    public static final String FILE_PATH_DIR = "/tmp";
    public static final BlobStoreTable BLOB_STORE_TABLE = BlobStoreTable.UPLOAD_MERCHANT;
    public static final String ACTIVITY_STATISTICS_DATA = "activityStatisticsData";
    public static final String BASE_AVG = "base_indicator_day_avg_";
    public static final String BASE_TOTAL = "base_indicator_total_";
    public static final String BASE_ACTIVITY_TOTAL = "base_indicator_activity_total_";
    public static final String CHILD_TASK_NAME_SUFFIX = "周期%s子任务";
    public static final String PARENT_TASK_DESC = "父任务";
    public static final String STAGE_SUFFIX = "阶段%s";
    public static final String CUSTOMIZE = "customize";
    public static final String TERMS_AGG = "terms";
    public static final String REGISTRATION_RISK_GROUP = "registrationRisk";
    public static final String REGISTRATION_RISK_DESC = "riskDesc";
    public static final String REGISTRATION_RISK_REASON = "registrationRiskReason";
    public static final String DRAW_RISK_GROUP = "drawRisk";
    public static final String DRAW_RISK_DESC = "drawRiskDesc";
    public static final String DRAW_RISK_REASON = "drawRiskReason";
    public static final String AGG_BASIC_FORMAT = "{\"%s\":{\"%s\":{\"field\":\"%s\"}}}";
    public static final String AGG_NESTED_FORMAT = "{\"%s\": {\"nested\": {\"path\": \"%s\"}, \"aggs\": %s}}";
    public static final String AGG_FILTER_FORMAT = "{\"%s\":{\"filter\":{\"%s\":{\"%s\": %s}}, \"aggs\": %s}}";
    public static final String AGG_TERMS_FORMAT = "{\"%s\":{\"%s\":{\"field\":\"%s\",\"size\":1}}}";
    /**
     * 不推荐用，超过4w精度会丢失5%，出现性能问题可以降级到此方式
     * https://www.elastic.co/guide/en/elasticsearch/reference/current/search-aggregations-metrics-cardinality-aggregation.html
     */
    public static final String AGG_CARDINALITY_FORMAT = "{\"%s\":{\"%s\":{\"field\":\"%s\",\"precision_threshold\":%s}}}";

    /********* 活动进度查询 *****************/
    public static final String OWNER_STAFF = "ownerStaff";
    public static final String SMB_OWNER_STAFF = "smbOwnerStaff";
    public static final String LEAF_PARTY_CODE = "leafPartyCode";
    public static final String SECOND_INDUSTRY_CODE = "secondIndustryCode";
    public static final String SECOND_INDUSTRY_NAME = "secondIndustryName";

    public static final String THIRD_INDUSTRY_CODE = "thirdIndustryCode";

    public static final String THIRD_INDUSTRY_NAME = "thirdIndustryName";
    public static final String SELLER_INDUSTRY = "sellerIndustry";
    public static final String ACTIVITY_NAME = "activityName";
    public static final String ACTIVITY_END_TIME = "activityEndTime";
    public static final String ACTIVITY_START_TIME = "activityStartTime";
    public static final String DIFF_TYPE_ORG = "0";
    public static final String DIFF_TYPE_STAFF = "1";
    public static final String DIFF_TYPE_BOTH = "2";
    public static final String DIFF_TYPE_ORG_SMB = "4";
    public static final String DIFF_TYPE_STAFF_SMB = "5";
    public static final String DIFF_TYPE_BOTH_SMB = "6";
    public static final String ACTIVITY_PROGRESS_QUERY_1 = "activityProgressQuery";
    public static final Long MY_ONE = 1L;
    public static final Long MY_ZERO = 0L;
    public static final String MY_YES = "是";
    public static final String MY_NO = "否";
    public static final String EXPORT_NOTICE_S = "exportActivityProgressDetailSuccess";
    public static final String EXPORT_NOTICE_F = "exportActivityProgressDetailFail";

    public static final String EXPORT_USER_ACTION_NOTICE_S = "exportUserActionDetailSuccess";
    public static final String EXPORT_USER_ACTION_NOTICE_F = "exportUserActionDetailFail";

    public static final String EXPORT_PLAN_OVERVIEW_NOTICE_S = "exportPlanOverviewSuccess";
    public static final String EXPORT_PLAN_OVERVIEW_NOTICE_F = "exportPlanOverviewSFail";

    public static final String E_STR = "-";
    public static final String E_STR_2 = "--";
    public static final String STAGE_SUFFIX_01 = "阶梯%s";
    public static final List<String> SPECIAL_SHOW_TYPE_LIST = Lists.newArrayList("info", "hover", "time");
    public static final String SHEET_NAME = "sheet1";
    public static final String DAY_CODE_FORMAT_PATTERN = "yyyy/MM/dd";
    public static final String STRATEGY_FIX_TARGET_KEY = "strategyActivity_indicator";
    public static final String STRATEGY_FIX_AWARD_KEY = "strategyActivity_award";

    /**
     * 行业bizCode
     */
    public static final String BIZ_CODE = "merchant_operation";
    /**
     * 中小bizCode
     */
    public static final String SMB_BIZ_CODE = "smb_operation";
    public static final Integer FIRST_LEVEL = 1;
    public static final Integer SECOND_LEVEL = 2;
    public static final String SECOND_INDUSTRY_CODE_KEY = "secondIndustryCode";
    public static final String SECOND_INDUSTRY_NAME_KEY = "secondIndustryName";
    public static final String FIRST_INDUSTRY_CODE_KEY = "firstIndustryCode";
    public static final String FIRST_INDUSTRY_NAME_KEY = "firstIndustryName";
    public static final String SELLER_INDUSTRY_KEY = "sellerIndustry";

    public static final String THIRD_INDUSTRY_NAME_KEY = "thirdIndustryName";

    public static final String THIRD_INDUSTRY_CODE_KEY = "thirdIndustryCode";

    /******************************* 中小权限相关字段 *******************************/

    public static final String SMB_FIRST_INDUSTRY_CODE_KEY = "smbFirstIndustryCode";
    public static final String SMB_FIRST_INDUSTRY_NAME_KEY = "smbFirstIndustryName";

    public static final String SMB_SECOND_INDUSTRY_CODE_KEY = "smbSecondIndustryCode";
    public static final String SMB_SECOND_INDUSTRY_NAME_KEY = "smbSecondIndustryName";


    public static final String SMB_THIRD_INDUSTRY_CODE_KEY = "smbThirdIndustryCode";
    public static final String SMB_THIRD_INDUSTRY_NAME_KEY = "smbThirdIndustryName";
    /******************************* 中小权限相关字段 *******************************/

}
