package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.notificationCode;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.needAuditChannelList;
import static com.kuaishou.kwaishop.platform.common.utils.JsonUtil.toJson;
import static java.util.Collections.singletonList;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.MerchantCenterMessageTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationConfigPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationCodeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.TaskPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-21
 */
@Lazy
@Slf4j
@Service
public class ActivityReachStepStrategyService extends AbstractNotificationCodeStrategyService {
    @Override
    public Set<NotificationCodeEnum> getNotificationCodeSet() {
        return Sets.newHashSet(NotificationCodeEnum.ACTIVITY_REACH_STEP);
    }

    @Override
    public List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList,
            List<AwardConfigDO> awardConfigList, NotificationConfigBO notificationConfig) {
        NotificationAdminDomainBO notificationAdminDomainBO = getNotificationAdminDomainBO(activity, notificationConfig);
        //构建templateConfig
        String templateConfig = notificationAdminDomainBO.getTemplateConfig();
        if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
            MerchantCenterMessageTemplateConfigBO templateConfigBO =
                    ObjectMapperUtils.fromJSON(templateConfig, MerchantCenterMessageTemplateConfigBO.class);
            if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
                templateConfigBO.setCode(notificationConfig.getTemplateCode());
            }
            templateConfig = toJson(templateConfigBO);
        }

        int status;
        if (needAuditChannelList.get().contains(notificationConfig.getChannel())) {
            status = NotificationStatusEnum.NEED_AUDIT.getVal();
        } else {
            status = NotificationStatusEnum.VALID.getVal();
        }

        List<NotificationPushConfigBO> res = Lists.newArrayList();
        long now = System.currentTimeMillis();
        //子任务第一阶段
        taskList = taskList.stream()
                .filter(task -> task.getParentTask() != 0 && task.getStage() == 1 && now < task.getEndTime())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "当前任务已结束，无法创建任务阶梯达成进度触达配置");
        }
        String finalTemplateConfig = templateConfig;

        taskList.forEach(task -> {
            PeriodConfigBO periodConfigBO = fromJSON(notificationAdminDomainBO.getPeriodConfig(), PeriodConfigBO.class);
            if (Objects.isNull(periodConfigBO)) {
                periodConfigBO = buildPeriodConfig(task, notificationConfig.getIntervalDay());
            }
            NotificationPushConfigBO notificationPushConfigBO = NotificationPushConfigBO.builder()
                    .activityId(activity.getId())
                    .entityId(task.getId())
                    .entityType(notificationAdminDomainBO.getEntityType())
                    .entityStatus(notificationAdminDomainBO.getEntityStatus())
                    .channel(notificationConfig.getChannel())
                    .templateConfig(finalTemplateConfig)
                    .occasion(notificationAdminDomainBO.getOccasion())
                    .creator(SymbolConstants.DEFAULT_USER_SYMBOL)
                    .modifier(SymbolConstants.DEFAULT_USER_SYMBOL)
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .status(status)
                    .periodConfig(periodConfigBO)
                    .scene(notificationConfig.getNotificationCode())
                    .build();
            res.add(notificationPushConfigBO);
        });
        return res;
    }

    private PeriodConfigBO buildPeriodConfig(TaskDO task, long interValDay) {
        //获取任务开始时间
        long startTime = task.getStartTime();
        //获取任务结束时间
        long endTime = task.getEndTime();
        Integer periodType = task.getPeriodType();
        TaskPeriodTypeEnum typeEnum = TaskPeriodTypeEnum.of(periodType);
        long gap = 0L;
        switch (typeEnum) {
            case ABSOLUTE:
                //计算开始时间和结束时间的差值
                gap = DateUtils.getDayBetween(startTime, endTime);
                break;
            case RELATIVE:
            case DELAY_RELATIVE:
                gap = task.getPeriodDay();
                break;
            default:
                break;
        }

        //计算延迟时间
        List<Long> dayList = DateUtils.calculateDayList(1, gap + 1, 1);

        String day = StringUtils.join(dayList, SymbolConstants.COMMA);

        NotificationPeriodBO notificationPeriod = NotificationPeriodBO.builder()
                .day(day)
                .hour("10,17")
                .minute("0")
                .periodType(NotificationConfigPeriodTypeEnum.ABSOLUTE.getVal())
                .intervalDay(interValDay)
                .build();

        return PeriodConfigBO.builder()
                .notificationPeriods(singletonList(notificationPeriod))
                .build();
    }
}
