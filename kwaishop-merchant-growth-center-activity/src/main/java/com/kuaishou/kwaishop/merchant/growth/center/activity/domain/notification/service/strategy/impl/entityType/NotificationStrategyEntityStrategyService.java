package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.entityType;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_REGISTRATION_CONSUMER_ERROR;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationEntityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.StrategyBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.enums.StrategyNotificationStatusTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.ActivityStrategyNotificationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.ActivityStrategyService;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 策略维度推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-28
 */
@Slf4j
@Service
public class NotificationStrategyEntityStrategyService extends AbstractNotificationEntityService {

    @Autowired
    private ActivityStrategyNotificationService activityStrategyNotificationService;

    @Autowired
    private ActivityStrategyService activityStrategyService;

    @Override
    public NotificationEntityTypeEnum getNotificationStrategyEntityType() {
        return NotificationEntityTypeEnum.STRATEGY;
    }

    @Override
    public boolean checkEntityStatus(long userId, NotificationPushConfigBO configBO) {
        int currentStatus = getCurrentStatus(userId, configBO);
        boolean flag = currentStatus == configBO.getEntityStatus();
        if (!flag) {
            log.info("[策略维度推送] 当前用户活动状态发生改变，取消推送 userId:{},"
                            + " configId:{}, currentStatus:{}, configStatus:{}",
                    userId, configBO.getId(), currentStatus, configBO.getEntityStatus());
        }
        return flag;
    }

    @Override
    public boolean checkEndTime(long userId, NotificationPushConfigBO configBO) {
        StrategyBO strategyBO =
                activityStrategyService.queryBaseStrategyByIdWithLocalCache(configBO.getEntityId());
        if (strategyBO == null) {
            log.error("[策略维度推送] strategyBO is null, msg:{}", ObjectMapperUtils.toJSON(configBO));
            throw new BizException(NOTIFICATION_REGISTRATION_CONSUMER_ERROR, "strategyBO is null");
        }
        // 当前时间大于策略活动结束时间
        if (System.currentTimeMillis() > strategyBO.getEndTime()) {
            log.info("[策略维度推送] 当前策略已结束，推送取消, userId:{}, configBO:{}", userId, ObjectMapperUtils.toJSON(configBO));
            return true;
        }
        return false;
    }

    private int getCurrentStatus(long userId, NotificationPushConfigBO configBO) {
        StrategyNotificationStatusTypeEnum strategyUserStatus = activityStrategyNotificationService
                .getStrategyUserStatus(configBO.getActivityId(), configBO.getEntityId(), userId);
        log.info("[NotificationStrategyEntityStrategyService] userId:{}, configId:{}, currentStatus:{}", userId,
                configBO.getId(), strategyUserStatus.getDesc());
        return strategyUserStatus.getCode();
    }
}
