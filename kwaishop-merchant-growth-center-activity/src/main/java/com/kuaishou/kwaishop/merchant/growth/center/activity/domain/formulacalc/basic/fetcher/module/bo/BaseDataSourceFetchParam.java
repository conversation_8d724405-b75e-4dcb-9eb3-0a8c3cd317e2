package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseDataSourceFetchParam {

    /**
     * 用户ID
     */
    private long userId;

    /**
     * 指标配置
     */
    private IndicatorConfigDO indicatorConfig;

    /**
     * 指标元数据
     */
    private IndicatorDO indicator;

    /**
     * 基期时间列表
     */
    private List<String> dateList;

    /**
     * 基期数据查询类型
     */
    private BaseDataSourceFetchTypeEnum baseDataSourceFetchType;

    /**
     * 周期天数
     */
    private Long cycleDuration;
}
