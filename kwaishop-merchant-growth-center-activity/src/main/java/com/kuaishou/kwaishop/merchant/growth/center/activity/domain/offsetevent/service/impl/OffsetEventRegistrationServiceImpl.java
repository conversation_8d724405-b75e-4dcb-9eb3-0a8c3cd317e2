package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.getTaskOffsetEvent;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveBasicConfigFromTaskExt;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.enums.UserTaskPerfEnum.OFFSET_EVENT_BASIC_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.offsetEventCheckIndicatorAuditTimeWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.enableRefreshRegistrationTagList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.UserDrawTaskResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.UserUnableDrawTaskReasonEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.OffsetEventRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.OffsetEventService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.tag.model.enums.TagEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.utils.TaskPeriodUtil;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.annotation.CodeNote;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-17
 */
@Service
@Slf4j
public class OffsetEventRegistrationServiceImpl implements OffsetEventRegistrationService {
    @Resource
    private UserRegistrationRecordDAO userRegistrationRecordDAO;
    @Resource
    private OffsetEventService offsetEventService;
    @Resource
    private AdminActivityOnlineService adminActivityOnlineService;
    @Resource
    private IndicatorConfigDAO indicatorConfigDAO;
    @Resource
    private IndicatorLocalCacheService indicatorLocalCacheService;
    @Resource
    private RegistrationConverter registrationConverter;
    @Resource
    private TaskLocalCacheService taskLocalCacheService;
    @Resource
    private IndicatorBasicService indicatorBasicService;
    @Resource
    private IndicatorBasicNewService indicatorBasicNewService;

    @Override
    public void refreshRegistrationRecord(UserRegistrationRecordDO registrationRecordDO) {
        Long userId = registrationRecordDO.getUserId();
        Long activityId = registrationRecordDO.getActivityId();
        Long parentTaskId = registrationRecordDO.getEntityId();
        if (!registrationRecordDO.getEntityType().equals(EntityTypeEnum.TASK.getCode())) {
            return;
        }
        // 资格非有效状态
        if (!UserRegistrationStatusEnum.VALID.getCode().equals(registrationRecordDO.getStatus())) {
            return;
        }
        // 非偏移任务过滤
        if (!registrationConverter.checkOffsetEventRegistrationRecord(registrationRecordDO)) {
            return;
        }
        UserRegistrationRecordExtBO userRegistrationRecordExtBO =
                ObjectMapperUtils.fromJSON(registrationRecordDO.getExt(), UserRegistrationRecordExtBO.class);
        // 标签不存在
        if (null == userRegistrationRecordExtBO || CollectionUtils.isEmpty(userRegistrationRecordExtBO.getTags())) {
            return;
        }
        List<String> originalTags = userRegistrationRecordExtBO.getTags();
        List<String> snapshotTags = Lists.newArrayList(originalTags);
        // 无待领取标签
        if (CollectionUtils.isEmpty(originalTags) || CollectionUtils.isEmpty(
                CollectionUtils.intersection(originalTags, enableRefreshRegistrationTagList.get()))) {
            return;
        }
        AddRegistrationOptionBO option = userRegistrationRecordExtBO.getOption();
        if (null == option) {
            option = new AddRegistrationOptionBO();
        }
        // 重新进行资格标签计算
        TaskDO parentTask = taskLocalCacheService.getTaskByTaskId(parentTaskId);
        if (null == parentTask) {
            log.error("[刷新资格]父任务不存在, userId:{}, activityId:{}, taskId:{}", activityId, userId, parentTaskId);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "父任务不存在");
        }
        List<String> currentTags = calRegistrationRecordTag(parentTask, userId, option);

        // 重刷基期数据
        if (!currentTags.contains(TagEnum.TASK_WAIT_DRAW.getTagName()) && !currentTags.contains(
                TagEnum.TASK_UNABLE_DRAW.getTagName())) {
            refreshRegistrationBasicInfo(userId, parentTask, registrationRecordDO);
        }

        // 重新计算tag
        originalTags.removeAll(
                Lists.newArrayList(TagEnum.TASK_WAIT_DRAW.getTagName(), TagEnum.TASK_UNABLE_DRAW.getTagName()));
        originalTags.addAll(currentTags);

        if (snapshotTags.size() == originalTags.size() && new HashSet<>(snapshotTags).containsAll(originalTags)) {
            log.warn("[刷新资格]标签无变化,不落db， userId:{}, activityId:{}, taskId:{}", activityId, userId, parentTaskId);
            return;
        }

        userRegistrationRecordExtBO.setTags(originalTags);
        userRegistrationRecordExtBO.setOption(option);
        registrationRecordDO.setExt(ObjectMapperUtils.toJSON(userRegistrationRecordExtBO));
        userRegistrationRecordDAO.updateUserRegistrationRecordById(registrationRecordDO);
    }

    @Override
    public UserDrawTaskResult checkOffsetEventTaskCanDrawTask(Long userId, TaskDO parentTask) {
        String taskOffsetEvent = getTaskOffsetEvent(parentTask);
        // 非偏移任务
        if (StringUtils.isBlank(taskOffsetEvent)) {
            return UserDrawTaskResult.builder().enable(Boolean.TRUE).build();
        }
        UserDrawTaskResult result = doCheckOffsetEventTaskCanDrawTask(userId, parentTask, taskOffsetEvent);
        log.info("[偏移事件资格校验结果] userId:{}, parentTaskId:{}, result:{}", userId, parentTask.getId(),
                ObjectMapperUtils.toJSON(result));
        return result;
    }

    /**
     * 计算资格标签
     */
    @Override
    public List<String> calRegistrationRecordTag(TaskDO layerParentTask, Long userId,
            AddRegistrationOptionBO userOption) {
        List<String> tagList = Lists.newArrayList();
        String taskOffsetEvent = getTaskOffsetEvent(layerParentTask);
        // 非偏移事件, 不计算tag
        if (StringUtils.isBlank(taskOffsetEvent)) {
            return tagList;
        }
        UserDrawTaskResult result = checkOffsetEventTaskCanDrawTask(userId, layerParentTask);
        if (result.isEnable()) {
            userOption.setOffsetEventFinishFlag(Boolean.TRUE);
            return tagList;
        }
        UserUnableDrawTaskReasonEnum reasonEnum = result.getReasonEnum();
        checkArgument(null != reasonEnum, "不可领取原因为空");
        userOption.setOffsetEventFinishFlag(Boolean.FALSE);
        switch (reasonEnum) {
            case OFFSET_EVENT_NOT_HAPPEN:
            case BASIC_DATA_NOT_FILLED:
                tagList.add(TagEnum.TASK_WAIT_DRAW.getTagName());
                break;
            case TASK_PERIOD_TIME_INVALID:
            case BASIC_TIME_INVALID:
                tagList.add(TagEnum.TASK_UNABLE_DRAW.getTagName());
                break;
            default:
                break;
        }
        return tagList;
    }

    @Override
    public boolean existOffsetEventTask(Long activityId, Long userId, List<Long> taskIdList) {
        List<UserRegistrationRecordDO> userRegistrations;
        // 传入父任务，优先判断指定父任务中是否存在偏移任务。否则，整个活动级别判断
        if (CollectionUtils.isNotEmpty(taskIdList)) {
            userRegistrations =
                    userRegistrationRecordDAO.batchQueryUserRegistrationRecords(activityId, EntityTypeEnum.TASK,
                            taskIdList, userId, UserRegistrationStatusEnum.VALID, Boolean.TRUE);
        } else {
            userRegistrations = userRegistrationRecordDAO.queryUserRegistrationRecordsWithReadMaster(activityId,
                    EntityTypeEnum.TASK, userId, UserRegistrationStatusEnum.VALID, Boolean.TRUE);
        }
        if (CollectionUtils.isEmpty(userRegistrations)) {
            return false;
        }
        return userRegistrations.stream().anyMatch(
                userRegistration -> registrationConverter.checkOffsetEventRegistrationRecord(userRegistration));
    }

    /**
     * 重刷资格中基期数据
     */
    @CodeNote("解析用户报名记录的json数据")
    private void refreshRegistrationBasicInfo(Long userId, TaskDO parentTask,
            UserRegistrationRecordDO registrationRecordDO) {
        Map<String, Object> jsonMap =
                fromJSON(registrationRecordDO.getJsonData(), Map.class, String.class, Object.class);
        jsonMap.putAll(adminActivityOnlineService.getUserLayerTaskOffsetEventJsonData(userId, parentTask));
        List<IndicatorConfigDO> baseIndicatorConfigs =
                indicatorConfigDAO.queryTaskBaseIndicatorConfig(parentTask.getActivityId(), parentTask.getId());
        List<Long> baseIndicatorIds =
                baseIndicatorConfigs.stream().map(IndicatorConfigDO::getIndicatorId).collect(Collectors.toList());
        Map<Long, IndicatorDO> baseIndicatorMap = indicatorLocalCacheService.queryTaskIndicators(baseIndicatorIds);
        jsonMap.putAll(
                adminActivityOnlineService.getUserLayerTaskBasicJsonData(userId, parentTask, baseIndicatorConfigs,
                        baseIndicatorMap));
        registrationRecordDO.setJsonData(toJSON(jsonMap));
    }

    /**
     * 校验
     */
    private UserDrawTaskResult doCheckOffsetEventTaskCanDrawTask(Long userId, TaskDO parentTask,
            String taskOffsetEvent) {
        UserDrawTaskResult result =
                UserDrawTaskResult.builder().taskOffsetEventType(taskOffsetEvent).enable(Boolean.FALSE).build();
        Long offsetEventHappenTime = offsetEventService.getOffsetEventHappenTime(taskOffsetEvent, userId);
        // 偏移事件未发生
        if (null == offsetEventHappenTime) {
            log.warn("[偏移事件资格校验] 偏移事件未发生， userId:{}, parentTaskId:{}", userId, parentTask.getId());
            result.setReasonEnum(UserUnableDrawTaskReasonEnum.OFFSET_EVENT_NOT_HAPPEN);
            return result;
        }
        result.setOffsetEventHappenTime(offsetEventHappenTime);
        ImmutablePair<Long, Long> taskPeriodTime = TaskPeriodUtil.getTaskPeriodTime(userId, parentTask, null, null);
        Long userTaskStartTime = taskPeriodTime.getLeft();
        Long userTaskEndTime = taskPeriodTime.getRight();
        result.setTaskStartTime(userTaskStartTime);
        result.setTaskEndTime(userTaskEndTime);
        // 不在任务周期内. 用户任务开始时间需在活动时间内，结束时间可大于活动结束时间
        if (userTaskStartTime < parentTask.getStartTime() || userTaskStartTime >= parentTask.getEndTime()) {
            log.warn("[偏移事件资格校验] 不在任务周期内， userId:{}, parentTaskId:{}", userId, parentTask.getId());
            result.setReasonEnum(UserUnableDrawTaskReasonEnum.TASK_PERIOD_TIME_INVALID);
            return result;
        }
        BasicConfigBO basicConfigBO = resolveBasicConfigFromTaskExt(parentTask);
        // 无基期配置
        if (null == basicConfigBO || CollectionUtils.isEmpty(basicConfigBO.getBasicIndicatorConfigList())) {
            return UserDrawTaskResult.builder().enable(Boolean.TRUE).build();
        }
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = basicConfigBO.getBasicIndicatorConfigList();
        for (BasicIndicatorConfigBO basicIndicatorConfigBO : basicIndicatorConfigList) {
            BasicTimeRange basicTimeRange = indicatorBasicNewService.getBasicTimeRange(userId, basicIndicatorConfigBO);
            if (null == basicTimeRange.getEarliestStartTime() || null == basicTimeRange.getLatestEndTime()) {
                log.warn("[偏移事件资格校验] 基期时间无效， userId:{}, parentTaskId:{}", userId, parentTask.getId());
                result.setReasonEnum(UserUnableDrawTaskReasonEnum.BASIC_TIME_INVALID);
                perfException(OFFSET_EVENT_BASIC_TIME, "偏移事件基期校验不通过", "基期时间无效");
                return result;
            }
            Long basicStartTime = basicTimeRange.getEarliestStartTime();
            Long basicEndTime = basicTimeRange.getLatestEndTime();
            result.setBaseStartTime(basicStartTime);
            result.setBaseEndTime(basicEndTime);
            // 基期时间与任务时间重合
            if (basicEndTime > userTaskStartTime) {
                log.warn("[偏移事件资格校验] 基期时间与任务时间重合， userId:{}, parentTaskId:{}", userId,
                        parentTask.getId());
                result.setReasonEnum(UserUnableDrawTaskReasonEnum.BASIC_TIME_INVALID);
                perfException(OFFSET_EVENT_BASIC_TIME, "偏移事件基期校验不通过", "基期时间与任务时间重合");
                return result;
            }
            List<Long> indicatorIds = Collections.singletonList(basicIndicatorConfigBO.getIndicatorId());
            long earliestStartTime = indicatorBasicService.getIndicatorsEffectiveStartTime(indicatorIds);
            if (basicStartTime < earliestStartTime) {
                log.error("[偏移事件资格校验] 基期最早时间超过最大时间， userId:{}, parentTaskId:{}", userId,
                        parentTask.getId());
                result.setReasonEnum(UserUnableDrawTaskReasonEnum.BASIC_TIME_INVALID);
                perfException(OFFSET_EVENT_BASIC_TIME, "偏移事件基期校验不通过", "基期最早时间超过最大时间");
                return result;
            }
            long maxIndicatorAuditTime = 0L;
            // 未命中白名单
            if (!offsetEventCheckIndicatorAuditTimeWhiteList.get().contains(parentTask.getActivityId())) {
                maxIndicatorAuditTime = indicatorBasicService.getMaxIndicatorAuditTime(indicatorIds);
            }

            // 基期结束时间+基期指标稳定期作为基期稳定期
            if (basicEndTime + maxIndicatorAuditTime > System.currentTimeMillis()) {
                log.warn(
                        "[偏移事件资格校验] 基期时间未结束, userId:{}, parentTaskId:{}, basicEndTime:{}, maxIndicatorAuditTime:{}",
                        userId, parentTask.getId(), basicEndTime, maxIndicatorAuditTime);
                result.setReasonEnum(UserUnableDrawTaskReasonEnum.BASIC_DATA_NOT_FILLED);
                return result;
            }
        }
        result.setEnable(Boolean.TRUE);
        return result;
    }

    public static void main(String[] args) {
        UserRegistrationRecordExtBO userRegistrationRecordExtBO = new UserRegistrationRecordExtBO();
        userRegistrationRecordExtBO.setTags(Lists.newArrayList(TagEnum.TASK_WAIT_DRAW.getTagName()));

        List<String> originalTags = userRegistrationRecordExtBO.getTags();
        List<String> snapshotTags = Lists.newArrayList(originalTags);


        originalTags.removeAll(Lists.newArrayList(TagEnum.TASK_WAIT_DRAW.getTagName()));
        originalTags.addAll(Lists.newArrayList(TagEnum.TASK_UNABLE_DRAW.getTagName()));


        System.out.println(snapshotTags.size() == originalTags.size() && new HashSet<>(snapshotTags).containsAll(originalTags));
    }
}
