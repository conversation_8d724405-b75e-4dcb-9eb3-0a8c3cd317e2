package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.IndustryReportConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CancelUserJoinRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.DeleteUserReportRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.InterveneUserTimeRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinIndustryTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryAllReportRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryAllReportRecordResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryReportOperatorIndustryRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.SendUserResourceActivityAwardRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.TerminateUserResourceRuleRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-21
 */
public interface ManageForResourceBizService {

    /**
     * 创建补贴活动
     */
    long createResourceActivity(CreateResourceActivityRequest request);

    /**
     * 创建补贴任务
     */
    List<Long> createResourceTask(CreateResourceTaskRequest request);

    /**
     * 领取补贴任务
     */
    void joinResourceTask(JoinResourceTaskRequest request);

    /**
     * 领取重点招商任务
     */
    void joinIndustryTask(JoinIndustryTaskRequest request);

    /**
     * 查询提报记录
     */
    QueryAllReportRecordResponse queryJoinRecord(QueryAllReportRecordRequest request);

    /**
     * 取消重点招商任务
     */
    void cancelIndustryTask(CancelUserJoinRecordRequest request);

    /**
     * 删除提报记录
     */
    void deleteReportRecord(DeleteUserReportRecordRequest request);

    /**
     * 查询提报人对应的行业
     */
    IndustryReportConfigBO queryOperatorIndustry(QueryReportOperatorIndustryRequest request);

    /**
     * 干预用户任务时间
     */
    void interveneUserTime(InterveneUserTimeRequest request);

    /**
     * 终止用户任务
     */
    void terminateUserResourceRule(TerminateUserResourceRuleRequest request);

    /**
     * 终止用户某个父任务下的所有任务记录
     */
    void terminateUserRecordByParentId(long userId, long activityId, long parentTaskId);

    /**
     * 发放用户补贴活动奖励
     */
    void sendUserResourceActivityAward(SendUserResourceActivityAwardRequest request);

    /**
     * 查询权益配置，若不存在则创建，创建加锁
     */
    long queryAndCreateInterestConfig(int awardType, long awardExpireTime, String fundBudgetId);
}
