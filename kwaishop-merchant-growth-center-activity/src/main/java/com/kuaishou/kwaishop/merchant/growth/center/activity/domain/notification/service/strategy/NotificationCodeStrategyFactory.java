package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
@Lazy
@Service
public class NotificationCodeStrategyFactory {
    private Map<NotificationCodeEnum, NotificationCodeStrategyService> notificationPeriodConfigStrategyServiceMap;

    @Autowired
    private List<NotificationCodeStrategyService> notificationPeriodConfigStrategyServiceList;

    @PostConstruct
    private void init() {
        notificationPeriodConfigStrategyServiceMap = new HashMap<>();
        notificationPeriodConfigStrategyServiceList.forEach(e -> {
            Set<NotificationCodeEnum> notificationCodeSet = e.getNotificationCodeSet();
            if (CollectionUtils.isEmpty(notificationCodeSet)) {
                return;
            }
            for (NotificationCodeEnum notificationCodeEnum : notificationCodeSet) {
                notificationPeriodConfigStrategyServiceMap.put(notificationCodeEnum, e);
            }
        });
    }

    public NotificationCodeStrategyService getNotificationPeriodConfigStrategyService(NotificationCodeEnum codeEnum) {
        return notificationPeriodConfigStrategyServiceMap.get(codeEnum);
    }
}
