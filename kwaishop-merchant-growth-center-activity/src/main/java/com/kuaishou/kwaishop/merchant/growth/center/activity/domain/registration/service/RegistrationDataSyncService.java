package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.syncMerchantDataTaskSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.registrationDataSyncBufferSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.registrationDataSyncTag;
import static com.kuaishou.kwaishop.merchant.growth.center.protobuf.MerchantGrowthStatus.WAIT_PROCESS_VALUE;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.phantomthief.util.CursorIterator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.MerchantActivityLayerDataLocalDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.MerchantActivityLayerDataRemoteDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.offline.MerchantActivityLayerDataRemoteDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.MerchantActivityLayerDataLocalDO;
import com.kuaishou.kwaishop.merchant.growth.utils.sync.AbstractSingleSync;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-13
 */
@Service
public class RegistrationDataSyncService extends
        AbstractSingleSync<MerchantActivityLayerDataRemoteDO, MerchantActivityLayerDataLocalDO> {


    @Autowired
    private MerchantActivityLayerDataRemoteDAO merchantActivityLayerDataRemoteDAO;

    @Autowired
    private MerchantActivityLayerDataLocalDAO merchantActivityLayerDataLocalDAO;


    @Override
    public MerchantActivityLayerDataLocalDO transform(
            MerchantActivityLayerDataRemoteDO remoteDO) {
        MerchantActivityLayerDataLocalDO localDO = new MerchantActivityLayerDataLocalDO();
        localDO.setUserId(remoteDO.getSellerId());
        localDO.setTag(remoteDO.getTag());
        localDO.setEntityIds("");
        localDO.setEntityAlias("");
        localDO.setJsonData(remoteDO.getJsonData());
        localDO.setStatus(WAIT_PROCESS_VALUE);
        localDO.setCreateTime(System.currentTimeMillis());
        localDO.setUpdateTime(System.currentTimeMillis());
        return localDO;
    }

    @Override
    public String getBizName() {
        return "RegistrationDataSyncService";
    }

    @Override
    public Stream<MerchantActivityLayerDataRemoteDO> fetch() {
        return CursorIterator.<Long, MerchantActivityLayerDataRemoteDO> newGenericBuilder()
                .bufferSize(registrationDataSyncBufferSize.get())
                .start(0L)
                .cursorExtractor(MerchantActivityLayerDataRemoteDO::getId)
                .buildEx((cursor, limit) -> {
                    return merchantActivityLayerDataRemoteDAO.fetchList(registrationDataSyncTag.get(),
                            0, cursor, limit);
                })
                .stream();
    }

    @Override
    public boolean insert(MerchantActivityLayerDataLocalDO localDO) {
        MerchantActivityLayerDataLocalDO localData =
                merchantActivityLayerDataLocalDAO.queryByTagAndUserId(localDO.getTag(),
                        localDO.getUserId());
        if (localData == null) {
            boolean inserted = merchantActivityLayerDataLocalDAO.insert(localDO) > 0;
            if (inserted) {
                perfSuccess(ActivityPerfTagEnum.REGISTRATION_DATA_SYNC_ITEM, "insert", localDO.getTag());
            }
            return inserted;
        }
        if (syncMerchantDataTaskSwitch.get()) {
            localDO.setId(localData.getId());
            boolean updated = merchantActivityLayerDataLocalDAO.updateById(localDO) > 0;
            if (updated) {
                perfSuccess(ActivityPerfTagEnum.REGISTRATION_DATA_SYNC_ITEM, "update", localDO.getTag());
            }
            return updated;
        }
        return true;
    }

    @Override
    public void after(boolean inserted, MerchantActivityLayerDataLocalDO localDO) {
        if (inserted) {
            merchantActivityLayerDataRemoteDAO.updateSyncSuccess(localDO.getTag(), localDO.getUserId());
        }
    }
}
