package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.award.handler;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.converter.CalculateFormulaConverter.getIndicatorFactorFetchConfig;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Preconditions;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.core.ICalcFactorFetchHandler;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.annotation.BizFactor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.bo.FactorValueFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.config.DmFetchConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.BizSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.FactorSourceTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-21
 */
@BizFactor(bizScene = BizSceneEnum.AWARD_CALC, factorSourceType = FactorSourceTypeEnum.dm)
public class AwardCalcDmFactorFetchHandler implements ICalcFactorFetchHandler {
    @Override
    public void fillFactorFetchConfig(FactorValueFetchContext context) {
        Map<String, Object> bizParam = context.getBizParam();
        String factorCode = context.getFactorCode();
        Long returnIndicatorId = MapUtils.getLong(bizParam, FormulaCalcConstants.RETURN_INDICATOR_ID);
        String fetchConfig = getIndicatorFactorFetchConfig(returnIndicatorId, factorCode);
        Preconditions.checkArgument(StringUtils.isNotEmpty(fetchConfig),
                String.format("因子配置不存在，factorCode:%s, returnIndicatorId:%s", factorCode, returnIndicatorId));
        DmFetchConfig dmFetchConfig = ObjectMapperUtils.fromJSON(fetchConfig, DmFetchConfig.class);
        Object userTaskRecord = MapUtils.getObject(bizParam, FormulaCalcConstants.USER_TASK_RECORD);
        Preconditions.checkNotNull(userTaskRecord, "userTaskRecord不能为空");
        UserTaskRecordDO userTaskRecordDO = (UserTaskRecordDO) userTaskRecord;
        dmFetchConfig.setUserId(userTaskRecordDO.getUserId());
        dmFetchConfig.setStartTime(userTaskRecordDO.getStartTime());
        dmFetchConfig.setEndTime(userTaskRecordDO.getEndTime());
        context.setFetchConfig(dmFetchConfig);
    }
}
