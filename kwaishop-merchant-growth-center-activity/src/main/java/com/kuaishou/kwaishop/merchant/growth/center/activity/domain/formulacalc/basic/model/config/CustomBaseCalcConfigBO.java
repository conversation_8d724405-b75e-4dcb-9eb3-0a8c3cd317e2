package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.config;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomBaseCalcConfigBO {

    /**
     * 指标-自定义基期计算规则映射
     */
    private Map<String/*customBaseCalcRuleCode*/, CustomBaseCalcItemConfigBO> customBaseCalcItemConfigMap;
}
