package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.longtask;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.activityAllSceneKimRobotConfig;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.model.enums.PolicyDrawType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.model.enums.SiteTagEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerExportConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SlrBelongInfoCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportOutSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.LongTaskConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.KimReportUtil;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics.ExportActivityReviewDataRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.kim.model.ManageConfig;
import com.kuaishou.kwaishop.merchant.operation.longtask.client.execute.handler.BaseHandler;
import com.kuaishou.kwaishop.merchant.operation.longtask.client.execute.handler.ExecuteContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-07
 */
@Slf4j
@Component
public class ExportSellerDetailHandler implements BaseHandler {

    @Autowired
    private StatisticsReviewService statisticsReviewService;

    @Override
    public String bizCode() {
        return ExportFileBizTypeEnum.STATISTICS_DETAIL.getLongTaskBizCode();
    }

    @Override
    public Integer type() {
        return LongTaskConstants.BASE_TYPE;
    }

    @Override
    public void execute(ExecuteContext executeContext) {
        String bizExt = executeContext.getBizExt();
        long scheduleId = executeContext.getScheduleId();
        ExportActivityReviewDataRequest reviewDataRequest = fromJSON(bizExt, ExportActivityReviewDataRequest.class);
        long activityId = reviewDataRequest.getActivityId();
        String operator = reviewDataRequest.getOperator();
        List<String> selectHeads = reviewDataRequest.getTableHeadList();
        SellerExportConditionBO sellerExportCondition = buildExportCondition(reviewDataRequest);
        // 构造
        statisticsReviewService.exportActivitySellerReviewDetail(activityId, scheduleId, selectHeads, operator,
                sellerExportCondition);
    }

    public static SellerExportConditionBO buildExportCondition(ExportActivityReviewDataRequest request) {
        String belongStaff = request.getBelongStaff();
        List<String> firstIndustryCodeList = request.getFirstIndustryCodeList();
        List<String> secondIndustryCodeList = request.getSecondIndustryCodeList();
        List<String> thirdIndustryCodeList = request.getThirdIndustryCodeList();
        long userId = request.getUserId();
        int drawType = request.getDrawType();
        String siteCode = request.getSiteCode();
        SiteTagEnum siteTagEnum = SiteTagEnum.getByName(siteCode);
        SlrBelongInfoCondition slrBelongInfoCondition = new SlrBelongInfoCondition(belongStaff, userId,
                firstIndustryCodeList, secondIndustryCodeList, thirdIndustryCodeList,
                PolicyDrawType.getByCode(drawType), siteTagEnum);
        ExportOutSourceEnum exportOutSource = StringUtils.isBlank(request.getExportSource()) ? null
                : ExportOutSourceEnum.valueOf(request.getExportSource());
        return new SellerExportConditionBO(slrBelongInfoCondition, exportOutSource);
    }

    @Override
    public void handleException(ExecuteContext executeContext) {
        String bizExt = executeContext.getBizExt();
        ExportActivityReviewDataRequest reviewDataRequest = fromJSON(bizExt, ExportActivityReviewDataRequest.class);
        // kim通知
        String scene = "exportSellerDetailFail";
        ManageConfig manageConfig = activityAllSceneKimRobotConfig.getMap(String.class, ManageConfig.class).get(scene);
        String msg = String.format(manageConfig.getKimContent(), reviewDataRequest.getActivityId(),
                reviewDataRequest.getOperator());
        // 发送通知
        KimReportUtil.kimNoticeMsg(scene, msg, manageConfig.getMentionList());
    }
}
