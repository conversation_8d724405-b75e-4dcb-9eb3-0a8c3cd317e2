package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfSubtag;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
public enum NotificationPerfEnum implements PerfSubtag {
    // consumer
    USER_AWARD_STATUS_CHANGE_NOTIFICATION_PUSH_CONSUMER("UserAwardStatusChangeNotificationConsumer", "用户奖励状态变更推送消息"),
    USER_TASK_STATUS_CHANGE_NOTIFICATION_PUSH_CONSUMER("UserTaskStatusChangeNotificationPushConsumer", "用户任务状态变更推送消息"),
    USER_ACTIVITY_STATUS_CHANGE_NOTIFICATION_PUSH_CONSUMER("UserActivityStatusChangeNotificationPushConsumer",
            "用户活动状态变更推送消息"),
    USER_ACTIVITY_REGISTER_STATUS_NOTIFICATION_CONSUMER("UserActivityRegisterStatusNotificationConsumer", "用户活动报名推送消息"),
    USER_AUDIT_STATUS_CHANGE_NOTIFICATION_PUSH_CONSUMER("UserAuditStatusChangeNotificationConsumer", "用户审核状态变更推送消息"),
    USER_STRATEGY_STATUS_CHANGE_NOTIFICATION_PUSH_CONSUMER("UserStrategyStatusChangeNotificationConsumer",
            "用户策略状态变更推送消息"),
    DELAY_NOTIFICATION_PUSH_CONSUMER("DelayNotificationPushConsumer", "延迟推送消息处理"),
    FINISH_NOTIFICATION_PUSH_CONSUMER("FinishNotificationPushConsumer", "推送完成消息处理"),
    // consumer admin
    NOTIFICATION_CREATE_CONSUMER("NotificationCreateConsumer", "触达创建消息处理"),

    // rpc
    CREATE_NOTIFICATION_CONFIG("CreateNotificationConfig", "主动创建推送配置"),
    TRIGGER_NOTIFICATION_CONFIG("TriggerNotificationConfig", "触发推送配置"),
    UPDATE_NOTIFICATION_CONFIG("UpdateNotificationConfig", "更新推送配置"),
    INVALID_NOTIFICATION_CONFIG_BY_ID("InvalidNotification", "失效推送配置"),
    VALID_NOTIFICATION_CONFIG_BY_ID("ValidNotification", "生效推送配置"),
    INVALID_NOTIFICATION_CONFIG_BY_CONDITION("InvalidNotificationByCondition", "根据条件失效推送配置"),
    VALID_NOTIFICATION_CONFIG_BY_CONDITION("ValidNotificationByCondition", "根据条件生效推送配置"),
    GET_ACTIVITY_NOTIFICATION_CONFIG("GetActivityNoVtificationConfig", "获取活动推送配置"),
    GET_NOTIFICATIONS_CONFIG_BY_CONDITIONS("getNotificationConfigsByConditions", "获取推送配置"),
    HARD_DELETE_NOTIFICATION_CONFIG("hardDeleteNotificationConfig", "硬删除推送配置"),
    DEL_NOTIFICATION_LOCK("delNotificationLock", "解锁"),
    BATCH_UPDATE_NOTIFICATION_TEMPLATE_AND_PERIOD_CONFIG("batchUpdateNotificationTemplateAndPeriodConfig",
            "批量修改推送模版和周期性配置"),
    DEL_USER_ACTIVITY_PUSH_LOCK("delUserActivityPushLock", "解除用户活动下所有推送锁"),
    QUERY_USER_NOTIFICATION_INFO("queryUserNotificationInfo", "查询用户触达信息"),

    // service
    NOTIFICATION_PUSH_CREATE_SERVICE("NotificationPushCreateService", "用户消息推送创建"),
    NOTIFICATION_PUSH_BASIC_SERVICE("NotificationPushBasicService", "消息基础服务"),
    ACTIVITY_REGISTRATION_NOTIFICATION_SERVICE("ActivityRegistrationNotificationService", "活动报名领取提醒服务"),

    NOTIFICATION_CONFIG_ERROR("NotificationConfigError", "触达配置异常"),

    USER_NOTIFICATION_INFO_QUERY("userNotificationInfoQuery", "用户触达信息查询"),
    NOTIFICATION_CHANGE_PROCESS("notificationChangeProcess", "用户触达配置变更回刷处理"),
    NOTIFICATION_CHANGE_ACTIVITY_PROCESS("notificationChangeActivityProcess", "触达配置变更活动实体处理"),
    NOTIFICATION_CHANGE_TASK_PROCESS("notificationChangeTaskProcess", "触达配置变更任务实体处理"),


    // fetch
    FETCH_MERCHANT_MESSAGE_CENTER("MerchantMessageCenterFetchService", "商家通知中心"),
    FETCH_FLOW_CENTER_MESSAGE("flowCenterMessagePush", "主站流量平台推送"),
    FETCH_CS_SHOP_GROUP("csShopGroup", "店铺群消息"),
    FETCH_INVESTMENT_ACTIVITY_SERVICE("fetch.investmentActivity", "招商活动报名"),
    FETCH_INVESTMENT_ACTIVITY_LIST("fetch.investment.activity.list", "拉取招商活动报名商家"),
    FETCH_INVESTMENT_ACTIVITY_INFO("fetch.investment.activity.info", "获取招商活动信息"),
    FETCH_SERVICE_MARKET("fetch.service.market", "服务市场"),
    FETCH_IM_PRIVATE_MESSAGE_SEND("fetch.im.private.message.send", "私信消息推送"),
    FETCH_SYSTEM_DELIVERY_MESSAGE_PUSH("fetch.system.delivery.message.push", "智子弹窗消息推送"),
    DAO_CHECK("task.dao.check", "dao层加固"),

    ;

    private final String scene;
    private final String desc;

    NotificationPerfEnum(String scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }

    @Override
    public String getScene() {
        return scene;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
