package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.AgainRegistrationInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateTaskByTemplateCodeRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateTaskOfActivityWithVariableRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.QueryUserTaskOperatorLogRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.UserDataStatisticDetailResponse;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.WhiteListRegistrationDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.WhiteListRegistrationRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-07
 */
public interface ManageForAdminBizService {

    /**
     * 按照模板创建任务
     */
    void createTaskByTemplateTag(CreateTaskByTemplateCodeRequest request);

    /**
     * 按照模板创建活动下的任务
     */
    void createTaskOfActivityWithVariable(CreateTaskOfActivityWithVariableRequest request);

    /**
     * 基于本地数据重新报名活动
     */
    List<AgainRegistrationInfoDTO> againRegistrationByLocalBasicsData(String userIdStr);

    /**
     * 用户数据详细统计
     */
    UserDataStatisticDetailResponse userDataStatisticDetail(long userId, long activityId, long indicatorId,
            int awardType);

    /**
     * 获取商家任务路径日志
     */
    List<String> getUserTaskOperatorLog(QueryUserTaskOperatorLogRequest request);

    /**
     * 运营加白报名
     */
    List<WhiteListRegistrationDTO> whiteListRegistration(WhiteListRegistrationRequest request);
}