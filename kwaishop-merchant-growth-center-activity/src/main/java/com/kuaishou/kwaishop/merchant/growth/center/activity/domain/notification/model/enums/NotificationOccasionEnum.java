package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
public enum NotificationOccasionEnum {
    UNKNOWN_PUSH_OCCASION(0, "未知"),
    BEFORE(1, "前"),
    IMMEDIATE(2, "立即推送"),
    AFTER(3, "延后推送"),
    ;

    private int val;
    private String desc;

    NotificationOccasionEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static NotificationOccasionEnum of(int type) {
        for (NotificationOccasionEnum notificationOccasionEnum : NotificationOccasionEnum.values()) {
            if (notificationOccasionEnum.getVal() == type) {
                return notificationOccasionEnum;
            }
        }
        return UNKNOWN_PUSH_OCCASION;
    }

    public static List<Integer> getAllValidNotificationPushOccasionValues() {
        return Arrays.stream(NotificationOccasionEnum.values())
                .map(NotificationOccasionEnum::getVal)
                .filter(val -> val > 0)
                .collect(Collectors.toList());
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
