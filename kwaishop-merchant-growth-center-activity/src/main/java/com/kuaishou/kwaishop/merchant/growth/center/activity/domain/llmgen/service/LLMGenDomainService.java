package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMCommonExecuteParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMCommonExecuteResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncProcessParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncResultUpdateParamBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-21
 */
public interface LLMGenDomainService {

    /**
     * 执行多模态Agent
     */
    LLMCommonExecuteResultBO execute(LLMCommonExecuteParamBO param);

    /**
     * 异步生成llm物料
     */
    void processGenAsync(LLMContentGenAsyncProcessParamBO param);

    /**
     * 设置llm物料生成结果
     */
    Boolean setGenAsyncResult(LLMContentGenAsyncResultUpdateParamBO param);
}
