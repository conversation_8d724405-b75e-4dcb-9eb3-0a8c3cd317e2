package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributorTypeValidBatchExecuteEvent extends BatchExecuteEvent {
    /**
     * 人群配置
     */
    private CrowdConfigBO crowdConfig;
}
