package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByIdExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByIdMaxCacheSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByIdRefreshTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByUniqIdxExpireTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByUniqIdxMaxCacheSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationByUniqIdxRefreshTime;

import java.time.Duration;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.kuaishou.framework.concurrent.AsyncReloadCacheLoader;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.GetNotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushConfigCacheService;

import lombok.extern.slf4j.Slf4j;

/**
 * 推送配置缓存service
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
@Lazy
@Slf4j
@Service
public class NotificationPushConfigCacheServiceImpl implements NotificationPushConfigCacheService {

    @Autowired
    private NotificationPushBasicService notificationPushBasicService;

    private final LoadingCache<GetNotificationPushConfigBO, List<NotificationPushConfigBO>>
            notificationConfigByIdxLoadingCache = KsCacheBuilder.newBuilder()
            .maximumSize(notificationByUniqIdxMaxCacheSize.get())
            .refreshAfterWrite(() -> Duration.ofSeconds(notificationByUniqIdxRefreshTime.get()))
            .expireAfterAccess(() -> Duration.ofSeconds(notificationByUniqIdxExpireTime.get()))
            .concurrencyLevel(5)
            .enablePerf("notification.task.local.cache.notificationConfigLoadingCacheByUniqIdx")
            .build(new AsyncReloadCacheLoader<GetNotificationPushConfigBO, List<NotificationPushConfigBO>>() {
                @Override
                public List<NotificationPushConfigBO> load(GetNotificationPushConfigBO getConfigBO) {
                    return notificationPushBasicService.getNotificationPushConfig(getConfigBO.getEntityId(),
                            getConfigBO.getEntityType(), getConfigBO.getEntityStatus(), true);
                }
            });

    private final LoadingCache<Long, NotificationPushConfigBO> notificationConfigByIdLoadingCache =
            KsCacheBuilder.newBuilder()
                    .maximumSize(notificationByIdMaxCacheSize.get())
                    .refreshAfterWrite(() -> Duration.ofSeconds(notificationByIdRefreshTime.get()))
                    .expireAfterAccess(() -> Duration.ofSeconds(notificationByIdExpireTime.get()))
                    .concurrencyLevel(5)
                    .enablePerf("notification.task.local.cache.notificationConfigLoadingCacheByPrimaryId")
                    .build(new AsyncReloadCacheLoader<Long, NotificationPushConfigBO>() {
                        @Override
                        public NotificationPushConfigBO load(Long configId) throws Exception {
                            return notificationPushBasicService.getNotificationPushConfigById(configId, false);
                        }
                    });

    /**
     * 走索引查询符合条件的推送配置
     */
    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByIdxLoadingCache(
            GetNotificationPushConfigBO getNotificationPushConfigBO) {
        try {
            return notificationConfigByIdxLoadingCache.getUnchecked(getNotificationPushConfigBO);
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

    /**
     * 走主键查询推送配置
     */
    @Override
    public NotificationPushConfigBO getNotificationPushConfigByIdLoadingCache(long id) {
        try {
            return notificationConfigByIdLoadingCache.getUnchecked(id);
        } catch (Exception e) {
            return null;
        }
    }
}
