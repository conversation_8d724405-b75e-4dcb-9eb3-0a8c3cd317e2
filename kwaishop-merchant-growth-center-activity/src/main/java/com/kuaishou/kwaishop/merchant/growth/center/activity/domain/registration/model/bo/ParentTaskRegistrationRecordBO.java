package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParentTaskRegistrationRecordBO {

    /**
     * 父任务Task
     */
    private TaskDO parentTask;

    /**
     * 基础JsonData数据
     */
    private Map<String, Object> jsonData;
}
