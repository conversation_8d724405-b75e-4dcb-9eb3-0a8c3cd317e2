package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.BatchCreateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.BatchUpdateNotificationTemplateAndPeriodConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.CreateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.DelUserActivityPushLockRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigDetailByConfigIdDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigDetailByConfigIdRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigsByConditionsDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.GetNotificationConfigsByConditionsRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.HardDeleteNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.InvalidNotificationByConditionRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.InvalidNotificationByConfigIdRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.QueryUserNotificationInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.TriggerNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UpdateNotificationConfigRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UserNotificationShowConfig;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.ValidNotificationByConditionRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.ValidNotificationByConfigIdRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-11
 */
public interface NotificationPushBizService {
    boolean createNotificationPushConfig(CreateNotificationConfigRequest request);

    boolean batchCreateNotificationPushConfig(BatchCreateNotificationConfigRequest request);

    void triggerNotificationConfig(TriggerNotificationConfigRequest request);

    boolean updateNotificationConfig(UpdateNotificationConfigRequest request);

    boolean invalidNotificationConfigById(InvalidNotificationByConfigIdRequest request);

    boolean validNotificationConfigById(ValidNotificationByConfigIdRequest request);

    boolean invalidNotificationConfigByCondition(InvalidNotificationByConditionRequest request);

    boolean validNotificationConfigByCondition(ValidNotificationByConditionRequest request);

    GetNotificationConfigDetailByConfigIdDTO getNotificationConfigDetailById(
            GetNotificationConfigDetailByConfigIdRequest request);

    GetNotificationConfigsByConditionsDTO getNotificationConfigsByConditions(GetNotificationConfigsByConditionsRequest request);

    boolean hardDeleteNotificationConfigs(HardDeleteNotificationConfigRequest request);

    boolean updateNotificationTemplateAndPeriodConfig(BatchUpdateNotificationTemplateAndPeriodConfigRequest request);

    void delUserActivityNotificationPushLock(DelUserActivityPushLockRequest request);

    UserNotificationShowConfig queryUserNotificationInfo(QueryUserNotificationInfoRequest request);
}
