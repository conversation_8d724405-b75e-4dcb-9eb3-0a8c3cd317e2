package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.notificationCode;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.needAuditChannelList;
import static com.kuaishou.kwaishop.platform.common.utils.JsonUtil.toJson;
import static java.util.Collections.singletonList;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Sets;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.custom.NotificationAdminDomainBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.MerchantCenterMessageTemplateConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationConfigPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationCodeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-21
 */
@Lazy
@Slf4j
@Service
public class ActivityProcessingStrategyService extends AbstractNotificationCodeStrategyService {
    @Override
    public Set<NotificationCodeEnum> getNotificationCodeSet() {
        return Sets.newHashSet(NotificationCodeEnum.ACTIVITY_PROCESSING);
    }

    @Override
    public List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList,
            List<AwardConfigDO> awardConfigList, NotificationConfigBO notificationConfig) {
        NotificationAdminDomainBO notificationAdminDomainBO = getNotificationAdminDomainBO(activity, notificationConfig);
        //构建templateConfig
        String templateConfig = notificationAdminDomainBO.getTemplateConfig();
        if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
            MerchantCenterMessageTemplateConfigBO templateConfigBO
                    = ObjectMapperUtils.fromJSON(templateConfig, MerchantCenterMessageTemplateConfigBO.class);
            if (StringUtils.isNotEmpty(notificationConfig.getTemplateCode())) {
                templateConfigBO.setCode(notificationConfig.getTemplateCode());
            }
            templateConfig = toJson(templateConfigBO);
        }

        int status;
        if (needAuditChannelList.get().contains(notificationConfig.getChannel())) {
            status = NotificationStatusEnum.NEED_AUDIT.getVal();
        } else {
            status = NotificationStatusEnum.VALID.getVal();
        }

        int channel = notificationConfig.getChannel();
        NotificationChannelEnum channelEnum = NotificationChannelEnum.of(channel);
        PeriodConfigBO periodConfigBO;
        switch (channelEnum) {
            case MERCHANT_HWLM_ITEM_DECISIONINFO_PAGE:
                periodConfigBO = null;
                break;
            case WORK_PLATFORM_POPUP:
                periodConfigBO = fromJSON(notificationAdminDomainBO.getPeriodConfig(), PeriodConfigBO.class);
                if (Objects.isNull(periodConfigBO)) {
                    periodConfigBO = buildPeriodConfig(activity.getStartTime(), activity.getEndTime(),
                            notificationConfig.getIntervalDay());
                }
                break;
            default:
                return null;
        }
        NotificationPushConfigBO notificationPushConfigBO = NotificationPushConfigBO.builder()
                .activityId(activity.getId())
                .entityId(activity.getId())
                .entityType(notificationAdminDomainBO.getEntityType())
                .entityStatus(notificationAdminDomainBO.getEntityStatus())
                .channel(notificationConfig.getChannel())
                .templateConfig(templateConfig)
                .occasion(notificationAdminDomainBO.getOccasion())
                .creator(SymbolConstants.DEFAULT_USER_SYMBOL)
                .modifier(SymbolConstants.DEFAULT_USER_SYMBOL)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .status(status)
                .periodConfig(periodConfigBO)
                .scene(notificationConfig.getNotificationCode())
                .build();

        return Collections.singletonList(notificationPushConfigBO);
    }

    private PeriodConfigBO buildPeriodConfig(long activityStartTime, long activityEndTime,
            long intervalDay) {
        if (intervalDay <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "间隔天数不能小于1");
        }
        if (intervalDay > DateUtils.getDayBetween(activityStartTime, activityEndTime)) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "间隔天数大于活动持续时间，不能创建任务进行触达配置");
        }
        //获取当前时间
        long now = System.currentTimeMillis();
        if (now > activityEndTime) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "当前活动已结束，无法创建任务进行触达配置");
        }
        // 计算开始时间和结束时间的差值
        long drawGap = DateUtils.getDayBetween(activityStartTime, activityEndTime);

        // 计算push延迟时间
        List<Long> dayList = DateUtils.calculateDayList(1, drawGap + 1, intervalDay);

        String day = StringUtils.join(dayList, SymbolConstants.COMMA);

        NotificationPeriodBO notificationPeriod = NotificationPeriodBO.builder()
                .day(day)
                .hour("10")
                .minute("0")
                .periodType(NotificationConfigPeriodTypeEnum.ABSOLUTE.getVal())
                .intervalDay(intervalDay)
                .build();

        return PeriodConfigBO.builder()
                .notificationPeriods(singletonList(notificationPeriod))
                .build();
    }
}
