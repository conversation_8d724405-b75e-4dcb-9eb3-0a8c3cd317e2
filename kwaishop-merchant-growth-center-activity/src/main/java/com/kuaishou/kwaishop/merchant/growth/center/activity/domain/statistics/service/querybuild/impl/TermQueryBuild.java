package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.END_SIGN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.START_SIGN;
import static org.apache.commons.lang3.StringUtils.EMPTY;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildHandler;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-26
 */
@Slf4j
@Lazy
@Service
public class TermQueryBuild implements QueryBuildHandler {
    @Override
    public QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam) {
        // 定值查询
        if (StringUtils.isBlank(condition.getFieldName())) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "term查询条件字段不能为空");
        }
        ConditionValueTypeEnum valueTypeEnum = ConditionValueTypeEnum.of(condition.getConditionValueType());
        if (valueTypeEnum.equals(ConditionValueTypeEnum.UNKNOWN)) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "无法识别term查询条件值类型");
        }
        String conditionValue = condition.getConditionValue();
        // 通配符替换
        if (conditionValue.startsWith(START_SIGN) && conditionValue.endsWith(END_SIGN)) {
            String key = conditionValue.replace(START_SIGN, EMPTY).replace(END_SIGN, EMPTY);
            if (!entityParam.containsKey(key)) {
                throw new BizException(BasicErrorCode.SERVER_ERROR, "无法识别term查询条件值通配符-" + key);
            }
            return QueryBuilders.termQuery(condition.getFieldName(), entityParam.get(key));
        } else {
            if (valueTypeEnum.equals(ConditionValueTypeEnum.NUMBER)) {
                return QueryBuilders.termQuery(condition.getFieldName(), Long.parseLong(conditionValue));
            }
            return QueryBuilders.termQuery(condition.getFieldName(), conditionValue);
        }
    }

    @Override
    public ConditionTypeEnum conditionType() {
        return ConditionTypeEnum.TERM;
    }
}
