package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-22
 */
@AllArgsConstructor
public enum UserUnableDrawTaskReasonEnum {
    OFFSET_EVENT_NOT_HAPPEN("OFFSET_EVENT_NOT_HAPPEN", "偏移事件未发生"),

    TASK_PERIOD_TIME_INVALID("TASK_PERIOD_TIME_INVALID", "任务时间无效"),

    BASIC_TIME_INVALID("BASIC_TIME_INVALID", "基期时间无效"),

    BASIC_DATA_NOT_FILLED("BASIC_DATA_NOT_FILLED", "基期数据未填写"),
    ;

    private String code;

    private String desc;
}
