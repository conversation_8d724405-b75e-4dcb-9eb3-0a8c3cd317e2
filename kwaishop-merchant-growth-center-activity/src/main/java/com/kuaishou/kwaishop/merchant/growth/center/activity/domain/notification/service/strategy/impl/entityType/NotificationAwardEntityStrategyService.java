package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.entityType;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationEntityService;

import lombok.extern.slf4j.Slf4j;

/**
 * 奖励维度推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Slf4j
@Service
public class NotificationAwardEntityStrategyService extends AbstractNotificationEntityService {

    @Override
    public NotificationEntityTypeEnum getNotificationStrategyEntityType() {
        return NotificationEntityTypeEnum.AWARD;
    }

    @Override
    public boolean checkEntityStatus(long userId, NotificationPushConfigBO configBO) {
        // 目前奖励只立即触发，不会校验状态
        return false;
    }

    @Override
    public boolean checkEndTime(long userId, NotificationPushConfigBO configBO) {
        // 目前奖励只立即触发，不会校验状态
        return false;
    }
}
