package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Getter
@AllArgsConstructor
public enum BaseDataSourceFetchTypeEnum {

    DEFAULT(1, "默认策略"),
    PRE_SEGMENT(2, "前置拆分查询策略"),
    ;

    private final int type;

    private final String desc;

    public static BaseDataSourceFetchTypeEnum getByType(int type) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(DEFAULT);
    }
}
