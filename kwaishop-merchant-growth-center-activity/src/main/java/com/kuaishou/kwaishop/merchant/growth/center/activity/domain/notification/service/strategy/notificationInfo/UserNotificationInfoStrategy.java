package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.notification.UserNotificationShowConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
public interface UserNotificationInfoStrategy {

    /**
     * 查询用户触达信息
     */
    UserNotificationShowConfig query(long userId, String param);

    /**
     * 获取通知信息查询来源
     */
    UserNotificationSourceTypeEnum getSource();
}
