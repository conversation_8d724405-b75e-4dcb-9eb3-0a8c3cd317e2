package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-08
 */
@AllArgsConstructor
@Getter
public enum PlanStatusEnum {
    UNKNOWN(0, "未知"),

    WAIT_START(10, "待开始"),

    PROCESSING(20, "进行中"),

    END(30, "已结束"),
    ;

    public static PlanStatusEnum of(int status) {
        for (PlanStatusEnum val : PlanStatusEnum.values()) {
            if (val.getStatus() == status) {
                return val;
            }
        }
        return UNKNOWN;
    }

    private final int status;

    private final String desc;
}
