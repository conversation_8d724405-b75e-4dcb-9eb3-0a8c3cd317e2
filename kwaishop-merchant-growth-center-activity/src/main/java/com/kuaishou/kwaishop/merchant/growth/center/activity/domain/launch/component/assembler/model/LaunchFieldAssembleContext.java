package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchFieldAssembleContext {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 投放配置
     */
    private LaunchConfigBO launchConfig;

    /**
     * 上下文Map
     */
    private Map<String, Object> contextMap;
}
