package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.OffsetEventDataSourceTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
public interface OffsetEventHappenTimeFetchService<T> {
    /**
     * 取数方式
     */
    OffsetEventDataSourceTypeEnum getDataSourceType();

    /**
     * 获取偏移事件发生时间
     */
    Long fetchOffsetEventHappenTime(OffsetEventConfigBO<T> offsetEventConfigBO, Long userId);
}
