package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service.processor;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.trAmountSpecialIndicatorList;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.app.consumer.hivemsg.HiveSyncDoneMsgInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.domain.HiveSyncDone;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service.AbstractHiveSyncDoneMsgProcessor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.biz.IndicatorCustomizeCalcProcessService;

import lombok.extern.slf4j.Slf4j;


/**
 * hive->kafka回流结束后消息通知:记录处理日期
 *
 * <AUTHOR>
 */
@HiveSyncDone(bizCode = "tr_amount_order_record_sync_finish")
@Slf4j
public class TrAmountOrderRecordSyncFinishNotifyProcessor extends AbstractHiveSyncDoneMsgProcessor {

    @Autowired
    private IndicatorCustomizeCalcProcessService indicatorCustomizeCalcProcessService;

    @Override
    protected void doProcess(HiveSyncDoneMsgInfo msgInfo) {
        log.info("接收指标计算订单表数据回流结束消息通知 TrAmountOrderRecordSyncFinishNotifyProcessor msgInfo:{}",
                ObjectMapperUtils.toJSON(msgInfo));
        String pDate = msgInfo.getPDate();
        List<Long> specialIndicatorList = trAmountSpecialIndicatorList.get();
        for (Long indicatorId : specialIndicatorList) {
            indicatorCustomizeCalcProcessService.recordCustomizeIndicatorSyncDate(indicatorId, pDate);
        }
    }
}
