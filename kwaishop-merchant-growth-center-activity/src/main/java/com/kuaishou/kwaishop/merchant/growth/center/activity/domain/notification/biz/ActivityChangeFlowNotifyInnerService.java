package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.biz;

import java.util.Map;
import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ActivityFlowChangeNotifyConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.ActivityFlowChangeSendNotifyMsg;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-11
 */
public interface ActivityChangeFlowNotifyInnerService {

    /**
     * 发奖单风控送审通知消息发送
     *
     * @param auditKey
     * @param auditKey
     */
    void awardAuditRiskNotifyMsgSend(String auditKey);


    /**
     * 发奖单风控送审超时通知消息发送
     *
     * @param activityNotityEntityIdMap
     */
    void awardAuditRiskTimeoutNotifyMsgSend(Map<Long, Set<Long>> activityNotityEntityIdMap);

    /**
     * 发奖单风控送审通知消息消费
     *
     * @param recordSendNotifyMsg
     */
    void awardAuditRiskNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg, ActivityDO activityDO) throws Exception;

    /**
     * 发奖单风控送审通知消息消费
     *
     * @param recordSendNotifyMsg
     */
    void awardAuditRiskTimeoutNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg) throws Exception;

    /**
     * 活动点击上线通知
     *
     * @param activityId
     */
    void onlineActivityOperateNotifyMsgSend(Long activityId);

    /**
     * @param recordSendNotifyMsg
     * @param activityDO
     * @throws Exception
     */
    void onlineActivityOperateNotifyMsgConsume(ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                                               ActivityDO activityDO) throws Exception;

    ActivityDO checkActivity(long activityId, ActivityFlowChangeSendNotifyMsg recordSendNotifyMsg,
                             ActivityFlowChangeNotifyConfig activityNotifyConfig);


}
