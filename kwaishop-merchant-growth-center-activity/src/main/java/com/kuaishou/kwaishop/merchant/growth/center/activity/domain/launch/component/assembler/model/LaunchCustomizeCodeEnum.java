package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-12
 */
@Getter
@AllArgsConstructor
public enum LaunchCustomizeCodeEnum {

    UNKNOWN("unknown", "未知"),
    ACTIVITY_COMPLETE_PROGRESS("activityCompleteProgress", "活动完成进度"),
    ACTIVITY_AWARD_AGG_INFO("activityAwardAggInfo", "活动奖励聚合信息"),
    ACTIVITY_TIME_RANGE("activityTimeRange", "活动时间范围"),
    ACTIVITY_DRAW_NUM("activityDrawNum", "活动领取人数"),
    ;

    private final String code;

    private final String desc;

    public static LaunchCustomizeCodeEnum getByCode(String code) {
        return Arrays.stream(values()).filter(e -> StringUtils.equals(code, e.getCode())).findFirst().orElse(UNKNOWN);
    }
}
