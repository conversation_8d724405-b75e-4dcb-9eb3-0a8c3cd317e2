package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJson;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_AVG_DATA;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.RESOURCE;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.id.sequence.IdSequenceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RegistrationDrawOptionField;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RegistrationDrawRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RegistrationDrawRuleField;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationCacheBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationOptionFieldEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityIdBizType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.ValidationUtils;

/**
 * 报名域转换器
 *
 * <AUTHOR>
 */
@Component
public class RegistrationConverterImpl implements RegistrationConverter {

    @Autowired
    private IdSequenceService idSequenceService;

    @Override
    public UserRegistrationRecordDO convertToUserRecordDO(UserRegistrationRecordBO userRegistrationRecordBO) {
        if (userRegistrationRecordBO == null) {
            return null;
        }
        UserRegistrationRecordDO userRegistrationRecordDO = new UserRegistrationRecordDO();
        EntityTypeEnum entityType = userRegistrationRecordBO.getEntityType();
        BeanUtils.copyProperties(userRegistrationRecordBO, userRegistrationRecordDO);
        userRegistrationRecordDO.setEntityType(entityType == null ? null : entityType.getCode());
        userRegistrationRecordDO.setModifier(userRegistrationRecordBO.getOperator());
        userRegistrationRecordDO.setCreator(userRegistrationRecordBO.getId() == null
                                            ? userRegistrationRecordBO.getOperator() : null);
        UserRegistrationRecordExtBO ext = new UserRegistrationRecordExtBO();
        if (StringUtils.isNotBlank(userRegistrationRecordBO.getExt())) {
            ext = fromJSON(userRegistrationRecordBO.getExt(), UserRegistrationRecordExtBO.class);
            userRegistrationRecordDO.setExt(toJSON(ext));
        }
        // 兼容逻辑，有些是直接在上层传了source
        if (StringUtils.isNotBlank(userRegistrationRecordBO.getSource())) {
            ext.setSource(userRegistrationRecordBO.getSource());
            userRegistrationRecordDO.setExt(toJSON(ext));
        }
        return userRegistrationRecordDO;
    }

    @Override
    public List<UserRegistrationRecordDO> batchConvertToUserRecordDO(
            Collection<UserRegistrationRecordBO> userRegistrationRecords) {
        if (CollectionUtils.isEmpty(userRegistrationRecords)) {
            return Lists.newArrayList();
        }
        List<UserRegistrationRecordDO> userRegistrationRecordDOS = Lists.newArrayList();
        for (UserRegistrationRecordBO userRegistrationRecord : userRegistrationRecords) {
            // 检查合法性，非法会抛出异常
            ValidationUtils.validate(userRegistrationRecord);
            UserRegistrationRecordDO userRegistrationRecordDO = convertToUserRecordDO(userRegistrationRecord);
            // 生成主键ID
            if (userRegistrationRecordDO.getId() == null || userRegistrationRecordDO.getId() <= 0) {
                long id = idSequenceService.getId(ActivityIdBizType.USER_REGISTRATION_RECORD_ID_TYPE);
                userRegistrationRecordDO.setId(id);
            }
            if (userRegistrationRecordDO.getCreateTime() == null) {
                userRegistrationRecordDO.setCreateTime(System.currentTimeMillis());
            }
            if (userRegistrationRecordDO.getRegistrationTime() == null) {
                userRegistrationRecordDO.setRegistrationTime(0L);
            }
            userRegistrationRecordDO.setUpdateTime(System.currentTimeMillis());
            userRegistrationRecordDOS.add(userRegistrationRecordDO);
        }
        return userRegistrationRecordDOS;
    }

    @Override
    public UserRegistrationRecordBO convertToUserRecordBO(UserRegistrationRecordDO userRegistrationRecordDO) {
        if (userRegistrationRecordDO == null) {
            return null;
        }
        UserRegistrationRecordBO userRegistrationRecordBO = new UserRegistrationRecordBO();
        BeanUtils.copyProperties(userRegistrationRecordDO, userRegistrationRecordBO);
        userRegistrationRecordBO.setEntityType(EntityTypeEnum.getByCode(userRegistrationRecordDO.getEntityType()));
        userRegistrationRecordBO.setOperator(userRegistrationRecordDO.getModifier());
        if (StringUtils.isNotBlank(userRegistrationRecordDO.getExt())) {
            UserRegistrationRecordExtBO ext = fromJSON(userRegistrationRecordDO.getExt(),
                    UserRegistrationRecordExtBO.class);
            userRegistrationRecordBO.setSource(ext.getSource());
        }
        return userRegistrationRecordBO;
    }

    @Override
    public AddRegistrationOptionBO convertToUserRegistrationOption(UserRegistrationRecordDO userRegistrationRecordDO) {
        if (userRegistrationRecordDO == null) {
            return new AddRegistrationOptionBO();
        }
        if (StringUtils.isNotBlank(userRegistrationRecordDO.getExt())) {
            UserRegistrationRecordExtBO ext = fromJSON(userRegistrationRecordDO.getExt(),
                    UserRegistrationRecordExtBO.class);
            if (ext != null && ext.getOption() != null) {
                return ext.getOption();
            }
        }
        return new AddRegistrationOptionBO();
    }

    @Override
    public UserRegistrationRecordBO buildDefaultRecordBO(long userId, long activityId, EntityTypeEnum entityType,
            long entityId, String operator, String jsonData) {
        UserRegistrationRecordBO activityRecordBO = new UserRegistrationRecordBO();
        activityRecordBO.setUserId(userId);
        activityRecordBO.setActivityId(activityId);
        activityRecordBO.setEntityType(entityType);
        activityRecordBO.setEntityId(entityId);
        activityRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        activityRecordBO.setRegistrationTime(System.currentTimeMillis());
        activityRecordBO.setOperator(operator);
        activityRecordBO.setSource(RESOURCE);
        activityRecordBO.setJsonData(jsonData);
        return activityRecordBO;
    }

    @Override
    public Map<String, Object> resolveBasicDataFromJsonData(UserRegistrationRecordDO registrationRecordDO) {
        if (registrationRecordDO == null || StringUtils.isBlank(registrationRecordDO.getJsonData())) {
            return Maps.newHashMap();
        }
        return fromJson(registrationRecordDO.getJsonData());
    }

    @Override
    public AddRegistrationOptionBO resolveCrowdOption(String drawRule) {
        AddRegistrationOptionBO option = AddRegistrationOptionBO.builder().build();
        if (StringUtils.isEmpty(drawRule)) {
            return option;
        }
        RegistrationDrawRule drawRuleModel = fromJSON(drawRule, RegistrationDrawRule.class);
        if (CollectionUtils.isEmpty(drawRuleModel.getOptionFields())) {
            return option;
        }
        for (RegistrationDrawOptionField field : drawRuleModel.getOptionFields()) {
            if (RegistrationOptionFieldEnum.overrideStartTime.getValue().equals(field.getField())) {
                option.setOverrideStartTime(Long.valueOf(field.getValue()));
            }
            if (RegistrationOptionFieldEnum.preSubActivityId.getValue().equals(field.getField())) {
                option.setPreSubActivityId(Collections.singletonList(Long.valueOf(field.getValue())));
            }
        }
        return option;
    }

    @Override
    public List<String> resolveDrawRuleFieldCondition(String drawRule, String field) {
        if (StringUtils.isEmpty(drawRule) || StringUtils.isEmpty(field)) {
            return Lists.newArrayList();
        }
        RegistrationDrawRule drawRuleBO = fromJSON(drawRule, RegistrationDrawRule.class);
        if (drawRuleBO == null || CollectionUtils.isEmpty(drawRuleBO.getFields())) {
            return Lists.newArrayList();
        }
        RegistrationDrawRuleField condition =
                drawRuleBO.getFields().stream().filter(a -> field.equals(a.getField())).findFirst().orElse(null);
        if (condition == null) {
            return Lists.newArrayList();
        }
        return condition.getConditions();
    }

    @Override
    public AddRegistrationOptionBO resolveRegistrationOptionByExt(String ext) {
        if (StringUtils.isEmpty(ext)) {
            return new AddRegistrationOptionBO();
        }
        UserRegistrationRecordExtBO extModel = fromJSON(ext, UserRegistrationRecordExtBO.class);
        if (extModel == null || extModel.getOption() == null) {
            return new AddRegistrationOptionBO();
        }
        return extModel.getOption();
    }

    @Override
    public UserRegistrationCacheBO convertToCacheBO(UserRegistrationRecordDO record) {
        return UserRegistrationCacheBO.builder()
                .userId(record.getUserId())
                .activityId(record.getActivityId())
                .entityId(record.getEntityId())
                .entityType(record.getEntityType())
                .registrationTime(record.getRegistrationTime())
                .jsonData(record.getJsonData())
                .build();
    }

    @Override
    public boolean checkOffsetEventRegistrationRecord(UserRegistrationRecordDO userRegistration) {
        AddRegistrationOptionBO addRegistrationOptionBO =
                this.convertToUserRegistrationOption(userRegistration);
        if (null == addRegistrationOptionBO) {
            return false;
        }
        Boolean offsetEventFinishFlag = addRegistrationOptionBO.getOffsetEventFinishFlag();
        // offsetEventFinishFlag存在值，则代表偏移任务
        return null != offsetEventFinishFlag;
    }

    @Override
    public Long fetchBasicIndicatorValue(UserRegistrationRecordDO userRegistrationRecordDO, Long indicatorId) {
        Map<String, Object> jsonData = resolveBasicDataFromJsonData(userRegistrationRecordDO);
        if (MapUtils.isEmpty(jsonData)) {
            return null;
        }
        String basicValueKey = BASE_INDICATOR_AVG_DATA.format(indicatorId);
        String baseDayAvgStr = MapUtils.getString(jsonData, basicValueKey);
        if (StringUtils.isBlank(baseDayAvgStr)) {
            return null;
        }
        return new BigDecimal(baseDayAvgStr).setScale(0, RoundingMode.UP).longValue();
    }
}
