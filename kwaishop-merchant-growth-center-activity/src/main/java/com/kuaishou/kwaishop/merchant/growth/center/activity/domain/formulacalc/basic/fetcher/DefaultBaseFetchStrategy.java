package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.bo.BaseDataSourceFetchResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.fetcher.module.enums.BaseDataSourceFetchTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
@Component
@Slf4j
public class DefaultBaseFetchStrategy implements BaseDataSourceFetchStrategy {

    @Resource
    private AdminActivityOnlineService adminActivityOnlineService;

    @Override
    public BaseDataSourceFetchResult fetch(BaseDataSourceFetchParam param) {
        long userId = param.getUserId();
        IndicatorConfigDO indicatorConfig = param.getIndicatorConfig();
        IndicatorDO indicator = param.getIndicator();
        List<String> dateList = param.getDateList();

        List<Map<String, Object>> rawDataList = adminActivityOnlineService.querySingleBaseIndicatorRawValue(
                userId, indicatorConfig, indicator, dateList);

        log.info("[默认基期查询策略] 查询成功 param:{}, result:{}", toJSON(param), toJSON(rawDataList));

        return BaseDataSourceFetchResult.builder().rawIndicatorBaseValues(rawDataList).build();
    }

    @Override
    public BaseDataSourceFetchTypeEnum getType() {
        return BaseDataSourceFetchTypeEnum.DEFAULT;
    }
}
