package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.entity;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
@Data
@Builder
public class ActivityTaskEntity {
    /**
     * 活动ID
     */
    private long activityId;
    /**
     * 系列ID
     */
    private long seriesType;
    /**
     * 父任务Id列表
     */
    private List<Long> parentTaskIdList;
    /**
     * 子任务ID列表
     */
    private List<Long> childTaskIdList;
}
