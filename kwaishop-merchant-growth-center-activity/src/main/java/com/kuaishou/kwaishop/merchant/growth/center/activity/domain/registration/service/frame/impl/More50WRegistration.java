package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.frame.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.registration50wPlusActivityId;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.registration50wPlusBasicDataList;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.frame.ActivityRegistrationAbstract;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-19
 */
@Slf4j
@Lazy
@Service
public class More50WRegistration extends ActivityRegistrationAbstract<Map<String, Object>> {

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    public Map<String, Object> convert(String param) {
        return fromJSON(param, Map.class, String.class, Object.class);
    }

    @Override
    public boolean filter(long userId, Map<String, Object> paramObject) {
        if (riskBlackList(userId)) {
            log.info("[50w+报名] 风控黑名单过滤, userId:{}, tag:{}", userId, getTag());
            return true;
        }
        List<String> paramKeyList = registration50wPlusBasicDataList.get();
        for (String paramKey : paramKeyList) {
            if (!paramObject.containsKey(paramKey)) {
                log.info("[50w+报名] 字段缺失, userId:{}, paramKey:{}, paramObject:{}", userId, paramKey,
                        toJSON(paramObject));
                return true;
            }
        }
        return false;
    }

    @Override
    protected Long getActivityId(long userId, Map<String, Object> paramObject) {
        return registration50wPlusActivityId.get();
    }

    @Override
    protected List<Long> getTaskIdList(long userId, Map<String, Object> paramObject, long activityId) {
        List<TaskDO> activityTaskList = taskLocalCacheService.getTaskListByActivityId(activityId);
        if (CollectionUtils.isEmpty(activityTaskList)) {
            log.info("[50w+报名] 未查询到活动下面的任务，activityId:{}, userId:{}", activityId, userId);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "未查询到活动下面的任务");
        }
        List<Long> taskIdList = activityTaskList.stream()
                .filter(e -> e.getParentTask() == 0).map(BaseDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIdList)) {
            log.info("[50w+报名] 未查询到父任务ID，activityId:{}, userId:{}", activityId, userId);
            throw new BizException(BasicErrorCode.SERVER_ERROR, "未查询到父任务ID");
        }
        return taskIdList;
    }

    @Override
    public String getTag() {
        return "more50w";
    }
}
