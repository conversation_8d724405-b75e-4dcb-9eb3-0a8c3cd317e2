package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-11
 */
public enum NotificationExecuteStatusEnum {
    EXECUTE(0, "执行当前推送"),
    SKIP(1, "跳过当次，重新计算下次"),
    STOP(2, "停止当次以及后续推送"),
    ;

    private int val;

    private String desc;

    NotificationExecuteStatusEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static NotificationExecuteStatusEnum of(int value) {
        for (NotificationExecuteStatusEnum order : NotificationExecuteStatusEnum.values()) {
            if (order.getVal() == value) {
                return order;
            }
        }
        return EXECUTE;
    }


    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
