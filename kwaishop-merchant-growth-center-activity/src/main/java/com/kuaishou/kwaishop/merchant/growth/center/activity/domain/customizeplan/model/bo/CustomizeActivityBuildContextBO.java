package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ComponentConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomizeActivityBuildContextBO {

    /**
     * 用户ID
     */
    private long userId;

    /**
     * 活动Map
     */
    private ActivityDO activity;

    /**
     * 用户活动记录Map
     */
    private UserActivityRecordDO userActivityRecord;

    /**
     * 用户任务记录Map
     */
    private Map<Long, UserTaskRecordDO> userTaskRecordMap;

    /**
     * 用户父任务资格记录
     */
    private Map<Long, UserRegistrationRecordBO> userRegistrationRecordMap;

    /**
     * 用户父任务map
     */
    private Map<Long, TaskDO> parentTaskMap;

    /**
     * 用户父任务对应子任务Map
     */
    private Map<Long, List<TaskDO>> subTaskMap;

    /**
     * 父任务对应组件Map
     */
    private Map<Long, ComponentConfigBO> componentMap;

    /**
     * 子任务对应指标记录
     */
    private Map<Long, List<IndicatorRecordBO>> indicatorRecordMap;
}
