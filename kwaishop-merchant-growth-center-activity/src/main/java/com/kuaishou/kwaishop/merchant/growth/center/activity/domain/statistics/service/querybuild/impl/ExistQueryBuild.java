package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.impl;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildHandler;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-31
 */
@Slf4j
@Lazy
@Service
public class ExistQueryBuild implements QueryBuildHandler {
    @Override
    public QueryBuilder buildConditionQueryBuild(StatisticsConditionBO condition, Map<String, Object> entityParam) {
        // 定值查询
        if (StringUtils.isBlank(condition.getFieldName())) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "exist查询条件字段不能为空");
        }
        return QueryBuilders.existsQuery(condition.getFieldName());
    }

    @Override
    public ConditionTypeEnum conditionType() {
        return ConditionTypeEnum.EXIST;
    }
}
