package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-28
 */
public enum RegistrationOptionFieldEnum {
    UNKNOWN("UNKNOWN", "未知"),

    overrideStartTime("overrideStartTime", "跃迁开始时间"),

    preSubActivityId("preSubActivityId", "前置子活动"),
    ;

    private final String value;
    private final String desc;

    //
    public static RegistrationOptionFieldEnum of(String value) {
        for (RegistrationOptionFieldEnum val : RegistrationOptionFieldEnum.values()) {
            if (val.getValue().equals(value)) {
                return val;
            }
        }
        return UNKNOWN;
    }

    RegistrationOptionFieldEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
