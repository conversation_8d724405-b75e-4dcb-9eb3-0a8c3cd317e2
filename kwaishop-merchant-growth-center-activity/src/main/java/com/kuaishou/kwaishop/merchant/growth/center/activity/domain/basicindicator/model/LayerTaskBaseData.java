package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums.BaseFormulaResultTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LayerTaskBaseData {

    /**
     * 基期计算类型
     */
    private BaseFormulaResultTypeEnum type;

    /**
     * 基期Map
     */
    private Map<String, Object> baseDataMap;
}
