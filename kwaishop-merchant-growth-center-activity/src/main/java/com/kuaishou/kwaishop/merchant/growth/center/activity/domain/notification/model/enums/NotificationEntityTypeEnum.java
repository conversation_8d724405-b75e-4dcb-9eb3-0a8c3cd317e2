package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 * 重点！推送域实体类型，与事件消息中的 EntityTypeEnum 不一样
 */
public enum NotificationEntityTypeEnum {
    UNKNOWN_ENTITY_TYPE(0, "未知"),
    ACTIVITY(1, "活动"),
    TASK(2, "任务"),
    AWARD(3, "奖励"),
    STRATEGY(10, "策略实体"),
    ;
    private int val;
    private String desc;

    NotificationEntityTypeEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static NotificationEntityTypeEnum of(int type) {
        for (NotificationEntityTypeEnum entityType : NotificationEntityTypeEnum.values()) {
            if (entityType.getVal() == type) {
                return entityType;
            }
        }
        return UNKNOWN_ENTITY_TYPE;
    }

    public static List<Integer> getAllNotificationValidEntityTypes() {
        return Arrays.stream(NotificationEntityTypeEnum.values())
                .map(NotificationEntityTypeEnum::getVal)
                .filter(val -> val > 0)
                .collect(Collectors.toList());
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
