package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_END_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.ACTIVITY_START_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.buildAggJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.parseActivityRiskTermsResult;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.config.es.EsIndexConfig.KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.convert.StatisticsConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.ActivityRiskStatisticSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SlrBelongInfoCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.RangeCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.review.StatisticsConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ConditionValueTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.RangeQueryTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRecordService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewV2Service;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.TermsAggregationBuild;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.StaffPolicyRepoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.dimension.ChildrenTaskDimensionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.StatisticsEsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.TermsAggData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.es.EsQueryResponse;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.statistics.StatisticsDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics.StatisticsDTO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-24
 */
@Slf4j
@Lazy
@Service
public class StatisticsReviewV2ServiceImpl implements StatisticsReviewV2Service {


    @Autowired
    private QueryBuildFactory queryBuildFactory;


    @Autowired
    private StatisticsEsDAO statisticsEsDAO;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private StatisticsRecordService statisticsRecordService;

    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private AwardConfigService awardConfigService;

    @Override
    public void buildSellerIndustryBelongQueryCondition(SlrBelongInfoCondition belongInfoCondition, String staffCode,
                                                        boolean jumpPermissionCheck,
                                                        List<StatisticsConditionBO> queryConditions) {
        if (jumpPermissionCheck) {
            return;
        }
        if (StringUtils.isNotBlank(staffCode)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("ownerStaff.keyword",
                    Lists.newArrayList(staffCode)));
        }
        List<String> firstIndustryCodeList = belongInfoCondition.getFirstIndustryCodeList();
        if (CollectionUtils.isNotEmpty(firstIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("firstIndustryCode.keyword",
                    firstIndustryCodeList));
        }
        List<String> secondIndustryCodeList = belongInfoCondition.getSecondIndustryCodeList();
        if (CollectionUtils.isNotEmpty(secondIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("secondIndustryCode.keyword",
                    secondIndustryCodeList));
        }
        List<String> thirdIndustryCodeList = belongInfoCondition.getThirdIndustryCodeList();
        if (CollectionUtils.isNotEmpty(thirdIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("leafPartyCode.keyword",
                    thirdIndustryCodeList));
        }
    }

    @Override
    public void buildSellerSmbBelongQueryCondition(SlrBelongInfoCondition belongInfoCondition, String staffCode,
                                                   boolean jumpPermissionCheck,
                                                   List<StatisticsConditionBO> queryConditions) {
        if (jumpPermissionCheck) {
            return;
        }
        if (StringUtils.isNotBlank(staffCode)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("smbOwnerStaff.keyword",
                    Lists.newArrayList(staffCode)));
        }
        List<String> firstIndustryCodeList = belongInfoCondition.getFirstIndustryCodeList();
        if (CollectionUtils.isNotEmpty(firstIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("smbFirstIndustryCode.keyword",
                    firstIndustryCodeList));
        }
        List<String> secondIndustryCodeList = belongInfoCondition.getSecondIndustryCodeList();
        if (CollectionUtils.isNotEmpty(secondIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("smbSecondIndustryCode.keyword",
                    secondIndustryCodeList));
        }
        List<String> thirdIndustryCodeList = belongInfoCondition.getThirdIndustryCodeList();
        if (CollectionUtils.isNotEmpty(thirdIndustryCodeList)) {
            queryConditions.add(queryBuildFactory.buildStrTermsConditionShould("smbThirdIndustryCode.keyword",
                    thirdIndustryCodeList));
        }
    }

    @Override
    public Map<String, TermsAggData> getSellerSignUpAggMap(StaffPolicyRepoConfig policyRepoConfig,
                                                           List<Long> userIdList,
                                                           List<StatisticsConditionBO> queryConditions) {
        // 构建查询条件
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderNotMust(queryConditions, null);
        String sellerSignUpInfoAggregations = policyRepoConfig.getSellerSignUpInfoAggregations();
        String aggJson = buildAggJsonByConfig(sellerSignUpInfoAggregations);
        // 聚合查询商家报名数据
        return statisticsEsDAO.queryTermsAggData(KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ, queryBuilder, aggJson);
    }

    @Override
    public List<StatisticsConditionBO> baseAggQueryBuild(StaffPolicyRepoConfig policyRepoConfig,
                                                         List<Long> userIdList) {
        List<StatisticsConditionBO> queryConditions = Lists.newArrayList();
        queryConditions.add(queryBuildFactory.buildTermsCondition("userId", userIdList));
        // 剔除风控资格
        queryConditions.add(queryBuildFactory.buildTermCondition("risk", 0));
        // 活动结束时间(需要刷从8月1号开始上线的活动的activityEndTime)
        if (BooleanUtils.isNotTrue(policyRepoConfig.getJumpActivityEndTimeFilterSwitch())) {
            // 活动结束时间大于等于当前时间
            queryConditions.add(queryBuildFactory.buildRangeCondition(ACTIVITY_END_TIME,
                    new RangeCondition(String.valueOf(System.currentTimeMillis()), RangeQueryTypeEnum.GTE),
                    null, ConditionValueTypeEnum.NUMBER));
        }
        List<Long> aggActivityIdBlackList = policyRepoConfig.getAggActivityIdBlackList();
        // 统计剔除黑名单活动id
        if (CollectionUtils.isNotEmpty(aggActivityIdBlackList)) {
            queryConditions.add(queryBuildFactory.buildTermsCondition("activityId", aggActivityIdBlackList, true));
        }
        return queryConditions;
    }

    @Override
    public Map<String, TermsAggData> getSignUpFinishAggDataMap(StaffPolicyRepoConfig policyRepoConfig,
                                                               List<StatisticsConditionBO> queryConditions) {
        queryConditions.add(queryBuildFactory.buildTermCondition("draw", 1));
        queryConditions.add(queryBuildFactory.buildNestedTermCondition("isSuccess", "taskInfo", 1));
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderNotMust(queryConditions, null);
        String sellerSignUpFinishAggregations = policyRepoConfig.getSellerSignUpFinishAggregations();
        String finishAggJson = buildAggJsonByConfig(sellerSignUpFinishAggregations);
        // 聚合查询商家已完成活动数据
        return statisticsEsDAO.queryTermsAggData(KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ, queryBuilder,
                finishAggJson);
    }

    @Override
    public Map<Long, ActivityRiskStatisticSummary> getRiskAggDataMap(StaffPolicyRepoConfig policyRepoConfig,
                                                                     List<StatisticsConditionBO> queryConditions) {
        QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderNotMust(queryConditions, null);
        String activityRiskDataAggregations = policyRepoConfig.getActivityRiskDataAggregations();
        String riskAggJson = buildAggJsonByConfig(activityRiskDataAggregations);
        // 聚合活动商家分控数据
        Map<String, TermsAggData> termsAggDataMap =
                statisticsEsDAO.queryTermsAggData(KWAISHOP_GROWTH_ACTIVITY_USER_STATISTICS_YZ, queryBuilder,
                        riskAggJson);
        // 解析聚合结果
        return parseActivityRiskTermsResult(policyRepoConfig.getActivityRiskDataTermsAggPath(), termsAggDataMap);
    }

    private static String buildAggJsonByConfig(String sellerSignUpInfoAggregations) {
        List<TermsAggregationBuild> signUpInfoAggregations = fromJSON(sellerSignUpInfoAggregations, List.class,
                TermsAggregationBuild.class);
        // 构造聚合json
        return buildAggJson(signUpInfoAggregations);
    }

    @Override
    public Long getProgressActivityCnt(StaffPolicyRepoConfig policyRepoConfig,
                                       List<StatisticsConditionBO> queryConditions) {
        Long progressActivityCnt = null;
        if (BooleanUtils.isTrue(policyRepoConfig.getAddActivityStartTimeFilterSwitch())) {
            // 活动开始时间 <= 当前时间
            queryConditions.add(queryBuildFactory.buildRangeCondition(ACTIVITY_START_TIME,
                    null, new RangeCondition(String.valueOf(System.currentTimeMillis()), RangeQueryTypeEnum.LTE),
                    ConditionValueTypeEnum.NUMBER));
            QueryBuilder queryBuilder = queryBuildFactory.buildQueryBuilderNotMust(queryConditions, null);
            EsQueryResponse<Map<String, Object>> queryResponse = statisticsEsDAO.pageQueryUserData(queryBuilder, 1, 1);
            if (queryResponse != null && queryResponse.getTotal() > 0) {
                progressActivityCnt = queryResponse.getTotal();
            }
        }
        return progressActivityCnt;
    }

    @Override
    public StatisticsDTO getActivityDimensionStatistics(Long activityId) {
        if (activityId <= 0) {
            throw new BizException(ErrorCode.BasicErrorCode.PARAM_INVALID, "活动ID为空");
        }
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (null == activityDO) {
            throw new BizException(PARAM_INVALID, "活动不存在");
        }
        List<TaskDO> taskDOList = taskLocalCacheService.getTaskListByActivityId(activityId);

        // 活动维度
        ChildrenTaskDimensionBO childrenTaskDimensionBO = buildActivityDimension(activityId, taskDOList);

        // 获取统计信息
        List<StatisticsDO> activityStatisticList = statisticsRecordService.queryStatisticsInfo(activityId,
                Collections.singletonList(childrenTaskDimensionBO.getEntityId()), EntityTypeEnum.ACTIVITY.getCode());
        List<Long> taskIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(childrenTaskDimensionBO.getChildrenTaskIds())) {
            taskIds.addAll(childrenTaskDimensionBO.getChildrenTaskIds());
        }
        List<IndicatorConfigBO> indicatorConfigs = Lists.newArrayList();
        List<AwardConfigBO> awardConfigBOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(taskIds)) {
            // 获取指标配置
            indicatorConfigs =
                    indicatorService.batchListIndicatorConfig(Collections.singletonList(activityId), taskIds)
                            .stream().filter(e -> e.getType().intValue() == EntityTypeEnum.TASK.getCode())
                            .collect(Collectors.toList());
            // 获取奖励配置
            awardConfigBOS = awardConfigService.getMultiTaskAwardConfig(activityId, taskIds);
        }
        List<StatisticsDTO> statisticsList = StatisticsConverter.convert2StaticsDTO(activityStatisticList,
                indicatorConfigs, awardConfigBOS, Collections.singletonList(childrenTaskDimensionBO));
        if (CollectionUtils.isEmpty(statisticsList)) {
            log.error("getActivityDimensionStatistics statisticsList is empty, activityId:{}", activityId);
            return null;
        }
        return statisticsList.get(0);
    }

    private ChildrenTaskDimensionBO buildActivityDimension(long activityId, List<TaskDO> taskList) {
        ChildrenTaskDimensionBO childrenTaskDimensionBO = new ChildrenTaskDimensionBO();
        childrenTaskDimensionBO.setSubActivityOrder(0);
        childrenTaskDimensionBO.setLayerOrder(0);
        childrenTaskDimensionBO.setPeriodOrder(0);
        childrenTaskDimensionBO.setEntityType(EntityTypeEnum.ACTIVITY.getCode());
        childrenTaskDimensionBO.setEntityId(activityId);
        if (CollectionUtils.isNotEmpty(taskList)) {
            childrenTaskDimensionBO.setChildrenTaskIds(taskList.stream()
                    .map(BaseDO::getId)
                    .collect(Collectors.toList()));
        }
        return childrenTaskDimensionBO;
    }
}
