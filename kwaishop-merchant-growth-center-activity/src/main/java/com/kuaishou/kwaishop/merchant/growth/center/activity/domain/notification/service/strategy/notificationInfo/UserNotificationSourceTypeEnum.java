package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.notificationInfo;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-21
 */
@Getter
@AllArgsConstructor
public enum UserNotificationSourceTypeEnum {

    UNKNOWN("UNKNOWN", "未知"),
    SELECTION_DECISION("SELECTION_DECISION", "选品中心"),
    ;

    private final String source;

    private final String desc;

    public static UserNotificationSourceTypeEnum of(String source) {
        return Arrays.stream(values())
                .filter(type -> StringUtils.equals(type.getSource(), source)).findFirst()
                .orElse(UNKNOWN);
    }
}
