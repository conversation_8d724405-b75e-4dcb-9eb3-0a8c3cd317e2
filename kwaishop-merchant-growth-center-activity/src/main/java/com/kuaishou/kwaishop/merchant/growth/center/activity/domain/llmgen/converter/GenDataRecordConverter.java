package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.converter;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.gendata.GenDataRecordDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-22
 */
public class GenDataRecordConverter {

    public static GenDataRecordDO convertToDO(GenDataRecordBO genDataRecordBO) {
        if (genDataRecordBO == null) {
            return null;
        }

        return GenDataRecordDO.builder()
                .id(genDataRecordBO.getId())
                .scene(genDataRecordBO.getScene())
                .uniqueKey(genDataRecordBO.getUniqueKey())
                .status(genDataRecordBO.getStatus())
                .content(toJSON(genDataRecordBO.getContent()))
                .ext(genDataRecordBO.getExt())
                .createTime(genDataRecordBO.getCreateTime())
                .updateTime(genDataRecordBO.getUpdateTime())
                .version(genDataRecordBO.getVersion())
                .deleted(genDataRecordBO.getDeleted())
                .creator(genDataRecordBO.getCreator())
                .modifier(genDataRecordBO.getModifier())
                .build();
    }

    public static GenDataRecordBO convertFromDO(GenDataRecordDO genDataRecordDO) {
        if (genDataRecordDO == null) {
            return null;
        }

        GenDataRecordContentBO genDataContent = null;
        if (StringUtils.isNotBlank(genDataRecordDO.getContent())) {
            genDataContent = fromJSON(genDataRecordDO.getContent(), GenDataRecordContentBO.class);
        }

        return GenDataRecordBO.builder()
                .id(genDataRecordDO.getId())
                .scene(genDataRecordDO.getScene())
                .uniqueKey(genDataRecordDO.getUniqueKey())
                .status(genDataRecordDO.getStatus())
                .content(genDataContent)
                .ext(genDataRecordDO.getExt())
                .createTime(genDataRecordDO.getCreateTime())
                .updateTime(genDataRecordDO.getUpdateTime())
                .version(genDataRecordDO.getVersion())
                .deleted(genDataRecordDO.getDeleted())
                .creator(genDataRecordDO.getCreator())
                .modifier(genDataRecordDO.getScene())
                .build();
    }
}
