package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.drawNextTaskActivitySeriesType;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationOptionFieldEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.RegistrationHandleTaskService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.biz.UserActivityBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.UserTaskEventMsg;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawRuleCondition;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawRuleDTO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-21
 */

@Slf4j
@Lazy
@Service
public class RegistrationDrawNextTaskHandleImpl implements RegistrationHandleTaskService {

    @Autowired
    private UserActivityBizService userActivityBizService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    public static final String DRAW_NEXT_DYNAMIC_EVENT = "complete_pre_sub_activity";


    @Override
    public void handle(UserTaskEventMsg userTaskEventMsg) {
        if (userTaskEventMsg.getEventType() != UserTaskEventTypeEnum.SUCCEED.getValue()) {
            return;
        }
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(userTaskEventMsg.getActivityId());
        if (activityDO == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动不存在");
        }
        List<Long> drawNextSeriesTypes = drawNextTaskActivitySeriesType.get();
        if (!drawNextSeriesTypes.contains(activityDO.getSeriesType())) {
            log.info("[领取下一阶段活动]当前只支持的活动类型:{},活动ID:{},用户id:{},任务id:{}", drawNextSeriesTypes,
                    userTaskEventMsg.getActivityId(), userTaskEventMsg.getUserId(), userTaskEventMsg.getTaskId());
            return;
        }
        TaskDO taskConfig = taskLocalCacheService.getTaskByTaskId(userTaskEventMsg.getTaskId());
        if (taskConfig == null) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "任务不存在");
        }
        if (taskConfig.getParentTask() != 0) {
            log.info("[领取下一阶段活动]父任务完成事件可领取下一阶段任务:taskId:{},userId:{}",
                    userTaskEventMsg.getTaskId(), userTaskEventMsg.getUserId());
            return;
        }
        // 构建子活动条件
        Integer subTaskOrder = taskConfig.getStage();
        DynamicDrawRuleDTO subOrderRule = convertDynamicDrawRule("sub_activity_id", String.valueOf(subTaskOrder));
        //构建option选项
        DynamicDrawRuleDTO preActivityId =
                convertDynamicDrawRule(RegistrationOptionFieldEnum.preSubActivityId.getValue(),
                        String.valueOf(taskConfig.getId()));

        // 构建活动条件
        DynamicDrawRuleDTO activityRule = convertDynamicDrawRule("activity_id", String.valueOf(activityDO.getId()));
        List<DynamicDrawRuleDTO> dynamicDrawRules = Lists.newArrayList(subOrderRule, activityRule, preActivityId);
        DynamicDrawActivityRequest request = DynamicDrawActivityRequest.newBuilder()
                .setCode(DRAW_NEXT_DYNAMIC_EVENT)
                .addAllDrawRule(dynamicDrawRules)
                .setUserId(userTaskEventMsg.getUserId())
                .setRegistrationType(0)
                .setSource(RegistrationSourceEnum.NEXT_TASK.getCode())
                .build();
        userActivityBizService.dynamicDrawActivity(request);
    }

    public DynamicDrawRuleDTO convertDynamicDrawRule(String field, String condition) {
        DynamicDrawRuleCondition ruleCondition = DynamicDrawRuleCondition.newBuilder()
                .setConditionCode(condition)
                .setConditionName(StringUtils.EMPTY)
                .build();
        return DynamicDrawRuleDTO.newBuilder()
                .setField(field)
                .setDesc(StringUtils.EMPTY)
                .addFieldOptions(ruleCondition)
                .build();
    }

    @Override
    public List<UserTaskEventTypeEnum> getHandleEventType() {
        return Lists.newArrayList(UserTaskEventTypeEnum.SUCCEED);
    }
}
