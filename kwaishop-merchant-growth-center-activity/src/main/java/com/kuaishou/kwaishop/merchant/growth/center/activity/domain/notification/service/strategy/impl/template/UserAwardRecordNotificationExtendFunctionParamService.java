package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfSuccess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.UserAwardRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.param.AwardRecordQueryParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.combine.service.LianHeLaXinService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.StrategyAwardService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;

import lombok.extern.slf4j.Slf4j;

/**
 * 拉新累计奖励
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-04
 */
@Service
@Slf4j
public class UserAwardRecordNotificationExtendFunctionParamService
        extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private AwardService awardService;

    @Autowired
    private LianHeLaXinService combineService;

    @Autowired
    private StrategyAwardService strategyAwardService;

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.CUMULATIVE_BACK_AWARD);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = new HashMap<>();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(Maps.newHashMap())
                .executeStatus(EXECUTE)
                .build();
        // 降级、黑名单校验
        NotificationExtendFunctionParamBO checkResult = checkDegradationAndBlackList(userId, configBO);
        if (checkResult != null) {
            return checkResult;
        }
        // 获取用户奖励记录
        UserAwardRecordBO userAwardRecord = getUserAwardRecord(userId, configBO);
        if (userAwardRecord == null) {
            log.warn("[获取模板参数失败] Fail to query userAwardRecord, userId is :{}, "
                    + "notificationPushConfigBO is :{}", userId, configBO);
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, SKIP.getDesc(), String.valueOf(configBO.getEntityId()));
            // 用户奖励记录需要DE数据同步，如果同步延迟了，将获取不到数据，跳过此次推送，等待下次的推送
            result.setExecuteStatus(SKIP);
            return result;
        }
        // 如果累计奖励为0，则不进行推送
        if (userAwardRecord.getAwardValue() == null || userAwardRecord.getAwardValue() == 0) {
            log.info("[获取模板参数] 用户累计奖励为0，不用推送，userId is : {}, notificationPushConfigBO is :{}", userId, configBO);
            perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), String.valueOf(configBO.getEntityId()));
            result.setExecuteStatus(STOP);
            return result;
        }
        // 拼接奖励内容，数值 + 单位
        String award = AwardUnitUtils.changeFenUnitAwardValueToYuan(userAwardRecord.getAwardType(),
                userAwardRecord.getAwardValue());
        String awardUnitDesc =
                combineService.buildAwardUnitDesc(userAwardRecord.getAwardType(), false);
        params.put(TemplateParamTypeEnum.CUMULATIVE_BACK_AWARD.getName(), award + awardUnitDesc);
        result.setTemplateParamMap(params);
        perfSuccess(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, EXECUTE.getDesc(), String.valueOf(configBO.getEntityId()));
        return result;
    }

    /**
     * 获取用户奖励记录
     */
    private UserAwardRecordBO getUserAwardRecord(long userId, NotificationPushConfigBO configBO) {
        // 获取关联的奖励配置
        AwardConfigBO relatedAwardConfig = queryRelatedAwardConfig(configBO);
        if (relatedAwardConfig == null) {
            return null;
        }
        AwardRecordQueryParam queryParam = AwardRecordQueryParam.builder().userId(userId)
                .activityId(relatedAwardConfig.getActivityId()).entityId(relatedAwardConfig.getEntityId())
                .awardConfigId(relatedAwardConfig.getId()).build();
        List<UserAwardRecordBO> userActivityAwards = awardService.queryUserAwardRecord(queryParam);
        return CollectionUtils.isEmpty(userActivityAwards) ? null : userActivityAwards.get(0);
    }

    /**
     * 根据不同触达类型，通过不同方式获取奖励配置id
     */
    private AwardConfigBO queryRelatedAwardConfig(NotificationPushConfigBO configBO) {
        if (configBO.getEntityType() != NotificationEntityTypeEnum.AWARD.getVal()) {
            // 如果触达类型不是奖励，configBO中的entityId为策略id
            return strategyAwardService.getStrategyMainAwardRuleConfigWithCache(configBO.getEntityId());
        }
        // 如果触达类型是奖励，configBO中的entityId为奖励配置id
        AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
        if (awardConfigDO == null) {
            return null;
        }
        return AwardConfigBO.builder().id(awardConfigDO.getId())
                .awardType(AwardTypeEnum.getByCode(awardConfigDO.getAwardType()))
                .activityId(awardConfigDO.getActivityId())
                .entityId(awardConfigDO.getEntityId()).build();

    }
}
