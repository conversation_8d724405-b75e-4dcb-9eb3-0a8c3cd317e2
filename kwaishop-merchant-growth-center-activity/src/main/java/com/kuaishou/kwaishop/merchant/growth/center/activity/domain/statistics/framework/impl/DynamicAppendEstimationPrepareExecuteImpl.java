package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.convert.AutoEstimateFlagGenerator.setEstimateType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.estimationCrowdPartitionSize;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.EstimateCrowdAppendResultVO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.EstimationCrowdPullBasicData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.AutoEstimationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.DynamicAppendEstimationPrepareBatchEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-07
 */
@Service
@Slf4j
@Lazy
public class DynamicAppendEstimationPrepareExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private EstimationCrowdPrepareExecuteImpl estimationCrowdPrepareExecute;

    @Autowired
    private EstimationGroupCrowdPrepareExecuteImpl estimationGroupCrowdPrepareExecute;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        DynamicAppendEstimationPrepareBatchEvent estimationPrepareBatchEvent =
                (DynamicAppendEstimationPrepareBatchEvent) event;
        if (estimationPrepareBatchEvent == null || estimationPrepareBatchEvent.invalid()) {
            log.error("[动态追加测算] 参数异常 {}", toJSON(event));
            throw new IllegalArgumentException("estimationPrepareBatchEvent is invalid");
        }
        EstimationStrategyTypeEnum strategyType = estimationPrepareBatchEvent.getStrategyType();

        return estimationPrepareBatchEvent.getCrowdAppendResultList().stream()
                .map(EstimateCrowdAppendResultVO::getSellerId)
                .collect(Collectors.toSet());
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        log.info("[动态追加测算] batchCustomizeExecute start eventId {} userIdSize {}", eventId, userIdList.size());
        EstimationCrowdPullBasicData crowdPullBasicData = fromJSON(executeConfig, EstimationCrowdPullBasicData.class);
        EstimationStrategyTypeEnum strategyType = crowdPullBasicData.getStrategyType();
        ExecuteHandleResult handleResult;
        if (EstimationStrategyTypeEnum.GROUP.equals(strategyType)) {
            handleResult = estimationGroupCrowdPrepareExecute.batchCustomizeExecute(userIdList,
                    eventId, executeConfig);
        } else if (EstimationStrategyTypeEnum.STRATEGY.equals(strategyType)) {
            handleResult = estimationCrowdPrepareExecute.batchCustomizeExecute(userIdList, eventId, executeConfig);
        } else {
            log.error("[动态追加测算] 参数异常 {}", executeConfig);
            throw new IllegalArgumentException("executeConfig is invalid");
        }
        log.info("[动态追加测算] batchCustomizeExecute success eventId {} userIdSize {}", eventId, userIdList.size());
        return handleResult;
    }

    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 获取执行配置必要信息携带
        DynamicAppendEstimationPrepareBatchEvent executeEvent = (DynamicAppendEstimationPrepareBatchEvent) event;
        // 获取人群&基期配置
        EstimationCrowdPullBasicData crowdPullBasicData =
                new EstimationCrowdPullBasicData(executeEvent.getCrowdConfigBO(),
                        executeEvent.getBasicConfigBO(), false,
                        executeEvent.getBaseIndicatorMap(), Maps.newHashMap(),
                        executeEvent.getPDate(), executeEvent.getActivityDays());
        crowdPullBasicData.setAutoEstimateFlag(setEstimateType(0, AutoEstimationTypeEnum.REALTIME_ESTIMATE));
        crowdPullBasicData.setStrategyType(executeEvent.getStrategyType());
        crowdPullBasicData.setStrategyId(executeEvent.getStrategyId());
        return toJSON(crowdPullBasicData);
    }

    @Override
    protected boolean needRetryWhenExp() {
        return true;
    }

    @Override
    protected boolean filterSuccessUser() {
        return true;
    }

    @Override
    int getPartitionSize() {
        return estimationCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.DYNAMIC_APPEND_ESTIMATION_CROWD_PREPARE;
    }
}
