package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-17
 */
@Slf4j
@Service
@Lazy
public class BatchExecuteFactory {
    private Map<Integer, BatchExecuteFramework> batchExecuteFrameworkMap;

    @Autowired
    private List<BatchExecuteFramework> batchExecuteFrameworks;

    @PostConstruct
    private void init() {
        batchExecuteFrameworkMap = new HashMap<>();
        batchExecuteFrameworks
                .forEach(i -> batchExecuteFrameworkMap.put(i.getBatchExecuteType().getValue(), i));
    }

    /**
     * 根据处理类型获取对应的处理类
     *
     * @param type 指标ID
     * @return 对应的处理类
     */
    public BatchExecuteFramework getBatchExecuteByType(int type) {
        BatchExecuteFramework batchExecuteFramework = batchExecuteFrameworkMap.get(type);
        if (batchExecuteFramework == null) {
            throw new BizException(BasicErrorCode.SERVER_ERROR, "批量处理框架无法处理类型-" + type);
        }
        return batchExecuteFrameworkMap.get(type);
    }
}
