package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.factory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.handler.CustomizePlanHandlerService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-09
 */
@Slf4j
@Service
@Lazy
public class CustomizePlanHandlerFactory {

    private Map<String, CustomizePlanHandlerService> customizePlanHandlerServiceMap;

    @Autowired
    private List<CustomizePlanHandlerService> customizePlanHandlerServiceList;

    @PostConstruct
    private void init() {
        customizePlanHandlerServiceMap = new HashMap<>();
        customizePlanHandlerServiceList.forEach(e -> customizePlanHandlerServiceMap.put(e.getScene().getScene(), e));
    }

    /**
     * 根据不同场景获取对应处理类
     */
    public CustomizePlanHandlerService getCustomizePlanHandlerService(String scene) {
        return customizePlanHandlerServiceMap.get(scene);
    }
}
