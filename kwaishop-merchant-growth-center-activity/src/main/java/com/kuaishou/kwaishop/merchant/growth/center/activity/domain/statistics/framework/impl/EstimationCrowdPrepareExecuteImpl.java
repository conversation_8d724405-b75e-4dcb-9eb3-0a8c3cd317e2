package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.convert.AutoEstimateFlagGenerator.setEstimateType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationPerfEnum.DAP_ESTIMATION_PREPARE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.estimationCrowdPartitionSize;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import com.github.rholder.retry.RetryException;
import com.google.api.client.util.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.IndustryActivityBuildService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.protocol.StrategyRelateConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.ProtocolCheckService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.client.CrowdFetchClient;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.converter.CrowdBOConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.command.UserEstimationStrategySaveCmd;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyMsgSendService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.UserEstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.EstimationCrowdPullBasicData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.StrategyIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserBasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserCrowdAbConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategySnapshotEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationPrepareEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationResultRecordAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.AutoEstimationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.StrategySnapshotSubStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationResultAggregateRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.service.UserEstimationStrategyDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.EstimationCrowdInfoBatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-05
 */
@Service
@Slf4j
@Lazy
public class EstimationCrowdPrepareExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private EstimationCacheService estimationCacheService;

    @Autowired
    private UserEstimationStrategyDomainService userEstimationStrategyDomainService;

    @Autowired
    private UserEstimationResultAggregateRepository userEstimationResultAggregateRepository;

    @Autowired
    private IndustryActivityBuildService industryActivityBuildService;

    @Autowired
    private CrowdFetchClient crowdFetchClient;

    @Autowired
    private UserEstimationStrategyReadService userEstimationStrategyReadService;

    @Autowired
    private ProtocolCheckService protocolCheckService;

    @Autowired
    private EstimationStrategyMsgSendService estimationStrategyMsgSendService;


    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        EstimationCrowdInfoBatchExecuteEvent executeEvent = (EstimationCrowdInfoBatchExecuteEvent) event;
        // 1 查询本次测算人群导入配置
        String eventId = event.getEventId();
        try {
            EstimationStrategyAggregateRoot strategyAggregateRoot = executeEvent.getStrategyAggregateRoot();
            CrowdConfigBO crowdConfigBO = strategyAggregateRoot.getCrowdConfigBO();
            Long strategyId = executeEvent.getStrategyId();
            String strategyVersion = executeEvent.getStrategyVersion();
            String prepareSyncVersion = executeEvent.getPrepareSyncVersion();
            crowdConfigBO.setStrategyConfig(new StrategyRelateConfig(strategyId, strategyVersion,
                    EstimationStrategyTypeEnum.STRATEGY.getCode()));
            // 1.1 先尝试从缓存中拉取人群数据（重试场景）
            Set<Long> estimationCrowdSet = loadCrowdInfoFromCache(strategyAggregateRoot, strategyId,
                    prepareSyncVersion);
            if (CollectionUtils.isNotEmpty(estimationCrowdSet)) {
                return estimationCrowdSet;
            }
            // 2 从hive or excel or 人群包拉取人群
            Set<Long> crowdUserIdSet = industryActivityBuildService.getSellerByCrowdType(crowdConfigBO.getCrowdType(),
                    toJSON(crowdConfigBO));
            // ab命中实验组的人群
            Set<Long> crowdAbUserIdSet = Sets.newHashSet();
            if (crowdConfigBO.isAbCrowd()) {
                // 直接查询对应的AB实验组人群
                crowdAbUserIdSet =
                        crowdFetchClient.queryCrowdUserIdsByExperimentId(CrowdBOConverter.convertToCrowdBO(crowdConfigBO),
                                crowdConfigBO.getAbExperimentId());
                if (BooleanUtils.isNotTrue(crowdConfigBO.getFullEstimate())) {
                    crowdUserIdSet = crowdAbUserIdSet;
                }
            }
            log.info("[{}] initExecuteCrowdAndCache crowdUserIdSetSize:{}, crowdAbUserIdSetSize:{}", eventId,
                    crowdUserIdSet.size(), crowdAbUserIdSet.size());
            if (CollectionUtils.isEmpty(crowdUserIdSet)) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "查询测算人群数据为空");
            }
            if (BooleanUtils.isTrue(executeEvent.getCustomizeBasic())) {
                BasicConfigBO basicConfigBO = strategyAggregateRoot.getBasicConfigBO();
                // 有商家不在自定义基期里面。那么需要异常抛出
                protocolCheckService.checkStrategyCrowdExcelToSeller("测算校验-", basicConfigBO, crowdUserIdSet);
            }
            // 3 缓存测算总人数
            int size = crowdUserIdSet.size();
            estimationCacheService.setEstimationCrowdTotalCnt(strategyId, prepareSyncVersion,
                    EstimationStrategyTypeEnum.STRATEGY, size);

            // 4 缓存测算用户集合到bitMap
            estimationCacheService.setEstimationCrowdSet(strategyId, prepareSyncVersion,
                    EstimationStrategyTypeEnum.STRATEGY, crowdUserIdSet);
            estimationCacheService.setEstimationCrowdAbSet(String.valueOf(strategyId), crowdAbUserIdSet);
            // 5 更新测算人群总数到对应快照中
            EstimationStrategySnapshotEntity specifyVersionEntity = strategyAggregateRoot.getSpecifyVersionEntity();
            specifyVersionEntity.setSubStatus(StrategySnapshotSubStatusEnum.CROWD_DATA_SYNC);
            specifyVersionEntity.fillEstimationStartTime();
            strategyAggregateRoot.updateCrowCntOnExt((long) size);
            PerfUtil.perfSuccess(DAP_ESTIMATION_PREPARE, "initExecuteCrowdAndCache", eventId);
            return crowdUserIdSet;
        } catch (Exception e) {
            log.error("[{}] initExecuteCrowdAndCache happen error", eventId, e);
            PerfUtil.perfException(DAP_ESTIMATION_PREPARE, "initExecuteCrowdAndCache", eventId);
            throw new RuntimeException(e);
        }
    }

    /**
     * 先尝试从缓存中拉取人群数据
     *
     * @param strategyAggregateRoot
     * @param strategyId
     * @param prepareSyncVersion
     * @return
     * @throws IOException
     */
    private Set<Long> loadCrowdInfoFromCache(EstimationStrategyAggregateRoot strategyAggregateRoot, Long strategyId,
                                             String prepareSyncVersion) throws IOException {
        if (strategyAggregateRoot.getExtBO() != null && strategyAggregateRoot.getExtBO().getSyncCrowdCnt() != null) {
            long syncCrowdCnt = strategyAggregateRoot.getExtBO().getSyncCrowdCnt();
            long cacheCrowdTotalCnt = estimationCacheService.getEstimationCrowdTotalCnt(strategyId,
                    prepareSyncVersion, EstimationStrategyTypeEnum.STRATEGY);
            if (syncCrowdCnt == cacheCrowdTotalCnt) {
                Set<Long> estimationCrowdSet = estimationCacheService.getEstimationCrowdSet(strategyId,
                        prepareSyncVersion, EstimationStrategyTypeEnum.STRATEGY);
                if (syncCrowdCnt == estimationCrowdSet.size()) {
                    return estimationCrowdSet;
                }
            }
        }
        return Sets.newHashSet();
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        if (!EnvUtils.isProd()) {
            log.info("[EstimationCrowdPrepareExecuteImpl] batchCustomizeExecute start eventId {}", eventId);
        }
        ExecuteHandleResult result = new ExecuteHandleResult();
        EstimationCrowdPullBasicData crowdPullBasicData = fromJSON(executeConfig, EstimationCrowdPullBasicData.class);
        BasicConfigBO basicConfigBO = crowdPullBasicData.getBasicConfigBO();
        String bizDate = crowdPullBasicData.getBizDate();
        Boolean customizeBasic = crowdPullBasicData.getCustomizeBasic();
        String prepareSyncVersion = crowdPullBasicData.getPrepareSyncVersion();
        List<Long> successUserIdList = Lists.newArrayList();
        List<Long> failedUserIdList = Lists.newArrayList();
//        Pair<Long, String> pair = parseBizKey(eventId);
        Long strategyId = crowdPullBasicData.getStrategyId();
        String strategyVersion = crowdPullBasicData.getStrategyVersion();
        // 判断后续是否需要重新同步版本基期
        userIdList.forEach(userId -> {
            String ukId = String.join("-", eventId, String.valueOf(userId));
            // 加锁
            try {
                // 1 查询DB判断是否已经落库
                UserEstimationResultRecordAggregateRoot estimationResultRecord =
                        userEstimationResultAggregateRepository.queryByBizKey(userId, strategyId, strategyVersion,
                                true);
                // 1.1 幂等判断基期/AB数据是否齐全
                if (userPrepareDataReady(estimationResultRecord)) {
                    // 防止前置消息发送失败，再次尝试发送同步消息
                    // 不加锁目前会重复发消息的情况出现，怀疑是消费无ack导致重复消费
                    sendHiveMsg(estimationResultRecord.getUserEstimationPrepareEntity(), bizDate, true);
                    successUserIdList.add(userId);
                    return;
                }
                Map<String, Object> jsonKeyBasicMap = Maps.newHashMap();
                if (BooleanUtils.isTrue(customizeBasic)) {
                    // 需要获取自定义基期数据
                    jsonKeyBasicMap = userEstimationStrategyReadService.getUserEstimationCustomizeValue(strategyId,
                            EstimationStrategyTypeEnum.STRATEGY, userId, basicConfigBO);
                }
                // 2 构造保存指令
                UserEstimationStrategySaveCmd userEstimationStrategySaveCmd = buildSaveCmd(estimationResultRecord,
                        jsonKeyBasicMap, crowdPullBasicData);
                userEstimationStrategySaveCmd.setPrepareSyncVersion(prepareSyncVersion);
                UserEstimationPrepareEntity estimationPrepareEntity =
                        userEstimationStrategyDomainService.saveUserEstimationStrategy(userEstimationStrategySaveCmd);
                // 3 发送kafka消息同步hive（维护p_date;kafka2Hive保障幂等）
                sendHiveMsg(estimationPrepareEntity, bizDate, false);
                // 4 处理结果构建（会被缓存做幂等）
                successUserIdList.add(userId);
                PerfUtil.perfSuccess(DAP_ESTIMATION_PREPARE, "saveCrowdInfo", ukId);
            } catch (DataIntegrityViolationException ex) {
                log.warn("[EstimationCrowdPrepareExecuteImpl] 重复消费！userId {} eventId {}", userId, eventId);
                perfException(DAP_ESTIMATION_PREPARE, "duplicateKeyException", ukId);
            } catch (Exception e) {
                log.error("测算人群拉取同步出现异常 userId {} eventId {}", userId, eventId, e);
                PerfUtil.perfException(DAP_ESTIMATION_PREPARE, "saveCrowdInfo", ukId, e.getMessage());
                failedUserIdList.add(userId);
            }
        });

        result.setSuccessUserList(successUserIdList);
        estimationCacheService.batchAddUserToSuccessList(eventId, successUserIdList);
        result.setFailUserList(failedUserIdList);
        return result;
    }

    private void sendHiveMsg(UserEstimationPrepareEntity estimationPrepareEntity, String bizDate, boolean next)
            throws ExecutionException, RetryException {
        boolean sendRes = estimationStrategyMsgSendService.sendUserPrepareMsg(estimationPrepareEntity,
                bizDate, next);
        if (sendRes) {
            // 成功发送成功标识
            estimationPrepareEntity.updateSendFlag();
        }
    }

    /**
     * 幂等判断基期/AB数据是否齐全
     *
     * @param estimationResultRecord
     * @return
     */
    private static boolean userPrepareDataReady(UserEstimationResultRecordAggregateRoot estimationResultRecord) {
        return estimationResultRecord != null && estimationResultRecord.getId() != null
                && estimationResultRecord.getUserEstimationPrepareEntity() != null
                && estimationResultRecord.getUserEstimationPrepareEntity().userBasicDataReady();
    }

    @Override
    protected boolean needRetryWhenExp() {
        return true;
    }

    private UserEstimationStrategySaveCmd buildSaveCmd(
            UserEstimationResultRecordAggregateRoot estimationResultRecord,
            Map<String, Object> jsonKeyBasicMap, EstimationCrowdPullBasicData crowdPullBasicData) {
        Long userId = estimationResultRecord.getUserId();
        Long strategyId = estimationResultRecord.getStrategyId();
        String strategyVersion = estimationResultRecord.getStrategyVersion();
        Integer activityDays = crowdPullBasicData.getActivityDays();
        Map<Long, IndicatorDO> baseIndicatorMap = crowdPullBasicData.getBaseIndicatorMap();
        Map<Long, IndicatorConfigDO> estimationIndicatorConfigMap =
                crowdPullBasicData.getEstimationIndicatorConfigMap();
        CrowdConfigBO crowdConfigBO = crowdPullBasicData.getCrowdConfigBO();
        BasicConfigBO basicConfigBO = crowdPullBasicData.getBasicConfigBO();
        Map<Long, String> jsonKeyMap = crowdPullBasicData.getJsonKeyMap();
        // 之前已插入对应的prepare数据，无需再查询基期
        UserBasicConfigBO userBasicConfigBO = null;
        UserCrowdAbConfigBO crowdConfig = null;
        boolean notInitFinishBefore = isNotInitFinishBefore(estimationResultRecord);
        // 2.1 查询基期
        if (notInitFinishBefore) {
            userBasicConfigBO = UserBasicConfigBO.builder()
                    .indicatorBasicDataList(userEstimationStrategyReadService.parseIndicatorBasicData(userId,
                            basicConfigBO, jsonKeyBasicMap, jsonKeyMap, baseIndicatorMap, activityDays,
                            estimationIndicatorConfigMap))
                    .build();
            // 2.2 获取人群参数
            crowdConfig = userEstimationStrategyReadService.getUserCrowdConfig(crowdConfigBO, userId);
        }
        // 2.3 构造实体落库
        return UserEstimationStrategySaveCmd.builder()
                .userId(userId)
                .strategyId(strategyId)
                .strategyVersion(strategyVersion)
                .userBasicConfigBO(userBasicConfigBO)
                .crowdConfig(crowdConfig)
                .oldEntity(estimationResultRecord)
                .notInitBefore(notInitFinishBefore)
                .strategyTypeEnum(EstimationStrategyTypeEnum.STRATEGY)
                .build();
    }

    /**
     * 未初始化完成
     *
     * @param estimationResultRecord
     * @return
     */
    private static boolean isNotInitFinishBefore(UserEstimationResultRecordAggregateRoot estimationResultRecord) {
        return estimationResultRecord == null || estimationResultRecord.getUserEstimationPrepareEntity() == null
                || !estimationResultRecord.getUserEstimationPrepareEntity().userBasicDataReady();
    }


    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 获取执行配置必要信息携带
        EstimationCrowdInfoBatchExecuteEvent executeEvent = (EstimationCrowdInfoBatchExecuteEvent) event;
        EstimationStrategyAggregateRoot strategyAggregateRoot = executeEvent.getStrategyAggregateRoot();
        // 获取人群&基期配置
        Map<Long, IndicatorDO> baseIndicatorMap = executeEvent.getBaseIndicatorMap();
        EstimationCrowdPullBasicData crowdPullBasicData =
                new EstimationCrowdPullBasicData(strategyAggregateRoot.getCrowdConfigBO(),
                        strategyAggregateRoot.getBasicConfigBO(), executeEvent.getCustomizeBasic(),
                        baseIndicatorMap, executeEvent.getJsonKeyMap(),
                        strategyAggregateRoot.getBizDate(), strategyAggregateRoot.getPeriodConfigBO().getPeriodNum());
        crowdPullBasicData.setAutoEstimateFlag(setEstimateType(0, AutoEstimationTypeEnum.NO_AUTO));
        crowdPullBasicData.setPrepareSyncVersion(executeEvent.getPrepareSyncVersion());
        crowdPullBasicData.setStrategyId(executeEvent.getStrategyId());
        crowdPullBasicData.setStrategyVersion(executeEvent.getStrategyVersion());
        StrategyIndicatorConfigBO indicatorConfigBO = strategyAggregateRoot.getIndicatorConfigBO();
        if (indicatorConfigBO != null) {
            // 构造测算指标配置
            crowdPullBasicData.setEstimationIndicatorConfigMap(indicatorConfigBO.buildEstimationIndicatorConfig(baseIndicatorMap));
        }
        return toJSON(crowdPullBasicData);
    }

    @Override
    protected boolean filterSuccessUser() {
        return true;
    }

    @Override
    int getPartitionSize() {
        return estimationCrowdPartitionSize.get();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.ESTIMATION_CROWD_PREPARE;
    }
}
