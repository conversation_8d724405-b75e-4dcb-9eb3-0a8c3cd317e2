package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.template;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldResolveContextBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFieldResolveResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchFixContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model.LaunchTemplateCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.LaunchFieldResolveStrategy;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchContentFieldConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchContentPlaceHolderConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchContentTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.service.LaunchResolveService;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
@Component
@Slf4j
public class LaunchTemplateContentResolveStrategy implements LaunchFieldResolveStrategy {

    private static final String PLACE_HOLDER_TEMPLATE = "${%s}";

    @Autowired
    private LaunchResolveService launchResolveService;

    @Override
    public LaunchFieldResolveResultBO resolve(LaunchFieldResolveContextBO context) {
        Map<String, Object> result = new HashMap<>();
        LaunchContentFieldConfigBO fieldConfig = context.getFieldConfig();
        String fieldCode = fieldConfig.getFieldCode();
        Integer contentType = fieldConfig.getContentType();
        LaunchConfigBO launchConfig = context.getFieldAssembleContext().getLaunchConfig();

        String contentValue = fieldConfig.getContentValue();
        if (StringUtils.isBlank(contentValue)) {
            result.put(fieldCode, StringUtils.EMPTY);
            return LaunchFieldResolveResultBO.builder().resMap(result).build();
        }

        LaunchFixContentBO launchFixContent =
                ObjectMapperUtils.fromJSON(contentValue, LaunchFixContentBO.class);

        String content = StringUtils.defaultString(launchFixContent.getFixContent(), "");
        if (StringUtils.isBlank(content)) {
            result.put(fieldCode, StringUtils.EMPTY);
            return LaunchFieldResolveResultBO.builder().resMap(result).build();
        }
        // 获取对应模板配置 todo wyl 把占位符模版加到map中
        List<LaunchContentPlaceHolderConfigBO> launchContentPlaceHolderConfigs =
                launchResolveService.resolveContentPlaceHolderConfig(launchConfig.getChannel(), launchConfig.getScene(), fieldCode, contentType);
        if (CollectionUtils.isEmpty(launchContentPlaceHolderConfigs)) {
            result.put(fieldCode, content);
            return LaunchFieldResolveResultBO.builder().resMap(result).build();
        }
        Map<String, String> placeHolderMap = new HashMap<>();
        for (LaunchContentPlaceHolderConfigBO launchContentPlaceHolderConfig : launchContentPlaceHolderConfigs) {
            String placeHolder = launchContentPlaceHolderConfig.getPlaceHolder();
            LaunchTemplateCodeEnum templateCodeEnum = LaunchTemplateCodeEnum.getByCode(placeHolder);
            switch (templateCodeEnum) {
                case ACTIVITY_ID:
                    placeHolderMap.put(placeHolder, String.valueOf(launchConfig.getActivityId()));
                    break;
                default:
                    throw new BizException(BasicErrorCode.SERVER_ERROR, "不支持的占位符类型");
            }
        }
        // 替换占位符
        content = resolveTemplateContent(content, placeHolderMap);
        result.put(fieldCode, content);
        return LaunchFieldResolveResultBO.builder().resMap(result).build();
    }

    private String resolveTemplateContent(String contentValue, Map<String, String> placeHolderMap) {
        if (MapUtils.isEmpty(placeHolderMap)) {
            return contentValue;
        }
        for (Map.Entry<String, String> placeHolderEntry : placeHolderMap.entrySet()) {
            String namePlaceHolder = String.format(PLACE_HOLDER_TEMPLATE, placeHolderEntry.getKey());
            if (contentValue.contains(namePlaceHolder)) {
                contentValue = contentValue.replace(namePlaceHolder, placeHolderEntry.getValue());
            }
        }
        return contentValue;
    }



    @Override
    public LaunchContentTypeEnum getContentType() {
        return LaunchContentTypeEnum.TEMPLATE;
    }
}
