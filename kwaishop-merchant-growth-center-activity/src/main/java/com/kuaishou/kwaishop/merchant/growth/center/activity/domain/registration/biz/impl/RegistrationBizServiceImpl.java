package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.DYNAMIC_EVENT_DRAW_SIGN_UP_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.crowdEventConfigList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.dynamicDrawOptionWithCodeSpecifyConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils.checkExcelContentValid;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.PARAM_INVALID;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.CrowdEventBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.CrowdEventRuleBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.CrowdEventRuleConditionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.biz.RegistrationBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RegistrationDrawRule;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.RegistrationDrawRuleField;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationRuleOperatorEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.RegistrationConfigStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.TaskCrowdTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ExcelUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.RegistrationConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.registration.RegistrationConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.registration.UpdateOneUserActivityRegistrationRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawRuleCondition;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.DynamicDrawRuleDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.QueryDynamicDrawOptionRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-20
 */
@Service
@Slf4j
@Lazy
public class RegistrationBizServiceImpl implements RegistrationBizService {

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private RegistrationConfigDAO registrationConfigDAO;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Override
    public void customizeSetActivityUserBasicData(long activityId) {
        // 获取基值Excel地址
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null) {
            log.error("[自定义用户活动基值] 无效活动ID，activityId:{}", activityId);
            throw new BizException(BasicErrorCode.PARAM_INVALID, "无效活动ID");
        }
        if (StringUtils.isBlank(activityDO.getCrowdConfig())) {
            log.error("[自定义用户活动基值] 人群配置为空，activityId:{}", activityId);
            throw new BizException(BasicErrorCode.PARAM_INVALID, "人群配置为空");
        }
        CrowdConfigBO crowdConfigBO = fromJSON(activityDO.getCrowdConfig(), CrowdConfigBO.class);
        String excelFileUrl = crowdConfigBO.getCustomizeBasicDataFileUrl();
        // 解析Excel
        List<List<Object>> rows = ExcelUtils.parseFileFromCdnUrl(excelFileUrl);
        // 校验内容
        checkExcelContentValid(rows);
        // 表头
        List<String> heads =
                rows.get(0).stream().map(Object::toString).map(e -> e.split("-")[0]).collect(Collectors.toList());
        // 循环覆盖
        for (int row = 1; row < rows.size(); row++) {
            List<Object> cols = rows.get(row);
            long userId = Long.parseLong(String.valueOf(cols.get(0)));
            Map<String, Object> inputBasicData = Maps.newHashMap();
            for (int col = 1; col < cols.size(); col++) {
                //
                inputBasicData.put(heads.get(col), cols.get(col));
            }
            userRegistrationService.coverUserBasicData(userId, activityId, inputBasicData);
        }
    }

    @Override
    public boolean calcMatchRegistration(RegistrationConfigDO registrationConfig,
            List<DynamicDrawRuleDTO> facts) {
        if (StringUtils.isBlank(registrationConfig.getDrawRule())) {
            return true;
        }
        RegistrationDrawRule drawRule = fromJSON(registrationConfig.getDrawRule(), RegistrationDrawRule.class);
        if (drawRule == null || CollectionUtils.isEmpty(drawRule.getFields())) {
            return true;
        }
        if (CollectionUtils.isEmpty(facts)) {
            return false;
        }
        if (!checkMatchRegistration(drawRule, facts)) {
            log.error("[判断准入条件] 准入配置不合法，activityId:{}", registrationConfig.getActivityId());
            perfException(DYNAMIC_EVENT_DRAW_SIGN_UP_ACTIVITY, String.valueOf(registrationConfig.getActivityId()),
                    "param.fail");
            return false;
        }
        List<Boolean> ruleResult = drawRule.getFields().stream().map(field -> calcRuleExpression(field, facts))
                .collect(Collectors.toList());
        return calcResultByOperator(ruleResult, drawRule.getOperator());
    }

    public boolean checkMatchRegistration(RegistrationDrawRule drawRule, List<DynamicDrawRuleDTO> facts) {
        for (RegistrationDrawRuleField field : drawRule.getFields()) {
            if (StringUtils.isEmpty(field.getField()) || CollectionUtils.isEmpty(field.getConditions())
                    || RegistrationRuleOperatorEnum.UNKNOWN.getValue().equals(field.getOperator())) {
                return false;
            }
            List<String> effectConditions = field.getConditions().stream().filter(StringUtils::isNotBlank).collect(
                    Collectors.toList());
            if (CollectionUtils.isEmpty(effectConditions)) {
                return false;
            }
        }
        for (DynamicDrawRuleDTO fact : facts) {
            if (StringUtils.isEmpty(fact.getField()) || CollectionUtils.isEmpty(fact.getFieldOptionsList())) {
                return false;
            }
        }
        return true;
    }

    public boolean calcRuleExpression(RegistrationDrawRuleField ruleField, List<DynamicDrawRuleDTO> facts) {
        List<Boolean> matchResults = Lists.newArrayList();
        for (String condition : ruleField.getConditions()) {
            Boolean conditionResult = facts.stream()
                    .anyMatch(fact -> ruleField.getField().equals(fact.getField()) && CollectionUtils.isNotEmpty(
                            fact.getFieldOptionsList()) && matchCondition(fact.getFieldOptionsList(), condition));
            matchResults.add(conditionResult);
        }
        return calcResultByOperator(matchResults, ruleField.getOperator());
    }

    public boolean matchCondition(List<DynamicDrawRuleCondition> options, String condition) {
        for (DynamicDrawRuleCondition option : options) {
            if (condition.equals(option.getConditionCode())) {
                return true;
            }
        }
        return false;
    }

    public boolean calcResultByOperator(List<Boolean> matchResults, String ruleOperatorStr) {
        RegistrationRuleOperatorEnum ruleOperator = RegistrationRuleOperatorEnum.of(ruleOperatorStr);
        // 默认and操作符
        switch (ruleOperator) {
            case UNKNOWN:
            case AND_OPERATOR:
                return matchResults.stream().allMatch(b -> b);
            case OR_OPERATOR:
                return matchResults.stream().anyMatch(b -> b);
            default:
                break;
        }
        return false;
    }

    @Override
    public List<DynamicDrawRuleDTO> queryDynamicDrawOption(QueryDynamicDrawOptionRequest request) {
        long activityId = request.getActivityId();
        String eventCode = request.getCode();

        if (activityId <= 0 && StringUtils.isBlank(eventCode)) {
            throw new BizException(PARAM_INVALID);
        }

        List<DynamicDrawRuleDTO> res = Lists.newArrayList();
        // 获取最新上线关联 eventCode 活动
        activityId = activityId > 0 ? activityId : getLatestActivityByEventCode(eventCode);
        if (activityId == 0L) {
            return res;
        }

        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null) {
            log.error("[获取问卷信息] 活动为空 activityId:{}", activityId);
            return res;
        }
        List<Integer> effectiveStatusList =
                Arrays.asList(ActivityStatusEnum.EFFECTIVE.getCode(), ActivityStatusEnum.ONLINE.getCode());
        if (!effectiveStatusList.contains(activityDO.getStatus())) {
            return res;
        }
        if (!ActivityResolver.isCanRegistration(activityDO)) {
            log.info("[获取问卷信息] 活动已结束 activityId:{}", activityId);
            return res;
        }
        //查询activityId对应的所有registrtionConfig
        List<RegistrationConfigDO> registrationConfigList =
                registrationConfigDAO.queryRegistrationConfigByActivityId(activityId);
        if (CollectionUtils.isEmpty(registrationConfigList)) {
            log.info("[获取问卷信息] 活动对应无问卷信息 activityId:{}", activityId);
            return res;
        }
        //将drawRule按code聚合
        Map<String, List<RegistrationDrawRuleField>> drawRuleMap = Maps.newHashMap();
        registrationConfigList.forEach(registrationConfig -> {
            String code = registrationConfig.getCode();
            if (StringUtils.isEmpty(code)) {
                return;
            }
            String drawRule = registrationConfig.getDrawRule();
            if (StringUtils.isEmpty(drawRule)) {
                return;
            }
            RegistrationDrawRule rule = fromJSON(drawRule, RegistrationDrawRule.class);
            if (rule == null || CollectionUtils.isEmpty(rule.getFields())) {
                return;
            }
            List<RegistrationDrawRuleField> drawRuleFields = drawRuleMap.getOrDefault(code, Lists.newArrayList());
            //获取并追加活动关联的所有drawRuleFields
            drawRuleFields = getActivityDynamicDrawRuleFields(rule.getFields(), drawRuleFields);
            drawRuleMap.put(code, drawRuleFields);
        });

        return convertToDynamicDrawRuleDTO(drawRuleMap);
    }

    @Override
    public void updateUserRegistrationJsonData(UpdateOneUserActivityRegistrationRequest request) {
        long activityId = request.getActivityId();
        long userId = request.getUserId();
        long entityId = request.getEntityId();
        int entityType = request.getEntityType();
        String jsonData = request.getJsonData();
        if (activityId <= 0 || userId <= 0 || entityId <= 0 || entityType <= 0 || StringUtils.isBlank(jsonData)) {
            log.error("[更新用户报名信息] 参数不合法 activityId:{}, userId:{}, entityId:{}, entityType:{}, jsonData:{}", activityId,
                    userId, entityId, entityType, jsonData);
            throw new BizException(PARAM_INVALID);
        }

        UserRegistrationRecordDO registrationRecordDO = userRegistrationRecordDAO.queryRecordByUnique(activityId,
                userId, EntityTypeEnum.getByCode(entityType), entityId, false);
        if (registrationRecordDO == null) {
            log.error("[更新用户资格信息] 用户资格记录不存在 activityId:{}, userId:{}, entityId:{}, entityType:{}", activityId, userId
                    , entityId, entityType);
            throw new BizException(PARAM_INVALID);
        }
        registrationRecordDO.setJsonData(jsonData);
        userRegistrationRecordDAO.updateUserRegistrationRecordById(registrationRecordDO);
    }

    public Long getLatestActivityByEventCode(String eventCode) {
        // 优先获取指定 activity
        Map<String, String> specifyMap = dynamicDrawOptionWithCodeSpecifyConfig.getMap();
        if (MapUtils.isNotEmpty(specifyMap) && specifyMap.containsKey(eventCode)) {
            return Long.parseLong(specifyMap.get(eventCode));
        }

        List<RegistrationConfigDO> registrationConfigs = registrationConfigDAO.queryRegistrationConfigByCode(
                eventCode, TaskCrowdTypeEnum.EVENT.getCode(), RegistrationConfigStatusEnum.EFFECT.getCode(),
                System.currentTimeMillis());
        if (CollectionUtils.isEmpty(registrationConfigs)) {
            return 0L;
        }
        // 过滤配置了对应code活动ID列表
        List<Long> activityIds = registrationConfigs.stream()
                .filter(config -> Objects.equals(EntityTypeEnum.TASK.getCode(), config.getEntityType()))
                .map(RegistrationConfigDO::getActivityId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(activityIds)) {
            return 0L;
        }

        // 过滤已上线 & 最新配置活动
        Map<Long, ActivityDO> activityMap = activityLocalCacheService.batchQueryActivityInfo(activityIds);
        ActivityDO selectedActivity = activityMap.values().stream()
                .filter(activity -> Objects.equals(activity.getStatus(), ActivityStatusEnum.ONLINE.getCode()))
                .max(Comparator.comparingLong(ActivityDO::getId)).orElse(null);
        if (selectedActivity == null) {
            return 0L;
        }

        return selectedActivity.getId();
    }

    private List<DynamicDrawRuleDTO> convertToDynamicDrawRuleDTO(
            Map<String, List<RegistrationDrawRuleField>> drawRuleFieldMap) {
        List<DynamicDrawRuleDTO> res = Lists.newArrayList();
        if (MapUtils.isEmpty(drawRuleFieldMap)) {
            return res;
        }
        //从KConf上拉取所有crowdEvent配置
        List<CrowdEventBO> crowdEvents = crowdEventConfigList.getList();
        if (CollectionUtils.isEmpty(crowdEvents)) {
            return res;
        }
        //将拉取下来的配置按照事件code聚合
        Map<String, CrowdEventBO> crowdEventsMap =
                crowdEvents.stream()
                        .collect(Collectors.toMap(CrowdEventBO::getCode, Function.identity(), (k1, k2) -> k1));
        drawRuleFieldMap.forEach((code, drawRuleFields) -> {
            CrowdEventBO crowdEvent = crowdEventsMap.get(code);
            if (crowdEvent == null) {
                return;
            }
            List<CrowdEventRuleBO> rules = crowdEvent.getRules();
            if (CollectionUtils.isEmpty(rules)) {
                return;
            }
            //将KConf上获取到的rules配置按field聚合
            Map<String, CrowdEventRuleBO> crowdEventRuleMap = rules.stream()
                    .collect(Collectors.toMap(CrowdEventRuleBO::getField, Function.identity(), (k1, k2) -> k1));
            drawRuleFields.forEach(drawRuleField -> {
                List<DynamicDrawRuleCondition> fieldOptions = Lists.newArrayList();
                String field = drawRuleField.getField();
                List<String> drawRuleFieldConditions = drawRuleField.getConditions();
                CrowdEventRuleBO crowdEventRule = crowdEventRuleMap.get(field);
                if (crowdEventRule == null) {
                    return;
                }
                List<CrowdEventRuleConditionBO> conditions = crowdEventRule.getConditions();
                //将KConf上拉取到的conditions按conditionCode聚合
                Map<String, String> conditionMap = conditions.stream().collect(
                        Collectors.toMap(CrowdEventRuleConditionBO::getCode, CrowdEventRuleConditionBO::getName));
                drawRuleFieldConditions.forEach(condition -> {
                    DynamicDrawRuleCondition dynamicDrawRuleCondition = DynamicDrawRuleCondition.newBuilder()
                            .setConditionCode(condition)
                            .setConditionName(conditionMap.getOrDefault(condition, ""))
                            .build();
                    fieldOptions.add(dynamicDrawRuleCondition);
                });
                DynamicDrawRuleDTO dynamicDrawRule = DynamicDrawRuleDTO.newBuilder()
                        .setField(field)
                        .setDesc(crowdEventRule.getTitle())
                        .addAllFieldOptions(fieldOptions)
                        .build();
                res.add(dynamicDrawRule);
            });
        });

        return res;
    }

    private List<RegistrationDrawRuleField> getActivityDynamicDrawRuleFields(
            /*要追加的ruleFields*/List<RegistrationDrawRuleField> ruleFields,
            /*已处理好的ruleFields*/ List<RegistrationDrawRuleField> drawRuleFields) {
        if (CollectionUtils.isEmpty(ruleFields)) {
            return drawRuleFields;
        }
        Map<String, RegistrationDrawRuleField> drawRuleFieldMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(drawRuleFields)) {
            //按field字段聚合
            drawRuleFieldMap = drawRuleFields.stream()
                    .collect(
                            Collectors.toMap(RegistrationDrawRuleField::getField, Function.identity(), (k1, k2) -> k1));
        }
        Map<String, RegistrationDrawRuleField> finalDrawRuleFieldMap = drawRuleFieldMap;
        ruleFields.forEach(ruleField -> {
            String field = ruleField.getField();
            RegistrationDrawRuleField registrationDrawRuleField = finalDrawRuleFieldMap.get(field);
            if (registrationDrawRuleField == null) {
                registrationDrawRuleField = new RegistrationDrawRuleField();
                registrationDrawRuleField.setField(field);
            }
            List<String> drawRuleConditions = registrationDrawRuleField.getConditions();
            if (CollectionUtils.isEmpty(drawRuleConditions)) {
                drawRuleConditions = Lists.newArrayList();
            }
            List<String> ruleConditions = ruleField.getConditions();
            if (CollectionUtils.isEmpty(ruleConditions)) {
                return;
            }
            drawRuleConditions.addAll(ruleConditions);
            //对相同的condition进行去重
            drawRuleConditions = drawRuleConditions.stream().distinct().collect(Collectors.toList());
            registrationDrawRuleField.setConditions(drawRuleConditions);
            finalDrawRuleFieldMap.put(field, registrationDrawRuleField);
        });
        if (MapUtils.isEmpty(finalDrawRuleFieldMap)) {
            return drawRuleFields;
        }
        List<RegistrationDrawRuleField> res = Lists.newArrayList();
        finalDrawRuleFieldMap.forEach((key, value) -> res.add(finalDrawRuleFieldMap.get(key)));
        return res;
    }
}
