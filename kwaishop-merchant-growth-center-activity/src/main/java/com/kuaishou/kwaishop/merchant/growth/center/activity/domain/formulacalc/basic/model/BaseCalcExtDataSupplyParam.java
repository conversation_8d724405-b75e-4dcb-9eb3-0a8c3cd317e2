package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.basic.model.enums.BaseCalcExtDataTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseCalcExtDataSupplyParam {

    private Long userId;

    private IndicatorConfigDO indicatorConfig;

    private BaseCalcExtDataTypeEnum type;

    private Map<String, Object> inputDataMap;
}
