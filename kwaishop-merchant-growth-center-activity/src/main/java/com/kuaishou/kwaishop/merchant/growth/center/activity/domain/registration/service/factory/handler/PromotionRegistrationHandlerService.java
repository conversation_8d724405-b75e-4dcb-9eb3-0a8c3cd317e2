package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.handler;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.ActivityRegistrationHandleService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.custom.ManualDrawSignUpActivityRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-23
 */

@Slf4j
@Lazy
@Service
public class PromotionRegistrationHandlerService implements ActivityRegistrationHandleService {

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    public void handleManualDrawSignUp(ManualDrawSignUpActivityRequest request) {
    }

    @Override
    public boolean handleAddUserRegistration(long userId, long activityId, List<Long> parentTaskIds, String source,
            Map<Long, AddRegistrationOptionBO> optionMap) {
        return true;
    }

    @Override
    public void handleRiskInterruptRegistration(Long userId, Long activityId, String riskReason) {
    }


    @Override
    public ActivitySeriesType getActivitySeriesType() {
        return ActivitySeriesType.ACTIVITY_YEAR_GOODS;
    }
}
