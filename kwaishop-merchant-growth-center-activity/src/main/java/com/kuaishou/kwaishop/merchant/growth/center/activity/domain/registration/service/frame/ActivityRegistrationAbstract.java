package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.frame;

import static com.kuaishou.framework.concurrent.DynamicThreadExecutor.dynamic;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationLocalStatusEnum.REGISTRATION_IGNORE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationLocalStatusEnum.REGISTRATION_SUCCESS;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.ACTIVITY_SHARD_DATA_SOURCE_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.MYSQL_SHARD_CONFIG_KCONF_PATH_PARTTERN;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.TableNameConstants.TABLE_NAME_MERCHANT_ACTIVITY_LAYER_DATA_LOCAL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.REGISTRATION_SIGN_TASK;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.REGISTRATION_QUERY_TASK_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.activityRegistrationStopSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.activityDefaultTimeoutSeconds;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.registrationTaskThreadSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerListConfigKey.registrationLocalStatusList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.registrationBlackMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.alreadyRegistrationActivityFilterList;
import static com.kuaishou.kwaishop.merchant.growth.center.client.common.enums.MerchantGrowthResponseEnum.SERVER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationCacheBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.MerchantActivityLayerDataLocalDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.MysqlShardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.MysqlShardConfigBO.TableShardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.MerchantActivityLayerDataLocalDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.selection.seller.service.client.client.KwaishopSelectionSellerClient;
import com.kuaishou.kwaishop.selection.seller.service.protobuf.ExistEntity;
import com.kuaishou.kwaishop.selection.seller.service.protobuf.QueryExistRequest;
import com.kuaishou.kwaishop.selection.seller.service.protobuf.QueryExistResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;
import com.kuaishou.old.exception.ServiceException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-18
 */
@Slf4j
public abstract class ActivityRegistrationAbstract<T> {

    private static final String RISK_CALLER = "BONUS_SELLER";

    protected ConcurrentHashMap<String, Long> taskAliasMap = new ConcurrentHashMap<>();

    protected ConcurrentHashMap<String, Long> activityAliasMap = new ConcurrentHashMap<>();

    @Autowired
    private MerchantActivityLayerDataLocalDAO merchantActivityLayerDataLocalDAO;

    @Autowired
    private KwaishopSelectionSellerClient selectionGroupClient;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private RiskControlFetchService riskControlFetchService;


    public void start(String tag) {
        ExecutorService executor = dynamic(registrationTaskThreadSize::get, "signupMerchantLevelGmvData-job");
        try {
            int mysqlShardCount = getMysqlShardCount();
            List<Integer> statusList = registrationLocalStatusList.get();
            for (int shardIndex = 0; shardIndex < mysqlShardCount; shardIndex++) {
                log.info("[分区执行开始] shardIndex:{}", shardIndex);
                merchantActivityLayerDataLocalDAO.cursorGetLayerData(shardIndex, tag, statusList)
                        .forEach(layerData -> {
                            if (activityRegistrationStopSwitch.get()) {
                                log.error("[用户报名快速停止]");
                                throw new BizException(BasicErrorCode.SERVER_ERROR);
                            }
                            executor.submit(() -> processor(layerData));
                        });
                log.info("[分区执行结束] shardIndex:{}", shardIndex);
            }
            shutAndWaitExecutors(executor);
        } catch (Exception e) {
            log.error("[registrationStart exception]", e);
        }
    }


    // 报名处理器
    public void processor(MerchantActivityLayerDataLocalDO dataDO) {

        long userId = dataDO.getUserId();
        String tag = dataDO.getTag();
        perfScene(REGISTRATION_SIGN_TASK, tag, "报名人数");
        // 参数校验
        if (userId < 1 || StringUtils.isEmpty(tag)) {
            merchantActivityLayerDataLocalDAO.updateStatus(tag, userId, REGISTRATION_IGNORE.getCode());
            log.error("[用户报名参数错误]，userId:{},tag:{}", userId, tag);
            return;
        }
        try {
            T paramObject = convert(dataDO.getJsonData());

            // 1，报名过滤
            if (filter(userId, paramObject)) {
                log.info("[用户报名被过滤]，userId:{},tag:{}", userId, tag);
                perfScene(REGISTRATION_SIGN_TASK, tag, "报名过滤");
                merchantActivityLayerDataLocalDAO.updateStatus(tag, userId, REGISTRATION_IGNORE.getCode());
                return;
            }
            // 2，获取将要报名的活动id
            Long activityId = getActivityId(userId, paramObject);

            if (alreadyRegistrationActivityFilter(userId, activityId)) {
                log.info("[用户已报名活动过滤]，userId:{},tag:{},activity:{}", userId, tag, activityId);
                perfScene(REGISTRATION_SIGN_TASK, tag, "报名过滤");
                merchantActivityLayerDataLocalDAO.updateStatus(tag, userId, REGISTRATION_IGNORE.getCode());
                return;
            }

            // 3，获取将要报名的任务id
            List<Long> taskIdList = getTaskIdList(userId, paramObject, activityId);

            if (activityId < 1 || CollectionUtils.isEmpty(taskIdList)) {
                log.error("[报名活动信息不存]，activityId:{},taskIdList:{}", activityId, ObjectMapperUtils.toJSON(taskIdList));
                throw new BizException(REGISTRATION_QUERY_TASK_ERROR);
            }

            // 4，报名任务
            registration(userId, activityId, taskIdList, dataDO.getJsonData());

            merchantActivityLayerDataLocalDAO.updateStatus(tag, userId, REGISTRATION_SUCCESS.getCode());
            perfScene(REGISTRATION_SIGN_TASK, tag, "报名成功");
        } catch (Exception e) {
            log.error("[用户报名失败]，userId:{},tag:{}", userId, tag, e);
            perfScene(REGISTRATION_SIGN_TASK, tag, "报名失败");
        }
    }

    // 仅适用于报名活动一级子任务，后续其它场景需要扩展
    protected boolean registration(long userId, long activityId, List<Long> taskIdList, String jsonData) {
        boolean registrationActivity = registrationActivity(userId, activityId, jsonData);
        boolean registrationTask = registrationTask(userId, activityId, taskIdList);
        return registrationActivity && registrationTask;
    }

    private boolean registrationActivity(long userId, long activityId, String jsonData) {
        List<UserRegistrationRecordBO> activityBOList = Lists.newArrayList();
        UserRegistrationRecordBO activityRecordBO = new UserRegistrationRecordBO();
        activityRecordBO.setUserId(userId);
        activityRecordBO.setActivityId(activityId);
        activityRecordBO.setEntityType(EntityTypeEnum.ACTIVITY);
        activityRecordBO.setEntityId(activityId);
        activityRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
        activityRecordBO.setRegistrationTime(System.currentTimeMillis());
        activityRecordBO.setOperator("System");
        activityRecordBO.setSource("layerRegistration");
        activityRecordBO.setJsonData(jsonData);
        activityBOList.add(activityRecordBO);
        return userRegistrationService.batchInsertUserRegistrationRecord(activityBOList);
    }

    private boolean registrationTask(long userId, long activityId, List<Long> taskIdList) {
        List<UserRegistrationRecordBO> taskBOList = Lists.newArrayList();
        for (long taskId : taskIdList) {
            UserRegistrationRecordBO taskRecordBO = new UserRegistrationRecordBO();
            taskRecordBO.setUserId(userId);
            taskRecordBO.setActivityId(activityId);
            taskRecordBO.setEntityType(EntityTypeEnum.TASK);
            taskRecordBO.setEntityId(taskId);
            taskRecordBO.setStatus(UserRegistrationStatusEnum.VALID.getCode());
            taskRecordBO.setRegistrationTime(System.currentTimeMillis());
            taskRecordBO.setOperator("System");
            taskRecordBO.setSource("layerRegistration");
            taskBOList.add(taskRecordBO);
        }
        return userRegistrationService.batchInsertUserRegistrationRecord(taskBOList);
    }

    /**
     * 是否对已报名活动的用户做过滤
     */
    protected boolean alreadyRegistrationActivityFilter(long userId, long activityId) {
        List<Long> activityIdList = alreadyRegistrationActivityFilterList.get();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return false;
        }
        if (!activityIdList.contains(activityId)) {
            return false;
        }

        List<UserRegistrationCacheBO> userRegistrationCache =
                userRegistrationService.getUserRegistrationCache(userId, activityId);
        if (CollectionUtils.isNotEmpty(userRegistrationCache)) {
            return true;
        }
        return false;
    }

    /**
     * 参数类型转换器
     */
    public abstract T convert(String param) throws Exception;

    /**
     * 报名过滤
     */
    public abstract boolean filter(long userId, T paramObject);

    /**
     * 获取报名活动id逻辑，子类实现
     */
    protected abstract Long getActivityId(long userId, T paramObject);

    /**
     * 获取报名任务id逻辑，子类实现
     */
    protected abstract List<Long> getTaskIdList(long userId, T paramObject, long activityId);

    /**
     * 根据表名获取对应的分片数量
     */
    protected int getMysqlShardCount() {
        String shardConfigJson = Kconfs.ofString(String.format(MYSQL_SHARD_CONFIG_KCONF_PATH_PARTTERN,
                ACTIVITY_SHARD_DATA_SOURCE_NAME), StringUtils.EMPTY).build().get();
        if (StringUtils.isBlank(shardConfigJson)) {
            return 0;
        }
        MysqlShardConfigBO mysqlShardConfigBO = fromJSON(shardConfigJson, MysqlShardConfigBO.class);
        TableShardConfigBO tableShardConfig = MapUtils.getObject(mysqlShardConfigBO.getLogicTableRules(),
                TABLE_NAME_MERCHANT_ACTIVITY_LAYER_DATA_LOCAL, new TableShardConfigBO());
        return tableShardConfig.getTotalShardNumber() == null ? 0 : tableShardConfig.getTotalShardNumber();
    }

    /**
     * 判断商家id是否在黑名单圈选列表中
     */
    protected boolean inBlackList(long userId, String tag) {
        Map<String, Long> blackMap = registrationBlackMap.getMap(String.class, Long.class);
        // 黑名单过滤
        Long groupId = blackMap.get(tag);
        if (groupId == null) {
            // 未配置黑名单过滤，直接返回
            return false;
        }

        QueryExistRequest request = QueryExistRequest.newBuilder()
                .setGroupId(groupId)
                .addObjectId(userId)
                .build();
        QueryExistResponse response =
                selectionGroupClient.queryExist(request, activityDefaultTimeoutSeconds.get() * 1000L);
        if (response.getBaseResponse().getRespCode() != BasicErrorCode.SUCCESS.getCode()) {
            log.error("[画像黑名单接口失败] groupId={}, userId={}, response={}",
                    groupId, userId, ProtobufUtil.protoToJsonString(response));
            throw ServiceException.ofMessage(SERVER_ERROR.getCode(),
                    SERVER_ERROR.getDesc());
        }
        List<ExistEntity> entityList = response.getExistEntityList();
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.get(0).getExist();
        }
        return true;
    }

    /**
     * 是否在风控黑名单
     */
    protected boolean riskBlackList(long userId) {
        return riskControlFetchService.inRiskBlackList(userId);
    }

    /**
     * 通过alias获取任务ID
     */
    protected List<Long> getTaskIdByAlias(List<String> aliasList) {
        List<Long> taskIds = new ArrayList<>();
        for (String alias : aliasList) {
            TaskDO task = taskLocalCacheService.getTaskByAlias(alias);
            if (task == null) {
                log.error("[未查询到任务信息] getTaskIdByAlias, alias:{}", alias);
                throw new BizException(REGISTRATION_QUERY_TASK_ERROR);
            }
            taskIds.add(task.getId());
            taskAliasMap.put(alias, task.getId());
        }
        return taskIds;
    }

    /**
     * 等待线程池处理结束 会阻塞主线程; 线程池结束后不能复用 需要重新创建
     */
    private void shutAndWaitExecutors(ExecutorService executorService) {
        executorService.shutdown();
        while (true) {
            if (executorService.isTerminated()) {
                break;
            }
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 清理本地缓存
     */
    public void clearMap() {
        taskAliasMap.clear();
        activityAliasMap.clear();
    }


    /**
     * 报名tag
     */
    public abstract String getTag();
}
