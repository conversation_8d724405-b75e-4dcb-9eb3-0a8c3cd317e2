package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.longtask;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.ExportFileBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.LongTaskConstants;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.statistics.ExportActivityReviewDataRequest;
import com.kuaishou.kwaishop.merchant.operation.longtask.client.execute.handler.BaseHandler;
import com.kuaishou.kwaishop.merchant.operation.longtask.client.execute.handler.ExecuteContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-07
 */
@Slf4j
@Component
public class ExportActivityReviewHandler implements BaseHandler {


    @Autowired
    private StatisticsReviewService statisticsReviewService;

    @Override
    public String bizCode() {
        return ExportFileBizTypeEnum.ACTIVITY_REVIEW.getLongTaskBizCode();
    }

    @Override
    public Integer type() {
        return LongTaskConstants.BASE_TYPE;
    }

    @Override
    public void execute(ExecuteContext executeContext) {
        String bizExt = executeContext.getBizExt();
        ExportActivityReviewDataRequest reviewDataRequest = fromJSON(bizExt, ExportActivityReviewDataRequest.class);
        statisticsReviewService.exportActivityReviewData(reviewDataRequest.getActivityId(),
                reviewDataRequest.getSubActivityOrder(), reviewDataRequest.getLayer(), reviewDataRequest.getOperator());
    }

    @Override
    public void handleException(ExecuteContext executeContext) {

    }
}
