package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.DISTRIBUTE_LOCK_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.exception.ActivityExceptionCode.NOTIFICATION_PUSH_CREATE_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationCreateCacheLockCASTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.notificationCreateCacheLockMaxWaitTime;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.cache.facade.CacheLock;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationConfigPeriodTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationOccasionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationRedisKeyEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPeriodConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.NotificationPushBasicService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ActivityRedisUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.notification.NotificationPushConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.notification.NotificationPushConfigDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * 基础service
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
@Lazy
@Slf4j
@Service
public class NotificationPushBasicServiceImpl implements NotificationPushBasicService {

    @Autowired
    private NotificationPushConfigDAO notificationPushConfigDAO;

    @Autowired
    private NotificationPeriodConfigService notificationPeriodConfigService;

    @Override
    public boolean createNotificationPushConfig(NotificationPushConfigBO configBO) {
        long entityId = configBO.getEntityId();
        int entityType = configBO.getEntityType();
        int entityStatus = configBO.getEntityStatus();
        int channel = configBO.getChannel();
        int occasion = configBO.getOccasion();
        // 唯一键查询
        NotificationPushConfigDO existsConfigDO = notificationPushConfigDAO
                .getNotificationPushConfigByUnqIdx(entityId, entityType, entityStatus, channel, occasion, "", true);
        if (existsConfigDO != null) {
            throw new BizException(NOTIFICATION_PUSH_CREATE_ERROR, "创建失败，已创建过该推送配置！");
        }
        return concurrentInsert(configBO);
    }

    @Override
    public boolean createOrUpdateNotificationPushConfig(NotificationPushConfigBO configBO) {
        long entityId = configBO.getEntityId();
        int entityType = configBO.getEntityType();
        int entityStatus = configBO.getEntityStatus();
        int channel = configBO.getChannel();
        int occasion = configBO.getOccasion();
        String scene = StringUtils.isEmpty(configBO.getScene()) ? ":" : configBO.getScene();
        // 唯一键查询
        NotificationPushConfigDO existsConfigDO = notificationPushConfigDAO
                .getNotificationPushConfigByUnqIdx(entityId, entityType, entityStatus, channel, occasion, scene, true);
        if (existsConfigDO != null) {
            log.info("[NotificationPushBasicService] notificationConfig already exists! config:{}",
                    ObjectMapperUtils.toJSON(configBO));
            NotificationPushConfigDO configDO = NotificationPushConfigDO.builder().build();
            BeanUtils.copyProperties(configBO, configDO);
            configDO.setId(existsConfigDO.getId());
            configDO.setVersion(existsConfigDO.getVersion());
            if (configBO.getPeriodConfig() != null) {
                configDO.setPeriodConfig(ObjectMapperUtils.toJSON(configBO.getPeriodConfig()));
            }
            return notificationPushConfigDAO.updateSelectiveById(configDO) > 0;
        }
        return concurrentInsert(configBO);
    }

    private boolean concurrentInsert(NotificationPushConfigBO configBO) {
        // 并发锁
        String redisKey;
        if (StringUtils.isEmpty(configBO.getScene())) { //兼容老唯一键
            redisKey = NotificationRedisKeyEnum.CREATE_NOTIFICATION_CONFIG
                    .getFullKeyJoinWithColon(configBO.getEntityId(), Long.valueOf(configBO.getEntityType()),
                            Long.valueOf(configBO.getEntityStatus()), Long.valueOf(configBO.getChannel()),
                            Long.valueOf(configBO.getOccasion()));
        } else { //新唯一键
            redisKey = NotificationRedisKeyEnum.CREATE_NOTIFICATION_CONFIG
                    .getFullKeyJoinWithColon(configBO.getEntityId(), Long.valueOf(configBO.getEntityType()),
                            Long.valueOf(configBO.getEntityStatus()), Long.valueOf(configBO.getChannel()),
                            Long.valueOf(configBO.getOccasion()));
            redisKey = redisKey + ":" + configBO.getScene();
        }

        boolean hasLock = false;
        CacheLock cacheLock = ActivityRedisUtils.getDistributeLock(redisKey, 100);
        try {
            if (cacheLock.tryLock(notificationCreateCacheLockMaxWaitTime.get(),
                    notificationCreateCacheLockCASTime.get())) {
                hasLock = true;
                NotificationPushConfigDO configDO = NotificationPushConfigDO.builder().build();
                BeanUtils.copyProperties(configBO, configDO);
                configDO.setPeriodConfig(ObjectMapperUtils.toJSON(configBO.getPeriodConfig()));
                log.info("[NotificationPushBasicService] configDO:{}", ObjectMapperUtils.toJSON(configDO));
                long insertFLag = notificationPushConfigDAO.saveOrUpdate(configDO);
                if (insertFLag > 0) {
                    configBO.setId(configDO.getId());
                }
                return insertFLag > 0;
            }
            throw new BizException(DISTRIBUTE_LOCK_ERROR);
        } finally {
            if (hasLock) {
                cacheLock.release();
            }
        }
    }

    @Override
    public boolean updateNotificationPushConfig(NotificationPushConfigBO configBO) {
        NotificationPushConfigDO configDO = NotificationPushConfigDO.builder().build();
        configDO.setId(configBO.getId());
        configDO.setModifier(configBO.getModifier());
        if (configBO.getActivityId() > 0) {
            configDO.setActivityId(configBO.getActivityId());
        }
        if (configBO.getEntityId() > 0) {
            configDO.setEntityId(configBO.getEntityId());
        }
        if (configBO.getEntityType() > 0) {
            configDO.setEntityType(configBO.getEntityType());
        }
        if (configBO.getEntityStatus() > 0) {
            configDO.setEntityStatus(configBO.getEntityStatus());
        }
        if (configBO.getChannel() > 0) {
            configDO.setChannel(configBO.getChannel());
        }
        if (configBO.getOccasion() > 0) {
            configDO.setOccasion(configBO.getOccasion());
        }
        if (configBO.getStatus() > 0) {
            configDO.setStatus(configBO.getStatus());
        }
        if (configBO.getVersion() > 0) {
            configDO.setVersion(configBO.getVersion());
        }
        if (StringUtils.isNotBlank(configBO.getTemplateConfig())) {
            configDO.setTemplateConfig(configBO.getTemplateConfig());
        }
        if (configBO.getPeriodConfig() != null) {
            configDO.setPeriodConfig(ObjectMapperUtils.toJSON(configBO.getPeriodConfig()));
        }
        log.info("configDO:{}", ObjectMapperUtils.toJSON(configDO));
        return notificationPushConfigDAO.updateSelectiveById(configDO) > 0;
    }

    @Override
    public boolean updateNotificationConfigStatusByConditions(long activityId, NotificationEntityTypeEnum entityType,
            int entityStatus, NotificationStatusEnum status, String modifier) {
        return notificationPushConfigDAO
                .updateNotificationConfigStatusByConditions(activityId, entityType, entityStatus, status, modifier) > 0;
    }

    @Override
    public boolean batchUpdateNotificationPushConfigStatus(List<Long> configIds, NotificationStatusEnum status,
            String modifier) {
        return notificationPushConfigDAO.batchUpdateNotificationConfigStatus(configIds, status, modifier) > 0;
    }

    @Override
    public boolean batchUpdateNotificationTemplateAndPeriodConfig(List<Long> configIds, String templateConfig,
            String periodConfig, String modifier) {
        return notificationPushConfigDAO
                .batchUpdateNotificationTemplateAndPeriodConfig(configIds, templateConfig, periodConfig, modifier) > 0;
    }

    @Override
    public boolean hardDeleteNotificationPushConfigs(long activityId, NotificationEntityTypeEnum entityType,
            List<Long> entityIdList, int channel) {
        return notificationPushConfigDAO.hardDeleteNotificationPushConfigs(activityId, entityType, entityIdList, channel) > 0;
    }

    @Override
    public int hardDeleteNotificationPushConfigs(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return 0;
        }
        return notificationPushConfigDAO.deleteByIdList(configIdList);
    }

    @Override
    public NotificationPushConfigBO getNotificationPushConfigById(long id, boolean readMaster) {
        NotificationPushConfigDO configDO = notificationPushConfigDAO.getNotificationPushConfigById(id, readMaster);
        return transformNotificationConfigDOToBO(configDO);
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigsByIds(List<Long> ids) {
        List<NotificationPushConfigDO> configDOList = notificationPushConfigDAO.getNotificationPushConfigByIds(ids);
        if (CollectionUtils.isEmpty(configDOList)) {
            return Collections.emptyList();
        }
        return configDOList.stream()
                .map(this::transformNotificationConfigDOToBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfig(long entityId, int entityType, int entityStatus,
            boolean readMaster) {
        List<NotificationPushConfigDO> configDOList = notificationPushConfigDAO
                .getNotificationPushConfigs(entityId, entityType, entityStatus, readMaster);
        if (CollectionUtils.isEmpty(configDOList)) {
            return Collections.emptyList();
        }
        return configDOList.stream()
                .map(this::transformNotificationConfigDOToBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByConditions(long activityId,
            NotificationEntityTypeEnum entityType, int entityStatus, List<Long> entityIdList) {
        return notificationPushConfigDAO
                .getNotificationPushConfigByConditions(activityId, entityType, entityStatus, entityIdList)
                .stream()
                .map(this::transformNotificationConfigDOToBO)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfig(long activityId, long entityId, int entityType) {
        return notificationPushConfigDAO.getNotificationPushConfig(activityId, entityId, entityType).stream()
                .map(this::transformNotificationConfigDOToBO)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByConditions(long activityId, int entityType) {
        return notificationPushConfigDAO.getNotificationPushConfigByConditions(activityId, entityType)
                .stream().map(this::transformNotificationConfigDOToBO)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByActivityId(long activityId) {
        return notificationPushConfigDAO.getNotificationByActivityId(activityId)
                .stream().map(this::transformNotificationConfigDOToBO)
                .collect(Collectors.toList());
    }

    private NotificationPushConfigBO transformNotificationConfigDOToBO(NotificationPushConfigDO configDO) {
        if (configDO == null) {
            return null;
        }
        NotificationPushConfigBO configBO = NotificationPushConfigBO.builder().build();
        BeanUtils.copyProperties(configDO, configBO);
        PeriodConfigBO periodConfigBO =
                ObjectMapperUtils.fromJSON(configDO.getPeriodConfig(), PeriodConfigBO.class);
        configBO.setPeriodConfig(periodConfigBO);
        return configBO;
    }

    @Override
    public long getNotificationPushNextOccasionTime(PeriodConfigBO config, long eventTime) {
        List<NotificationPeriodBO> notificationPeriods = config.getNotificationPeriods();
        if (CollectionUtils.isEmpty(notificationPeriods)) {
            log.error("[NotificationPushBasicService] notificationPeriods is empty");
            return 0;
        }
        // 每个NotificationPeriod计算出一个最近候选时间
        List<Long> latestTimes = new ArrayList<>();
        for (NotificationPeriodBO notificationPeriodBO : notificationPeriods) {
            long latestTime;
            NotificationConfigPeriodTypeEnum periodType =
                    NotificationConfigPeriodTypeEnum.of(notificationPeriodBO.getPeriodType());
            switch (periodType) {
                case ABSOLUTE:
                    latestTime = notificationPeriodConfigService
                            .parseAbsoluteLatestTime(notificationPeriodBO, eventTime);
                    break;
                case RELATIVE:
                    latestTime = notificationPeriodConfigService
                            .parseRelativeLatestTime(notificationPeriodBO, eventTime);
                    break;
                default:
                    log.error("[NotificationPushBasicService] 不支持的推送周期类型，periodType:{}",
                            notificationPeriodBO.getPeriodType());
                    throw new BizException(BasicErrorCode.PARAM_INVALID, "不支持当前推送周期类型");
            }
            latestTimes.add(latestTime);
        }
        // 筛选出最小时间
        return latestTimes.stream().filter(e -> e != 0).min(Long::compareTo).orElse(0L);
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByEntityIds(List<Long> entityIds,
            NotificationEntityTypeEnum entityType, int entityStatus, NotificationChannelEnum channel, String scene) {
        return notificationPushConfigDAO.getNotificationPushConfigByEntityIds(entityIds, entityType,
                        entityStatus, channel, scene).stream()
                .map(this::transformNotificationConfigDOToBO)
                .collect(Collectors.toList());
    }

    @Override
    public List<NotificationPushConfigBO> getNotificationPushConfigByScene(long activityId,
            NotificationChannelEnum channel, String scene, NotificationOccasionEnum occasion) {

        return notificationPushConfigDAO.getNotificationPushConfigByScene(activityId, channel.getVal(), scene,
                occasion.getVal()).stream().map(this::transformNotificationConfigDOToBO).collect(Collectors.toList());
    }
}
