package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户报名缓存
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationCacheBO {
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 实体ID
     */
    private Long entityId;
    /**
     * 实体类型
     */
    private Integer entityType;
    /**
     * 报名时间
     */
    private Long registrationTime;
    /**
     * 用户基础数据
     */
    private String jsonData;
}
