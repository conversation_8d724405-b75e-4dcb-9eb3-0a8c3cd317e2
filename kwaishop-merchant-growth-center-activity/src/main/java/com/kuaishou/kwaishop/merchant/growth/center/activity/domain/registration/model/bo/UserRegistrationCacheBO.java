package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户报名缓存
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationCacheBO {
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 实体ID
     */
    private Long entityId;
    /**
     * 实体类型
     */
    private Integer entityType;
    /**
     * 报名时间
     */
    private Long registrationTime;
    /**
     * 用户基础数据
     * 统一梳理上游消费场景，判断是否需要针对子任务不同json data做统一兼容处理
     * @CodeNote("解析用户报名记录的json数据") 统一标注本次兼容改动逻辑
     */
    private String jsonData;
}
