package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey.merchantBootcampAwardTemplateParamConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongConfigKey.merchantBootcampNotificationLockTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ActivityRedisUtils.lockNxEx;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.LevelFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * 奖励发放-商家训练营通知
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-11
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class AwardNotificationMerchantBootcampParamService extends AbstractNotificationExtendFunctionParamService {

    private static final String IDEMPOTENT_KEY = "merchant:bootcamp:push:%s:%s";

    private final LevelFetchService levelFetchService;

    private final AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(TemplateParamTypeEnum.CAMP_ID);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(NotificationExecuteStatusEnum.EXECUTE)
                .build();

        AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
        if (Objects.isNull(awardConfigDO)) {
            log.error("[商家训练营奖励通知] awardConfigDO为空, 取消通知发送");
            result.setExecuteStatus(NotificationExecuteStatusEnum.STOP);
            return result;
        }

        if (!Objects.equals(awardConfigDO.getAwardType(), AwardTypeEnum.SERVICE_MARKET_COUPON.getCode())) {
            log.info("[商家训练营奖励通知] 非服务市场类型奖励不发送课程通知, awardConfigId:{}", awardConfigDO.getId());
            result.setExecuteStatus(NotificationExecuteStatusEnum.STOP);
            return result;
        }

        String redisKey = String.format(IDEMPOTENT_KEY, awardConfigDO.getActivityId(), userId);
        if (!lockNxEx(redisKey, "1", merchantBootcampNotificationLockTime.get())) {
            log.info("[商家训练营奖励通知] 幂等 userId:{}, awardConfigId:{}", userId, awardConfigDO.getId());
            result.setExecuteStatus(NotificationExecuteStatusEnum.STOP);
            return result;
        }

        int sellerCurrentLevel = levelFetchService.getSellerCurrentLevel(userId);
        String campId =
                merchantBootcampAwardTemplateParamConfig.getMap(Integer.class, String.class).get(sellerCurrentLevel);

        if (StringUtils.isBlank(campId)) {
            log.info("[商家训练营奖励通知] 分层:{} 没有配置对应的课程id，取消通知发送", sellerCurrentLevel);
            result.setExecuteStatus(NotificationExecuteStatusEnum.STOP);
        }

        params.put(TemplateParamTypeEnum.CAMP_ID.getName(), campId);
        result.setTemplateParamMap(params);
        return result;
    }
}
