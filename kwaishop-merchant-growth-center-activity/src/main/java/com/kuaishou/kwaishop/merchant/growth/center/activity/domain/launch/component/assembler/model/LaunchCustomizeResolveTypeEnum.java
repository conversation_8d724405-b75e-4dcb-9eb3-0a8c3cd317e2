package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Arrays;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Getter
@AllArgsConstructor
public enum LaunchCustomizeResolveTypeEnum {

    DEFAULT(0, "默认解析"),
    RULE(1, "规则解析"),
    KFLOW(2, "kflow引擎解析"),
    ;

    private final Integer type;

    private final String desc;

    public static LaunchCustomizeResolveTypeEnum getByType(Integer type) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.getType(), type)).findFirst().orElse(DEFAULT);
    }
}
