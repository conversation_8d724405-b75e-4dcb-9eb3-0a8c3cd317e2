package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.formulacalc.award.handler;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.converter.CalculateFormulaConverter.getIndicatorFactorFetchConfig;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Preconditions;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.calculate.core.ICalcFactorFetchHandler;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.annotation.BizFactor;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.bo.FactorValueFetchContext;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.config.RegistrationBasicValueFetchConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.constants.FormulaCalcConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.BizSceneEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.formulacalculate.model.enums.FactorSourceTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-08
 */
@BizFactor(bizScene = BizSceneEnum.AWARD_CALC, factorSourceType = FactorSourceTypeEnum.basic)
public class AwardCalcRegistrationBasicValueFactorFetchHandler implements ICalcFactorFetchHandler {
    @Override
    public void fillFactorFetchConfig(FactorValueFetchContext context) {
        Map<String, Object> bizParam = context.getBizParam();
        String factorCode = context.getFactorCode();
        Long returnIndicatorId = MapUtils.getLong(bizParam, FormulaCalcConstants.RETURN_INDICATOR_ID);
        String fetchConfig = getIndicatorFactorFetchConfig(returnIndicatorId, factorCode);
        Preconditions.checkArgument(StringUtils.isNotEmpty(fetchConfig),
                String.format("因子配置不存在，factorCode:%s, returnIndicatorId:%s", factorCode, returnIndicatorId));
        RegistrationBasicValueFetchConfig basicValueFetchConfig =
                ObjectMapperUtils.fromJSON(fetchConfig, RegistrationBasicValueFetchConfig.class);
        Long userId = MapUtils.getLong(bizParam, FormulaCalcConstants.USER_ID);
        Preconditions.checkNotNull(userId, "userId不能为空");
        Long activityId = MapUtils.getLong(bizParam, FormulaCalcConstants.ACTIVITY_ID);
        Preconditions.checkNotNull(activityId, "activityId不能为空");
        Long parentTaskId = MapUtils.getLong(bizParam, FormulaCalcConstants.PARENT_TASK_ID);
        Preconditions.checkNotNull(parentTaskId, "parentTaskId不能为空");
        basicValueFetchConfig.setUserId(userId);
        basicValueFetchConfig.setActivityId(activityId);
        basicValueFetchConfig.setParentTaskId(parentTaskId);
        context.setFetchConfig(basicValueFetchConfig);
    }
}
