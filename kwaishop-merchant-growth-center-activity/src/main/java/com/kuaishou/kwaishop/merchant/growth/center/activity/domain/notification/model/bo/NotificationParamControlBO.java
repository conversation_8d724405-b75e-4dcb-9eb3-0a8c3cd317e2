package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationParamControlBO {
    /**
     * 场景
     */
    private String scene;
    /**
     * 文案map
     */
    private Map<String, String> text;
    /**
     * stop管控开关
     */
    private boolean switchFlag;
}
