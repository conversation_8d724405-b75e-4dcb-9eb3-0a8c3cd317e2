package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-23
 */
@Slf4j
@Service
@Lazy
public class ActivityRegistrationHandleFactory {

    private Map<Integer, ActivityRegistrationHandleService> registrationHandleServiceMap;

    @Autowired
    private List<ActivityRegistrationHandleService> registrationHandleServices;

    @PostConstruct
    private void init() {
        registrationHandleServiceMap = new HashMap<>();
        registrationHandleServices
                .forEach(p -> registrationHandleServiceMap.put(p.getActivitySeriesType().getValue(), p));
    }


    public ActivityRegistrationHandleService getRegistrationService(Integer activitySeriesType) {
        return registrationHandleServiceMap.get(activitySeriesType);
    }
}
