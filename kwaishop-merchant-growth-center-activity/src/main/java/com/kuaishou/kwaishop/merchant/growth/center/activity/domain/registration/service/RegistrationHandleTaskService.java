package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.UserTaskEventMsg;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-21
 */
public interface RegistrationHandleTaskService {

    void handle(UserTaskEventMsg userTaskEventMsg);

    List<UserTaskEventTypeEnum> getHandleEventType();
}
