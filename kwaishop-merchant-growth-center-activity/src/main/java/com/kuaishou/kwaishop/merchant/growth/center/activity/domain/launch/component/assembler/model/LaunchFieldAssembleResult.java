package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchFieldAssembleResult {

    /**
     * 字段-内容映射
     */
    private Map<String, Object> resultMap;
}
