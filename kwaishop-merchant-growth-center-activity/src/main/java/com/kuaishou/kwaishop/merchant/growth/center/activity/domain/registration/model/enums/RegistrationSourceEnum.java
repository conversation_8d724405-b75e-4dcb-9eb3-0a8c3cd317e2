package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;

import java.util.Objects;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-23
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RegistrationSourceEnum {
    INVESTMENT("investment", "招商"),
    NEXT_TASK("drawNextTask", "晋级");

    private String code;
    private String desc;

    public static RegistrationSourceEnum getByCode(String code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }

}
