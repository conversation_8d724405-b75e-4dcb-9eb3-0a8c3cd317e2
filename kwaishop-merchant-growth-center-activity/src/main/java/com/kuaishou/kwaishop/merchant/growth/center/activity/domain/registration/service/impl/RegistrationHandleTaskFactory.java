package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskEventTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.RegistrationHandleTaskService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-21
 */

@Slf4j
@Service
@Lazy
public class RegistrationHandleTaskFactory {

    private Map<Integer, List<RegistrationHandleTaskService>> registrationHandleTaskEventMap;


    @Autowired
    private List<RegistrationHandleTaskService> registrationHandleTaskServices;


    @PostConstruct
    private void init() {
        registrationHandleTaskEventMap = Maps.newHashMap();
        registrationHandleTaskServices.forEach(p -> {
            for (UserTaskEventTypeEnum handleEvent : p.getHandleEventType()) {
                if (registrationHandleTaskEventMap.containsKey(handleEvent.getValue())) {
                    registrationHandleTaskEventMap.get(handleEvent.getValue()).add(p);
                } else {
                    registrationHandleTaskEventMap.put(handleEvent.getValue(), Lists.newArrayList(p));
                }
            }
        });
    }


    public List<RegistrationHandleTaskService> getHandleService(Integer eventType) {
        return registrationHandleTaskEventMap.get(eventType);
    }
}
