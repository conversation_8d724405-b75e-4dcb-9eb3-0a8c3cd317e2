package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.biz;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.sendUserResourceActivityAwardListSize;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.getTodayStartMilli;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.BatchUpdateSameTypeTaskInfoRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CancelUserJoinRecordRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.CreateResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.InterveneUserTimeRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinIndustryTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.JoinResourceTaskRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.SendUserResourceActivityAwardRequest;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.manage.TerminateUserResourceRuleRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-04-24
 */
public class ManageParamValidator {

    public static void checkParam(CancelUserJoinRecordRequest request) {
        if (request.getId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报ID为空");
        }
        if (request.getUserId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报用户ID为空");
        }
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报操作人为空");
        }
        if (StringUtils.isBlank(request.getIndustry())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报行业为空");
        }
    }

    public static void checkParam(JoinIndustryTaskRequest request) {
        if (CollectionUtils.isEmpty(request.getUserIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户ID列表为空");
        }
        if (request.getStartTime() < getTodayStartMilli()) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报开始时间不能早于当前时间");
        }
        if (StringUtils.isBlank(request.getIndustry())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报行业为空");
        }
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "提报操作人为空");
        }
    }

    /**
     * 参数校验
     */
    public static void checkParam(TerminateUserResourceRuleRequest request) {
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getResourceRuleId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴规则ID无效");
        }
        if (request.getUserId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户ID为空");
        }
    }

    /**
     * 参数校验
     */
    public static void checkParam(InterveneUserTimeRequest request) {
        if (request.getUserId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户ID为空");
        }
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getResourceRuleId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴规则ID无效");
        }
        if (request.getOriginBeginTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "无效原始开始时间");
        }
        if (request.getOriginEndTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "无效原始结束时间");
        }
        if (request.getInterveneBeginTime() <= 0 && request.getInterveneEndTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "干预时间不能同时为0");
        }
    }

    /**
     * 参数校验
     */
    public static void checkParam(BatchUpdateSameTypeTaskInfoRequest request) {
        if (StringUtils.isEmpty(request.getAliasTag())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "alias标识为空");
        }
        if (request.getActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动ID无效");
        }
        if (request.getTagIndex() < 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "标识位置错误");
        }
    }

    public static void checkParam(CreateResourceActivityRequest request) {
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getStartTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动开始时间无效");
        }
        if (request.getEndTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动结束时间无效");
        }
        if (StringUtils.isEmpty(request.getTemplateId())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "模版id不能为空");
        }
        if (StringUtils.isEmpty(request.getOperator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "操作人不能为空");
        }
    }

    public static void checkParam(CreateResourceTaskRequest request) {
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getResourceRuleId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴规则ID无效");
        }
        if (request.getStartTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动开始时间无效");
        }
        if (request.getEndTime() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动结束时间无效");
        }
        if (StringUtils.isEmpty(request.getTemplateId())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "模版id不能为空");
        }
        if (StringUtils.isEmpty(request.getOperator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "操作人不能为空");
        }
    }

    public static void checkParam(JoinResourceTaskRequest request) {
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getResourceRuleId() < 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴规则ID无效");
        }
        if (StringUtils.isEmpty(request.getOperator())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "操作人不能为空");
        }
    }

    public static void checkParam(SendUserResourceActivityAwardRequest request) {
        if (request.getResourceActivityId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "补贴活动ID无效");
        }
        if (request.getInterestConfigId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "权益配置ID无效");
        }
        if (CollectionUtils.isEmpty(request.getUserIdList())) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户ID列表不能为空");
        }
        if (request.getUserIdList().size() > sendUserResourceActivityAwardListSize.get()) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户ID列表超过长度"
                    + sendUserResourceActivityAwardListSize.get());
        }
    }

    public static void checkEndTime(long seriesType, long endTime) {
        // 实时对投参与时间不能小于当前时间
        if (seriesType == ActivitySeriesType.ACTIVITY_REALTIME_INVEST.getValue()
                && endTime < System.currentTimeMillis()) {
            throw new BizException(BasicErrorCode.PARAM_INVALID, "用户参与时间小于当前时间");
        }
    }
}
