package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.channel;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationChannelStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.NotificationPushFetchService;

import lombok.extern.slf4j.Slf4j;

/**
 * 通知中心渠道推送策略
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Lazy
@Slf4j
@Service
public class MerchantNotificationCenterChannelStrategyService implements NotificationChannelStrategyService {

    @Autowired
    private NotificationPushFetchService notificationPushFetchService;

    @Override
    public NotificationChannelEnum getNotificationChannel() {
        return NotificationChannelEnum.MERCHANT_NOTIFICATION_CENTER;
    }

    @Override
    public void executePush(long userId, NotificationPushConfigBO configBO, Map<String, String> templateParams) {
        // 商家通知中心推送
        notificationPushFetchService.merchantMessagePush(userId, configBO, templateParams);
    }
}
