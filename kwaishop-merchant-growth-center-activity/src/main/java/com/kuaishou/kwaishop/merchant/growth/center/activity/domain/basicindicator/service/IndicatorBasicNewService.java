package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.tuple.Pair;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicFactorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.BasicTimeRange;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.LayerTaskBaseData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-21
 */
public interface IndicatorBasicNewService {

    /**
     * 剔除节假日后的有效时间
     * 相对时间不剔除节假日
     */
    Integer resolveBasicFactorEffectiveDays(BasicFactorConfigBO basicFactorConfigBO);

    /**
     * 解析单个因子的基期用户有效期
     */
    List<String> resolveSingleFactorUserEffectiveBasicDate(Long userId, BasicFactorConfigBO basicFactorConfigBO);

    /**
     * 获取基期所有因子的最大时间范围
     */
    BasicTimeRange getBasicTimeRange(Long userId, BasicIndicatorConfigBO basicIndicatorConfigBO);

    /**
     * 获取基期所有指标所有因子的最大时间范围
     * 慎用！！
     */
    BasicTimeRange getBasicTimeRange(Long userId, BasicConfigBO basicConfigBO);

    /**
     * 解析单个因子的基期用户时间范围
     */
    Pair<Long, Long> resolveSingleFactorUserEffectiveBasicTimeRange(Long userId,
            BasicFactorConfigBO basicFactorConfigBO);

    /**
     * 从dm获取单一基期指标的基期值
     */
    LayerTaskBaseData queryUserSingleBasicIndicatorDataFromDM(long userId,
            BasicIndicatorConfigBO basicIndicatorConfigBO, IndicatorConfigDO indicatorConfigDO,
            Map<String, Object> extraData);

    /**
     * 从dm获取单一基期指标的基期值-指定精度
     *
     * @param userId
     * @param basicIndicatorConfigBO
     * @param indicatorConfigDO
     * @param scale
     * @return
     */
    LayerTaskBaseData queryUserSingleBasicIndicatorDataFromDM(long userId,
            BasicIndicatorConfigBO basicIndicatorConfigBO,
            IndicatorConfigDO indicatorConfigDO, Integer scale,
            Map<String, Object> extraData);

    /**
     * 从dm获取所有基期指标的基期值
     */
    Map<String, Object> queryUserTotalBasicIndicatorDataFromDM(long userId,
            BasicConfigBO basicConfigBO, List<IndicatorConfigDO> indicatorConfigDOList);

    /**
     * 批量从dm获取所有基期指标的基期值
     */
    Map<Long, Map<String, Object>> batchQueryUserTotalBasicIndicatorDataFromDM(List<Long> userIdList,
            BasicConfigBO basicConfigBO, List<IndicatorConfigDO> indicatorConfigDOList);

    /**
     * 从dm获取基期指标的基期值
     * DAP专用！！！
     * 测算时无indicatorConfigDO
     */
    Map<Long, LayerTaskBaseData> queryUserBasicIndicatorDataFromDMForDap(Long userId, BasicConfigBO basicConfigBO,
                                                                         Integer activityDays, Map<Long, IndicatorConfigDO>
                                                                                 estimateIndicatorConfigMap);

    /**
     * 从dm获取基期指标的基期值-指定精度
     * DAP专用！！！
     * @param userId
     * @param basicConfigBO
     * @param scale
     * @return
     */
//    Map<Long, Map<String, Object>> queryUserBasicIndicatorDataFromDMForDap(Long userId, BasicConfigBO basicConfigBO, Integer scale);


}
