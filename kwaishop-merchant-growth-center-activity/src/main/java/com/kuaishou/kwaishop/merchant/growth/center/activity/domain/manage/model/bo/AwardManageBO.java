package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.award.IndustryAwardBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.RoiCalcTimeTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.lottery.model.bo.LotteryItemManageBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.audit.AuditConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AwardManageBO {
    /**
     * 奖励配置对象
     */
    private AwardConfigDO awardConfig;
    /**
     * 通知配置
     */
    private List<NotificationPushConfigBO> noticeConfigList;
    /**
     * 审批配置
     */
    private List<AuditConfigDO> auditConfig;
    /**
     * 考核指标配置
     */
    private List<IndicatorManageBO> indicatorConfigList;

    /**
     * 抽奖项
     */
    private LotteryItemManageBO lotteryItem;

    /**
     * 是否提前发奖标识
     */
    private Boolean awardInAdvance;

    /**
     * roi计算退款率/tr率使用的时间类型
     */
    private RoiCalcTimeTypeEnum roiCalcTimeType;

    /**
     * 虚拟奖励
     */
    private List<IndustryAwardBO> virtualAwardList;
}
