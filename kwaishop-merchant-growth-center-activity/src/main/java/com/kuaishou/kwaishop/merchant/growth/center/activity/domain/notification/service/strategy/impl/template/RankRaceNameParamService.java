package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.RACE_NAME;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-16
 */
@Service
@Slf4j
public class RankRaceNameParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(RACE_NAME);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();
        // 获取活动ID
        long activityId = configBO.getEntityId();
        // 用户任务记录
        List<UserTaskRecordDO> userTaskRecordList =
                userTaskRecordDAO.queryUserTaskRecordByActivityId(userId, activityId, true);
        // 排行榜活动，一个用户只会参与一个赛道
        List<Long> traceTaskIdList = userTaskRecordList.stream()
                .filter(e -> e.getParentId() == 0)
                .map(UserTaskRecordDO::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(traceTaskIdList) || traceTaskIdList.size() != 1) {
            log.error("[通知通配符填充] 赛道名称填充失败，定位用户多个赛道父任务！userId:{}, traceTaskIdList:{}", userId, toJSON(traceTaskIdList));
            result.setExecuteStatus(STOP);
            return result;
        }
        // 赛道父任务名称就是赛道名称
        TaskDO traceParentTask = taskLocalCacheService.getTaskByTaskId(traceTaskIdList.get(0));
        if (StringUtils.isNotBlank(traceParentTask.getName())) {
            params.put(RACE_NAME.getName(), traceParentTask.getName());
        }
        return result;
    }
}
