package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.fromJSON;
import static com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.generatePeriodVersion;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationPerfEnum.DAP_ESTIMATION_RES_SAVE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.estimationCrowdPartitionSize;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.vo.DapEstimationStrategyResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.validator.DapResultValidator;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserEstimationIAwardResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserEstimationIndicatorResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserEstimationStrategyExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategySnapshotEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationPrepareEntity;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.UserEstimationResultRecordAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.AutoEstimationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimateCalcModeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.EstimationStrategyTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.UserEstimationStrategyRecordStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.EstimationStrategyAggregateRootRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationPrepareEntityRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationResultAggregateRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.service.UserEstimationStrategyDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.BatchExecuteFramework;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.EstimationResultSaveBatchEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.validator.ValidateResReportBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.dap.RewardStrategyVO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-07
 */
@Service
@Slf4j
@Lazy
public class EstimationResultSaveExecuteImpl extends BaseExecuteProcess implements BatchExecuteFramework {

    @Autowired
    private EstimationCacheService estimationCacheService;

    @Autowired
    private UserEstimationStrategyDomainService userEstimationStrategyDomainService;

    @Autowired
    private UserEstimationResultAggregateRepository userEstimationResultAggregateRepository;

    @Autowired
    private UserEstimationPrepareEntityRepository userEstimationPrepareEntityRepository;

    @Autowired
    private EstimationStrategyAggregateRootRepository estimationStrategyAggregateRootRepository;

    @Autowired
    private EstimationStrategyReadService estimationStrategyReadService;

    @Autowired
    private DapResultValidator dapResultValidator;

    @Override
    public Set<Long> initExecuteCrowdAndCache(BatchExecuteEvent event) {
        return event.getInputUserIds();
    }

    @Override
    public BatchExecuteType getBatchExecuteType() {
        return BatchExecuteType.ESTIMATION_RES_SAVE;
    }

    @Override
    ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig) {
        ExecuteHandleResult result = new ExecuteHandleResult();
        List<Long> successUserIdList = Lists.newArrayList();
        List<Long> failUserIdList = Lists.newArrayList();
        List<Long> resExUserIdList = Lists.newArrayList();
        Map<Long, DapEstimationStrategyResult> strategyResultMap = fromJSON(executeConfig, HashMap.class, Long.class,
                DapEstimationStrategyResult.class);
        if (MapUtils.isEmpty(strategyResultMap)) {
            log.error("[测算结果批处理] 测算结果获取异常");
            failUserIdList.addAll(userIdList);
            return result;
        }
        // 解析业务key
        Pair<Long, String> bizKeyPair = parseBizKey(eventId);
        Long strategyId = bizKeyPair.getLeft();
        String strategyVersion = bizKeyPair.getRight();
        // 查询对应的策略是否来源于批量测算
        EstimationStrategyAggregateRoot strategyAggregateRoot =
                estimationStrategyAggregateRootRepository.querySpecifyVersionEntity(strategyId, strategyVersion);
        if (strategyAggregateRoot == null) {
            log.error("[测算结果批处理] 策略不存在, strategyId:{}, strategyVersion:{}", strategyId, strategyVersion);
            failUserIdList.addAll(userIdList);
            return result;
        }
        // 获取用户策略来源
        AutoEstimationTypeEnum autoEstimationType = strategyAggregateRoot.periodEstimation()
                ? AutoEstimationTypeEnum.PERIOD_ESTIMATE : AutoEstimationTypeEnum.NO_AUTO;
        // 判断后续是否需要重新同步版本基期
        String prepareSyncVersion = estimationStrategyReadService.queryUserPrepareSyncVersion(strategyId,
                strategyVersion, EstimationStrategyTypeEnum.STRATEGY);
        boolean fromBatchEstimate = strategyAggregateRoot.fromBatchEstimate();
        userIdList.forEach(userId -> {
            String bizKey = String.join("-", eventId, String.valueOf(userId));
            DapEstimationStrategyResult estimationStrategyResult = strategyResultMap.get(userId);
            try {
                // 用户测算结果校验
                if (estimationStrategyResult == null || estimationStrategyResult.invalid()) {
                    throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "未查询到用户测算结果");
                }
                UserEstimationPrepareEntity userEstimationPrepareEntity;
                // 先查询策略对应的人群prepare数据
                UserEstimationResultRecordAggregateRoot estimationResultAggregateRoot =
                        userEstimationResultAggregateRepository.queryByBizKey(userId, strategyId,
                                strategyVersion, fromBatchEstimate);
                // 来自批量测算创建的策略
                if (fromBatchEstimate) {
                    // 基于组版本维度的用户基期记录来修改为策略维度的基期记录保存
                    userEstimationPrepareEntity = fillEstimationPrepareEntity(userId, estimationResultAggregateRoot,
                            strategyAggregateRoot, prepareSyncVersion, bizKey, successUserIdList, failUserIdList);
                    if (userEstimationPrepareEntity == null) {
                        throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "未查询到策略组维度用户测算准备数据");
                    }
                } else {
                    // 查询用户测算结果记录
                    if (estimationResultAggregateRoot == null) {
                        throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "未查询到前置初始化的用户测算记录");
                    }
                    // 先查询策略对应的人群prepare数据
                    userEstimationPrepareEntity =
                            userEstimationPrepareEntityRepository.queryByBizKey(userId, strategyId, prepareSyncVersion,
                                    EstimationStrategyTypeEnum.STRATEGY);
                    if (userEstimationPrepareEntity == null) {
                        // 未查询到策略记录，说明不是本次测算的人群,直接返回
                        log.warn("userId {} strategyId {} don't find prepare record", userId, strategyId);
                        return;
                    }
                }
                if (UserEstimationStrategyRecordStatusEnum.finishStatus(estimationResultAggregateRoot.getStatus())) {
                    successUserIdList.add(userId);
                    return;
                }
                // 已更新完成直接返回
                ValidateResReportBO resReportBO = saveEstimationResult(estimationResultAggregateRoot,
                        userEstimationPrepareEntity, estimationStrategyResult, autoEstimationType);
                // 未通过校验的用户id
                if (BooleanUtils.isFalse(resReportBO.getPass())) {
                    resExUserIdList.add(userId);
                }
                successUserIdList.add(userId);
                PerfUtil.perfSuccess(DAP_ESTIMATION_RES_SAVE, "saveEstimation", bizKey);
            } catch (Exception e) {
                failUserIdList.add(userId);
                log.error("[测算结果批处理] happen error eventId {} userId {} estimationStrategyResult {}", eventId, userId,
                        toJSON(estimationStrategyResult), e);
                PerfUtil.perfException(DAP_ESTIMATION_RES_SAVE, "saveEstimation", bizKey, e.getMessage());
            }
        });

        // 缓存，用于后续判断是否完成全部保存
        estimationCacheService.batchAddUserToSuccessList(eventId, successUserIdList);
        estimationCacheService.batchAddResUserToExList(eventId, resExUserIdList);
        result.setSuccessUserList(successUserIdList);
        result.setFailUserList(failUserIdList);
        return result;
    }

    private ValidateResReportBO saveEstimationResult(UserEstimationResultRecordAggregateRoot estimationResultAggregateRoot,
                                                     UserEstimationPrepareEntity userEstimationPrepareEntity,
                                                     DapEstimationStrategyResult estimationStrategyResult,
                                                     AutoEstimationTypeEnum autoEstimationType) {
        estimationResultAggregateRoot.setUserEstimationPrepareEntity(userEstimationPrepareEntity);
        boolean subTaskEstimation = estimationStrategyResult.dayTaskEstimation();
        UserEstimationIndicatorResultBO indicatorResult;
        UserEstimationIAwardResultBO awardResult;
        // 子任务测算
        if (subTaskEstimation) {
            Map<String, DapEstimationStrategyResult.EstimationWeekStrategyResult> dayStrategy = estimationStrategyResult
                    .getDayStrategy();
            Map<String, List<DapEstimationStrategyResult.EstimationIndicatorResult>> subTaskIndicatorResult =
                    Maps.newHashMap();
            dayStrategy.forEach((key, value) -> subTaskIndicatorResult.put(key, value.getIndicatorResult()));
            indicatorResult = new UserEstimationIndicatorResultBO(EstimateCalcModeEnum.CYCLE.getCode(),
                    subTaskIndicatorResult);
            Map<String, List<RewardStrategyVO>> subTaskRewardResult = Maps.newHashMap();
            dayStrategy.forEach((key, value) -> subTaskRewardResult.put(key, value.getRewardResult()));
            awardResult = new UserEstimationIAwardResultBO(EstimateCalcModeEnum.CYCLE.getCode(), subTaskRewardResult);
        } else {
            indicatorResult = new UserEstimationIndicatorResultBO(EstimateCalcModeEnum.TOTAL.getCode(),
                    estimationStrategyResult
                            .getWeekStrategy().getIndicatorResult());
            awardResult = new UserEstimationIAwardResultBO(EstimateCalcModeEnum.TOTAL.getCode(),
                    estimationStrategyResult
                            .getWeekStrategy().getRewardResult());
        }
        // 保存测算结果
        estimationResultAggregateRoot.setIndicatorResult(indicatorResult);
        estimationResultAggregateRoot.setAwardResult(awardResult);
        // validator测算结果校验器校验（ROI校验、基期对比、阶梯校验等）
        ValidateResReportBO resReportBO = dapResultValidator.validate(estimationResultAggregateRoot);
        if (!EnvUtils.isProd()) {
            log.info("[测算结果批处理] saveEstimationResult  estimationResultAggregateRoot {} estimationStrategyResult {}",
                    toJSON(estimationResultAggregateRoot), toJSON(estimationStrategyResult));
        }
        UserEstimationStrategyExtBO extBO = estimationResultAggregateRoot.getExtBO();
        if (extBO == null) {
            extBO = new UserEstimationStrategyExtBO();
            estimationResultAggregateRoot.setExtBO(extBO);
        }
        // 设置自动测算类型
        extBO.setAutoEstimationType(autoEstimationType);
        userEstimationStrategyDomainService.saveEstimationResult(estimationResultAggregateRoot, resReportBO);
        return resReportBO;
    }

    private UserEstimationPrepareEntity fillEstimationPrepareEntity(Long userId,
                                                                    UserEstimationResultRecordAggregateRoot
                                                                            estimationResultAggregateRoot,
                                                                    EstimationStrategyAggregateRoot strategyAggregateRoot,
                                                                    String prepareSyncVersion, String bizKey,
                                                                    List<Long> successUserIdList,
                                                                    List<Long> failUserIdList) {
        UserEstimationPrepareEntity userEstimationPrepareEntity;
        // 幂等判断
        if (estimationResultAggregateRoot != null && estimationResultAggregateRoot.getId() != null
                && estimationResultAggregateRoot.getUserEstimationPrepareEntity() != null) {
            successUserIdList.add(userId);
            return estimationResultAggregateRoot.getUserEstimationPrepareEntity();
        }
        userEstimationPrepareEntity =
                estimationResultAggregateRoot.getUserEstimationPrepareEntity();
        String groupPrepareVersion = prepareSyncVersion;
        // 周期测算策略需要修改prepareSyncVersion，因为现在策略组不同版本的子策略下面的版本都是V1.0，需要通过periodIndex确定是哪个策略组版本
        if (strategyAggregateRoot.periodEstimation()) {
            EstimationStrategySnapshotEntity specifyVersionEntity = strategyAggregateRoot.getSpecifyVersionEntity();
            Integer periodIndex = specifyVersionEntity.getExtBO().getPeriodIndex();
            if (periodIndex == null) {
                log.error("[测算结果批处理] 周期期测算策略没有对应的周期索引 bizKey {}", bizKey);
                failUserIdList.add(userId);
                return null;
            }
            groupPrepareVersion = generatePeriodVersion(periodIndex);
        }
        if (userEstimationPrepareEntity == null) {
            // 插入对应的用户prepare数据（写扩散）
            // 查询出组维度的用户基期记录
            UserEstimationPrepareEntity estimationGroupPrepareEntity =
                    userEstimationPrepareEntityRepository.queryByBizKey(userId, strategyAggregateRoot.getGroupId(),
                            groupPrepareVersion, EstimationStrategyTypeEnum.GROUP);
            if (estimationGroupPrepareEntity == null) {
                log.error("[测算组结果批处理] 测算策略组用户记录数据获取异常 bizKey {}", bizKey);
                failUserIdList.add(userId);
                return null;
            }
            userEstimationPrepareEntity = estimationGroupPrepareEntity;
            userEstimationPrepareEntity.setId(null);
            // 修改用户prepare数据
            userEstimationPrepareEntity.setStrategyId(strategyAggregateRoot.getId());
            userEstimationPrepareEntity.setStrategyVersion(prepareSyncVersion);
            userEstimationPrepareEntity.setStrategyType(EstimationStrategyTypeEnum.STRATEGY);
            long nowTime = System.currentTimeMillis();
            userEstimationPrepareEntity.setCreateTime(nowTime);
            userEstimationPrepareEntity.setUpdateTime(nowTime);
        }
        return userEstimationPrepareEntity;
    }

    @Override
    protected boolean filterSuccessUser() {
        return true;
    }


    /**
     * 解析出业务keys
     *
     * @param eventId
     * @return
     */
    private static Pair<Long, String> parseBizKey(String eventId) {
        String[] split = eventId.split("-");
        Long strategyId = Long.valueOf(split[0]);
        String strategyVersion = split[1];
        return Pair.of(strategyId, strategyVersion);
    }

    @Override
    String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList) {
        // 获取执行配置必要信息携带
        EstimationResultSaveBatchEvent resultSaveBatchEvent = (EstimationResultSaveBatchEvent) event;
        Map<Long, DapEstimationStrategyResult> userStrategyResultMap = resultSaveBatchEvent.getUserStrategyResultMap();
        Map<Long, DapEstimationStrategyResult> configUserMap = Maps.newHashMap();
        userIdList.forEach(userId -> configUserMap.put(userId, userStrategyResultMap.get(userId)));
        return toJSON(configUserMap);
    }

    @Override
    int getPartitionSize() {
        return estimationCrowdPartitionSize.get();
    }
}
