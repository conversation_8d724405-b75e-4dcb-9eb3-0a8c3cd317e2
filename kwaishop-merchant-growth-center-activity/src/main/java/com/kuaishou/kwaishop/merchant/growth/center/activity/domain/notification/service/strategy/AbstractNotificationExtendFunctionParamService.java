package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.STRATEGY_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.skipNotificationPushSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.strategyNotificationStrategyBlackList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.strategyNotificationControlConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityTailNumberKey.strategyNotificationUserWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.HashMap;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.config.StrategyNotificationControlConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.distribute.CrowdGroupDistributeService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;

import lombok.extern.slf4j.Slf4j;

/**
 * 重点：子类实现不可用 @Lazy，会导致service注册失败
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-28
 */
@Slf4j
public abstract class AbstractNotificationExtendFunctionParamService implements
        InitializingBean {

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Autowired
    private CrowdGroupDistributeService crowdGroupDistributeService;

    /**
     * 策略方法标识
     */
    public abstract List<TemplateParamTypeEnum> templateParamTypes();

    /**
     * 获取模版变量
     */
    public abstract NotificationExtendFunctionParamBO getExtendFunctionParams(long userId,
            NotificationPushConfigBO configBO, List<TemplateParamTypeEnum> templateParams);

    /**
     * 降级和黑名单检查
     */
    public NotificationExtendFunctionParamBO checkDegradationAndBlackList(long userId,
            NotificationPushConfigBO configBO) {
        NotificationExtendFunctionParamBO skipFlag = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(new HashMap<>())
                .executeStatus(SKIP)
                .build();
        // 推送降级开关，降级成全部跳过
        if (Boolean.TRUE.equals(skipNotificationPushSwitch.get())) {
            log.info("[获取模板参数] 推送全局降级, 跳过本次推送, userId is :{}, notificationPushConfig is :{}", userId,
                    toJSON(configBO));
            return skipFlag;
        }
        // 获取策略id
        Long strategyId = getStrategyId(configBO);
        if (strategyId == null || strategyId <= 0) {
            return skipFlag;
        }
        // 策略维度黑名单
        if (strategyNotificationStrategyBlackList.get().contains(strategyId)) {
            log.info("[获取模板参数] 该策略在黑名单中, 跳过本次推送, strategyId is :{}, userId is :{}, "
                    + "notificationPushConfig is :{}", strategyId, userId, toJSON(configBO));
            return skipFlag;
        }
        // 用户维度黑名单
        if (!strategyNotificationUserWhiteList.get().isOnFor(userId)) {
            log.info("[获取模板参数] 该用户在黑名单中，跳过本次推送, strategyId is :{}, userId is :{}, "
                    + "notificationPushConfig is :{}", strategyId, userId, toJSON(configBO));
            return skipFlag;
        }
        // 推送管控配置，后续推送相关管控名单统一收敛在该BO中
        StrategyNotificationControlConfigBO controlConfig = strategyNotificationControlConfig.getObject();
        if (controlConfig == null) {
            return null;
        }
        // 如果该用户在黑名单人群则跳过
        if (CollectionUtils.isNotEmpty(controlConfig.getBlackListCrowdIds())
                && crowdGroupDistributeService.checkUserInCrowds(userId, controlConfig.getBlackListCrowdIds())) {
            return skipFlag;
        }

        return null;
    }

    /**
     * 获取策略id
     */
    public Long getStrategyId(NotificationPushConfigBO configBO) {
        // 触达配置类型为策略，直接返回
        if (NotificationEntityTypeEnum.STRATEGY.getVal() == configBO.getEntityType()) {
            return configBO.getEntityId();
        }
        AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
        if (awardConfigDO == null) {
            log.error("[获取模板参数失败] awardConfig为空, notificationPushConfig is {}", toJSON(configBO));
            perfFail(STRATEGY_NOTIFICATION_TEMPLATE_PARAM, STOP.getDesc(), "奖励配置",
                    String.valueOf(configBO.getEntityId()));
            return null;
        }
        return awardConfigDO.getEntityId();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        NotificationTemplateParamStrategyFactory.register(templateParamTypes(), this);
    }
}
