package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
@Lazy
@Service
public class NotificationChannelStrategyFactory {

    private Map<NotificationChannelEnum, NotificationChannelStrategyService> notificationChannelStrategyServiceMap;

    @Autowired
    private List<NotificationChannelStrategyService> notificationChannelStrategyServiceList;

    @PostConstruct
    private void init() {
        notificationChannelStrategyServiceMap = new HashMap<>();
        notificationChannelStrategyServiceList.forEach(
                strategy -> notificationChannelStrategyServiceMap.put(strategy.getNotificationChannel(), strategy));
    }

    /**
     * 根据推送渠道获取对应策略service
     */
    public NotificationChannelStrategyService getNotificationChannelStrategyService(int channel) {
        return notificationChannelStrategyServiceMap.get(NotificationChannelEnum.of(channel));
    }

}
