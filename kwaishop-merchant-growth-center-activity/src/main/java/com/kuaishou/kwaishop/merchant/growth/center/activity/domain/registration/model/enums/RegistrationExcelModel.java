package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-22
 */
public enum RegistrationExcelModel {
    UNKNOWN(0, "未知"),
    L2_L4_GMV(1, "L2~L4GMV任务模板"),
    O5_SMALL(2, "O5中小任务"),
    O5_JEWELRY(3, "O5珠宝任务"),
    O6_LIVE_DAY(4, "O6开播天数任务"),
    O6_MERCHANT_VIDEO(5, "O6带货短视频任务"),
    ;
    private final int value;
    private final String desc;

    public static RegistrationExcelModel of(int value) {
        for (RegistrationExcelModel val : RegistrationExcelModel.values()) {
            if (val.getValue() == value) {
                return val;
            }
        }
        return UNKNOWN;
    }

    RegistrationExcelModel(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
