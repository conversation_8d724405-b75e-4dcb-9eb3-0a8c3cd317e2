package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.rule;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.AdmissionRuleConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.AdmissionRuleEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
public interface CustomizePlanAdmissionRuleService {

    /**
     * 判断用户是否命中准入规则
     */
    boolean hitAdmissionRule(long userId, AdmissionRuleConfig admissionRuleConfig);

    /**
     * 准入规则
     */
    AdmissionRuleEnum getAdmissionRule();
}
