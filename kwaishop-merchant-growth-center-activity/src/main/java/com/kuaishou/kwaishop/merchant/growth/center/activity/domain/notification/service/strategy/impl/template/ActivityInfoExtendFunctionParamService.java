package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getActivityBizType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getBizType;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getRulePageUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.getSellerActivityPcDetailUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver.resolveShowConfigMap;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum.SELLER;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.ACTIVITY_ID;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.ACTIVITY_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.DRAW_END_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.PC_RULE_PAGE_URL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.REMAIN_END_TIME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.RULE_PAGE_URL;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.ActivityPerfTagEnum.TASK_NOTIFICATION_TEMPLATE_PARAM;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.notificationAwardDetailPageSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.awardDetailPageUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.componentBuildRulePageUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringConfigKey.kwaiWelfareCenterActivityPromoterJumpUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityStringConfigKey.pcTaskCenterUrl;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToStringMMdd;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.ActivityBizTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.TimeConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-10
 */
@Slf4j
@Service
public class ActivityInfoExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(ACTIVITY_NAME, ACTIVITY_ID, DRAW_END_TIME, REMAIN_END_TIME, RULE_PAGE_URL, PC_RULE_PAGE_URL);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(configBO.getActivityId());
        if (activityDO == null) {
            log.error("[活动模板参数] 无对应活动！userId:{}, config:{}", userId, toJSON(configBO));
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "活动未知");
            // 查询不到奖励配置，停止推送
            result.setExecuteStatus(STOP);
            return result;
        }
        boolean checkRes = checkActivityInfoComplete(activityDO, userId);
        if (!checkRes) {
            // 校验失败，数据异常，停止推送
            result.setExecuteStatus(STOP);
            perfFail(TASK_NOTIFICATION_TEMPLATE_PARAM, "校验失败");
            return result;
        }
        if (StringUtils.isNotBlank(activityDO.getShowName())) {
            params.put(ACTIVITY_NAME.getName(), activityDO.getShowName());
        } else if (StringUtils.isNotBlank(activityDO.getName())) {
            params.put(ACTIVITY_NAME.getName(), activityDO.getName());
        }
        String rulePageUrl = rulePageUrl(activityDO, configBO.getChannel());
        if (StringUtils.isNotBlank(rulePageUrl)) {
            params.put(RULE_PAGE_URL.getName(), addSourceForSellerActivity(activityDO, rulePageUrl));
        }
        if (null != getBizType(activityDO) && SELLER.getCode().equals(getBizType(activityDO))) {
            String pcJumpUrl = getSellerActivityPcDetailUrl(activityDO);
            if (StringUtils.isBlank(pcJumpUrl)) {
                pcJumpUrl = pcTaskCenterUrl.get();
            }
            params.put(PC_RULE_PAGE_URL.getName(), addSourceForSellerActivity(activityDO, pcJumpUrl));
        }
        // 奖励页面链接
        NotificationEntityTypeEnum entityType = NotificationEntityTypeEnum.of(configBO.getEntityType());
        if (notificationAwardDetailPageSwitch.get() && Objects.equals(entityType, NotificationEntityTypeEnum.AWARD)) {
            params.put(RULE_PAGE_URL.getName(), addSourceForSellerActivity(activityDO, awardDetailPageUrl.get()));
        }
        params.put(ACTIVITY_ID.getName(), String.valueOf(activityDO.getId()));
        params.put(DRAW_END_TIME.getName(), milliToStringMMdd(activityDO.getDrawEndTime()));
        params.put(REMAIN_END_TIME.getName(), remainDays(activityDO.getEndTime()));
        return result;
    }

    /**
     * 校验活动信息是否完整
     */
    private boolean checkActivityInfoComplete(ActivityDO activityDO, long userId) {
        if (StringUtils.isBlank(activityDO.getName())) {
            log.error("[任务模板参数] 活动名称为空！userId:{}, activityDO:{}", userId, toJSON(activityDO));
            return false;
        }
        if (activityDO.getId() <= 0L) {
            log.error("[任务模板参数] 活动ID异常！userId:{}, activityDO:{}", userId, toJSON(activityDO));
            return false;
        }
        if (activityDO.getDrawEndTime() <= 0L) {
            log.error("[任务模板参数] 活动领取结束时间异常！userId:{}, activityDO:{}", userId, toJSON(activityDO));
            return false;
        }
        return true;
    }

    public String remainDays(long time) {
        long remainTime = time - System.currentTimeMillis();
        remainTime = remainTime < 0L ? 0 : remainTime;
        long days = remainTime / TimeConstants.DAY / 1000 + 1;
        return String.valueOf(days);
    }

    public String rulePageUrl(ActivityDO activityDO, int channel) {
        String rulePageUrl = getRulePageUrl(activityDO, channel);
        if (StringUtils.isNotEmpty(rulePageUrl)) {
            return assembleProtoHeader(rulePageUrl, channel);
        }
        if (getBizType(activityDO) != null && getBizType(activityDO).equals(ActivityBizTypeEnum.DAREN.getCode())
                && StringUtils.isEmpty(rulePageUrl)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> showConfigMap = resolveShowConfigMap(activityDO);
        if (MapUtils.isEmpty(showConfigMap)) {
            return StringUtils.EMPTY;
        }
        if (showConfigMap.containsKey("promotion")) {
            // 大促有大促的链接
            return kwaiWelfareCenterActivityPromoterJumpUrl.get();
        }
        rulePageUrl = componentBuildRulePageUrl.get() + activityDO.getId();
        return assembleProtoHeader(rulePageUrl, channel);
    }

    public String assembleProtoHeader(String pageUrl, int channel) {
        NotificationChannelEnum channelEnum = NotificationChannelEnum.of(channel);
        if (!channelEnum.getProtoHeader()) {
            pageUrl = StringUtils.replace(pageUrl, "https://", "");
            pageUrl = StringUtils.replace(pageUrl, "kwai://", "");
            pageUrl = StringUtils.replace(pageUrl, "ksshop://", "");
        }
        return pageUrl;
    }

    /**
     * 商家活动添加source参数
     */
    public static String addSourceForSellerActivity(ActivityDO activityDO, String rulePageUrl) {
        ActivityBizTypeEnum activityBizType = getActivityBizType(activityDO);
        if (activityBizType != SELLER) {
            return rulePageUrl;
        }
        if (rulePageUrl.contains("source=")) {
            return rulePageUrl;
        }
        StringBuilder sb = new StringBuilder(rulePageUrl);
        if (!rulePageUrl.contains("?")) {
            sb.append("?source=").append("push");
        } else {
            sb.append("&source=").append("push");
        }
        return sb.toString();
    }
}
