package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCommonConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchBizMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchChannelMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchContentItemConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchContentSceneGenTypeConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchContentTypeMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchFormFieldMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchFormMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchSceneMetaBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchBizMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchChannelMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchChannelPromptInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchContentItemConfigDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchContentTypeMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchFormFieldMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchFormMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.dto.LaunchSceneMetaDTO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchContentTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-16
 */
public class LaunchMetaConverter {

    public static LaunchMetaDTO convertToLaunchConfigDTO(LaunchMetaBO launchMetaBO, String operator) {
        if (launchMetaBO == null) {
            return null;
        }

        // 投放渠道配置
        List<LaunchChannelMetaDTO> launchChannelMetaDTOList = CollectionUtils.emptyIfNull(
                        launchMetaBO.getLaunchChannelMetaList()).stream()
                .map(e -> convertToLaunchChannelConfigDTO(e, operator))
                .filter(Objects::nonNull).collect(Collectors.toList());

        List<String> channelCodeList = launchChannelMetaDTOList.stream().map(LaunchChannelMetaDTO::getChannelCode)
                .collect(Collectors.toList());

        // 投放业务配置
        List<LaunchBizMetaDTO> launchBizMetaDTOList =
                CollectionUtils.emptyIfNull(launchMetaBO.getLaunchBizMetaList()).stream()
                        .filter(bizMeta -> {
                            List<String> relatedChannelCodeList = bizMeta.getRelatedChannelCodeList();
                            return CollectionUtils.isNotEmpty(relatedChannelCodeList) && CollectionUtils.containsAny(
                                    relatedChannelCodeList, channelCodeList);
                        })
                        .map(LaunchMetaConverter::convertToLaunchBiMetaDTO).filter(
                                Objects::nonNull).collect(Collectors.toList());

        return LaunchMetaDTO.builder()
                .launchChannelMetaList(launchChannelMetaDTOList)
                .launchBizMetaList(launchBizMetaDTOList)
                .build();
    }

    public static LaunchChannelMetaDTO convertToLaunchChannelConfigDTO(LaunchChannelMetaBO launchChannelMetaBO,
            String operator) {
        if (launchChannelMetaBO == null) {
            return null;
        }
        List<LaunchSceneMetaDTO> launchSceneMetaDTOList = CollectionUtils.emptyIfNull(
                        launchChannelMetaBO.getLaunchSceneMetaList()).stream()
                .map(e -> convertToLaunchSceneConfigDTO(e, operator))
                .filter(Objects::nonNull).collect(Collectors.toList());

        List<LaunchChannelPromptInfoDTO> launchChannelPromptInfoDTOList = ListUtils.emptyIfNull(
                launchChannelMetaBO.getPromptInfoList()).stream().map(promptInfo ->
                LaunchChannelPromptInfoDTO.builder()
                        .promptText(promptInfo.getPromptText())
                        .jumpButtonText(promptInfo.getJumpButtonText())
                        .jumpButtonUrl(promptInfo.getJumpButtonUrl())
                        .build()).collect(Collectors.toList());

        return LaunchChannelMetaDTO.builder()
                .channelCode(launchChannelMetaBO.getChannelCode())
                .channelName(launchChannelMetaBO.getChannelName())
                .sketchPicUrl(launchChannelMetaBO.getSketchPicUrl())
                .promptInfoList(launchChannelPromptInfoDTOList)
                .launchSceneMetaList(launchSceneMetaDTOList)
                .build();
    }

    public static LaunchSceneMetaDTO convertToLaunchSceneConfigDTO(LaunchSceneMetaBO launchSceneMetaBO,
            String operator) {
        if (launchSceneMetaBO == null) {
            return null;
        }

        List<Integer> supportGenTypeList = Lists.newArrayList();
        LaunchCommonConfigBO config = launchCommonConfig.getObject();
        if (config != null && config.getLaunchContentGenConfig() != null
                && MapUtils.isNotEmpty(config.getLaunchContentGenConfig().getSceneSupportGenTypeConfigMap())) {
            Map<String, List<LaunchContentSceneGenTypeConfigBO>> sceneSupportGenTypeConfigMap =
                    config.getLaunchContentGenConfig().getSceneSupportGenTypeConfigMap();
            List<LaunchContentSceneGenTypeConfigBO> sceneGenTypeConfigs =
                    sceneSupportGenTypeConfigMap.get(launchSceneMetaBO.getSceneCode());

            if (CollectionUtils.isNotEmpty(sceneGenTypeConfigs)) {
                sceneGenTypeConfigs.forEach(sceneGenTypeConfig -> {
                    if (sceneGenTypeConfig.getFullSwitch()) {
                        supportGenTypeList.add(sceneGenTypeConfig.getGenType());
                        return;
                    }

                    if (CollectionUtils.isNotEmpty(sceneGenTypeConfig.getGrayOperatorList())
                            && sceneGenTypeConfig.getGrayOperatorList().contains(operator)) {
                        supportGenTypeList.add(sceneGenTypeConfig.getGenType());
                        return;
                    }
                });
            }
        }
        return LaunchSceneMetaDTO.builder()
                .sceneCode(launchSceneMetaBO.getSceneCode())
                .sceneName(launchSceneMetaBO.getSceneName())
                .sketchPicUrl(launchSceneMetaBO.getSketchPicUrl())
                .supportExp(BooleanUtils.isTrue(launchSceneMetaBO.getSupportExp()))
                .supportGenTypeList(supportGenTypeList)
                .launchFormMeta(convertToLaunchFormConfigDTO(launchSceneMetaBO.getLaunchFormMeta()))
                .build();
    }

    public static LaunchFormMetaDTO convertToLaunchFormConfigDTO(LaunchFormMetaBO launchFormMetaBO) {
        if (launchFormMetaBO == null) {
            return null;
        }

        List<LaunchFormFieldMetaDTO> launchFormFieldMetaDTOList = CollectionUtils.emptyIfNull(
                        launchFormMetaBO.getLaunchFormFieldMetaList()).stream()
                .map(LaunchMetaConverter::convertToLaunchFormFieldConfigDTO)
                .filter(Objects::nonNull).collect(Collectors.toList());

        return LaunchFormMetaDTO.builder()
                .launchFormFieldMetaList(launchFormFieldMetaDTOList)
                .build();
    }

    public static LaunchFormFieldMetaDTO convertToLaunchFormFieldConfigDTO(
            LaunchFormFieldMetaBO launchFormFieldMetaBO) {
        if (launchFormFieldMetaBO == null) {
            return null;
        }

        List<LaunchContentTypeMetaBO> supportedContentTypeMetaList =
                launchFormFieldMetaBO.getSupportedContentTypeMetaList();

        List<LaunchContentTypeMetaDTO> launchContentTypeMetaList;
        if (CollectionUtils.isEmpty(supportedContentTypeMetaList)) {
            // 兼容历史逻辑
            List<Integer> supportedContentTypeList = launchFormFieldMetaBO.getSupportedContentTypeList();
            launchContentTypeMetaList = ListUtils.emptyIfNull(supportedContentTypeList).stream().map(contentType -> {
                LaunchContentTypeEnum contentTypeEnum = LaunchContentTypeEnum.getByType(contentType);
                if (Objects.equals(contentTypeEnum, LaunchContentTypeEnum.UNKNOWN)) {
                    return null;
                }
                return LaunchContentTypeMetaDTO.builder()
                        .contentType(contentTypeEnum.getType())
                        .typeName(contentTypeEnum.getDesc())
                        .defaultValue(StringUtils.EMPTY)
                        .modifiable(true)
                        .desc(StringUtils.EMPTY)
                        .linkSuffix(StringUtils.EMPTY)
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } else {
            launchContentTypeMetaList = supportedContentTypeMetaList.stream().map(contentTypeMeta -> {
                LaunchContentTypeEnum contentTypeEnum =
                        LaunchContentTypeEnum.getByType(contentTypeMeta.getContentType());
                if (Objects.equals(contentTypeEnum, LaunchContentTypeEnum.UNKNOWN)) {
                    return null;
                }
                List<LaunchContentItemConfigDTO> selectItems = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(contentTypeMeta.getSelectItems())) {
                    selectItems = contentTypeMeta.getSelectItems().stream()
                            .map(LaunchMetaConverter::convertToLaunchFormFieldConfigDTO)
                            .collect(Collectors.toList());
                }
                return LaunchContentTypeMetaDTO.builder()
                        .contentType(contentTypeMeta.getContentType())
                        .typeName(contentTypeEnum.getDesc())
                        .defaultValue(StringUtils.defaultIfEmpty(contentTypeMeta.getDefaultValue(), StringUtils.EMPTY))
                        .selectItems(selectItems)
                        .modifiable(BooleanUtils.toBooleanDefaultIfNull(contentTypeMeta.getModifiable(), true))
                        .desc(StringUtils.defaultIfEmpty(contentTypeMeta.getDesc(), StringUtils.EMPTY))
                        .promptInfo(StringUtils.defaultIfEmpty(contentTypeMeta.getPromptInfo(), StringUtils.EMPTY))
                        .linkSuffix(StringUtils.defaultIfEmpty(contentTypeMeta.getLinkSuffix(), StringUtils.EMPTY))
                        .build();
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }

        return LaunchFormFieldMetaDTO.builder()
                .fieldCode(launchFormFieldMetaBO.getFieldCode())
                .fieldName(launchFormFieldMetaBO.getFieldName())
                .supportedContentTypeList(launchContentTypeMetaList)
                .build();
    }

    private static LaunchContentItemConfigDTO convertToLaunchFormFieldConfigDTO(
            LaunchContentItemConfigBO launchContentItemConfigBO) {
        if (launchContentItemConfigBO == null) {
            return null;
        }
        return LaunchContentItemConfigDTO.builder()
                .name(StringUtils.defaultIfEmpty(launchContentItemConfigBO.getName(), StringUtils.EMPTY))
                .value(StringUtils.defaultIfEmpty(launchContentItemConfigBO.getValue(), StringUtils.EMPTY))
                .build();
    }

    private static LaunchBizMetaDTO convertToLaunchBiMetaDTO(LaunchBizMetaBO launchBizMetaBO) {
        if (launchBizMetaBO == null) {
            return null;
        }

        return LaunchBizMetaDTO.builder()
                .bizCode(launchBizMetaBO.getBizCode())
                .bizName(launchBizMetaBO.getBizName())
                .bizDesc(launchBizMetaBO.getBizDesc())
                .relatedChannelCodeList(launchBizMetaBO.getRelatedChannelCodeList())
                .build();
    }
}
