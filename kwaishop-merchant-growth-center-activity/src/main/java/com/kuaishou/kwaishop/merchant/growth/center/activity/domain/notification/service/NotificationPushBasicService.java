package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service;

import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.PeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationChannelEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationOccasionEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-06
 */
public interface NotificationPushBasicService {
    /**
     * 创建推送配置，只创建，已存在抛异常
     */
    boolean createNotificationPushConfig(NotificationPushConfigBO config);

    /**
     * 创建或更新推送配置，存在时更新
     */
    boolean createOrUpdateNotificationPushConfig(NotificationPushConfigBO config);

    /**
     * 更新推送配置
     */
    boolean updateNotificationPushConfig(NotificationPushConfigBO config);

    /**
     * 推荐使用
     * 批量更新推送配置生效状态 根据条件更新
     */
    boolean updateNotificationConfigStatusByConditions(long activityId, NotificationEntityTypeEnum entityType,
            int entityStatus, NotificationStatusEnum status, String modifier);

    /**
     * 批量更新推送配置生效状态 configId主键更新
     */
    boolean batchUpdateNotificationPushConfigStatus(List<Long> configIds, NotificationStatusEnum status,
            String modifier);

    /**
     * 批量更新模版配置和周期性配置
     */
    boolean batchUpdateNotificationTemplateAndPeriodConfig(List<Long> configIds, String templateConfig,
            String periodConfig, String modifier);

    /**
     * 硬删除消息推送配置
     */
    boolean hardDeleteNotificationPushConfigs(long activityId, NotificationEntityTypeEnum entityType,
            List<Long> entityIdList, int channel);

    /**
     *  根据notification主键id批量删除消息推送配置
     */
    int hardDeleteNotificationPushConfigs(List<Long> configIdList);

    /**
     * 根据主键获取推送配置
     */
    NotificationPushConfigBO getNotificationPushConfigById(long id, boolean readMaster);

    /**
     * 根据主键获取推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfigsByIds(List<Long> id);

    /**
     * 获取推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfig(long entityId, int entityType, int entityStatus,
            boolean readMaster);

    /**
     * 推荐使用
     * 根据条件 获取任务推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfigByConditions(long activityId,
            NotificationEntityTypeEnum entityType, int entityStatus, List<Long> entityIdList);

    /**
     * 任务后台使用
     * 获取推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfig(long activityId, long entityId, int entityType);

    /**
     * 任务后台使用
     * 根据条件 获取任务推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfigByConditions(long activityId, int entityType);

    /**
     * 获取活动下推送配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfigByActivityId(long activityId);

    /**
     * 获取推送配置下一个触发时间
     */
    long getNotificationPushNextOccasionTime(PeriodConfigBO config, long eventTime);

    /**
     * 根据entityIds查询用户触达配置
     */
    List<NotificationPushConfigBO> getNotificationPushConfigByEntityIds(List<Long> entityIds,
            NotificationEntityTypeEnum entityType, int entityStatus, NotificationChannelEnum channel, String scene);

    List<NotificationPushConfigBO> getNotificationPushConfigByScene(long activityId, NotificationChannelEnum channel,
            String scene, NotificationOccasionEnum occasion);
}
