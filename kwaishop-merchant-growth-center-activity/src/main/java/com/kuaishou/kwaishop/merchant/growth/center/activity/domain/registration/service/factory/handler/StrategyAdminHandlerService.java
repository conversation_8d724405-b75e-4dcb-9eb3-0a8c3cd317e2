package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.handler;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.converter.ActivityAssembler.resolveActivityInvestment;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.STRATEGY_DRAW_SIGN_UP_ACTIVITY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver.resolveExperimentIdFromCrowdConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.converter.CrowdBOConverter.parseCrowdSelectionSource;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.ActivityConstants.REMAIN_DRAW;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.preJoinInvestDraw;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.StreamUtils.distinctByKey;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.ActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.activity.ActivitySeriesType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.CrowdConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.bo.LiteConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.ActivityResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver.TaskResolver;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.convert.AdminActivityConvert;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.RegistrationDataBuildRes;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.client.CrowdFetchClient;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.converter.CrowdBOConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.model.CrowdBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.crowd.model.CrowdSelectionSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.AddRegistrationOptionBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordExtBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.RegistrationSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums.UserRegistrationStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.UserRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.service.factory.ActivityRegistrationHandleService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityDrawService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.ActivityInvestmentReportFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.BaseDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.shard.UserRegistrationRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.custom.ManualDrawSignUpActivityRequest;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-05-24
 */
@Slf4j
@Lazy
@Service
public class StrategyAdminHandlerService implements ActivityRegistrationHandleService {

    @Autowired
    private AdminActivityOnlineService adminActivityOnlineService;

    @Autowired
    private ActivityLocalCacheService activityLocalCacheService;

    @Autowired
    private TaskLocalCacheService taskLocalCacheService;

    @Autowired
    private IndicatorConfigDAO indicatorConfigDAO;

    @Autowired
    private AdminActivityConvert adminActivityConvert;

    @Autowired
    private UserRegistrationRecordDAO userRegistrationRecordDAO;

    @Autowired
    private UserRegistrationService userRegistrationService;

    @Autowired
    private UserActivityRecordDAO userActivityRecordDAO;

    @Autowired
    private UserActivityDrawService userActivityDrawService;

    @Autowired
    private ActivityInvestmentReportFetchService activityInvestmentReportFetchService;

    @Autowired
    private CrowdFetchClient crowdFetchClient;

    @Autowired
    private UserAwardRecordDAO userAwardRecordDAO;


    @Override
    public void handleManualDrawSignUp(ManualDrawSignUpActivityRequest request) {
    }

    @Override
    public boolean handleAddUserRegistration(long userId, long activityId, List<Long> parentTaskIds, String source,
            Map<Long, AddRegistrationOptionBO> optionMap) {
        // 正常情况不会出现参数不正确，如果不正确，不处理
        if (activityId == 0) {
            log.error("[处理追加用户]参数不存在,活动ID: {}", activityId);
            return true;
        }
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null) {
            log.error("[处理追加用户]追加人群活动不存在,活动ID: {}", activityId);
            throw new BizException(BasicErrorCode.PARAM_INVALID, "活动不存在");
        }
        // 正常情况不会出现参数不正确，如果不正确，不处理
        if (CollectionUtils.isEmpty(parentTaskIds) && !ActivityResolver.isLiteActivity(activityDO)) {
            log.error("[处理追加用户]参数不存在,活动ID: {}", activityId);
            return true;
        }
        if (ActivityResolver.isLiteActivity(activityDO)) {
            return handleLiteActivityAddRegistration(userId, activityDO, source, optionMap);
        }
        parentTaskIds = parentTaskIds.stream().distinct().collect(Collectors.toList());
        List<TaskDO> parentTaskDOList = taskLocalCacheService.batchGetTaskByTaskId(parentTaskIds);
        // 进行ab过滤
        List<Long> addParentTaskIds =
                filterUserCanDrawTaskByAbCrowdAndAddToCrowd(userId, parentTaskIds, parentTaskDOList);
        if (CollectionUtils.isEmpty(addParentTaskIds)) {
            log.warn("[处理追加用户] 经ab过滤后，父任务为空, userId:{}, activityId:{}, source:{}", userId, activityId,
                    source);
            return true;
        }
        // 触发ab上报
        adminActivityOnlineService.reportAbCrowd(userId, activityId, parentTaskDOList);
        List<UserRegistrationRecordDO> registrationList = userRegistrationRecordDAO
                .listRecordsByUserAndActivityId(activityId, userId);
        UserRegistrationRecordDO userActivityRegistration = null;
        List<Long> existParentTaskIds = Lists.newArrayList();
        List<Long> allExistParentTaskIds = Lists.newArrayList();
        // 用户有资格记录，排除已经添加的
        // todo js 活动资格被封控，追加的父任务默认写风控资格记录
        Map<Integer, List<UserRegistrationRecordDO>> userRegistrationMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(registrationList)) {
            userRegistrationMap =
                    registrationList.stream().collect(Collectors.groupingBy(UserRegistrationRecordDO::getEntityType));
        }
        if (userRegistrationMap.containsKey(EntityTypeEnum.ACTIVITY.getCode())) {
            userActivityRegistration = userRegistrationMap.get(EntityTypeEnum.ACTIVITY.getCode()).get(0);
            if (userActivityRegistration != null
                    && !UserRegistrationStatusEnum.VALID.getCode().equals(userActivityRegistration.getStatus())) {
                log.warn("[处理追加用户]追加人群用户被风控,活动ID: {},userId:{}", activityId, userId);
                return false;
            }
        }
        if (userRegistrationMap.containsKey(EntityTypeEnum.TASK.getCode())) {
            List<UserRegistrationRecordDO> existsParentRegistrations =
                    userRegistrationMap.get(EntityTypeEnum.TASK.getCode());
            existParentTaskIds = existsParentRegistrations.stream()
                    .filter(e -> e.getStatus().equals(UserRegistrationStatusEnum.VALID.getCode()))
                    .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());
            allExistParentTaskIds = existsParentRegistrations.stream()
                    .map(UserRegistrationRecordDO::getEntityId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(allExistParentTaskIds)) {
            addParentTaskIds = divideAddParentIds(userId, activityId, allExistParentTaskIds, addParentTaskIds);
        }

        //  针对来自非招商的用户追加事件，则需要判断是否存在需要报名指定招商活动，如果当前用户已经报名招商，则需要进行活动领取
        boolean needDrawActivity = checkInvestNeedDrawActivity(activityDO, userId, source);
        // 不存在差量需要添加的
        if (CollectionUtils.isEmpty(addParentTaskIds)) {
            drawRemainUserTasks(userId, activityDO, parentTaskIds, source, existParentTaskIds, Lists.newArrayList(),
                    needDrawActivity);
            return true;
        }
        // 基值指标配置&指标元数据
        List<IndicatorConfigDO> baseIndicatorConfigs = indicatorConfigDAO
                .queryActivityIndicatorConfigByType(activityId, EntityTypeEnum.ACTIVITY.getCode());
        List<Long> finalAddParentTaskIds = addParentTaskIds;
        List<TaskDO> canDrawTaskDOS = parentTaskDOList.stream()
                .filter(e -> finalAddParentTaskIds.contains(e.getId()))
                .collect(Collectors.toList());
        RegistrationDataBuildRes res = adminActivityOnlineService
                .buildUserActivityRegistration(userId, activityDO, canDrawTaskDOS, baseIndicatorConfigs, source,
                        optionMap);
        List<UserRegistrationRecordDO> addRegistrations = res.getRecords();
        // 中风控
        if (res.isRisk()) {
            log.info("[追加用户]用户添加资格被风控，sellerId:{}.activityId:{}", userId, activityId);
            if (userActivityRegistration == null) {
                addRegistrations.add(
                        adminActivityConvert.buildRiskRegistrationActivityRecord(userId, activityId, source));
            }
            userRegistrationService.batchInsertRegistrationRecords(addRegistrations);
            return false;
        }
        if (userActivityRegistration == null) {
            addRegistrations.add(adminActivityConvert.buildRegistrationActivityRecord(userId, activityId, source));
        }
        userRegistrationService.batchInsertRegistrationRecords(addRegistrations);
        // 处理报名,之前无活动资格的不需要处理，报名consumer自动处理
        if (userActivityRegistration == null && !needDrawActivity) {
            return true;
        }
        // 报名报存量和增量
        if (CollectionUtils.isEmpty(addRegistrations)) {
            addParentTaskIds = Lists.newArrayList();
        } else {
            addParentTaskIds = addRegistrations.stream()
                    .filter(a -> EntityTypeEnum.TASK.getCode().equals(a.getEntityType()))
                    .map(UserRegistrationRecordDO::getEntityId)
                    .collect(Collectors.toList());
        }
        // 针对已存在的和本次追加的合理的补领
        drawRemainUserTasks(userId, activityDO, parentTaskIds, source, existParentTaskIds, addParentTaskIds,
                needDrawActivity);
        return true;
    }

    private boolean handleLiteActivityAddRegistration(Long userId, ActivityDO activity, String source,
            Map<Long, AddRegistrationOptionBO> optionMap) {
        List<UserRegistrationRecordDO> registrationList = userRegistrationRecordDAO
                .queryUserRegistrationRecords(activity.getId(), EntityTypeEnum.ACTIVITY, activity.getId(), userId,
                        null);
        if (CollectionUtils.isNotEmpty(registrationList)) {
            return true;
        }
        LiteConfig liteConfig = ActivityResolver.getActivityLiteConfig(activity);
        CrowdConfigBO crowdConfigBO = liteConfig.getCrowdConfig();
        List<CrowdBO> crowdBOS = Collections.singletonList(CrowdBOConverter.convertToCrowdBO(crowdConfigBO));
        if (crowdConfigBO.getAbExperimentId() != null && crowdConfigBO.getAbExperimentId() != 0) {
            crowdFetchClient.addUserToCrowds(userId, crowdBOS);
            List<Long> hitExperimentIds = crowdFetchClient.queryUserHitExperimentIdsInCrowds(userId, crowdBOS, true);
            CrowdSelectionSourceEnum selectionSourceEnum = parseCrowdSelectionSource(crowdConfigBO);
            if (selectionSourceEnum != null) {
                adminActivityOnlineService.reportAbCrowdV2(userId, activity.getId(), crowdConfigBO.getCrowdKey(),
                        selectionSourceEnum.getSource());
            }
            if (CollectionUtils.isEmpty(hitExperimentIds)
                    || !hitExperimentIds.contains(crowdConfigBO.getAbExperimentId())) {
                return true;
            }
        }
        RegistrationDataBuildRes res = adminActivityOnlineService
                .buildUserActivityRegistration(userId, activity, null, null, source, optionMap);
        List<UserRegistrationRecordDO> addRegistrations =
                CollectionUtils.isEmpty(res.getRecords()) ? Lists.newArrayList() : res.getRecords();
        if (res.isRisk()) {
            addRegistrations.add(
                    adminActivityConvert.buildRiskRegistrationActivityRecord(userId, activity.getId(), source));
        } else {
            addRegistrations.add(
                    adminActivityConvert.buildRegistrationActivityRecord(userId, activity.getId(), source));
        }
        userRegistrationService.batchInsertRegistrationRecords(addRegistrations);
        return !res.isRisk();
    }

    private List<Long> filterUserCanDrawTaskByAbCrowdAndAddToCrowd(long userId, List<Long> parentTaskIds,
            List<TaskDO> parentTaskDOS) {
        // 不包含实验组信息的父任务
        List<TaskDO> noExperimentParentTasks =
                parentTaskDOS.stream().filter(e -> resolveExperimentIdFromCrowdConfig(e) == null).collect(
                        Collectors.toList());
        List<Long> noExperimentTaskIds =
                noExperimentParentTasks.stream().map(BaseDO::getId).collect(Collectors.toList());
        // 包含实验组信息的父任务
        List<TaskDO> experimentParentTasks =
                parentTaskDOS.stream().filter(e -> resolveExperimentIdFromCrowdConfig(e) != null).collect(
                        Collectors.toList());
        if (CollectionUtils.isEmpty(experimentParentTasks)) {
            // 无需过滤
            return parentTaskIds;
        }
        // 动态添加至人群包
        List<CrowdBO> experimentCrowdBOS = experimentParentTasks.stream()
                .map(TaskResolver::resolveCrowdConfig)
                .map(CrowdBOConverter::convertToCrowdBO)
                .filter(distinctByKey(CrowdBO::generateCrowdUniqueKey))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(experimentCrowdBOS)) {
            // 无需过滤
            return parentTaskIds;
        }
        crowdFetchClient.addUserToCrowds(userId, experimentCrowdBOS);
        // 对每个父任务下进行ab判存
        List<TaskDO> canDrawTasks =
                adminActivityOnlineService.getCanDrawParentTaskListWithExperiment(userId, experimentParentTasks);
        if (CollectionUtils.isEmpty(canDrawTasks)) {
            return noExperimentTaskIds;
        }
        noExperimentTaskIds.addAll(canDrawTasks.stream().map(BaseDO::getId).collect(Collectors.toList()));
        return noExperimentTaskIds;
    }

    /**
     * 针对来自非招商的用户追加事件，则需要判断是否存在需要报名指定招商活动，如果当前用户已经报名招商，则需要进行活动领取
     */
    public boolean checkInvestNeedDrawActivity(ActivityDO activityDO, Long userId, String source) {
        // 判断开关
        Boolean checkSwitch = preJoinInvestDraw.get();
        if (checkSwitch != null && !checkSwitch) {
            return false;
        }
        // 招商事件直接返回
        // 对于活动参与的要求是招商活动A但实时招商事件为招商B这种情况先不考虑
        if (RegistrationSourceEnum.INVESTMENT.getCode().equalsIgnoreCase(source)) {
            return false;
        }
        try {
            Long investmentId = resolveActivityInvestment(activityDO);
            if (investmentId == null) {
                return false;
            }
            log.info("[checkInvestNeedDrawActivity] success userId {} activityId {}", userId, activityDO.getId());
            // 查询招商接口
            return activityInvestmentReportFetchService.isActivityInvestmentReport(userId,
                    Collections.singletonList(investmentId));
        } catch (Exception e) {
            log.error("[checkInvestNeedDrawActivity] fail userId {} activityId {}", userId,
                    activityDO.getId(), e);
            return false;
        }
    }

    public void drawRemainUserTasks(Long userId, ActivityDO activityDO, List<Long> parentTaskIds, String source,
            List<Long> existsParentTaskIds, List<Long> addParentTaskIds,
            boolean needDrawActivity) {
        Long activityId = activityDO.getId();
        UserActivityRecordDO userActivity = userActivityRecordDAO.queryUserActivityRecord(userId, activityId,
                true);
        // 用户活动不存在的不需要处理，之前有资格尚未报名
        if (userActivity == null && !needDrawActivity) {
            return;
        }
        if (CollectionUtils.isEmpty(parentTaskIds)) {
            // 兼容极速版
            return;
        }
        parentTaskIds = parentTaskIds.stream()
                .filter(a -> existsParentTaskIds.contains(a) || addParentTaskIds.contains(a))
                .collect(Collectors.toList());
        log.info("[追加用户]任务补领，用户id:{},父任务id:{},活动id:{}", userId, parentTaskIds, activityId);
        if (CollectionUtils.isEmpty(parentTaskIds)) {
            return;
        }
        // 幂等考虑，报名所有追加父任务
        userActivityDrawService.batchDrawTaskWithParam(userId, activityId, parentTaskIds, REMAIN_DRAW,
                StringUtils.EMPTY, null);
    }


    public List<Long> divideAddParentIds(long userId, long activityId,
            List<Long> userExistTaskIds,
            List<Long> parentTaskIds) {
        List<Long> addParentTaskIds = parentTaskIds;
        List<TaskDO> existParentTasks = taskLocalCacheService.batchGetTaskByTaskId(userExistTaskIds);
        List<TaskDO> addParentTasks = taskLocalCacheService.batchGetTaskByTaskId(parentTaskIds);
        // 校验待追加父任务之间是否互斥，互斥则打点告警
        if (CollectionUtils.isEmpty(addParentTasks)) {
            return addParentTaskIds;
        }
        Map<Integer, List<TaskDO>> addParentMap =
                addParentTasks.stream().collect(Collectors.groupingBy(TaskDO::getStage));
        for (List<TaskDO> sameStageTask : addParentMap.values()) {
            if (CollectionUtils.isNotEmpty(sameStageTask) && sameStageTask.size() > 1) {
                log.error("[校验追加任务分层互斥]相同分层子任务同时追加报名,activityId:{},用户ID:{}", activityId,
                        userId);
                perfFail(STRATEGY_DRAW_SIGN_UP_ACTIVITY, "same.stage", String.valueOf(activityId));
                return Lists.newArrayList();
            }
        }
        // 相同子任务下不同分层也不允许重复追加
        if (CollectionUtils.isEmpty(existParentTasks)) {
            return addParentTaskIds;
        }
        addParentTaskIds = Lists.newArrayList();
        // 根据子活动聚类
        Map<Integer, List<TaskDO>> existParentMap =
                existParentTasks.stream().collect(Collectors.groupingBy(TaskDO::getStage));
        for (TaskDO addTask : addParentTasks) {
            // 同一‍分层已加入的任务，不做处理
            if (existParentMap.containsKey(addTask.getStage())) {
                log.warn("[校验追加任务分层互斥]已存在相同分层子任务报名,activityId:{},用户Id:{}", activityId,
                        userId);
                continue;
            }
            addParentTaskIds.add(addTask.getId());
        }
        return addParentTaskIds;
    }


//    private boolean batchInsertRegistrationRecords(List<UserRegistrationRecordDO> records) {
//        if (CollectionUtils.isEmpty(records)) {
//            return true;
//        }
//        try {
//            userRegistrationRecordDAO.batchInsertUserRegistrationRecord(records);
//        } catch (DataIntegrityViolationException e) {
//            log.warn("[追加用户] 资格记录插入幂等！msg:{}", toJSON(records));
//            perfSuccess(ADd_DRAW_SIGN_UP_ACTIVITY, "duplicate", e.getClass().getSimpleName());
//            return false;
//        } catch (BizException e) {
//            log.error("[追加用户] 资格记录插入失败！msg:{}", toJSON(records), e);
//            perfFail(ADd_DRAW_SIGN_UP_ACTIVITY, "insertAfterRetry", e.getClass().getSimpleName());
//            throw new BizException(BasicErrorCode.SERVER_ERROR);
//        } catch (Exception e) {
//            log.error("[追加用户] 资格记录插入失败！msg:{}", toJSON(records), e);
//            perfException(ADd_DRAW_SIGN_UP_ACTIVITY, "insertException", e.getClass().getSimpleName());
//            throw new BizException(BasicErrorCode.SERVER_ERROR);
//        }
//        return true;
//    }


    @Override
    public void handleRiskInterruptRegistration(Long userId, Long activityId, String riskReason) {
        ActivityDO activityDO = activityLocalCacheService.queryActivityInfo(activityId);
        if (activityDO == null || activityDO.getStatus() != ActivityStatusEnum.ONLINE.getCode()) {
            return;
        }
        List<UserRegistrationRecordDO> userRegistrations =
                userRegistrationRecordDAO.queryUserRegistrationRecords(activityId, null, null, userId,
                        UserRegistrationStatusEnum.VALID);
        if (CollectionUtils.isEmpty(userRegistrations)) {
            return;
        }
        userRegistrations.forEach(userRegistration -> {
            UserRegistrationRecordExtBO recordExtBO;
            if (StringUtils.isBlank(userRegistration.getExt())) {
                recordExtBO = new UserRegistrationRecordExtBO();
            } else {
                recordExtBO = fromJSON(userRegistration.getExt(), UserRegistrationRecordExtBO.class);
            }
            recordExtBO.setRiskReason(riskReason);
            recordExtBO.setRiskInterrupt(true);
            userRegistration.setExt(toJSON(recordExtBO));
        });
        UserActivityRecordDO userActivity = userActivityRecordDAO.queryUserActivityRecord(userId, activityId, false);
        if (userActivity != null) {
            List<UserAwardRecordDO> userAwardRecords =
                    userAwardRecordDAO.listUserActivityRecord(userId, activityId, true);
            if (CollectionUtils.isNotEmpty(userAwardRecords)) {
                log.warn("[处理二次风控] 用户存在奖励记录 userID:{},activityId:{}", userId, activityId);
                return;
            }
            // 没报名且超过活动时间的不需要清除了
        } else if (activityDO.getEndTime() <= System.currentTimeMillis()) {
            return;
        }
        userRegistrationService.batchUpdateUserRegistrationRecordsStatus(userRegistrations,
                UserRegistrationStatusEnum.RISK);

    }


    @Override
    public ActivitySeriesType getActivitySeriesType() {
        return ActivitySeriesType.ACTIVITY_STRATEGY_ADMIN;
    }
}
