package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.manage.model.bo.entity;


import java.util.List;

import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
@Data
@Builder
public class UserRecordEntity {
    /**
     * 用户活动记录
     */
    private UserActivityRecordDO userActivityRecord;
    /**
     * 用户任务记录
     */
    private List<UserTaskRecordDO> userTaskRecordList;
    /**
     * 用户指标记录
     */
    private List<IndicatorRecordDO> userIndicatorRecordList;
    /**
     * 用户奖励记录
     */
    private List<UserAwardRecordDO> userAwardRecordList;
}
