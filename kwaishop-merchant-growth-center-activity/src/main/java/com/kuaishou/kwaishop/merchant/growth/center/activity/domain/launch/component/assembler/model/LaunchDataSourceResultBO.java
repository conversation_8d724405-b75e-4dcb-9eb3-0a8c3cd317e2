package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.model;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LaunchDataSourceResultBO {

    private Map<String, Object> resMap;

    public static LaunchDataSourceResultBO getDefaultResult() {
        return new LaunchDataSourceResultBO(Maps.newConcurrentMap());
    }

    public void aggResult(LaunchDataSourceResultBO otherRes) {
        this.resMap.putAll(MapUtils.emptyIfNull(otherRes.getResMap()));
    }
}
