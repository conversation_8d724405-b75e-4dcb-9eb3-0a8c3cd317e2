package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.component.assembler.strategy.fixcontent;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.enums.LaunchContentTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Component
@Slf4j
public class LaunchPhotoContentResolveStrategy extends LaunchDefaultFixContentResolveStrategy {

    @Override
    public LaunchContentTypeEnum getContentType() {
        return LaunchContentTypeEnum.PHOTO;
    }
}
