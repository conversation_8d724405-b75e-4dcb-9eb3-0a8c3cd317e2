package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.condition;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.NotificationPushJudgeStrategyService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-26
 */
@Service
@Slf4j
@Lazy
public class UserTaskNotCompleteStrategyServiceImpl implements NotificationPushJudgeStrategyService {

    @Autowired
    private UserTaskRecordDAO userTaskRecordDAO;

    @Override
    public String judgeConditionCode() {
        return "userTaskNotComplete";
    }

    @Override
    public boolean judgePush(long userId, NotificationPushConfigBO configBO) {
        UserTaskRecordDO userTaskRecord =
                userTaskRecordDAO.queryUserTaskRecord(userId, configBO.getActivityId(), configBO.getEntityId(), false);
        if (null == userTaskRecord) {
            log.error("[触达推送决策] userTaskNotComplete 用户任务记录不存在, userId:{}, activityId:{}, entityId:{}",
                    userId, configBO.getActivityId(), configBO.getEntityId());
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "用户任务记录不存在");
        }
        return userTaskRecord.getStatus() != UserTaskStatusEnum.SUCCESS.getValue();
    }
}
