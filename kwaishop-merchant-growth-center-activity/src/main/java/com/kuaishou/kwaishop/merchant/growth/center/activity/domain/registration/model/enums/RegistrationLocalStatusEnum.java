package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.enums;

import java.util.Objects;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 报名本地表状态，用于扫表报名场景
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RegistrationLocalStatusEnum {
    WAIT_PROCESS(1, "待处理"),
    REGISTRATION_SUCCESS(5, "报名成功"),
    REGISTRATION_IGNORE(6, "报名忽略");

    private Integer code;
    private String desc;

    public static RegistrationLocalStatusEnum getByCode(Integer code) {
        return Stream.of(values()).filter(item -> Objects.equals(code, item.getCode()))
                .findFirst().orElse(null);
    }

}
