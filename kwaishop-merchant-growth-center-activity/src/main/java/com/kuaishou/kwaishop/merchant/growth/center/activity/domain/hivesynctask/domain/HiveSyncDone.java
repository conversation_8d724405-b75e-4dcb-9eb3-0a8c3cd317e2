package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.domain;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-27
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Component
public @interface HiveSyncDone {
    String bizCode();
}
