package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.factory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.OffsetEventHappenTimeFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.OffsetEventDataSourceTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
@Component
public class OffsetEventDataSourceFactory {
    @Resource
    private List<OffsetEventHappenTimeFetchService<?>> offsetEventHappenTimeFetchServices;

    private final Map<OffsetEventDataSourceTypeEnum, OffsetEventHappenTimeFetchService<?>> map = new HashMap<>(4);

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(offsetEventHappenTimeFetchServices)) {
            return;
        }
        offsetEventHappenTimeFetchServices.forEach(
                offsetEventHappenTimeFetchService -> map.put(offsetEventHappenTimeFetchService.getDataSourceType(),
                        offsetEventHappenTimeFetchService));
    }

    public OffsetEventHappenTimeFetchService<?> route(OffsetEventDataSourceTypeEnum offsetEventDataSourceTypeEnum) {
        return map.get(offsetEventDataSourceTypeEnum);
    }
}
