package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ActivityPerfEnum.MQ_BATCH_USER_EXECUTE_EVENT;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityStringListConfigKey.batchExecuteJumpEventIdList;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfScene;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.BatchExecuteResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.ExecuteHandleResult;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.framework.model.event.BatchExecuteEvent;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.enums.BatchExecuteType;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.BatchExecuteUserEventMsg;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-15
 */
@Slf4j
public abstract class BaseExecuteProcess {

    @Autowired
    private StatisticsMsgProduceService msgProduceService;

    @Autowired
    protected StatisticsCacheService statisticsCacheService;

    /**
     * 用户批量切分转发
     */
    public void partitionCrowdSendMsg(Set<Long> totalCrowd, BatchExecuteEvent event) {
        // 缓存总人数
        statisticsCacheService.setBatchExecuteTotalNum(event.getEventId(), totalCrowd.size());
        // 遍历活动下用户(进行切分)
        int partitionSize = getPartitionSize();
        // 事件类型
        int executeType = event.getExecuteType();
        // 事件ID
        String eventId = event.getEventId();
        List<List<Long>> partitionUsers = Lists.partition(new ArrayList<>(totalCrowd), partitionSize);
        if (!EnvUtils.isProd()) {
            log.info("partitionCrowdSendMsg eventId {} userIdSize {} pSize {}", event.getEventId(), totalCrowd.size(),
                    partitionUsers.size());
        }
        partitionUsers.forEach(userIdList -> msgProduceService.sendBatchExecuteMsg(executeType,
                eventId, buildExecuteConfig(event, userIdList), userIdList, needFinishAction(), needRetryWhenExp()));
    }

    /**
     * 批量用户处理
     */
    public void batchExecute(BatchExecuteUserEventMsg eventMsg) {
        List<Long> userIdList = eventMsg.getUserIdList();
        String eventId = eventMsg.getEventId();
        Integer executeType = eventMsg.getExecuteType();
        // 统一幂等校验逻辑
        List<Long> needHandleUser = filterUser(userIdList, eventId, executeType);
        if (CollectionUtils.isEmpty(needHandleUser)) {
            return;
        }
        // 直接返回
        List<String> jumpEventIds = batchExecuteJumpEventIdList.get();
        if (jumpEventIds.contains(eventId)) {
            return;
        }
        ExecuteHandleResult result = batchCustomizeExecute(userIdList, eventId, eventMsg.getExecuteConfig());
        // 自定义批量用户处理逻辑
        // 更新用户处理成功信息
        statisticsCacheService.batchExecuteAddUserToSuccessList(eventId, result.getSuccessUserList());
        List<Long> failUserList = result.getFailUserList();
        statisticsCacheService.batchExecuteAddUserToFailList(eventId, failUserList);
        // kim结果播报
        if (eventMsg.getFinishAction()) {
            try {
                batchExecuteResultFinishAction(statisticsCacheService.getBatchExecuteResult(eventId));
            } catch (Exception e) {
                log.error("[批量处理框架] 处理结束动作异常！{}", eventId, e);
                perfException(MQ_BATCH_USER_EXECUTE_EVENT, "finish.action", eventId, e.getClass().getSimpleName());
            }
        }
        if (eventMsg.getRetry() && CollectionUtils.isNotEmpty(failUserList)) {
            log.warn("当前批处理场景需要重试 eventId {} failUserList size {}", eventId, failUserList.size());
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "当前批处理场景需要重试");
        }
    }

    protected List<Long> filterUser(List<Long> userIdList, String eventId, Integer executeType) {
        // 第一层幂等，缓存过滤
        List<Long> continueUserList = Lists.newArrayList();
        for (Long userId : userIdList) {
            if (isCheckUserAlreadyDone(eventId, executeType, userId)) {
                perfScene(MQ_BATCH_USER_EXECUTE_EVENT, "cacheFilter", eventId);
                continue;
            }
            continueUserList.add(userId);
        }
        return continueUserList;
    }

    private boolean isCheckUserAlreadyDone(String eventId, Integer executeType, Long userId) {
        if (filterSuccessUser()) {
            return statisticsCacheService.batchExecuteCheckUserAlreadySuccess(eventId, executeType, userId);
        }
        return statisticsCacheService.batchExecuteCheckUserAlreadyDone(eventId, executeType, userId);
    }

    protected boolean needFinishAction() {
        return false;
    }

    /**
     * 是否重试
     *
     * @return
     */
    protected boolean needRetryWhenExp() {
        return false;
    }

    /**
     * 是否过滤成功用户
     *
     * @return
     */
    protected boolean filterSuccessUser() {
        return false;
    }

    ;

    protected void batchExecuteResultFinishAction(BatchExecuteResult handleResult) {
    }

    /**
     * 自定义批量用户处理逻辑
     */
    abstract ExecuteHandleResult batchCustomizeExecute(List<Long> userIdList, String eventId, String executeConfig);

    /**
     * 获取执行配置必要信息
     */
    abstract String buildExecuteConfig(BatchExecuteEvent event, List<Long> userIdList);

    /**
     * 获取用户切分数量大小
     */
    abstract int getPartitionSize();

    /**
     * 获取处理类型
     */
    abstract BatchExecuteType getBatchExecuteType();
}
