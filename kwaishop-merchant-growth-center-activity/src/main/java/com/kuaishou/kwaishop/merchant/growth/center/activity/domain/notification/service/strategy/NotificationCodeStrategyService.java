package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy;

import java.util.List;
import java.util.Set;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.NotificationConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.enums.NotificationCodeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-20
 */
public interface NotificationCodeStrategyService {
    /**
     * 获取支持的NotificationCode
     */
    Set<NotificationCodeEnum> getNotificationCodeSet();

    /**
     * 构建NotificationPushConfig
     */
    List<NotificationPushConfigBO> buildNotificationConfig(ActivityDO activity, List<TaskDO> taskList,
            List<AwardConfigDO> awardConfigList, NotificationConfigBO notificationConfig);
}
