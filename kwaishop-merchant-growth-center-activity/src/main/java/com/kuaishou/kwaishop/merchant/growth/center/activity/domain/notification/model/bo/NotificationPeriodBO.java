package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationPeriodBO {
    /**
     * 周期类型，1-绝对，2-相对
     */
    private int periodType;
    /**
     * 天
     */
    private String day;
    /**
     * 时
     */
    private String hour;
    /**
     * 分钟
     */
    private String minute;

    /**
     * 配置的间隔天数
     */
    private long intervalDay;
}
