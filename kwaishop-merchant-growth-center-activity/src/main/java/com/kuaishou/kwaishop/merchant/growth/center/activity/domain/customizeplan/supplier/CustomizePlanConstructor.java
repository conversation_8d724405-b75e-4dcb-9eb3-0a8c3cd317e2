package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.supplier;

import java.util.Map;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.CustomizePlan;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.enums.ConstructorEnum;

/**
 * 定制计划构造器
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
public interface CustomizePlanConstructor {

    /**
     * 构建定制计划
     * @param userId 用户ID
     * @param configMap 构造器参数
     * @return 构建完成的定制计划
     */
    CustomizePlan initCustomizePlan(long userId, Map<String, Object> configMap);

    /**
     * 构造器类型
     */
    ConstructorEnum getConstructorCode();

    /**
     * 获取该构造器对应参数的map的key
     */
    String getParamMapKey();
}