package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.enums.OffsetEventDataSourceTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OffsetEventConfigBO<T> {
    /**
     * 偏移事件类型
     */
    private String eventType;

    /**
     * 获取偏移点的数据源类型
     */
    private OffsetEventDataSourceTypeEnum dataSourceType;

    /**
     * 获取偏移点的数据源配置
     */
    private T dataSourceConfig;
}
