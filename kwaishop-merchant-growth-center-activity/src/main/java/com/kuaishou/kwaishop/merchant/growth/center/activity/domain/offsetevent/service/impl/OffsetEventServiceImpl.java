package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonListConfigKey.offsetEventConfig;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.base.Preconditions;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.core.client.OffsetEventHappenTimeFetchClient;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetEventConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.model.bo.OffsetPeriodConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.OffsetEventService;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-15
 */
@Service
public class OffsetEventServiceImpl implements OffsetEventService {
    @Resource
    private OffsetEventHappenTimeFetchClient offsetEventHappenTimeFetchClient;

    @Override
    public OffsetEventConfig getOffsetEventConfig(String offsetEventType) {
        if (StringUtils.isEmpty(offsetEventType)) {
            return null;
        }
        List<OffsetEventConfig> list = offsetEventConfig.getList();
        return list.stream().filter(offsetEventConfig -> offsetEventType.equals(offsetEventConfig.getEventType()))
                .findFirst().orElse(null);
    }

    @Override
    public Long getOffsetEventHappenTime(String offsetEventType, Long userId) {
        OffsetEventConfig offsetEventConfig = getOffsetEventConfig(offsetEventType);
        Preconditions.checkNotNull(offsetEventConfig, "offsetEventConfig is null");
        return offsetEventHappenTimeFetchClient.fetchOffsetEventHappenTime(offsetEventConfig, userId);
    }

    @Override
    public OffsetPeriodBO calculateOffsetPeriod(OffsetPeriodConfigBO offsetPeriodConfig, Long userId) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(offsetPeriodConfig.getOffsetEventType()),
                "offsetEventType is empty");
        Preconditions.checkNotNull(offsetPeriodConfig.getPeriodTime(), "periodTime is null");
        Preconditions.checkNotNull(offsetPeriodConfig.getRelativeTime(), "relativeTime is null");
        Long offsetEventHappenTime = getOffsetEventHappenTime(offsetPeriodConfig.getOffsetEventType(), userId);
        if (null == offsetEventHappenTime) {
            return OffsetPeriodBO.builder().build();
        }
        long startTime = offsetEventHappenTime + offsetPeriodConfig.getRelativeTime();
        long endTime = startTime + offsetPeriodConfig.getPeriodTime() - DateUtils.DAY_MILLISECONDS;
        return OffsetPeriodBO.builder()
                .startTime(DateUtils.getStartOfDay(startTime))
                .endTime(DateUtils.getEndOfDay(endTime))
                .offsetEventHappenTime(offsetEventHappenTime)
                .build();
    }
}
