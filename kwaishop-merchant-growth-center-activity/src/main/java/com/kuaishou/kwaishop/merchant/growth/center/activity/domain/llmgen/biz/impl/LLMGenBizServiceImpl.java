package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.biz.impl;

import static com.github.rholder.retry.WaitStrategies.fixedWait;
import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenPerfEnum.LLM_GEN_ASYNC;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenPerfEnum.LLM_GEN_MSG_SEND;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenPerfEnum.LLM_GEN_RESULT_QUERY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.DataSourceConstants.ACTIVITY_SINGLE_DATA_SOURCE_NAME;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.ValidateUtil.checkArgument;
import static com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfException;
import static com.kuaishou.kwaishop.merchant.growth.utils.perf.PerfUtil.perfFail;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.biz.LLMGenBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenAsyncParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenResultBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentGenResultQueryParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.bo.LLMContentKeyGenParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.enums.LLMGenSceneTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.bo.GenDataRecordContentBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.model.gendata.enums.GenDataStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.llmgen.service.LLMGenDataDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.common.protobuf.LLMContentGenAsyncMsg;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode.BasicErrorCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-21
 */
@Service
@Slf4j
public class LLMGenBizServiceImpl implements LLMGenBizService {

    @Autowired
    private LLMGenCacheService llmGenCacheService;

    @Autowired
    private LLMGenDataDomainService llmGenDataDomainService;

    @Autowired
    @Qualifier("llmContentGenAsyncMsgProducer")
    private MsgProducer llmContentGenAsyncMsgProducer;

    // 发送消息
    private static final Retryer<Boolean> RETRY = RetryerBuilder.<Boolean> newBuilder()
            .retryIfExceptionOfType(Exception.class)
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .withWaitStrategy(fixedWait(100, TimeUnit.MILLISECONDS))
            .build();

    @Override
    @TransactionalDataSourceRouting(ACTIVITY_SINGLE_DATA_SOURCE_NAME)
    public LLMContentGenAsyncBO genAsync(LLMContentGenAsyncParamBO param) {
        String uniqueKey = param.getUniqueKey();

        try {
            GenDataRecordBO originRecord = llmGenDataDomainService.queryByUniqueKey(uniqueKey);
            if (originRecord != null) {
                log.error("[llm物料异步生成] 生成记录已存在 param:{}", toJSON(param));
                throw new BizException(SERVER_ERROR, "重复请求");
            }

            GenDataRecordBO genDataRecord = GenDataRecordBO.builder()
                    .scene(param.getGenScene())
                    .uniqueKey(uniqueKey)
                    .status(GenDataStatusEnum.GENERATING.getStatus())
                    .content(GenDataRecordContentBO.builder()
                            .content(toJSON(Maps.newHashMap()))
                            .errorMsg("")
                            .build())
                    .ext(toJSON(Maps.newHashMap()))
                    .creator(param.getOperator())
                    .modifier(param.getOperator())
                    .build();
            llmGenDataDomainService.createGenDataRecord(genDataRecord);

            // 缓存结果失效时间
            Boolean cacheResult =
                    llmGenCacheService.setLLMContentGenAsyncUniqueKey(uniqueKey, param.getExpireSeconds());
            if (!cacheResult) {
                log.error("[llm物料异步生成] 缓存失败 param:{}", toJSON(param));
                throw new BizException(SERVER_ERROR, "缓存失败");
            }

            // 推送物料生成异步消息
            sendGenAsyncMsg(param, uniqueKey);

            return LLMContentGenAsyncBO.builder().uniqueKey(uniqueKey).build();
        } catch (Exception e) {
            log.error("[llm物料异步生成] 生成异常 param:{}", toJSON(param), e);
            perfException(LLM_GEN_ASYNC, uniqueKey);
            throw new BizException(SERVER_ERROR, "llm物料异步生成异常");
        }
    }

    private void sendGenAsyncMsg(LLMContentGenAsyncParamBO param, String uniqueKey) {
        LLMContentGenAsyncMsg message = LLMContentGenAsyncMsg.newBuilder()
                .setScene(param.getGenScene())
                .setCustomizeInfo(toJSON(param.getCustomizeInfo()))
                .setOperator(param.getOperator())
                .setUniqueKey(uniqueKey)
                .build();
        // 发送消息
        try {
            RETRY.call(() -> sendMsg(message));
        } catch (BizException | RetryException e) {
            log.error("[llm物料异步生成] llm物料异步生成消息重试发送失败！msg:{}", toJSON(message), e);
            perfFail(LLM_GEN_MSG_SEND, "afterRetry", e.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("[llm物料异步生成] llm物料异步生成消息重试处理异常！msg:{}", toJSON(message), e);
            perfException(LLM_GEN_MSG_SEND, e);
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
    }

    private Boolean sendMsg(LLMContentGenAsyncMsg message) {
        MqMessage eventMsg = llmContentGenAsyncMsgProducer.createMsgBuilder(message).build();
        MqSyncSendResult sendResult = llmContentGenAsyncMsgProducer.sendSync(eventMsg);
        if (!sendResult.isSuccess()) {
            log.error("[llm物料异步生成] 消息发送失败，{}", toJSON(message));
            perfFail(LLM_GEN_MSG_SEND, "sendFail");
            throw new BizException(BasicErrorCode.SERVER_ERROR);
        }
        log.info("[llm物料异步生成] 消息发送成功，{}", toJSON(message));
        return true;
    }

    @Override
    public LLMContentGenResultBO queryGenResult(LLMContentGenResultQueryParamBO param) {
        checkArgument(param != null, "llm物料查询参数不能为空");
        checkArgument(StringUtils.isNotBlank(param.getUniqueKey()), "llm物料查询唯一键不能为空");
        checkArgument(StringUtils.isNotBlank(param.getOperator()), "llm物料查询操作人不能为空");

        String uniqueKey = param.getUniqueKey();

        try {
            GenDataRecordBO genDataRecord = llmGenDataDomainService.queryByUniqueKey(uniqueKey);
            if (genDataRecord == null) {
                log.error("[llm物料查询] 生成记录不存在 param:{}", toJSON(param));
                perfException(LLM_GEN_RESULT_QUERY, "record.not.exist", uniqueKey);
                throw new BizException(SERVER_ERROR, "llm物料生成记录不存在");
            }

            // 如果在生成中，但是缓存已经时效，返回超时
            String cacheKey = llmGenCacheService.getLLMContentGenAsyncUniqueKey(uniqueKey);
            if (Objects.equals(genDataRecord.getStatus(), GenDataStatusEnum.GENERATING.getStatus())
                    && StringUtils.isBlank(cacheKey)) {
                log.warn("[llm物料查询] 生成超时 param:{}", toJSON(param));

                return LLMContentGenResultBO.builder()
                        .status(GenDataStatusEnum.GENERATE_FAILED.getStatus())
                        .errorMsg("生成超时，稍后再试").build();
            }

            GenDataRecordContentBO genDataContent = genDataRecord.getContent();
            if (genDataContent == null) {
                log.error("[llm物料查询] 生成记录异常 param:{}", toJSON(param));
                throw new BizException(SERVER_ERROR, "生成记录异常，请稍候再试");
            }

            String content = genDataContent.getContent();
            Map<String, Object> contentMap = StringUtils.isBlank(content)
                                             ? Maps.newHashMap()
                                             : fromJSON(content, Map.class, String.class, Object.class);

            LLMContentGenResultBO result = LLMContentGenResultBO.builder()
                    .status(genDataRecord.getStatus())
                    .content(contentMap)
                    .errorMsg(genDataContent.getErrorMsg())
                    .build();

            if (!EnvUtils.isProd()) {
                log.info("[llm物料查询] 查询完成 param:{}, result:{}", toJSON(param), toJSON(result));
            }

            return result;
        } catch (Exception e) {
            log.error("[llm物料查询] 查询失败 param:{}", toJSON(param), e);
            perfException(LLM_GEN_RESULT_QUERY, uniqueKey);
            throw new BizException(SERVER_ERROR, "llm物料查询查询失败");
        }
    }

    @Override
    public String genUniqueKey(LLMContentKeyGenParamBO param) {
        checkArgument(param != null, "llm物料生成唯一键参数不能为空");
        checkArgument(StringUtils.isNotBlank(param.getGenScene()), "llm物料生成场景不能为空");
        checkArgument(StringUtils.isNotBlank(param.getBizKey()), "llm物料生成业务键不能为空");

        String genScene = param.getGenScene();
        String bizKey = param.getBizKey();

        LLMGenSceneTypeEnum genSceneType = LLMGenSceneTypeEnum.getByScene(genScene);
        if (Objects.equals(genSceneType, LLMGenSceneTypeEnum.UNKNOWN)) {
            log.error("[llm物料生成唯一键] 生成场景不存在 param:{}", toJSON(param));
            throw new BizException(BasicErrorCode.PARAM_INVALID, "llm物料生成场景不存在");
        }

        return Joiner.on("_").join(genSceneType.getPrefix(), bizKey);
    }
}
