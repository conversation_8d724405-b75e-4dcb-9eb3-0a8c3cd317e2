package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizePlanConstructorConfig {
    /**
     * 定制计划补全code
     */
    private String constructorCode;

    /**
     * 参数
     */
    private Map<String, Object> configMap;
}
