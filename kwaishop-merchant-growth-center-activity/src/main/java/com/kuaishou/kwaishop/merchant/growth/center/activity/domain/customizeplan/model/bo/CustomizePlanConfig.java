package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomizePlanConfig {
    /**
     * 定制计划对应的唯一Code
     */
    private String planCode;

    /**
     * 定制计划降级开关
     */
    private Boolean upgradeSwitch;

    /**
     * 定制计划纬度的放量开关
     */
    private String grayTailNumber;

    /**
     * 定制计划展示优先级
     */
    private Integer priority;

    /**
     * 定制计划的基础信息（由运营提供）
     */
    private CustomizePlanBasicInfoConfig basicInfo;

    /**
     * 定制计划的数据来源配置
     */
    private CustomizePlanConstructorConfig customizePlanConstructor;

    /**
     * 准入规则配置
     */
    private AdmissionRuleConfig admissionRuleConfig;
}
