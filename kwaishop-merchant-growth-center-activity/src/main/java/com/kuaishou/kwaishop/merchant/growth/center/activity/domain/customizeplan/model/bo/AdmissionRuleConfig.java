package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdmissionRuleConfig {
    /**
     * 准入规则类型 对应AdmissionRuleEnum
     */
    private String type;

    /**
     * 参数
     */
    private Map<String, Object> configMap;
}
