package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.impl.template;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.EXECUTE;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.SKIP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationExecuteStatusEnum.STOP;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum.AWARD_DECR_TEXT;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.kuaishou.infra.kess.conf.common.utils.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardRecordExtraBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.UserAwardRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.param.AwardRecordQueryParam;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationExtendFunctionParamBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.NotificationPushConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.NotificationEntityTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums.TemplateParamTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.service.strategy.AbstractNotificationExtendFunctionParamService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.service.StrategyAwardService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-26
 */
@Slf4j
@Service
public class AwardDecrTextExtendFunctionParamService extends AbstractNotificationExtendFunctionParamService {

    @Autowired
    private AwardService awardService;

    @Autowired
    private StrategyAwardService strategyAwardService;

    @Autowired
    private AwardConfigLocalCacheService awardConfigLocalCacheService;

    @Override
    public List<TemplateParamTypeEnum> templateParamTypes() {
        return Lists.newArrayList(AWARD_DECR_TEXT);
    }

    @Override
    public NotificationExtendFunctionParamBO getExtendFunctionParams(long userId, NotificationPushConfigBO configBO,
            List<TemplateParamTypeEnum> templateParams) {
        Map<String, String> params = Maps.newHashMap();
        NotificationExtendFunctionParamBO result = NotificationExtendFunctionParamBO.builder()
                .templateParamMap(params)
                .executeStatus(EXECUTE)
                .build();
        // 获取用户奖励记录
        UserAwardRecordBO userAwardRecord = getUserAwardRecord(userId, configBO);
        if (userAwardRecord == null) {
            log.error("奖励记录为空,无法获取奖励取消通知文本, userId: {}, configBO: {}",
                    userId, toJSON(configBO));
            result.setExecuteStatus(SKIP);
            return result;
        }
        AwardRecordExtraBO recordExt = new AwardRecordExtraBO();
        if (userAwardRecord.getExtra() != null) {
            recordExt = ObjectMapperUtils.fromJSON(userAwardRecord.getExtra(), AwardRecordExtraBO.class);
        }
        if (StringUtils.isNotBlank(recordExt.getAwardDecrText())) {
            params.put(AWARD_DECR_TEXT.getName(), recordExt.getAwardDecrText());
        } else {
            result.setExecuteStatus(STOP);
            log.error("奖励取消通知文本为空, userAwardRecord: {}", toJSON(userAwardRecord));
        }
        result.setTemplateParamMap(params);
        return result;
    }

    /**
     * 获取用户奖励记录
     */
    private UserAwardRecordBO getUserAwardRecord(long userId, NotificationPushConfigBO configBO) {
        // 获取关联的奖励配置
        AwardConfigBO relatedAwardConfig = queryRelatedAwardConfig(configBO);
        if (relatedAwardConfig == null) {
            return null;
        }
        AwardRecordQueryParam queryParam = AwardRecordQueryParam.builder().userId(userId)
                .activityId(relatedAwardConfig.getActivityId()).entityId(relatedAwardConfig.getEntityId())
                .awardConfigId(relatedAwardConfig.getId()).build();
        List<UserAwardRecordBO> userActivityAwards = awardService.queryUserAwardRecord(queryParam);
        return CollectionUtils.isEmpty(userActivityAwards) ? null : userActivityAwards.get(0);
    }

    /**
     * 根据不同触达类型，通过不同方式获取奖励配置id
     */
    private AwardConfigBO queryRelatedAwardConfig(NotificationPushConfigBO configBO) {
        if (configBO.getEntityType() != NotificationEntityTypeEnum.AWARD.getVal()) {
            // 如果触达类型不是奖励，configBO中的entityId为策略id
            return strategyAwardService.getStrategyMainAwardRuleConfigWithCache(configBO.getEntityId());
        }
        // 如果触达类型是奖励，configBO中的entityId为奖励配置id
        AwardConfigDO awardConfigDO = awardConfigLocalCacheService.queryAwardConfigById(configBO.getEntityId());
        if (awardConfigDO == null) {
            return null;
        }
        return AwardConfigBO.builder().id(awardConfigDO.getId())
                .awardType(AwardTypeEnum.getByCode(awardConfigDO.getAwardType()))
                .activityId(awardConfigDO.getActivityId())
                .entityId(awardConfigDO.getEntityId()).build();

    }
}
