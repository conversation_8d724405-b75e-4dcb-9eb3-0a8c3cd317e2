package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.converter;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.launchCommonConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchActivityTimeInfo;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.bo.LaunchInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchCommonConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchExternalAccessConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.LaunchLiveEndPageConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchActivityTimeInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchEntityInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchEntityInfoDTO.Builder;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchInfoDTO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.launch.LaunchInfoDataDTO;
import com.kuaishou.protobuf.livestream.LiveCdnNodeView;
import com.kuaishou.protobuf.livestream.LiveEndPageKdsContent;
import com.kuaishou.protobuf.livestream.LiveEndPageVarietyContainerSize;
import com.kuaishou.protobuf.livestream.LiveEndPageVarietyModuleResponse;
import com.kuaishou.protobuf.livestream.LiveEndPageVarietyType;
import com.kuaishou.protobuf.livestream.LiveMotivationProtoModel;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-18
 */
public class LaunchConfigAssembler {

    private static final String LIVE_END_PAGE_DEFAULT_COMPONENT_NAME = "LiveTaskCard";

    public static LaunchInfoDataDTO convertToLaunchInfoData(List<LaunchInfoBO> launchInfoList) {
        LaunchInfoDataDTO.Builder builder = LaunchInfoDataDTO.newBuilder();
        if (CollectionUtils.isEmpty(launchInfoList)) {
            return builder.addAllLaunchInfo(Lists.newArrayList()).build();
        }

        List<LaunchInfoDTO> launchInfoDTOList = launchInfoList.stream().map(launchInfo -> {
            LaunchInfoDTO.Builder launchInfoBuilder = LaunchInfoDTO.newBuilder();
            launchInfoBuilder.setActivityId(launchInfo.getActivityId());
            launchInfoBuilder.setActivityName(
                    StringUtils.defaultIfEmpty(launchInfo.getActivityName(), StringUtils.EMPTY));
            launchInfoBuilder.setActivityDesc(
                    StringUtils.defaultIfEmpty(launchInfo.getActivityDesc(), StringUtils.EMPTY));
            if (null != launchInfo.getUserActivityStatus()) {
                launchInfoBuilder.setUserActivityStatus(launchInfo.getUserActivityStatus());
            }
            launchInfoBuilder.setShowTag(StringUtils.defaultIfEmpty(launchInfo.getShowTag(), StringUtils.EMPTY));
            launchInfoBuilder.setShowTagName(
                    StringUtils.defaultIfEmpty(launchInfo.getShowTagName(), StringUtils.EMPTY));
            launchInfoBuilder.setLaunchContentInfo(StringUtils
                    .defaultIfEmpty(launchInfo.getLaunchContentInfo(), StringUtils.EMPTY));

            List<LaunchEntityInfoDTO> launchEntityInfoDTOList =
                    ListUtils.emptyIfNull(launchInfo.getEntityContentInfoList()).stream().map(entityContentInfo -> {
                        Builder launchEntityInfoBuilder = LaunchEntityInfoDTO.newBuilder();
                        launchEntityInfoBuilder.setEntityType(entityContentInfo.getEntityType());
                        launchEntityInfoBuilder.setEntityId(entityContentInfo.getEntityId());
                        launchEntityInfoBuilder.setEntityLaunchContentInfo(StringUtils
                                .defaultIfEmpty(entityContentInfo.getContent(), StringUtils.EMPTY));
                        return launchEntityInfoBuilder.build();
                    }).collect(Collectors.toList());
            launchInfoBuilder.addAllEntityInfo(launchEntityInfoDTOList);
            launchInfoBuilder.setExt(StringUtils.defaultIfEmpty(launchInfo.getExt(), toJSON(new HashMap<>())));
            launchInfoBuilder.setPriority(null == launchInfo.getPriority() ? 0 : launchInfo.getPriority());
            launchInfoBuilder.setShowConfig(StringUtils.defaultIfEmpty(launchInfo.getShowConfig(), StringUtils.EMPTY));
            LaunchActivityTimeInfo launchActivityTimeInfo = launchInfo.getLaunchActivityTimeInfo();
            if (launchActivityTimeInfo != null) {
                LaunchActivityTimeInfoDTO launchActivityTimeInfoDTO = LaunchActivityTimeInfoDTO.newBuilder()
                        .setStartTime(convertTime(launchActivityTimeInfo.getStartTime()))
                        .setEndTime(convertTime(launchActivityTimeInfo.getEndTime()))
                        .setDrawEndTime(convertTime(launchActivityTimeInfo.getDrawEndTime()))
                        .setShowEndTime(convertTime(launchActivityTimeInfo.getShowEndTime())).build();
                launchInfoBuilder.setActivityTimeInfo(launchActivityTimeInfoDTO);
            }

            return launchInfoBuilder.build();
        }).collect(Collectors.toList());

        return builder.addAllLaunchInfo(launchInfoDTOList).build();
    }

    private static Long convertTime(Long timestamp) {
        return timestamp == null || timestamp < 0L ? 0L : timestamp;
    }

    public static List<LiveMotivationProtoModel> convertToLiveMotivationProto(List<LaunchInfoBO> launchInfoList) {
        if (CollectionUtils.isEmpty(launchInfoList)) {
            return Lists.newArrayList();
        }

        launchInfoList = launchInfoList.subList(0, 1);
        return launchInfoList.stream().map(LaunchConfigAssembler::convertToLiveMotivationProto)
                .collect(Collectors.toList());
    }

    public static LiveMotivationProtoModel convertToLiveMotivationProto(LaunchInfoBO launchInfo) {
        String launchContentInfo = launchInfo.getLaunchContentInfo();
        if (StringUtils.isBlank(launchContentInfo)) {
            return null;
        }

        Map<String, Object> launchContentInfoMap = fromJSON(launchContentInfo, Map.class, String.class, Object.class);
        if (MapUtils.isEmpty(launchContentInfoMap)) {
            return null;
        }

        String banner = MapUtils.getString(launchContentInfoMap, "banner", "");
        String actionJumpUrl = MapUtils.getString(launchContentInfoMap, "actionJumpUrl", "");
        if (StringUtils.isBlank(actionJumpUrl)) {
            return null;
        }

        LiveMotivationProtoModel.Builder builder = LiveMotivationProtoModel.newBuilder();
        builder.addAllBackgroundUrl(
                Lists.newArrayList(LiveCdnNodeView.newBuilder().setCdn(banner).setUrl(banner).build()));
        builder.setJumpUrl(actionJumpUrl);
        return builder.build();
    }

    public static LiveEndPageVarietyModuleResponse convertToLiveEndPageKdsContent(List<LaunchInfoBO> launchInfoList,
            String version) {
        if (CollectionUtils.isEmpty(launchInfoList)) {
            return LiveEndPageVarietyModuleResponse.getDefaultInstance();
        }

        LaunchCommonConfigBO commonConfig = launchCommonConfig.getObject();
        if (commonConfig == null || commonConfig.getExternalAccessConfig() == null) {
            return LiveEndPageVarietyModuleResponse.getDefaultInstance();
        }
        LaunchExternalAccessConfigBO externalAccessConfig = commonConfig.getExternalAccessConfig();

        // 默认
        String bundleId = StringUtils.defaultIfEmpty(externalAccessConfig.getLiveEndPageBundleId(), "");
        String componentName = LIVE_END_PAGE_DEFAULT_COMPONENT_NAME;
        Float hwRatio = externalAccessConfig.getLiveEndPageVarietyContainerHwRatio();
        Integer height = externalAccessConfig.getLiveEndPageVarietyContainerHeight();

        // 根据上游版本下发
        Map<String, LaunchLiveEndPageConfigBO> liveEndPageVersionConfigMap =
                externalAccessConfig.getLiveEndPageVersionConfigMap();
        if (StringUtils.isNotBlank(version) && MapUtils.isNotEmpty(liveEndPageVersionConfigMap)) {
            LaunchLiveEndPageConfigBO launchLiveEndPageConfig = liveEndPageVersionConfigMap.get(version);
            if (launchLiveEndPageConfig != null) {
                bundleId = StringUtils.defaultIfEmpty(launchLiveEndPageConfig.getBundleId(), bundleId);
                componentName = StringUtils.defaultIfEmpty(launchLiveEndPageConfig.getComponentName(), componentName);
                hwRatio = launchLiveEndPageConfig.getLiveEndPageVarietyContainerHwRatio() == null
                          ? hwRatio : launchLiveEndPageConfig.getLiveEndPageVarietyContainerHwRatio();
                height = launchLiveEndPageConfig.getLiveEndPageVarietyContainerHeight() == null
                         ? height : launchLiveEndPageConfig.getLiveEndPageVarietyContainerHeight();
            }
        }

        LiveEndPageVarietyModuleResponse.Builder builder = LiveEndPageVarietyModuleResponse.newBuilder();

        LiveEndPageKdsContent.Builder liveEndPageKdsContentBuilder = LiveEndPageKdsContent.newBuilder();
        liveEndPageKdsContentBuilder.setBundleId(bundleId);
        liveEndPageKdsContentBuilder.setViewKey(
                StringUtils.defaultIfEmpty(externalAccessConfig.getLiveEndPageViewKey(), ""));
        liveEndPageKdsContentBuilder.setData(toJSON(launchInfoList.get(0)));
        liveEndPageKdsContentBuilder.setMinBundleVer(externalAccessConfig.getLiveEndPageMinBundleVer());
        liveEndPageKdsContentBuilder.setContainerSize(LiveEndPageVarietyContainerSize.newBuilder()
                .setHwRatio(hwRatio).setHeight(height).build());
        liveEndPageKdsContentBuilder.setLogParams(
                StringUtils.defaultIfEmpty(externalAccessConfig.getLiveEndPageLogParams(), ""));
        liveEndPageKdsContentBuilder.setComponentName(componentName);

        builder.setKdsContent(liveEndPageKdsContentBuilder.build());
        builder.setVarietyType(LiveEndPageVarietyType.RN);
        return builder.build();
    }
}
