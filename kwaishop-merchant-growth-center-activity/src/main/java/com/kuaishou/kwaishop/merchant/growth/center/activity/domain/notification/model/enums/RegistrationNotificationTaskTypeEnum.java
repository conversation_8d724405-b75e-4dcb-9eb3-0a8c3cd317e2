package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.enums;

import java.util.List;

import com.google.common.collect.Lists;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-24
 */
public enum RegistrationNotificationTaskTypeEnum {
    UNKNOWN(0, "未知"),
    NOTIFICATION_EXECUTE(1, "执行推送"),
    NOTIFICATION_CANCEL(2, "取消推送")
    ;

    private int val;
    private String desc;

    RegistrationNotificationTaskTypeEnum(int val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public static RegistrationNotificationTaskTypeEnum of(int code) {
        for (RegistrationNotificationTaskTypeEnum type : RegistrationNotificationTaskTypeEnum.values()) {
            if (type.getVal() == code) {
                return type;
            }
        }
        return NOTIFICATION_EXECUTE;
    }

    public static List<RegistrationNotificationTaskTypeEnum> getAllValidTypeEnums() {
        return Lists.newArrayList(NOTIFICATION_EXECUTE, NOTIFICATION_CANCEL);
    }

    public int getVal() {
        return val;
    }

    public void setVal(int val) {
        this.val = val;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
