package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.service;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.hivesynctask.domain.HiveSyncDone;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-27
 */
@Component
public class HiveSyncDoneMsgProcessorFactory implements ApplicationContextAware, InitializingBean {
    private ApplicationContext applicationContext;

    private final Map<String, AbstractHiveSyncDoneMsgProcessor> processorMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public AbstractHiveSyncDoneMsgProcessor getProcessor(String bizCode) {
        return processorMap.get(bizCode);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, AbstractHiveSyncDoneMsgProcessor> beans =
                applicationContext.getBeansOfType(AbstractHiveSyncDoneMsgProcessor.class);
        if (MapUtils.isEmpty(beans)) {
            return;
        }
        for (AbstractHiveSyncDoneMsgProcessor processor : beans.values()) {
            HiveSyncDone annotation = AnnotationUtils.findAnnotation(processor.getClass(), HiveSyncDone.class);
            if (null == annotation) {
                throw new RuntimeException(String.format("类:%s未配置注解", processor.getClass().getSimpleName()));
            }
            String bizCode = annotation.bizCode();
            if (StringUtils.isEmpty(bizCode)) {
                throw new RuntimeException(String.format("类:%s注解的bizCode为空", processor.getClass().getSimpleName()));
            }
            processorMap.put(bizCode, processor);
        }
    }
}
