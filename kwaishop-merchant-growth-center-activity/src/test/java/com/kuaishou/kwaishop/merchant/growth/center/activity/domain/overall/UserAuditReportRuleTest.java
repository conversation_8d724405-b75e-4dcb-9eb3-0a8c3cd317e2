package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.overall;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.AuditTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.audit.model.enums.UserAuditStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordChangeTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataContentBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataHeaderBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.utils.OverallMapUtils;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.RuleCenterClient;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.executor.utils.RulePerfUtil;

/**
 * <AUTHOR>
 */
public class UserAuditReportRuleTest {

    private static String json = "{\n"
            + "    \"origin.overallChangeType\": \"USPCJ\",\n"
            + "    \"origin.recordType\": \"userAudit\",\n"
            + "    \"origin.beforeEventData\": {\n"
            + "        \"id\": 33086,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"subjectId\": \"2181835997_10419_569190_4217317\",\n"
            + "        \"auditConfigId\": 1001326,\n"
            + "        \"activityId\": 10419,\n"
            + "        \"auditTime\": 1666086556191,\n"
            + "        \"status\": 30,\n"
            + "        \"warningTimes\": 0,\n"
            + "        \"auditMsgLog\": \"\",\n"
            + "        \"createTime\": 1666086556191,\n"
            + "        \"updateTime\": 1666086572938,\n"
            + "        \"version\": 1,\n"
            + "        \"deleted\": 0,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\"\n"
            + "    },\n"
            + "    \"origin.afterEventData\": {\n"
            + "        \"id\": 33086,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"subjectId\": \"2181835997_10419_569190_4217317\",\n"
            + "        \"auditConfigId\": 1001326,\n"
            + "        \"activityId\": 10419,\n"
            + "        \"auditTime\": 1666086556191,\n"
            + "        \"status\": 30,\n"
            + "        \"warningTimes\": 0,\n"
            + "        \"auditMsgLog\": \"\",\n"
            + "        \"createTime\": 1666086556191,\n"
            + "        \"updateTime\": 1666086572938,\n"
            + "        \"version\": 1,\n"
            + "        \"deleted\": 0,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\"\n"
            + "    },\n"
            + "    \"taskActivity.name\": \"活动名称\",\n"
            + "    \"taskActivity.startTime\": 1688373266000,\n"
            + "    \"taskActivity.endTime\": 1688373266000,\n"
            + "    \"taskActivity.resourceActivityId\": 1,\n"
            + "    \"taskIndicator.name\": \"测试指标名称\",\n"
            + "    \"task.name\": \"测试任务名称\",\n"
            + "    \"taskAudit.entityId\": 11,\n"
            + "    \"taskAudit.entityType\": 1,\n"
            + "    \"taskAudit.riskCode\": \"测试风控保护码\",\n"
            + "    \"taskAudit.auditWaitTime\": 22\n"
            + "}";

    public static void main(String[] args) {
        Map<String, Object> context = ObjectMapperUtils.fromJson(json);
        UserAuditReportRuleTest ruleTest = new UserAuditReportRuleTest();
        String result = ruleTest.buildActivityData(context);
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("context", context);
        Object ruleResult = RuleCenterClient.getInstance()
                .ruleExecute("overall_user_audit_report_data_rule", ruleContext);
       //System.err.println(result);
        System.err.println(ruleResult);
    }

    private String buildActivityData(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String changeType = MapUtils.getString(context, "origin.overallChangeType");
        String recordType = MapUtils.getString(context, "origin.recordType");
        if (StringUtils.isBlank(changeType) || !OverallRecordTypeEnum.USER_AUDIT_RECORD.getCode().equals(recordType)) {
            result.put("result", 0);
            result.put("errorMsg", "变更类型不能为空，且数据类型需要为userAudit");
            return ObjectMapperUtils.toJSON(result);
        }
        Long resourceActivityId = MapUtils.getLong(context, "taskActivity.resourceActivityId");
        // 对于没有和横向关联的活动，暂时不做上报
        if (resourceActivityId == null) {
            result.put("result", 1);
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> beforeEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.beforeEventData");
        Map<String, Object> afterEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.afterEventData");
        OverallProcessDataBO processData = new OverallProcessDataBO();
        // header数据
        OverallProcessDataHeaderBO processDataHeader = buildProcessDataHeader(afterEventData, changeType,
                recordType, resourceActivityId);
        // 变更前数据
        OverallProcessDataContentBO beforeContent = buildProcessBeforeContent(beforeEventData);
        // 变更后数据
        OverallProcessDataContentBO afterContent = buildProcessAfterContent(context, beforeEventData,
                afterEventData, changeType);
        String creator =  MapUtils.getString(afterEventData, "creator");
        processData.setEventTime(RulePerfUtil.getCurrentTimeMs());
        processData.setSource("resourceOverall");
        // 原始消息里暂时只能拿到创建人
        processData.setOperator(creator);
        processData.setDataHeader(processDataHeader);
        processData.setBeforeContent(beforeContent);
        processData.setAfterContent(afterContent);
        result.put("data", ObjectMapperUtils.toJSON(processData));
        result.put("result", 1);
        return ObjectMapperUtils.toJSON(result);
    }

    /**
     * 变更前数据JSON
     */
    private OverallProcessDataContentBO buildProcessBeforeContent(Map<String, Object> beforeEventData) {
        OverallProcessDataContentBO beforeData = new OverallProcessDataContentBO();
        beforeData.setDesc(StringUtils.EMPTY);
        String beforeDataJson = MapUtils.isEmpty(beforeEventData) ? "" : ObjectMapperUtils.toJSON(beforeEventData);
        beforeData.setData(beforeDataJson);
        return beforeData;
    }

    /**
     * 变更后数据JSON
     */
    private OverallProcessDataContentBO buildProcessAfterContent(Map<String, Object> context,
            Map<String, Object> beforeEventData, Map<String, Object> afterEventData, String changeType) {
        OverallProcessDataContentBO afterData = new OverallProcessDataContentBO();
        String changeDesc = buildAfterChangeDesc(context, beforeEventData, afterEventData, changeType);
        String afterDataJson = MapUtils.isEmpty(afterEventData) ? "" : ObjectMapperUtils.toJSON(afterEventData);
        afterData.setDesc(changeDesc);
        afterData.setData(afterDataJson);
        return afterData;
    }

    private String buildAfterChangeDesc(Map<String, Object> context,Map<String, Object> beforeEventData,
            Map<String, Object> afterEventData, String changeType) {
        Long activityId = MapUtils.getLong(afterEventData, "activityId", Long.valueOf("0"));
        Long auditTime = MapUtils.getLong(afterEventData, "auditTime", Long.valueOf("0"));
        Long auditConfigId = MapUtils.getLong(afterEventData, "auditConfigId", Long.valueOf("0"));
        Long auditId = MapUtils.getLong(context, "taskAudit.auditId", Long.valueOf("0"));
        AuditTypeEnum auditType = AuditTypeEnum.getByCode(MapUtils.getInteger(context, "taskAudit.auditType", 0));
        String riskCode = MapUtils.getString(context, "taskAudit.riskCode", "");
        Long auditWaitTime = MapUtils.getLong(context, "taskAudit.auditWaitTime", Long.valueOf("0"));
        EntityTypeEnum entityType = EntityTypeEnum.getByCode(MapUtils
                .getInteger(context, "taskAudit.entityType", 0));
        UserAuditStatusEnum beforeStatus = UserAuditStatusEnum.getByCode(MapUtils
                .getInteger(beforeEventData, "status", 0));
        UserAuditStatusEnum afterStatus = UserAuditStatusEnum.getByCode(MapUtils
                .getInteger(afterEventData, "status", 0));
        String desc = "";
        // 活动记录创建
        if (OverallRecordChangeTypeEnum.USER_AUDIT_CREATE.getCode().equals(changeType)) {
            desc = String.format("用户审批记录创建，活动ID:%s，实体类型:[%s-%s]，审核ID:%s，审批配置ID:%s，审核类型:[%s-%s]，"
                            + "审批等待时间:%s，风控保护码:%s", activityId, entityType.getCode(), entityType.getDesc(),
                    auditId, auditConfigId, auditType.getCode(), auditType.getDesc(), auditWaitTime, riskCode);
        } else if (OverallRecordChangeTypeEnum.USER_AUDIT_UPDATE.getCode().equals(changeType)) {
            Map<String, String> diffMap = OverallMapUtils.diffMap(beforeEventData, afterEventData);
            desc = String.format("用户审批记录更新，活动ID:%s，实体类型:[%s-%s]，审核ID:%s，变更前状态:[%s-%s]，"
                            + "变更后状态:[%s-%s], 变更字段明细:%s", activityId, entityType.getCode(),
                    entityType.getDesc(), auditId, beforeStatus.getCode(), beforeStatus.getDesc(),
                    afterStatus.getCode(), afterStatus.getDesc(), diffMap);
        } else if (OverallRecordChangeTypeEnum.USER_AUDIT_SUCCESS.getCode().equals(changeType)) {
            desc = String.format("用户记录审批成功，活动ID:%s，实体类型:[%s-%s]，审核ID:%s，审核类型:[%s-%s]，送审时间:%s，当前状态:[%s-%s]，风控保护码:%s",
                    activityId, entityType.getCode(), entityType.getDesc(), auditId, auditType.getCode(),
                    auditType.getDesc(), auditTime, afterStatus.getCode(), afterStatus.getDesc(), riskCode);
        } else if (OverallRecordChangeTypeEnum.USER_AUDIT_FAIL.getCode().equals(changeType)) {
            desc = String.format("用户记录审批失败，活动ID:%s，实体类型:[%s-%s]，审核ID:%s，审核类型:[%s-%s]，送审时间:%s，当前状态:[%s-%s]，风控保护码:%s",
                    activityId, entityType.getCode(), entityType.getDesc(), auditId, auditType.getCode(),
                    auditType.getDesc(), auditTime, afterStatus.getCode(), afterStatus.getDesc(), riskCode);
        }
        return desc;
    }

    /**
     * 上报数据的Header
     */
    private OverallProcessDataHeaderBO buildProcessDataHeader(Map<String, Object> afterEventData,
            String changeType, String recordType, Long resourceActivityId) {
        OverallProcessDataHeaderBO dataHeader = new OverallProcessDataHeaderBO();
        // 用分布式唯一ID用做检索key
        Long id = MapUtils.getLong(afterEventData, "id", Long.valueOf("0"));
        Long userId = MapUtils.getLong(afterEventData, "userId", Long.valueOf("0"));
        OverallRecordChangeTypeEnum changeTypeEnum = OverallRecordChangeTypeEnum.of(changeType);
        dataHeader.setUserId(userId);
        dataHeader.setActivityId(resourceActivityId);
        dataHeader.setChangeType(changeType);
        dataHeader.setRecordType(recordType);
        dataHeader.setSearchKey(changeTypeEnum.getTypePrefix() + "_" + id);
        dataHeader.setActivityType(0);
        return dataHeader;
    }

}
