package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.biz.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kuaishou.kwaishop.merchant.growth.center.activity.app.schedule.activity.StrategyAdminActivityOnlineTask;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.model.enums.ManualTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.service.ActivityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.activity.service.UserActivityOperateService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.kstry.ActivityKsTryService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.biz.UserActivityCustomBizService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.CancelUserActivityService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityDrawService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.ActivityTestUserDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.MerchantActivityLayerDataLocalDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.StatisticsEsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.InterestFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.bo.UserHandlerResBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserActivityRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.task.test.ReDrawActivityAndRecoverIndicatorProgressRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-04
 */
class ActivityTestBizServiceImplTest {
    @Mock
    CancelUserActivityService cancelUserActivityService;
    @Mock
    MerchantActivityLayerDataLocalDAO merchantActivityLayerDataLocalDAO;
    @Mock
    ActivityService activityService;
    @Mock
    UserActivityDrawService userActivityDrawService;
    @Mock
    ActivityTestUserDAO activityTestUserDAO;
    @Mock
    UserTaskRecordDAO userTaskRecordDAO;
    @Mock
    UserActivityRecordDAO userActivityRecordDAO;
    @Mock
    InterestFetchService interestFetchService;
    @Mock
    ActivityLocalCacheService activityLocalCacheService;
    @Mock
    ActivityKsTryService activityKsTryService;
    @Mock
    AdminCacheService adminCacheService;
    @Mock
    TaskLocalCacheService taskLocalCacheService;
    @Mock
    StrategyAdminActivityOnlineTask strategyAdminActivityOnlineTask;
    @Mock
    StatisticsEsDAO statisticsEsDAO;
    @Mock
    UserActivityCustomBizService userActivityCustomBizService;
    @Mock
    IndicatorRecordDAO indicatorRecordDAO;
    @Mock
    IndicatorCacheService indicatorCacheService;
    @Mock
    UserActivityOperateService userActivityOperateService;

    @InjectMocks
    ActivityTestBizServiceImpl activityTestBizServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testReDrawActivityAndRecoveryIndicatorProgress() {
        List<UserTaskRecordDO> userTaskList = buildUserTaskRecordDOList();
        when(userTaskRecordDAO.queryUserTaskRecordList(anyLong(), anyLong(), any(List.class))).thenReturn((userTaskList));
        UserActivityRecordDO userActivityRecordDO = buildUserActivityRecordDOList();
        when(userActivityRecordDAO.queryUserActivityRecord(anyLong(), anyLong(), anyBoolean())).thenReturn(userActivityRecordDO);
        when(activityLocalCacheService.queryActivityInfo(anyLong())).thenReturn(new ActivityDO("name", "showName",
                "bizName", Integer.valueOf(0), Long.valueOf(1), "description", Integer.valueOf(0), Integer.valueOf(0)
                , "crowdConfig", "alias", Long.valueOf(1), Long.valueOf(1), Long.valueOf(1), Long.valueOf(1),
                Integer.valueOf(0), "ext", Integer.valueOf(0), "cycleConfig", "completeCondition", Long.valueOf(1),
                "showConfig", "newSellerMustTask", Integer.valueOf(0), "frontConfig", Long.valueOf(1), "attachInfo",
                "galaxyPageConfig", null));
        List<TaskDO> taskList = buildTaskDOList();
        when(taskLocalCacheService.getTaskListByActivityId(anyLong())).thenReturn(taskList);
        List<TaskDO> subTaskList = buildSubTaskDOList();
        when(taskLocalCacheService.batchGetChildTask(any(List.class))).thenReturn(subTaskList);
        when(userActivityCustomBizService.handleSingleUserTaskDelete(anyLong(), anyLong(), any(List.class),
                any(List.class), anyBoolean(), anyBoolean())).thenReturn(new UserHandlerResBO());
        List<IndicatorRecordDO> indicatorRecordDOList = List.of(new IndicatorRecordDO(Long.valueOf(1), Long.valueOf(1), "uniqueId", Long.valueOf(1), Long.valueOf(1),
                "subIndicatorId", Long.valueOf(1), Long.valueOf(1), Long.valueOf(1), Long.valueOf(1), Long.valueOf(1), Long.valueOf(1), 10, "ext"));
        when(indicatorRecordDAO.listUserRecordOfTaskList(anyLong(), anyLong(), any(List.class), anyBoolean())).thenReturn(indicatorRecordDOList);

        ReDrawActivityAndRecoverIndicatorProgressRequest build =
                ReDrawActivityAndRecoverIndicatorProgressRequest.newBuilder()
                        .setActivityId(1)
                        .setManualType(ManualTypeEnum.SELLER_LIST_STRING.getCode())
                        .setManualEntity("2176580531")
                        .setOperator("sunhuzeng")
                        .setSleepTime(300)
                        .setSleepTimeBeforeUpdateIndicator(1000)
                        .build();
        activityTestBizServiceImpl.reDrawActivityAndRecoveryIndicatorProgress(build);
        verify(indicatorCacheService).cacheUserTaskIndicatorValue(anyLong(), anyLong(), anyLong(), anyLong());
//        verify(userActivityOperateService).drawUserActivityByTagNew(anyLong(), anyString(), anyBoolean(), anyString());
    }

    private List<TaskDO> buildSubTaskDOList() {
        TaskDO taskDO = new TaskDO();
        taskDO.setId(1L);
        return Collections.singletonList(taskDO);
    }

    private List<TaskDO> buildTaskDOList() {
        TaskDO taskDO = new TaskDO();
        taskDO.setParentTask(0L);
        taskDO.setId(1L);
        return Collections.singletonList(taskDO);
    }

    private UserActivityRecordDO buildUserActivityRecordDOList() {
        UserActivityRecordDO userActivityRecordDO = new UserActivityRecordDO();
        return userActivityRecordDO;
    }

    private List<UserTaskRecordDO> buildUserTaskRecordDOList() {
        UserTaskRecordDO userTaskRecordDO = new UserTaskRecordDO();
        userTaskRecordDO.setTaskId(1L);
        userTaskRecordDO.setStatus(UserTaskStatusEnum.FAIL.getValue());
        userTaskRecordDO.setParentId(1L);
        return Collections.singletonList(userTaskRecordDO);
    }
}
