package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;

import com.kuaishou.framework.config.util.TailNumberConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.ComponentDataBO;

/**
 * <AUTHOR> <hong<PERSON><PERSON>@kuaishou.com>
 * Created on 2022-04-21
 */
public class KconfTest {
    public static void main(String[] args) {
        TailNumberConfig from = TailNumberConfig.from("100;0-4;;1,2,3");
        System.out.println(from.isOnFor(3));
        ComponentDataBO componentDataBO = fromJSON("{}", ComponentDataBO.class);
        System.out.println(componentDataBO);
    }
}
