package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.combine;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.localDateToMilli;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil.milliToLocalDate;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Joiner;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.LocalDateUtil;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

/**
 * <AUTHOR> <hong<PERSON><PERSON>@kuaishou.com>
 * Created on 2022-03-11
 */
public class DateUtilTest {
    public static void main(String[] args) {
        Double a = new BigDecimal(0.068D).multiply(new BigDecimal(100L)).doubleValue();
        System.out.println(String.valueOf(a));
        long dayBetween = DateUtils.getDayBetween(1644551891000L, 1646971091000L);
        System.out.println(dayBetween);
        long l = DateUtils.addDay(System.currentTimeMillis(), 1);

        long l1 = DateUtils.setDayAndHour(l, DateUtils.getDayOfMonth(l), 1);
        System.out.println(DateUtils.normalFormatTimeStamp(l1));
        System.out.println(DateUtils.normalFormatTimeStamp(DateUtils.addDay(System.currentTimeMillis(), 18)));
        System.out.println(DateUtils.getMothOfYear(System.currentTimeMillis()));
        System.out.println(new Date(System.currentTimeMillis()).getTime());

        boolean sameDay = DateUtils.isSameDay(1647435845226L, 1647435845236L);
        System.out.println(sameDay);
        // 状态：1647525720135
        // 同步：1647525720112
        System.out.println(1647525720135L > 1647525720112L);
        // 2022-10-19 21:30:52   ======> 2022-10-09 21:30:52
        long l2 = DateUtils.addDay(1666186252000L, -9);
        long l3 = DateUtils.addDay(1666186252000L, -38);
        System.out.println(l2);
        System.out.println(l3);
        long dayBetween1 = DateUtils.getDayBetween(1666186252000L, 1666186252000L);
        System.out.println(dayBetween1);
        List<String> strings = LocalDateUtil.calculateAllDateBetween(1666108800000L, 1666281599000L);
        System.out.println(strings);

        long startOfDay = DateUtils.getStartOfDay(1666186252000L);
        long endOfDay = DateUtils.getEndOfDay(1666186252000L);
        System.out.println(
                "startOfDay:" + startOfDay + "  :" + DateUtils.formatTimeStamp(startOfDay, "YYYY MMdd HHmmss SS"));
        System.out.println(
                "endOfDay:" + endOfDay + "  :" + DateUtils.formatTimeStamp(endOfDay, "YYYY MMdd HHmmss SS"));

        // 10-22 - 10-24
        long dayBetween2 = DateUtils.getDayBetween(1666368000000L, 1666627199999L);
        System.out.println("活动天数" + dayBetween2);

        // 2022-10-22 23:59:59 - 2022-10-24 00:00:00
        long dayBetween3 = DateUtils.getDayBetween(1666404000000L, 1666558800000L);
        System.out.println("活动天数" + dayBetween3);

        List<Long> collect = LongStream.rangeClosed(1, 10).boxed().collect(Collectors.toList());
        String day = StringUtils.join(collect, SymbolConstants.COMMA);
        System.out.println(day);
        String join = Joiner.on(SymbolConstants.COMMA).join(collect);
        System.out.println(join);

        long dayBetween4 = DateUtils.getDayBetween(1668441600000L, 1671292799999L);
        System.out.println("相差天数：" + dayBetween4);
        long now = System.currentTimeMillis();
        long dayInterval = DateUtils.getDayBetween(now, 1671292799999L) / 2;
        System.out.println(dayInterval);
        long drawEndTime = DateUtils.addDay(now, (int) dayInterval);
        System.out.println(drawEndTime);
        System.out.println(DateUtils.getEndOfDay(drawEndTime));
        long cur = System.currentTimeMillis();
        long activityStartTimeOld = localDateToMilli(milliToLocalDate(DateUtils.getStartOfDay(cur))
                .minusDays(7));
        long activityStartTimeNew = DateUtils
                .addDay(DateUtils.getStartOfDay(now), -7);
        System.out.println(activityStartTimeOld);
        System.out.println(activityStartTimeNew);


        long dayBetween5 = LocalDateUtil.calculateDayBetween(1667235600000L, 1667275200000L);
        System.out.println("活动天数5   " + dayBetween5);

        // 2022-12-21 00:00:00 -    2022-12-25 00:00:00
        long dayBetween6 = LocalDateUtil.calculateDayBetween(1671552000000L, 1671897600000L);
        System.out.println("dayBetween6   " + dayBetween5);
    }
}
