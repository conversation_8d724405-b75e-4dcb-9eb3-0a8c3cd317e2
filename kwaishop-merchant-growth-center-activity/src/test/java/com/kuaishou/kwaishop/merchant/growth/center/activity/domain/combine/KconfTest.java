package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.combine;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.skipNotificationPushSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityBooleanConfigKey.strategyNotificationByNowSwitch;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityIntegerConfigKey.strategyNotificationSignUpDelay;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.strategyActivityWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityLongListConfigKey.strategyCreateNotificationWhiteList;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.crowdFetchConfig;

import java.util.List;
import java.util.Map;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordExtFieldBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.config.CrowdSelectFetchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.config.StrategyNotificationBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityJsonMapConfigKey;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-03-01
 */
public class KconfTest {
    public static void main(String[] args) {
        Map<String, StrategyNotificationBO> map = ActivityJsonMapConfigKey.strategyNotification.getMap();
        System.out.println(map);
        List<Long> list = strategyActivityWhiteList.get();
        System.out.println(list);
        System.out.println(list.contains(31L));
        IndicatorRecordExtFieldBO indicatorRecordExtFieldBO =
                ObjectMapperUtils.fromJSON("{\"syncTime\":\"123\"}", IndicatorRecordExtFieldBO.class);
        System.out.println(indicatorRecordExtFieldBO);
        Integer integer = strategyNotificationSignUpDelay.get();
        System.out.println(integer);
        String s = null;
        System.out.println(ObjectMapperUtils.toJSON(s));

        //long l = 1647344897000L+47502793;
        long l =  47502793 + 1647345000000L;
        //System.out.println(DateUtils.normalFormatTimeStamp(l));
        System.out.println("策略开始报名时间："+DateUtils.normalFormatTimeStamp(1647345000000L)+" push推送时间："+DateUtils.normalFormatTimeStamp(l));
        // long l1 = 220302786 + 1647344897000L;
        long l1 = 220302786 + 1647446400565L;
        //System.out.println(DateUtils.normalFormatTimeStamp(l1));
        System.out.println("策略开始时间："+DateUtils.normalFormatTimeStamp(1647446400565L)+" push推送时间："+DateUtils.normalFormatTimeStamp(l1));

        System.out.println(strategyCreateNotificationWhiteList.get());

        System.out.println(strategyNotificationByNowSwitch.get());

        System.out.println(skipNotificationPushSwitch.get());

        CrowdSelectFetchConfigBO o = crowdFetchConfig.getObject();
        System.out.println(o);
    }
}
