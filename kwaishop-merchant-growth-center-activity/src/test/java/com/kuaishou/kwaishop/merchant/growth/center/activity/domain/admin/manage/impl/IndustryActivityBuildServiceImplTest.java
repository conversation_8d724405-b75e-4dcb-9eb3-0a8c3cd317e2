package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.manage.impl;

import java.text.DecimalFormat;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-14
 */
class IndustryActivityBuildServiceImplTest {

    @InjectMocks
    IndustryActivityBuildServiceImpl industryActivityBuildServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void convertDoubleStr() {
        double num = 25.0;
        double num2 = 25.012300;
        double num3 = 2050.012300;
        double num4 = 2050.0;
        double num5 = 23.010;
        DecimalFormat df = new DecimalFormat("###################.###########");
        String strNum = df.format(num);
        String strNum2 = df.format(num2);
        String numberAsString = industryActivityBuildServiceImpl.convertDoubleStr(num2);
        String numberAsString2 = industryActivityBuildServiceImpl.convertDoubleStr(num);
        String numberAsString3 = industryActivityBuildServiceImpl.convertDoubleStr(num3);
        String numberAsString4 = industryActivityBuildServiceImpl.convertDoubleStr(num4);
        String numberAsString5 = industryActivityBuildServiceImpl.convertDoubleStr(num5);

        Assertions.assertEquals("25", strNum);
        Assertions.assertEquals("25.0123", strNum2);
        Assertions.assertEquals("25.0123", numberAsString);
        Assertions.assertEquals("25", numberAsString2);
        Assertions.assertEquals("2050.0123", numberAsString3);
        Assertions.assertEquals("2050", numberAsString4);
        Assertions.assertEquals("23.01", numberAsString5);
    }

    public static void main(String[] args) {
        double num = 25.0;
        double num2 = 25.012300;
        double num3 = 2050.012300;
        double num4 = 2050.0;
        double num5 = 23.010;
        DecimalFormat df = new DecimalFormat("###################.###########");
        String strNum = df.format(num);
        String strNum2 = df.format(num2);
        String numberAsString = convertDoubleStr(num2);
        String numberAsString2 = convertDoubleStr(num);
        String numberAsString3 = convertDoubleStr(num3);
        String numberAsString4 = convertDoubleStr(num4);
        String numberAsString5 = convertDoubleStr(num5);

        Assertions.assertEquals("25", strNum);
        Assertions.assertEquals("25.0123", strNum2);
        Assertions.assertEquals("25.0123", numberAsString);
        Assertions.assertEquals("25", numberAsString2);
        Assertions.assertEquals("2050.0123", numberAsString3);
        Assertions.assertEquals("2050", numberAsString4);
        Assertions.assertEquals("23.01", numberAsString5);
    }

    public static String convertDoubleStr(Double v) {
        return String.valueOf(v).replaceAll("\\.0*$", "");
    }

}