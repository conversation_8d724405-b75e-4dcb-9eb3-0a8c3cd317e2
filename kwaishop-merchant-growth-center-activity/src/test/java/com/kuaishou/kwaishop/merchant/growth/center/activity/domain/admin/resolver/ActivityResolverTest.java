package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.resolver;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Optional;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ActivityTagConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-07
 */
class ActivityResolverTest {

    @Test
    void getActivityTagConfig() {
        Optional<ActivityTagConfig> activityTagConfigOptional = ActivityResolver.getActivityTagConfig(
                "distributionRebatePolicyLongTerm");
        assertTrue(activityTagConfigOptional.isPresent());
        ActivityTagConfig activityTagConfig = activityTagConfigOptional.get();
        assertEquals("distributionRebatePolicyLongTerm", activityTagConfig.getTagCode());
        assertTrue(Boolean.TRUE.equals(activityTagConfig.getAttitudes().get("businessSupplyWithoutAward")));

    }
}