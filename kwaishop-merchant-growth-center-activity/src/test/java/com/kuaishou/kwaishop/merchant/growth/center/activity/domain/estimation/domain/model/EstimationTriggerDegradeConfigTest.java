package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-02
 */
class EstimationTriggerDegradeConfigTest {
    EstimationTriggerDegradeConfig estimationTriggerDegradeConfig =
            new EstimationTriggerDegradeConfig(1, 14, false);

    @Test
    void testGeneDegradeTriggerTimestamp() {
        EstimationTriggerDegradeConfig estimationTriggerDegradeConfig = new EstimationTriggerDegradeConfig();
        estimationTriggerDegradeConfig.setEstimateEarlyDaysBeforePeriodStart(1);
        estimationTriggerDegradeConfig.setEstimateStartHour(14);
        Long triggerTimestamp = estimationTriggerDegradeConfig.geneDegradeTriggerTimestamp(1742227200000L);
        Assertions.assertEquals(1742191200000L, triggerTimestamp);
    }
}
