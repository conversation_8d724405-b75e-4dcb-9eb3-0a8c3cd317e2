package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.openApiConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.IndicatorHandlerUtil.parseCategoryLevelListToParamV2;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.app.consumer.indicator.kafka.IndicatorLiveHeartbeatConsumer;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.bo.IndicatorRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.service.IndicatorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.CategoryBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.openapi.AccessTokenResp;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.openapi.OpenApiBaseResp;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.openapi.OpenApiConfig;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.BizException;
import com.kuaishou.kwaishop.merchant.growth.utils.exception.ErrorCode;
import com.kuaishou.merchant.utils.HttpResponse;
import com.kuaishou.merchant.utils.HttpUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-12
 */
@Service
@Slf4j
@Lazy
public class IndicatorUnitTestService {
    @Autowired
    private IndicatorService indicatorService;

    @Autowired
    private IndicatorLiveHeartbeatConsumer indicatorLiveHeartbeatConsumer;


    public void testConcurrentLiveDuration(){

    }

    public void testListIndicatorRecord() {
        long userId = 19016476L;
        long activityId = 102;
        long entityId = 103;
        long indicatorId = 2;
        int pageNo = 1;
        int pageSize = 10;
        List<IndicatorRecordBO> indicatorRecordBOList =
                indicatorService.listIndicatorRecord(userId, activityId, entityId, indicatorId, pageNo, pageSize);
        log.info("[testListIndicatorRecord][indicatorRecordBOList:{}]",
                ObjectMapperUtils.toJSON(indicatorRecordBOList));
    }

    public void testQueryLatestIndicatorRecord() {
        long userId = 19016476L;
        long activityId = 102;
        long entityId = 103;
        long indicatorId = 2;
        IndicatorRecordBO indicatorRecordBO =
                indicatorService.queryLatestIndicatorRecord(userId, activityId, entityId, indicatorId);
        log.info("[testQueryLatestIndicatorRecord][indicatorRecordBO:{}]", ObjectMapperUtils.toJSON(indicatorRecordBO));
    }

    public void testBatchQueryLatestIndicatorRecord() {
        long userId = 19016476L;
        long activityId = 102;
        List<Long> entityIds = Arrays.asList(104L, 103L);
        long indicatorId = 2;
        List<IndicatorRecordBO> indicatorRecordBOList =
                indicatorService.batchQueryLatestIndicatorRecord(userId, activityId, entityIds, indicatorId);
        log.info("[testBatchQueryLatestIndicatorRecord][indicatorRecordBOList:{}]",
                ObjectMapperUtils.toJSON(indicatorRecordBOList));
    }

    public void testBatchQueryLatestIndicatorRecord2() {
        List<Long> userIds = Arrays.asList(19016476L, 10L);
        long activityId = 102;
        long entityId = 103;
        long indicatorId = 2;
        List<IndicatorRecordBO> indicatorRecordBOList =
                indicatorService.batchQueryLatestIndicatorRecord(userIds, activityId, entityId, indicatorId);
        log.info("[testBatchQueryLatestIndicatorRecord2][indicatorRecordBOList:{}]",
                ObjectMapperUtils.toJSON(indicatorRecordBOList));
    }

    public void testCreateIndicatorConfig() {
        IndicatorConfigBO indicatorConfigBO =
                IndicatorConfigBO.builder().activityId(102L).entityId(103L).type(1).indicatorId(3L).indicatorSubId("123")
                        .beginValue(100L).targetValue(1000L).operator("EQUAL").status(1).build();
        indicatorService.createIndicatorConfig(indicatorConfigBO);
    }

    public void testUpdateIndicatorConfigById() {
        IndicatorConfigBO indicatorConfigBO =
                IndicatorConfigBO.builder().beginValue(111L).targetValue(1122L).operator("EQUALLL").status(2).build();
        indicatorService.updateIndicatorConfigById(7, indicatorConfigBO);
    }

    public void testQueryIndicatorConfigById() {
        IndicatorConfigBO indicatorConfigBO = indicatorService.queryIndicatorConfigById(6);
        log.info("[testQueryIndicatorConfigById][indicatorConfigBO:{}]", ObjectMapperUtils.toJSON(indicatorConfigBO));
    }

    public void testListIndicatorConfig() {
        long activityId = 102;
        long entityId = 103;
        long indicatorId = 3;
        List<IndicatorConfigBO> indicatorConfigBOList =
                indicatorService.listIndicatorConfig(activityId, entityId, indicatorId);
        log.info("[testQueryIndicatorConfigById][indicatorConfigBOList:{}]",
                ObjectMapperUtils.toJSON(indicatorConfigBOList));
    }

    @Test
    public void sendKimLegoMarkdownMessage() {
        OpenApiBaseResp optionalOpenApiBaseResp = null;
        String url = "https://is-gateway-test.corp.kuaishou.com" + "/token/get";
        Map<String, String> param = Maps.newHashMap();
        param.put("appKey", "640686c5-ca32-46cb-942f-5d1f87484195");
        param.put("secretKey", "106c7b171cdc3ba75f1fb3046191cd86");
        param.put("grantType", "client_credentials");
        try {
            HttpResponse response = HttpUtils.getMethodWithCacheUrl(url, param);
            if (response.getStatus() == HttpStatus.SC_OK) {
                Type type = new TypeToken<OpenApiBaseResp<AccessTokenResp>>() {
                }.getType();
                optionalOpenApiBaseResp = new Gson().fromJson(response.getBody(), type);

            }
        } catch (IOException e) {
            log.error("OpenApiClient getToken error,param:{}", JsonMapperUtils.toJson(param), e);
        }



        if (optionalOpenApiBaseResp == null) {
            log.error("getOpenApiToken error,res:{}", JsonMapperUtils.toJson(optionalOpenApiBaseResp));
        }
        OpenApiBaseResp<AccessTokenResp> baseResp = optionalOpenApiBaseResp;
        //        commands.setex(key, (int) TimeUnit.HOURS.toSeconds(12L), JsonMapperUtils.toJson
        //        (optionalOpenApiBaseResp.get().getResult()));
        String accessToken = baseResp.getResult().getAccessToken();


        HashMap<String, Object> map = Maps.newHashMap();
        map.put("username", "zenghuanyu03");
        map.put("msgType", "markdown");
        try {
            OpenApiConfig apiConfig = openApiConfig.getObject();
            String urlSend = "https://is-gateway-test.corp.kuaishou.com/openapi/v2/message/send";


            HashMap<String, String> contentMap = Maps.newHashMap();
            contentMap.put("content", "hhehheheheheh");
            map.put("markdown", contentMap);
//            String body = JsonMapperUtils.toJson(map);
//            log.info("sendMarkdown info,body:{}", body);

            RestTemplate restTemplate = new RestTemplate();
            //设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", "Bearer " + accessToken);
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            //用HttpEntity封装整个请求报文
            HttpEntity<String> httpEntity = new HttpEntity<>(ObjectMapperUtils.toJSON(map), headers);
            log.info("[BPM_FETCH_SERVICE_INFO][httpEntity:{}]", ObjectMapperUtils.toJSON(httpEntity));
            //发送post请求
            ResponseEntity<String> response =
                    restTemplate.postForEntity(urlSend, httpEntity, String.class);
            log.info("[BPM_FETCH_SERVICE_INFO][response:{}]", ObjectMapperUtils.toJSON(response));

        } catch (Exception e) {
            log.error("sendMarkdown error", e);
        }

    }

    @Test
    public void stringToJsonTest() {
        Map<String, Object> item = Maps.newHashMap();
        List<Long> ltvList11 = Lists.newArrayList();
        ltvList11.add(22l);
        ltvList11.add(31l);
        item.put("ltvList",ltvList11);
        Object ltvList = item.get("ltvList");
        if (!(ltvList instanceof List)) {
            log.error("[LVT列表获取] 非预期格式. item:{}", toJSON(item));
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR, "非预期格式");
        }
        Collection<Long> longs = fromJSON(toJSON(ltvList), List.class, Long.class);
        log.info("[BPM_FETCH_SERVICE_INFO][response:{}]", ObjectMapperUtils.toJSON(longs));
    }

    @Test
    public  void testtopTenEffective() {
        String one = "[2115724,7504887,130283,1511932,9264113,7248430,1926832,4604488,2307465," +
                "1269369,10562691,5151323,4491132,1358409,2083501,5054364,4538321]";
        List<Long> valueList = fromJSON(one, List.class, Long.class);

        // 降序排序
        valueList = valueList.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        Double topNIndex = Math.ceil((double) valueList.size() / 10);
        int topNIndexInt = topNIndex.intValue();
        long topTenValue = valueList.get(topNIndexInt - 1);

    }

    @Test
    public void testParseLeafCategoryHierarchyMap() {
        String categoryString =
                "[{\"categoryId\":34816,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":34817,\"hierarchy\":2,"
                        + "\"childCategory\":[]},{\"categoryId\":40818,\"hierarchy\":2,\"childCategory\":[]},"
                        + "{\"categoryId\":40819,\"hierarchy\":2,\"childCategory\":[]},{\"categoryId\":40820,"
                        + "\"hierarchy\":2,\"childCategory\":[]},{\"categoryId\":36619,\"hierarchy\":2,"
                        + "\"childCategory\":[]},{\"categoryId\":36621,\"hierarchy\":2,\"childCategory\":[]}]},"
                        + "{\"categoryId\":1034,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":1044,"
                        + "\"hierarchy\":2,\"childCategory\":[{\"categoryId\":1045,\"hierarchy\":3,"
                        + "\"childCategory\":[]},{\"categoryId\":1046,\"hierarchy\":3,\"childCategory\":[]},"
                        + "{\"categoryId\":1047,\"hierarchy\":3,\"childCategory\":[]}]},{\"categoryId\":1561,"
                        + "\"hierarchy\":2,\"childCategory\":[]},{\"categoryId\":1562,\"hierarchy\":2,"
                        + "\"childCategory\":[{\"categoryId\":1573,\"hierarchy\":3,\"childCategory\":[]},"
                        + "{\"categoryId\":1577,\"hierarchy\":3,\"childCategory\":[]},{\"categoryId\":1563,"
                        + "\"hierarchy\":3,\"childCategory\":[]}]},{\"categoryId\":1053,\"hierarchy\":2,"
                        + "\"childCategory\":[{\"categoryId\":1350,\"hierarchy\":3,\"childCategory\":[]},"
                        + "{\"categoryId\":1351,\"hierarchy\":3,\"childCategory\":[]},{\"categoryId\":1054,"
                        + "\"hierarchy\":3,\"childCategory\":[]}]}]},{\"categoryId\":1546,\"hierarchy\":1,"
                        + "\"childCategory\":[{\"categoryId\":1547,\"hierarchy\":2,"
                        + "\"childCategory\":[{\"categoryId\":1549,\"hierarchy\":3,\"childCategory\":[]},"
                        + "{\"categoryId\":1550,\"hierarchy\":3,\"childCategory\":[]}]}]},{\"categoryId\":1163,"
                        + "\"hierarchy\":1,\"childCategory\":[{\"categoryId\":1177,\"hierarchy\":2,"
                        + "\"childCategory\":[{\"categoryId\":1180,\"hierarchy\":3,\"childCategory\":[]}]}]}]";

        List<CategoryBO> categories = fromJSON(categoryString, List.class, CategoryBO.class);
        Map<String, List<String>> result = parseCategoryLevelListToParamV2(categories);
        log.info("result:{}", toJSON(result));
    }
}
