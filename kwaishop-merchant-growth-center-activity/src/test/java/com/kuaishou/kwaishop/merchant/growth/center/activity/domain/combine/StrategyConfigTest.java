package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.combine;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy.model.bo.config.PullNewUserStrategyConfigBO;

/**
 * <AUTHOR> <hong<PERSON><PERSON>@kuaishou.com>
 * Created on 2022-03-23
 */
public class StrategyConfigTest {
    public static void main(String[] args) {
        String config = "{\"awardTime\":null,\"useLuckyBag\":false,\"awardSubsidyModel\":\"toB\","
                + "\"awardSubsidyChannel\":\"\",\"pushChannel\":null}";
        PullNewUserStrategyConfigBO strategyConfig = fromJSON(config,PullNewUserStrategyConfigBO.class);
        System.out.println(strategyConfig);
    }
}
