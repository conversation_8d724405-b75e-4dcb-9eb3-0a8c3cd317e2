package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.helper.list.strategy;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.converter.ActivityTaskRecordConverter.findUserDimDrawTime;

import java.util.List;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.UserTaskRecordDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-01
 */
class SellerWorkbenchResolveStrategyTest {

    @InjectMocks
    SellerWorkbenchResolveStrategy sellerWorkbenchResolveStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testFindUserDimDrawTime() {
        List<UserTaskRecordDO> taskRecordDOList = Lists.newArrayList();
//        List<UserTaskRecordDO> userTaskRecordDOS = ObjectMapperUtils.fromJSON(userTaskDoStr, List.class,
//                UserTaskRecordDO.class);
        UserTaskRecordDO userTaskRecordDO = new UserTaskRecordDO();
        userTaskRecordDO.setStartTime(1720368000000L);
        userTaskRecordDO.setEndTime(1720627199000L);
        taskRecordDOList.add(userTaskRecordDO);
        Pair<Long, Long> result = findUserDimDrawTime(taskRecordDOList);
        Assertions.assertTrue(result.getLeft() > 0);
        Assertions.assertTrue(result.getRight() > 0);
        Assertions.assertTrue(result.getLeft() <= result.getRight());
    }

}
