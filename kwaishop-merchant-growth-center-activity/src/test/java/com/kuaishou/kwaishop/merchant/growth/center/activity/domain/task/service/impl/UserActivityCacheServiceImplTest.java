package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.impl;

import java.util.Map;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.customizeplan.model.bo.UserTaskTimeRangeBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-05
 */
class UserActivityCacheServiceImplTest {
    UserActivityCacheServiceImpl userActivityCacheServiceImpl = new UserActivityCacheServiceImpl();

    @Test
    void testAddUserActivityUserTaskTimeInfo() {
        userActivityCacheServiceImpl.addUserActivityUserTaskTimeInfo(0L, Map.of(Long.valueOf(1),
                new UserTaskTimeRangeBO(Long.valueOf(1), Long.valueOf(1))));
    }

    @Test
    void testAddUserActivityUserTaskTimeInfo2() {
        userActivityCacheServiceImpl.addUserActivityUserTaskTimeInfo(0L, 0L, new UserTaskTimeRangeBO(Long.valueOf(1),
                Long.valueOf(1)));
    }

    @Test
    void testGetUserActivityUserTaskTimeInfo() {
        UserTaskTimeRangeBO result = userActivityCacheServiceImpl.getUserActivityUserTaskTimeInfo(0L, 1L);
//        Assertions.assertEquals(new UserTaskTimeRangeBO(Long.valueOf(1), Long.valueOf(1)), result);
    }
}
