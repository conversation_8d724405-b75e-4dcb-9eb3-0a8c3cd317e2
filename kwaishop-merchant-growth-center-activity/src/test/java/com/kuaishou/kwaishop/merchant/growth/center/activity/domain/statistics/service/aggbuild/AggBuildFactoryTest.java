package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.buildAggJson;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.aggbuild.AggBuildFactory.parseTermsResult;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerPolicySignUpSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.EsAggregationTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.model.TermsAggData;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-04
 */
class AggBuildFactoryTest {

    @Test
    void testBuildAggJson3() {
        TermsAggregationBuild agg1 = new TermsAggregationBuild("name1", "activityId", EsAggregationTypeEnum.TERMS);
        TermsAggregationBuild subAgg1 = new TermsAggregationBuild("name3", "draw", EsAggregationTypeEnum.TERMS);
        TermsAggregationBuild subAgg2 = new TermsAggregationBuild("name4", "risk", EsAggregationTypeEnum.TERMS);

        agg1.subAggregation(subAgg1);
        agg1.subAggregation(subAgg2);

        TermsAggregationBuild agg2 = new TermsAggregationBuild("name2", "activityId", EsAggregationTypeEnum.SUM);

        List<TermsAggregationBuild> aggregations = List.of(agg1, agg2);
        String s = buildAggJson(aggregations);
        System.out.println(s);
    }

    @Test
    void testParseTermsResult_01(){

    }

    @Test
    void testBuildAggJson4() {
        String str = "{\"activityId_agg\":{\"doc_count_error_upper_bound\":0,\"sum_other_doc_count\":0,"
                + "\"buckets\":[{\"key\":13633,\"doc_count\":9,\"draw_agg\":{\"doc_count_error_upper_bound\":0,"
                + "\"sum_other_doc_count\":0,\"buckets\":[{\"key\":0,\"doc_count\":8},{\"key\":1," +
                "\"doc_count\":1}]}}]}}";
        String str2 = "{\n" +
                "        \"agg\": {\n" +
                "            \"doc_count_error_upper_bound\": 0,\n" +
                "            \"sum_other_doc_count\": 0,\n" +
                "            \"buckets\": [\n" +
                "                {\n" +
                "                    \"key\": 12901,\n" +
                "                    \"doc_count\": 1097,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 908\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 189\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 12971,\n" +
                "                    \"doc_count\": 959,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 912\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 47\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 12840,\n" +
                "                    \"doc_count\": 924,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 890\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 34\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 11244,\n" +
                "                    \"doc_count\": 555,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 555\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 11251,\n" +
                "                    \"doc_count\": 555,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 555\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 13137,\n" +
                "                    \"doc_count\": 200,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 185\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 15\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 12695,\n" +
                "                    \"doc_count\": 97,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 97\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 13411,\n" +
                "                    \"doc_count\": 91,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 87\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 4\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 12855,\n" +
                "                    \"doc_count\": 90,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 0,\n" +
                "                                \"doc_count\": 90\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                },\n" +
                "                {\n" +
                "                    \"key\": 12860,\n" +
                "                    \"doc_count\": 83,\n" +
                "                    \"agg\": {\n" +
                "                        \"doc_count_error_upper_bound\": 0,\n" +
                "                        \"sum_other_doc_count\": 0,\n" +
                "                        \"buckets\": [\n" +
                "                            {\n" +
                "                                \"key\": 1,\n" +
                "                                \"doc_count\": 83\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "}";
        Map<String, TermsAggData> stringLongTermsMap = fromJSON(str2, Map.class, String.class, TermsAggData.class);
        System.out.println(toJSON(stringLongTermsMap));
        Map<Long, SellerPolicySignUpSummary> longSellerPolicySignUpSummaryMap = parseTermsResult("agg.agg", stringLongTermsMap);
        System.out.println(toJSON(longSellerPolicySignUpSummaryMap));
    }

}
