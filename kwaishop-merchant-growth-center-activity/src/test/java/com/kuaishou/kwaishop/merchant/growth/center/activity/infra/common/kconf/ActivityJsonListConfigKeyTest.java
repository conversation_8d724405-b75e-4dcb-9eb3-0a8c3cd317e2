package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf;


import static com.kuaishou.infra.falcon.util.JsonUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.activityAutoLaunchConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.autoNotificationSwitchConfigV2;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityObjectConfigKey.krpcEnvironmentGrayConfig;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.v2.ActivityTailNumberKey.sendActivityOnlineMsgSwitchV2;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ActivityTagConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.launch.model.config.AutoLaunchConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.notification.model.bo.AutoNotificationSwitchConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.TailNumberUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.config.KrpcEnvironmentGrayConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-06
 */
public class ActivityJsonListConfigKeyTest {


//    @Test
//    void getActivityTagDetailConfigList() {
//        List<ActivityTagConfig> list = activityTagDetailConfigList.getList();
//        List<ActivityTagConfig> allList = getAllActivityConfigList(list);
//        allList.forEach(activityTagConfig -> {
//            System.out.println(activityTagConfig.getTagName());
//        });
//        Assertions.assertEquals(14, allList.size());
//    }

    public List<ActivityTagConfig> getAllActivityConfigList(List<ActivityTagConfig> activityTagConfigList) {
        List<ActivityTagConfig> result = new ArrayList<>();
        if (activityTagConfigList == null || activityTagConfigList.isEmpty()) {
            return result;
        }
        for (ActivityTagConfig activityTagConfig : activityTagConfigList) {
            result.add(activityTagConfig);
            // 处理子标签，确保不为 null
            if (activityTagConfig.getSubTags() != null) {
                result.addAll(getAllActivityConfigList(activityTagConfig.getSubTags()));
            }
        }
        return result;
    }

    @Test
    public void test() {
        AutoLaunchConfigBO autoLaunchConfig = activityAutoLaunchConfig.getObject();
        System.out.println("autoLaunchConfig");
//        autoLaunchConfig = fromJSON(toJson(activityAutoLaunchConfig.getObject()), AutoLaunchConfigBO.class);
//        System.out.println(autoLaunchConfig);
    }

    @Test
    public void test1() {
        AutoNotificationSwitchConfig config = autoNotificationSwitchConfigV2.getObject();
        boolean onFor = TailNumberUtils.isOnFor(11111L, config.getDarenGreyTailNumber());
        System.out.println("onFor: " + onFor);
    }

    @Test
    public void test2() {
        boolean onFor = sendActivityOnlineMsgSwitchV2.get().isOnFor(11111L);
        System.out.println("onFor: " + onFor);
    }

    @Test
    public void test3() {
        KrpcEnvironmentGrayConfig config = krpcEnvironmentGrayConfig.getObject();
        String json = toJSON(config);
        System.out.println(json);
        System.out.println(config.getEnableSwitch());
    }

}
