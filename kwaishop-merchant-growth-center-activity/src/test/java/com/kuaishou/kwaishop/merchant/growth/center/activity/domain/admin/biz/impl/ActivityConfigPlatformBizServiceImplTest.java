package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.biz.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.SubActivityTagConvert.getAllLeafNodes;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.convert.SubActivityTagConvert.rebuildForest;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kconf.ActivityObjectConfigKey.subActivityTagConfig;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.SubActivityTagConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.SubActivityTagItemConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.admin.QueryActivityTagRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-07
 */
class ActivityConfigPlatformBizServiceImplTest {

    @Test
    void queryActivityTag() {

        ActivityConfigPlatformBizServiceImpl service = new ActivityConfigPlatformBizServiceImpl();
        String queryActivityTag = service.queryActivityTag(QueryActivityTagRequest.newBuilder()
                .setBizType("strategy_admin")
                .setOperator("zl")
                .build());
        System.out.printf(queryActivityTag);
    }

    @Test
    void subActivityTagTest() {
        SubActivityTagConfigBO configBO = subActivityTagConfig.getObject();
        System.out.println(ObjectMapperUtils.toJSON(configBO));

        List<SubActivityTagItemConfigBO> subActivityTagBizConfig = configBO.getSubActivityTagConfig();

        List<SubActivityTagItemConfigBO> allLeafNodes = getAllLeafNodes(subActivityTagBizConfig);
        System.out.println("__________________________________________");
        System.out.println(ObjectMapperUtils.toJSON(allLeafNodes));

        // 构建索引
    }


    @Test
    void test2() {
        SubActivityTagConfigBO configBO = subActivityTagConfig.getObject();
        List<SubActivityTagItemConfigBO> subActivityTagBizConfig = configBO.getSubActivityTagConfig();
        List<SubActivityTagItemConfigBO> allLeafNodes = getAllLeafNodes(subActivityTagBizConfig);

        List<SubActivityTagItemConfigBO> collect = allLeafNodes.stream()
                .filter(a -> a.getSupportedBizTypeList().contains("seller"))
                .collect(Collectors.toList());

        List<SubActivityTagItemConfigBO> subActivityTagItemConfigBOS = rebuildForest(collect, subActivityTagBizConfig);
        System.out.println(ObjectMapperUtils.toJSON(subActivityTagItemConfigBOS));
    }


}


