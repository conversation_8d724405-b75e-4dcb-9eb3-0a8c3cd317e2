package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.biz.impl;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.admin.QueryActivityTagRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-07
 */
class ActivityConfigPlatformBizServiceImplTest {

    @Test
    void queryActivityTag() {

        ActivityConfigPlatformBizServiceImpl service = new ActivityConfigPlatformBizServiceImpl();
        String queryActivityTag = service.queryActivityTag(QueryActivityTagRequest.newBuilder()
                .setBizType("strategy_admin")
                .setOperator("zl")
                .build());
        System.out.printf(queryActivityTag);
    }
}