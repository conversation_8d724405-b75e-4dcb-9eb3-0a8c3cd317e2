package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.model.enums.ApproveRecordStatus.APPROVE_RECORD_STATUS_APPROVE_PROCESSING;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.model.enums.ApproveRecordStatus.APPROVE_RECORD_STATUS_PRE_VERIFY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.model.enums.ApproveRecordStatus.APPROVE_RECORD_STATUS_WAIT_APPROVE;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.model.bo.ApproveConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.model.bo.ApproveRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.approve.service.ApproveService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-09-12
 */
@Service
@Slf4j
@Lazy
public class ApproveUnitTestService {
    @Autowired
    private ApproveService approveService;

    void testSaveApproveRecord() {
        ApproveRecordBO approveRecordBO = ApproveRecordBO.builder()
                .activityId(102L)
                .entityId(105L)
                .type(1)
                .entityStatus(1)
                .finishTime(System.currentTimeMillis())
                .approveConfigId(120L)
                .approveDetail("xxxx")
                .flowCode("xxuu")
                .build();
        approveService.saveApproveRecord(approveRecordBO);
    }

    void testUpdateApproveRecordStatus() {
        ApproveRecordBO approveRecordBO = ApproveRecordBO.builder()
                .flowId("dddeeegeg")
                .build();
        approveService.updateApproveRecordStatus(1, APPROVE_RECORD_STATUS_PRE_VERIFY,
                APPROVE_RECORD_STATUS_APPROVE_PROCESSING, approveRecordBO);
    }

    void testBatchUpdateApproveRecordStatus() {
        ApproveRecordBO approveRecordBO2 = ApproveRecordBO.builder().id(2L).build();
        ApproveRecordBO approveRecordBO3 = ApproveRecordBO.builder().id(3L).build();
        List<ApproveRecordBO> approveRecordBOList = Arrays.asList(approveRecordBO2, approveRecordBO3);
        approveService.batchUpdateApproveRecordStatus(APPROVE_RECORD_STATUS_PRE_VERIFY,
                APPROVE_RECORD_STATUS_WAIT_APPROVE, "213312", "tulip test", approveRecordBOList);
    }

    void testGetApproveRecord() {
        ApproveRecordBO approveRecordBO = approveService.getApproveRecord(1);
        log.info("[testGetApproveRecord][approveRecordBO:{}]", ObjectMapperUtils.toJSON(approveRecordBO));
    }

    void testListApproveRecord() {
        long activityId = 102L;
        List<ApproveRecordBO> approveRecordBOList =
                approveService.listApproveRecord(activityId, 1, 0);
        log.info("[testListApproveRecord][approveRecordBOList:{}]", ObjectMapperUtils.toJSON(approveRecordBOList));
    }

    void testGetLatestApproveRecord() {
        long activityId = 102L;
        long entityId = 103L;
        int type = 1;
        ApproveRecordBO approveRecordBO = approveService.getLatestApproveRecord(activityId, entityId, type);
        log.info("[testGetLatestApproveRecord][approveRecordBO:{}]", ObjectMapperUtils.toJSON(approveRecordBO));
    }

    void testGetApproveRecordByFlowId() {
        String flowId = "3456";
        int type = 1;
        List<ApproveRecordBO> approveRecordBO = approveService.getApproveRecordByFlowId(flowId, type);
        log.info("[testGetApproveRecordByFlowId][approveRecordBO:{}]", ObjectMapperUtils.toJSON(approveRecordBO));
    }

    private void testSaveApproveConfig() {
        ApproveConfigBO approveConfigBO = ApproveConfigBO.builder()
                .activityId(102L)
                .entityId(104L)
                .type(1)
                .status(1)
                .flowCode("1111")
                .approveConfig("balabla")
                .build();
        approveService.saveApproveConfig(approveConfigBO);
    }

    private void testUpdateApproveConfig() {
        ApproveConfigBO approveConfigBO = ApproveConfigBO.builder()
                .status(2)
                .flowCode("1122")
                .approveConfig("balablaaaaaaa")
                .modifier("update by tulip")
                .build();
        approveService.updateApproveConfig(1L, approveConfigBO);
    }

    private void testGetApproveConfig() {
        ApproveConfigBO approveConfigBO = approveService.getApproveConfig(1L);
        log.info("[testGetApproveConfig][approveConfigBO:{}]", approveConfigBO);
    }

    private void testListApproveConfig() {
        List<ApproveConfigBO> approveConfigBOList = approveService.listApproveConfig(102L, 104L, 1);
        log.info("[testListApproveConfig][approveConfigBOList:{}]", approveConfigBOList);
    }
}
