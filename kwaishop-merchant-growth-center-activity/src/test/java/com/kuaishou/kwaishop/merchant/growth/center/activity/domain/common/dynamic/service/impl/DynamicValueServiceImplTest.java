package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.converter.AwardConfigConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardRuleInputHandlerFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.AwardConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;

class DynamicValueServiceImplTest {
    @Mock
    IndicatorDAO indicatorDAO;
    @Mock
    AwardConfigDAO awardConfigDAO;
    @Mock
    UserTaskRecordDAO userTaskRecordDAO;
    @Mock
    IndicatorConfigDAO indicatorConfigDAO;
    @Mock
    IndicatorRecordDAO indicatorRecordDAO;
    @Mock
    AwardConfigConverter awardConfigConverter;
    @Mock
    TaskLocalCacheService taskLocalCacheService;
    @Mock
    UserRegistrationRecordDAO userRegistrationRecordDAO;
    @Mock
    ActivityLocalCacheService activityLocalCacheService;
    @Mock
    IndicatorLocalCacheService indicatorLocalCacheService;
    @Mock
    AwardRuleInputHandlerFactory awardRuleInputHandlerFactory;
    @InjectMocks
    DynamicValueServiceImpl dynamicValueServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    void testCalcAwardConfigMaxValueFromEstimation() {
//        when(indicatorDAO.queryById(anyLong())).thenReturn(new IndicatorDO());
//        when(taskLocalCacheService.getTaskByTaskId(anyLong())).thenReturn(new TaskDO());
////        when(indicatorLocalCacheService.queryTaskIndicatorConfig(anyLong(), anyLong())).thenReturn(List.of(new IndicatorConfigDO()));
//
//        long result = dynamicValueServiceImpl.calcAwardConfigMaxValueFromEstimation(0L, new AwardConfigBO(), new TaskDO(), new AwardStepConfigBO(), Map.of("jsonData", "jsonData"));
//        Assertions.assertEquals(0L, result);
    }
}
