package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Test;
import org.testng.collections.Maps;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.AwardDynamicValue;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-19
 */
class AwardInAdvanceUtilsTest {

    @Test
    void testAwardInAdvanceMode() {
        String awardConfigStr = "[{\"id\":********,\"awardName\":\"果蔬偏移\",\"awardType\":\"LIVE_BROADCAST_FLOW\"," +
                "\"accountType\":null,\"activityId\":13443,\"entityId\":8269080,\"expireTime\":0," +
                "\"sendRuleType\":\"TASK\",\"sendRule\":null,\"awardSubject\":null,\"awardInventory\":null," +
                "\"interestPackageId\":null,\"interestConfigId\":0,\"interestExtParam\":null,\"interestScene\":null," +
                "\"operator\":\"wb_zhengguihua03\",\"awardValue\":null,\"maxAwardValue\":0," +
                "\"minReturnAwardValue\":null,\"componentValues\":null,\"awardInAdvance\":null},{\"id\":********," +
                "\"awardName\":\"果蔬偏移\",\"awardType\":\"LIVE_BROADCAST_FLOW\",\"accountType\":null," +
                "\"activityId\":13443,\"entityId\":8269081,\"expireTime\":0,\"sendRuleType\":\"TASK\"," +
                "\"sendRule\":null,\"awardSubject\":null,\"awardInventory\":null,\"interestPackageId\":null," +
                "\"interestConfigId\":0,\"interestExtParam\":null,\"interestScene\":null," +
                "\"operator\":\"wb_zhengguihua03\",\"awardValue\":null,\"maxAwardValue\":0," +
                "\"minReturnAwardValue\":null,\"componentValues\":null,\"awardInAdvance\":null},{\"id\":********," +
                "\"awardName\":\"果蔬偏移\",\"awardType\":\"LIVE_BROADCAST_FLOW\",\"accountType\":null," +
                "\"activityId\":13443,\"entityId\":8269083,\"expireTime\":0,\"sendRuleType\":\"TASK\"," +
                "\"sendRule\":null,\"awardSubject\":null,\"awardInventory\":null,\"interestPackageId\":null," +
                "\"interestConfigId\":0,\"interestExtParam\":null,\"interestScene\":null," +
                "\"operator\":\"wb_zhengguihua03\",\"awardValue\":null,\"maxAwardValue\":0," +
                "\"minReturnAwardValue\":null,\"componentValues\":null,\"awardInAdvance\":null},{\"id\":********," +
                "\"awardName\":\"果蔬偏移\",\"awardType\":\"LIVE_BROADCAST_FLOW\",\"accountType\":null," +
                "\"activityId\":13443,\"entityId\":8269084,\"expireTime\":0,\"sendRuleType\":\"TASK\"," +
                "\"sendRule\":null,\"awardSubject\":null,\"awardInventory\":null,\"interestPackageId\":null," +
                "\"interestConfigId\":0,\"interestExtParam\":null,\"interestScene\":null," +
                "\"operator\":\"wb_zhengguihua03\",\"awardValue\":null,\"maxAwardValue\":0," +
                "\"minReturnAwardValue\":null,\"componentValues\":null,\"awardInAdvance\":null}]";
        List<AwardConfigBO> awardConfigList = fromJSON(awardConfigStr, List.class, AwardConfigBO.class);
        String resMap = "{\"********\":{\"result\":1,\"message\":\"成功\",\"awardValue\":1,\"maxAwardValue\":0," +
                "\"componentValues\":{\"awardMode\":1,\"fixedAward\":1}},\"********\":{\"result\":1," +
                "\"message\":\"成功\",\"awardValue\":2,\"maxAwardValue\":0,\"componentValues\":{\"awardMode\":1," +
                "\"fixedAward\":2}},\"********\":{\"result\":1,\"message\":\"成功\",\"awardValue\":1," +
                "\"maxAwardValue\":0,\"componentValues\":{\"awardMode\":1,\"fixedAward\":1}}," +
                "\"********\":{\"result\":1,\"message\":\"成功\",\"awardValue\":2,\"maxAwardValue\":0," +
                "\"componentValues\":{\"awardMode\":1,\"fixedAward\":2}}}";
        Map<Long, AwardDynamicValue> res = fromJSON(resMap, Map.class, Long.class, AwardDynamicValue.class);
        String taskMetaMapStr = "{\"8269084\":{\"id\":8269084,\"createTime\":1723431114708," +
                "\"updateTime\":1723431114708,\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\"," +
                "\"deleted\":0,\"version\":1,\"activityId\":13443,\"name\":\"果蔬商家入驻\",\"type\":2," +
                "\"description\":null,\"parentTask\":8269082,\"preTask\":0,\"priority\":1,\"stage\":2," +
                "\"periodType\":5,\"periodDay\":5,\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000006\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22645," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}," +
                "\"8269083\":{\"id\":8269083,\"createTime\":1723431114708,\"updateTime\":1723431114708," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1," +
                "\"activityId\":13443,\"name\":\"果蔬商家入驻\",\"type\":2,\"description\":null,\"parentTask\":8269082," +
                "\"preTask\":0,\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2," +
                "\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000005\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22645," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}," +
                "\"8269080\":{\"id\":8269080,\"createTime\":1723431114677,\"updateTime\":1723431114677," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1," +
                "\"activityId\":13443,\"name\":\"果蔬7.20后首次动销\",\"type\":2,\"description\":null," +
                "\"parentTask\":8269079,\"preTask\":0,\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2," +
                "\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000002\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}," +
                "\"8269081\":{\"id\":8269081,\"createTime\":1723431114677,\"updateTime\":1723431114677," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1," +
                "\"activityId\":13443,\"name\":\"果蔬7.20后首次动销\",\"type\":2,\"description\":null," +
                "\"parentTask\":8269079,\"preTask\":0,\"priority\":1,\"stage\":2,\"periodType\":5,\"periodDay\":5," +
                "\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000003\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}}";
        Map<Long, TaskDO> taskMetaMap = fromJSON(taskMetaMapStr, Map.class, Long.class, TaskDO.class);
        String taskDOListStr = "[{\"id\":8269080,\"createTime\":1723431114677,\"updateTime\":1723431114677," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1," +
                "\"activityId\":13443,\"name\":\"果蔬7.20后首次动销\",\"type\":2,\"description\":null," +
                "\"parentTask\":8269079,\"preTask\":0,\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2," +
                "\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000002\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269081," +
                "\"createTime\":1723431114677,\"updateTime\":1723431114677,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443,\"name\":\"果蔬7" +
                ".20后首次动销\",\"type\":2,\"description\":null,\"parentTask\":8269079,\"preTask\":0,\"priority\":1," +
                "\"stage\":2,\"periodType\":5,\"periodDay\":5,\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000003\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269083," +
                "\"createTime\":1723431114708,\"updateTime\":1723431114708,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443," +
                "\"name\":\"果蔬商家入驻\",\"type\":2,\"description\":null,\"parentTask\":8269082,\"preTask\":0," +
                "\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2,\"startTime\":1721404800000," +
                "\"endTime\":1723996799999,\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3," +
                "\\\"config\\\":null}\",\"viewConfig\":null,\"alias\":\"13443020000000005\",\"status\":20," +
                "\"ext\":\"{\\\"stepNum\\\": 1, \\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, " +
                "\\\"offsetEventType\\\": \\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null," +
                "\"resourceRuleId\":22645,\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269084," +
                "\"createTime\":1723431114708,\"updateTime\":1723431114708,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443," +
                "\"name\":\"果蔬商家入驻\",\"type\":2,\"description\":null,\"parentTask\":8269082,\"preTask\":0," +
                "\"priority\":1,\"stage\":2,\"periodType\":5,\"periodDay\":5,\"startTime\":1721404800000," +
                "\"endTime\":1723996799999,\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3," +
                "\\\"config\\\":null}\",\"viewConfig\":null,\"alias\":\"13443020000000006\",\"status\":20," +
                "\"ext\":\"{\\\"stepNum\\\": 1, \\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, " +
                "\\\"offsetEventType\\\": \\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null," +
                "\"resourceRuleId\":22645,\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}]";
        List<TaskDO> taskDOList = fromJSON(taskDOListStr, List.class, TaskDO.class);
        AwardInAdvanceUtils.awardInAdvanceMode(1487237401L, 13443L, awardConfigList, res, taskMetaMap, taskDOList);
    }

    @Test
    void testAwardInAdvanceModeSimple() {
        String taskDOListStr = "[{\"id\":8269080,\"createTime\":1723431114677,\"updateTime\":1723431114677," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1," +
                "\"activityId\":13443,\"name\":\"果蔬7.20后首次动销\",\"type\":2,\"description\":null," +
                "\"parentTask\":8269079,\"preTask\":0,\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2," +
                "\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000002\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269081," +
                "\"createTime\":1723431114677,\"updateTime\":1723431114677,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443,\"name\":\"果蔬7" +
                ".20后首次动销\",\"type\":2,\"description\":null,\"parentTask\":8269079,\"preTask\":0,\"priority\":1," +
                "\"stage\":2,\"periodType\":5,\"periodDay\":5,\"startTime\":1721404800000,\"endTime\":1723996799999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"13443020000000003\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, \\\"offsetEventType\\\": " +
                "\\\"after20240720FirstSale\\\"}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":22644," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269083," +
                "\"createTime\":1723431114708,\"updateTime\":1723431114708,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443," +
                "\"name\":\"果蔬商家入驻\",\"type\":2,\"description\":null,\"parentTask\":8269082,\"preTask\":0," +
                "\"priority\":1,\"stage\":1,\"periodType\":5,\"periodDay\":2,\"startTime\":1721404800000," +
                "\"endTime\":1723996799999,\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3," +
                "\\\"config\\\":null}\",\"viewConfig\":null,\"alias\":\"13443020000000005\",\"status\":20," +
                "\"ext\":\"{\\\"stepNum\\\": 1, \\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, " +
                "\\\"offsetEventType\\\": \\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null," +
                "\"resourceRuleId\":22645,\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"},{\"id\":8269084," +
                "\"createTime\":1723431114708,\"updateTime\":1723431114708,\"creator\":\"wb_zhengguihua03\"," +
                "\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":1,\"activityId\":13443," +
                "\"name\":\"果蔬商家入驻\",\"type\":2,\"description\":null,\"parentTask\":8269082,\"preTask\":0," +
                "\"priority\":1,\"stage\":2,\"periodType\":5,\"periodDay\":5,\"startTime\":1721404800000," +
                "\"endTime\":1723996799999,\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3," +
                "\\\"config\\\":null}\",\"viewConfig\":null,\"alias\":\"13443020000000006\",\"status\":20," +
                "\"ext\":\"{\\\"stepNum\\\": 1, \\\"delayTime\\\": 86400000, \\\"awardInAdvance\\\": true, " +
                "\\\"offsetEventType\\\": \\\"sellerSettleIn\\\"}\",\"crowdType\":0,\"crowdConfig\":null," +
                "\"resourceRuleId\":22645,\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2, \\\"conditionGroups\\\": null}\"}]";
        List<TaskDO> taskDOList = fromJSON(taskDOListStr, List.class, TaskDO.class);
        taskDOList = taskDOList.stream()
                .filter(taskDO -> taskDO.getParentTask() != 0)
                .collect(Collectors.toList());
        Map<String, Object> map = Maps.newHashMap();
        map.put("awardMode", 1);
        map.put("fixedAward", 1);
        AwardDynamicValue value = AwardDynamicValue.builder()
                .result(1)
                .awardValue(1)
                .maxAwardValue(0)
                .componentValues(map)
                .build();
        AwardInAdvanceUtils.awardInAdvanceModeSimple(taskDOList, 8269083L, value);
    }
}
