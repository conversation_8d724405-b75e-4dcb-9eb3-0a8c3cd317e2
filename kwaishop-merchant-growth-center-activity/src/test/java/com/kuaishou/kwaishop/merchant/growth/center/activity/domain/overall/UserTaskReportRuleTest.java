package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.overall;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserTaskStatusEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordChangeTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataContentBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataHeaderBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.utils.OverallMapUtils;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.executor.utils.RulePerfUtil;

/**
 * <AUTHOR>
 */
public class UserTaskReportRuleTest {

    private static String json = "{\n"
            + "    \"origin.overallChangeType\": \"URWGX\",\n"
            + "    \"origin.recordType\": \"userTask\",\n"
            + "    \"origin.beforeEventData\": {\n"
            + "        \"id\": 2088005,\n"
            + "        \"activityId\": 10419,\n"
            + "        \"taskId\": 569189,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"startTime\": 1663655242000,\n"
            + "        \"endTime\": 1666108800000,\n"
            + "        \"status\": 40,\n"
            + "        \"version\": 2,\n"
            + "        \"deleted\": 0,\n"
            + "        \"createTime\": 1666086532238,\n"
            + "        \"updateTime\": 1666086555681,\n"
            + "        \"ext\": null,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\",\n"
            + "        \"parentId\": 0\n"
            + "    },\n"
            + "    \"origin.afterEventData\": {\n"
            + "        \"id\": 2088005,\n"
            + "        \"activityId\": 10419,\n"
            + "        \"taskId\": 569189,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"startTime\": 1663655242000,\n"
            + "        \"endTime\": 1666108800000,\n"
            + "        \"status\": 30,\n"
            + "        \"version\": 2,\n"
            + "        \"deleted\": 0,\n"
            + "        \"createTime\": 1666086532238,\n"
            + "        \"updateTime\": 1666086555681,\n"
            + "        \"ext\": null,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\",\n"
            + "        \"parentId\": 1\n"
            + "    },\n"
            + "    \"taskActivity.name\": \"活动名称\",\n"
            + "    \"taskActivity.startTime\": 1688373266000,\n"
            + "    \"taskActivity.endTime\": 1688373266000,\n"
            + "    \"taskActivity.resourceActivityId\": 1,\n"
            + "    \"taskIndicator.name\": \"测试指标名称\",\n"
            + "    \"task.name\": \"测试任务名称\"\n"
            + "}";

    private String buildActivityData(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String changeType = MapUtils.getString(context, "origin.overallChangeType");
        String recordType = MapUtils.getString(context, "origin.recordType");
        if (StringUtils.isBlank(changeType) || !OverallRecordTypeEnum.USER_TASK_RECORD.getCode().equals(recordType)) {
            result.put("result", 0);
            result.put("errorMsg", "变更类型不能为空，且数据类型需要为userTask");
            return ObjectMapperUtils.toJSON(result);
        }
        Long resourceActivityId = MapUtils.getLong(context, "taskActivity.resourceActivityId");
        // 对于没有和横向关联的活动，暂时不做上报
        if (resourceActivityId == null) {
            result.put("result", 1);
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> beforeEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.beforeEventData");
        Map<String, Object> afterEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.afterEventData");
        OverallProcessDataBO processData = new OverallProcessDataBO();
        // header数据
        OverallProcessDataHeaderBO processDataHeader = buildProcessDataHeader(afterEventData, changeType,
                recordType, resourceActivityId);
        // 变更前数据
        OverallProcessDataContentBO beforeContent = buildProcessBeforeContent(beforeEventData);
        // 变更后数据
        OverallProcessDataContentBO afterContent = buildProcessAfterContent(context, beforeEventData,
                afterEventData, changeType);
        String creator =  MapUtils.getString(afterEventData, "creator");
        processData.setEventTime(RulePerfUtil.getCurrentTimeMs());
        processData.setSource("resourceOverall");
        // 原始消息里暂时只能拿到创建人
        processData.setOperator(creator);
        processData.setDataHeader(processDataHeader);
        processData.setBeforeContent(beforeContent);
        processData.setAfterContent(afterContent);
        result.put("data", ObjectMapperUtils.toJSON(processData));
        result.put("result", 1);
        return ObjectMapperUtils.toJSON(result);
    }

    /**
     * 变更前数据JSON
     */
    private OverallProcessDataContentBO buildProcessBeforeContent(Map<String, Object> beforeEventData) {
        OverallProcessDataContentBO beforeData = new OverallProcessDataContentBO();
        beforeData.setDesc(StringUtils.EMPTY);
        String beforeDataJson = MapUtils.isEmpty(beforeEventData) ? "" : ObjectMapperUtils.toJSON(beforeEventData);
        beforeData.setData(beforeDataJson);
        return beforeData;
    }

    /**
     * 变更后数据JSON
     */
    private OverallProcessDataContentBO buildProcessAfterContent(Map<String, Object> context,
            Map<String, Object> beforeEventData, Map<String, Object> afterEventData, String changeType) {
        OverallProcessDataContentBO afterData = new OverallProcessDataContentBO();
        String changeDesc = buildAfterChangeDesc(context, beforeEventData, afterEventData, changeType);
        String afterDataJson = MapUtils.isEmpty(afterEventData) ? "" : ObjectMapperUtils.toJSON(afterEventData);
        afterData.setDesc(changeDesc);
        afterData.setData(afterDataJson);
        return afterData;
    }

    private String buildAfterChangeDesc(Map<String, Object> context,Map<String, Object> beforeEventData,
            Map<String, Object> afterEventData, String changeType) {
        Long activityId = MapUtils.getLong(afterEventData, "activityId", Long.valueOf("0"));
        Long taskId = MapUtils.getLong(afterEventData, "taskId", Long.valueOf("0"));
        Long parentTaskId = MapUtils.getLong(afterEventData, "parentId", Long.valueOf("0"));
        String taskName = MapUtils.getString(context, "task.name", "");
        Long startTime = MapUtils.getLong(afterEventData, "startTime", Long.valueOf("0"));
        Long endTime = MapUtils.getLong(afterEventData, "endTime", Long.valueOf("0"));
        UserTaskStatusEnum beforeStatus = UserTaskStatusEnum.of(MapUtils
                .getInteger(beforeEventData, "status", 0));
        UserTaskStatusEnum afterStatus = UserTaskStatusEnum.of(MapUtils
                .getInteger(afterEventData, "status", 0));
        String desc = "";
        // 活动记录创建
        if (OverallRecordChangeTypeEnum.USER_TASK_CREATE.getCode().equals(changeType)) {
            desc = String.format("任务记录创建，活动ID:%s，任务名称:[%s-%s]，父任务ID:%s，起始时间:%s, 结束时间:%s",
                    activityId, taskId, taskName, parentTaskId, startTime, endTime);
        } else if (OverallRecordChangeTypeEnum.USER_TASK_UPDATE.getCode().equals(changeType)) {
            Map<String, String> diffMap = OverallMapUtils.diffMap(beforeEventData, afterEventData);
            desc = String.format("用户任务信息更新，活动ID:%s，任务名称:[%s-%s], 变更前状态:[%s-%s]，变更后状态:[%s-%s]，"
                            + "变更字段明细:%s", activityId, taskId, taskName, beforeStatus.getValue(),
                    beforeStatus.getType(), afterStatus.getValue(), afterStatus.getType(), diffMap);
        } else if (OverallRecordChangeTypeEnum.USER_TASK_REACH.getCode().equals(changeType)) {
            desc = String.format("用户任务完成，活动ID:%s，任务名称:[%s-%s], 任务状态:[%s-%s]", activityId,
                    taskId, taskName, afterStatus.getValue(), afterStatus.getType());
        }
        return desc;
    }

    /**
     * 上报数据的Header
     */
    private OverallProcessDataHeaderBO buildProcessDataHeader(Map<String, Object> afterEventData,
            String changeType, String recordType, Long resourceActivityId) {
        OverallProcessDataHeaderBO dataHeader = new OverallProcessDataHeaderBO();
        // 用分布式唯一ID用做检索key
        Long id = MapUtils.getLong(afterEventData, "id", Long.valueOf("0"));
        Long userId = MapUtils.getLong(afterEventData, "userId", Long.valueOf("0"));
        OverallRecordChangeTypeEnum changeTypeEnum = OverallRecordChangeTypeEnum.of(changeType);
        dataHeader.setUserId(userId);
        dataHeader.setActivityId(resourceActivityId);
        dataHeader.setChangeType(changeType);
        dataHeader.setRecordType(recordType);
        dataHeader.setSearchKey(changeTypeEnum.getTypePrefix() + "_" + id);
        dataHeader.setActivityType(0);
        return dataHeader;
    }

}
