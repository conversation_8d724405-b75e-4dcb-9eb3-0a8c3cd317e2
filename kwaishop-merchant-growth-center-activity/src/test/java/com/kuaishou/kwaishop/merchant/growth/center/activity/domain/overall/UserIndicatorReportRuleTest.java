package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.overall;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator.model.enums.UserIndicatorStatusEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordChangeTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataContentBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataHeaderBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.utils.OverallMapUtils;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.RuleCenterClient;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.executor.utils.RulePerfUtil;

/**
 * <AUTHOR>
 */
public class UserIndicatorReportRuleTest {

    private static String json = "{\n"
            + "    \"origin.overallChangeType\": \"UZBCJ\",\n"
            + "    \"origin.recordType\": \"userIndicator\",\n"
            + "    \"origin.beforeEventData\": {\n"
            + "        \"id\": 1992549,\n"
            + "        \"userId\": 2174428317,\n"
            + "        \"activityId\": 11275,\n"
            + "        \"entityId\": 580808,\n"
            + "        \"configId\": 859913,\n"
            + "        \"indicatorId\": 161,\n"
            + "        \"subIndicatorId\": \"\",\n"
            + "        \"beginValue\": 1,\n"
            + "        \"currentValue\": 2,\n"
            + "        \"targetValue\": 8,\n"
            + "        \"startTime\": 1680883200000,\n"
            + "        \"endTime\": 1712591999000,\n"
            + "        \"completeTime\": 0,\n"
            + "        \"status\": 10,\n"
            + "        \"createTime\": 1680927991878,\n"
            + "        \"updateTime\": 1680927991878,\n"
            + "        \"ext\": null,\n"
            + "        \"version\": 1,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\",\n"
            + "        \"deleted\": 0,\n"
            + "        \"uniqueId\": \"2174428317_11275_580808_161\"\n"
            + "    },\n"
            + "    \"origin.afterEventData\": {\n"
            + "        \"id\": 1992549,\n"
            + "        \"userId\": 2174428317,\n"
            + "        \"activityId\": 11275,\n"
            + "        \"entityId\": 580808,\n"
            + "        \"configId\": 859913,\n"
            + "        \"indicatorId\": 161,\n"
            + "        \"subIndicatorId\": \"\",\n"
            + "        \"beginValue\": 1,\n"
            + "        \"currentValue\": 11,\n"
            + "        \"targetValue\": 8,\n"
            + "        \"startTime\": 1680883200000,\n"
            + "        \"endTime\": 1712591999000,\n"
            + "        \"completeTime\": 0,\n"
            + "        \"status\": 10,\n"
            + "        \"createTime\": 1680927991878,\n"
            + "        \"updateTime\": 1680927991878,\n"
            + "        \"ext\": null,\n"
            + "        \"version\": 1,\n"
            + "        \"creator\": \"system\",\n"
            + "        \"modifier\": \"system\",\n"
            + "        \"deleted\": 0,\n"
            + "        \"uniqueId\": \"2174428317_11275_580808_161\"\n"
            + "    },\n"
            + "    \"taskActivity.name\": \"活动名称\",\n"
            + "    \"taskActivity.startTime\": 1688373266000,\n"
            + "    \"taskActivity.endTime\": 1688373266000,\n"
            + "    \"taskActivity.resourceActivityId\": 1,\n"
            + "    \"taskIndicator.name\": \"测试指标名称\"\n"
            + "}";

    public static void main(String[] args) {
        Map<String, Object> context = ObjectMapperUtils.fromJson(json);
        UserIndicatorReportRuleTest ruleTest = new UserIndicatorReportRuleTest();
        String result = ruleTest.buildActivityData(context);
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("context", context);
        Object ruleResult = RuleCenterClient.getInstance()
                .ruleExecute("overall_user_indicator_report_data_rule", ruleContext);
        // System.err.println(result);
        System.err.println(ruleResult);
    }

    private String buildActivityData(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String changeType = MapUtils.getString(context, "origin.overallChangeType");
        String recordType = MapUtils.getString(context, "origin.recordType");
        if (StringUtils.isBlank(changeType) || !OverallRecordTypeEnum.USER_INDICATOR_RECORD.getCode().equals(recordType)) {
            result.put("result", 0);
            result.put("errorMsg", "变更类型不能为空，且数据类型需要为userIndicator");
            return ObjectMapperUtils.toJSON(result);
        }
        Long resourceActivityId = MapUtils.getLong(context, "taskActivity.resourceActivityId");
        // 对于没有和横向关联的活动，暂时不做上报
        if (resourceActivityId == null) {
            result.put("result", 1);
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> beforeEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.beforeEventData");
        Map<String, Object> afterEventData = (Map<String, Object>) MapUtils.getMap(context, "origin.afterEventData");
        OverallProcessDataBO processData = new OverallProcessDataBO();
        // header数据
        OverallProcessDataHeaderBO processDataHeader = buildProcessDataHeader(afterEventData, changeType,
                recordType, resourceActivityId);
        // 变更前数据
        OverallProcessDataContentBO beforeContent = buildProcessBeforeContent(beforeEventData);
        // 变更后数据
        OverallProcessDataContentBO afterContent = buildProcessAfterContent(context, beforeEventData,
                afterEventData, changeType);
        String creator =  MapUtils.getString(afterEventData, "creator");
        processData.setEventTime(RulePerfUtil.getCurrentTimeMs());
        processData.setSource("resourceOverall");
        // 原始消息里暂时只能拿到创建人
        processData.setOperator(creator);
        processData.setDataHeader(processDataHeader);
        processData.setBeforeContent(beforeContent);
        processData.setAfterContent(afterContent);
        result.put("data", ObjectMapperUtils.toJSON(processData));
        result.put("result", 1);
        return ObjectMapperUtils.toJSON(result);
    }

    /**
     * 变更前数据JSON
     */
    private OverallProcessDataContentBO buildProcessBeforeContent(Map<String, Object> beforeEventData) {
        OverallProcessDataContentBO beforeData = new OverallProcessDataContentBO();
        beforeData.setDesc(StringUtils.EMPTY);
        String beforeDataJson = MapUtils.isEmpty(beforeEventData) ? "" : ObjectMapperUtils.toJSON(beforeEventData);
        beforeData.setData(beforeDataJson);
        return beforeData;
    }

    /**
     * 变更后数据JSON
     */
    private OverallProcessDataContentBO buildProcessAfterContent(Map<String, Object> context,
            Map<String, Object> beforeEventData, Map<String, Object> afterEventData, String changeType) {
        OverallProcessDataContentBO afterData = new OverallProcessDataContentBO();
        String changeDesc = buildAfterChangeDesc(context, beforeEventData, afterEventData, changeType);
        String afterDataJson = MapUtils.isEmpty(afterEventData) ? "" : ObjectMapperUtils.toJSON(afterEventData);
        afterData.setDesc(changeDesc);
        afterData.setData(afterDataJson);
        return afterData;
    }

    private String buildAfterChangeDesc(Map<String, Object> context,Map<String, Object> beforeEventData,
            Map<String, Object> afterEventData, String changeType) {
        Long activityId = MapUtils.getLong(afterEventData, "activityId", Long.valueOf("0"));
        Long indicatorId = MapUtils.getLong(afterEventData, "indicatorId", Long.valueOf("0"));
        String indicatorName = MapUtils.getString(context, "taskIndicator.name", "");
        Long beforeCurrentValue = MapUtils.getLong(beforeEventData, "currentValue", Long.valueOf("0"));
        Long afterCurrentValue = MapUtils.getLong(afterEventData, "currentValue", Long.valueOf("0"));
        Long targetValue = MapUtils.getLong(afterEventData, "targetValue", Long.valueOf("0"));
        Long beginValue = MapUtils.getLong(afterEventData, "beginValue", Long.valueOf("0"));
        Long startTime = MapUtils.getLong(afterEventData, "startTime", Long.valueOf("0"));
        Long endTime = MapUtils.getLong(afterEventData, "endTime", Long.valueOf("0"));
        UserIndicatorStatusEnum beforeStatus = UserIndicatorStatusEnum.of(MapUtils
                .getInteger(beforeEventData, "status", 0));
        UserIndicatorStatusEnum afterStatus = UserIndicatorStatusEnum.of(MapUtils
                .getInteger(afterEventData, "status", 0));
        String desc = "";
        // 活动记录创建
        if (OverallRecordChangeTypeEnum.USER_INDICATOR_CREATE.getCode().equals(changeType)) {
            desc = String.format("指标记录创建，活动ID:%s，指标名称:[%s-%s]，起始值:%s，目标值:%s，起始时间:%s, 结束时间:%s",
                    activityId, indicatorId, indicatorName, beginValue, targetValue, startTime, endTime);
        } else if (OverallRecordChangeTypeEnum.USER_INDICATOR_UPDATE.getCode().equals(changeType)) {
            Map<String, String> diffMap = OverallMapUtils.diffMap(beforeEventData, afterEventData);
            desc = String.format("用户指标信息更新，活动ID:%s，指标名称:[%s-%s], 变更前状态:[%s-%s]，变更后状态:[%s-%s]，"
                            + "指标当前值:[%s->%s], 变更字段明细:%s", activityId, indicatorId, indicatorName,
                    beforeStatus.getValue(), beforeStatus.getType(), afterStatus.getValue(), afterStatus.getType(),
                    beforeCurrentValue, afterCurrentValue, diffMap);
        } else if (OverallRecordChangeTypeEnum.USER_INDICATOR_REACH.getCode().equals(changeType)) {
            desc = String.format("用户指标达标，活动ID:%s，指标名称:[%s-%s]，指标当前值:%s，目标值:%s",
                    activityId, indicatorId, indicatorName, afterCurrentValue, targetValue);
        }
        return desc;
    }

    /**
     * 上报数据的Header
     */
    private OverallProcessDataHeaderBO buildProcessDataHeader(Map<String, Object> afterEventData,
            String changeType, String recordType, Long resourceActivityId) {
        OverallProcessDataHeaderBO dataHeader = new OverallProcessDataHeaderBO();
        // 用分布式唯一ID用做检索key
        Long id = MapUtils.getLong(afterEventData, "id", Long.valueOf("0"));
        Long userId = MapUtils.getLong(afterEventData, "userId", Long.valueOf("0"));
        OverallRecordChangeTypeEnum changeTypeEnum = OverallRecordChangeTypeEnum.of(changeType);
        dataHeader.setUserId(userId);
        dataHeader.setActivityId(resourceActivityId);
        dataHeader.setChangeType(changeType);
        dataHeader.setRecordType(recordType);
        dataHeader.setSearchKey(changeTypeEnum.getTypePrefix() + "_" + id);
        dataHeader.setActivityType(0);
        return dataHeader;
    }

}
