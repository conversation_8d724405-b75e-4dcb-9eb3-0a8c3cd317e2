package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STRATEGY_FIX_AWARD_KEY;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.constants.ActivityStatisticsConstants.STRATEGY_FIX_TARGET_KEY;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.api.client.util.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.service.industry.IndustryActivityResolveService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.generator.service.IdGeneratorService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.converter.RegistrationConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsCalcService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsMsgProduceService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRecordService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsRoiService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.export.FileExportFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.querybuild.QueryBuildFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.ActivityConfigService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.audit.UserAuditRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.es.StatisticsEsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.IndicatorRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.indicator.localcache.IndicatorLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.registration.UserRegistrationRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.statistics.StatisticsDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.localcache.TaskLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.DataManagerFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.OrgManageFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.RiskControlFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.UserInfoFetchService;

class StatisticsReviewServiceImplTest {

    @Mock
    IndicatorDAO indicatorDAO;
    @Mock
    StatisticsDAO statisticsDAO;
    @Mock
    StatisticsEsDAO statisticsEsDAO;
    @Mock
    AdminKimService adminKimService;
    @Mock
    FileExportFactory fileExportFactory;
    @Mock
    IndicatorConfigDAO indicatorConfigDAO;
    @Mock
    UserTaskRecordDAO userTaskRecordDAO;
    @Mock
    QueryBuildFactory queryBuildFactory;
    @Mock
    IdGeneratorService idGeneratorService;
    @Mock
    UserAwardRecordDAO userAwardRecordDAO;
    @Mock
    UserAuditRecordDAO userAuditRecordDAO;
    @Mock
    UserInfoFetchService userInfoFetchService;
    @Mock
    IndicatorRecordDAO userIndicatorRecordDAO;
    @Mock
    ActivityConfigService activityConfigService;
    @Mock
    TaskLocalCacheService taskLocalCacheService;
    @Mock
    StatisticsCalcService statisticsCalcService;
    @Mock
    RegistrationConverter registrationConverter;
    @Mock
    StatisticsCacheService statisticsCacheService;
    @Mock
    RiskControlFetchService riskControlFetchService;
    @Mock
    StatisticsRecordService statisticsRecordService;
    @Mock
    DataManagerFetchService dataManagerFetchService;
    @Mock
    UserRegistrationRecordDAO userRegistrationRecordDAO;
    @Mock
    IndustryActivityResolveService activityResolveService;
    @Mock
    IndicatorLocalCacheService indicatorLocalCacheService;
    @Mock
    StatisticsMsgProduceService statisticsMsgProduceService;
    @Mock
    AwardConfigLocalCacheService awardConfigLocalCacheService;
    @Mock
    StatisticsRoiService statisticsRoiService;
    @Mock
    ActivityLocalCacheService activityLocalCacheService;
    @Mock
    OrgManageFetchService orgManageFetchService;

    @InjectMocks
    StatisticsReviewServiceImpl statisticsReviewServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testDestroy() {
        statisticsReviewServiceImpl.destroy();
    }


    @Test
    void testCompatibleFixIndicatorAndAward() {
        List<Map<String, Object>> mapList = Lists.newArrayList();

        String baseInfoMapStr = "{\n" +
                "    \"taskId\": 8249635,\n" +
                "    \"baseTr\": 190,\n" +
                "    \"baseTrUp\": 200545,\n" +
                "    \"baseTrDown\": 10525626,\n" +
                "    \"baseRefundRate\": 1680,\n" +
                "    \"baseRefundRateUp\": 1859613,\n" +
                "    \"baseRefundRateDown\": 11058020,\n" +
                "    \"base_indicator_activity_total_172\": 700,\n" +
                "    \"activityDays\": 7,\n" +
                "    \"baseIndicatorEffectiveDays\": 21,\n" +
                "    \"base_indicator_day_avg_172\": \"100\",\n" +
                "    \"strategyActivity_award_15_1\": \"{\\\"userId\\\":2176580531,\\\"step\\\":1," +
                "\\\"awardType\\\":15,\\\"value\\\":null,\\\"awardTargetType\\\":\\\"differentFixed\\\"," +
                "\\\"values\\\":[{\\\"rewardReturnType\\\":null,\\\"rewardReturnIndicatorId\\\":null," +
                "\\\"value\\\":\\\"200\\\"}]}\",\n" +
                "    \"strategyActivity_indicator_172_1\": \"{\\\"userId\\\":2176580531,\\\"step\\\":1," +
                "\\\"indicatorId\\\":172,\\\"value\\\":null,\\\"indicatorTargetType\\\":\\\"differentFixed\\\"," +
                "\\\"incValue\\\":\\\"100000\\\"}\"\n" +
                "}";
        String baseInfoMapStr2 = "{\n" +
                "\"taskId\": 8249669,\n" +
                "\"baseTr\": 620,\n" +
                "\"baseTrUp\": 385292874,\n" +
                "\"baseTrDown\": 6180593343,\n" +
                "\"baseRefundRate\": 4920,\n" +
                "\"baseRefundRateUp\": 3958985823,\n" +
                "\"baseRefundRateDown\": 8042783222,\n" +
                "\"base_indicator_activity_total_200\": 0,\n" +
                "\"base_indicator_activity_total_172\": 0,\n" +
                "\"activityDays\": 14,\n" +
                "\"baseIndicatorEffectiveDays\": 13,\n" +
                "\"base_indicator_day_avg_172\": \"0\",\n" +
                "\"base_indicator_day_avg_200\": \"0\",\n" +
                "\"strategyActivity_award_4_1\": \"{\\\"userId\\\":1669601175,\\\"step\\\":1,\\\"awardType\\\":4,\\\"value\\\":null,\\\"awardTargetType\\\":\\\"incrSpecifyIndicatorReturn\\\",\\\"values\\\":[{\\\"rewardReturnType\\\":1,\\\"rewardReturnIndicatorId\\\":172,\\\"value\\\":\\\"0.4\\\"}]}\",\n" +
                "\"strategyActivity_award_4_2\": \"{\\\"userId\\\":1669601175,\\\"step\\\":2,\\\"awardType\\\":4,\\\"value\\\":null,\\\"awardTargetType\\\":\\\"incrSpecifyIndicatorReturn\\\",\\\"values\\\":[{\\\"rewardReturnType\\\":1,\\\"rewardReturnIndicatorId\\\":172,\\\"value\\\":\\\"0.5\\\"}]}\",\n" +
                "\"strategyActivity_award_15_1\": \"{\\\"userId\\\":1669601175,\\\"step\\\":1,\\\"awardType\\\":15,\\\"value\\\":null,\\\"awardTargetType\\\":\\\"differentFixed\\\",\\\"values\\\":[{\\\"rewardReturnType\\\":null,\\\"rewardReturnIndicatorId\\\":null,\\\"value\\\":\\\"200\\\"}]}\",\n" +
                "\"strategyActivity_award_15_2\": \"{\\\"userId\\\":1669601175,\\\"step\\\":2,\\\"awardType\\\":15,\\\"value\\\":null,\\\"awardTargetType\\\":\\\"differentFixed\\\",\\\"values\\\":[{\\\"rewardReturnType\\\":null,\\\"rewardReturnIndicatorId\\\":null,\\\"value\\\":\\\"600\\\"}]}\",\n" +
                "\"strategyActivity_indicator_172_1\": \"{\\\"userId\\\":1669601175,\\\"step\\\":1,\\\"indicatorId\\\":172,\\\"value\\\":null,\\\"indicatorTargetType\\\":\\\"differentFixed\\\",\\\"incValue\\\":\\\"100000\\\"}\",\n" +
                "\"strategyActivity_indicator_172_2\": \"{\\\"userId\\\":1669601175,\\\"step\\\":2,\\\"indicatorId\\\":172,\\\"value\\\":null,\\\"indicatorTargetType\\\":\\\"differentFixed\\\",\\\"incValue\\\":\\\"200000\\\"}\",\n" +
                "\"strategyActivity_indicator_200_1\": \"{\\\"userId\\\":1669601175,\\\"step\\\":1,\\\"indicatorId\\\":200,\\\"value\\\":null,\\\"indicatorTargetType\\\":\\\"differentFixed\\\",\\\"incValue\\\":\\\"9000000\\\"}\",\n" +
                "\"strategyActivity_indicator_200_2\": \"{\\\"userId\\\":1669601175,\\\"step\\\":2,\\\"indicatorId\\\":200,\\\"value\\\":null,\\\"indicatorTargetType\\\":\\\"differentFixed\\\",\\\"incValue\\\":\\\"28800000\\\"}\"\n" +
                "}";
        Map<String, Object> baseInfoMap = ObjectMapperUtils.fromJson(baseInfoMapStr);
        Map<String, Object> baseInfoMap2 = ObjectMapperUtils.fromJson(baseInfoMapStr2);
        mapList.add(baseInfoMap);
        mapList.add(baseInfoMap2);
        statisticsReviewServiceImpl.compatibleFixIndicatorAndAward(mapList);
        mapList.forEach(map -> {
            Set<String> keySet = map.keySet();
            keySet = keySet.stream()
                    .filter(key -> key.startsWith(STRATEGY_FIX_AWARD_KEY) || key.startsWith(STRATEGY_FIX_TARGET_KEY))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(keySet)){
                return;
            }
            keySet.forEach(key -> {
                Object obj = map.get(key);
                Assertions.assertTrue(obj instanceof Long);
            });
        });
//        System.out.println(ObjectMapperUtils.toJSON(mapList));
    }

    @Test
    void testPageQueryReviewInfo() {
//        when(statisticsEsDAO.pageQueryUserData(any(QueryBuilder.class), anyInt(), anyInt())).thenReturn(new EsQueryResponse<Map<String, Object>>());
//        when(queryBuildFactory.buildQueryBuilder(any(List<StatisticsConditionBO>.class),
//                any(Map<String, Object>.class))).thenReturn(null);
//        when(queryBuildFactory.buildTermCondition(anyString(), any(Number.class))).thenReturn(new StatisticsConditionBO());
//        when(queryBuildFactory.buildTermsCondition(anyString(), any(List<Long>.class))).thenReturn(new StatisticsConditionBO());
//        when(queryBuildFactory.buildStrTermsConditionShould(anyString(), any(List<String>.class))).thenReturn(new StatisticsConditionBO());
//
//        EsQueryResponse<Map<String, Object>> result =
//                statisticsReviewServiceImpl.pageQueryReviewInfo(new StaffPolicyRepoConfig(), List.of(Long.valueOf(1))
//                        , new SlrBelongInfoCondition("staffCode", Long.valueOf(1), List.of("firstIndustryCodeList"),
//                                List.of("secondIndustryCodeList"), List.of("thirdIndustryCodeList"),
//                                PolicyDrawType.UNKNOWN), 0, 0);
//        Assertions.assertEquals(new EsQueryResponse<Map<String, Object>>(), result);
    }

}
