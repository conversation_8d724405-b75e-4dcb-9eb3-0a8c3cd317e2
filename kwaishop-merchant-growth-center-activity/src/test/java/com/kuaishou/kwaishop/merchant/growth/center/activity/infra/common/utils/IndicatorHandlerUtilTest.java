package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import java.util.List;

import org.junit.Test;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.CategoryBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-29
 */
public class IndicatorHandlerUtilTest {

    @Test
    public void testParseCategoryLevelList() {
        String json =
                "[{\"categoryId\":1034,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":1044,\"hierarchy\":2,"
                        + "\"childCategory\":[]},{\"categoryId\":1561,\"hierarchy\":2,\"childCategory\":[]},"
                        + "{\"categoryId\":1562,\"hierarchy\":2,\"childCategory\":[]},{\"categoryId\":1053,"
                        + "\"hierarchy\":2,\"childCategory\":[{\"categoryId\":1051,\"hierarchy\":3,"
                        + "\"childCategory\":[{\"categoryId\":1052,\"hierarchy\":3,\"childCategory\":[]}]}]}]},"
                        + "{\"categoryId\":3732,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":3733,"
                        + "\"hierarchy\":2,\"childCategory\":[]}]}]";

        List<CategoryBO> categoryBOS = ObjectMapperUtils.fromJSON(json, List.class, CategoryBO.class);
        List<Long> result = IndicatorHandlerUtil.parseCategory3LevelList(categoryBOS);
        System.out.println(toJSON(result));
    }

    @Test
    public void testParseCategoryLevelList2() {
        String json =
                "[{\"categoryId\":1163,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":1187,\"hierarchy\":2,"
                        + "\"childCategory\":[{\"categoryId\":1190,\"hierarchy\":3,\"childCategory\":[]}]}]},"
                        + "{\"categoryId\":1163,\"hierarchy\":1,\"childCategory\":[{\"categoryId\":1187,"
                        + "\"hierarchy\":2,\"childCategory\":[{\"categoryId\":1191,\"hierarchy\":3,"
                        + "\"childCategory\":[]}]}]}]";

        List<CategoryBO> categoryBOS = ObjectMapperUtils.fromJSON(json, List.class, CategoryBO.class);
        List<Long> result = IndicatorHandlerUtil.parseCategory3LevelList(categoryBOS);
        System.out.println("三级类目id:" + toJSON(result));
        System.out.println("二级类目id:" + toJSON(IndicatorHandlerUtil.parseCategory2LevelList(categoryBOS)));
    }
}

