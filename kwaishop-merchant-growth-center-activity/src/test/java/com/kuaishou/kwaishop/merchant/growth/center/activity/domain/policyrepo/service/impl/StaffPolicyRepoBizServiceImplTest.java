package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.service.impl;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SellerPolicySignUpSummary;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.model.bo.SlrBelongInfoCondition;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.statistics.service.StatisticsReviewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.model.bo.StaffPolicyRepoConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.localcache.AwardConfigLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.activity.ActivityDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy.QueryPolicyListReq;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.policy.StaffPolicyDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-10
 */
class StaffPolicyRepoBizServiceImplTest {
    @Mock
    ActivityLocalCacheService activityLocalCacheService;
    @Mock
    AwardConfigLocalCacheService awardConfigLocalCacheService;
    @Mock
    StatisticsReviewService statisticsReviewService;
    @InjectMocks
    StaffPolicyRepoBizServiceImpl staffPolicyRepoBizServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetStaffPolicyList() {
        String activityMapStr = "{\"13633\":{\"id\":13633,\"createTime\":1725328600413,\"updateTime\":1725328703315," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"system\",\"deleted\":0,\"version\":5," +
                "\"name\":\"分销返现奖励\",\"showName\":\"分销返现奖励\",\"bizName\":\"1012\",\"type\":2,\"seriesType\":10012," +
                "\"description\":\"\",\"crowdType\":2,\"drawLimitType\":0,\"crowdConfig\":\"\"," +
                "\"alias\":\"1725328600015\",\"startTime\":1725120000000,\"endTime\":1727711999999," +
                "\"drawStartTime\":0,\"drawEndTime\":1727711999999,\"status\":30,\"ext\":\"{\\\"bizType\\\": " +
                "\\\"daren\\\", \\\"riskCode\\\": \\\"2022070101025850\\\", \\\"signType\\\": 1, \\\"traceKey\\\": " +
                "\\\"stategy_activity_1725328469995\\\", \\\"application\\\": \\\"official\\\", \\\"maxDrawCount\\\":" +
                " 0, \\\"batchEstimate\\\": 2, \\\"initSellerNum\\\": 9, \\\"businessSupply\\\": true, " +
                "\\\"limitDrawCount\\\": false, \\\"onlineStartTime\\\": 1725328674421, \\\"remainDrawCount\\\": 0, " +
                "\\\"withBudgetLimit\\\": false, \\\"activityTimeType\\\": 1, \\\"activityPatternType\\\": " +
                "\\\"task\\\", \\\"allBudgetLimitTaskSettle\\\": false}\",\"cycleType\":0,\"cycleConfig\":null," +
                "\"completeCondition\":\"{\\\"config\\\": null, \\\"entityType\\\": 2, \\\"conditionType\\\": 1}\"," +
                "\"showEndTime\":1728230399999,\"showConfig\":\"{\\\"darenWorkbench\\\": {\\\"appBanner\\\": " +
                "\\\"https://js-ec.static.yximgs.com/udata/pkg/ks-merchant/kwaishop-ssr/banner1725246297777.png\\\", " +
                "\\\"taskItemInfos\\\": {}}}\",\"showTag\":null,\"createType\":1,\"frontConfig\":\"[{\\\"scene\\\": " +
                "\\\"rulePage\\\"}, {\\\"scene\\\": \\\"darenComponent\\\", \\\"component\\\": [{\\\"data\\\": " +
                "\\\"{\\\\\\\"targetInfo\\\\\\\":[{\\\\\\\"subTitle\\\\\\\":\\\\\\\"挂车短视频发布数\\\\\\\"," +
                "\\\\\\\"buttonText\\\\\\\":\\\\\\\"去发布\\\\\\\",\\\\\\\"buttonJumpUrl\\\\\\\":\\\\\\\"https://www" +
                ".baidu.com/\\\\\\\",\\\\\\\"awardText\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showFlag\\\\\\\":true," +
                "\\\\\\\"showProgress\\\\\\\":true,\\\\\\\"indicatorId\\\\\\\":197," +
                "\\\\\\\"indicatorName\\\\\\\":\\\\\\\"挂车短视频发布数\\\\\\\"}]," +
                "\\\\\\\"subTitle\\\\\\\":\\\\\\\"测试实验\\\\\\\",\\\\\\\"detailPageUrl\\\\\\\":\\\\\\\"\\\\\\\"," +
                "\\\\\\\"showName\\\\\\\":\\\\\\\"短视频\\\\\\\"}\\\", \\\"entityId\\\": 615324, \\\"entityType\\\": 2, " +
                "\\\"levelOrder\\\": 1, \\\"componentName\\\": \\\"STEP\\\", \\\"subActivityOrder\\\": 1}], " +
                "\\\"sceneShowConfig\\\": {\\\"sceneList\\\": null, \\\"sceneType\\\": 0}}]\"," +
                "\"resourceActivityId\":6321,\"attachInfo\":null,\"galaxyPageConfig\":null,\"liteConfig\":null}}";
        String awardConfigListStr = "[{\"id\":4250897,\"createTime\":1725328600052,\"updateTime\":1725328600052," +
                "\"creator\":\"wb_zhengguihua03\",\"modifier\":\"wb_zhengguihua03\",\"deleted\":0,\"version\":0," +
                "\"awardName\":\"飞机师\",\"awardType\":24,\"activityId\":13633,\"entityId\":615325,\"expireTime\":0," +
                "\"allotRule\":null,\"awardSubject\":\"\",\"sendRuleType\":\"task\"," +
                "\"sendRule\":\"{\\\"awardSendType\\\":6,\\\"awardCount\\\":0,\\\"interestConfigId\\\":0," +
                "\\\"awardSendRuleCode\\\":\\\"strategy_admin_award_calc\\\"," +
                "\\\"awardSendRuleConfig\\\":\\\"{\\\\\\\"awardType\\\\\\\":24,\\\\\\\"expireTimeConfig\\\\\\\":{}," +
                "\\\\\\\"sendConfig\\\\\\\":{\\\\\\\"delaySendTimeType\\\\\\\":1},\\\\\\\"sendRate\\\\\\\":100," +
                "\\\\\\\"stepConfigs\\\\\\\":[{\\\\\\\"step\\\\\\\":1," +
                "\\\\\\\"type\\\\\\\":\\\\\\\"incrSpecifyIndicatorReturn\\\\\\\"," +
                "\\\\\\\"value\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"maxAwardValue\\\\\\\\\\\\\\\":1000000," +
                "\\\\\\\\\\\\\\\"minAwardValue\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"rateUpperLimitPercent\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"specifyIndicatorInfos\\\\\\\\\\\\\\\":[{\\\\\\\\\\\\\\\"indicatorId" +
                "\\\\\\\\\\\\\\\":209,\\\\\\\\\\\\\\\"returnType\\\\\\\\\\\\\\\":2," +
                "\\\\\\\\\\\\\\\"returnPercent\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"20\\\\\\\\\\\\\\\"," +
                "\\\\\\\\\\\\\\\"calcFactor\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"settleGmvUpperLimit\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"formulaId\\\\\\\\\\\\\\\":0}]," +
                "\\\\\\\\\\\\\\\"customAeardDapFileUrl\\\\\\\\\\\\\\\":null}\\\\\\\",\\\\\\\"awardType\\\\\\\":null," +
                "\\\\\\\"uniqueKey\\\\\\\":null}]}\\\",\\\"awardCalType\\\":1," +
                "\\\"awardRuleInput\\\":[\\\"gmvFullAperture\\\",\\\"strategyAdminBase\\\",\\\"specifyIndicator\\\"," +
                "\\\"trIndicator\\\",\\\"returnFormulaCalc\\\"],\\\"isAwardDeduct\\\":0,\\\"additionalSend\\\":false," +
                "\\\"awardMathConfig\\\":null,\\\"ext\\\":null,\\\"maxAwardCount\\\":0}\",\"interestPackageId\":null," +
                "\"interestConfigId\":0,\"interestExtParam\":null,\"status\":1," +
                "\"delayCalcRule\":\"{\\\"parallelSwitch\\\": false, \\\"hitCalcRuleList\\\": [{\\\"priority\\\": 9, " +
                "\\\"calcRuleCode\\\": \\\"strategy_admin_award_delay_calc\\\", \\\"calcTypeName\\\": " +
                "\\\"INDICATOR_OUTPUT_DELAY\\\", \\\"beforeDelayValue\\\": 2592000000}], \\\"delayCalcStrategy\\\": " +
                "\\\"MAX\\\", \\\"delayCalcBizDomain\\\": \\\"AWARD_SEND\\\", \\\"toleranceExceptionSwitch\\\": " +
                "false}\",\"sendTimeType\":1}]";
        String sellerSignUpSummaryMap = "{\"13633\":{\"sellerId\":null,\"activityId\":13633,\"signedUpCount\":1," +
                "\"noSignUpCount\":8}}";
        Map<Long, ActivityDO> map = ObjectMapperUtils.fromJSON(activityMapStr, Map.class, Long.class, ActivityDO.class);
        when(activityLocalCacheService.batchQueryActivityInfo(any(List.class))).thenReturn(map);
        List<AwardConfigDO> awardConfigDOList = ObjectMapperUtils.fromJSON(awardConfigListStr, List.class,
                AwardConfigDO.class);
        when(awardConfigLocalCacheService.queryAwardConfigByActivityId(anyLong())).thenReturn(awardConfigDOList);
        Map<Long, SellerPolicySignUpSummary> summaryMap = ObjectMapperUtils.fromJSON(sellerSignUpSummaryMap,
                Map.class, Long.class, SellerPolicySignUpSummary.class);
        when(statisticsReviewService.querySellerActivitySignUpSummary(any(StaffPolicyRepoConfig.class),
                any(List.class), any(SlrBelongInfoCondition.class))).thenReturn(summaryMap);

        QueryPolicyListReq build = QueryPolicyListReq.newBuilder()
                .addActivityId(13633L)
                .setOperator("sunhuzeng")
                .build();
        List<StaffPolicyDTO> result = staffPolicyRepoBizServiceImpl.getStaffPolicyList(build);
        Assertions.assertFalse(result.isEmpty());
    }

    @Test
    void testGetStaffPolicyDetail() {
        String activityStr = "";
//        when(activityLocalCacheService.queryActivityInfo(anyLong())).thenReturn(null);
        String awardConfigListStr = "";
//        when(awardConfigLocalCacheService.queryAwardConfigByActivityId(anyLong())).thenReturn(List.of(null));

        StaffPolicyDTO result = staffPolicyRepoBizServiceImpl.getStaffPolicyDetail(13633L);
        Assertions.assertNotNull(result);
    }
}
