package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.SecurityLogUtils.safeLog;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-17
 */
class SecurityLogUtilsTest {

    @Test
    void testSafeLog() {
        String contextJson = "{\"userId\":1234567890,\"ruleCode\":\"testRuleCode\",\"context\":{\"key1\":\"value1\",\"key2\":\"value2\"}}";
        System.out.println(safeLog(contextJson, null));
        String contextJson2 = "{\n" +
                "    \"NewSellerGrowthTaskPC\": {\n" +
                "        \"pageCode\": \"NewSellerGrowthTaskPC\",\n" +
                "        \"moduleResourceConfigs\": [\n" +
                "            {\n" +
                "                \"moduleCode\": \"newSellerFirstSaleCard\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"ASYNC\",\n" +
                "                \"renderHandlerSpecify\": \"NewSellerFirstSaleCardState\",\n" +
                "                \"moduleDesc\": \"卡片内容信息\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 1500,\n" +
                "                \"dataMergeRuleType\": \"KFLOW\",\n" +
                "                \"dataMergeRule\": \"new_seller_first_sale_card_merge_rule\",\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"strategyLaunchInfo\",\n" +
                "                        \"type\": \"KRPC\",\n" +
                "                        \"dataLoadMode\": \"MULTI_THREAD\",\n" +
                "                        \"fetchDataConfig\": \"{\\\"kessServiceName\\\":\\\"kwaishop-merchant-growth-center\\\",\\\"interfaceName\\\":\\\"kuaishou.kwaishop.merchant.growth.center.activity.launch.ActivityLaunchService\\\",\\\"methodName\\\":\\\"QueryLaunchInfo\\\",\\\"timeoutMs\\\":\\\"1000\\\",\\\"requestJsonTemplate\\\":\\\"{\\\\\\\"userId\\\\\\\":#{userId},\\\\\\\"channel\\\\\\\":\\\\\\\"seller_workbench_home_page\\\\\\\",\\\\\\\"scene\\\\\\\":\\\\\\\"seller_workbench_home_page_new_seller_first_sale_card\\\\\\\"}\\\"}\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"serviceResponseMock\": null\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"code\": \"sellerDetailInfo\",\n" +
                "                        \"type\": \"CUSTOMIZE\",\n" +
                "                        \"dataLoadMode\": \"MULTI_THREAD\",\n" +
                "                        \"fetchDataConfig\": \"{\\\"customizeType\\\":\\\"sellerDetailInfo\\\"}\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"serviceResponseMock\": null\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"code\": \"sellerBusinessInfo\",\n" +
                "                        \"type\": \"CUSTOMIZE\",\n" +
                "                        \"dataLoadMode\": \"MULTI_THREAD\",\n" +
                "                        \"fetchDataConfig\": \"{\\\"customizeType\\\":\\\"sellerBusinessInfo\\\",\\\"customizeParam\\\":\\\"{\\\\\\\"queryId\\\\\\\":11420,\\\\\\\"resKey\\\\\\\":\\\\\\\"pay_order_amt\\\\\\\"}\\\"}\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"serviceResponseMock\": null,\n" +
                "                        \"mockResultMap\": {\n" +
                "                            \"2181835997\": 100\n" +
                "                        }\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"code\": \"sellerAwardInfo\",\n" +
                "                        \"type\": \"KRPC\",\n" +
                "                        \"dataLoadMode\": \"MULTI_THREAD\",\n" +
                "                        \"fetchDataConfig\": \"{\\\"kessServiceName\\\":\\\"kwaishop-merchant-interest-center\\\",\\\"interfaceName\\\":\\\"kuaishou.kwaishop.merchant.interest.center.query.KwaishopMerchantInterestQueryService\\\",\\\"methodName\\\":\\\"QueryInterestAwardAggregation\\\",\\\"timeoutMs\\\":\\\"1000\\\",\\\"requestJsonTemplate\\\":\\\"{\\\\\\\"userId\\\\\\\":#{userId},\\\\\\\"scene\\\\\\\":\\\\\\\"taskAwardPc\\\\\\\"}\\\",\\\"responseToCamelCase\\\":true}\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"serviceResponseMock\": null\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"code\": \"sellerFlowInfo\",\n" +
                "                        \"type\": \"KRPC\",\n" +
                "                        \"dataLoadMode\": \"MULTI_THREAD\",\n" +
                "                        \"fetchDataConfig\": \"{\\\"kessServiceName\\\":\\\"kwaishop-merchant-interest-center\\\",\\\"interfaceName\\\":\\\"kuaishou.kwaishop.merchant.interest.center.query.KwaishopMerchantInterestQueryService\\\",\\\"methodName\\\":\\\"QueryInterestAvailableAwardCard\\\",\\\"timeoutMs\\\":\\\"1000\\\",\\\"requestJsonTemplate\\\":\\\"{\\\\\\\"userId\\\\\\\":#{userId}}\\\",\\\"responseToCamelCase\\\":true}\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"serviceResponseMock\": null\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"smbPlanJoinPageApp\": {\n" +
                "        \"pageCode\": \"smbPlanJoinPageApp\",\n" +
                "        \"moduleResourceConfigs\": [\n" +
                "            {\n" +
                "                \"moduleCode\": \"commonConfig\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"中小行业内容活动策划通用配置\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"pagePicUrl\": \"大大大\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"userJoinState\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"ASYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageUserJoinState\",\n" +
                "                \"moduleDesc\": \"用户报名状态组件\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 1000\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"progressStep\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageProgressStepState\",\n" +
                "                \"moduleDesc\": \"活动进度条组件\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 1000\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"activityReportModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"中小行业内容活动策划「活动报名组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"参与「清凉一夏·出游季」抢占夏日C位\",\n" +
                "                            \"subTitle\": \"高流量扶持+榜单强曝光，引爆直播间销量！\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"iconUrl\": \"\",\n" +
                "                                    \"title\": \"榜单新玩法\",\n" +
                "                                    \"subTitle\": \"冲榜强曝光\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"iconUrl\": \"\",\n" +
                "                                    \"title\": \"海量流量加持\",\n" +
                "                                    \"subTitle\": \"直播短视频同享\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"iconUrl\": \"\",\n" +
                "                                    \"title\": \"精准曝光触达\",\n" +
                "                                    \"subTitle\": \"开播即享流量\"\n" +
                "                                }\n" +
                "                            ],\n" +
                "                            \"reportBtn\": {\n" +
                "                                \"btnText\": \"立即免费报名\",\n" +
                "                                \"rejectReason\": \"暂无法参加活动，请联系平台运营小二\",\n" +
                "                                \"tips\": \"限量80名 先到先得\"\n" +
                "                            }\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"awardModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"smbPlan「权益组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"\",\n" +
                "                            \"subTitle\": \"\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"title\": \"首页海景房\",\n" +
                "                                    \"subTitle\": \"置顶展示，全方位曝光\",\n" +
                "                                    \"iconUrl\": \"3\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可曝光流量\",\n" +
                "                                    \"awardValue\": \"1000万\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"导购榜单\",\n" +
                "                                    \"subTitle\": \"开播冲榜，直达消费者\",\n" +
                "                                    \"iconUrl\": \"3\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可曝光流量\",\n" +
                "                                    \"awardValue\": \"8000万\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"直播预约\",\n" +
                "                                    \"subTitle\": \"开播即可召回观众\",\n" +
                "                                    \"iconUrl\": \"3\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可召回流量\",\n" +
                "                                    \"awardValue\": \"300万\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"流量加权\",\n" +
                "                                    \"subTitle\": \"直播、短视频同享加权\",\n" +
                "                                    \"iconUrl\": \"3\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可召回流量\",\n" +
                "                                    \"awardValue\": \"500万\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"resourceModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"smbPlan「资源组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"\",\n" +
                "                            \"subTitle\": \"\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"title\": \"APP开屏资源\",\n" +
                "                                    \"subTitle\": \"23\",\n" +
                "                                    \"iconUrl\": \"32\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可曝光流量\",\n" +
                "                                    \"awardValue\": \"10亿\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"直播爆款首页banner\",\n" +
                "                                    \"subTitle\": \"23\",\n" +
                "                                    \"iconUrl\": \"32\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可曝光流量\",\n" +
                "                                    \"awardValue\": \"800万\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"首页置顶快捷入口\",\n" +
                "                                    \"subTitle\": \"23\",\n" +
                "                                    \"iconUrl\": \"32\",\n" +
                "                                    \"bannerUrl\": \"4\",\n" +
                "                                    \"awardText\": \"预估可曝光流量\",\n" +
                "                                    \"awardValue\": \"3000万\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"otherSignUpModule\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageAllSellerSignUpState\",\n" +
                "                \"moduleDesc\": \"大家都在报组件\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 500\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"qaModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"smbPlan「问答组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"活动常见问答\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"question\": \"1.需要额外付费吗？\",\n" +
                "                                    \"answer\": \"全程0费用，资源由平台承担\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"question\": \"2.如何才能获得更多资源？\",\n" +
                "                                    \"answer\": \"根据自己情况报更高活动目标GMV\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"question\": \"3. 我可以随便报目标GMV吗？\",\n" +
                "                                    \"answer\": \"目标GMV需根据自身情况，合理报高。如所报名目标无法完成，平台将视情况撤回商家全部资源\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"signUpModal\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"ASYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageInvestmentSignUpWdState\",\n" +
                "                \"moduleDesc\": \"smbPlan「报名弹窗组件」\",\n" +
                "                \"moduleTimeoutMs\": 1200,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"staticData\": {\n" +
                "                            \"buttonText\": \"确认抢占\",\n" +
                "                            \"desc\": \"已根据您历史表现，自动为您填写最适合的目标金额，您也可以手动修改；目标范围与对应可抢占的资源如下表。\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"pageValidStartTime\": 1753113600000,\n" +
                "        \"pageValidEndTime\": 1754236799999\n" +
                "    },\n" +
                "    \"smbPlanPreHotPageApp\": {\n" +
                "        \"pageCode\": \"smbPlanPreHotPageApp\",\n" +
                "        \"moduleResourceConfigs\": [\n" +
                "        {\n" +
                "                \"moduleCode\": \"floor\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"中小行业内容活动策划-楼层顺序组件\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"orders\":  [\"commonConfig\", \"progressStep\", \"taskModule\", \"qaModule\"]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"commonConfig\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"中小行业内容活动策划通用配置\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"pagePicUrl\": \"大大大\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"userJoinState\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"ASYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageUserJoinState\",\n" +
                "                \"moduleDesc\": \"用户报名状态组件\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 1000\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"progressStep\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageProgressStepState\",\n" +
                "                \"moduleDesc\": \"活动进度条组件\",\n" +
                "                \"disable\": false,\n" +
                "                \"tailNumber\": \"\",\n" +
                "                \"moduleTimeoutMs\": 1000\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"taskModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"预热器「任务组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"倒计时5天，做任务助力冲榜！\",\n" +
                "                            \"subTitle\": \"预热期做任务得奖励，赢在起跑线\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"title\": \"发3次直播预告，蓄力冲榜\",\n" +
                "                                    \"subTitle\": \"收获免费流量，同时可获得平台奖励\",\n" +
                "                                    \"awards\": [\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"流量加权\"\n" +
                "                                        },\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"直播预告模块\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"qrUrl\": \"\",\n" +
                "                                    \"reportBtn\": {\n" +
                "                                        \"btnText\": \"发预告\",\n" +
                "                                        \"jumpUrl\": \"111\"\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"发5个短视频，涨粉涨人气\",\n" +
                "                                    \"subTitle\": \"发布打榜赛短视频，带上话题 #夏日元气榜# 标签即可完成任务\",\n" +
                "                                    \"awards\": [\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"流量加权\"\n" +
                "                                        },\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"直播预告模块\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"qrUrl\": \"11112\",\n" +
                "                                    \"reportBtn\": {\n" +
                "                                        \"btnText\": \"发视频\",\n" +
                "                                        \"jumpUrl\": \"111\"\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"目标再提升1.4万，可获免费流量\",\n" +
                "                                    \"subTitle\": \"合理设定更高目标，优先享更多资源\",\n" +
                "                                    \"awards\": [\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"海景房资源\"\n" +
                "                                        },\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"主播墙资源\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"qrUrl\": \"11112\",\n" +
                "                                    \"reportBtn\": {\n" +
                "                                        \"type\": \"signUp\",\n" +
                "                                        \"btnText\": \"提目标\",\n" +
                "                                        \"jumpUrl\": \"111\"\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"title\": \"扫码进群，领官方活动氛围素材\",\n" +
                "                                    \"subTitle\": \"长按保存二维码，微信扫码进快手企微群\",\n" +
                "                                    \"awards\": [\n" +
                "                                        {\n" +
                "                                            \"iconUrl\": \"11223\",\n" +
                "                                            \"awardText\": \"海量免费官方活动氛围素材\"\n" +
                "                                        }\n" +
                "                                    ],\n" +
                "                                    \"qrUrl\": \"11112\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"signUpModal\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"ASYNC\",\n" +
                "                \"renderHandlerSpecify\": \"SmbPlanPageInvestmentSignUpWdState\",\n" +
                "                \"moduleDesc\": \"预热期「修改目标组件」\",\n" +
                "                \"moduleTimeoutMs\": 1200,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 1000,\n" +
                "                        \"staticData\": {\n" +
                "                            \"buttonText\": \"确认抢占\",\n" +
                "                            \"desc\": \"已根据您历史表现，自动为您填写最适合的目标金额，您也可以手动修改；目标范围与对应可抢占的资源如下表。\"\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"moduleCode\": \"qaModule\",\n" +
                "                \"type\": \"KCONF\",\n" +
                "                \"moduleType\": \"NORMAL_MODULE\",\n" +
                "                \"renderType\": \"SYNC\",\n" +
                "                \"moduleDesc\": \"smbPlan「问答组件」\",\n" +
                "                \"moduleTimeoutMs\": 500,\n" +
                "                \"dynamicDataSourceConfigs\": [\n" +
                "                    {\n" +
                "                        \"code\": \"tips\",\n" +
                "                        \"type\": \"KCONF\",\n" +
                "                        \"dataLoadMode\": \"SYNC\",\n" +
                "                        \"dataLoadTimeout\": 500,\n" +
                "                        \"staticData\": {\n" +
                "                            \"title\": \"常见问答\",\n" +
                "                            \"list\": [\n" +
                "                                {\n" +
                "                                    \"question\": \"1.需要额外付费吗？\",\n" +
                "                                    \"answer\": \"全程0费用，资源由平台承担\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"question\": \"2.如何才能获得更多资源？\",\n" +
                "                                    \"answer\": \"根据自己情况报更高活动目标GMV\"\n" +
                "                                },\n" +
                "                                {\n" +
                "                                    \"question\": \"3. 我可以随便报目标GMV吗？\",\n" +
                "                                    \"answer\": \"目标GMV需根据自身情况，合理报高。如所报名目标无法完成，平台将视情况撤回商家全部资源\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        System.out.println(safeLog(contextJson2, null));
    }

}
