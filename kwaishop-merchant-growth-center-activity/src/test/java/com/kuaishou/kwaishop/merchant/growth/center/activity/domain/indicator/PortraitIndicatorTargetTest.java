package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.indicator;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;

/**
 * <AUTHOR>
 */
public class PortraitIndicatorTargetTest {

    public static void main(String[] args) {
        TargetConfigBO targetConfig = new TargetConfigBO();
        targetConfig.setBaseIndicatorId(11L);
        IndicatorStepTargetConfigBO stepTargetConfig = new IndicatorStepTargetConfigBO();
        stepTargetConfig.setStep(1);
        stepTargetConfig.setType("incrPercent");
        stepTargetConfig.setValue("60");
        IndicatorStepTargetConfigBO stepTargetConfig2 = new IndicatorStepTargetConfigBO();
        stepTargetConfig2.setStep(2);
        stepTargetConfig2.setType("userAssign");
        stepTargetConfig2.setValue("{\"baseIndicatorId\":201,\"stepConfigs\":[{\"step\":1,\"type\":\"userAssign\","
                + "\"value\":\"{\\\"userAssignFileUrl\\\":\\\"https://cdnfile.corp.kuaishou"
                + ".com/kc/files/a/fangzhou/pkg/ks-merchant/fangzhou/3h8pzij01.1c741280d45754cc.xlsx\\\","
                + "\\\"fileName\\\":\\\"文件名\\\"}\"}]}");
        targetConfig.setStepConfigs(Lists.newArrayList(stepTargetConfig, stepTargetConfig2));
        Map<String, Object> context = Maps.newHashMap();
        context.put("targetConfig", ObjectMapperUtils.toJSON(targetConfig));
        context.put("baseIndicatorEffectiveDays", 10);
        context.put("industryActivity_2_indicator_1", 99);
        context.put("base_indicator_11", 100);
        context.put("activityDays", 3);
        context.put("indicatorId", 1);
        String result = testCalcTargetValue(context);
        System.out.println(result);
    }

    private static String testCalcTargetValue(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String indicatorTargetConfigJson = MapUtils.getString(context, "targetConfig");
        if (StringUtils.isBlank(indicatorTargetConfigJson)) {
            result.put("result", 11);
            result.put("message", "指标阶段目标配置不能为空");
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> stringObjectMap = ObjectMapperUtils.fromJson(indicatorTargetConfigJson);
        TargetConfigBO targetConfig = ObjectMapperUtils.fromJSON(indicatorTargetConfigJson, TargetConfigBO.class);
        if (targetConfig == null || CollectionUtils.isEmpty(targetConfig.getStepConfigs())
                || targetConfig.getBaseIndicatorId() == null) {
            result.put("result", 11);
            result.put("message", "指标目标值配置缺失");
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Long> stepTargetMap = Maps.newHashMap();
        // 计算第几阶段的目标值
        for (IndicatorStepTargetConfigBO stepConfig : targetConfig.getStepConfigs()) {
            int step = stepConfig.getStep();
            if (StringUtils.isAnyBlank(stepConfig.getType(), stepConfig.getValue())) {
                result.put("result", 11);
                result.put("message", "指标阶梯规则配置有误");
                return ObjectMapperUtils.toJSON(result);
            }
            long targetValue = calcIndicatorTargetValue(context, step, targetConfig, stepConfig);
            if (targetValue < 0) {
                result.put("result", 11);
                result.put("message", "计算指标目标值失败，请检查基期数据及指标配置");
                return ObjectMapperUtils.toJSON(result);
            }
            stepTargetMap.put("step_target_" + step, targetValue);
        }
        // 取出第一阶梯
        Long firstTargetValue = MapUtils.getLong(stepTargetMap, "step_target_1", -1L);
        result.put("result", 1);
        result.put("message", "成功");
        result.put("targetValue", firstTargetValue);
        // 将各个阶梯的目标值在组成信息中留底
        result.put("componentValues", stepTargetMap);
        return ObjectMapperUtils.toJSON(result);
    }

    private static long calcIndicatorTargetValue(Map<String, Object> context, int step, TargetConfigBO targetConfig,
            IndicatorStepTargetConfigBO stepTargetConfig) {
        // 日均值
        long baseIndicatorDayAvg = calcBaseIndicatorDayAvg(context, targetConfig);
        if (baseIndicatorDayAvg <= 0) {
            return -1;
        }
        // 根据考核类型获取对应的目标值
        String type = stepTargetConfig.getType();
        // 当前的指标ID
        Long indicatorId = MapUtils.getLong(context, "indicatorId");
        // 活动期天数
        Long activityDays = MapUtils.getLong(context, "activityDays", 1L);
        long targetValue = 0L;
        if ("userAssign".equals(type)) {
            String stepTargetKey = String.format("industryActivity_%s_indicator_%s", step, indicatorId);
            targetValue = MapUtils.getLong(context, stepTargetKey, 0L);
        } else if ("incrFixed".equals(type)) {
            // 基期日均*活动天数+增量值
            targetValue = new BigDecimal(baseIndicatorDayAvg).multiply(new BigDecimal(activityDays))
                    .add(new BigDecimal(stepTargetConfig.getValue())).longValue();
        } else if ("incrPercent".equals(type)) {
            // 基期日均*活动天数*(1+增幅)
            targetValue = new BigDecimal(baseIndicatorDayAvg).multiply(new BigDecimal(activityDays))
                    .multiply(new BigDecimal(100 + Long.parseLong(stepTargetConfig.getValue())))
                    .divide(new BigDecimal(100)).longValue();
        } else {
            targetValue = -1L;
        }
        return targetValue;
    }

    /**
     * 计算基期指标的日均值
     */
    private static long calcBaseIndicatorDayAvg(Map<String, Object> context, TargetConfigBO targetConfig) {
        // 总的基期目标值
        long baseIndicatorTotal = MapUtils.getLong(context,
                "base_indicator_" + targetConfig.getBaseIndicatorId(), 0L);
        // 有效的基期天数
        Long baseIndicatorEffectiveDays = MapUtils.getLong(context, "baseIndicatorEffectiveDays", -1L);
        if (baseIndicatorEffectiveDays <= 0) {
            return -1;
        }
        return new BigDecimal(baseIndicatorTotal)
                .divide(new BigDecimal(baseIndicatorEffectiveDays), RoundingMode.UP).longValue();
    }

    public static class TargetConfigBO {
        // 关联的基期指标ID
        Long baseIndicatorId;

        // 各阶段的配置
        List<IndicatorStepTargetConfigBO> stepConfigs;

        public List<IndicatorStepTargetConfigBO> getStepConfigs() {
            return stepConfigs;
        }

        public void setStepConfigs(
                List<IndicatorStepTargetConfigBO> stepConfigs) {
            this.stepConfigs = stepConfigs;
        }

        public Long getBaseIndicatorId() {
            return baseIndicatorId;
        }

        public void setBaseIndicatorId(Long baseIndicatorId) {
            this.baseIndicatorId = baseIndicatorId;
        }
    }

    public static class IndicatorStepTargetConfigBO {
        /**
         * 指标阶段
         */
        Integer step;
        /**
         * 指标目标类型
         */
        String type;
        /**
         * 指标目标配置值
         */
        String value;

        public Integer getStep() {
            return step;
        }

        public void setStep(Integer step) {
            this.step = step;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
