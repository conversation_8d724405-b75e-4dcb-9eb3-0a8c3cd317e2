package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils.changeFenUnitAwardValueToYuan;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.enums.AwardTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.utils.AwardUnitUtils;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.AmountUtils;
import com.kuaishou.kwaishop.merchant.growth.center.common.utils.DateUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-10-21
 */
public class AmountUtilTest {

    public static void main(String[] args) {
        Long a = DateUtils.getDayBetween(1711468800000L, 1711641600000L);
        String s = AmountUtils.changeY2F(100L);
        System.out.println(s);
        String s1 = AmountUtils.changeY2F("100");
        System.out.println(s1);
        long l = AwardUnitUtils.changeYuanUnitAwardValueToFenWithCheck(
                AwardTypeEnum.getByCode(18), String.valueOf(5),
                false);
        System.out.println(l);

        // 分转元
        String awardCount = changeFenUnitAwardValueToYuan(AwardTypeEnum.getByCode(18), 500L, true);
        System.out.println(awardCount);
    }
}
