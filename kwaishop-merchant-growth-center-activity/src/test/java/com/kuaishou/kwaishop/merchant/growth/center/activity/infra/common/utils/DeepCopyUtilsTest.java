package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils;

import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-07-15
 */
public class DeepCopyUtilsTest {

    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDeepCopy() throws Exception {
        Map<String, Object> origin = new HashMap<>();
        Integer a = 0;
        Map<String, Object> data = new HashMap<>();
        data.put("aa", 1);
        data.put("bb", 2);
        origin.put("a", a);
        origin.put("b", 2);
        origin.put("data", data);
        Map<String, Object> copyMap = new HashMap<>(origin);
        System.out.println(copyMap);
        a = 1;
        origin.put("b", 4);
        data.put("aa", 3);
        data.put("bb", 4);
        System.out.println(copyMap);

//        Map<String, Object> deepCopy = deepCopy(origin);
//        origin.put("b", 0);
//        data.put("aa", 5);
//        data.put("bb", 6);
//        System.out.println(deepCopy);
    }

}
