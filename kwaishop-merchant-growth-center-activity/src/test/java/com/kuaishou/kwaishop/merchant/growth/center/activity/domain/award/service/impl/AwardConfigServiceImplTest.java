package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.api.client.util.Lists;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.converter.AwardConfigConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.platform.UserStepAwardInfoBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.AwardDynamicValue;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.service.DynamicValueServiceI;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.AwardConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

class AwardConfigServiceImplTest {
    @Mock
    AwardConfigDAO awardConfigDAO;
    @Mock
    UserAwardRecordDAO userAwardRecordDAO;
    @Mock
    AwardConfigConverter awardConfigConverter;
    @Mock
    DynamicValueServiceI dynamicValueService;
    @InjectMocks
    AwardConfigServiceImpl awardConfigServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetUserPlatformAwardConfig() {
        long userId = 2176580531L;
        long activityId = 12710L;
        String taskListStr = "[{\"id\":8249635,\"createTime\":1713250657101,\"updateTime\":1713250657101," +
                "\"creator\":\"sunhuzeng\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":0," +
                "\"activityId\":12710,\"name\":\"11\",\"type\":1,\"description\":null,\"parentTask\":0,\"preTask\":0," +
                "\"priority\":1,\"stage\":1,\"periodType\":1,\"periodDay\":null,\"startTime\":1713110400000," +
                "\"endTime\":1713715199999,\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":2," +
                "\\\"config\\\":null}\",\"viewConfig\":null,\"alias\":\"12710020000000001\",\"status\":20," +
                "\"ext\":\"{\\\"remark\\\": \\\"规则1\\\", \\\"stepNum\\\": 1, \\\"delayTime\\\": 0, \\\"inputType\\\":" +
                " 2, \\\"cycleConfig\\\": {\\\"cycleType\\\": 1}, \\\"relatedFlag\\\": true, \\\"taskTimeType\\\": 1," +
                " \\\"basicConfigBO\\\": {\\\"fixedEndTime\\\": 1711814400000, \\\"baseAlgorithm\\\": \\\"avgDay\\\"," +
                " \\\"customizeType\\\": 0, \\\"fixedStartTime\\\": 1709222400000, \\\"hiveImportConfig\\\": " +
                "{\\\"table\\\": \\\"\\\", \\\"database\\\": \\\"\\\", \\\"crowdCondition\\\": \\\"\\\", " +
                "\\\"extraColumnList\\\": [], \\\"partitionCondition\\\": \\\"\\\", \\\"sellerIdColumnName\\\": " +
                "\\\"\\\"}, \\\"baseIndicatorList\\\": [172], \\\"indicatorTimeType\\\": \\\"customTime\\\", " +
                "\\\"baseAlgorithmCustomizeUrl\\\": \\\"\\\"}, \\\"strategyConfig\\\": {\\\"strategyId\\\": 65, " +
                "\\\"strategyVersion\\\": \\\"V1.0\\\"}}\",\"crowdType\":8,\"crowdConfig\":\"{\\\"crowdType\\\":8," +
                "\\\"strategyConfig\\\":{\\\"strategyId\\\":65,\\\"strategyVersion\\\":\\\"V1.0\\\"}}\"," +
                "\"resourceRuleId\":0,\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":null},{\"id\":8249636,\"createTime\":1713250657105,\"updateTime\":1713250657105," +
                "\"creator\":\"sunhuzeng\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":1," +
                "\"activityId\":12710,\"name\":\"11周期1子任务\",\"type\":1,\"description\":null,\"parentTask\":8249635," +
                "\"preTask\":0,\"priority\":1,\"stage\":1,\"periodType\":1,\"periodDay\":null," +
                "\"startTime\":1713110400000,\"endTime\":1713715199999," +
                "\"completeCondition\":\"{\\\"conditionType\\\":1,\\\"entityType\\\":3,\\\"config\\\":null}\"," +
                "\"viewConfig\":null,\"alias\":\"12710020000000002\",\"status\":20,\"ext\":\"{\\\"stepNum\\\": 1, " +
                "\\\"delayTime\\\": 0}\",\"crowdType\":0,\"crowdConfig\":null,\"resourceRuleId\":16714," +
                "\"externalId\":null,\"externalSource\":null,\"tags\":\"\"," +
                "\"awardCondition\":\"{\\\"conditionType\\\": 2}\"}]";
        List<TaskDO> mockTaskList = ObjectMapperUtils.fromJSON(taskListStr, List.class, TaskDO.class);
        String indicatorMapStr = "{\"172\":{\"id\":172,\"createTime\":1680198845713,\"updateTime\":1711424282886," +
                "\"creator\":\"wangshouchang\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":20," +
                "\"name\":\"风控后支付GMV(卖家）\",\"description\":\"风控后支付GMV，按real_seller_id维度统计\",\"auditTime\":604800000," +
                "\"updateType\":1,\"finishType\":2,\"closeType\":2,\"syncRisk\":0,\"status\":1," +
                "\"ext\":\"{\\\"decimal\\\": false, \\\"baseHeader\\\": \\\"卖家风控后支付GMV（基期均值）\\\", " +
                "\\\"returnTypeList\\\": null, \\\"baseDataMultiple\\\": \\\"100\\\", \\\"baseHeaderJsonKey\\\": " +
                "\\\"base_indicator_day_avg_172\\\", \\\"returnAwardTypeBlacklist\\\": null, " +
                "\\\"specifyIndicatorConfigBO\\\": null, \\\"activityPatternTypeConfigList\\\": null}\"," +
                "\"indicatorCondition\":\"\",\"source\":5,\"dataManageConfig\":\"{\\\"queryId\\\":10958," +
                "\\\"resCode\\\":\\\"sumUnriskActyPayOrderAmt\\\",\\\"showQueryId\\\":10649," +
                "\\\"showResCode\\\":\\\"sumPayAmt\\\",\\\"baseQueryId\\\":10959,\\\"baseResCode\\\":\\\"value\\\"," +
                "\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null,\\\"prtLatestVersion\\\":2," +
                "\\\"prodLatestVersion\\\":2,\\\"allVersion\\\":[{\\\"version\\\":1," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":10770," +
                "\\\"resCode\\\":\\\"sumUnriskActyPayOrderAmt\\\",\\\"showQueryId\\\":10649," +
                "\\\"showResCode\\\":\\\"sumPayAmt\\\",\\\"baseQueryId\\\":10688,\\\"baseResCode\\\":\\\"value\\\"," +
                "\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null},{\\\"version\\\":2," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":10958," +
                "\\\"resCode\\\":\\\"sumUnriskActyPayOrderAmt\\\",\\\"showQueryId\\\":10649," +
                "\\\"showResCode\\\":\\\"sumPayAmt\\\",\\\"baseQueryId\\\":10959,\\\"baseResCode\\\":\\\"value\\\"," +
                "\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}]}\",\"type\":1,\"calcType\":1,\"unit\":\"元\"," +
                "\"tags\":\"[\\\"targetCompleteProgress\\\", \\\"targetCompleteReturn\\\", \\\"level_riskedGmv\\\", " +
                "\\\"targetCompleteNecessary\\\", \\\"taskProgress\\\", \\\"taskReturn\\\", " +
                "\\\"showChannel_seller\\\", \\\"scene_roi\\\", \\\"showChannel_daren\\\", \\\"scene_performance\\\"," +
                " \\\"leaderboardProgress\\\", \\\"leaderboardReturn\\\", \\\"estimationMesurement\\\", " +
                "\\\"estimationIndicatorProgress\\\", \\\"estimationIndicatorSpecify\\\"]\",\"showName\":\"销售额\"," +
                "\"indicatorTip\":\"该指标实时更新仅供完成进度参考，取用户支付GMV，实际GMV进度根据平台判定有效的数据进行结算\",\"statisticsConfig\":null}}";
        Map<Long, IndicatorDO> indicatorMap = ObjectMapperUtils.fromJSON(indicatorMapStr, Map.class, Long.class,
                IndicatorDO.class);
        String userRegistrationStr = "{\"8249635\":{\"id\":823705005,\"userId\":2176580531,\"activityId\":12710," +
                "\"entityType\":\"TASK\",\"entityId\":8249635,\"status\":1,\"registrationTime\":1713251110230," +
                "\"version\":0,\"operator\":\"system\",\"source\":\"online\"," +
                "\"ext\":\"{\\\"source\\\":\\\"online\\\",\\\"quitSource\\\":null,\\\"originBasicData\\\":null," +
                "\\\"option\\\":null,\\\"riskReason\\\":null}\",\"jsonData\":\"{\\\"activityDays\\\": 7, " +
                "\\\"baseIndicatorEffectiveDays\\\": 21, \\\"base_indicator_day_avg_172\\\": \\\"100\\\", " +
                "\\\"strategyActivity_award_15_1\\\": \\\"{\\\\\\\"userId\\\\\\\":2176580531,\\\\\\\"step\\\\\\\":1," +
                "\\\\\\\"awardType\\\\\\\":15,\\\\\\\"value\\\\\\\":null," +
                "\\\\\\\"awardTargetType\\\\\\\":\\\\\\\"differentFixed\\\\\\\"," +
                "\\\\\\\"values\\\\\\\":[{\\\\\\\"rewardReturnType\\\\\\\":null," +
                "\\\\\\\"rewardReturnIndicatorId\\\\\\\":null,\\\\\\\"value\\\\\\\":\\\\\\\"200\\\\\\\"}]}\\\", " +
                "\\\"strategyActivity_indicator_172_1\\\": \\\"{\\\\\\\"userId\\\\\\\":2176580531," +
                "\\\\\\\"step\\\\\\\":1,\\\\\\\"indicatorId\\\\\\\":172,\\\\\\\"value\\\\\\\":null," +
                "\\\\\\\"indicatorTargetType\\\\\\\":\\\\\\\"differentFixed\\\\\\\"," +
                "\\\\\\\"incValue\\\\\\\":\\\\\\\"100000\\\\\\\"}\\\"}\"}}";
        Map<Long, UserRegistrationRecordBO> userRegistrationMap = ObjectMapperUtils.fromJSON(userRegistrationStr,
                Map.class, Long.class, UserRegistrationRecordBO.class);
        String awardConfigStr = "[{\"id\":12271900,\"createTime\":1713250657178,\"updateTime\":1713250657178," +
                "\"creator\":\"sunhuzeng\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":0," +
                "\"awardName\":\"奖励\",\"awardType\":15,\"activityId\":12710,\"entityId\":8249636,\"expireTime\":0," +
                "\"allotRule\":null,\"awardSubject\":\"\",\"sendRuleType\":\"task\"," +
                "\"sendRule\":\"{\\\"awardSendType\\\":6,\\\"awardCount\\\":0,\\\"interestConfigId\\\":0," +
                "\\\"awardSendRuleCode\\\":\\\"strategy_admin_award_calc\\\"," +
                "\\\"awardSendRuleConfig\\\":\\\"{\\\\\\\"awardType\\\\\\\":15," +
                "\\\\\\\"expireTimeConfig\\\\\\\":{\\\\\\\"expireTimeType\\\\\\\":2,\\\\\\\"expireDays\\\\\\\":90}," +
                "\\\\\\\"sendConfig\\\\\\\":{\\\\\\\"awardSendType\\\\\\\":1,\\\\\\\"delaySendTimeType\\\\\\\":1}," +
                "\\\\\\\"stepConfigs\\\\\\\":[{\\\\\\\"step\\\\\\\":1," +
                "\\\\\\\"type\\\\\\\":\\\\\\\"estimationStrategyCalc\\\\\\\"," +
                "\\\\\\\"value\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"maxAwardValue\\\\\\\\\\\\\\\":10000," +
                "\\\\\\\\\\\\\\\"minAwardValue\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"rateUpperLimitPercent\\\\\\\\\\\\\\\":null," +
                "\\\\\\\\\\\\\\\"specifyIndicatorInfos\\\\\\\\\\\\\\\":[]," +
                "\\\\\\\\\\\\\\\"customAeardDapFileUrl\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"https://cdnfile.corp.kuaishou" +
                ".com/kc/files/a/ks-merchant-inner/strategy_estimation_65-V1-01713248720220" +
                ".xlsx\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"awardType\\\\\\\":null,\\\\\\\"uniqueKey\\\\\\\":null}]}\\\"," +
                "\\\"awardCalType\\\":1,\\\"awardRuleInput\\\":[\\\"gmvFullAperture\\\",\\\"strategyAdminBase\\\"]," +
                "\\\"isAwardDeduct\\\":0,\\\"awardMathConfig\\\":null,\\\"ext\\\":null,\\\"maxAwardCount\\\":0}\"," +
                "\"interestPackageId\":null,\"interestConfigId\":0,\"interestExtParam\":null,\"status\":1," +
                "\"delayCalcRule\":null}]";
        List<AwardConfigDO> awardConfigDOList = ObjectMapperUtils.fromJSON(awardConfigStr, List.class,
                AwardConfigDO.class);
        when(awardConfigDAO.queryMultiTaskAwardConfig(anyLong(), any())).thenReturn(awardConfigDOList);
        when(userAwardRecordDAO.listUserActivityRecord(anyLong(), anyLong(), anyBoolean())).thenReturn(Lists.newArrayList());
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());
        String awardDynamicValueStr = "{\"result\":1,\"message\":\"成功\",\"awardValue\":200," +
                "\"componentValues\":{\"maxAwardValue\":10000,\"minAwardValue\":0," +
                "\"stepAssignKey\":\"strategyActivity_award_15_1\",\"awardMode\":7,\"fixedAward\":200}}";
        AwardDynamicValue awardDynamicValue = ObjectMapperUtils.fromJSON(awardDynamicValueStr, AwardDynamicValue.class);
        when(dynamicValueService.getRuleFixedAwardValue(anyLong(), any(), any(), anyInt(), any())).thenReturn(awardDynamicValue);

        List<UserStepAwardInfoBO> result = awardConfigServiceImpl.getUserPlatformAwardConfig(userId, activityId,
                mockTaskList, indicatorMap, userRegistrationMap);
        result.forEach(userStepAwardInfoBO -> {
            Assertions.assertEquals("differentFixed", userStepAwardInfoBO.getType());
            Assertions.assertEquals(200, userStepAwardInfoBO.getFixedAwardValue());
        });
    }

}