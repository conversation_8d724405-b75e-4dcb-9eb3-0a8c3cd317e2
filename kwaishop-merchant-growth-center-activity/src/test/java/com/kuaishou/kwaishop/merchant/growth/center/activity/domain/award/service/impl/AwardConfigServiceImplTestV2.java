package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.AwardConfigService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-11
 */
@RunWith(MockitoJUnitRunner.class)
class AwardConfigServiceImplTestV2 {

    @InjectMocks
    private AwardConfigService awardConfigService;

    @Test
    void getUserPlatformAwardConfig() {

    }
}