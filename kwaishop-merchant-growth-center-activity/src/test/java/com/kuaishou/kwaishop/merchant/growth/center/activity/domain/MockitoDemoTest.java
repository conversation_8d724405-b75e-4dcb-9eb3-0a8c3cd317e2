package com.kuaishou.kwaishop.merchant.growth.center.activity.domain;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Maps;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.converter.AwardConfigConverter;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.model.bo.AwardConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award.service.impl.AwardConfigServiceImpl;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.model.bo.AwardDynamicValue;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.common.dynamic.service.DynamicValueServiceI;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.registration.model.bo.UserRegistrationRecordBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.AwardConfigDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.award.UserAwardRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.AwardConfigDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.award.UserAwardRecordDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.task.TaskDO;

/**
 * JUnit5常用注解
 * 1. @Test 将方法标识为测试方法。
 * 2. @BeforeEach 在每次测试之前执行。用于准备测试环境，例如初始化测试类中的字段、配置环境等。
 * 3. @AfterEach 每次测试后执行。用于清理测试环境，例如删除临时数据、恢复默认值、清理昂贵的内存结构。
 * 4. @BeforeAll 注释在所有测试开始之前执行一次的静态方法。它用于执行时间密集型活动，例如连接到数据库。需要定义标有此注释的方法才能static与 JUnit 一起使用。
 * 5. @AfterAll 注释一个静态方法，该方法在所有测试完成后执行一次。它用于执行清理活动，例如，断开与数据库的连接。使用此注释注释的方法需要定义为static与 JUnit 一起使用。
 * 6. @Tag("<TagName>") 标记一个测试方法，JUnit 5 中的测试可以通过标记进行过滤。例如，仅运行标有“快速”的测试。
 * 7. @DisplayName("<Name>")    <Name> 将由测试运行器显示。与方法名称相反，名称可以包含空格以提高可读性。
 * 8. @RepeatedTest(<Number>) 类似于@Test但重复测试 <Number> 次
 * 9. @Disabled("reason") 禁用具有选项原因的测试方法。
 * 10. @ExtendWith 允许您注册一个扩展类，为测试添加功能
 *
 * mockito常用注解&方法
 * 1. mock：mock整个对象，对函数调用都使用mock的方法，除非显示的调用doCallRealMethod()
 * 2. spy：mock部分对象，对函数的调用均执行真正的方法，除了使用doXxx或者thenXxx的部分
 * 3. thenReturn：在执行到when()中的方法后，返回一个对象。该对象需和目标类方法中的声明类型一致
 * 4. thenThrow：在执行到when()中的方法后，抛出一个异常。
 * 5. thenAnswer / then：在执行到when()中的方法后，执行一段代码，该代码封装在Answer<T>接口中。
 * 6. thenCallRealMethod：对when()中的代码正常执行
 * 7. doReturn：在执行到when()中的方法前，返回一个对象。该对象需和目标类方法中的声明类型一致
 * 8. doThrow：在执行到when()中的方法前，抛出一个异常
 * 9. doAnswer：在执行到when()中的方法前，执行一段代码，该代码封装在Answer<T>接口中
 * 10. doCallRealMethod：对when()中的代码正常执行
 * 11.  verify：可以校验 mock 对象是否发生过某些操作，配合 time 方法，可以校验某些操作发生的次数
 * 12. anyInt(),anyLong(),anyString(),anyList()：mock对象调用函数的参数匹配
 *
 * 详细mockito使用文档参考：https://github.com/hehonghui/mockito-doc-zh#0
 */
class MockitoDemoTest {

    @Mock
    AwardConfigDAO awardConfigDAO;
    @Mock
    UserAwardRecordDAO userAwardRecordDAO;
    @Mock
    AwardConfigConverter awardConfigConverter;
    @Mock
    DynamicValueServiceI dynamicValueService;
    @InjectMocks
    AwardConfigServiceImpl awardConfigServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testGetMultiTaskAwardConfig() {
        when(awardConfigDAO.queryMultiTaskAwardConfig(anyLong(), any())).thenReturn(List.of(new AwardConfigDO()));
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());

        List<AwardConfigBO> result = awardConfigServiceImpl.getMultiTaskAwardConfig(0L, List.of(Long.valueOf(1)));
        Assertions.assertEquals(List.of(new AwardConfigBO()), result);
    }

    @Test
    void testGetMultiUserTaskAwardConfig() {
        when(awardConfigDAO.queryMultiTaskAwardConfig(anyLong(), any())).thenReturn(List.of(new AwardConfigDO()));
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());
        when(dynamicValueService.batchGetAwardValue(anyLong(), anyLong(), any())).thenReturn(Map.of(Long.valueOf(1),
                new AwardDynamicValue()));
        when(dynamicValueService.batchGetReturnAwardValue(anyLong(), anyLong(), any())).thenReturn(Map.of(Long.valueOf(1), Long.valueOf(1)));

//        List<AwardConfigBO> result = awardConfigServiceImpl.getMultiUserTaskAwardConfig(0L, 0L,
//                List.of(Long.valueOf(1)));
//        Assertions.assertEquals(List.of(new AwardConfigBO()), result);
    }


    @Test
    void testGetUserPlatformAwardConfig() {
        long userId = 2176580531L;
        long activityId = 12694L;
        // mock入参
        List<TaskDO> mockTaskList = Lists.newArrayList();
        Map<Long, IndicatorDO> indicatorMap = Maps.newHashMap();
        Map<Long, UserRegistrationRecordBO> userRegistrationMap = Maps.newHashMap();
        // mock内部分支调用
        when(awardConfigDAO.queryMultiTaskAwardConfig(anyLong(), any())).thenReturn(List.of(new AwardConfigDO()));
        when(userAwardRecordDAO.listUserActivityRecord(anyLong(), anyLong(), anyBoolean())).thenReturn(List.of(new UserAwardRecordDO()));
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());
        when(dynamicValueService.getRuleFixedAwardValue(anyLong(), any(), any(), anyInt(), any())).thenReturn(new AwardDynamicValue());

//        List<UserStepAwardInfoBO> result = awardConfigServiceImpl.getUserPlatformAwardConfig(userId, activityId,
//                mockTaskList, indicatorMap, userRegistrationMap);
//        // 增量代码覆盖断言
//        Assertions.assertEquals(List.of(new UserStepAwardInfoBO()), result);
    }

    @Test
    void testQueryAwardConfigById() {
        when(awardConfigDAO.queryById(anyLong())).thenReturn(new AwardConfigDO());
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());

        AwardConfigBO result = awardConfigServiceImpl.queryAwardConfigById(0L);
        Assertions.assertEquals(new AwardConfigBO(), result);
    }

    @Test
    void testGetAwardConfigList() {
        when(awardConfigDAO.queryAwardConfig(anyLong(), anyLong())).thenReturn(List.of(new AwardConfigDO()));
        when(awardConfigConverter.convertToAwardConfigBO(any())).thenReturn(new AwardConfigBO());

        List<AwardConfigBO> result = awardConfigServiceImpl.getAwardConfigList(0L, 0L);
        Assertions.assertEquals(List.of(new AwardConfigBO()), result);
    }

//    @Test
//    void testIsAwardConfigLottery() {
//        Boolean result = awardConfigServiceImpl.isAwardConfigLottery(new AwardConfigBO());
//        Assertions.assertEquals(Boolean.TRUE, result);
//    }
}