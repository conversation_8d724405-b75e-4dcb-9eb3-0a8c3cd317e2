package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.CollectionUtils;

import com.kuaishou.kwaishop.delivery.horserace.service.client.HorseRaceClient;
import com.kuaishou.kwaishop.sic.service.client.client.KwaishopSelectionItemClient;
import com.kuaishou.kwaishop.sic.service.protobuf.ProfileDetail;
import com.kuaishou.kwaishop.sic.service.protobuf.SelectionBaseResponseInfo;
import com.kuaishou.kwaishop.sic.service.protobuf.SelectionGroupFetchRequest;
import com.kuaishou.kwaishop.sic.service.protobuf.SelectionGroupFetchResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
class DeliveryFetchServiceImplTest {
    @Mock
    HorseRaceClient horseRaceClient;

    @Mock
    KwaishopSelectionItemClient kwaishopSelectionItemClient;
    @InjectMocks
    DeliveryFetchServiceImpl deliveryFetchServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testQueryAllGroupUserInSingleTrace() {
        String str = "[{\"id\":111928263,\"selectionGroupType\":\"SELLER_TYPE\",\"profileMap\":{\"item_type\":\"2\"," +
                "\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\",\"selection_item_source\":\"0\"}," +
                "\"selectionGroupId\":120997},{\"id\":2171880196,\"selectionGroupType\":\"SELLER_TYPE\"," +
                "\"profileMap\":{\"item_type\":\"2\",\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\"," +
                "\"selection_item_source\":\"0\"},\"selectionGroupId\":120997},{\"id\":2172552905," +
                "\"selectionGroupType\":\"SELLER_TYPE\",\"profileMap\":{\"item_type\":\"2\"," +
                "\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\",\"selection_item_source\":\"0\"}," +
                "\"selectionGroupId\":120997},{\"id\":2174813395,\"selectionGroupType\":\"SELLER_TYPE\"," +
                "\"profileMap\":{\"item_type\":\"2\",\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\"," +
                "\"selection_item_source\":\"0\"},\"selectionGroupId\":120997},{\"id\":2178768663," +
                "\"selectionGroupType\":\"SELLER_TYPE\",\"profileMap\":{\"item_type\":\"2\"," +
                "\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\",\"selection_item_source\":\"0\"}," +
                "\"selectionGroupId\":120997},{\"id\":2181456310,\"selectionGroupType\":\"SELLER_TYPE\"," +
                "\"profileMap\":{\"item_type\":\"2\",\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\"," +
                "\"selection_item_source\":\"0\"},\"selectionGroupId\":120997},{\"id\":2181835997," +
                "\"selectionGroupType\":\"SELLER_TYPE\",\"profileMap\":{\"item_type\":\"2\"," +
                "\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\",\"selection_item_source\":\"0\"}," +
                "\"selectionGroupId\":120997},{\"id\":2194362425,\"selectionGroupType\":\"SELLER_TYPE\"," +
                "\"profileMap\":{\"item_type\":\"2\",\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\"," +
                "\"selection_item_source\":\"0\"},\"selectionGroupId\":120997},{\"id\":2195051064," +
                "\"selectionGroupType\":\"SELLER_TYPE\",\"profileMap\":{\"item_type\":\"2\"," +
                "\"rankingTrack\":\"1-全行业开门红\",\"use_reco_sort\":\"1\",\"selection_item_source\":\"0\"}," +
                "\"selectionGroupId\":120997}]";
        List<ProfileDetail> list = fromJSON(str, List.class, ProfileDetail.class);
        SelectionGroupFetchResponse groupFetchResponse = SelectionGroupFetchResponse.newBuilder()
                .setBaseRespInfo(SelectionBaseResponseInfo.newBuilder()
                        .setRespCode(1)
                        .build())
                .setSelectionGroupId(120997)
                .addAllProfileDetailList(list)
                .build();
        when(kwaishopSelectionItemClient.fetchSelectionGroupDetail(any(SelectionGroupFetchRequest.class), anyLong())).thenReturn(groupFetchResponse)
                .thenReturn(
                        SelectionGroupFetchResponse.newBuilder()
                                .setBaseRespInfo(SelectionBaseResponseInfo.newBuilder()
                                        .setRespCode(1)
                                        .build())
                                .setSelectionGroupId(120997)
                                .build()
                )
        ;

        List<Long> result = deliveryFetchServiceImpl.queryAllGroupUserInSingleTrace(120997L, 1);
        Assertions.assertTrue(CollectionUtils.hasElements(result));
    }
}
