package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.impl;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.enums.RegistrationJsonDataKeyEnum.BASE_INDICATOR_AVG_DATA;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.industry.activity.BasicIndicatorConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminActivityOnlineService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.LayerTaskBaseData;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.model.enums.BaseFormulaResultTypeEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.basicindicator.service.IndicatorBasicNewService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.UserBasicConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.EstimationStrategyAggregateRootRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.UserEstimationPrepareEntityRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.model.dos.indicator.IndicatorDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-17
 */
class UserEstimationStrategyReadServiceImplTest {
    @Mock
    EstimationCacheService estimationCacheService;
    @Mock
    UserEstimationPrepareEntityRepository prepareEntityRepository;
    @Mock
    EstimationStrategyAggregateRootRepository strategyAggregateRootRepository;
    @Mock
    AdminActivityOnlineService adminActivityOnlineService;
    @Mock
    IndicatorBasicNewService indicatorBasicNewService;
    @InjectMocks
    UserEstimationStrategyReadServiceImpl userEstimationStrategyReadServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testHandleCustomUserBasicInfo() {
        String jsonKeyBasicAvgMapStr = "{\"base_indicator_day_avg_197\":\"1\"," +
                "\"base_indicator_day_avg_172\":\"10000\"}";
        Map<String, Object> jsonKeyBasicAvgMap = ObjectMapperUtils.fromJSON(jsonKeyBasicAvgMapStr, Map.class,
                String.class, Object.class);
        String jsonKeyMapStr = "{\"197\":\"base_indicator_day_avg_197\",\"172\":\"base_indicator_day_avg_172\"}";
        Map<Long, String> jsonKeyMap = ObjectMapperUtils.fromJSON(jsonKeyMapStr, Map.class, Long.class, String.class);
        String baseIndicatorStr = "{\"197\":{\"id\":197,\"createTime\":1680233880410,\"updateTime\":1713264565047," +
                "\"creator\":\"chenyulong\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":10," +
                "\"name\":\"挂车短视频发布数\",\"description\":\"挂车短视频发布数\",\"auditTime\":86400000,\"updateType\":1,"
                +
                "\"finishType\":2,\"closeType\":0,\"syncRisk\":0,\"status\":1,\"ext\":\"{\\\"decimal\\\": false, " +
                "\\\"baseHeader\\\": \\\"挂车短视频发布数（基期均值）\\\", \\\"returnTypeList\\\": null, \\\"baseDataMultiple\\\": "
                +
                "\\\"1\\\", \\\"baseHeaderJsonKey\\\": \\\"base_indicator_day_avg_197\\\", " +
                "\\\"returnAwardTypeBlacklist\\\": null, \\\"specifyIndicatorConfigBO\\\": null, " +
                "\\\"activityPatternTypeConfigList\\\": null}\",\"indicatorCondition\":\"\",\"source\":5," +
                "\"dataManageConfig\":\"{\\\"queryId\\\":10045,\\\"resCode\\\":\\\"resCode\\\"," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"resCode\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null," +
                "\\\"prtLatestVersion\\\":1,\\\"prodLatestVersion\\\":1,\\\"allVersion\\\":[{\\\"version\\\":1," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":10045,\\\"resCode\\\":\\\"resCode\\\"," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"resCode\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}]}\"," +
                "\"type\":1,\"calcType\":1,\"unit\":\"次\",\"tags\":\"[\\\"targetCompleteProgress\\\", " +
                "\\\"fansRiseProgress\\\", \\\"taskProgress\\\", \\\"level_video\\\", \\\"10010\\\", " +
                "\\\"showChannel_seller\\\", \\\"showChannel_daren\\\", \\\"goodsMatchProcess\\\", " +
                "\\\"scene_performance\\\", \\\"estimationIndicatorProgress\\\"]\",\"showName\":\"挂车短视频发布数\"," +
                "\"indicatorTip\":\"短视频需最晚在发布第二天24点之前挂车且公开并在线上保留24小时以上\",\"statisticsConfig\":null},"
                +
                "\"172\":{\"id\":172,\"createTime\":1708915599707,\"updateTime\":1718891906164," +
                "\"creator\":\"wuyouling\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":6," +
                "\"name\":\"直播间上架商品数\",\"description\":\"选定时间范围内，账号开播期间，归属于uid下的直播间内加过小黄车的商品总数\","
                +
                "\"auditTime\":86400000,\"updateType\":1,\"finishType\":2,\"closeType\":2,\"syncRisk\":0," +
                "\"status\":1,\"ext\":null,\"indicatorCondition\":\"\",\"source\":5," +
                "\"dataManageConfig\":\"{\\\"queryId\\\":null,\\\"resCode\\\":null,\\\"showQueryId\\\":null," +
                "\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045,\\\"baseResCode\\\":\\\"photo_cnt\\\"," +
                "\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null,\\\"prtLatestVersion\\\":2," +
                "\\\"prodLatestVersion\\\":2,\\\"allVersion\\\":[{\\\"version\\\":1," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":null,\\\"resCode\\\":null," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":11075," +
                "\\\"baseResCode\\\":\\\"value\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}," +
                "{\\\"version\\\":2,\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":null,\\\"resCode\\\":null," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"photo_cnt\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}]}\"," +
                "\"type\":1,\"calcType\":1,\"unit\":\"个\",\"tags\":\"[\\\"targetCompleteProgress\\\", " +
                "\\\"taskProgress\\\", \\\"level_live\\\", \\\"showChannel_seller\\\", " +
                "\\\"estimationIndicatorProgress\\\"]\",\"showName\":\"直播间上架商品数\",\"indicatorTip\":\"\"," +
                "\"statisticsConfig\":null}}";
        Map<Long, IndicatorDO> baseIndicatorMap = ObjectMapperUtils.fromJSON(baseIndicatorStr, Map.class, Long.class,
                IndicatorDO.class);
        Integer activityDays = 7;
        List<BasicIndicatorConfigBO> basicIndicatorConfigList = Lists.newArrayList();
        BasicIndicatorConfigBO basicIndicatorConfigBO = new BasicIndicatorConfigBO();
        basicIndicatorConfigBO.setIndicatorId(197L);
        basicIndicatorConfigList.add(basicIndicatorConfigBO);
        BasicIndicatorConfigBO basicIndicatorConfigBO2 = new BasicIndicatorConfigBO();
        basicIndicatorConfigBO.setIndicatorId(172L);
        basicIndicatorConfigList.add(basicIndicatorConfigBO2);
        List<UserBasicConfigBO.UserIndicatorBasicConfig> indicatorBasicConfigList =
                userEstimationStrategyReadServiceImpl.handleCustomUserBasicInfo(jsonKeyBasicAvgMap, jsonKeyMap,
                        baseIndicatorMap, activityDays, basicIndicatorConfigList);
        //Assertions.assertTrue(CollectionUtils.isNotEmpty(indicatorBasicConfigList) && indicatorBasicConfigList
        // .stream().noneMatch(config -> config.getHighAccuracyBasicAvg() == null));
    }

    @Test
    void testGetUserIndicatorBasicInfoFromDM() {
        Map<Long, LayerTaskBaseData> dmMap = Maps.newHashMap();
        Map<String, Object> indicatorMap = Maps.newHashMap();
        indicatorMap.put(BASE_INDICATOR_AVG_DATA.format(197L), "2.2");
        LayerTaskBaseData layerTaskBaseData1 =
                LayerTaskBaseData.builder().type(BaseFormulaResultTypeEnum.AVG).baseDataMap(indicatorMap).build();
        indicatorMap.put(BASE_INDICATOR_AVG_DATA.format(377L), "4.1");
        LayerTaskBaseData layerTaskBaseData2 =
                LayerTaskBaseData.builder().type(BaseFormulaResultTypeEnum.AVG).baseDataMap(indicatorMap).build();
        dmMap.put(197L, layerTaskBaseData1);
        dmMap.put(377L, layerTaskBaseData2);
        when(indicatorBasicNewService.queryUserBasicIndicatorDataFromDMForDap(anyLong(), any(BasicConfigBO.class),
                any(), anyMap()))
                .thenReturn(dmMap);
        Long userId = 2181835997L;
        String basicConfigStr = "{\"basicIndicatorConfigList\":[{\"indicatorId\":197," +
                "\"basicValueType\":\"SYSTEM_CALC\",\"basicCustomConfig\":null," +
                "\"basicSystemCalcConfig\":{\"basicFactorConfigList\":[{\"factorCode\":\"basic_1\"," +
                "\"baseAlgorithm\":\"AVG_DAY\",\"indicatorTimeType\":\"CUSTOM_TIME\"," +
                "\"baseAlgorithmCustomizeUrl\":null,\"customizeType\":null,\"hiveImportConfig\":null," +
                "\"fixedStartTime\":1717171200000,\"fixedEndTime\":1719590400000,\"offsetEventType\":null," +
                "\"relativeTime\":null,\"periodTime\":null,\"coefficient\":1,\"exclusiveFestivalDays\":true," +
                "\"exclusiveMaxMinDays\":true,\"exclusiveInvalidLiveDays\":false}],\"basicCalcType\":null," +
                "\"fixedMinValue\":null,\"fixedMaxValue\":null}},{\"indicatorId\":377," +
                "\"basicValueType\":\"SYSTEM_CALC\",\"basicCustomConfig\":null," +
                "\"basicSystemCalcConfig\":{\"basicFactorConfigList\":[{\"factorCode\":\"basic_1\"," +
                "\"baseAlgorithm\":\"AVG_DAY\",\"indicatorTimeType\":\"CUSTOM_TIME\"," +
                "\"baseAlgorithmCustomizeUrl\":null,\"customizeType\":null,\"hiveImportConfig\":null," +
                "\"fixedStartTime\":1717171200000,\"fixedEndTime\":1719590400000,\"offsetEventType\":null," +
                "\"relativeTime\":null,\"periodTime\":null,\"coefficient\":1,\"exclusiveFestivalDays\":true," +
                "\"exclusiveMaxMinDays\":true,\"exclusiveInvalidLiveDays\":false}],\"basicCalcType\":null," +
                "\"fixedMinValue\":null,\"fixedMaxValue\":null}}],\"baseIndicatorList\":[197,377]}";
        BasicConfigBO basicConfigBO = ObjectMapperUtils.fromJSON(basicConfigStr, BasicConfigBO.class);
        String baseIndicatorStr = "{\"197\":{\"id\":197,\"createTime\":1680233880410,\"updateTime\":1713264565047," +
                "\"creator\":\"chenyulong\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":10," +
                "\"name\":\"挂车短视频发布数\",\"description\":\"挂车短视频发布数\",\"auditTime\":86400000,\"updateType\":1,"
                +
                "\"finishType\":2,\"closeType\":0,\"syncRisk\":0,\"status\":1,\"ext\":\"{\\\"decimal\\\": false, " +
                "\\\"baseHeader\\\": \\\"挂车短视频发布数（基期均值）\\\", \\\"returnTypeList\\\": null, \\\"baseDataMultiple\\\": "
                +
                "\\\"1\\\", \\\"baseHeaderJsonKey\\\": \\\"base_indicator_day_avg_197\\\", " +
                "\\\"returnAwardTypeBlacklist\\\": null, \\\"specifyIndicatorConfigBO\\\": null, " +
                "\\\"activityPatternTypeConfigList\\\": null}\",\"indicatorCondition\":\"\",\"source\":5," +
                "\"dataManageConfig\":\"{\\\"queryId\\\":10045,\\\"resCode\\\":\\\"resCode\\\"," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"resCode\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null," +
                "\\\"prtLatestVersion\\\":1,\\\"prodLatestVersion\\\":1,\\\"allVersion\\\":[{\\\"version\\\":1," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":10045,\\\"resCode\\\":\\\"resCode\\\"," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"resCode\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}]}\"," +
                "\"type\":1,\"calcType\":1,\"unit\":\"次\",\"tags\":\"[\\\"targetCompleteProgress\\\", " +
                "\\\"fansRiseProgress\\\", \\\"taskProgress\\\", \\\"level_video\\\", \\\"10010\\\", " +
                "\\\"showChannel_seller\\\", \\\"showChannel_daren\\\", \\\"goodsMatchProcess\\\", " +
                "\\\"scene_performance\\\", \\\"estimationIndicatorProgress\\\"]\",\"showName\":\"挂车短视频发布数\"," +
                "\"indicatorTip\":\"短视频需最晚在发布第二天24点之前挂车且公开并在线上保留24小时以上\",\"statisticsConfig\":null},"
                +
                "\"377\":{\"id\":377,\"createTime\":1708915599707,\"updateTime\":1718891906164," +
                "\"creator\":\"wuyouling\",\"modifier\":\"sunhuzeng\",\"deleted\":0,\"version\":6," +
                "\"name\":\"直播间上架商品数\",\"description\":\"选定时间范围内，账号开播期间，归属于uid下的直播间内加过小黄车的商品总数\","
                +
                "\"auditTime\":86400000,\"updateType\":1,\"finishType\":2,\"closeType\":2,\"syncRisk\":0," +
                "\"status\":1,\"ext\":null,\"indicatorCondition\":\"\",\"source\":5," +
                "\"dataManageConfig\":\"{\\\"queryId\\\":null,\\\"resCode\\\":null,\\\"showQueryId\\\":null," +
                "\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045,\\\"baseResCode\\\":\\\"photo_cnt\\\"," +
                "\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null,\\\"prtLatestVersion\\\":2," +
                "\\\"prodLatestVersion\\\":2,\\\"allVersion\\\":[{\\\"version\\\":1," +
                "\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":null,\\\"resCode\\\":null," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":11075," +
                "\\\"baseResCode\\\":\\\"value\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}," +
                "{\\\"version\\\":2,\\\"status\\\":\\\"PROD_ONLINE\\\",\\\"queryId\\\":null,\\\"resCode\\\":null," +
                "\\\"showQueryId\\\":null,\\\"showResCode\\\":null,\\\"baseQueryId\\\":10045," +
                "\\\"baseResCode\\\":\\\"photo_cnt\\\",\\\"dm2ServiceCode\\\":null,\\\"dm2ResCode\\\":null}]}\"," +
                "\"type\":1,\"calcType\":1,\"unit\":\"个\",\"tags\":\"[\\\"targetCompleteProgress\\\", " +
                "\\\"taskProgress\\\", \\\"level_live\\\", \\\"showChannel_seller\\\", " +
                "\\\"estimationIndicatorProgress\\\"]\",\"showName\":\"直播间上架商品数\",\"indicatorTip\":\"\"," +
                "\"statisticsConfig\":null}}";
        Map<Long, IndicatorDO> baseIndicatorMap = ObjectMapperUtils.fromJSON(baseIndicatorStr, Map.class, Long.class,
                IndicatorDO.class);
        Integer activityDays = 7;
        List<Long> baseIndicatorList = Lists.newArrayList();
        baseIndicatorList.add(197L);
        baseIndicatorList.add(377L);
        List<UserBasicConfigBO.UserIndicatorBasicConfig> indicatorBasicConfigList =
                userEstimationStrategyReadServiceImpl.getUserIndicatorBasicInfoFromDM(userId, basicConfigBO,
                        baseIndicatorMap, activityDays, baseIndicatorList, null);
//        Assertions.assertTrue(CollectionUtils.isNotEmpty(indicatorBasicConfigList)
//                && indicatorBasicConfigList.stream().noneMatch(config -> config.getHighAccuracyBasicAvg() == null));
    }
}