package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.AdminEstimationKimService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.ProtocolCheckService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyLinkService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.EstimationStrategyReadService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.entity.EstimationStrategyGroupAggregateRoot;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.StrategyGroupStatusEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.EstimationStrategyAggregateRootRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.StrategyGroupAggregateRootRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.repository.StrategySnapshotEntityRepository;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.service.EstimationStrategyDomainService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.DapEstimationFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.LongTaskFetchService;
import com.kuaishou.kwaishop.merchant.growth.center.protobuf.activity.estimation.EstimationStrategyGroupModifyRequest;

class EstimationStrategyWriteServiceImplTest {
    @Mock
    EstimationStrategyReadService estimationStrategyReadService;
    @Mock
    EstimationStrategyDomainService estimationStrategyDomainService;
    @Mock
    StrategyGroupAggregateRootRepository groupEntityRepository;
    @Mock
    EstimationStrategyAggregateRootRepository strategyAggregateRootRepository;
    @Mock
    ProtocolCheckService checkService;
    @Mock
    LongTaskFetchService longTaskFetchService;
    @Mock
    DapEstimationFetchService dapEstimationFetchService;
    @Mock
    StrategySnapshotEntityRepository strategySnapshotEntityRepository;
    @Mock
    EstimationStrategyLinkService estimationStrategyLinkService;
    @Mock
    AdminEstimationKimService adminEstimationKimService;
    @Mock
    StrategyGroupAggregateRootRepository strategyGroupAggregateRootRepository;
    @InjectMocks
    EstimationStrategyWriteServiceImpl estimationStrategyWriteServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    void testModifyGroup() {
        String reqJson = "{\n" +
                "    \"saveType\": 1,\n" +
                "    \"operator\": \"sunhuzeng\",\n" +
                "    \"groupConfig\": {\n" +
                "        \"goalType\": 1,\n" +
                "        \"measurementConfig\": [\n" +
                "            {\n" +
                "                \"indexId\": 172,\n" +
                "                \"indexName\": \"风控后支付GMV(卖家）\",\n" +
                "                \"indexValue\": \"1000000\",\n" +
                "                \"unit\": \"元\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"indexId\": 283,\n" +
                "                \"indexName\": \"商业化投流\",\n" +
                "                \"indexValue\": \"10000\",\n" +
                "                \"unit\": \"元\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"strategyPeriod\": 14,\n" +
                "        \"projectRoiThreshold\": \"1\",\n" +
                "        \"singleRoiThreshold\": \"0.8\",\n" +
                "        \"enableBudgetAllocation\": 1,\n" +
                "        \"settleRate\": \"0.7\",\n" +
                "        \"trRate\": \"0.04\",\n" +
                "        \"stageNum\": 3,\n" +
                "        \"indicatorAssessmentType\": 2,\n" +
                "        \"singleIndicatorMaxNum\": 3,\n" +
                "        \"indicatorConfigs\": [\n" +
                "            {\n" +
                "                \"indicatorId\": 172,\n" +
                "                \"indicatorName\": \"风控后支付GMV(卖家）\",\n" +
                "                \"valueMin\": \"10000\",\n" +
                "                \"valueMax\": \"\",\n" +
                "                \"unit\": \"元\",\n" +
                "                \"modifyType\": 0,\n" +
                "                \"modifyValue\": \"\",\n" +
                "                \"order\": 0,\n" +
                "                \"indicatorTags\": [\n" +
                "                    \"targetCompleteProgress\",\n" +
                "                    \"targetCompleteReturn\",\n" +
                "                    \"level_riskedGmv\",\n" +
                "                    \"targetCompleteNecessary\",\n" +
                "                    \"taskProgress\",\n" +
                "                    \"taskReturn\",\n" +
                "                    \"showChannel_seller\",\n" +
                "                    \"scene_roi\",\n" +
                "                    \"showChannel_daren\",\n" +
                "                    \"scene_performance\",\n" +
                "                    \"leaderboardProgress\",\n" +
                "                    \"leaderboardReturn\",\n" +
                "                    \"estimationMesurement\",\n" +
                "                    \"estimationIndicatorProgress\",\n" +
                "                    \"estimationIndicatorSpecify\"\n" +
                "                ],\n" +
                "                \"required\": true\n" +
                "            },\n" +
                "            {\n" +
                "                \"indicatorId\": 382,\n" +
                "                \"indicatorName\": \"风控后图文渠道支付GMV（卖家）\",\n" +
                "                \"valueMin\": \"1000\",\n" +
                "                \"valueMax\": \"\",\n" +
                "                \"unit\": \"元\",\n" +
                "                \"modifyType\": 0,\n" +
                "                \"modifyValue\": \"\",\n" +
                "                \"order\": 0,\n" +
                "                \"indicatorTags\": [\n" +
                "                    \"level_riskedGmv\",\n" +
                "                    \"targetCompleteProgress\",\n" +
                "                    \"targetCompleteReturn\",\n" +
                "                    \"taskProgress\",\n" +
                "                    \"taskReturn\",\n" +
                "                    \"targetCompleteNecessary\",\n" +
                "                    \"showChannel_daren\",\n" +
                "                    \"scene_performance\",\n" +
                "                    \"scene_roi\",\n" +
                "                    \"leaderboardProgress\",\n" +
                "                    \"leaderboardReturn\",\n" +
                "                    \"estimationIndicatorProgress\",\n" +
                "                    \"showChannel_seller\"\n" +
                "                ],\n" +
                "                \"required\": false\n" +
                "            },\n" +
                "            {\n" +
                "                \"indicatorId\": 283,\n" +
                "                \"indicatorName\": \"商业化投流\",\n" +
                "                \"valueMin\": \"1000\",\n" +
                "                \"valueMax\": \"\",\n" +
                "                \"unit\": \"元\",\n" +
                "                \"modifyType\": 0,\n" +
                "                \"modifyValue\": \"\",\n" +
                "                \"order\": 0,\n" +
                "                \"indicatorTags\": [\n" +
                "                    \"showChannel_seller\",\n" +
                "                    \"showChannel_daren\",\n" +
                "                    \"scene_roi\",\n" +
                "                    \"taskProgress\",\n" +
                "                    \"taskReturn\",\n" +
                "                    \"level_business\",\n" +
                "                    \"targetCompleteProgress\",\n" +
                "                    \"targetCompleteReturn\",\n" +
                "                    \"estimationIndicatorProgress\",\n" +
                "                    \"estimationIndicatorSpecify\",\n" +
                "                    \"estimationMesurement\"\n" +
                "                ],\n" +
                "                \"required\": false\n" +
                "            },\n" +
                "            {\n" +
                "                \"indicatorId\": 177,\n" +
                "                \"indicatorName\": \"新粉数量\",\n" +
                "                \"valueMin\": \"100\",\n" +
                "                \"valueMax\": \"\",\n" +
                "                \"unit\": \"个\",\n" +
                "                \"modifyType\": 0,\n" +
                "                \"modifyValue\": \"\",\n" +
                "                \"order\": 0,\n" +
                "                \"indicatorTags\": [\n" +
                "                    \"fansRiseNecessary\",\n" +
                "                    \"level_fans\",\n" +
                "                    \"taskProgress\",\n" +
                "                    \"showChannel_seller\",\n" +
                "                    \"showChannel_daren\",\n" +
                "                    \"scene_roi\",\n" +
                "                    \"fansRiseReturn\",\n" +
                "                    \"estimationIndicatorProgress\",\n" +
                "                    \"targetCompleteAwardUnitPriceCalc\",\n" +
                "                    \"taskAwardUnitPriceCalc\",\n" +
                "                    \"targetCompleteProgress\"\n" +
                "                ],\n" +
                "                \"required\": false\n" +
                "            },\n" +
                "            {\n" +
                "                \"indicatorId\": 200,\n" +
                "                \"indicatorName\": \"累计推流时长（挂车）（分钟）\",\n" +
                "                \"valueMin\": \"120\",\n" +
                "                \"valueMax\": \"\",\n" +
                "                \"unit\": \"分钟\",\n" +
                "                \"modifyType\": 0,\n" +
                "                \"modifyValue\": \"\",\n" +
                "                \"order\": 0,\n" +
                "                \"indicatorTags\": [\n" +
                "                    \"targetCompleteProgress\",\n" +
                "                    \"fansRiseProgress\",\n" +
                "                    \"taskProgress\",\n" +
                "                    \"level_live\",\n" +
                "                    \"showChannel_seller\",\n" +
                "                    \"showChannel_daren\",\n" +
                "                    \"goodsMatchProcess\",\n" +
                "                    \"scene_performance\",\n" +
                "                    \"estimationIndicatorProgress\"\n" +
                "                ],\n" +
                "                \"required\": false\n" +
                "            }\n" +
                "        ],\n" +
                "        \"awardConfigs\": {\n" +
                "            \"awardCondition\": 2,\n" +
                "            \"singleAwardConfig\": [\n" +
                "                {\n" +
                "                    \"awardType\": 4,\n" +
                "                    \"awardName\": \"磁力金牛PC端\",\n" +
                "                    \"targetType\": \"incrSpecifyIndicatorReturn\",\n" +
                "                    \"minAwardValue\": \"\",\n" +
                "                    \"maxAwardValue\": \"10000\",\n" +
                "                    \"unit\": \"元\",\n" +
                "                    \"specifyIndicatorInfos\": [\n" +
                "                        {\n" +
                "                            \"indicatorId\": 172,\n" +
                "                            \"indicatorName\": \"风控后支付GMV(卖家）\",\n" +
                "                            \"returnType\": 1,\n" +
                "                            \"modifyType\": 0,\n" +
                "                            \"modifyValue\": \"\",\n" +
                "                            \"order\": 0\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"modifyType\": 0,\n" +
                "                    \"modifyValue\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        },\n" +
                "        \"basicConfig\": {\n" +
                "            \"baseAlgorithm\": \"avgDay\",\n" +
                "            \"indicatorTimeType\": \"customTime\",\n" +
                "            \"fixedStartTime\": 1714492800000,\n" +
                "            \"baseIndicatorList\": [\n" +
                "                172,\n" +
                "                382,\n" +
                "                283,\n" +
                "                177,\n" +
                "                200\n" +
                "            ],\n" +
                "            \"fixedEndTime\": 1718726400000\n" +
                "        },\n" +
                "        \"crowdConfig\": {\n" +
                "            \"dynamicEventRules\": [],\n" +
                "            \"crowdType\": 3,\n" +
                "            \"crowdId\": 26608785,\n" +
                "            \"crowdFileName\": \"\",\n" +
                "            \"crowdFileUrl\": \"\",\n" +
                "            \"hiveImportConfig\": {\n" +
                "                \"database\": \"\",\n" +
                "                \"table\": \"\",\n" +
                "                \"crowdCondition\": \"\",\n" +
                "                \"sellerIdColumnName\": \"\",\n" +
                "                \"partitionCondition\": \"\",\n" +
                "                \"extraColumnList\": []\n" +
                "            },\n" +
                "            \"abExperimentId\": 36227,\n" +
                "            \"crowdFileImportType\": \"\",\n" +
                "            \"dynamicAppendCrowdType\": 0,\n" +
                "            \"fullEstimate\": true\n" +
                "        },\n" +
                "        \"matchModelId\": 1,\n" +
                "        \"matchType\": \"sellerTag\",\n" +
                "        \"groupId\": \"171\"\n" +
                "    }\n" +
                "}";
        EstimationStrategyGroupModifyRequest req = ObjectMapperUtils.fromJSON(reqJson,
                EstimationStrategyGroupModifyRequest.class);
        EstimationStrategyGroupAggregateRoot value = new EstimationStrategyGroupAggregateRoot();
        value.setStatus(StrategyGroupStatusEnum.DRAFT);
//        when(groupEntityRepository.getById(anyLong())).thenReturn(value);
//        when(strategyAggregateRootRepository.getById(anyLong())).thenReturn(new EstimationStrategyAggregateRoot());
//        when(strategySnapshotEntityRepository.getById(anyLong())).thenReturn(new EstimationStrategySnapshotEntity());
//        when(strategyGroupEntityRepository.getById(anyLong())).thenReturn(value);
        try {
            estimationStrategyWriteServiceImpl.modifyGroup(req);
        } catch (Exception e) {
        }
//        verify(estimationStrategyDomainService).modifyGroup(any());
    }
}
