package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.calcNextPeriodStartTime;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.calculateBasicRollRangeTime;

import java.util.List;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.fetch.bo.dap.EstimationSaveTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-10
 */
class EstimationVersionManagerTest {

    @Test
    void generateNextVersion() {
        EstimationVersionManager estimationVersionManager = new EstimationVersionManager();
        System.out.println(estimationVersionManager.generateNextVersion(null,
                EstimationSaveTypeEnum.INC_MAJOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.0",
                EstimationSaveTypeEnum.INC_MAJOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.3",
                EstimationSaveTypeEnum.INC_MAJOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.30",
                EstimationSaveTypeEnum.INC_MAJOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.0",
                EstimationSaveTypeEnum.INC_MINOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.3",
                EstimationSaveTypeEnum.INC_MINOR_VERSION));
        System.out.println(estimationVersionManager.generateNextVersion("V1.31",
                EstimationSaveTypeEnum.INC_MINOR_VERSION));
    }

    @Test
    void getSmallVersion() {
        System.out.println(EstimationVersionManager.getSmallVersion("V1.232"));
        System.out.println(EstimationSaveTypeEnum.judge("V1.2", EstimationSaveTypeEnum.ORIGINAL_VERSION));
        System.out.println(EstimationSaveTypeEnum.judge("V20.20", null));
        System.out.println(EstimationSaveTypeEnum.judge("V20.0", null));

    }

    @Test
    void calculateBasicRollRangeTimeTest() {
        // 25-01-01
        Long periodStartTime = 1735660800000L;
        List<Integer> periodNumList = Lists.newArrayList(7, 7, 10, 10);
//        Pair<Long, Long> fPeriodBasicPair = calculateBasicRollRangeTime(periodStartTime, periodNumList,
//                0, 3, 30L);
//        System.out.println("第一周期基期时间范围：" + fPeriodBasicPair.getLeft() + " - " + fPeriodBasicPair.getRight());

        Pair<Long, Long> secondPeriodBasicPair = calculateBasicRollRangeTime(periodStartTime, periodNumList,
                0, 3, 30L);
        // 2024-12-13 00:00:00.000 ～ 2025-01-11 00:00:00.000
        // 活动第一周期时间：25-01-01 ～25-01-07 ，第二周期开始时间：25-01-08～ 25-01-14，T-3开始测算，则01.12开始测算，基期结束时间为01.11
        System.out.println("第二周期基期时间范围：" + secondPeriodBasicPair.getLeft() + " - " + secondPeriodBasicPair.getRight());

        Pair<Long, Long> thirdPeriodBasicPair = calculateBasicRollRangeTime(periodStartTime, periodNumList,
                1, 3, 30L);
        // 2024-12-24 00:00:00.000 ～2025-01-21 00:00:00.000
        System.out.println("第三周期基期时间范围：" + thirdPeriodBasicPair.getLeft() + " - " + thirdPeriodBasicPair.getRight());

        Pair<Long, Long> forthPeriodBasicPair = calculateBasicRollRangeTime(periodStartTime, periodNumList,
                2, 3, 30L);
        // 2024-12-12 00:00:00.000 ～2025-01-11 00:00:00.000
        System.out.println("第四周期基期时间范围：" + forthPeriodBasicPair.getLeft() + " - " + forthPeriodBasicPair.getRight());

    }

    @Test
    void calcNextPeriodStartTimeTest() {
        // 25-01-01
         Long basicEndTime  = 1735920000000L;
        Long periodStartTime = calcNextPeriodStartTime(basicEndTime, 3);
        // 2025-01-08 00:00:00.000
        System.out.println(periodStartTime);
    }
}