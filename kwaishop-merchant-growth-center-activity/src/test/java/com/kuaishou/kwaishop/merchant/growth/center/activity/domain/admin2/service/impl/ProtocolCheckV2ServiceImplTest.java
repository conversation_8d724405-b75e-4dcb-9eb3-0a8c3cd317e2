package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.service.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-07
 */
class ProtocolCheckV2ServiceImplTest {

    @Test
    void isBusinessSupplyWithoutAward() {
        ProtocolCheckV2ServiceImpl service = new ProtocolCheckV2ServiceImpl();
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("normal"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("growthTask"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("growthCenter_growthTask"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("growthCenter_growthTask_award"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("promotion"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("promotion_23_818"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("horizontalPolicy"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("daren"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("portraitActivityDistributorNoAward"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("newSellerMustTask"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("strategyClassification"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("livePlan"));
        Assertions.assertFalse(service.isBusinessSupplyWithoutAward("newItemPlan"));
        Assertions.assertTrue(service.isBusinessSupplyWithoutAward("distributionRebatePolicyLongTerm"));

//        Assertions.assertThrows(BizException.class, () -> {
//            service.isBusinessSupplyWithoutAward("test");
//        });

    }
}