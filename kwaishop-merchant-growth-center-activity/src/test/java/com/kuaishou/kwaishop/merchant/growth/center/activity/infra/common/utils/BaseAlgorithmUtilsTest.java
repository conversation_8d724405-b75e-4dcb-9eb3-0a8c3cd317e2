package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-29
 */
class BaseAlgorithmUtilsTest {
//    @Test
//    void testAverageDayWithExclusion() {
//        // 测试平均日算法，排除最大最小值
//        IndicatorAlgorithmCalcParam param = new IndicatorAlgorithmCalcParam(
//                Lists.newArrayList(100L, 200L, 300L, 400L, 500L),
//                ActivityBaseAlgorithmEnum.AVG_DAY,
//                true, // 排除最大最小值
//                2 // 保留两位小数
//        );
//        double expected = new BigDecimal(900)
//                .divide(BigDecimal.valueOf(3), 2, BigDecimal.ROUND_UP)
//                .doubleValue();
//        assertEquals(expected, algorithmProcessIndicatorDataAvgByScale(param));
//    }
//
//    @Test
//    void testAverageDayWithoutExclusion() {
//        // 测试平均日算法，不排除最大最小值
//        IndicatorAlgorithmCalcParam param = new IndicatorAlgorithmCalcParam(
//                Lists.newArrayList(100L, 200L, 300L, 400L, 500L),
//                ActivityBaseAlgorithmEnum.AVG_DAY,
//                false,
//                2
//        );
//        double expected = new BigDecimal(1500)
//                .divide(BigDecimal.valueOf(5), 2, BigDecimal.ROUND_UP)
//                .doubleValue();
//        assertEquals(expected, algorithmProcessIndicatorDataAvgByScale(param));
//    }
//
//    @Test
//    void testTopTen() {
//        // 测试Top 10算法
//        IndicatorAlgorithmCalcParam param = new IndicatorAlgorithmCalcParam(
//                Lists.newArrayList(100L, 200L, 300L, 400L, 500L, 600L, 700L, 800L, 900L, 1000L),
//                ActivityBaseAlgorithmEnum.TOP_10,
//                false,
//                0
//        );
//    }
//
//    @Test
//    void testEmptyList() {
//        // 测试空列表的情况
//        IndicatorAlgorithmCalcParam param = new IndicatorAlgorithmCalcParam(
//                Lists.newArrayList(),
//                ActivityBaseAlgorithmEnum.AVG_DAY,
//                false,
//                2
//        );
//        assertEquals(Objects.equals(algorithmProcessIndicatorDataAvgByScale(param), 0.0), true);
//    }
//
//    @Test
//    void testDefaultScale() {
//        // 测试默认scale为0的情况
//        IndicatorAlgorithmCalcParam param = new IndicatorAlgorithmCalcParam(
//                Lists.newArrayList(100L, 200L, 300L),
//                ActivityBaseAlgorithmEnum.AVG_DAY,
//                false,
//                null
//        );
//        assertEquals(200.0, algorithmProcessIndicatorDataAvgByScale(param));
//    }

}



