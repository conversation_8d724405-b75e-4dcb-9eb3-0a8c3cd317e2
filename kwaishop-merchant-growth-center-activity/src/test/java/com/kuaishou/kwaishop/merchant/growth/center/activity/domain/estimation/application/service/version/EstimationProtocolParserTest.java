package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version;

import static com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.version.EstimationVersionManager.calculatePeriodTime;

import java.util.List;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-17
 */class EstimationProtocolParserTest {

    @Test
    void testCalculatePeriodTime(){
        Long firstPeriodStartTime = 1740844799999L; // 2021-06-01 00:00:00
        List<Integer> periodNumList = List.of(7, 7, 10);
        int periodIndex = 0;

        Pair<Long, Long> periodTime = calculatePeriodTime(firstPeriodStartTime, periodNumList, periodIndex);
        log(periodTime, periodIndex);
        Pair<Long, Long> periodTime2 = calculatePeriodTime(firstPeriodStartTime, periodNumList, 1);
        log(periodTime2, 1);
        Pair<Long, Long> periodTime3 = calculatePeriodTime(firstPeriodStartTime, periodNumList, 2);
        log(periodTime3, 2);
    }

    private static void log(Pair<Long, Long> periodTime, int periodIndex) {
        if (periodTime != null) {
            System.out.println("周期 " + periodIndex + " 的开始时间: " + periodTime.getLeft());
            System.out.println("周期 " + periodIndex + " 的结束时间: " + periodTime.getRight());
        } else {
            System.out.println("无效的输入参数");
        }
    }

}
