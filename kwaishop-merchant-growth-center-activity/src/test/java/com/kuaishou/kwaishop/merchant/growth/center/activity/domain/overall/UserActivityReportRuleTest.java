package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.overall;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.merchant.growth.center.activity.common.enums.UserActivityStatusEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordChangeTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.enums.OverallRecordTypeEnum;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataContentBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.model.bo.OverallProcessDataBO.OverallProcessDataHeaderBO;
import com.kuaishou.kwaishop.merchant.resource.overall.client.utils.OverallMapUtils;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.RuleCenterClient;
import com.kuaishou.kwaishop.merchant.strategy.center.client.rule.executor.utils.RulePerfUtil;

/**
 * <AUTHOR>
 */
public class UserActivityReportRuleTest {

    private static String json = "{\n"
            + "    \"origin.overallChangeType\": \"UHDCJ\",\n"
            + "    \"origin.recordType\": \"userActivity\",\n"
            + "    \"origin.beforeEventData\": {\n"
            + "        \"id\": 3073822,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"activityId\": 11549,\n"
            + "        \"status\": 20,\n"
            + "        \"version\": 0,\n"
            + "        \"deleted\": 0,\n"
            + "        \"createTime\": 1688373226527,\n"
            + "        \"updateTime\": 1688373226527,\n"
            + "        \"ext\": \"{\\\"source\\\": \\\"autoDraw\\\"}\",\n"
            + "        \"creator\": \"autoDraw\",\n"
            + "        \"modifier\": \"autoDraw\"\n"
            + "    },\n"
            + "    \"origin.afterEventData\": {\n"
            + "        \"id\": 3073822,\n"
            + "        \"userId\": 2181835997,\n"
            + "        \"activityId\": 11549,\n"
            + "        \"status\": 20,\n"
            + "        \"version\": 0,\n"
            + "        \"deleted\": 0,\n"
            + "        \"createTime\": 1688373226537,\n"
            + "        \"updateTime\": 1688373226522,\n"
            + "        \"ext\": \"{\\\"source\\\": \\\"autoDraw\\\"}\",\n"
            + "        \"creator\": \"autoDraw\",\n"
            + "        \"modifier\": \"autoDraw\"\n"
            + "    },\n"
            + "    \"taskActivity.name\": \"活动名称\",\n"
            + "    \"taskActivity.startTime\": 1688373266000,\n"
            + "    \"taskActivity.endTime\": 1688373266000,\n"
            + "    \"taskActivity.resourceActivityId\": 1\n"
            + "}";

    public static void main(String[] args) {
        Map<String, Object> context = ObjectMapperUtils.fromJson(json);
        UserActivityReportRuleTest ruleTest = new UserActivityReportRuleTest();
        String result = ruleTest.buildActivityData(context);
        Map<String, Object> ruleContext = Maps.newHashMap();
        ruleContext.put("context", context);
        Object ruleResult = RuleCenterClient.getInstance()
                .ruleExecute("overall_user_activity_report_data_rule", ruleContext);
        // System.err.println(result);
        System.err.println(ruleResult);
    }

    private String buildActivityData(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String changeType = MapUtils.getString(context, "origin.overallChangeType");
        String recordType = MapUtils.getString(context, "origin.recordType");
        if (StringUtils.isBlank(changeType) || !OverallRecordTypeEnum.USER_ACTIVITY_RECORD.getCode().equals(recordType)) {
            result.put("result", 0);
            result.put("errorMsg", "变更类型不能为空，且数据类型需要为userActivity");
            return ObjectMapperUtils.toJSON(result);
        }
        Long resourceActivityId = MapUtils.getLong(context, "taskActivity.resourceActivityId");
        // 对于没有和横向关联的活动，暂时不做上报
        if (resourceActivityId == null) {
            result.put("result", 1);
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> beforeEventData = MapUtils.getMap(context, "origin.beforeEventData");
        Map<String, Object> afterEventData = MapUtils.getMap(context, "origin.afterEventData");
        OverallProcessDataBO processData = new OverallProcessDataBO();
        // header数据
        OverallProcessDataHeaderBO processDataHeader = buildProcessDataHeader(afterEventData, changeType,
                recordType, resourceActivityId);
        // 变更前数据
        OverallProcessDataContentBO beforeContent = buildProcessBeforeContent(beforeEventData);
        // 变更后数据
        OverallProcessDataContentBO afterContent = buildProcessAfterContent(context, beforeEventData,
                afterEventData, changeType);
        String creator =  MapUtils.getString(afterEventData, "creator");
        processData.setEventTime(RulePerfUtil.getCurrentTimeMs());
        processData.setSource("resourceOverall");
        // 原始消息里暂时只能拿到创建人
        processData.setOperator(creator);
        processData.setDataHeader(processDataHeader);
        processData.setBeforeContent(beforeContent);
        processData.setAfterContent(afterContent);
        result.put("data", ObjectMapperUtils.toJSON(processData));
        result.put("result", 1);
        return ObjectMapperUtils.toJSON(result);
    }

    /**
     * 变更前数据JSON
     */
    private OverallProcessDataContentBO buildProcessBeforeContent(Map<String, Object> beforeEventData) {
        OverallProcessDataContentBO beforeData = new OverallProcessDataContentBO();
        beforeData.setDesc(StringUtils.EMPTY);
        String beforeDataJson = MapUtils.isEmpty(beforeEventData) ? "" : ObjectMapperUtils.toJSON(beforeEventData);
        beforeData.setData(beforeDataJson);
        return beforeData;
    }

    /**
     * 变更后数据JSON
     */
    private OverallProcessDataContentBO buildProcessAfterContent(Map<String, Object> context,
            Map<String, Object> beforeEventData, Map<String, Object> afterEventData, String changeType) {
        OverallProcessDataContentBO afterData = new OverallProcessDataContentBO();
        String changeDesc = buildAfterChangeDesc(context, beforeEventData, afterEventData, changeType);
        String afterDataJson = MapUtils.isEmpty(afterEventData) ? "" : ObjectMapperUtils.toJSON(afterEventData);
        afterData.setDesc(changeDesc);
        afterData.setData(afterDataJson);
        return afterData;
    }

    private String buildAfterChangeDesc(Map<String, Object> context,Map<String, Object> beforeEventData,
            Map<String, Object> afterEventData, String changeType) {
        Long createTime = MapUtils.getLong(afterEventData, "createTime", Long.valueOf("0"));
        String ext = MapUtils.getString(afterEventData, "ext", "");
        UserActivityStatusEnum beforeStatus = UserActivityStatusEnum.of(MapUtils
                .getInteger(beforeEventData, "status", 0));
        UserActivityStatusEnum afterStatus = UserActivityStatusEnum.of(MapUtils
                .getInteger(afterEventData, "status", 0));
        String desc = "";
        // 活动记录创建
        if (OverallRecordChangeTypeEnum.USER_ACTIVITY_CREATE.getCode().equals(changeType)) {
            desc = String.format("用户进行了活动报名, 报名时间:%s, 报名来源:%s", createTime, ext);
        } else if (OverallRecordChangeTypeEnum.USER_ACTIVITY_UPDATE.getCode().equals(changeType)) {
            Map<String, String> diffMap = OverallMapUtils.diffMap(beforeEventData, afterEventData);
            desc = String.format("用户活动信息更新，变更前状态:[%s-%s]，变更后状态:[%s-%s], 变更的字段:%s",
                    beforeStatus.getValue(), beforeStatus.getType(),
                    afterStatus.getValue(), afterStatus.getType(), diffMap);
        }
        return desc;
    }

    /**
     * 上报数据的Header
     */
    private OverallProcessDataHeaderBO buildProcessDataHeader(Map<String, Object> afterEventData,
            String changeType, String recordType, Long resourceActivityId) {
        OverallProcessDataHeaderBO dataHeader = new OverallProcessDataHeaderBO();
        // 用分布式唯一ID用做检索key
        Long id = MapUtils.getLong(afterEventData, "id", Long.valueOf("0"));
        Long userId = MapUtils.getLong(afterEventData, "userId", Long.valueOf("0"));
        OverallRecordChangeTypeEnum changeTypeEnum = OverallRecordChangeTypeEnum.of(changeType);
        dataHeader.setUserId(userId);
        dataHeader.setActivityId(resourceActivityId);
        dataHeader.setChangeType(changeType);
        dataHeader.setRecordType(recordType);
        dataHeader.setSearchKey(changeTypeEnum.getTypePrefix() + "_" + id);
        dataHeader.setActivityType(0);
        return dataHeader;
    }

}
