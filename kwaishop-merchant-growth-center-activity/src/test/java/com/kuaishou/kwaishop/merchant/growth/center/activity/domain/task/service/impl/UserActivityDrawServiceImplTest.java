package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.offsetevent.service.OffsetEventRegistrationService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.UserActivityRepositoryService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.task.service.factory.ActivityCrowdStrategyFactory;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.ActivityLocalCacheService;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.activity.UserActivityRecordDAO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.dao.task.UserTaskRecordDAO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-05
 */
class UserActivityDrawServiceImplTest {
    @Mock
    UserActivityRecordDAO userActivityRecordDAO;
    @Mock
    UserTaskRecordDAO userTaskRecordDAO;
    @Mock
    UserActivityRepositoryService userActivityRepositoryService;
    @Mock
    ActivityLocalCacheService activityLocalCacheService;
    @Mock
    ActivityCrowdStrategyFactory activityCrowdStrategyFactory;
    @Mock
    OffsetEventRegistrationService offsetEventRegistrationService;
    @Mock
    ActivityCrowdRegistrationServiceImpl activityCrowdRegistrationService;
    @InjectMocks
    UserActivityDrawServiceImpl userActivityDrawServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

//    @Test
//    void testBatchDrawTaskWithParamNew() {
//        when(userActivityRecordDAO.queryUserActivityRecord(anyLong(), anyLong(), anyBoolean())).thenReturn(null);
//        when(userTaskRecordDAO.queryUserTaskRecordList(anyLong(), anyLong(), any(List.class))).thenReturn(null);
//        ActivityDO activity = new ActivityDO("name", "showName",
//                "bizName", Integer.valueOf(0), Long.valueOf(1), "description",2, Integer.valueOf(0)
//                , "crowdConfig", "alias", 1720124158000L, 33277266910000L, 1720124158000L, 33277266910000L,
//                Integer.valueOf(0), "ext", Integer.valueOf(0), "cycleConfig", "completeCondition", Long.valueOf(1),
//                "showConfig", "showTag", Integer.valueOf(0), "frontConfig", Long.valueOf(1), "attachInfo",
//                "galaxyPageConfig", null);
//        activity.setId(123L);
//        List<UserTaskRecordBO> taskRecordBOS = Lists.newArrayList();
//        UserTaskRecordBO userTaskRecordBO = new UserTaskRecordBO();
//        userTaskRecordBO.setTaskId(1334L);
//        taskRecordBOS.add(userTaskRecordBO);
//        when(activityCrowdRegistrationService.wrapCanDrawTaskRecordByTask(anyLong(), any(List.class), any(), any())
//        ).thenReturn(taskRecordBOS);
//        UserActivityRecordBO userActivityRecordBO = new UserActivityRecordBO();
//        userActivityRecordBO.setActivityId(123L);
//        when(activityCrowdRegistrationService.wrapCanDrawUserActivityRecord(anyLong(), any())).thenReturn
//        (userActivityRecordBO);
//
//        when(activityCrowdStrategyFactory.getActivityCrowdService(anyInt())).thenReturn
//        (activityCrowdRegistrationService);
//        when(activityLocalCacheService.queryActivityInfo(anyLong())).thenReturn(activity);
//        when(offsetEventRegistrationService.existOffsetEventTask(anyLong(), anyLong(), any(List.class))).thenReturn
//        (true);
//
//        userActivityDrawServiceImpl.batchDrawTaskWithParamForTest(123L, 123L, List.of(Long.valueOf(1334)),
//        "unlimited",
//                "{}", new DrawTaskParam(Long.valueOf(1), Long.valueOf(1)));
//        verify(userActivityRepositoryService).saveUserActivityTaskRecord(any(UserActivityRecordBO.class),
//                any(List.class));
//    }

}
