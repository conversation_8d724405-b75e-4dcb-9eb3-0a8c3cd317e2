package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.convert;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.domain.model.enums.AutoEstimationTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-26
 */
class AutoEstimateFlagGeneratorTest {

    @Test
    void testBatchSetEstimateType() {
        int result = AutoEstimateFlagGenerator.batchSetEstimateType(List.of(AutoEstimationTypeEnum.PERIOD_ESTIMATE,
                AutoEstimationTypeEnum.REALTIME_ESTIMATE));
        Assertions.assertEquals(6, result);
    }


    @Test
    void testSetEstimateType() {
        int result = AutoEstimateFlagGenerator.setEstimateType(0, AutoEstimationTypeEnum.NO_AUTO);
        Assertions.assertEquals(0, result);
    }

    @Test
    void testClearEstimateType() {
        int result = AutoEstimateFlagGenerator.clearEstimateType(7, AutoEstimationTypeEnum.PERIOD_ESTIMATE);
        Assertions.assertEquals(5, result);
    }

    @Test
    void testEstimateTypeEnable() {
        boolean result = AutoEstimateFlagGenerator.estimateTypeEnable(2,
                AutoEstimationTypeEnum.PERIOD_ESTIMATE);
        Assertions.assertEquals(true, result);
        boolean result2 = AutoEstimateFlagGenerator.estimateTypeEnable(6,
                AutoEstimationTypeEnum.PERIOD_ESTIMATE);
        Assertions.assertEquals(true, result2);
        boolean result3 = AutoEstimateFlagGenerator.estimateTypeEnable(6,
                AutoEstimationTypeEnum.REALTIME_ESTIMATE);
        Assertions.assertEquals(true, result3);
    }
}
