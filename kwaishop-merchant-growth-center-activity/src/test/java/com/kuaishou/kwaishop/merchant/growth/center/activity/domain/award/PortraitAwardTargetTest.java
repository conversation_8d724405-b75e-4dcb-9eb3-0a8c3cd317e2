package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.award;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;

/**
 * <AUTHOR>
 */
public class PortraitAwardTargetTest {

    public static void main(String[] args) {
//        test1();
        String s = String.valueOf(new BigDecimal("1000550").divide(new BigDecimal(100), RoundingMode.UP).longValue());
        System.out.println(s);

    }

    private static void test1() {
        Map<String, Object> context = Maps.newHashMap();
        TargetAwardRuleConfigBO targetAwardRule = new TargetAwardRuleConfigBO();
        TargetAwardRuleConfigBO targetAwardRule2 = new TargetAwardRuleConfigBO();
        AwardRuleStepConfigBO stepConfig = new AwardRuleStepConfigBO();
        stepConfig.setStep(1);
        targetAwardRule.setBusinessCostReturnPercent(25L);
        targetAwardRule.setGmvReturnPercent(30L);
        targetAwardRule.setMaxAwardValue(1000L);
        stepConfig.setValue(ObjectMapperUtils.toJSON(targetAwardRule));
        stepConfig.setType("incrBusinessCostReturnPercent");
        AwardRuleStepConfigBO stepConfig2 = new AwardRuleStepConfigBO();
        stepConfig2.setStep(2);
        targetAwardRule2.setBusinessCostReturnPercent(35L);
        targetAwardRule2.setGmvReturnPercent(40L);
        targetAwardRule2.setMaxAwardValue(2000L);
        stepConfig2.setValue(ObjectMapperUtils.toJSON(targetAwardRule2));
        stepConfig2.setType("incrBusinessCostReturnPercent");
        AwardRuleConfigBO ruleConfig = new AwardRuleConfigBO();
        ruleConfig.setAwardType(1);
        ruleConfig.setStepConfigs(Lists.newArrayList(stepConfig, stepConfig2));
        context.put("ruleConfig", ObjectMapperUtils.toJSON(ruleConfig));
        context.put("maxReachStep", 1);
        context.put("activityDays", 4);
        context.put("gmvBaseDayAvg", 100);
        context.put("gmvCurrentValue", 500);
        context.put("businessCostBaseDayAvg", 150);
        context.put("businessCostCurrentValue", 800);
        context.put("industryActivity_1_award_1", 12);
        context.put("industryActivity_2_award_1", 22);
        String result = testCalcAwardValue(context);
        System.out.println(result);
    }

    private static String testCalcAwardValue(Map<String, Object> context) {
        Map<String, Object> result = Maps.newHashMap();
        String awardRuleConfigJson = MapUtils.getString(context, "ruleConfig", StringUtils.EMPTY);
        AwardRuleConfigBO awardRuleConfig = ObjectMapperUtils.fromJSON(awardRuleConfigJson, AwardRuleConfigBO.class);
        if (awardRuleConfig == null || CollectionUtils.isEmpty(awardRuleConfig.getStepConfigs())) {
            result.put("result", 11);
            result.put("message", "奖励阶段规则配置不能为空");
            return ObjectMapperUtils.toJSON(result);
        }
        Map<String, Object> componentValues = Maps.newHashMap();
        // 用户最大达标阶段
        int maxReachStep = MapUtils.getInteger(context, "maxReachStep", 0);
        componentValues.put("maxReachStep", maxReachStep);
        long awardValue = 0L;
        for (AwardRuleStepConfigBO stepConfig : awardRuleConfig.getStepConfigs()) {
            // 按照最大达标阶梯给用户计算奖励
            if (!Objects.equals(maxReachStep, stepConfig.getStep())) {
                continue;
            }
            // 计算该阶段的奖励值
            awardValue = calcStepAwardValue(context, stepConfig, awardRuleConfig.getAwardType(), componentValues);
        }
        result.put("result", 1);
        result.put("message", "成功");
        result.put("awardValue", awardValue);
        result.put("componentValues", componentValues);
        return ObjectMapperUtils.toJSON(result);
    }

    private static long calcStepAwardValue(Map<String, Object> context, AwardRuleStepConfigBO stepConfig,
            int awardType, Map<String, Object> componentValues) {
        long stepAwardValue = 0;
        TargetAwardRuleConfigBO targetAwardRuleConfig =
                ObjectMapperUtils.fromJSON(stepConfig.getValue(), TargetAwardRuleConfigBO.class);
        int awardMode = 0;
        // 两个指标均达到门槛后，根据不同的类型来计算对应的奖励金额
        if ("userAssign_sameFixed".equals(stepConfig.getType()) || "userAssign_differentFixed".equals(
                stepConfig.getType())) {
            String stepAssignKey = String.format("industryActivity_%s_award_%s", stepConfig.getStep(), awardType);
            // 按固定金额设置
            stepAwardValue = MapUtils.getLong(context, stepAssignKey, 0L);
            componentValues.put("fixedAward", stepAwardValue);
            awardMode = 1;
        } else if ("incrGmvReturnPercent".equals(stepConfig.getType())) {
            // 按增量GMV返点
            long gmvIncrValue = calcIndicatorIncrValue(context, componentValues, true);
            stepAwardValue = calcIncrReturnValue(gmvIncrValue, targetAwardRuleConfig.getGmvReturnPercent());
            componentValues.put("gmvReturnPercent", targetAwardRuleConfig.getGmvReturnPercent());
            awardMode = 2;
        } else if ("incrBusinessCostReturnPercent".equals(stepConfig.getType())) {
            // 按增量商业化消耗返点
            long businessCostIncrValue = calcIndicatorIncrValue(context, componentValues, false);
            stepAwardValue =
                    calcIncrReturnValue(businessCostIncrValue, targetAwardRuleConfig.getBusinessCostReturnPercent());
            componentValues.put("businessCostReturnPercent", targetAwardRuleConfig.getBusinessCostReturnPercent());
            awardMode = 3;
        } else if ("incrGmvAndBusinessCostReturnPercent".equals(stepConfig.getType())) {
            // 按增量GMV返点 + 增量商业化消耗返点
            long gmvReturnValue = calcIncrReturnValue(calcIndicatorIncrValue(context, componentValues, true),
                    targetAwardRuleConfig.getGmvReturnPercent());
            long businessReturnValue = calcIncrReturnValue(calcIndicatorIncrValue(context, componentValues, false),
                    targetAwardRuleConfig.getBusinessCostReturnPercent());
            stepAwardValue = gmvReturnValue + businessReturnValue;
            componentValues.put("gmvReturnPercent", targetAwardRuleConfig.getGmvReturnPercent());
            componentValues.put("businessCostReturnPercent", targetAwardRuleConfig.getBusinessCostReturnPercent());
            awardMode = 4;
        } else if ("fansLTV90ReturnPercent".equals(stepConfig.getType())) {
            long ltv90Value = MapUtils.getLong(context, "portraitFansLtv90", 0L);
            long activityDays = MapUtils.getLong(context, "activityDays", 1L);
            stepAwardValue = calcIncrReturnValue(ltv90Value, targetAwardRuleConfig.getFansLTV90ReturnPercent());
            componentValues.put("activityDays", activityDays);
            componentValues.put("LTV90D", ltv90Value);
            componentValues.put("fansLTV90ReturnPercent", targetAwardRuleConfig.getFansLTV90ReturnPercent());
            awardMode = 5;
        } else if ("fansLTV90ReturnPercentAndBusinessCostReturnPercent".equals(stepConfig.getType())) {
            long ltv90Value = MapUtils.getLong(context, "portraitFansLtv90", 0L);
            long ltv90ReturnValue = calcIncrReturnValue(ltv90Value, targetAwardRuleConfig.getFansLTV90ReturnPercent());
            long businessReturnValue = calcIncrReturnValue(calcIndicatorIncrValue(context, componentValues, false),
                    targetAwardRuleConfig.getBusinessCostReturnPercent());
            stepAwardValue = ltv90ReturnValue + businessReturnValue;
            componentValues.put("LTV90D", ltv90Value);
            componentValues.put("fansLTV90ReturnPercent", targetAwardRuleConfig.getFansLTV90ReturnPercent());
            componentValues.put("businessCostReturnPercent", targetAwardRuleConfig.getBusinessCostReturnPercent());
            awardMode = 6;
        }
        componentValues.put("awardMode", awardMode);
        if (targetAwardRuleConfig.getMaxAwardValue() != null) {
            componentValues.put("maxAwardValue", targetAwardRuleConfig.getMaxAwardValue());
            // 不能超过该阶梯的最大值
            stepAwardValue = Math.min(targetAwardRuleConfig.getMaxAwardValue(), stepAwardValue);
        } else {
            componentValues.put("maxAwardValue", Long.MAX_VALUE);
        }
        return stepAwardValue;
    }

    private static long calcIncrReturnValue(long incrValue, long percent) {
        return new BigDecimal(incrValue).multiply(new BigDecimal(percent))
                .divide(new BigDecimal(100), RoundingMode.UP).longValue();
    }

    /**
     * 计算指标的增量值
     */
    private static long calcIndicatorIncrValue(Map<String, Object> context, Map<String, Object> componentValues,
            boolean isGmv) {
        String baseIndicatorKey = isGmv ? "gmvBaseDayAvg" : "businessCostBaseDayAvg";
        String indicatorValueKey = isGmv ? "gmvCurrentValue" : "businessCostCurrentValue";
        // 活动天数
        long activityDays = MapUtils.getLong(context, "activityDays", 1L);
        // 基期日均值
        long baseIndicatorDayAvgValue = MapUtils.getLong(context, baseIndicatorKey, 0L);
        // 活动开始到现在累计值
        long indicatorCurrentValue = MapUtils.getLong(context, indicatorValueKey, 0L);
        // 增量值
        long incrValue = indicatorCurrentValue - activityDays * baseIndicatorDayAvgValue;
        componentValues.put(baseIndicatorKey, baseIndicatorDayAvgValue);
        componentValues.put("activityDays", activityDays);
        // 增量为负数就按0处理
        return Math.max(incrValue, 0);
    }

    /**
     * 奖励规则配置
     */
    public static class AwardRuleConfigBO {
        /**
         * 该奖励规则绑定的奖励类型
         */
        private Integer awardType;
        // 各阶段的奖励规则配置
        List<AwardRuleStepConfigBO> stepConfigs;

        public Integer getAwardType() {
            return awardType;
        }

        public void setAwardType(Integer awardType) {
            this.awardType = awardType;
        }

        public List<AwardRuleStepConfigBO> getStepConfigs() {
            return stepConfigs;
        }

        public void setStepConfigs(
                List<AwardRuleStepConfigBO> stepConfigs) {
            this.stepConfigs = stepConfigs;
        }
    }

    /**
     * 奖励每个阶段的规则配置
     */
    public static class AwardRuleStepConfigBO {
        /**
         * 指标阶段
         */
        Integer step;
        /**
         * 指标目标类型
         */
        String type;
        /**
         * 对应目标类型的配置值
         */
        String value;

        public Integer getStep() {
            return step;
        }

        public void setStep(Integer step) {
            this.step = step;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }

    /**
     * 目标达成后的奖励规则配置
     */
    public static class TargetAwardRuleConfigBO {
        /**
         * GMV返点比例
         */
        Long gmvReturnPercent;
        /**
         * 商业化消耗返点比例
         */
        Long businessCostReturnPercent;
        /**
         * 新粉LTV90返点比例
         */
        Long fansLTV90ReturnPercent;
        /**
         * 最大奖励值
         */
        Long maxAwardValue;

        public Long getGmvReturnPercent() {
            return gmvReturnPercent;
        }

        public void setGmvReturnPercent(Long gmvReturnPercent) {
            this.gmvReturnPercent = gmvReturnPercent;
        }

        public Long getBusinessCostReturnPercent() {
            return businessCostReturnPercent;
        }

        public Long getFansLTV90ReturnPercent() {
            return fansLTV90ReturnPercent;
        }

        public void setFansLTV90ReturnPercent(Long fansLTV90ReturnPercent) {
            this.fansLTV90ReturnPercent = fansLTV90ReturnPercent;
        }

        public void setBusinessCostReturnPercent(Long businessCostReturnPercent) {
            this.businessCostReturnPercent = businessCostReturnPercent;
        }

        public Long getMaxAwardValue() {
            return maxAwardValue;
        }

        public void setMaxAwardValue(Long maxAwardValue) {
            this.maxAwardValue = maxAwardValue;
        }
    }
}
