package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.checker.impl;

import java.util.ArrayList;
import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.UserAwardLimitAwardConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.UserAwardLimitLevelConfig;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.UserAwardLimitRuleConfigBO;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.UserAwardLimitSubActivityConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-08-19
 */
class UserAwardLimitAdminProtocolCheckServiceImplTest {

    @Test
    void checkNewRulesContainOldRules() {

        // new
        List<UserAwardLimitRuleConfigBO> newReluConfigs = getOld();

        // old
        List<UserAwardLimitRuleConfigBO> oldReluConfigs = getNew();

        UserAwardLimitAdminProtocolCheckServiceImpl.checkNewRulesContainOldRules(oldReluConfigs, newReluConfigs);
    }

    @NotNull
    private static List<UserAwardLimitRuleConfigBO> getNew() {
        List<UserAwardLimitRuleConfigBO> oldReluConfigs = new ArrayList<>();
        List<UserAwardLimitSubActivityConfig> oldSubConfigs = new ArrayList<>();

        UserAwardLimitAwardConfig oldAwardConfig = UserAwardLimitAwardConfig.builder()
                .awardType(1)
                .awardName("奖励")
                .build();

        UserAwardLimitLevelConfig oldLevelConfig = UserAwardLimitLevelConfig.builder()
                .levelName("分层1")
                .isLimited(true)
                .levelAwardConfig(List.of(oldAwardConfig))
                .build();

        List<UserAwardLimitLevelConfig> oldLevelConfigs = new ArrayList<>();

        oldLevelConfigs.add(oldLevelConfig);

        UserAwardLimitSubActivityConfig oldSubActivityConfig1 =
                UserAwardLimitSubActivityConfig.builder()
                        .name("name1")
                        .subActivityOrder(1)
                        .levelConfigs(oldLevelConfigs)
                        .build();

        oldSubConfigs.add(oldSubActivityConfig1);
        UserAwardLimitRuleConfigBO oldRuleConfig1 = UserAwardLimitRuleConfigBO.builder()
                .status(2)
                .capacity(11L)
                .limitedSubActivitys(oldSubConfigs)
                .build();

        oldReluConfigs.add(oldRuleConfig1);
        return oldReluConfigs;
    }

    @NotNull
    private static List<UserAwardLimitRuleConfigBO> getOld() {
        List<UserAwardLimitRuleConfigBO> newReluConfigs = new ArrayList<>();
        List<UserAwardLimitSubActivityConfig> newSubConfigs = new ArrayList<>();
        List<UserAwardLimitLevelConfig> newLevelConfigs = new ArrayList<>();

        UserAwardLimitAwardConfig newAwardConfig = UserAwardLimitAwardConfig.builder()
                .awardType(1)
                .awardName("奖励1")
                .build();

        UserAwardLimitLevelConfig newLevelConfig = UserAwardLimitLevelConfig.builder()
                .levelName("分层1")
                .isLimited(true)
                .levelAwardConfig(List.of(newAwardConfig))
                .build();

        newLevelConfigs.add(newLevelConfig);

        UserAwardLimitSubActivityConfig newSubActivityConfig1 =
                UserAwardLimitSubActivityConfig.builder()
                        .name("name1")
                        .subActivityOrder(1)
                        .levelConfigs(newLevelConfigs)
                        .build();

        newSubConfigs.add(newSubActivityConfig1);

        UserAwardLimitRuleConfigBO newRuleConfig1 = UserAwardLimitRuleConfigBO.builder()
                .status(1)
                .capacity(11L)
                .limitedSubActivitys(newSubConfigs)
                .build();

        newReluConfigs.add(newRuleConfig1);
        return newReluConfigs;
    }


}