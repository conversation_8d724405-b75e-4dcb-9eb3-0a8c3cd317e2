package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.strategy;

import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.framework.supplier.DynamicSuppliers;

/**
 * <AUTHOR>
 */
public class RateLimitTest {

    protected static final DynamicRateLimiter traverseRateLimiter =
            DynamicSuppliers.dynamicRateLimiter(() -> 2000);

    public static void main(String[] args) {
        int count = 1000000;
        for (int i = 0; i < count; i++) {
            traverseRateLimiter.acquire();
            System.out.println(i);
        }

    }

}
