package com.kuaishou.kwaishop.merchant.growth.center.activity.domain.policyrepo.service.convert;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin2.model.bo.config.ActivityTagConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-05
 */
class PolicyRepoConverterTest {

    @Test
    void testMatchShowTag() {

        String str = "[\n" +
                "  {\n" +
                "    \"tagName\": \"默认\",\n" +
                "    \"tagCode\": \"normal\",\n" +
                "    \"bizType\": \"strategy_admin\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"tagName\": \"成长中心-成长任务（无奖励）\",\n" +
                "    \"tagCode\": \"growthCenter_growthTask\",\n" +
                "    \"bizType\": \"strategy_admin\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"tagName\": \"成长中心-成长任务（有奖励）\",\n" +
                "    \"tagCode\": \"growthCenter_growthTask_award\",\n" +
                "    \"bizType\": \"strategy_admin\"\n" +
                "  },\n" +
                "  {\n" +
                "    \"tagName\": \"大促标签\",\n" +
                "    \"tagCode\": \"promotion\",\n" +
                "    \"bizType\": \"strategy_admin\",\n" +
                "    \"subTags\": [\n" +
                "      {\n" +
                "        \"tagName\": \"818大促\",\n" +
                "        \"tagCode\": \"promotion_23_818\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  {\n" +
                "    \"tagName\": \"达人标签\",\n" +
                "    \"tagCode\": \"daren\",\n" +
                "    \"bizType\": \"strategy_admin\",\n" +
                "    \"subTags\": [\n" +
                "      {\n" +
                "        \"tagName\": \"分销活动广场（无奖励）\",\n" +
                "        \"tagCode\": \"portraitActivityDistributorNoAward\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";
        List<ActivityTagConfig> activityTagConfigs = fromJSON(str, List.class, ActivityTagConfig.class);
        List<String> result = PolicyRepoConverter.matchShowTag(activityTagConfigs, "promotion_23_818");
        List<String> result2 = PolicyRepoConverter.matchShowTag(activityTagConfigs, "normal");
        Assertions.assertEquals(result.contains("818大促"), true);
        Assertions.assertEquals(result2.contains("默认"), true);

    }

    @Test
    public void calculateDaysDifferenceTest() {
        Integer day = PolicyRepoConverter.calculateDaysDifference(1725553645912L);
        System.out.println(day);
        Integer day2 = PolicyRepoConverter.calculateDaysDifference(1725898529000L);
        System.out.println(day2);

    }
}
