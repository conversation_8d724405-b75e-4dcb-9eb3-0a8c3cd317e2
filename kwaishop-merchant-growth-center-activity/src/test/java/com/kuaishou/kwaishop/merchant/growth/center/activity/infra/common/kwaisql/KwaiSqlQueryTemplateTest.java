package com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kwaisql;

import java.util.List;

import org.junit.jupiter.api.Test;

import com.google.api.client.util.Lists;
import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.estimation.application.service.async.hivequery.DapDataKwaiSqlQueryTemplate;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kwaisql.enums.HiveDataSourceEnum;
import com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.kwaisql.param.HiveQueryParam;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-11
 */
class KwaiSqlQueryTemplateTest {

    @Test
    void buildSql() {
        DapDataKwaiSqlQueryTemplate dapDataKwaiSqlQueryTemplate = new DapDataKwaiSqlQueryTemplate();
        List<HiveQueryParam.ExprCondition> conditions = Lists.newArrayList();
        conditions.add(new HiveQueryParam.ExprCondition("key1", "123", "=", true));
        conditions.add(new HiveQueryParam.ExprCondition("key2", "13", ">=", false));
        conditions.add(new HiveQueryParam.ExprCondition("key3", "13", "<", true));

        HiveQueryParam hiveQueryParam = HiveQueryParam.builder()
                .countMode(false)
                .hiveDataSourceEnum(HiveDataSourceEnum.DAP_ESTIMATION_RESULT_Q)
                .partitionKey("p_date")
                .partitionValue("20240311")
                .database("test")
                .table("test_011")
                .conditionList(conditions)
                .build();
        String sql = dapDataKwaiSqlQueryTemplate.buildSql(hiveQueryParam);
        System.out.println(sql);
    }
}