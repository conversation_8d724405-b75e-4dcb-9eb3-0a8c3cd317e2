package com.kuaishou.kwaishop.merchant.growth.center.activity.app.rpc.test;

import static com.kuaishou.infra.falcon.util.JsonUtils.toJSON;
import static com.kuaishou.kwaishop.merchant.growth.center.activity.infra.common.utils.DynamicStringRenderer.renderString;

import java.util.HashMap;

import org.junit.jupiter.api.Test;

import com.kuaishou.kwaishop.merchant.growth.center.activity.domain.admin.model.bo.strategy.common.StrategyActivityQueryParamBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-19
 */
public class AwardTestServiceImplTest {


    @Test
    public void test() {
        StrategyActivityQueryParamBO seller = StrategyActivityQueryParamBO.builder()
                .pageNo(1)
                .pageSize(100)
                .status(30)
                .bizType("seller")
                .geDrawEndTime(System.currentTimeMillis())
                .orderByCreateTimeDesc(true)
                .show(true)
                .promotion(false)
                .needActivityDetailUrl(true)
                .build();
        System.out.println(toJSON(seller));
    }

    @Test
    public void test2() {

        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("activityId", "123");

        System.out.println(renderString("https://app.kwaixiaodian.com/page/kwaishop-b-cultivation-activity-center/index.html?__PAGE_NAME__=customize&themeStyle=1&layoutType=4&activityId=${activityId}", objectObjectHashMap));
    }


}
