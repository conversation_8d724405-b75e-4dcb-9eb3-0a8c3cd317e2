<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>kwaishop-merchant-growth-center-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>kwaishop-merchant-growth-center-activity</artifactId>
    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kess-conf-common</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-center-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--kwaishop-platform-common-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-platform-common</artifactId>
        </dependency>
        <!--    es    -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-es</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-center-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-common-utils</artifactId>
            <version>1.0.40</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-distribute-user-center-client</artifactId>
            <version>1.0.6</version>
        </dependency>
        <!-- 商业化权益 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>ad-ecom-benefit-sdk</artifactId>
            <version>1.0.36</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-interest-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou</groupId>
            <artifactId>openapi-retrofit2-sdk</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <!--KwaiSQL-->
        <dependency>
            <groupId>com.kuaishou.dataarch</groupId>
            <artifactId>themis-job-sdk</artifactId>
        </dependency>
        <!--Hive-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-datamgt-apexm-sdk</artifactId>
        </dependency>
        <!--权限中心-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-dp-auth-dsc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-component-proto-split-live</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.7.0</version>
        </dependency>
        <!--服务市场-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-service-market-sdk</artifactId>
        </dependency>
        <!-- 电商依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-ecologic-locus-sdk</artifactId>
            <!--            <version>1.0.17</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-shop-center-client</artifactId>
        </dependency>
        <!-- 店铺装修 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-shop-page-service-client</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!--招商活动-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-activity-investment-sdk</artifactId>
        </dependency>
        <!-- 长任务 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-operation-long-task-client</artifactId>
            <version>1.0.39</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>distribute-center-client</artifactId>
            <version>1.0.47</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-education-center-client</artifactId>
            <version>1.0.16</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-qualification-client</artifactId>
            <version>1.0.97</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-muses-center-client</artifactId>
            <version>1.0.11</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop.trade.rate</groupId>
            <artifactId>kwaishop-trade-rate-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-sellerdata-management-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-antispam-client</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.kuaishou.kwaishop</groupId>-->
        <!--            <artifactId>backend-server-client</artifactId>-->
        <!--            <version>1.0.138</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-live-assistant-sdk</artifactId>
            <!--            <version>1.0.31</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-flow-reservation-sdk</artifactId>
            <!--            <version>1.0.67</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-industry-auction-service-client</artifactId>
            <version>1.0.31</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-live-baseinfo-service-client</artifactId>
            <!--            <version>1.0.28</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-common-log</artifactId>
            <version>1.0.34</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-message-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-message-center-sdk</artifactId>
            <version>1.0.22</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-framework-common</artifactId>
            <!--            <version>1.0.15</version>-->
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-utils-adapter-response-proto</artifactId>
            <!--            <version>1.0.33</version>-->
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-utils-adapter-utils</artifactId>
            <!--            <version>1.0.33</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-short-video-service-client</artifactId>
            <version>1.0.52</version>
            <exclusions>
                <exclusion>
                    <artifactId>kuaishou-ad-new-biz-proto</artifactId>
                    <groupId>kuaishou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-cs-common-client</artifactId>
            <version>1.0.38</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-cs-common-proto</artifactId>
            <version>1.0.38</version>
        </dependency>
        <!-- 快手依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-es</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou</groupId>
            <artifactId>ad-ecom-wallet-sdk</artifactId>
            <!--            <version>1.0.6</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-env-utils</artifactId>
            <!--            <version>1.0.7</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-common-definition</artifactId>
            <!--            <version>1.0.84</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-photo-basic-query-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-photo-basic-define</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-ad-reco-base-ad-info-proto</artifactId>
            <version>1.0.7188</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-live-foundation-sdk</artifactId>
            <!--            <version>1.0.4554</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-biz-def</artifactId>
            <!--            <version>1.0.1649</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-rpc-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-webservice-idseq-sdk</artifactId>
            <!--            <version>1.1.72</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-zkclient</artifactId>
            <!--            <version>1.0.52</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-notify-sdk</artifactId>
            <!--            <version>1.0.15</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-reco-platform-sdk</artifactId>
            <!--            <version>1.0.537</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-scheduler-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-ecology-crm-center-client</artifactId>
            <!--            <version>1.0.59</version>-->
            <exclusions>
                <exclusion>
                    <groupId>kuaishou</groupId>
                    <artifactId>kwaishop-education-center-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-strategy-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-selection-user-service-client</artifactId>
            <version>1.0.77</version>
        </dependency>
        <!-- 基础架构依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-sentinel-ratelimiter</artifactId>
            <!--            <version>1.0.80</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-databus-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-redis-api</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-kws-tool</artifactId>
            <!--            <version>1.0.47</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-datasource</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-redis-impl</artifactId>
            <!--            <version>1.0.476</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-reporter</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-api</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-databus-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-loadingcache</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-krpc-metadata</artifactId>
            <!--            <version>1.0.127</version>-->
        </dependency>
        <!-- 三方依赖 -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.3.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-ad-ukm-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>kuaishou-ad-new-biz-proto</artifactId>
                    <groupId>kuaishou</groupId>
                </exclusion>
            </exclusions>
            <!--            <version>1.0.102</version>-->
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <!--            <version>1.18.20</version>-->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <!--            <version>5.2.0-kwai-2</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-annotations-api</artifactId>
            <!--            <version>9.0.40</version>-->
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <!--            <version>4.3</version>-->
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <!--            <version>2.0.1.Final</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <!--            <version>1.6</version>-->
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client</artifactId>
            <!--            <version>1.27.0</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ecyrd.speed4j</groupId>
            <artifactId>speed4j</artifactId>
            <!--            <version>0.18</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <!--            <version>1.19</version>-->
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.github.phantomthief</groupId>
            <artifactId>cursor-iterator</artifactId>
            <!--            <version>1.0.13</version>-->
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <!--            <version>0.11.1</version>-->
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
            <version>0.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <!--            <version>3.0.1u2</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <!--            <version>1.7.30-kwai-2</version>-->
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <!--            <version>2.11.11</version>-->
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <!--            <version>28.1-jre-kwai5</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <!--            <version>0.10.2.1U5.1.32</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <!--            <version>4.1.1</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <!--            <version>4.1.1</version>-->
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <!--            <version>1.3.2</version>-->
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <!--            <version>3.1.0</version>-->
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.kuaishou.dp</groupId>-->
        <!--            <artifactId>dp-schema-service-sdk</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.kuaishou.dp</groupId>
            <artifactId>one-service-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hbase-shaded-client</artifactId>
                    <groupId>org.apache.hbase</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.gaea</groupId>
            <artifactId>gaea-monitor-starter</artifactId>
            <version>1.0.55</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <!--AB平台依赖-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-abtest-core</artifactId>
        </dependency>
        <!-- 保证金-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-funds-center-sdk</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>kuaishou</groupId>-->
        <!--            <artifactId>kwaishop-product-brand-client</artifactId>-->
        <!--        </dependency>-->
        <!-- 工作台-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-shop-combine-sdk</artifactId>
            <version>1.0.134</version>
        </dependency>
        <!-- 跟播助手-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-flow-live-backend-service-client</artifactId>
            <version>1.0.14</version>
            <exclusions>
                <exclusion>
                    <artifactId>kwaishop-marketing-tools-service-client</artifactId>
                    <groupId>kuaishou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-delivery-horserace-service-client</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-delivery-backend-service-client</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-sic-service-client</artifactId>
            <!--            <version>1.0.33</version>-->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-product-supplychain-service-client</artifactId>
            <version>1.0.330</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-antispam-eb-oms-sdk</artifactId>
            <!--            <version>1.0.70</version>-->
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 横向补贴系统 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-operation-center-client</artifactId>
            <version>1.0.87</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-operation-core-center-client</artifactId>
            <version>1.0.62</version>
        </dependency>
        <!-- 类目系统 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-product-categoryandattr-client</artifactId>
        </dependency>
        <!-- kstry -->
        <dependency>
            <groupId>com.kuaishou.gaea</groupId>
            <artifactId>gaea-kstry-starter</artifactId>
            <version>1.0.75</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>kuaishou</groupId>-->
        <!--            <artifactId>kuaishou-live-foundation-public-service</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-distribute-match-service-client</artifactId>
            <version>1.0.39</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-enrichment-operation-center-client</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-education-center-client</artifactId>
            <version>1.0.8</version>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.6.4</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.blobstore</groupId>
            <artifactId>ks-boot-starter-bs3-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-sic-service-rich-client</artifactId>
            <version>1.0.49</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishop-access-control-sdk</artifactId>
            <version>1.0.106</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-im-sdk</artifactId>
            <version>2.0.399</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-sophon-solution-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-split-biz-common-tag-sdk</artifactId>
            <version>1.0.266</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-photo-social-sdk</artifactId>
            <version>1.0.2286</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-charge-center-client</artifactId>
            <version>1.0.103</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwai-business</groupId>
            <artifactId>kwai-business-flow-engine</artifactId>
            <!-- 请使用最新稳定版本 -->
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-tianhe-galax-center-client</artifactId>
        </dependency>
        <!-- mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <!-- 达芬奇画像平台 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-userprofile-sdk</artifactId>
        </dependency>
        <!--  DAP SDK   -->
        <dependency>
            <groupId>com.kuaishou.dp</groupId>
            <artifactId>kuaishou-data-strategy-sdk</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.alibaba.cola/cola-component-statemachine -->
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-statemachine</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-growth-common-kconf</artifactId>
            <version>1.0.34</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-selection-seller-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-merchant-portrait-center-rich-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwai-business</groupId>
            <artifactId>kwai-business-flow-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwai-business</groupId>
            <artifactId>kwai-business-flow-assembly</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-live-author-tools-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwai-business</groupId>
            <artifactId>kwai-business-flow-serverapi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-krpc-admin-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwai-business</groupId>
            <artifactId>kwai-business-flow-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-lang-bridge-sdk</artifactId>
            <version>1.0.16</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>design-ai-sdk</artifactId>
            <version>1.0.3</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>